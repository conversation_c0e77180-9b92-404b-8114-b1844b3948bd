<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 统计卡片组件
-->

<template>
  <el-card class="stat-card" :class="cardClass">
    <div class="stat-content">
      <div class="stat-info">
        <div class="stat-title">{{ title }}</div>
        <div class="stat-value">{{ formattedValue }}</div>
        <div v-if="trend !== undefined" class="stat-trend" :class="trendClass">
          <el-icon class="trend-icon">
            <component :is="trendIcon" />
          </el-icon>
          <span class="trend-text">{{ trendText }}</span>
        </div>
      </div>
      <div class="stat-icon" :style="{ backgroundColor: iconBgColor }">
        <el-icon :color="color" size="24">
          <component :is="icon" />
        </el-icon>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'

interface Props {
  title: string
  value: number | string
  icon: string
  color: string
  trend?: number
  suffix?: string
  prefix?: string
}

const props = withDefaults(defineProps<Props>(), {
  suffix: '',
  prefix: ''
})

// 计算属性
const formattedValue = computed(() => {
  let val = props.value
  
  // 如果是数字，进行格式化
  if (typeof val === 'number') {
    if (val >= 10000) {
      val = (val / 10000).toFixed(1) + 'w'
    } else if (val >= 1000) {
      val = (val / 1000).toFixed(1) + 'k'
    } else {
      val = val.toString()
    }
  }
  
  return props.prefix + val + props.suffix
})

const cardClass = computed(() => {
  return {
    'stat-card--positive': props.trend && props.trend > 0,
    'stat-card--negative': props.trend && props.trend < 0,
    'stat-card--neutral': props.trend === 0
  }
})

const trendClass = computed(() => {
  if (props.trend === undefined) return ''
  
  return {
    'trend--positive': props.trend > 0,
    'trend--negative': props.trend < 0,
    'trend--neutral': props.trend === 0
  }
})

const trendIcon = computed(() => {
  if (props.trend === undefined) return ''
  
  if (props.trend > 0) return ArrowUp
  if (props.trend < 0) return ArrowDown
  return Minus
})

const trendText = computed(() => {
  if (props.trend === undefined) return ''
  
  const absValue = Math.abs(props.trend)
  
  if (props.trend > 0) {
    return `较昨日 +${absValue}%`
  } else if (props.trend < 0) {
    return `较昨日 -${absValue}%`
  } else {
    return '较昨日无变化'
  }
})

const iconBgColor = computed(() => {
  // 将颜色转换为半透明背景色
  const hex = props.color.replace('#', '')
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)
  
  return `rgba(${r}, ${g}, ${b}, 0.1)`
})
</script>

<style lang="scss" scoped>
.stat-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
  
  :deep(.el-card__body) {
    padding: 20px;
  }
}

.stat-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
  font-weight: 400;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
  line-height: 1;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  
  &.trend--positive {
    color: #67C23A;
  }
  
  &.trend--negative {
    color: #F56C6C;
  }
  
  &.trend--neutral {
    color: var(--el-text-color-secondary);
  }
}

.trend-icon {
  font-size: 12px;
}

.trend-text {
  font-weight: 500;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

// 卡片状态样式
.stat-card--positive {
  &:hover {
    border-left: 4px solid #67C23A;
  }
}

.stat-card--negative {
  &:hover {
    border-left: 4px solid #F56C6C;
  }
}

.stat-card--neutral {
  &:hover {
    border-left: 4px solid var(--el-color-info);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .stat-content {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .stat-value {
    font-size: 24px;
  }
  
  .stat-icon {
    width: 50px;
    height: 50px;
  }
}

// 暗色主题适配
:deep(.dark) {
  .stat-card {
    background-color: var(--el-bg-color-page);
    
    &:hover {
      background-color: var(--el-bg-color);
    }
  }
}
</style>

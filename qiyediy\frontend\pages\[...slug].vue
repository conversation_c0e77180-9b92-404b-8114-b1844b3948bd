<!--
  三只鱼网络科技 | 韩总 | 2024-12-20
  QiyeDIY企业建站系统 - 动态页面渲染器
-->

<template>
  <div class="dynamic-page">
    <!-- 页面头部 -->
    <AppHeader v-if="!pageData.config?.hideHeader" />

    <!-- 页面内容 -->
    <main class="page-content" :class="pageClasses">
      <!-- 页面组件渲染 -->
      <PageRenderer
        v-if="pageData.components?.length"
        :components="pageData.components"
        :page-config="pageData.config"
      />
      
      <!-- 空页面状态 -->
      <div v-else class="empty-page">
        <div class="empty-content">
          <h2>页面正在建设中</h2>
          <p>该页面暂无内容，请稍后再来查看。</p>
          <NuxtLink to="/" class="btn btn-primary">返回首页</NuxtLink>
        </div>
      </div>
    </main>

    <!-- 页面底部 -->
    <AppFooter v-if="!pageData.config?.hideFooter" />

    <!-- 自定义样式 -->
    <component :is="'style'" v-if="pageData.custom_css">
      {{ pageData.custom_css }}
    </component>

    <!-- 自定义脚本 -->
    <component :is="'script'" v-if="pageData.custom_js">
      {{ pageData.custom_js }}
    </component>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { DiyPage } from '~/types/diy'

const route = useRoute()
const router = useRouter()

// 页面数据
const pageData = ref<DiyPage>({} as DiyPage)
const loading = ref(true)
const error = ref<string | null>(null)

// 路由参数解析
// 例如：访问 /about/company 时，route.params.slug = ['about', 'company']
// 例如：访问 /products 时，route.params.slug = ['products']
// 例如：访问 /news/detail/123 时，route.params.slug = ['news', 'detail', '123']

// 计算属性
const pageClasses = computed(() => {
  const classes = ['page-renderer']
  
  if (pageData.value.config?.fullWidth) {
    classes.push('full-width')
  }
  
  if (pageData.value.config?.darkMode) {
    classes.push('dark-mode')
  }
  
  return classes
})

/**
 * 加载页面数据
 */
const loadPageData = async () => {
  try {
    loading.value = true
    error.value = null
    
    const slug = Array.isArray(route.params.slug) 
      ? route.params.slug.join('/') 
      : route.params.slug || 'home'
    
    // 调用API获取页面数据
    const response = await fetch(`/api/pages/${slug}`).then(res => res.json())
    
    if (response.code === 200) {
      pageData.value = response.data
      
      // 设置页面SEO信息
      updatePageSEO()
      
      // 记录页面访问
      recordPageView()
      
    } else {
      throw new Error(response.message || '页面不存在')
    }
    
  } catch (err: any) {
    console.error('加载页面失败:', err)
    error.value = err.message || '页面加载失败'
    
    // 404页面处理
    if (err.statusCode === 404) {
      throw createError({
        statusCode: 404,
        statusMessage: '页面不存在'
      })
    }
    
  } finally {
    loading.value = false
  }
}

/**
 * 更新页面SEO信息
 */
const updatePageSEO = () => {
  const page = pageData.value
  
  // 设置页面标题
  const title = page.seo_title || page.title || '页面'
  
  // 设置SEO元信息
  useSeoMeta({
    title,
    description: page.seo_description || page.description || '',
    keywords: page.seo_keywords || '',
    ogTitle: title,
    ogDescription: page.seo_description || page.description || '',
    ogImage: page.seo_image || '/images/og-default.jpg',
    ogUrl: `${useRuntimeConfig().public.siteUrl}${route.path}`,
    twitterCard: 'summary_large_image',
    twitterTitle: title,
    twitterDescription: page.seo_description || page.description || '',
    twitterImage: page.seo_image || '/images/og-default.jpg'
  })
  
  // 设置结构化数据
  if (page.config?.structuredData) {
    useJsonld(page.config.structuredData)
  }
}

/**
 * 记录页面访问
 */
const recordPageView = async () => {
  try {
    await $fetch('/api/analytics/page-view', {
      method: 'POST',
      body: {
        page_id: pageData.value.id,
        slug: pageData.value.slug,
        title: pageData.value.title,
        referrer: document.referrer,
        user_agent: navigator.userAgent
      }
    })
  } catch (error) {
    // 静默处理访问统计错误
    console.warn('记录页面访问失败:', error)
  }
}

// 页面加载
onMounted(() => {
  loadPageData()
})

// 监听路由变化
watch(() => route.params.slug, () => {
  loadPageData()
})
</script>

<style lang="scss" scoped>
.dynamic-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-content {
  flex: 1;
  
  &.full-width {
    width: 100%;
    max-width: none;
  }
  
  &.dark-mode {
    background: #1a1a1a;
    color: #ffffff;
  }
}

.page-renderer {
  &:not(.full-width) {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
}

.empty-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  
  .empty-content {
    max-width: 400px;
    
    h2 {
      font-size: 2rem;
      margin-bottom: 1rem;
      color: #333;
    }
    
    p {
      font-size: 1.125rem;
      color: #666;
      margin-bottom: 2rem;
      line-height: 1.6;
    }
  }
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  
  &.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-renderer:not(.full-width) {
    padding: 0 16px;
  }
  
  .empty-content {
    h2 {
      font-size: 1.5rem;
    }
    
    p {
      font-size: 1rem;
    }
  }
}
</style>

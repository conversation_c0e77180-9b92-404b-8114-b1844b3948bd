<!--
  三只鱼网络科技 | 韩总 | 2024-12-20
  QiyeDIY企业建站系统 - 文件管理器组件
-->

<template>
  <div class="file-manager">
    <!-- 工具栏 -->
    <div class="file-toolbar">
      <div class="toolbar-left">
        <!-- 路径导航 -->
        <el-breadcrumb separator="/">
          <el-breadcrumb-item @click="navigateTo('/')">
            <Icon name="home" />
            根目录
          </el-breadcrumb-item>
          <el-breadcrumb-item
            v-for="(path, index) in pathSegments"
            :key="index"
            @click="navigateTo(getPathByIndex(index))"
          >
            {{ path }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      
      <div class="toolbar-right">
        <!-- 搜索框 -->
        <el-input
          v-model="searchKeyword"
          placeholder="搜索文件..."
          size="small"
          style="width: 200px"
          @input="onSearch"
        >
          <template #prefix>
            <Icon name="search" />
          </template>
        </el-input>
        
        <!-- 视图切换 -->
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button label="grid">
            <Icon name="grid" />
          </el-radio-button>
          <el-radio-button label="list">
            <Icon name="list" />
          </el-radio-button>
        </el-radio-group>
        
        <!-- 上传按钮 -->
        <el-upload
          ref="uploadRef"
          :action="uploadUrl"
          :headers="uploadHeaders"
          :show-file-list="false"
          :before-upload="beforeUpload"
          :on-success="onUploadSuccess"
          :on-error="onUploadError"
          multiple
          drag
        >
          <el-button type="primary" size="small">
            <Icon name="upload" />
            上传文件
          </el-button>
        </el-upload>
        
        <!-- 新建文件夹 -->
        <el-button @click="showCreateFolder = true" size="small">
          <Icon name="folder-plus" />
          新建文件夹
        </el-button>
        
        <!-- 刷新 -->
        <el-button @click="refreshFiles" size="small" :loading="loading">
          <Icon name="refresh" />
        </el-button>
      </div>
    </div>
    
    <!-- 文件列表 -->
    <div class="file-content" :class="`view-${viewMode}`">
      <!-- 加载状态 -->
      <div v-if="loading" class="file-loading">
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
        <span>加载中...</span>
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="filteredFiles.length === 0" class="file-empty">
        <Icon name="folder-empty" size="64" />
        <p>{{ searchKeyword ? '没有找到匹配的文件' : '文件夹为空' }}</p>
        <el-button v-if="!searchKeyword" @click="showCreateFolder = true" type="primary">
          创建第一个文件夹
        </el-button>
      </div>
      
      <!-- 网格视图 -->
      <div v-else-if="viewMode === 'grid'" class="file-grid">
        <div
          v-for="file in filteredFiles"
          :key="file.id"
          class="file-item"
          :class="{
            'file-selected': selectedFiles.includes(file.id),
            'file-folder': file.type === 'folder'
          }"
          @click="onFileClick(file)"
          @dblclick="onFileDoubleClick(file)"
          @contextmenu.prevent="onFileRightClick(file, $event)"
        >
          <!-- 文件图标/缩略图 -->
          <div class="file-icon">
            <img
              v-if="file.type === 'image' && file.thumbnail"
              :src="file.thumbnail"
              :alt="file.name"
              class="file-thumbnail"
            />
            <Icon
              v-else
              :name="getFileIcon(file)"
              :size="48"
              :color="getFileIconColor(file)"
            />
          </div>
          
          <!-- 文件名 -->
          <div class="file-name" :title="file.name">
            {{ file.name }}
          </div>
          
          <!-- 文件信息 -->
          <div class="file-info">
            <span v-if="file.type !== 'folder'" class="file-size">
              {{ formatFileSize(file.size) }}
            </span>
            <span class="file-date">
              {{ formatDate(file.updated_at) }}
            </span>
          </div>
          
          <!-- 选择框 -->
          <div class="file-checkbox">
            <el-checkbox
              :model-value="selectedFiles.includes(file.id)"
              @change="onFileSelect(file.id, $event)"
              @click.stop
            />
          </div>
        </div>
      </div>
      
      <!-- 列表视图 -->
      <el-table
        v-else
        :data="filteredFiles"
        @selection-change="onSelectionChange"
        @row-click="onFileClick"
        @row-dblclick="onFileDoubleClick"
        @row-contextmenu="onFileRightClick"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="名称" min-width="200">
          <template #default="{ row }">
            <div class="file-name-cell">
              <Icon
                :name="getFileIcon(row)"
                :color="getFileIconColor(row)"
                size="20"
              />
              <span>{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="大小" width="100">
          <template #default="{ row }">
            {{ row.type === 'folder' ? '-' : formatFileSize(row.size) }}
          </template>
        </el-table-column>
        
        <el-table-column label="类型" width="100">
          <template #default="{ row }">
            {{ getFileTypeLabel(row.type) }}
          </template>
        </el-table-column>
        
        <el-table-column label="修改时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.updated_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              @click.stop="onFileRename(row)"
              size="small"
              text
            >
              重命名
            </el-button>
            <el-button
              @click.stop="onFileDelete(row)"
              size="small"
              text
              type="danger"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 右键菜单 -->
    <el-dropdown
      ref="contextMenuRef"
      trigger="contextmenu"
      :virtual-ref="contextMenuTarget"
      virtual-triggering
    >
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item @click="onFileOpen" v-if="contextFile?.type !== 'folder'">
            <Icon name="eye" />
            预览
          </el-dropdown-item>
          <el-dropdown-item @click="onFileDownload" v-if="contextFile?.type !== 'folder'">
            <Icon name="download" />
            下载
          </el-dropdown-item>
          <el-dropdown-item @click="onFileRename(contextFile)" v-if="contextFile">
            <Icon name="edit" />
            重命名
          </el-dropdown-item>
          <el-dropdown-item @click="onFileCopy" v-if="contextFile">
            <Icon name="copy" />
            复制
          </el-dropdown-item>
          <el-dropdown-item @click="onFileCut" v-if="contextFile">
            <Icon name="cut" />
            剪切
          </el-dropdown-item>
          <el-dropdown-item divided />
          <el-dropdown-item @click="onFileDelete(contextFile)" v-if="contextFile">
            <Icon name="delete" />
            删除
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    
    <!-- 新建文件夹对话框 -->
    <el-dialog
      v-model="showCreateFolder"
      title="新建文件夹"
      width="400px"
    >
      <el-form @submit.prevent="createFolder">
        <el-form-item label="文件夹名称">
          <el-input
            v-model="newFolderName"
            placeholder="请输入文件夹名称"
            @keyup.enter="createFolder"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateFolder = false">取消</el-button>
        <el-button type="primary" @click="createFolder">创建</el-button>
      </template>
    </el-dialog>
    
    <!-- 重命名对话框 -->
    <el-dialog
      v-model="showRename"
      title="重命名"
      width="400px"
    >
      <el-form @submit.prevent="renameFile">
        <el-form-item label="新名称">
          <el-input
            v-model="renameValue"
            placeholder="请输入新名称"
            @keyup.enter="renameFile"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showRename = false">取消</el-button>
        <el-button type="primary" @click="renameFile">确定</el-button>
      </template>
    </el-dialog>
    
    <!-- 文件预览对话框 -->
    <el-dialog
      v-model="showPreview"
      :title="previewFile?.name"
      width="80%"
      top="5vh"
    >
      <div class="file-preview">
        <!-- 图片预览 -->
        <img
          v-if="previewFile?.type === 'image'"
          :src="previewFile.url"
          :alt="previewFile.name"
          style="max-width: 100%; max-height: 60vh;"
        />
        
        <!-- 视频预览 -->
        <video
          v-else-if="previewFile?.type === 'video'"
          :src="previewFile.url"
          controls
          style="max-width: 100%; max-height: 60vh;"
        />
        
        <!-- 音频预览 -->
        <audio
          v-else-if="previewFile?.type === 'audio'"
          :src="previewFile.url"
          controls
          style="width: 100%;"
        />
        
        <!-- 文本预览 -->
        <pre
          v-else-if="previewFile?.type === 'text'"
          class="text-preview"
        >{{ previewContent }}</pre>
        
        <!-- 不支持预览 -->
        <div v-else class="preview-unsupported">
          <Icon name="file" size="64" />
          <p>此文件类型不支持预览</p>
          <el-button @click="onFileDownload" type="primary">
            下载文件
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { debounce } from 'lodash-es'

interface FileItem {
  id: string
  name: string
  type: 'folder' | 'image' | 'video' | 'audio' | 'text' | 'document' | 'archive' | 'other'
  size: number
  url: string
  thumbnail?: string
  path: string
  parent_id: string | null
  created_at: string
  updated_at: string
}

const props = defineProps({
  multiple: {
    type: Boolean,
    default: true
  },
  accept: {
    type: String,
    default: ''
  },
  maxSize: {
    type: Number,
    default: 10 * 1024 * 1024 // 10MB
  }
})

const emit = defineEmits(['select', 'upload', 'delete'])

// 响应式数据
const loading = ref(false)
const files = ref<FileItem[]>([])
const currentPath = ref('/')
const searchKeyword = ref('')
const viewMode = ref<'grid' | 'list'>('grid')
const selectedFiles = ref<string[]>([])

// 对话框状态
const showCreateFolder = ref(false)
const showRename = ref(false)
const showPreview = ref(false)
const newFolderName = ref('')
const renameValue = ref('')
const renameTarget = ref<FileItem | null>(null)
const previewFile = ref<FileItem | null>(null)
const previewContent = ref('')

// 右键菜单
const contextMenuRef = ref()
const contextMenuTarget = ref()
const contextFile = ref<FileItem | null>(null)

// 上传配置
const uploadRef = ref()
const uploadUrl = '/api/upload/file'
const uploadHeaders = {
  'Authorization': `Bearer ${localStorage.getItem('token')}`
}

// 计算属性
const pathSegments = computed(() => {
  return currentPath.value.split('/').filter(Boolean)
})

const filteredFiles = computed(() => {
  if (!searchKeyword.value) return files.value
  
  return files.value.filter(file =>
    file.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 方法
const loadFiles = async (path: string = currentPath.value) => {
  try {
    loading.value = true
    
    const response = await $fetch('/api/files', {
      params: { path }
    })
    
    if (response.code === 200) {
      files.value = response.data
    }
    
  } catch (error) {
    console.error('加载文件失败:', error)
    ElMessage.error('加载文件失败')
  } finally {
    loading.value = false
  }
}

const navigateTo = (path: string) => {
  currentPath.value = path
  loadFiles(path)
}

const getPathByIndex = (index: number): string => {
  const segments = pathSegments.value.slice(0, index + 1)
  return '/' + segments.join('/')
}

const refreshFiles = () => {
  loadFiles()
}

const onSearch = debounce(() => {
  // 搜索逻辑已在计算属性中实现
}, 300)

const onFileClick = (file: FileItem) => {
  if (props.multiple) {
    const index = selectedFiles.value.indexOf(file.id)
    if (index > -1) {
      selectedFiles.value.splice(index, 1)
    } else {
      selectedFiles.value.push(file.id)
    }
  } else {
    selectedFiles.value = [file.id]
  }
  
  emit('select', selectedFiles.value.map(id => 
    files.value.find(f => f.id === id)
  ).filter(Boolean))
}

const onFileDoubleClick = (file: FileItem) => {
  if (file.type === 'folder') {
    navigateTo(file.path)
  } else {
    onFileOpen()
  }
}

const onFileRightClick = (file: FileItem, event: MouseEvent) => {
  contextFile.value = file
  contextMenuTarget.value = event.target
}

const onFileSelect = (fileId: string, selected: boolean) => {
  if (selected) {
    if (!selectedFiles.value.includes(fileId)) {
      selectedFiles.value.push(fileId)
    }
  } else {
    const index = selectedFiles.value.indexOf(fileId)
    if (index > -1) {
      selectedFiles.value.splice(index, 1)
    }
  }
}

const onSelectionChange = (selection: FileItem[]) => {
  selectedFiles.value = selection.map(file => file.id)
}

const getFileIcon = (file: FileItem): string => {
  const iconMap: Record<string, string> = {
    folder: 'folder',
    image: 'image',
    video: 'video',
    audio: 'music',
    text: 'file-text',
    document: 'file-doc',
    archive: 'file-zip',
    other: 'file'
  }
  return iconMap[file.type] || 'file'
}

const getFileIconColor = (file: FileItem): string => {
  const colorMap: Record<string, string> = {
    folder: '#fbbf24',
    image: '#10b981',
    video: '#8b5cf6',
    audio: '#f59e0b',
    text: '#6b7280',
    document: '#3b82f6',
    archive: '#ef4444',
    other: '#9ca3af'
  }
  return colorMap[file.type] || '#9ca3af'
}

const getFileTypeLabel = (type: string): string => {
  const labelMap: Record<string, string> = {
    folder: '文件夹',
    image: '图片',
    video: '视频',
    audio: '音频',
    text: '文本',
    document: '文档',
    archive: '压缩包',
    other: '其他'
  }
  return labelMap[type] || '未知'
}

const formatFileSize = (size: number): string => {
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let index = 0
  
  while (size >= 1024 && index < units.length - 1) {
    size /= 1024
    index++
  }
  
  return `${size.toFixed(index === 0 ? 0 : 1)} ${units[index]}`
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 24 * 60 * 60 * 1000) {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit'
    })
  }
}

// 文件操作
const createFolder = async () => {
  if (!newFolderName.value.trim()) {
    ElMessage.warning('请输入文件夹名称')
    return
  }
  
  try {
    const response = await $fetch('/api/files/folder', {
      method: 'POST',
      body: {
        name: newFolderName.value,
        path: currentPath.value
      }
    })
    
    if (response.code === 200) {
      ElMessage.success('文件夹创建成功')
      showCreateFolder.value = false
      newFolderName.value = ''
      refreshFiles()
    }
    
  } catch (error) {
    console.error('创建文件夹失败:', error)
    ElMessage.error('创建文件夹失败')
  }
}

const onFileRename = (file: FileItem) => {
  renameTarget.value = file
  renameValue.value = file.name
  showRename.value = true
}

const renameFile = async () => {
  if (!renameValue.value.trim() || !renameTarget.value) {
    ElMessage.warning('请输入新名称')
    return
  }
  
  try {
    const response = await $fetch(`/api/files/${renameTarget.value.id}/rename`, {
      method: 'PUT',
      body: {
        name: renameValue.value
      }
    })
    
    if (response.code === 200) {
      ElMessage.success('重命名成功')
      showRename.value = false
      refreshFiles()
    }
    
  } catch (error) {
    console.error('重命名失败:', error)
    ElMessage.error('重命名失败')
  }
}

const onFileDelete = async (file: FileItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除 "${file.name}" 吗？`,
      '确认删除',
      {
        type: 'warning'
      }
    )
    
    const response = await $fetch(`/api/files/${file.id}`, {
      method: 'DELETE'
    })
    
    if (response.code === 200) {
      ElMessage.success('删除成功')
      refreshFiles()
      emit('delete', file)
    }
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除文件失败:', error)
      ElMessage.error('删除文件失败')
    }
  }
}

const onFileOpen = () => {
  if (!contextFile.value) return
  
  previewFile.value = contextFile.value
  
  if (contextFile.value.type === 'text') {
    loadTextContent(contextFile.value)
  }
  
  showPreview.value = true
}

const loadTextContent = async (file: FileItem) => {
  try {
    const response = await fetch(file.url)
    previewContent.value = await response.text()
  } catch (error) {
    console.error('加载文本内容失败:', error)
    previewContent.value = '无法加载文件内容'
  }
}

const onFileDownload = () => {
  if (!contextFile.value) return
  
  const link = document.createElement('a')
  link.href = contextFile.value.url
  link.download = contextFile.value.name
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const onFileCopy = () => {
  // 实现文件复制逻辑
  ElMessage.info('复制功能开发中...')
}

const onFileCut = () => {
  // 实现文件剪切逻辑
  ElMessage.info('剪切功能开发中...')
}

// 上传处理
const beforeUpload = (file: File) => {
  if (props.maxSize && file.size > props.maxSize) {
    ElMessage.error(`文件大小不能超过 ${formatFileSize(props.maxSize)}`)
    return false
  }
  
  if (props.accept) {
    const acceptTypes = props.accept.split(',').map(type => type.trim())
    const fileType = file.type
    const fileName = file.name
    
    const isAccepted = acceptTypes.some(accept => {
      if (accept.startsWith('.')) {
        return fileName.toLowerCase().endsWith(accept.toLowerCase())
      } else {
        return fileType.includes(accept.replace('*', ''))
      }
    })
    
    if (!isAccepted) {
      ElMessage.error('文件类型不支持')
      return false
    }
  }
  
  return true
}

const onUploadSuccess = (response: any, file: File) => {
  if (response.code === 200) {
    ElMessage.success('上传成功')
    refreshFiles()
    emit('upload', response.data)
  } else {
    ElMessage.error(response.message || '上传失败')
  }
}

const onUploadError = (error: any) => {
  console.error('上传失败:', error)
  ElMessage.error('上传失败')
}

// 生命周期
onMounted(() => {
  loadFiles()
})

// 监听路径变化
watch(currentPath, (newPath) => {
  loadFiles(newPath)
})
</script>

<style lang="scss" scoped>
.file-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.file-toolbar {
  padding: 16px 20px;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  flex-wrap: wrap;
}

.toolbar-left {
  flex: 1;
  min-width: 200px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.file-content {
  flex: 1;
  padding: 20px;
  overflow: auto;
  
  &.view-list {
    padding: 0;
  }
}

.file-loading,
.file-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #94a3b8;
  
  p {
    margin: 16px 0 20px;
    font-size: 16px;
  }
}

.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
}

.file-item {
  position: relative;
  padding: 12px;
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  
  &:hover {
    background: #f8fafc;
    border-color: #e2e8f0;
  }
  
  &.file-selected {
    background: #eef2ff;
    border-color: #667eea;
  }
  
  &.file-folder:hover {
    background: #fefce8;
    border-color: #fbbf24;
  }
}

.file-icon {
  margin-bottom: 8px;
  display: flex;
  justify-content: center;
}

.file-thumbnail {
  width: 48px;
  height: 48px;
  object-fit: cover;
  border-radius: 4px;
}

.file-name {
  font-size: 12px;
  color: #1e293b;
  margin-bottom: 4px;
  word-break: break-all;
  line-height: 1.3;
  max-height: 2.6em;
  overflow: hidden;
}

.file-info {
  font-size: 10px;
  color: #94a3b8;
  line-height: 1.2;
}

.file-size {
  display: block;
}

.file-date {
  display: block;
}

.file-checkbox {
  position: absolute;
  top: 4px;
  right: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.file-item:hover .file-checkbox,
.file-item.file-selected .file-checkbox {
  opacity: 1;
}

.file-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-preview {
  text-align: center;
}

.text-preview {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 16px;
  max-height: 400px;
  overflow: auto;
  text-align: left;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  white-space: pre-wrap;
}

.preview-unsupported {
  padding: 40px;
  color: #94a3b8;
  
  p {
    margin: 16px 0 24px;
    font-size: 16px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .file-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .toolbar-left,
  .toolbar-right {
    width: 100%;
  }
  
  .toolbar-right {
    justify-content: space-between;
  }
  
  .file-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 12px;
  }
  
  .file-content {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .file-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
  }
  
  .file-item {
    padding: 8px;
  }
  
  .file-icon {
    margin-bottom: 4px;
  }
  
  .file-name {
    font-size: 11px;
  }
  
  .file-info {
    font-size: 9px;
  }
}
</style>

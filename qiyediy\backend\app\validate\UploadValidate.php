<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * QiyeDIY企业建站系统 - 文件上传验证器
 */

declare(strict_types=1);

namespace app\validate;

use think\Validate;

/**
 * 文件上传验证器
 */
class UploadValidate extends Validate
{
    /**
     * 验证规则
     * @var array
     */
    protected $rule = [
        // 图片上传
        'file' => 'require|file|fileExt:jpg,jpeg,png,gif,webp,bmp|fileSize:5242880', // 5MB
        
        // 视频上传
        'video_file' => 'require|file|fileExt:mp4,avi,mov,wmv,flv,webm|fileSize:104857600', // 100MB
        
        // 文档上传
        'document_file' => 'require|file|fileExt:pdf,doc,docx,xls,xlsx,ppt,pptx,txt|fileSize:20971520', // 20MB
        
        // 头像上传
        'avatar_file' => 'require|file|fileExt:jpg,jpeg,png,gif,webp|fileSize:2097152', // 2MB
        
        // Base64上传
        'data' => 'require|checkBase64',
        'type' => 'in:image,video,document',
        
        // 图片裁剪
        'path' => 'require|checkFilePath',
        'x' => 'require|integer|egt:0',
        'y' => 'require|integer|egt:0',
        'width' => 'require|integer|gt:0|elt:2048',
        'height' => 'require|integer|gt:0|elt:2048',
        'quality' => 'integer|between:1,100'
    ];

    /**
     * 错误信息
     * @var array
     */
    protected $message = [
        'file.require' => '请选择要上传的文件',
        'file.file' => '上传的必须是文件',
        'file.fileExt' => '不支持的文件格式',
        'file.fileSize' => '文件大小超出限制',
        
        'video_file.require' => '请选择要上传的视频文件',
        'video_file.file' => '上传的必须是视频文件',
        'video_file.fileExt' => '不支持的视频格式',
        'video_file.fileSize' => '视频文件大小超出限制',
        
        'document_file.require' => '请选择要上传的文档文件',
        'document_file.file' => '上传的必须是文档文件',
        'document_file.fileExt' => '不支持的文档格式',
        'document_file.fileSize' => '文档文件大小超出限制',
        
        'avatar_file.require' => '请选择要上传的头像文件',
        'avatar_file.file' => '上传的必须是图片文件',
        'avatar_file.fileExt' => '头像只支持jpg、png、gif、webp格式',
        'avatar_file.fileSize' => '头像文件大小不能超过2MB',
        
        'data.require' => 'Base64数据不能为空',
        'type.in' => '文件类型参数错误',
        
        'path.require' => '文件路径不能为空',
        'x.require' => 'X坐标不能为空',
        'x.integer' => 'X坐标必须是整数',
        'x.egt' => 'X坐标不能小于0',
        'y.require' => 'Y坐标不能为空',
        'y.integer' => 'Y坐标必须是整数',
        'y.egt' => 'Y坐标不能小于0',
        'width.require' => '宽度不能为空',
        'width.integer' => '宽度必须是整数',
        'width.gt' => '宽度必须大于0',
        'width.elt' => '宽度不能超过2048像素',
        'height.require' => '高度不能为空',
        'height.integer' => '高度必须是整数',
        'height.gt' => '高度必须大于0',
        'height.elt' => '高度不能超过2048像素',
        'quality.integer' => '质量参数必须是整数',
        'quality.between' => '质量参数必须在1-100之间'
    ];

    /**
     * 验证场景
     * @var array
     */
    protected $scene = [
        // 图片上传场景
        'image' => ['file'],
        
        // 视频上传场景
        'video' => ['video_file' => 'file'],
        
        // 文档上传场景
        'document' => ['document_file' => 'file'],
        
        // 头像上传场景
        'avatar' => ['avatar_file' => 'file'],
        
        // Base64上传场景
        'base64' => ['data', 'type'],
        
        // 图片裁剪场景
        'crop' => ['path', 'x', 'y', 'width', 'height', 'quality']
    ];

    /**
     * 图片上传场景验证规则
     * @return UploadValidate
     */
    public function sceneImage(): UploadValidate
    {
        return $this->only(['file'])
                   ->append('file', 'checkImageFile');
    }

    /**
     * 视频上传场景验证规则
     * @return UploadValidate
     */
    public function sceneVideo(): UploadValidate
    {
        return $this->only(['file'])
                   ->remove('file', 'fileExt|fileSize')
                   ->append('file', 'fileExt:mp4,avi,mov,wmv,flv,webm|fileSize:104857600|checkVideoFile');
    }

    /**
     * 文档上传场景验证规则
     * @return UploadValidate
     */
    public function sceneDocument(): UploadValidate
    {
        return $this->only(['file'])
                   ->remove('file', 'fileExt|fileSize')
                   ->append('file', 'fileExt:pdf,doc,docx,xls,xlsx,ppt,pptx,txt|fileSize:20971520|checkDocumentFile');
    }

    /**
     * 头像上传场景验证规则
     * @return UploadValidate
     */
    public function sceneAvatar(): UploadValidate
    {
        return $this->only(['file'])
                   ->remove('file', 'fileExt|fileSize')
                   ->append('file', 'fileExt:jpg,jpeg,png,gif,webp|fileSize:2097152|checkAvatarFile');
    }

    /**
     * Base64上传场景验证规则
     * @return UploadValidate
     */
    public function sceneBase64(): UploadValidate
    {
        return $this->only(['data', 'type']);
    }

    /**
     * 图片裁剪场景验证规则
     * @return UploadValidate
     */
    public function sceneCrop(): UploadValidate
    {
        return $this->only(['path', 'x', 'y', 'width', 'height', 'quality']);
    }

    /**
     * 自定义验证规则：检查图片文件
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkImageFile($value, $rule, array $data)
    {
        if (!$value instanceof \think\file\UploadedFile) {
            return '上传的必须是文件';
        }

        // 检查文件是否上传成功
        if (!$value->isValid()) {
            return '文件上传失败：' . $value->getError();
        }

        // 检查MIME类型
        $mimeType = $value->getOriginalMime();
        $allowedMimes = [
            'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 
            'image/webp', 'image/bmp'
        ];
        
        if (!in_array($mimeType, $allowedMimes)) {
            return '不支持的图片格式';
        }

        // 检查图片尺寸
        $imageInfo = getimagesize($value->getPathname());
        if ($imageInfo === false) {
            return '无效的图片文件';
        }

        $maxWidth = config('upload.image_max_width', 2048);
        $maxHeight = config('upload.image_max_height', 2048);
        
        if ($imageInfo[0] > $maxWidth || $imageInfo[1] > $maxHeight) {
            return "图片尺寸不能超过 {$maxWidth}x{$maxHeight} 像素";
        }

        return true;
    }

    /**
     * 自定义验证规则：检查视频文件
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkVideoFile($value, $rule, array $data)
    {
        if (!$value instanceof \think\file\UploadedFile) {
            return '上传的必须是文件';
        }

        // 检查文件是否上传成功
        if (!$value->isValid()) {
            return '文件上传失败：' . $value->getError();
        }

        // 检查MIME类型
        $mimeType = $value->getOriginalMime();
        $allowedMimes = [
            'video/mp4', 'video/avi', 'video/quicktime', 
            'video/x-ms-wmv', 'video/x-flv', 'video/webm'
        ];
        
        if (!in_array($mimeType, $allowedMimes)) {
            return '不支持的视频格式';
        }

        return true;
    }

    /**
     * 自定义验证规则：检查文档文件
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkDocumentFile($value, $rule, array $data)
    {
        if (!$value instanceof \think\file\UploadedFile) {
            return '上传的必须是文件';
        }

        // 检查文件是否上传成功
        if (!$value->isValid()) {
            return '文件上传失败：' . $value->getError();
        }

        // 检查MIME类型
        $mimeType = $value->getOriginalMime();
        $allowedMimes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'text/plain'
        ];
        
        if (!in_array($mimeType, $allowedMimes)) {
            return '不支持的文档格式';
        }

        return true;
    }

    /**
     * 自定义验证规则：检查头像文件
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkAvatarFile($value, $rule, array $data)
    {
        if (!$value instanceof \think\file\UploadedFile) {
            return '上传的必须是文件';
        }

        // 检查文件是否上传成功
        if (!$value->isValid()) {
            return '文件上传失败：' . $value->getError();
        }

        // 检查MIME类型
        $mimeType = $value->getOriginalMime();
        $allowedMimes = [
            'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'
        ];
        
        if (!in_array($mimeType, $allowedMimes)) {
            return '头像只支持jpg、png、gif、webp格式';
        }

        // 检查图片尺寸
        $imageInfo = getimagesize($value->getPathname());
        if ($imageInfo === false) {
            return '无效的图片文件';
        }

        // 头像建议为正方形
        $minSize = 50;
        $maxSize = 1024;
        
        if ($imageInfo[0] < $minSize || $imageInfo[1] < $minSize) {
            return "头像尺寸不能小于 {$minSize}x{$minSize} 像素";
        }
        
        if ($imageInfo[0] > $maxSize || $imageInfo[1] > $maxSize) {
            return "头像尺寸不能超过 {$maxSize}x{$maxSize} 像素";
        }

        return true;
    }

    /**
     * 自定义验证规则：检查Base64数据
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkBase64($value, $rule, array $data)
    {
        if (empty($value)) {
            return 'Base64数据不能为空';
        }

        // 检查Base64格式
        if (!preg_match('/^data:([^;]+);base64,(.+)$/', $value, $matches)) {
            return '无效的Base64数据格式';
        }

        $mimeType = $matches[1];
        $base64Data = $matches[2];

        // 验证Base64编码
        if (!base64_decode($base64Data, true)) {
            return 'Base64数据解码失败';
        }

        // 检查MIME类型
        $allowedMimes = [
            'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
            'video/mp4', 'video/avi', 'video/quicktime',
            'application/pdf', 'text/plain'
        ];

        if (!in_array($mimeType, $allowedMimes)) {
            return '不支持的文件类型';
        }

        // 检查文件大小
        $decodedSize = strlen(base64_decode($base64Data));
        $maxSize = 10 * 1024 * 1024; // 10MB

        if ($decodedSize > $maxSize) {
            return '文件大小不能超过10MB';
        }

        return true;
    }

    /**
     * 自定义验证规则：检查文件路径
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkFilePath($value, $rule, array $data)
    {
        if (empty($value)) {
            return '文件路径不能为空';
        }

        // 检查路径格式
        if (!preg_match('/^[a-zA-Z0-9\/_.-]+$/', $value)) {
            return '文件路径格式不正确';
        }

        // 检查文件是否存在
        $fullPath = public_path() . '/storage/' . $value;
        if (!file_exists($fullPath)) {
            return '文件不存在';
        }

        // 检查是否为图片文件
        $imageInfo = getimagesize($fullPath);
        if ($imageInfo === false) {
            return '只能裁剪图片文件';
        }

        return true;
    }

    /**
     * 自定义验证规则：检查文件扩展名
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkFileExtension($value, $rule, array $data)
    {
        if (!$value instanceof \think\file\UploadedFile) {
            return '上传的必须是文件';
        }

        $extension = strtolower($value->getOriginalExtension());
        $allowedExtensions = explode(',', $rule);

        if (!in_array($extension, $allowedExtensions)) {
            return '不支持的文件扩展名：' . $extension;
        }

        return true;
    }

    /**
     * 自定义验证规则：检查文件大小
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkFileSize($value, $rule, array $data)
    {
        if (!$value instanceof \think\file\UploadedFile) {
            return '上传的必须是文件';
        }

        $maxSize = (int)$rule;
        $fileSize = $value->getSize();

        if ($fileSize > $maxSize) {
            return '文件大小超出限制：' . $this->formatFileSize($maxSize);
        }

        return true;
    }

    /**
     * 格式化文件大小
     * @param int $size
     * @return string
     */
    protected function formatFileSize(int $size): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unit = 0;
        
        while ($size >= 1024 && $unit < count($units) - 1) {
            $size /= 1024;
            $unit++;
        }
        
        return round($size, 2) . ' ' . $units[$unit];
    }
}

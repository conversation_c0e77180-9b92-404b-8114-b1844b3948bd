<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 用户下拉菜单组件
-->

<template>
  <div class="user-dropdown">
    <el-dropdown trigger="click" @command="handleCommand">
      <div class="user-info">
        <el-avatar :size="32" :src="userStore.avatar" class="user-avatar">
          <el-icon><User /></el-icon>
        </el-avatar>
        <span v-if="!appStore.isMobile" class="user-name">{{ userStore.displayName }}</span>
        <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
      </div>
      
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="profile" :icon="User">
            <span>个人资料</span>
          </el-dropdown-item>
          
          <el-dropdown-item command="settings" :icon="Setting">
            <span>账户设置</span>
          </el-dropdown-item>
          
          <el-dropdown-item command="password" :icon="Lock">
            <span>修改密码</span>
          </el-dropdown-item>
          
          <el-dropdown-item divided command="logout" :icon="SwitchButton">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 个人资料对话框 -->
    <ProfileDialog v-model="showProfile" />
    
    <!-- 账户设置对话框 -->
    <SettingsDialog v-model="showSettings" />
    
    <!-- 修改密码对话框 -->
    <PasswordDialog v-model="showPassword" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore } from '@/store/modules/app'
import { useUserStore } from '@/store/modules/user'
import { User, Setting, Lock, SwitchButton, ArrowDown } from '@element-plus/icons-vue'
import ProfileDialog from './UserDropdown/ProfileDialog.vue'
import SettingsDialog from './UserDropdown/SettingsDialog.vue'
import PasswordDialog from './UserDropdown/PasswordDialog.vue'

const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()

// 对话框状态
const showProfile = ref(false)
const showSettings = ref(false)
const showPassword = ref(false)

/**
 * 处理下拉菜单命令
 */
const handleCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      showProfile.value = true
      break
      
    case 'settings':
      showSettings.value = true
      break
      
    case 'password':
      showPassword.value = true
      break
      
    case 'logout':
      await handleLogout()
      break
  }
}

/**
 * 处理退出登录
 */
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '退出确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await userStore.logout()
    router.push('/login')
    
  } catch (error) {
    // 用户取消退出
  }
}
</script>

<style lang="scss" scoped>
.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: var(--el-fill-color-light);
  }
}

.user-avatar {
  flex-shrink: 0;
  border: 2px solid var(--el-border-color-lighter);
  transition: border-color 0.3s ease;
  
  &:hover {
    border-color: var(--el-color-primary);
  }
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-icon {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  transition: transform 0.3s ease;
  
  .user-info:hover & {
    transform: rotate(180deg);
  }
}

// 下拉菜单样式
:deep(.el-dropdown-menu) {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--el-border-color-light);
  padding: 8px 0;
  
  .el-dropdown-menu__item {
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 4px;
    margin: 2px 8px;
    transition: all 0.3s ease;
    
    &:hover {
      background-color: var(--el-fill-color-light);
      color: var(--el-color-primary);
    }
    
    &.is-divided {
      border-top: 1px solid var(--el-border-color-lighter);
      margin-top: 8px;
      padding-top: 8px;
    }
    
    .el-icon {
      margin-right: 8px;
      font-size: 16px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .user-name {
    display: none;
  }
  
  .user-info {
    padding: 6px 8px;
    gap: 4px;
  }
}

// 暗色主题适配
:deep(.dark) {
  .user-info {
    &:hover {
      background-color: var(--el-fill-color);
    }
  }
  
  .el-dropdown-menu {
    background-color: var(--el-bg-color-overlay);
    border-color: var(--el-border-color);
    
    .el-dropdown-menu__item {
      &:hover {
        background-color: var(--el-fill-color);
      }
      
      &.is-divided {
        border-top-color: var(--el-border-color);
      }
    }
  }
}
</style>

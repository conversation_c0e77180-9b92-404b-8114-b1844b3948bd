{"name": "qiyediy-frontend", "version": "1.0.0", "description": "QiyeDIY企业建站系统 - 前端展示", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint . --fix", "type-check": "nuxt typecheck"}, "devDependencies": {"@nuxt/devtools": "latest", "@nuxt/eslint-config": "^0.2.0", "@nuxt/image": "^1.1.0", "@nuxt/ui": "^2.11.1", "@nuxtjs/tailwindcss": "^6.8.4", "@types/node": "^20.10.4", "eslint": "^8.55.0", "nuxt": "^3.8.4", "typescript": "^5.3.3", "vue": "^3.4.0", "vue-router": "^4.2.5"}, "dependencies": {"@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "@nuxtjs/google-fonts": "^3.0.2", "@pinia/nuxt": "^0.5.1", "@vueuse/core": "^10.6.1", "@vueuse/nuxt": "^10.6.1", "dayjs": "^1.11.10", "framer-motion": "^10.16.16", "lodash-es": "^4.17.21", "pinia": "^2.1.7", "swiper": "^11.0.5", "vue-toastification": "^2.0.0-rc.5", "zod": "^3.22.4"}, "keywords": ["nuxt3", "vue3", "typescript", "tailwindcss", "ssr", "website", "frontend"], "author": {"name": "韩总", "email": "<EMAIL>", "url": "https://www.qiyediy.com"}, "license": "MIT", "homepage": "https://www.qiyediy.com", "repository": {"type": "git", "url": "https://github.com/qiyediy/frontend.git"}, "bugs": {"url": "https://github.com/qiyediy/frontend/issues"}}
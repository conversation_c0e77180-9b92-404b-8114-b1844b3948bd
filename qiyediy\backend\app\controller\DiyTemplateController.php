<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * QiyeDIY企业建站系统 - DIY模板管理控制器
 */

declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\model\DiyTemplate;
use app\service\DiyTemplateService;
use app\validate\DiyTemplateValidate;
use think\Response;

/**
 * DIY模板管理控制器
 */
class DiyTemplateController extends BaseController
{
    protected DiyTemplateService $templateService;

    public function __construct()
    {
        parent::__construct();
        $this->templateService = new DiyTemplateService();
    }

    /**
     * 模板列表
     * @return Response
     */
    public function index(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.template')) {
                return $this->error('没有权限访问', 403);
            }

            $params = $this->getPaginateParams();
            $result = $this->templateService->getList($params);

            // 记录操作日志
            $this->logOperation('view', 'diy_template_list');

            return $this->paginate($result);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取所有模板（用于下拉选择）
     * @return Response
     */
    public function all(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.view')) {
                return $this->error('没有权限访问', 403);
            }

            $templates = $this->templateService->getAll();

            return $this->success($templates);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 模板详情
     * @param int $id 模板ID
     * @return Response
     */
    public function read(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.template')) {
                return $this->error('没有权限访问', 403);
            }

            $template = $this->templateService->getById($id);
            if (!$template) {
                return $this->error('模板不存在', 404);
            }

            // 记录操作日志
            $this->logOperation('view', 'diy_template', $id);

            return $this->success($template);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 创建模板
     * @return Response
     */
    public function save(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.template')) {
                return $this->error('没有权限创建模板', 403);
            }

            $data = $this->post();

            // 数据验证
            $this->validate($data, DiyTemplateValidate::class . '.create');

            // 创建模板
            $template = $this->templateService->create($data);

            // 记录操作日志
            $this->logOperation('create', 'diy_template', $template->id, $data);

            return $this->success($template, '模板创建成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 更新模板
     * @param int $id 模板ID
     * @return Response
     */
    public function update(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.template')) {
                return $this->error('没有权限更新模板', 403);
            }

            $data = $this->post();

            // 数据验证
            $this->validate($data, DiyTemplateValidate::class . '.update');

            // 更新模板
            $template = $this->templateService->update($id, $data);

            // 记录操作日志
            $this->logOperation('update', 'diy_template', $id, $data);

            return $this->success($template, '模板更新成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除模板
     * @param int $id 模板ID
     * @return Response
     */
    public function delete(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.template')) {
                return $this->error('没有权限删除模板', 403);
            }

            // 删除模板
            $this->templateService->delete($id);

            // 记录操作日志
            $this->logOperation('delete', 'diy_template', $id);

            return $this->success([], '模板删除成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 批量删除模板
     * @return Response
     */
    public function batchDelete(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.template')) {
                return $this->error('没有权限删除模板', 403);
            }

            $ids = $this->post('ids', []);
            $this->validateBatchDelete($ids);

            // 批量删除
            $count = $this->templateService->batchDelete($ids);

            // 记录操作日志
            $this->logOperation('batch_delete', 'diy_template', null, ['ids' => $ids, 'count' => $count]);

            return $this->success(['count' => $count], "成功删除 {$count} 个模板");

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 启用模板
     * @param int $id 模板ID
     * @return Response
     */
    public function enable(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.template')) {
                return $this->error('没有权限操作', 403);
            }

            $this->templateService->enable($id);

            // 记录操作日志
            $this->logOperation('enable', 'diy_template', $id);

            return $this->success([], '模板已启用');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 禁用模板
     * @param int $id 模板ID
     * @return Response
     */
    public function disable(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.template')) {
                return $this->error('没有权限操作', 403);
            }

            $this->templateService->disable($id);

            // 记录操作日志
            $this->logOperation('disable', 'diy_template', $id);

            return $this->success([], '模板已禁用');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 复制模板
     * @param int $id 模板ID
     * @return Response
     */
    public function copy(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.template')) {
                return $this->error('没有权限复制模板', 403);
            }

            $data = $this->post();
            $template = $this->templateService->copy($id, $data);

            // 记录操作日志
            $this->logOperation('copy', 'diy_template', $template->id, ['source_id' => $id]);

            return $this->success($template, '模板复制成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 设置默认模板
     * @param int $id 模板ID
     * @return Response
     */
    public function setDefault(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.template')) {
                return $this->error('没有权限操作', 403);
            }

            $this->templateService->setDefault($id);

            // 记录操作日志
            $this->logOperation('set_default', 'diy_template', $id);

            return $this->success([], '默认模板设置成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取模板统计
     * @return Response
     */
    public function statistics(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.template')) {
                return $this->error('没有权限访问', 403);
            }

            $stats = $this->templateService->getStatistics();

            return $this->success($stats);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 导出模板
     * @return Response
     */
    public function export(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.template')) {
                return $this->error('没有权限导出', 403);
            }

            $params = $this->get();
            $filePath = $this->templateService->export($params);

            // 记录操作日志
            $this->logOperation('export', 'diy_template_list', null, $params);

            return $this->success(['file_path' => $filePath], '导出成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 导入模板
     * @return Response
     */
    public function import(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.template')) {
                return $this->error('没有权限导入', 403);
            }

            // 处理文件上传
            $fileInfo = $this->handleUpload('file', [
                'size' => 50 * 1024 * 1024, // 50MB
                'ext' => 'json,zip',
                'type' => 'import'
            ]);

            // 导入模板
            $result = $this->templateService->import($fileInfo['path']);

            // 记录操作日志
            $this->logOperation('import', 'diy_template_list', null, [
                'file' => $fileInfo['filename'],
                'result' => $result
            ]);

            return $this->success($result, '导入完成');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 安装模板
     * @return Response
     */
    public function install(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.template')) {
                return $this->error('没有权限安装模板', 403);
            }

            $data = $this->post();
            $template = $this->templateService->install($data);

            // 记录操作日志
            $this->logOperation('install', 'diy_template', $template->id, $data);

            return $this->success($template, '模板安装成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 卸载模板
     * @param int $id 模板ID
     * @return Response
     */
    public function uninstall(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.template')) {
                return $this->error('没有权限卸载模板', 403);
            }

            $this->templateService->uninstall($id);

            // 记录操作日志
            $this->logOperation('uninstall', 'diy_template', $id);

            return $this->success([], '模板卸载成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}

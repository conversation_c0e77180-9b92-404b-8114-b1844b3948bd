{"version": 3, "file": "ja.min.mjs", "sources": ["../../../../packages/locale/lang/ja.ts"], "sourcesContent": ["export default {\n  name: 'ja',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: 'クリア',\n    },\n    datepicker: {\n      now: '現在',\n      today: '今日',\n      cancel: 'キャンセル',\n      clear: 'クリア',\n      confirm: 'OK',\n      selectDate: '日付を選択',\n      selectTime: '時間を選択',\n      startDate: '開始日',\n      startTime: '開始時間',\n      endDate: '終了日',\n      endTime: '終了時間',\n      prevYear: '前年',\n      nextYear: '翌年',\n      prevMonth: '前月',\n      nextMonth: '翌月',\n      year: '年',\n      month1: '1月',\n      month2: '2月',\n      month3: '3月',\n      month4: '4月',\n      month5: '5月',\n      month6: '6月',\n      month7: '7月',\n      month8: '8月',\n      month9: '9月',\n      month10: '10月',\n      month11: '11月',\n      month12: '12月',\n      // week: '週次',\n      weeks: {\n        sun: '日',\n        mon: '月',\n        tue: '火',\n        wed: '水',\n        thu: '木',\n        fri: '金',\n        sat: '土',\n      },\n      months: {\n        jan: '1月',\n        feb: '2月',\n        mar: '3月',\n        apr: '4月',\n        may: '5月',\n        jun: '6月',\n        jul: '7月',\n        aug: '8月',\n        sep: '9月',\n        oct: '10月',\n        nov: '11月',\n        dec: '12月',\n      },\n    },\n    select: {\n      loading: 'ロード中',\n      noMatch: 'データなし',\n      noData: 'データなし',\n      placeholder: '選択してください',\n    },\n    mention: {\n      loading: 'ロード中',\n    },\n    cascader: {\n      noMatch: 'データなし',\n      loading: 'ロード中',\n      placeholder: '選択してください',\n      noData: 'データなし',\n    },\n    pagination: {\n      goto: '',\n      pagesize: '件/ページ',\n      total: '総計 {total} 件',\n      pageClassifier: 'ページ目へ',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'メッセージ',\n      confirm: 'OK',\n      cancel: 'キャンセル',\n      error: '正しくない入力',\n    },\n    upload: {\n      deleteTip: 'Delキーを押して削除する',\n      delete: '削除する',\n      preview: 'プレビュー',\n      continue: '続行する',\n    },\n    table: {\n      emptyText: 'データなし',\n      confirmFilter: '確認',\n      resetFilter: '初期化',\n      clearFilter: 'すべて',\n      sumText: '合計',\n    },\n    tour: {\n      next: '次へ',\n      previous: '前へ',\n      finish: 'ツアー終了',\n    },\n    tree: {\n      emptyText: 'データなし',\n    },\n    transfer: {\n      noMatch: 'データなし',\n      noData: 'データなし',\n      titles: ['リスト 1', 'リスト 2'],\n      filterPlaceholder: 'キーワードを入力',\n      noCheckedFormat: '総計 {total} 件',\n      hasCheckedFormat: '{checked}/{total} を選択した',\n    },\n    image: {\n      error: '失敗',\n    },\n    pageHeader: {\n      title: '戻る',\n    },\n    popconfirm: {\n      confirmButtonText: 'はい',\n      cancelButtonText: 'いいえ',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,gCAAgC,CAAC,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,gCAAgC,CAAC,UAAU,CAAC,gCAAgC,CAAC,SAAS,CAAC,oBAAoB,CAAC,SAAS,CAAC,0BAA0B,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,0BAA0B,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,OAAO,CAAC,gCAAgC,CAAC,MAAM,CAAC,gCAAgC,CAAC,WAAW,CAAC,kDAAkD,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,OAAO,CAAC,0BAA0B,CAAC,WAAW,CAAC,kDAAkD,CAAC,MAAM,CAAC,gCAAgC,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,2BAA2B,CAAC,KAAK,CAAC,6BAA6B,CAAC,cAAc,CAAC,gCAAgC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,gCAAgC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,gCAAgC,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,iEAAiE,CAAC,MAAM,CAAC,0BAA0B,CAAC,OAAO,CAAC,gCAAgC,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,gCAAgC,CAAC,aAAa,CAAC,cAAc,CAAC,WAAW,CAAC,oBAAoB,CAAC,WAAW,CAAC,oBAAoB,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,gCAAgC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,CAAC,iBAAiB,CAAC,kDAAkD,CAAC,eAAe,CAAC,6BAA6B,CAAC,gBAAgB,CAAC,kDAAkD,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,cAAc,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}
/* 页面设计器专用样式 */
.page-designer-container {
    max-width: 100%;
    margin: 0;
    padding: 0;
    background: rgba(15, 15, 15, 0.95);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 20px;
    backdrop-filter: blur(25px);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.4),
        0 0 40px rgba(120, 119, 198, 0.1);
    overflow: hidden;
    position: relative;
    margin-bottom: 30px;
    height: calc(100vh - 120px);
    display: flex;
    flex-direction: column;
}

.page-designer-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
        rgba(120, 119, 198, 0.8),
        rgba(255, 119, 198, 0.8),
        rgba(120, 219, 255, 0.8));
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* 设计器头部 */
.designer-header {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.15) 0%,
        rgba(255, 119, 198, 0.1) 50%,
        rgba(120, 219, 255, 0.15) 100%);
    padding: 20px 30px;
    border-bottom: 1px solid rgba(120, 119, 198, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.designer-title h1 {
    font-family: 'Orbitron', monospace;
    font-size: 24px;
    font-weight: 700;
    color: #fff;
    margin: 0 0 5px 0;
    text-shadow: 0 0 20px rgba(120, 119, 198, 0.6);
}

.template-info {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
}

.template-type {
    background: rgba(120, 119, 198, 0.3);
    padding: 2px 8px;
    border-radius: 12px;
    margin-right: 10px;
}

.template-updated {
    color: rgba(255, 255, 255, 0.5);
}

.designer-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

/* 设备切换按钮 */
.device-buttons {
    display: flex;
    gap: 5px;
    background: rgba(0, 0, 0, 0.3);
    padding: 5px;
    border-radius: 8px;
}

.device-btn {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.device-btn:hover {
    color: #fff;
    background: rgba(120, 119, 198, 0.3);
}

.device-btn.active {
    color: #fff;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8) 0%,
        rgba(255, 119, 198, 0.8) 100%);
}

/* 工具按钮 */
.tool-buttons {
    display: flex;
    gap: 10px;
}

.tool-btn {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(120, 119, 198, 0.3);
    color: rgba(255, 255, 255, 0.7);
    padding: 10px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.tool-btn:hover {
    color: #fff;
    background: rgba(120, 119, 198, 0.3);
    border-color: rgba(120, 119, 198, 0.6);
}

/* 主要操作按钮 */
.main-actions {
    display: flex;
    gap: 10px;
}

.btn-primary,
.btn-secondary {
    position: relative;
    z-index: 10;
    padding: 10px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8) 0%,
        rgba(255, 119, 198, 0.8) 100%);
    color: #fff;
    border: 1px solid rgba(120, 119, 198, 0.5);
}

.btn-secondary {
    background: linear-gradient(135deg,
        rgba(107, 114, 128, 0.8) 0%,
        rgba(75, 85, 99, 0.8) 100%);
    color: #fff;
    border: 1px solid rgba(107, 114, 128, 0.5);
}

.btn-primary:hover,
.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* 设计器工作区 */
.designer-workspace {
    flex: 1;
    overflow: hidden;
    position: relative;
    display: flex;
    height: 100%;
}

/* 左侧面板 */
.panel__left {
    width: 250px;
    background: rgba(20, 20, 20, 0.95);
    border-right: 1px solid rgba(120, 119, 198, 0.2);
    overflow-y: auto;
}

/* 中央编辑器 - 完全填充 */
.gjs-editor {
    flex: 1;
    height: 100%;
    width: auto;
    display: flex !important;
    flex-direction: row !important;
}

/* 确保编辑器主容器填充 */
#gjs {
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: row !important;
    min-height: auto !important;
}

/* 编辑器面板容器 */
.gjs-pn-panels {
    display: flex !important;
    width: 100% !important;
    height: 100% !important;
}

/* 中央画布容器 */
.gjs-cv-canvas-container {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
}

/* 右侧面板 */
.panel__right {
    width: 300px;
    background: rgba(20, 20, 20, 0.95);
    border-left: 1px solid rgba(120, 119, 198, 0.2);
    overflow-y: auto;
}

/* 面板切换器 */
.panel__switcher {
    display: flex;
    border-bottom: 1px solid rgba(120, 119, 198, 0.2);
}

.panel__switcher button {
    flex: 1;
    padding: 10px;
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
}

.panel__switcher button:hover,
.panel__switcher button.active {
    background: rgba(120, 119, 198, 0.3);
    color: #fff;
}

/* 容器样式 */
.blocks-container,
.layers-container,
.styles-container,
.traits-container {
    padding: 15px;
    min-height: 200px;
}

/* 确保面板可见 */
.panel__left,
.panel__right {
    flex-shrink: 0;
}

/* GrapesJS 深色主题优化 */
.gjs-editor {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    background-color: #2d3748;
    color: #e2e8f0;
}

/* 左侧面板样式 */
.gjs-pn-panels {
    background-color: #2d3748;
}

.gjs-pn-panel {
    background-color: #2d3748;
    border-right: 1px solid #4a5568;
}

/* 块管理器样式 */
.gjs-blocks-c {
    background-color: #2d3748;
    padding: 10px;
}

.gjs-block-categories {
    background-color: #2d3748;
}

.gjs-block-category {
    background-color: #2d3748;
    border-bottom: 1px solid #4a5568;
}

.gjs-block-category .gjs-caret-icon {
    color: #e2e8f0;
}

.gjs-block-category .gjs-title {
    color: #e2e8f0;
    font-weight: 500;
    padding: 10px;
}

.gjs-blocks-cs {
    background-color: #2d3748;
}

.gjs-block {
    background-color: #4a5568;
    border: 1px solid #718096;
    color: #e2e8f0;
    margin: 5px;
    padding: 10px;
    border-radius: 4px;
    cursor: pointer;
}

.gjs-block:hover {
    background-color: #718096;
    border-color: #a0aec0;
}

.gjs-block-label {
    color: #e2e8f0;
    font-size: 11px;
    text-align: center;
}

/* 右侧面板样式 */
.gjs-pn-panel.gjs-pn-views {
    background-color: #2d3748;
    border-left: 1px solid #4a5568;
}

/* 图层管理器 */
.gjs-layers-c {
    background-color: #2d3748;
}

.gjs-layer {
    background-color: #2d3748;
    color: #e2e8f0;
    border-bottom: 1px solid #4a5568;
}

.gjs-layer:hover {
    background-color: #4a5568;
}

.gjs-layer.gjs-selected {
    background-color: #718096;
}

/* 样式管理器 */
.gjs-sm-sectors {
    background-color: #2d3748;
}

.gjs-sm-sector {
    background-color: #2d3748;
    border-bottom: 1px solid #4a5568;
}

.gjs-sm-sector .gjs-sm-title {
    background-color: #4a5568;
    color: #e2e8f0;
    padding: 10px;
    font-weight: 500;
}

.gjs-sm-properties {
    background-color: #2d3748;
}

.gjs-sm-property {
    background-color: #2d3748;
    color: #e2e8f0;
    border-bottom: 1px solid #4a5568;
}

.gjs-sm-property .gjs-sm-label {
    color: #e2e8f0;
}

.gjs-field {
    background-color: #4a5568;
    border: 1px solid #718096;
    color: #e2e8f0;
}

.gjs-field:focus {
    border-color: #a0aec0;
    background-color: #718096;
}

/* 特征管理器 */
.gjs-trt-traits {
    background-color: #2d3748;
}

.gjs-trt-trait {
    background-color: #2d3748;
    color: #e2e8f0;
    border-bottom: 1px solid #4a5568;
}

.gjs-trt-trait .gjs-label {
    color: #e2e8f0;
}

/* 中央画布区域 - 完全填充 */
.gjs-cv-canvas {
    background-color: #f7fafc;
    width: 100% !important;
    height: 100% !important;
    flex: 1 !important;
}

.gjs-cv-canvas__frames {
    width: 100% !important;
    height: 100% !important;
}

.gjs-frame-wrapper {
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    justify-content: stretch !important;
    align-items: stretch !important;
    padding: 0 !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
}

.gjs-frame {
    border: none !important;
    width: 100% !important;
    max-width: none !important;
    height: 100% !important;
    min-height: 100% !important;
    background-color: #ffffff;
    flex: 1 !important;
}

/* 画布工具栏 - 隐藏 */
.gjs-cv-canvas__tools {
    display: none !important;
}

/* 画布内容区域 - 完全填充 */
.gjs-cv-canvas__frames {
    flex: 1 !important;
    width: 100% !important;
    height: 100% !important;
    overflow: hidden !important;
    background-color: #f7fafc !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* 画布容器本身 */
.gjs-cv-canvas {
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
    height: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
}

/* 工具栏按钮 */
.gjs-pn-buttons {
    background-color: #2d3748;
}

.gjs-pn-btn {
    background-color: #4a5568;
    color: #e2e8f0;
    border: 1px solid #718096;
}

.gjs-pn-btn:hover {
    background-color: #718096;
}

.gjs-pn-btn.gjs-pn-active {
    background-color: #718096;
    border-color: #a0aec0;
}

/* 确保编辑器容器正确显示 */
#gjs {
    min-height: 500px;
}

/* 消息提示样式 */
.message-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 4px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    animation: slideIn 0.3s ease-out;
}

.message-toast.success {
    background-color: #28a745;
}

.message-toast.error {
    background-color: #dc3545;
}

.message-toast.info {
    background-color: #17a2b8;
}

.message-toast.warning {
    background-color: #ffc107;
    color: #212529;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 加载动画样式 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 模态框和弹窗样式 */
.gjs-mdl-container {
    background-color: rgba(45, 55, 72, 0.8);
}

.gjs-mdl-content {
    background-color: #2d3748;
    border: 1px solid #4a5568;
    color: #e2e8f0;
}

.gjs-mdl-header {
    background-color: #4a5568;
    border-bottom: 1px solid #718096;
    color: #e2e8f0;
}

.gjs-mdl-title {
    color: #e2e8f0;
}

.gjs-mdl-close {
    color: #e2e8f0;
}

.gjs-mdl-close:hover {
    color: #a0aec0;
}

/* 选择器样式 */
.gjs-clm-tags {
    background-color: #2d3748;
}

.gjs-clm-tag {
    background-color: #4a5568;
    color: #e2e8f0;
    border: 1px solid #718096;
}

.gjs-clm-tag:hover {
    background-color: #718096;
}

/* 设备切换按钮 */
.gjs-pn-devices-c {
    background-color: #2d3748;
}

.gjs-pn-device {
    background-color: #4a5568;
    color: #e2e8f0;
    border: 1px solid #718096;
}

.gjs-pn-device:hover {
    background-color: #718096;
}

.gjs-pn-device.gjs-pn-active {
    background-color: #718096;
    border-color: #a0aec0;
}

/* 代码编辑器 */
.gjs-cm-editor {
    background-color: #2d3748;
    color: #e2e8f0;
}

.CodeMirror {
    background-color: #2d3748 !important;
    color: #e2e8f0 !important;
}

.CodeMirror-gutters {
    background-color: #4a5568 !important;
    border-right: 1px solid #718096 !important;
}

.CodeMirror-linenumber {
    color: #a0aec0 !important;
}

/* 滚动条样式 */
.gjs-blocks-c::-webkit-scrollbar,
.gjs-layers-c::-webkit-scrollbar,
.gjs-sm-sectors::-webkit-scrollbar,
.gjs-trt-traits::-webkit-scrollbar {
    width: 8px;
}

.gjs-blocks-c::-webkit-scrollbar-track,
.gjs-layers-c::-webkit-scrollbar-track,
.gjs-sm-sectors::-webkit-scrollbar-track,
.gjs-trt-traits::-webkit-scrollbar-track {
    background-color: #2d3748;
}

.gjs-blocks-c::-webkit-scrollbar-thumb,
.gjs-layers-c::-webkit-scrollbar-thumb,
.gjs-sm-sectors::-webkit-scrollbar-thumb,
.gjs-trt-traits::-webkit-scrollbar-thumb {
    background-color: #4a5568;
    border-radius: 4px;
}

.gjs-blocks-c::-webkit-scrollbar-thumb:hover,
.gjs-layers-c::-webkit-scrollbar-thumb:hover,
.gjs-sm-sectors::-webkit-scrollbar-thumb:hover,
.gjs-trt-traits::-webkit-scrollbar-thumb:hover {
    background-color: #718096;
}

/* 输入框和表单元素 */
.gjs-field-color,
.gjs-field-select,
.gjs-field-number,
.gjs-field-text {
    background-color: #4a5568 !important;
    border: 1px solid #718096 !important;
    color: #e2e8f0 !important;
}

.gjs-field-color:focus,
.gjs-field-select:focus,
.gjs-field-number:focus,
.gjs-field-text:focus {
    border-color: #a0aec0 !important;
    background-color: #718096 !important;
}

/* 下拉菜单 */
select.gjs-field {
    background-color: #4a5568 !important;
    color: #e2e8f0 !important;
    border: 1px solid #718096 !important;
}

/* 复选框和单选框 */
input[type="checkbox"],
input[type="radio"] {
    background-color: #4a5568;
    border: 1px solid #718096;
}

/* 按钮样式统一 */
.gjs-btn-prim {
    background-color: #4a5568;
    color: #e2e8f0;
    border: 1px solid #718096;
}

.gjs-btn-prim:hover {
    background-color: #718096;
}

/* 面板标题 */
.gjs-pn-panel .gjs-pn-btn {
    background-color: #4a5568;
    color: #e2e8f0;
    border-bottom: 1px solid #718096;
}

.gjs-pn-panel .gjs-pn-btn.gjs-pn-active {
    background-color: #718096;
}

/* 确保所有文本颜色正确 */
.gjs-editor * {
    color: inherit;
}

/* 图标颜色 */
.gjs-pn-btn i,
.gjs-block i,
.gjs-layer i {
    color: #e2e8f0;
}

/* 分隔线 */
.gjs-separator {
    background-color: #4a5568;
}

/* 工具提示 */
.gjs-tooltip {
    background-color: #2d3748;
    color: #e2e8f0;
    border: 1px solid #4a5568;
}

/* 右键菜单 */
.gjs-context-menu {
    background-color: #2d3748;
    border: 1px solid #4a5568;
}

.gjs-context-menu-item {
    color: #e2e8f0;
}

.gjs-context-menu-item:hover {
    background-color: #4a5568;
}

/* 拖拽指示器 */
.gjs-dashed {
    border-color: #718096 !important;
}

/* 选中元素边框 */
.gjs-selected {
    outline: 2px solid #a0aec0 !important;
}

/* 悬停元素边框 */
.gjs-hovered {
    outline: 1px solid #718096 !important;
}

/* 确保画布背景保持白色 */
.gjs-cv-canvas .gjs-frame {
    background-color: #ffffff;
}

/* 面板头部 */
.gjs-pn-panel-header {
    background-color: #4a5568;
    color: #e2e8f0;
    border-bottom: 1px solid #718096;
}

/* 搜索框 */
.gjs-search {
    background-color: #4a5568;
    border: 1px solid #718096;
    color: #e2e8f0;
}

.gjs-search:focus {
    border-color: #a0aec0;
    background-color: #718096;
}

/* 标签页 */
.gjs-pn-tabs {
    background-color: #2d3748;
    border-bottom: 1px solid #4a5568;
}

.gjs-pn-tab {
    background-color: #4a5568;
    color: #e2e8f0;
    border: 1px solid #718096;
}

.gjs-pn-tab.gjs-pn-active {
    background-color: #718096;
    border-color: #a0aec0;
}

/* 确保所有面板内容区域背景一致 */
.gjs-pn-panel .gjs-pn-panel-content {
    background-color: #2d3748;
}

/* 修复可能的白色背景 */
.gjs-editor .gjs-pn-panel,
.gjs-editor .gjs-blocks-c,
.gjs-editor .gjs-layers-c,
.gjs-editor .gjs-sm-sectors,
.gjs-editor .gjs-trt-traits {
    background-color: #2d3748 !important;
}

/* GrapesJS 主色调配置 */
:root {
    --gjs-primary-color: #2d3748;
    --gjs-secondary-color: #4a5568;
    --gjs-tertiary-color: #718096;
    --gjs-quaternary-color: #a0aec0;
    --gjs-text-color: #e2e8f0;
    --gjs-border-color: #4a5568;
    --gjs-canvas-bg: #f7fafc;
}

/* 使用CSS变量应用主色调 */
.gjs-editor {
    background-color: var(--gjs-primary-color) !important;
    color: var(--gjs-text-color) !important;
}

.gjs-pn-panels,
.gjs-pn-panel,
.gjs-blocks-c,
.gjs-block-categories,
.gjs-block-category,
.gjs-blocks-cs,
.gjs-layers-c,
.gjs-sm-sectors,
.gjs-sm-sector,
.gjs-sm-properties,
.gjs-trt-traits {
    background-color: var(--gjs-primary-color) !important;
}

.gjs-block,
.gjs-pn-btn,
.gjs-sm-sector .gjs-sm-title,
.gjs-field {
    background-color: var(--gjs-secondary-color) !important;
    border-color: var(--gjs-tertiary-color) !important;
    color: var(--gjs-text-color) !important;
}

.gjs-block:hover,
.gjs-pn-btn:hover,
.gjs-layer:hover,
.gjs-field:focus {
    background-color: var(--gjs-tertiary-color) !important;
}

.gjs-cv-canvas {
    background-color: var(--gjs-canvas-bg) !important;
}

/* 确保中央编辑区域完全填充 */
.gjs-cv-canvas,
.gjs-cv-canvas__frames,
.gjs-frame-wrapper {
    width: 100% !important;
    height: 100% !important;
}

.gjs-frame-wrapper {
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    align-items: stretch !important;
    justify-content: stretch !important;
    position: relative !important;
    top: 0 !important;
    left: 0 !important;
}

.gjs-frame {
    flex: 1 !important;
    width: 100% !important;
    height: 100% !important;
    border: none !important;
    margin: 0 !important;
    position: relative !important;
    top: 0 !important;
    left: 0 !important;
}

/* 隐藏不必要的工具栏 */
.gjs-cv-canvas__head,
.gjs-toolbar,
.gjs-cv-tools {
    display: none !important;
}

/* GrapesJS 编辑器样式调整 */
#gjs {
    height: 100% !important;
    border: none;
    border-radius: 0;
    overflow: hidden;
}

/* 自定义 GrapesJS 面板样式 */
.gjs-pn-panels {
    background: rgba(20, 20, 20, 0.95);
    border-right: 1px solid rgba(120, 119, 198, 0.2);
}

.gjs-pn-panel {
    background: transparent;
}

.gjs-pn-btn {
    background: rgba(120, 119, 198, 0.1);
    border: 1px solid rgba(120, 119, 198, 0.3);
    color: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

.gjs-pn-btn:hover,
.gjs-pn-btn.gjs-pn-active {
    background: rgba(120, 119, 198, 0.3);
    color: #fff;
}

/* 画布区域 */
.gjs-cv-canvas {
    background: #f8f9fa;
}

/* 块管理器样式 */
.gjs-blocks-c {
    background: rgba(20, 20, 20, 0.95);
    border-right: 1px solid rgba(120, 119, 198, 0.2);
}

.gjs-block {
    background: rgba(120, 119, 198, 0.1);
    border: 1px solid rgba(120, 119, 198, 0.3);
    color: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

.gjs-block:hover {
    background: rgba(120, 119, 198, 0.3);
    color: #fff;
    transform: translateY(-2px);
}

/* 样式管理器 */
.gjs-sm-sectors {
    background: rgba(20, 20, 20, 0.95);
}

.gjs-sm-sector {
    background: transparent;
    border-bottom: 1px solid rgba(120, 119, 198, 0.2);
}

.gjs-sm-title {
    background: rgba(120, 119, 198, 0.1);
    color: rgba(255, 255, 255, 0.9);
    border-bottom: 1px solid rgba(120, 119, 198, 0.3);
}

/* 图层管理器 */
.gjs-lm-layers {
    background: rgba(20, 20, 20, 0.95);
}

.gjs-lm-layer {
    background: transparent;
    color: rgba(255, 255, 255, 0.8);
    border-bottom: 1px solid rgba(120, 119, 198, 0.1);
}

.gjs-lm-layer:hover,
.gjs-lm-layer.gjs-lm-layer-sel {
    background: rgba(120, 119, 198, 0.2);
    color: #fff;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .designer-actions {
        gap: 15px;
    }
    
    .device-buttons {
        display: none;
    }
}

@media (max-width: 768px) {
    .designer-header {
        flex-direction: column;
        gap: 15px;
        padding: 15px 20px;
    }
    
    .designer-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .tool-buttons {
        display: none;
    }
}

/* 加载动画 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(120, 119, 198, 0.3);
    border-top: 3px solid rgba(120, 119, 198, 0.8);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 消息提示样式 */
.message-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    color: #fff;
    font-weight: 600;
    z-index: 10000;
    animation: slideIn 0.3s ease;
}

.message-toast.success {
    background: linear-gradient(135deg,
        rgba(34, 197, 94, 0.9) 0%,
        rgba(22, 163, 74, 0.9) 100%);
}

.message-toast.error {
    background: linear-gradient(135deg,
        rgba(239, 68, 68, 0.9) 0%,
        rgba(220, 38, 38, 0.9) 100%);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

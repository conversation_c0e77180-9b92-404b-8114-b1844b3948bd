<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 认证服务
 */

declare(strict_types=1);

namespace app\service;

use app\model\User;
use app\model\Role;
use think\facade\Cache;
use think\facade\Config;

/**
 * 认证服务类
 */
class AuthService
{
    /**
     * 用户登录
     * @param string $username 用户名或邮箱
     * @param string $password 密码
     * @return array
     * @throws \Exception
     */
    public function login(string $username, string $password): array
    {
        // 查找用户（支持用户名或邮箱登录）
        $user = User::where(function ($query) use ($username) {
            $query->where('username', $username)
                  ->whereOr('email', $username);
        })->find();

        if (!$user) {
            throw new \Exception('用户不存在');
        }

        // 检查用户状态
        if ($user->status === User::STATUS_DISABLED) {
            throw new \Exception('用户已被禁用');
        }

        // 验证密码
        if (!$user->verifyPassword($password)) {
            throw new \Exception('密码错误');
        }

        // 更新登录信息
        $user->updateLoginInfo();

        // 生成Token
        $token = $this->generateToken($user);

        // 获取用户权限
        $permissions = $user->getAllPermissions();

        return [
            'token' => $token,
            'user' => $user->toArray(),
            'permissions' => $permissions,
            'expires_in' => Config::get('jwt.ttl', 7200)
        ];
    }

    /**
     * 用户注册
     * @param array $data 注册数据
     * @return array
     * @throws \Exception
     */
    public function register(array $data): array
    {
        // 检查用户名是否存在
        if (User::where('username', $data['username'])->exists()) {
            throw new \Exception('用户名已存在');
        }

        // 检查邮箱是否存在
        if (User::where('email', $data['email'])->exists()) {
            throw new \Exception('邮箱已存在');
        }

        // 检查手机号是否存在
        if (!empty($data['phone']) && User::where('phone', $data['phone'])->exists()) {
            throw new \Exception('手机号已存在');
        }

        // 创建用户
        $userData = [
            'username' => $data['username'],
            'email' => $data['email'],
            'password' => $data['password'],
            'real_name' => $data['real_name'] ?? '',
            'phone' => $data['phone'] ?? '',
            'status' => User::STATUS_ENABLED
        ];

        $user = User::create($userData);

        // 分配默认角色
        $defaultRole = Role::getDefaultRole();
        if ($defaultRole) {
            $user->assignRoles([$defaultRole->id]);
        }

        // 生成Token
        $token = $this->generateToken($user);

        // 获取用户权限
        $permissions = $user->getAllPermissions();

        return [
            'token' => $token,
            'user' => $user->toArray(),
            'permissions' => $permissions,
            'expires_in' => Config::get('jwt.ttl', 7200)
        ];
    }

    /**
     * 用户登出
     * @param string $token JWT Token
     * @return void
     */
    public function logout(string $token): void
    {
        // 将Token加入黑名单
        $this->addTokenToBlacklist($token);
    }

    /**
     * 刷新Token
     * @param string $token 旧Token
     * @return array
     * @throws \Exception
     */
    public function refreshToken(string $token): array
    {
        // 验证Token
        $payload = verify_jwt($token);
        if (!$payload) {
            throw new \Exception('Token无效');
        }

        // 检查Token是否在黑名单中
        if ($this->isTokenBlacklisted($token)) {
            throw new \Exception('Token已失效');
        }

        // 获取用户信息
        $user = User::find($payload['user_id']);
        if (!$user) {
            throw new \Exception('用户不存在');
        }

        // 检查用户状态
        if ($user->status === User::STATUS_DISABLED) {
            throw new \Exception('用户已被禁用');
        }

        // 将旧Token加入黑名单
        $this->addTokenToBlacklist($token);

        // 生成新Token
        $newToken = $this->generateToken($user);

        return [
            'token' => $newToken,
            'expires_in' => Config::get('jwt.ttl', 7200)
        ];
    }

    /**
     * 修改密码
     * @param int $userId 用户ID
     * @param string $oldPassword 旧密码
     * @param string $newPassword 新密码
     * @return void
     * @throws \Exception
     */
    public function changePassword(int $userId, string $oldPassword, string $newPassword): void
    {
        $user = User::find($userId);
        if (!$user) {
            throw new \Exception('用户不存在');
        }

        // 验证旧密码
        if (!$user->verifyPassword($oldPassword)) {
            throw new \Exception('原密码错误');
        }

        // 更新密码
        $user->resetPassword($newPassword);
    }

    /**
     * 发送重置密码邮件
     * @param string $email 邮箱
     * @return void
     * @throws \Exception
     */
    public function sendResetPasswordEmail(string $email): void
    {
        $user = User::where('email', $email)->find();
        if (!$user) {
            throw new \Exception('邮箱不存在');
        }

        // 生成重置Token
        $token = md5($email . time() . uniqid());
        
        // 缓存重置Token（有效期30分钟）
        Cache::set("reset_password:{$token}", $user->id, 1800);

        // 发送邮件（这里需要实现邮件发送逻辑）
        // MailService::sendResetPasswordEmail($email, $token);
        
        // 临时返回Token（生产环境应该通过邮件发送）
        throw new \Exception("重置链接已发送到您的邮箱，Token: {$token}");
    }

    /**
     * 重置密码
     * @param string $token 重置Token
     * @param string $password 新密码
     * @return void
     * @throws \Exception
     */
    public function resetPassword(string $token, string $password): void
    {
        // 验证重置Token
        $userId = Cache::get("reset_password:{$token}");
        if (!$userId) {
            throw new \Exception('重置链接无效或已过期');
        }

        $user = User::find($userId);
        if (!$user) {
            throw new \Exception('用户不存在');
        }

        // 重置密码
        $user->resetPassword($password);

        // 删除重置Token
        Cache::delete("reset_password:{$token}");
    }

    /**
     * 发送验证码
     * @param string $phone 手机号
     * @param string $type 验证码类型
     * @return void
     * @throws \Exception
     */
    public function sendVerificationCode(string $phone, string $type): void
    {
        // 检查发送频率（60秒内只能发送一次）
        $cacheKey = "sms_code_sent:{$phone}:{$type}";
        if (Cache::get($cacheKey)) {
            throw new \Exception('验证码发送过于频繁，请稍后再试');
        }

        // 生成验证码
        $code = generate_code(6, true);

        // 缓存验证码（有效期5分钟）
        Cache::set("sms_code:{$phone}:{$type}", $code, 300);

        // 设置发送频率限制
        Cache::set($cacheKey, true, 60);

        // 发送短信（这里需要实现短信发送逻辑）
        // SmsService::sendCode($phone, $code, $type);
        
        // 临时返回验证码（生产环境应该通过短信发送）
        throw new \Exception("验证码已发送，Code: {$code}");
    }

    /**
     * 验证验证码
     * @param string $phone 手机号
     * @param string $code 验证码
     * @param string $type 验证码类型
     * @return array
     * @throws \Exception
     */
    public function verifyCode(string $phone, string $code, string $type): array
    {
        $cacheKey = "sms_code:{$phone}:{$type}";
        $cachedCode = Cache::get($cacheKey);

        if (!$cachedCode) {
            throw new \Exception('验证码已过期');
        }

        if ($cachedCode !== $code) {
            throw new \Exception('验证码错误');
        }

        // 删除验证码
        Cache::delete($cacheKey);

        return ['verified' => true];
    }

    /**
     * 绑定手机号
     * @param int $userId 用户ID
     * @param string $phone 手机号
     * @param string $code 验证码
     * @return void
     * @throws \Exception
     */
    public function bindPhone(int $userId, string $phone, string $code): void
    {
        // 验证验证码
        $this->verifyCode($phone, $code, 'bind_phone');

        // 检查手机号是否已被使用
        if (User::where('phone', $phone)->where('id', '<>', $userId)->exists()) {
            throw new \Exception('手机号已被其他用户使用');
        }

        // 更新用户手机号
        $user = User::find($userId);
        if (!$user) {
            throw new \Exception('用户不存在');
        }

        $user->save(['phone' => $phone]);
    }

    /**
     * 更新个人资料
     * @param int $userId 用户ID
     * @param array $data 更新数据
     * @return array
     * @throws \Exception
     */
    public function updateProfile(int $userId, array $data): array
    {
        $user = User::find($userId);
        if (!$user) {
            throw new \Exception('用户不存在');
        }

        // 允许更新的字段
        $allowedFields = ['real_name', 'avatar'];
        $updateData = [];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = $data[$field];
            }
        }

        if (empty($updateData)) {
            throw new \Exception('没有可更新的数据');
        }

        // 更新用户信息
        $user->save($updateData);

        return $user->toArray();
    }

    /**
     * 生成JWT Token
     * @param User $user 用户模型
     * @return string
     */
    private function generateToken(User $user): string
    {
        $payload = [
            'user_id' => $user->id,
            'username' => $user->username,
            'email' => $user->email
        ];

        return generate_jwt($payload);
    }

    /**
     * 将Token加入黑名单
     * @param string $token JWT Token
     * @return void
     */
    private function addTokenToBlacklist(string $token): void
    {
        $payload = verify_jwt($token);
        if ($payload && isset($payload['exp'])) {
            $ttl = $payload['exp'] - time();
            if ($ttl > 0) {
                Cache::set("blacklist_token:{$token}", true, $ttl);
            }
        }
    }

    /**
     * 检查Token是否在黑名单中
     * @param string $token JWT Token
     * @return bool
     */
    private function isTokenBlacklisted(string $token): bool
    {
        return Cache::get("blacklist_token:{$token}") === true;
    }
}

<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 角色验证器
 */

declare(strict_types=1);

namespace app\validate;

use think\Validate;

/**
 * 角色验证器
 */
class RoleValidate extends Validate
{
    /**
     * 验证规则
     * @var array
     */
    protected $rule = [
        'name' => 'require|length:2,50|unique:roles',
        'slug' => 'require|length:2,50|alphaNum|unique:roles',
        'description' => 'length:0,200',
        'permissions' => 'array',
        'is_default' => 'in:0,1',
        'sort_order' => 'integer|between:0,999'
    ];

    /**
     * 错误信息
     * @var array
     */
    protected $message = [
        'name.require' => '角色名称不能为空',
        'name.length' => '角色名称长度必须在2-50个字符之间',
        'name.unique' => '角色名称已存在',
        'slug.require' => '角色标识不能为空',
        'slug.length' => '角色标识长度必须在2-50个字符之间',
        'slug.alphaNum' => '角色标识只能包含字母和数字',
        'slug.unique' => '角色标识已存在',
        'description.length' => '角色描述长度不能超过200个字符',
        'permissions.array' => '权限列表必须是数组',
        'is_default.in' => '默认角色值不正确',
        'sort_order.integer' => '排序必须是整数',
        'sort_order.between' => '排序值必须在0-999之间'
    ];

    /**
     * 验证场景
     * @var array
     */
    protected $scene = [
        // 创建场景
        'create' => ['name', 'slug', 'description', 'permissions', 'is_default', 'sort_order'],
        
        // 更新场景
        'update' => ['name', 'slug', 'description', 'permissions', 'is_default', 'sort_order']
    ];

    /**
     * 创建场景验证规则
     * @return RoleValidate
     */
    public function sceneCreate(): RoleValidate
    {
        return $this->only(['name', 'slug', 'description', 'permissions', 'is_default', 'sort_order'])
                   ->append('slug', 'checkSlugFormat');
    }

    /**
     * 更新场景验证规则
     * @return RoleValidate
     */
    public function sceneUpdate(): RoleValidate
    {
        return $this->only(['name', 'slug', 'description', 'permissions', 'is_default', 'sort_order'])
                   ->remove('name', 'unique')
                   ->remove('slug', 'unique')
                   ->append('name', 'checkNameUpdate')
                   ->append('slug', 'checkSlugUpdate');
    }

    /**
     * 自定义验证规则：检查角色标识格式
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkSlugFormat($value, $rule, array $data)
    {
        // 角色标识只能包含字母、数字、下划线
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $value)) {
            return '角色标识只能包含字母、数字和下划线';
        }
        
        // 角色标识不能以数字开头
        if (is_numeric($value[0])) {
            return '角色标识不能以数字开头';
        }
        
        // 角色标识不能全是数字
        if (is_numeric($value)) {
            return '角色标识不能全是数字';
        }
        
        // 检查保留关键字
        $reserved = ['admin', 'root', 'system', 'guest', 'user', 'super', 'manager'];
        if (in_array(strtolower($value), $reserved)) {
            return '角色标识不能使用系统保留关键字';
        }
        
        return true;
    }

    /**
     * 自定义验证规则：检查角色名称更新时的唯一性
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkNameUpdate($value, $rule, array $data)
    {
        if (empty($value)) {
            return '角色名称不能为空';
        }

        // 检查唯一性（排除当前角色）
        $roleId = $data['id'] ?? 0;
        $exists = \app\model\Role::where('name', $value)
                                ->where('id', '<>', $roleId)
                                ->exists();
        if ($exists) {
            return '角色名称已存在';
        }
        
        return true;
    }

    /**
     * 自定义验证规则：检查角色标识更新时的唯一性
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkSlugUpdate($value, $rule, array $data)
    {
        if (empty($value)) {
            return '角色标识不能为空';
        }

        // 检查格式
        $formatCheck = $this->checkSlugFormat($value, $rule, $data);
        if ($formatCheck !== true) {
            return $formatCheck;
        }

        // 检查唯一性（排除当前角色）
        $roleId = $data['id'] ?? 0;
        $exists = \app\model\Role::where('slug', $value)
                                ->where('id', '<>', $roleId)
                                ->exists();
        if ($exists) {
            return '角色标识已存在';
        }
        
        return true;
    }

    /**
     * 自定义验证规则：检查权限列表
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkPermissions($value, $rule, array $data)
    {
        if (empty($value) || !is_array($value)) {
            return true;
        }

        // 获取系统所有权限
        $roleService = new \app\service\RoleService();
        $allPermissions = $roleService->getPermissions();
        
        $validPermissions = [];
        foreach ($allPermissions as $group) {
            foreach ($group['permissions'] as $permission) {
                $validPermissions[] = $permission['key'];
            }
        }

        // 检查权限是否有效
        foreach ($value as $permission) {
            if (!in_array($permission, $validPermissions)) {
                return "权限 {$permission} 不存在";
            }
        }
        
        return true;
    }

    /**
     * 自定义验证规则：检查默认角色设置
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkDefaultRole($value, $rule, array $data)
    {
        if ($value != 1) {
            return true;
        }

        // 如果设置为默认角色，检查是否已有其他默认角色
        $roleId = $data['id'] ?? 0;
        $exists = \app\model\Role::where('is_default', 1)
                                ->where('id', '<>', $roleId)
                                ->exists();
        
        if ($exists) {
            return '系统只能有一个默认角色，请先取消其他角色的默认状态';
        }
        
        return true;
    }
}

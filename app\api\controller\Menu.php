<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-11
 * 前台菜单API控制器 - ThinkPHP6企业级应用
 */

namespace app\api\controller;

use think\facade\Db;
use app\BaseController;

class Menu extends BaseController
{
    /**
     * 获取前台菜单数据
     */
    public function index()
    {
        try {
            $menus = $this->getMenuTree();
            
            return json([
                'success' => true,
                'data' => $menus,
                'message' => '获取成功'
            ]);
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * 获取菜单树形结构
     */
    private function getMenuTree($parentId = 0): array
    {
        $menus = Db::table('sys_menu')
            ->where('parent_id', $parentId)
            ->where('status', 1) // 只获取启用的菜单
            ->order('sort_order', 'asc')
            ->select()
            ->toArray();

        foreach ($menus as &$menu) {
            $menu['children'] = $this->getMenuTree($menu['id']);
            $menu['url'] = $this->buildMenuUrl($menu);
        }

        return $menus;
    }

    /**
     * 构建菜单URL
     */
    private function buildMenuUrl($menu): string
    {
        switch ($menu['link_type']) {
            case 'page':
                return '/page/' . ($menu['link_value'] ?? '');
            case 'category':
                return '/category/' . ($menu['link_value'] ?? '');
            case 'solution':
                return '/solutions/' . ($menu['link_value'] ?? '');
            case 'product':
                return '/products/' . ($menu['link_value'] ?? '');
            case 'external':
                // 外部链接，直接返回完整URL
                return $menu['link_value'] ?: '#';
            case 'url':
                // 内部URL，确保以/开头
                $url = $menu['link_value'] ?: '#';
                return $url === '#' ? '#' : (strpos($url, '/') === 0 ? $url : '/' . $url);
            default:
                // 其他类型，尝试构建URL
                $url = $menu['link_value'] ?: '#';
                return $url === '#' ? '#' : (strpos($url, '/') === 0 ? $url : '/' . $url);
        }
    }
}

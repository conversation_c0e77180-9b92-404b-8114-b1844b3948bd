<!--
  三只鱼网络科技 | 韩总 | 2024-12-20
  QiyeDIY企业建站系统 - 系统设置页面
-->

<template>
  <div class="system-settings">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">系统设置</h2>
        <p class="page-description">管理系统的基本配置和参数</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Check" @click="handleSaveAll" :loading="saving">
          保存所有设置
        </el-button>
      </div>
    </div>

    <!-- 设置内容 -->
    <div class="settings-content">
      <!-- 左侧导航 -->
      <div class="settings-nav">
        <el-menu
          v-model="activeSection"
          class="nav-menu"
          @select="handleSectionChange"
        >
          <el-menu-item index="basic">
            <el-icon><Setting /></el-icon>
            <span>基本设置</span>
          </el-menu-item>
          <el-menu-item index="upload">
            <el-icon><Upload /></el-icon>
            <span>上传设置</span>
          </el-menu-item>
          <el-menu-item index="email">
            <el-icon><Message /></el-icon>
            <span>邮件设置</span>
          </el-menu-item>
          <el-menu-item index="sms">
            <el-icon><ChatDotRound /></el-icon>
            <span>短信设置</span>
          </el-menu-item>
          <el-menu-item index="storage">
            <el-icon><FolderOpened /></el-icon>
            <span>存储设置</span>
          </el-menu-item>
          <el-menu-item index="cache">
            <el-icon><DataBoard /></el-icon>
            <span>缓存设置</span>
          </el-menu-item>
          <el-menu-item index="security">
            <el-icon><Lock /></el-icon>
            <span>安全设置</span>
          </el-menu-item>
          <el-menu-item index="backup">
            <el-icon><Download /></el-icon>
            <span>备份设置</span>
          </el-menu-item>
        </el-menu>
      </div>

      <!-- 右侧设置面板 -->
      <div class="settings-panel">
        <!-- 基本设置 -->
        <div v-show="activeSection === 'basic'" class="setting-section">
          <el-card>
            <template #header>
              <h3>基本设置</h3>
            </template>
            
            <el-form :model="basicSettings" label-width="120px" size="large">
              <el-form-item label="网站名称">
                <el-input v-model="basicSettings.site_name" placeholder="请输入网站名称" />
              </el-form-item>
              
              <el-form-item label="网站标题">
                <el-input v-model="basicSettings.site_title" placeholder="请输入网站标题" />
              </el-form-item>
              
              <el-form-item label="网站描述">
                <el-input
                  v-model="basicSettings.site_description"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入网站描述"
                />
              </el-form-item>
              
              <el-form-item label="网站关键词">
                <el-input v-model="basicSettings.site_keywords" placeholder="请输入网站关键词，用逗号分隔" />
              </el-form-item>
              
              <el-form-item label="网站Logo">
                <div class="logo-upload">
                  <el-upload
                    class="logo-uploader"
                    :action="uploadAction"
                    :headers="uploadHeaders"
                    :show-file-list="false"
                    :before-upload="beforeLogoUpload"
                    :on-success="handleLogoSuccess"
                  >
                    <img v-if="basicSettings.site_logo" :src="basicSettings.site_logo" class="logo-image" />
                    <el-icon v-else class="logo-uploader-icon"><Plus /></el-icon>
                  </el-upload>
                  <div class="upload-tips">
                    <p>建议尺寸：200x60像素</p>
                    <p>支持格式：PNG、JPG</p>
                  </div>
                </div>
              </el-form-item>
              
              <el-form-item label="网站域名">
                <el-input v-model="basicSettings.site_domain" placeholder="https://www.example.com" />
              </el-form-item>
              
              <el-form-item label="ICP备案号">
                <el-input v-model="basicSettings.icp_number" placeholder="请输入ICP备案号" />
              </el-form-item>
              
              <el-form-item label="联系邮箱">
                <el-input v-model="basicSettings.contact_email" placeholder="请输入联系邮箱" />
              </el-form-item>
              
              <el-form-item label="联系电话">
                <el-input v-model="basicSettings.contact_phone" placeholder="请输入联系电话" />
              </el-form-item>
              
              <el-form-item label="网站状态">
                <el-radio-group v-model="basicSettings.site_status">
                  <el-radio :label="1">正常运行</el-radio>
                  <el-radio :label="0">维护中</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item label="维护提示">
                <el-input
                  v-model="basicSettings.maintenance_message"
                  type="textarea"
                  :rows="2"
                  placeholder="网站维护中的提示信息"
                />
              </el-form-item>
            </el-form>
          </el-card>
        </div>

        <!-- 上传设置 -->
        <div v-show="activeSection === 'upload'" class="setting-section">
          <el-card>
            <template #header>
              <h3>上传设置</h3>
            </template>
            
            <el-form :model="uploadSettings" label-width="120px" size="large">
              <el-form-item label="图片大小限制">
                <el-input-number
                  v-model="uploadSettings.image_max_size"
                  :min="1"
                  :max="50"
                  controls-position="right"
                />
                <span class="unit">MB</span>
              </el-form-item>
              
              <el-form-item label="视频大小限制">
                <el-input-number
                  v-model="uploadSettings.video_max_size"
                  :min="1"
                  :max="500"
                  controls-position="right"
                />
                <span class="unit">MB</span>
              </el-form-item>
              
              <el-form-item label="文档大小限制">
                <el-input-number
                  v-model="uploadSettings.document_max_size"
                  :min="1"
                  :max="100"
                  controls-position="right"
                />
                <span class="unit">MB</span>
              </el-form-item>
              
              <el-form-item label="允许的图片格式">
                <el-checkbox-group v-model="uploadSettings.allowed_image_types">
                  <el-checkbox label="jpg">JPG</el-checkbox>
                  <el-checkbox label="jpeg">JPEG</el-checkbox>
                  <el-checkbox label="png">PNG</el-checkbox>
                  <el-checkbox label="gif">GIF</el-checkbox>
                  <el-checkbox label="webp">WebP</el-checkbox>
                  <el-checkbox label="bmp">BMP</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              
              <el-form-item label="允许的视频格式">
                <el-checkbox-group v-model="uploadSettings.allowed_video_types">
                  <el-checkbox label="mp4">MP4</el-checkbox>
                  <el-checkbox label="avi">AVI</el-checkbox>
                  <el-checkbox label="mov">MOV</el-checkbox>
                  <el-checkbox label="wmv">WMV</el-checkbox>
                  <el-checkbox label="flv">FLV</el-checkbox>
                  <el-checkbox label="webm">WebM</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              
              <el-form-item label="图片质量压缩">
                <el-slider
                  v-model="uploadSettings.image_quality"
                  :min="50"
                  :max="100"
                  show-stops
                  show-input
                />
                <span class="unit">%</span>
              </el-form-item>
              
              <el-form-item label="自动生成缩略图">
                <el-switch v-model="uploadSettings.auto_thumbnail" />
              </el-form-item>
              
              <el-form-item label="缩略图尺寸">
                <div class="thumbnail-sizes">
                  <div class="size-item">
                    <span>小图：</span>
                    <el-input-number v-model="uploadSettings.thumbnail_small_width" :min="50" :max="500" size="small" />
                    <span>×</span>
                    <el-input-number v-model="uploadSettings.thumbnail_small_height" :min="50" :max="500" size="small" />
                  </div>
                  <div class="size-item">
                    <span>中图：</span>
                    <el-input-number v-model="uploadSettings.thumbnail_medium_width" :min="100" :max="800" size="small" />
                    <span>×</span>
                    <el-input-number v-model="uploadSettings.thumbnail_medium_height" :min="100" :max="800" size="small" />
                  </div>
                  <div class="size-item">
                    <span>大图：</span>
                    <el-input-number v-model="uploadSettings.thumbnail_large_width" :min="200" :max="1200" size="small" />
                    <span>×</span>
                    <el-input-number v-model="uploadSettings.thumbnail_large_height" :min="200" :max="1200" size="small" />
                  </div>
                </div>
              </el-form-item>
            </el-form>
          </el-card>
        </div>

        <!-- 邮件设置 -->
        <div v-show="activeSection === 'email'" class="setting-section">
          <el-card>
            <template #header>
              <h3>邮件设置</h3>
            </template>
            
            <el-form :model="emailSettings" label-width="120px" size="large">
              <el-form-item label="邮件驱动">
                <el-select v-model="emailSettings.driver" placeholder="请选择邮件驱动">
                  <el-option label="SMTP" value="smtp" />
                  <el-option label="Sendmail" value="sendmail" />
                  <el-option label="阿里云邮件" value="aliyun" />
                  <el-option label="腾讯云邮件" value="tencent" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="SMTP服务器">
                <el-input v-model="emailSettings.smtp_host" placeholder="smtp.example.com" />
              </el-form-item>
              
              <el-form-item label="SMTP端口">
                <el-input-number v-model="emailSettings.smtp_port" :min="1" :max="65535" />
              </el-form-item>
              
              <el-form-item label="加密方式">
                <el-select v-model="emailSettings.smtp_encryption">
                  <el-option label="无加密" value="" />
                  <el-option label="SSL" value="ssl" />
                  <el-option label="TLS" value="tls" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="用户名">
                <el-input v-model="emailSettings.smtp_username" placeholder="邮箱账号" />
              </el-form-item>
              
              <el-form-item label="密码">
                <el-input v-model="emailSettings.smtp_password" type="password" placeholder="邮箱密码或授权码" />
              </el-form-item>
              
              <el-form-item label="发件人邮箱">
                <el-input v-model="emailSettings.from_email" placeholder="<EMAIL>" />
              </el-form-item>
              
              <el-form-item label="发件人名称">
                <el-input v-model="emailSettings.from_name" placeholder="网站名称" />
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="handleTestEmail" :loading="testingEmail">
                  发送测试邮件
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>

        <!-- 缓存设置 -->
        <div v-show="activeSection === 'cache'" class="setting-section">
          <el-card>
            <template #header>
              <h3>缓存设置</h3>
            </template>
            
            <el-form :model="cacheSettings" label-width="120px" size="large">
              <el-form-item label="缓存驱动">
                <el-select v-model="cacheSettings.driver">
                  <el-option label="Redis" value="redis" />
                  <el-option label="文件缓存" value="file" />
                  <el-option label="内存缓存" value="memory" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="Redis主机">
                <el-input v-model="cacheSettings.redis_host" placeholder="127.0.0.1" />
              </el-form-item>
              
              <el-form-item label="Redis端口">
                <el-input-number v-model="cacheSettings.redis_port" :min="1" :max="65535" />
              </el-form-item>
              
              <el-form-item label="Redis密码">
                <el-input v-model="cacheSettings.redis_password" type="password" placeholder="Redis密码" />
              </el-form-item>
              
              <el-form-item label="缓存前缀">
                <el-input v-model="cacheSettings.prefix" placeholder="qiyediy_" />
              </el-form-item>
              
              <el-form-item label="默认过期时间">
                <el-input-number v-model="cacheSettings.default_ttl" :min="60" :max="86400" />
                <span class="unit">秒</span>
              </el-form-item>
              
              <el-form-item>
                <el-button type="danger" @click="handleClearCache" :loading="clearingCache">
                  清空所有缓存
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>

        <!-- 安全设置 -->
        <div v-show="activeSection === 'security'" class="setting-section">
          <el-card>
            <template #header>
              <h3>安全设置</h3>
            </template>
            
            <el-form :model="securitySettings" label-width="120px" size="large">
              <el-form-item label="登录失败限制">
                <el-input-number v-model="securitySettings.login_max_attempts" :min="3" :max="10" />
                <span class="unit">次</span>
              </el-form-item>
              
              <el-form-item label="锁定时间">
                <el-input-number v-model="securitySettings.login_lockout_time" :min="5" :max="60" />
                <span class="unit">分钟</span>
              </el-form-item>
              
              <el-form-item label="密码最小长度">
                <el-input-number v-model="securitySettings.password_min_length" :min="6" :max="20" />
                <span class="unit">位</span>
              </el-form-item>
              
              <el-form-item label="密码复杂度">
                <el-checkbox-group v-model="securitySettings.password_rules">
                  <el-checkbox label="uppercase">包含大写字母</el-checkbox>
                  <el-checkbox label="lowercase">包含小写字母</el-checkbox>
                  <el-checkbox label="numbers">包含数字</el-checkbox>
                  <el-checkbox label="symbols">包含特殊字符</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              
              <el-form-item label="会话超时">
                <el-input-number v-model="securitySettings.session_timeout" :min="30" :max="1440" />
                <span class="unit">分钟</span>
              </el-form-item>
              
              <el-form-item label="IP白名单">
                <el-input
                  v-model="securitySettings.ip_whitelist"
                  type="textarea"
                  :rows="3"
                  placeholder="每行一个IP地址，支持CIDR格式"
                />
              </el-form-item>
              
              <el-form-item label="启用验证码">
                <el-switch v-model="securitySettings.enable_captcha" />
              </el-form-item>
              
              <el-form-item label="启用双因子认证">
                <el-switch v-model="securitySettings.enable_2fa" />
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { systemApi } from '@/api/system'
import {
  Check, Setting, Upload, Message, ChatDotRound, FolderOpened,
  DataBoard, Lock, Download, Plus
} from '@element-plus/icons-vue'

const userStore = useUserStore()

// 状态
const activeSection = ref('basic')
const saving = ref(false)
const testingEmail = ref(false)
const clearingCache = ref(false)

// 设置数据
const basicSettings = reactive({
  site_name: '',
  site_title: '',
  site_description: '',
  site_keywords: '',
  site_logo: '',
  site_domain: '',
  icp_number: '',
  contact_email: '',
  contact_phone: '',
  site_status: 1,
  maintenance_message: ''
})

const uploadSettings = reactive({
  image_max_size: 5,
  video_max_size: 100,
  document_max_size: 20,
  allowed_image_types: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  allowed_video_types: ['mp4', 'avi', 'mov', 'wmv'],
  image_quality: 80,
  auto_thumbnail: true,
  thumbnail_small_width: 150,
  thumbnail_small_height: 150,
  thumbnail_medium_width: 300,
  thumbnail_medium_height: 300,
  thumbnail_large_width: 600,
  thumbnail_large_height: 600
})

const emailSettings = reactive({
  driver: 'smtp',
  smtp_host: '',
  smtp_port: 587,
  smtp_encryption: 'tls',
  smtp_username: '',
  smtp_password: '',
  from_email: '',
  from_name: ''
})

const cacheSettings = reactive({
  driver: 'redis',
  redis_host: '127.0.0.1',
  redis_port: 6379,
  redis_password: '',
  prefix: 'qiyediy_',
  default_ttl: 3600
})

const securitySettings = reactive({
  login_max_attempts: 5,
  login_lockout_time: 15,
  password_min_length: 8,
  password_rules: ['lowercase', 'numbers'],
  session_timeout: 120,
  ip_whitelist: '',
  enable_captcha: true,
  enable_2fa: false
})

// 计算属性
const uploadAction = computed(() => {
  return import.meta.env.VITE_API_BASE_URL + '/upload/image'
})

const uploadHeaders = computed(() => {
  return {
    Authorization: `Bearer ${userStore.token}`
  }
})

/**
 * 加载设置数据
 */
const loadSettings = async () => {
  try {
    const response = await systemApi.getSettings()
    const settings = response.data
    
    // 分配到各个设置对象
    Object.assign(basicSettings, settings.basic || {})
    Object.assign(uploadSettings, settings.upload || {})
    Object.assign(emailSettings, settings.email || {})
    Object.assign(cacheSettings, settings.cache || {})
    Object.assign(securitySettings, settings.security || {})
    
  } catch (error) {
    console.error('加载设置失败:', error)
  }
}

/**
 * 处理分组切换
 */
const handleSectionChange = (section: string) => {
  activeSection.value = section
}

/**
 * 保存所有设置
 */
const handleSaveAll = async () => {
  try {
    saving.value = true
    
    const allSettings = {
      basic: basicSettings,
      upload: uploadSettings,
      email: emailSettings,
      cache: cacheSettings,
      security: securitySettings
    }
    
    await systemApi.updateSettings(allSettings)
    ElMessage.success('设置保存成功')
    
  } catch (error: any) {
    ElMessage.error(error.message || '设置保存失败')
  } finally {
    saving.value = false
  }
}

/**
 * Logo上传前检查
 */
const beforeLogoUpload = (rawFile: File) => {
  const isImage = rawFile.type.startsWith('image/')
  const isLt2M = rawFile.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('Logo必须是图片格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('Logo大小不能超过 2MB!')
    return false
  }
  return true
}

/**
 * Logo上传成功
 */
const handleLogoSuccess = (response: any) => {
  if (response.code === 200) {
    basicSettings.site_logo = response.data.url
    ElMessage.success('Logo上传成功')
  } else {
    ElMessage.error(response.message || 'Logo上传失败')
  }
}

/**
 * 测试邮件发送
 */
const handleTestEmail = async () => {
  try {
    testingEmail.value = true
    
    await systemApi.testEmail({
      to: userStore.user?.email,
      subject: '邮件配置测试',
      content: '这是一封测试邮件，如果您收到此邮件，说明邮件配置正确。'
    })
    
    ElMessage.success('测试邮件发送成功，请检查邮箱')
    
  } catch (error: any) {
    ElMessage.error(error.message || '测试邮件发送失败')
  } finally {
    testingEmail.value = false
  }
}

/**
 * 清空缓存
 */
const handleClearCache = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有缓存吗？此操作不可恢复。',
      '清空缓存确认',
      { type: 'warning' }
    )
    
    clearingCache.value = true
    await systemApi.clearCache()
    ElMessage.success('缓存清空成功')
    
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '缓存清空失败')
    }
  } finally {
    clearingCache.value = false
  }
}

onMounted(() => {
  loadSettings()
})
</script>

<style lang="scss" scoped>
.system-settings {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left {
  .page-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 8px 0;
  }
  
  .page-description {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin: 0;
  }
}

.settings-content {
  display: flex;
  gap: 20px;
}

.settings-nav {
  width: 200px;
  flex-shrink: 0;
  
  .nav-menu {
    border-radius: 8px;
    border: 1px solid var(--el-border-color-light);
    
    :deep(.el-menu-item) {
      height: 48px;
      line-height: 48px;
      
      &.is-active {
        background-color: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
      }
    }
  }
}

.settings-panel {
  flex: 1;
  
  .setting-section {
    :deep(.el-card__header) {
      padding: 20px 20px 0;
      
      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
      }
    }
    
    :deep(.el-card__body) {
      padding: 20px;
    }
  }
}

.logo-upload {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  
  .logo-uploader {
    :deep(.el-upload) {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
      
      &:hover {
        border-color: var(--el-color-primary);
      }
    }
  }
  
  .logo-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 60px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .logo-image {
    width: 120px;
    height: 60px;
    display: block;
    object-fit: contain;
  }
  
  .upload-tips {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    line-height: 1.5;
    
    p {
      margin: 0 0 4px 0;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.unit {
  margin-left: 8px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.thumbnail-sizes {
  .size-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    span {
      color: var(--el-text-color-secondary);
      font-size: 14px;
    }
    
    .el-input-number {
      width: 80px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .system-settings {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .settings-content {
    flex-direction: column;
  }
  
  .settings-nav {
    width: 100%;
    
    .nav-menu {
      display: flex;
      overflow-x: auto;
      
      :deep(.el-menu-item) {
        flex-shrink: 0;
        white-space: nowrap;
      }
    }
  }
}
</style>

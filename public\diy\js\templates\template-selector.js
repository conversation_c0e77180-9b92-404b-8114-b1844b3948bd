/**
 * 模板选择器组件
 * 提供整站模板选择和预览功能
 */

// 自定义确认对话框函数
function showConfirmDialog(title, message, onConfirm, isDanger = false) {
    return new Promise((resolve) => {
        const overlay = document.createElement('div');
        overlay.className = 'confirm-dialog-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        `;

        overlay.innerHTML = `
            <div class="confirm-dialog" style="
                background: white;
                border-radius: 12px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                max-width: 400px;
                width: 90%;
                transform: scale(0.8);
                transition: transform 0.3s ease;
            ">
                <div class="confirm-dialog-header" style="
                    padding: 20px 20px 0;
                    text-align: center;
                ">
                    <h3 style="
                        margin: 0;
                        font-size: 18px;
                        color: #2d3748;
                        font-weight: 600;
                    ">${title}</h3>
                </div>
                <div class="confirm-dialog-content" style="
                    padding: 15px 20px;
                    text-align: center;
                ">
                    <p style="
                        margin: 0;
                        font-size: 14px;
                        color: #4a5568;
                        line-height: 1.5;
                    ">${message}</p>
                </div>
                <div class="confirm-dialog-footer" style="
                    padding: 0 20px 20px;
                    display: flex;
                    gap: 10px;
                    justify-content: center;
                ">
                    <button class="confirm-dialog-btn cancel" style="
                        padding: 10px 20px;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 14px;
                        font-weight: 500;
                        transition: all 0.2s ease;
                        min-width: 80px;
                        background: #e2e8f0;
                        color: #4a5568;
                    ">取消</button>
                    <button class="confirm-dialog-btn confirm" style="
                        padding: 10px 20px;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 14px;
                        font-weight: 500;
                        transition: all 0.2s ease;
                        min-width: 80px;
                        background: ${isDanger ? '#e53e3e' : '#667eea'};
                        color: white;
                    ">确认</button>
                </div>
            </div>
        `;

        document.body.appendChild(overlay);

        // 显示动画
        setTimeout(() => {
            overlay.style.opacity = '1';
            overlay.style.visibility = 'visible';
            const dialog = overlay.querySelector('.confirm-dialog');
            dialog.style.transform = 'scale(1)';
        }, 10);

        // 绑定事件
        const cancelBtn = overlay.querySelector('.cancel');
        const confirmBtn = overlay.querySelector('.confirm');

        const closeDialog = (result) => {
            overlay.style.opacity = '0';
            overlay.style.visibility = 'hidden';
            const dialog = overlay.querySelector('.confirm-dialog');
            dialog.style.transform = 'scale(0.8)';

            setTimeout(() => {
                if (overlay.parentNode) {
                    document.body.removeChild(overlay);
                }
            }, 300);
            resolve(result);
        };

        cancelBtn.addEventListener('click', () => closeDialog(false));
        confirmBtn.addEventListener('click', () => {
            closeDialog(true);
            if (onConfirm && typeof onConfirm === 'function') {
                onConfirm();
            }
        });

        // 点击遮罩关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                closeDialog(false);
            }
        });

        // ESC键关闭
        const handleEsc = (e) => {
            if (e.key === 'Escape') {
                closeDialog(false);
                document.removeEventListener('keydown', handleEsc);
            }
        };
        document.addEventListener('keydown', handleEsc);
    });
}

// 自定义输入对话框函数
function showInputDialog(title, message, placeholder = '', onConfirm) {
    return new Promise((resolve) => {
        const overlay = document.createElement('div');
        overlay.className = 'confirm-dialog-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        `;

        overlay.innerHTML = `
            <div class="confirm-dialog" style="
                background: white;
                border-radius: 12px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                max-width: 400px;
                width: 90%;
                transform: scale(0.8);
                transition: transform 0.3s ease;
            ">
                <div class="confirm-dialog-header" style="
                    padding: 20px 20px 0;
                    text-align: center;
                ">
                    <h3 style="
                        margin: 0;
                        font-size: 18px;
                        color: #2d3748;
                        font-weight: 600;
                    ">${title}</h3>
                </div>
                <div class="confirm-dialog-content" style="
                    padding: 15px 20px;
                    text-align: center;
                ">
                    <p style="
                        margin: 0 0 15px 0;
                        font-size: 14px;
                        color: #4a5568;
                        line-height: 1.5;
                    ">${message}</p>
                    <input type="text" class="dialog-input" placeholder="${placeholder}" style="
                        width: 100%;
                        padding: 10px 12px;
                        border: 2px solid #e2e8f0;
                        border-radius: 6px;
                        font-size: 14px;
                        outline: none;
                        transition: border-color 0.2s ease;
                    ">
                </div>
                <div class="confirm-dialog-footer" style="
                    padding: 0 20px 20px;
                    display: flex;
                    gap: 10px;
                    justify-content: center;
                ">
                    <button class="confirm-dialog-btn cancel" style="
                        padding: 10px 20px;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 14px;
                        font-weight: 500;
                        transition: all 0.2s ease;
                        min-width: 80px;
                        background: #e2e8f0;
                        color: #4a5568;
                    ">取消</button>
                    <button class="confirm-dialog-btn confirm" style="
                        padding: 10px 20px;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 14px;
                        font-weight: 500;
                        transition: all 0.2s ease;
                        min-width: 80px;
                        background: #667eea;
                        color: white;
                    ">确认</button>
                </div>
            </div>
        `;

        document.body.appendChild(overlay);

        // 获取输入框
        const input = overlay.querySelector('.dialog-input');

        // 显示动画
        setTimeout(() => {
            overlay.style.opacity = '1';
            overlay.style.visibility = 'visible';
            const dialog = overlay.querySelector('.confirm-dialog');
            dialog.style.transform = 'scale(1)';
            input.focus();
        }, 10);

        // 绑定事件
        const cancelBtn = overlay.querySelector('.cancel');
        const confirmBtn = overlay.querySelector('.confirm');

        const closeDialog = (result, value = null) => {
            overlay.style.opacity = '0';
            overlay.style.visibility = 'hidden';
            const dialog = overlay.querySelector('.confirm-dialog');
            dialog.style.transform = 'scale(0.8)';

            setTimeout(() => {
                if (overlay.parentNode) {
                    document.body.removeChild(overlay);
                }
            }, 300);
            resolve(result ? value : null);
        };

        cancelBtn.addEventListener('click', () => closeDialog(false));
        confirmBtn.addEventListener('click', () => {
            const value = input.value.trim();
            if (value) {
                closeDialog(true, value);
                if (onConfirm && typeof onConfirm === 'function') {
                    onConfirm(value);
                }
            } else {
                input.style.borderColor = '#e53e3e';
                input.focus();
            }
        });

        // 输入框焦点样式
        input.addEventListener('focus', () => {
            input.style.borderColor = '#667eea';
        });

        input.addEventListener('blur', () => {
            input.style.borderColor = '#e2e8f0';
        });

        // 回车确认
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                confirmBtn.click();
            }
        });

        // 点击遮罩关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                closeDialog(false);
            }
        });

        // ESC键关闭
        const handleEsc = (e) => {
            if (e.key === 'Escape') {
                closeDialog(false);
                document.removeEventListener('keydown', handleEsc);
            }
        };
        document.addEventListener('keydown', handleEsc);
    });
}

const TemplateSelector = {
    eventsInitialized: false, // 标记事件是否已初始化

    // 初始化模板选择器
    init() {
        this.createTemplateSelector();
        if (!this.eventsInitialized) {
            this.bindEvents();
            this.eventsInitialized = true;
        }
    },

    // 显示模板选择器
    show() {
        this.createTemplateSelector();
        if (!this.eventsInitialized) {
            this.bindEvents();
            this.eventsInitialized = true;
        }
    },
    
    // 创建模板选择器界面
    createTemplateSelector() {
        const propertiesPanel = document.getElementById('properties-panel');
        if (!propertiesPanel) {
            console.error('属性面板未找到');
            return;
        }

        // 创建模板选择器HTML
        const templateSelectorHTML = `
            <div id="template-selector" class="template-selector">
                <div class="template-selector-header">
                    <h3>选择模板</h3>
                </div>

                <div class="template-grid">
                    ${this.generateTemplateCards()}
                </div>

                <div class="template-actions">
                    <button class="btn-custom-template" onclick="createCustomTemplate()">
                        <i class="icon-plus"></i>
                        空白模板
                    </button>
                </div>
            </div>
        `;

        propertiesPanel.innerHTML = templateSelectorHTML;
        console.log('✅ 模板选择器已创建');
    },
    
    // 生成模板卡片
    generateTemplateCards() {
        return Object.entries(websiteTemplates).map(([templateId, template]) => `
            <div class="template-card" data-template-id="${templateId}">
                <div class="template-preview">
                    <div class="template-preview-icon">
                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                            <line x1="9" y1="9" x2="15" y2="9"/>
                            <line x1="9" y1="13" x2="15" y2="13"/>
                            <line x1="9" y1="17" x2="13" y2="17"/>
                        </svg>
                        <div class="template-preview-text">网站模板</div>
                    </div>
                </div>
                <div class="template-info">
                    <h4 class="template-name">${template.name}</h4>
                    <div class="template-meta">
                        <span class="template-badge">
                            ${Object.keys(template.pages).length} 页面
                        </span>
                        <span class="template-badge">
                            ${this.countComponents(template)} 组件
                        </span>
                    </div>
                </div>
                <div class="template-actions">
                    <button class="btn-apply-full" data-action="apply" data-template-id="${templateId}">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="20,6 9,17 4,12"/>
                        </svg>
                        应用模板
                    </button>
                </div>
            </div>
        `).join('');
    },
    
    // 统计模板组件数量
    countComponents(template) {
        let count = 0;
        Object.values(template.pages).forEach(page => {
            count += page.components.length;
        });
        return count;
    },
    
    // 预览模板
    previewTemplate(templateId) {
        console.log('🔍 预览模板:', templateId);

        const template = websiteTemplates[templateId];
        if (!template) {
            console.error('❌ 模板不存在:', templateId);
            if (typeof showMessage === 'function') {
                showMessage('模板不存在', 'error');
            } else {
                alert('模板不存在');
            }
            return;
        }

        // 在新窗口中打开首页预览
        const previewWindow = window.open('', '_blank', 'width=1200,height=800,scrollbars=yes');

        const previewHTML = `
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${template.name} - 预览</title>
                <style>
                    body {
                        margin: 0;
                        font-family: 'Microsoft YaHei', sans-serif;
                        background: #f5f5f5;
                    }
                    .preview-header {
                        background: #667eea;
                        color: white;
                        padding: 20px;
                        text-align: center;
                        position: sticky;
                        top: 0;
                        z-index: 100;
                    }
                    .preview-content {
                        max-width: 1200px;
                        margin: 0 auto;
                        padding: 20px;
                    }
                    .component-preview {
                        background: white;
                        margin-bottom: 20px;
                        border-radius: 12px;
                        overflow: hidden;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                    }
                    .hero-section {
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        padding: 80px 40px;
                        text-align: center;
                    }
                    .feature-section {
                        padding: 60px 40px;
                    }
                    .feature-grid {
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                        gap: 30px;
                        margin-top: 40px;
                    }
                    .feature-card {
                        padding: 30px;
                        border: 1px solid #e2e8f0;
                        border-radius: 12px;
                        text-align: center;
                    }
                    .stats-section {
                        background: #1a1a2e;
                        color: white;
                        padding: 60px 40px;
                        text-align: center;
                    }
                    .stats-grid {
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                        gap: 30px;
                        margin-top: 40px;
                    }
                    .stat-item {
                        padding: 20px;
                    }
                    .stat-number {
                        font-size: 48px;
                        font-weight: bold;
                        color: #10d5c2;
                        margin-bottom: 10px;
                    }
                </style>
            </head>
            <body>
                <div class="preview-header">
                    <h1>${template.name} 预览</h1>
                    <p>包含 ${Object.keys(template.pages).length} 个页面，${this.countComponents(template)} 个组件</p>
                </div>

                <div class="preview-content">
                    <div class="component-preview">
                        <div class="hero-section">
                            <h1>专业企业建站解决方案</h1>
                            <p style="font-size: 18px; margin: 20px 0;">助力企业数字化转型，打造专业品牌形象</p>
                            <p>提供一站式企业官网建设服务，从设计到开发，从上线到运维，全程专业支持</p>
                        </div>
                    </div>

                    <div class="component-preview">
                        <div class="feature-section">
                            <h2 style="text-align: center; margin-bottom: 10px;">为什么选择我们</h2>
                            <p style="text-align: center; color: #666;">专业、可靠、高效的企业级解决方案</p>
                            <div class="feature-grid">
                                <div class="feature-card">
                                    <h3>快速部署</h3>
                                    <p>专业团队快速响应，高效部署实施</p>
                                </div>
                                <div class="feature-card">
                                    <h3>安全可靠</h3>
                                    <p>企业级安全保障，多重防护机制</p>
                                </div>
                                <div class="feature-card">
                                    <h3>定制开发</h3>
                                    <p>根据业务需求量身定制解决方案</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="component-preview">
                        <div class="stats-section">
                            <h2>实力见证</h2>
                            <p style="color: #ccc;">用数据说话，用实力证明</p>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-number">100+</div>
                                    <div>成功案例</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">30+</div>
                                    <div>合作客户</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">10+</div>
                                    <div>专业团队</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">22+</div>
                                    <div>服务年限</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </body>
            </html>
        `;

        previewWindow.document.write(previewHTML);
        previewWindow.document.close();

        console.log('✅ 预览窗口已打开');
    },
    
    // 切换预览页面
    switchPreviewPage(pageId, template = null) {
        if (!template) {
            // 从当前预览中获取模板
            const modal = document.querySelector('.template-preview-modal');
            const templateId = modal.querySelector('.btn-apply-template').onclick.toString().match(/'([^']+)'/)[1];
            template = websiteTemplates[templateId];
        }
        
        // 更新标签状态
        document.querySelectorAll('.preview-page-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.pageId === pageId);
        });
        
        // 生成页面预览内容
        const page = template.pages[pageId];
        const previewHTML = this.generatePagePreview(page, template);
        
        // 更新iframe内容
        const iframe = document.getElementById('preview-frame');
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
        iframeDoc.open();
        iframeDoc.write(previewHTML);
        iframeDoc.close();
    },
    
    // 生成页面预览HTML
    generatePagePreview(page, template) {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>${page.name} - ${template.name}</title>
                <link rel="stylesheet" href="/diy/css/all.css">
                <style>
                    :root {
                        --primary-color: ${template.globalSettings.primaryColor};
                        --secondary-color: ${template.globalSettings.secondaryColor};
                        --font-family: ${template.globalSettings.fontFamily};
                    }
                    body {
                        margin: 0;
                        padding: 20px;
                        font-family: var(--font-family);
                        background: #f5f5f5;
                    }
                    .preview-component {
                        margin-bottom: 20px;
                        background: white;
                        border-radius: 8px;
                        overflow: hidden;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                    }
                </style>
            </head>
            <body>
                ${page.components.map(component => `
                    <div class="preview-component">
                        ${this.generateComponentPreview(component)}
                    </div>
                `).join('')}
            </body>
            </html>
        `;
    },
    
    // 生成组件预览
    generateComponentPreview(component) {
        const template = ComponentManager.getTemplate(component.type);
        if (!template) return `<p>组件类型 ${component.type} 不存在</p>`;
        
        // 简化的组件预览
        switch (component.type) {
            case 'hero':
                return `
                    <div class="hero-preview" style="background: ${component.properties.backgroundGradient || '#667eea'}; color: white; padding: 60px 20px; text-align: center;">
                        <h1>${component.properties.title || '标题'}</h1>
                        <p>${component.properties.subtitle || '副标题'}</p>
                        <p>${component.properties.description || '描述'}</p>
                    </div>
                `;
            case 'card':
                return `
                    <div class="card-preview" style="padding: 40px 20px;">
                        <h2 style="text-align: center; margin-bottom: 10px;">${component.properties.title || '卡片标题'}</h2>
                        <p style="text-align: center; color: #666; margin-bottom: 30px;">${component.properties.subtitle || '卡片副标题'}</p>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                            ${(component.properties.cards || []).slice(0, 3).map(card => `
                                <div style="padding: 20px; border: 1px solid #eee; border-radius: 8px;">
                                    <h4>${card.title}</h4>
                                    <p style="color: #666; font-size: 14px;">${card.description}</p>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            case 'stats':
                return `
                    <div class="stats-preview" style="padding: 40px 20px; background: #1a1a2e; color: white; text-align: center;">
                        <h2>${component.properties.title || '统计标题'}</h2>
                        <p style="color: #ccc;">${component.properties.subtitle || '统计副标题'}</p>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 30px;">
                            ${(component.properties.stats || []).map(stat => `
                                <div style="padding: 20px;">
                                    <div style="font-size: 36px; font-weight: bold; color: #10d5c2;">${stat.number}</div>
                                    <div style="font-size: 18px; margin: 10px 0;">${stat.label}</div>
                                    <div style="font-size: 14px; color: #ccc;">${stat.description}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            default:
                return `
                    <div style="padding: 20px; text-align: center; color: #666;">
                        <p>${component.type} 组件预览</p>
                    </div>
                `;
        }
    },
    
    // 应用模板
    applyTemplate(templateId) {
        console.log('🎯 开始应用模板:', templateId);

        showConfirmDialog(
            '应用模板',
            '应用模板将清空当前内容，确定继续吗？',
            () => {
                try {
                    // 检查模板管理器是否存在
                    if (typeof TemplateManager === 'undefined') {
                        console.error('❌ TemplateManager 未定义');
                        if (typeof showMessage === 'function') {
                            showMessage('模板管理器未加载，请刷新页面重试', 'error');
                        } else {
                            alert('模板管理器未加载，请刷新页面重试');
                        }
                        return;
                    }

                    // 应用模板
                    TemplateManager.applyWebsiteTemplate(templateId);

                    // 显示页面管理器
                    this.showPageManager();

                    console.log('✅ 模板应用成功');
                    if (typeof showMessage === 'function') {
                        showMessage('模板应用成功！', 'success');
                    }
                } catch (error) {
                    console.error('❌ 应用模板失败:', error);
                    if (typeof showMessage === 'function') {
                        showMessage('应用模板失败: ' + error.message, 'error');
                    } else {
                        alert('应用模板失败: ' + error.message);
                    }
                }
            },
            true // 危险操作，使用红色按钮
        );
    },
    
    // 显示页面管理器
    showPageManager() {
        console.log('🔄 开始显示页面管理器...');

        const propertiesPanel = document.getElementById('properties-panel');
        if (!propertiesPanel) {
            console.error('❌ 属性面板未找到 #properties-panel');
            console.log('🔍 当前DOM中的面板元素:', document.querySelectorAll('[id*="properties"]'));
            return;
        }
        console.log('✅ 找到属性面板');

        // 直接替换整个属性面板内容，确保完全清除模板选择器
        const pageManagerHTML = `
            <div class="panel-title">页面管理</div>
            <div class="properties-content" id="properties-content">
                <div id="page-manager" class="page-manager">
                    <!-- 页面管理器内容将由 TemplateManager.updatePageManager 生成 -->
                </div>

                <div class="template-actions">
                    <button class="btn-back-to-templates" onclick="showTemplateSelector()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="m12 19-7-7 7-7"/>
                            <path d="M19 12H5"/>
                        </svg>
                        返回模板选择
                    </button>
                </div>
            </div>
        `;

        propertiesPanel.innerHTML = pageManagerHTML;
        console.log('✅ 页面管理器HTML已设置');

        // 延迟更新页面管理器，确保DOM已更新
        setTimeout(() => {
            if (typeof TemplateManager !== 'undefined' && TemplateManager.currentTemplate) {
                console.log('🔄 开始更新页面管理器...');
                TemplateManager.updatePageManager(TemplateManager.currentTemplate.pages);
                console.log('✅ 页面管理器已显示');
            } else {
                console.error('❌ TemplateManager 或当前模板未找到');
                console.log('TemplateManager 存在:', typeof TemplateManager !== 'undefined');
                console.log('当前模板存在:', TemplateManager && TemplateManager.currentTemplate);
            }
        }, 200);
    },
    
    // 创建自定义模板
    createCustomTemplate() {
        showInputDialog(
            '创建自定义模板',
            '请输入模板名称:',
            '我的自定义模板',
            (templateName) => {
                const templateId = templateName.toLowerCase().replace(/\s+/g, '-');

                // 创建空白模板
                const customTemplate = {
                    name: templateName,
                    description: '自定义模板',
                    preview: '/assets/images/templates/custom-preview.jpg',
                    globalSettings: {
                        siteName: templateName,
                        logo: '/assets/images/logo.png',
                        primaryColor: '#667eea',
                        secondaryColor: '#764ba2',
                        fontFamily: 'Microsoft YaHei, sans-serif'
                    },
                    pages: {
                        home: {
                            name: '首页',
                            path: '/',
                            icon: 'home',
                            components: []
                        }
                    }
                };

                // 添加到模板库
                websiteTemplates[templateId] = customTemplate;

                // 直接应用模板，不需要确认对话框
                try {
                    // 检查模板管理器是否存在
                    if (typeof TemplateManager === 'undefined') {
                        console.error('❌ TemplateManager 未定义');
                        if (typeof showMessage === 'function') {
                            showMessage('模板管理器未加载，请刷新页面重试', 'error');
                        }
                        return;
                    }

                    // 应用模板
                    TemplateManager.applyWebsiteTemplate(templateId);

                    // 显示页面管理器
                    this.showPageManager();

                    console.log('✅ 自定义模板应用成功');
                    if (typeof showMessage === 'function') {
                        showMessage(`自定义模板"${templateName}"创建并应用成功！`, 'success');
                    }
                } catch (error) {
                    console.error('❌ 应用自定义模板失败:', error);
                    if (typeof showMessage === 'function') {
                        showMessage('应用自定义模板失败: ' + error.message, 'error');
                    }
                }
            }
        );
    },
    
    // 绑定事件
    bindEvents() {
        // 模板卡片悬停效果
        document.addEventListener('mouseover', (e) => {
            if (e.target.closest('.template-card')) {
                e.target.closest('.template-card').classList.add('hover');
            }
        });

        document.addEventListener('mouseout', (e) => {
            if (e.target.closest('.template-card')) {
                e.target.closest('.template-card').classList.remove('hover');
            }
        });

        // 模板按钮点击事件
        document.addEventListener('click', (e) => {
            const button = e.target.closest('[data-action]');
            if (!button) return;

            e.preventDefault();
            e.stopPropagation();

            const action = button.dataset.action;
            const templateId = button.dataset.templateId;

            console.log('🔘 按钮点击:', action, templateId);

            if (action === 'apply') {
                this.applyTemplate(templateId);
            }
        });
    }
};

// 导出模块
window.TemplateSelector = TemplateSelector;

// 全局函数桥接
window.applyTemplate = function(templateId) {
    TemplateSelector.applyTemplate(templateId);
};

window.showTemplateSelector = function() {
    TemplateSelector.show();
};

window.showPage = function(pageId) {
    if (typeof TemplateManager !== 'undefined') {
        TemplateManager.showPage(pageId);
    } else {
        console.error('❌ TemplateManager 未定义');
    }
};

window.addPage = function() {
    if (typeof TemplateManager !== 'undefined') {
        TemplateManager.addPage();
    } else {
        console.error('❌ TemplateManager 未定义');
    }
};

window.createCustomTemplate = function() {
    TemplateSelector.createCustomTemplate();
};

{"version": 3, "sources": ["out-editor/vs/base/common/worker/simpleWorker.nls.ru.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\ndefine(\"vs/base/common/worker/simpleWorker.nls.ru\", {\n\t\"vs/base/common/platform\": [\n\t\t\"_\",\n\t],\n\t\"vs/editor/common/languages\": [\n\t\t\"массив\",\n\t\t\"логическое значение\",\n\t\t\"класс\",\n\t\t\"константа\",\n\t\t\"конструктор\",\n\t\t\"перечисление\",\n\t\t\"элемент перечисления\",\n\t\t\"событие\",\n\t\t\"поле\",\n\t\t\"файл\",\n\t\t\"функция\",\n\t\t\"интерфейс\",\n\t\t\"ключ\",\n\t\t\"метод\",\n\t\t\"модуль\",\n\t\t\"пространство имен\",\n\t\t\"NULL\",\n\t\t\"число\",\n\t\t\"объект\",\n\t\t\"оператор\",\n\t\t\"пакет\",\n\t\t\"свойство\",\n\t\t\"строка\",\n\t\t\"структура\",\n\t\t\"параметр типа\",\n\t\t\"Переменная\",\n\t\t\"{0} ({1})\",\n\t]\n});"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DA<PERSON>,OAAO,4CAA6C,CACnD,0BAA2B,CAC1B,GACD,EACA,6BAA8B,CAC7B,uCACA,gHACA,iCACA,yDACA,qEACA,2EACA,sHACA,6CACA,2BACA,2BACA,6CACA,yDACA,2BACA,iCACA,uCACA,oGACA,OACA,iCACA,uCACA,mDACA,iCACA,mDACA,uCACA,yDACA,4EACA,+DACA,WACD,CACD,CAAC", "names": [], "file": "simpleWorker.nls.ru.js"}
/* 新闻页面 - 蓝色渐变背景主题 */

/* ===== CSS 变量定义 ===== */
:root {
    --primary-color: #2c7be5;
    --secondary-color: #377dff;
    --accent-color: #00d4aa;
    --light-bg: #f8f9fa;
    --white: #ffffff;
    --text-dark: #495057;
    --text-gray: #6c757d;
    --text-light: #adb5bd;
    --border-light: #e9ecef;
    --border-color: #dee2e6;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.04);
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-lg: 0 8px 25px rgba(0,0,0,0.15);
    --transition: all 0.3s ease;
}

/* ===== 页面整体样式 ===== */
body {
    background: var(--light-bg);
    color: var(--text-dark);
    line-height: 1.6;
    font-family: -apple-system, BlinkMacSystemFont, "Se<PERSON>e UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
}

/* ===== 新闻页面包装器 ===== */
.news-page-wrapper {
    position: relative;
    min-height: 100vh;
    overflow: hidden;
}

.news-background {
    background: url('/assets/images/news-bg.png') no-repeat;
    background-position: center top !important;
    position: relative;
    padding: 40px 0 80px;
}

/* ===== 3D新闻插画装饰 ===== */
.hero-illustration {
    position: absolute;
    top: 3%;
    right: 10%;
    width: 400px;
    height: 300px;
    z-index: 10;
    opacity: 0.6;
    pointer-events: none;
}

.news-illustration {
    position: relative;
    width: 100%;
    height: 100%;
    transform: perspective(800px) rotateX(10deg) rotateY(-5deg);
}

/* 新闻报纸 */
.newspaper {
    position: absolute;
    bottom: 40px;
    left: 30px;
    width: 120px;
    height: 160px;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transform: rotate(-15deg);
    z-index: 5;
}

.newspaper-content {
    padding: 12px;
    height: 100%;
}

.newspaper-header {
    width: 100%;
    height: 20px;
    background: linear-gradient(135deg, #2c7be5, #377dff);
    border-radius: 4px;
    margin-bottom: 8px;
}

.newspaper-lines {
    height: calc(100% - 28px);
    background: repeating-linear-gradient(
        to bottom,
        #e9ecef 0px,
        #e9ecef 3px,
        transparent 3px,
        transparent 8px
    );
}

/* 信息流泡泡 */
.info-bubble {
    position: absolute;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, rgba(44, 123, 229, 0.9), rgba(55, 125, 255, 0.9));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    box-shadow: 0 4px 15px rgba(44, 123, 229, 0.3);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255,255,255,0.2);
}

.bubble-1 {
    top: 20px;
    right: 50px;
    z-index: 4;
}

.bubble-2 {
    top: 80px;
    right: 100px;
    z-index: 3;
    transform: scale(0.8);
}

.bubble-3 {
    top: 140px;
    right: 20px;
    z-index: 2;
    transform: scale(0.9);
}

/* 文档页面堆叠 */
.document-stack {
    position: absolute;
    bottom: 80px;
    right: 80px;
    width: 80px;
    height: 100px;
    z-index: 3;
}

.document {
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(145deg, #ffffff, #f1f3f4);
    border-radius: 8px 8px 6px 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.document::before {
    content: '';
    position: absolute;
    top: 15px;
    left: 12px;
    right: 12px;
    height: 3px;
    background: linear-gradient(135deg, #2c7be5, #377dff);
    border-radius: 2px;
}

.document::after {
    content: '';
    position: absolute;
    top: 25px;
    left: 12px;
    right: 12px;
    height: 50px;
    background: repeating-linear-gradient(
        to bottom,
        #e9ecef 0px,
        #e9ecef 1px,
        transparent 1px,
        transparent 6px
    );
}

.doc-1 {
    transform: translateZ(0px);
}

.doc-2 {
    transform: translateZ(-5px) translate(-3px, 3px);
    opacity: 0.8;
}

.doc-3 {
    transform: translateZ(-10px) translate(-6px, 6px);
    opacity: 0.6;
}

/* 新闻图标 */
.news-icon {
    position: absolute;
    top: 30px;
    left: 80px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #00d4aa, #26C6DA);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    box-shadow: 0 6px 20px rgba(0, 212, 170, 0.4);
    z-index: 6;
}

/* 数据流线条 */
.data-stream {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 150px;
    z-index: 1;
}

.stream-line {
    position: absolute;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(44, 123, 229, 0.3) 20%, 
        rgba(44, 123, 229, 0.6) 50%, 
        rgba(44, 123, 229, 0.3) 80%, 
        transparent 100%);
    border-radius: 2px;
    height: 3px;
}

.line-1 {
    top: 30px;
    left: 0;
    width: 120px;
    transform: rotate(15deg);
}

.line-2 {
    top: 70px;
    left: 40px;
    width: 100px;
    transform: rotate(-10deg);
}

.line-3 {
    top: 110px;
    left: 20px;
    width: 140px;
    transform: rotate(25deg);
}

/* 浮动动画 */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-8px) rotate(1deg); }
}

@keyframes bubble-float {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-12px) scale(1.05); }
}

@keyframes pulse {
    0%, 100% { opacity: 0.4; }
    50% { opacity: 0.8; }
}

@keyframes slide {
    0% { background-position: 0% 50%; }
    100% { background-position: 100% 50%; }
}

.news-illustration {
    animation: float 8s ease-in-out infinite;
}

.newspaper { 
    animation: float 10s ease-in-out infinite; 
    animation-delay: 1s; 
}

.info-bubble { 
    animation: bubble-float 6s ease-in-out infinite; 
}

.bubble-1 { animation-delay: 0s; }
.bubble-2 { animation-delay: 1s; }
.bubble-3 { animation-delay: 2s; }

.document-stack { 
    animation: float 12s ease-in-out infinite; 
    animation-delay: 0.5s; 
}

.news-icon { 
    animation: bubble-float 8s ease-in-out infinite; 
    animation-delay: 1.5s; 
}

.stream-line {
    animation: slide 4s ease-in-out infinite, pulse 3s ease-in-out infinite;
}

.line-1 { animation-delay: 0s, 0s; }
.line-2 { animation-delay: 0.5s, 1s; }
.line-3 { animation-delay: 1s, 2s; }

/* ===== 页面标题区域 ===== */
.page-header {
    position: relative;
    z-index: 10;
    padding: 60px 0 0;
    text-align: center;
}

.page-title-content {
    color: white;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
    letter-spacing: -0.02em;
}

.page-subtitle {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
    text-shadow: 0 1px 5px rgba(0,0,0,0.2);
}

.page-breadcrumb {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: rgba(255,255,255,0.2);
    padding: 12px 25px;
    border-radius: 30px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255,255,255,0.3);
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    margin-top: 150px;
    width: 1160px;
}

.breadcrumb-link {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: var(--transition);
    font-weight: 500;
}

.breadcrumb-link:hover {
    color: rgba(255,255,255,0.8);
    text-shadow: 0 0 10px rgba(255,255,255,0.5);
}

.breadcrumb-separator {
    color: rgba(255,255,255,0.7);
    font-weight: bold;
}

.breadcrumb-current {
    color: white;
    font-weight: 600;
    text-shadow: 0 0 8px rgba(255,255,255,0.3);
}

/* ===== 新闻内容区域 ===== */
.news-content {
    position: relative;
    z-index: 10;
    padding: 0 0 40px;
}

/* ===== 新闻分类部分 ===== */
.news-categories-section {
    margin-bottom: 40px;
}

.category-filters {
    background: url('/assets/images/index_section6_bg.png') center/cover,
                linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.85));
    border-radius: 20px;
    padding: 30px;
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255,255,255,0.3);
    position: relative;
    overflow: hidden;
}

.category-header {
    text-align: center;
    margin-bottom: 25px;
}

.category-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.category-subtitle {
    color: var(--text-gray);
    font-size: 0.95rem;
    margin: 0;
}

.category-tabs-wrapper {
    position: relative;
}

.category-tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    position: relative;
}

.category-tab {
    background: rgba(255,255,255,0.8);
    border: 2px solid transparent;
    border-radius: 25px;
    padding: 12px 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
    color: var(--text-dark);
    position: relative;
    overflow: hidden;
}

.category-tab:hover {
    background: rgba(44, 123, 229, 0.1);
    border-color: rgba(44, 123, 229, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(44, 123, 229, 0.2);
}

.category-tab.active {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 4px 15px rgba(44, 123, 229, 0.3);
}

.tab-accent {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 3px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    transition: var(--transition);
    border-radius: 2px;
}

.category-tab.active .tab-accent {
    width: 80%;
}

/* ===== 筛选状态提示 ===== */
.filter-status {
    background: #fff;
    border-radius: 15px;
    padding: 15px 20px;
    margin-bottom: 25px;
    backdrop-filter: blur(10px);
}

.filter-info {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--primary-color);
    font-weight: 500;
}

.filter-info i {
    font-size: 1.1rem;
}

.filter-info strong {
    color: var(--text-dark);
    background: rgba(44, 123, 229, 0.1);
    padding: 2px 8px;
    border-radius: 8px;
}

.clear-filter {
    margin-left: auto;
    color: var(--text-gray);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    border-radius: 8px;
    border: 1px solid white;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    transition: var(--transition);
    font-size: 0.9rem;
}

.clear-filter:hover {
    background: rgba(255, 107, 53, 0.2);
    color: #ff6b35;
    border-color: #ff6b35;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.2);
}

/* ===== 新闻列表部分 ===== */
.news-list-section {
    margin-bottom: 40px;
    position: relative; /* 为加载器提供相对定位容器 */
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
    justify-content: start; /* 左对齐，避免单个项目居中 */
    align-items: start; /* 顶部对齐 */
}

/* 当只有一个新闻项时，确保左对齐 */
.news-grid:has(.news-item:only-child) {
    justify-content: start;
    grid-template-columns: minmax(350px, 500px); /* 单个项目时固定宽度 */
}

/* 单个新闻项的最大宽度限制 */
.news-grid .news-item:only-child {
    max-width: 500px;
    justify-self: start; /* 强制左对齐 */
}

.news-item {
    transition: var(--transition);
    animation: fadeInUp 0.6s ease-out;
}

.news-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    height: 520px; /* 固定高度，确保所有卡片统一 */
    display: flex;
    flex-direction: column;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.news-image {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.news-card:hover .news-image img {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(44, 123, 229, 0.8), rgba(55, 125, 255, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.news-card:hover .image-overlay {
    opacity: 1;
}

.overlay-content {
    text-align: center;
    color: white;
    font-weight: 600;
}

.overlay-content i {
    font-size: 2rem;
    margin-bottom: 8px;
    display: block;
}

.featured-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, #ff6b35, #ff8e35);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.news-content {
    padding: 25px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.news-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem;
    color: var(--text-gray);
}

.meta-item.category {
    color: var(--primary-color);
    font-weight: 600;
}

.news-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 12px;
    line-height: 1.4;
}

.news-title a {
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
}

.news-title a:hover {
    color: var(--primary-color);
}

.news-excerpt {
    color: var(--text-gray);
    line-height: 1.6;
    margin-bottom: 20px;
    flex: 1;
    /* 限制摘要为3行，保持一致性 */
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 4.8em; /* 约等于3行的高度 */
}

.news-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

.read-more-btn1 {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: var(--transition);
    font-size: 0.9rem;
}

.read-more-btn1:hover {
    transform: translateX(3px);
    box-shadow: 0 4px 15px rgba(44, 123, 229, 0.3);
    color: white;
}

.news-author {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem;
    color: var(--text-gray);
}

/* ===== 分页导航 ===== */
.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 50px;
    margin-bottom: 30px;
}

.pagination-container .pagination,
.pagination-container ul.pagination {
    background: white;
    border-radius: 50px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    padding: 8px;
    border: 1px solid rgba(44, 123, 229, 0.1);
    display: flex;
    align-items: center;
    gap: 6px;
    list-style: none;
    margin: 0;
}

.pagination-container .pagination .page-item,
.pagination-container .pagination li {
    margin: 0;
    list-style: none;
}

.pagination-container .pagination .page-link,
.pagination-container .pagination a,
.pagination-container .pagination span {
    border: none;
    border-radius: 50%;
    margin: 0;
    padding: 0;
    color: var(--text-gray);
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: transparent;
    font-size: 0.9rem;
}

.pagination-container .pagination .page-link:hover,
.pagination-container .pagination a:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(44, 123, 229, 0.3);
}

.pagination-container .pagination .page-item.active .page-link,
.pagination-container .pagination .current,
.pagination-container .pagination .active a,
.pagination-container .pagination li.active span {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 4px 15px rgba(44, 123, 229, 0.3);
    font-weight: 600;
}

.pagination-container .pagination .page-item.disabled .page-link,
.pagination-container .pagination .disabled,
.pagination-container .pagination .disabled a {
    color: var(--text-light);
    background: transparent;
    cursor: not-allowed;
    opacity: 0.4;
}

.pagination-container .pagination .page-item.disabled .page-link:hover,
.pagination-container .pagination .disabled:hover,
.pagination-container .pagination .disabled a:hover {
    background: transparent;
    color: var(--text-light);
    transform: none;
    box-shadow: none;
    opacity: 0.4;
}

/* 上一页下一页按钮特殊样式 */
.pagination-container .pagination .page-item:first-child .page-link,
.pagination-container .pagination .page-item:last-child .page-link,
.pagination-container .pagination li:first-child a,
.pagination-container .pagination li:last-child a {
    font-weight: 600;
    width: 40px;
    height: 40px;

    border-radius: 50%;
    font-size: 0.9rem;
}

.pagination-container .pagination .page-item:first-child .page-link:hover,
.pagination-container .pagination .page-item:last-child .page-link:hover,
.pagination-container .pagination li:first-child a:hover,
.pagination-container .pagination li:last-child a:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(44, 123, 229, 0.3);
}

/* ===== 无新闻提示 ===== */
.no-news-found {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 20px;
    box-shadow: var(--shadow);
}

.no-news-content {
    max-width: 400px;
    margin: 0 auto;
}

.no-news-icon {
    font-size: 4rem;
    color: var(--text-light);
    margin-bottom: 20px;
}

.no-news-found h4 {
    color: var(--text-dark);
    margin-bottom: 15px;
    font-weight: 600;
}

.no-news-found p {
    color: var(--text-gray);
    margin-bottom: 25px;
    line-height: 1.6;
}

.btn-back-all {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: var(--transition);
}

.btn-back-all:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(44, 123, 229, 0.3);
    color: white;
}

/* ===== 侧边栏样式 ===== */
.news-sidebar {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.sidebar-widget {
    background: white;
    border-radius: 20px;
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
}

.sidebar-widget:hover {
    box-shadow: var(--shadow-lg);
}

.widget-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 20px 25px;
    position: relative;
}

.widget-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.widget-accent {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1));
}

.widget-content {
    padding: 25px;
}



/* ===== 热门新闻 ===== */
.hot-news-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.hot-news-item {
    display: flex;
    align-items: center; /* 改为居中对齐 */
    gap: 15px;
    padding: 15px;
    border-radius: 15px;
    border: 1px solid var(--border-light);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(5px);
    transition: var(--transition);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    height: 85px; /* 固定高度，所有项目统一 */
}

.hot-news-item:hover {
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(44, 123, 229, 0.2);
    box-shadow: 0 4px 15px rgba(44, 123, 229, 0.1);
    transform: translateY(-2px);
}

.hot-news-rank {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.9rem;
    flex-shrink: 0;
    box-shadow: 0 3px 10px rgba(44, 123, 229, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.9);
}

.hot-news-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center; /* 改为居中对齐，确保对称 */
    height: 55px; /* 固定内容区高度 */
    overflow: hidden;
    padding: 0; /* 移除内边距，让内容自然居中 */
}

.hot-news-content h5 {
    font-size: 0.9rem;
    margin: 0 0 4px 0; /* 只在底部添加小间距 */
    line-height: 1.1;
    /* 限制文本为2行 */
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 2.34em; /* 约等于2行的高度：0.9rem * 1.3 * 2 */
    font-weight: 500;
}

.hot-news-content h5 a {
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
}

.hot-news-content h5 a:hover {
    color: var(--primary-color);
}

.hot-news-meta {
    display: flex;
    gap: 12px;
    font-size: 0.75rem;
    color: var(--text-gray);
    margin: 0; /* 移除上边距，让布局更对称 */
    flex-shrink: 0; /* 不收缩 */
}

/* ===== 最新新闻 ===== */
.latest-news-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.latest-news-item {
    display: flex;
    align-items: center; /* 居中对齐 */
    gap: 12px;
    padding: 15px;
    border-radius: 12px;
    border: 1px solid var(--border-light);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(5px);
    transition: var(--transition);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    height: 100px; /* 固定高度，所有项目统一 */
}

.latest-news-item:hover {
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(44, 123, 229, 0.2);
    box-shadow: 0 4px 15px rgba(44, 123, 229, 0.1);
    transform: translateY(-2px);
}

.latest-news-image {
    width: 65px;
    height: 65px;
    border-radius: 12px;
    overflow: hidden;
    flex-shrink: 0;
    border: 2px solid rgba(44, 123, 229, 0.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.latest-news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.latest-news-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center; /* 改为居中对齐，确保对称 */
    height: 70px; /* 固定内容区高度 */
    overflow: hidden;
    padding: 0; /* 移除内边距，让内容自然居中 */
}

.latest-news-content h6 {
    font-size: 0.85rem;
    margin: 0 0 4px 0; /* 只在底部添加小间距 */
    line-height: 1.1;
    /* 限制文本为2行 */
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 2.21em; /* 约等于2行的高度：0.85rem * 1.3 * 2 */
    font-weight: 500;
}

.latest-news-content h6 a {
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
}

.latest-news-content h6 a:hover {
    color: var(--primary-color);
}

.latest-news-meta {
    display: flex;
    gap: 8px;
    font-size: 0.75rem;
    color: var(--text-gray);
    flex-wrap: wrap;
    margin: 0; /* 移除上边距，让布局更对称 */
    flex-shrink: 0; /* 不收缩 */
}

.latest-news-meta .category {
    color: var(--primary-color);
    font-weight: 500;
}

/* ===== 空状态样式 ===== */
.empty-state {
    text-align: center;
    padding: 20px;
    color: var(--text-gray);
    font-style: italic;
}

.empty-state p {
    margin: 0;
    font-size: 0.9rem;
}

/* ===== 加载器样式 ===== */
.news-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 100;
    background: rgba(255, 255, 255, 0.9);
    padding: 30px;
    border-radius: 15px;
    box-shadow: var(--shadow);
}

.loader-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-light);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.news-loader p {
    margin: 0;
    color: var(--text-gray);
    font-size: 0.9rem;
}

/* ===== 错误状态样式 ===== */
.error-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 20px;
    box-shadow: var(--shadow);
}

.error-icon {
    font-size: 3rem;
    color: #f56565;
    margin-bottom: 20px;
}

.error-state h4 {
    color: var(--text-dark);
    margin-bottom: 15px;
    font-weight: 600;
}

.error-state p {
    color: var(--text-gray);
    margin-bottom: 25px;
    line-height: 1.6;
}

.btn-retry {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: var(--transition);
}

.btn-retry:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(44, 123, 229, 0.3);
}

/* ===== 分类统计 ===== */
.categories-stats {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.category-stat-item {
    border-radius: 12px;
    border: 1px solid var(--border-light);
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(5px);
    transition: var(--transition);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.category-stat-item:hover {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(44, 123, 229, 0.2);
    box-shadow: 0 4px 15px rgba(44, 123, 229, 0.1);
    transform: translateY(-1px);
}

.category-stat-link {
    display: block;
    padding: 18px;
    text-decoration: none;
    color: inherit;
    border-radius: 12px;
}

.category-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.category-name {
    font-weight: 500;
    color: var(--text-dark);
}

.category-count {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: 600;
}

.category-progress {
    height: 4px;
    background: var(--border-light);
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
    transition: var(--transition);
}

/* ===== 标签云 ===== */
.tags-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag-item {
    background: var(--light-bg);
    color: var(--text-gray);
    padding: 6px 12px;
    border-radius: 15px;
    text-decoration: none;
    font-size: 0.85rem;
    transition: var(--transition);
    border: 1px solid var(--border-light);
}

.tag-item:hover,
.tag-item.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
}













/* ===== 响应式设计 ===== */
@media (max-width: 1200px) {    
    .hero-illustration {
        width: 300px;
        height: 250px;
        opacity: 0.2;
    }
}

@media (max-width: 992px) {
    .hero-illustration {
        display: none;
    }
}

@media (max-width: 768px) {
    .page-header {
        padding: 40px 0 30px;
    }
    
    .page-title {
        font-size: 2.2rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    .page-breadcrumb {
        padding: 8px 20px;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 1.8rem;
    }
    
    .page-breadcrumb {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }
    
    .breadcrumb-separator {
        display: none;
    }
    
    .news-background {
        padding: 20px 0 60px;
    }
}

/* ===== 动画效果 ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* AOS动画支持 */
[data-aos="fade-up"] {
    animation-duration: 0.6s;
    animation-fill-mode: both;
}

/* ===== 新闻页面响应式设计 ===== */
@media (max-width: 1200px) {
    .news-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
    }
    
    /* 单个新闻项响应式处理 */
    .news-grid:has(.news-item:only-child) {
        grid-template-columns: minmax(300px, 450px);
    }
    
    .news-card {
        height: 480px; /* 适应小屏幕的高度 */
    }
    
    .category-filters {
        padding: 25px;
    }
}

@media (max-width: 992px) {
    .news-sidebar {
        margin-top: 40px;
    }
    
    .news-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
    }
    
    /* 单个新闻项响应式处理 */
    .news-grid:has(.news-item:only-child) {
        grid-template-columns: minmax(280px, 400px);
    }
    
    .category-tabs {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 10px;
    }
    
    .category-tabs::-webkit-scrollbar {
        height: 4px;
    }
    
    .category-tabs::-webkit-scrollbar-track {
        background: var(--border-light);
        border-radius: 2px;
    }
    
    .category-tabs::-webkit-scrollbar-thumb {
        background: var(--primary-color);
        border-radius: 2px;
    }
}

@media (max-width: 768px) {
    .news-content {
        margin-top: 60px !important;
    }
    
    .category-filters {
        padding: 20px;
        margin-bottom: 30px;
    }
    
    .category-title {
        font-size: 1.3rem;
    }
    
    .news-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    /* 移动端单个新闻项处理 */
    .news-grid:has(.news-item:only-child) {
        grid-template-columns: 1fr;
    }
    
    .news-card {
        height: 450px; /* 移动端高度 */
    }
    
    .hot-news-item {
        height: 80px; /* 移动端热门新闻高度 */
    }
    
    .hot-news-content {
        height: 50px;
    }
    
    .latest-news-item {
        height: 90px; /* 移动端最新新闻高度 */
    }
    
    .latest-news-content {
        height: 60px;
    }
    
    .news-card {
        border-radius: 15px;
    }
    
    .news-image {
        height: 180px;
    }
    
    .news-content {
        padding: 20px;
    }
    
    .news-title {
        font-size: 1.1rem;
    }
    
    .widget-content {
        padding: 20px;
    }
    
    .hot-news-item {
        padding: 12px;
    }
    
    .hot-news-content h5 {
        font-size: 0.9rem;
    }
    
    /* 移动端分页器优化 */
    .pagination-container .pagination,
    .pagination-container ul.pagination {
        padding: 6px;
        gap: 4px;
    }
    
    .pagination-container .pagination .page-link,
    .pagination-container .pagination a,
    .pagination-container .pagination span {
        width: 36px;
        height: 36px;
        font-size: 0.85rem;
    }
    
    .pagination-container .pagination .page-item.active .page-link,
    .pagination-container .pagination .current,
    .pagination-container .pagination .active a,
    .pagination-container .pagination li.active span {
        background: var(--primary-color);
        color: white;
        box-shadow: 0 4px 15px rgba(44, 123, 229, 0.3);
        font-weight: 600;
    }
    
    .pagination-container .pagination .page-item:first-child .page-link,
    .pagination-container .pagination .page-item:last-child .page-link,
    .pagination-container .pagination li:first-child a,
    .pagination-container .pagination li:last-child a {
        width: 36px;
        height: 36px;
        font-size: 0.85rem;
    }
}

@media (max-width: 576px) {
    .category-filters {
        padding: 15px;
        border-radius: 15px;
    }
    
    .category-header {
        margin-bottom: 20px;
    }
    
    .category-title {
        font-size: 1.2rem;
        flex-direction: column;
        gap: 5px;
    }
    
    .category-subtitle {
        font-size: 0.9rem;
    }
    
    .category-tabs {
        gap: 8px;
    }
    
    .category-tab {
        padding: 10px 16px;
        font-size: 0.9rem;
    }
    
    .news-card {
        height: 420px; /* 小屏幕高度 */
    }
    
    .news-image {
        height: 160px;
    }
    
    .news-content {
        padding: 15px;
    }
    
    .news-meta {
        gap: 10px;
    }
    
    .meta-item {
        font-size: 0.8rem;
    }
    
    .news-title {
        font-size: 1rem;
    }
    
    .news-excerpt {
        font-size: 0.9rem;
        margin-bottom: 15px;
    }
    
    .read-more-btn {
        padding: 8px 16px;
        font-size: 0.85rem;
    }
    
    .sidebar-widget {
        border-radius: 15px;
    }
    
    .widget-header {
        padding: 15px 20px;
    }
    
    .widget-title {
        font-size: 1rem;
    }
    
    .widget-content {
        padding: 15px;
    }
    
    .hot-news-item {
        height: 75px; /* 小屏幕热门新闻高度 */
        padding: 12px;
    }
    
    .hot-news-content {
        height: 45px;
    }
    
    .hot-news-content h5 {
        font-size: 0.85rem;
        max-height: 2.21em;
    }
    
    .hot-news-meta {
        font-size: 0.7rem;
    }
    
    .latest-news-item {
        height: 85px; /* 小屏幕最新新闻高度 */
        padding: 12px;
    }
    
    .latest-news-content {
        height: 55px;
    }
    
    .latest-news-content h6 {
        font-size: 0.8rem;
        max-height: 2.08em;
    }
    
    .latest-news-meta {
        font-size: 0.7rem;
    }
    
    .latest-news-image {
        width: 55px;
        height: 55px;
    }
    
    /* 小屏幕分页器进一步优化 */
    .pagination-container .pagination,
    .pagination-container ul.pagination {
        padding: 4px;
        gap: 2px;
    }
    
    .pagination-container .pagination .page-link,
    .pagination-container .pagination a,
    .pagination-container .pagination span {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }
    
    .pagination-container .pagination .page-item.active .page-link,
    .pagination-container .pagination .current,
    .pagination-container .pagination .active a,
    .pagination-container .pagination li.active span {
        background: var(--primary-color);
        color: white;
        box-shadow: 0 4px 15px rgba(44, 123, 229, 0.3);
        font-weight: 600;
    }
    
    .pagination-container .pagination .page-item:first-child .page-link,
    .pagination-container .pagination .page-item:last-child .page-link,
    .pagination-container .pagination li:first-child a,
    .pagination-container .pagination li:last-child a {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }
    
    .no-news-found {
        padding: 40px 15px;
        border-radius: 15px;
    }
    
    .no-news-icon {
        font-size: 3rem;
    }
    
    .no-news-found h4 {
        font-size: 1.1rem;
    }
    
    .no-news-found p {
        font-size: 0.9rem;
    }
}

/* ===== 平板设备优化 ===== */
@media (min-width: 768px) and (max-width: 1024px) {
    .news-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }
    
    .category-tabs {
        justify-content: center;
    }
}

/* ===== 打印样式 ===== */
@media print {
    .news-sidebar,
    .pagination-wrapper,
    .news-categories-section {
        display: none;
    }
    
    .news-content {
        margin-top: 0 !important;
    }
    
    .news-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .news-card {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }
    
    .news-image {
        height: auto;
    }
    
    .image-overlay,
    .featured-badge {
        display: none;
    }
}

/* ===== 波纹效果 ===== */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(44, 123, 229, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* ===== 返回顶部按钮 ===== */
.back-to-top-btn:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 6px 20px rgba(44, 123, 229, 0.4);
}

/* ===== 加载骨架屏效果 ===== */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* ===== 工具提示 ===== */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: var(--text-dark);
    color: white;
    text-align: center;
    border-radius: 6px;
    padding: 5px 0;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.8rem;
}

.tooltip .tooltiptext::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--text-dark) transparent transparent transparent;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* ===== 新闻卡片悬停增强 ===== */
.news-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(44, 123, 229, 0.05), rgba(55, 125, 255, 0.05));
    opacity: 0;
    transition: var(--transition);
    pointer-events: none;
}

.news-card:hover::before {
    opacity: 1;
}

/* ===== 高对比度模式支持 ===== */
@media (prefers-contrast: high) {
    .category-tab {
        border: 2px solid var(--text-dark);
    }
    
    .news-card {
        border: 1px solid var(--border-color);
    }
    
    .sidebar-widget {
        border: 1px solid var(--border-color);
    }
}

/* ===== 减少动画模式支持 ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===== 无障碍访问优化 ===== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 焦点样式 */
.category-tab:focus,
.read-more-btn:focus,
.tag-item:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* ===== 暗色主题支持（可选） ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --light-bg: #1a1a1a;
        --white: #2d2d2d;
        --text-dark: #e0e0e0;
        --text-gray: #b0b0b0;
        --text-light: #888888;
        --border-light: #404040;
        --border-color: #505050;
    }
    
    .category-filters {
        background: linear-gradient(135deg, rgba(45, 45, 45, 0.9), rgba(45, 45, 45, 0.7));
    }
    
    .news-card {
        background: var(--white);
        color: var(--text-dark);
    }
    
    .sidebar-widget {
        background: var(--white);
        color: var(--text-dark);
    }
}

/* ===== 新闻详情页面专用样式 ===== */

/* 新闻详情文章卡片 */
.news-detail-card {
    background: var(--white);
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    margin-bottom: 40px;
    transition: var(--transition);
}

.news-detail-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(0,0,0,0.15);
}

/* 新闻详情头部图片 */
.news-detail-header {
    position: relative;
    margin-bottom: 30px;
}

.news-detail-image {
    position: relative;
    width: 100%;
    height: 400px;
    overflow: hidden;
    border-radius: 20px 20px 0 0;
}

.news-detail-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.news-detail-card:hover .news-detail-image img {
    transform: scale(1.05);
}

.news-detail-image .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        to bottom,
        rgba(0,0,0,0.1) 0%,
        rgba(0,0,0,0.3) 100%
    );
}

.overlay-gradient {
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(44, 123, 229, 0.1) 0%,
        rgba(55, 125, 255, 0.1) 100%
    );
}

/* 新闻详情元信息 */
.news-detail-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    padding: 20px 30px;
    background: rgba(248, 249, 255, 0.8);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-light);
}

.news-detail-meta .meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-gray);
    font-size: 0.9rem;
    font-weight: 500;
}

.news-detail-meta .meta-item i {
    color: var(--primary-color);
    font-size: 1rem;
}

.news-detail-meta .meta-item.featured {
    color: #f39c12;
}

.news-detail-meta .meta-item.featured i {
    color: #f39c12;
}

/* 新闻详情标题区域 */
.news-detail-title-section {
    padding: 30px;
    border-bottom: 1px solid var(--border-light);
}

.news-detail-title {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--text-dark);
    line-height: 1.3;
    margin-bottom: 20px;
}

/* 新闻摘要 */
.news-summary {
    display: flex;
    gap: 20px;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f1ff 100%);
    padding: 25px;
    border-radius: 15px;
    border-left: 5px solid var(--primary-color);
    margin-top: 20px;
}

.summary-icon {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.summary-content {
    flex: 1;
}

.summary-label {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 1rem;
}

.summary-text {
    color: var(--text-gray);
    line-height: 1.6;
    margin: 0;
    font-style: italic;
}

/* 新闻详情正文内容 */
.news-detail-content {
    padding: 30px;
}

.article-content {
    color: var(--text-dark);
    line-height: 1.8;
    font-size: 1.1rem;
    margin-bottom: 40px;
}

.article-content h1,
.article-content h2,
.article-content h3,
.article-content h4,
.article-content h5,
.article-content h6 {
    color: var(--text-dark);
    font-weight: 600;
    margin: 30px 0 15px 0;
}

.article-content h2 {
    font-size: 1.8rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

.article-content h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.article-content p {
    margin-bottom: 20px;
    text-align: justify;
}

.article-content img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    margin: 20px 0;
    box-shadow: var(--shadow);
}

.article-content blockquote {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f1ff 100%);
    border-left: 4px solid var(--primary-color);
    padding: 20px;
    margin: 20px 0;
    border-radius: 0 10px 10px 0;
    font-style: italic;
    color: var(--text-gray);
}

.article-content ul,
.article-content ol {
    padding-left: 30px;
    margin-bottom: 20px;
}

.article-content li {
    margin-bottom: 8px;
}

.article-content code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    color: #e83e8c;
}

.article-content pre {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    overflow-x: auto;
    margin: 20px 0;
    border: 1px solid var(--border-light);
}

/* 标签区域 */
.news-tags-section {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 2px solid var(--border-light);
}

.tags-title {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.tags-list .tag-item {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.tags-list .tag-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(44, 123, 229, 0.3);
    text-decoration: none;
    color: white;
}

/* 分享区域 */
.news-share-section {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 2px solid var(--border-light);
    text-align: center;
}

.share-title {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.share-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.share-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    font-size: 0.9rem;
}

.share-btn.facebook {
    background: linear-gradient(135deg, #1877f2, #42a5f5);
    color: white;
}

.share-btn.twitter {
    background: linear-gradient(135deg, #1da1f2, #42a5f5);
    color: white;
}

.share-btn.linkedin {
    background: linear-gradient(135deg, #0077b5, #42a5f5);
    color: white;
}

.share-btn.wechat {
    background: linear-gradient(135deg, #07c160, #42a5f5);
    color: white;
}

.share-btn.copy {
    background: linear-gradient(135deg, #6c757d, #adb5bd);
    color: white;
}

.share-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    text-decoration: none;
    color: white;
}

/* 相关文章推荐区域 */
.related-articles-section {
    margin-top: 50px;
}

.section-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-title {
    color: var(--text-dark);
    font-weight: 700;
    font-size: 1.8rem;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.section-title i {
    color: var(--primary-color);
    font-size: 2rem;
}

.section-accent {
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
    margin: 0 auto;
}

.related-articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.related-article-item {
    transition: var(--transition);
}

.related-article-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.related-article-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.related-article-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.related-article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.related-article-card:hover .related-article-image img {
    transform: scale(1.1);
}

.related-article-image .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    opacity: 0;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.related-article-card:hover .related-article-image .image-overlay {
    opacity: 1;
}

.related-article-image .overlay-content {
    color: white;
    text-align: center;
    font-weight: 600;
}

.related-article-image .overlay-content i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

.related-article-content {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.related-article-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 0.85rem;
    color: var(--text-gray);
}

.related-article-meta .category {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 4px 10px;
    border-radius: 12px;
    font-weight: 500;
}

.related-article-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
    line-height: 1.4;
}

.related-article-title a {
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
}

.related-article-title a:hover {
    color: var(--primary-color);
    text-decoration: none;
}

.related-article-excerpt {
    color: var(--text-gray);
    line-height: 1.6;
    margin: 0;
    flex: 1;
}

/* 返回列表按钮区域 */
.back-to-list-section {
    text-align: center;
    padding: 20px 0;
}

/* 响应式设计 - 新闻详情页面 */
@media (max-width: 768px) {
    .news-detail-title {
        font-size: 1.8rem;
    }
    
    .news-detail-image {
        height: 250px;
    }
    
    .news-detail-meta {
        padding: 15px 20px;
        gap: 15px;
    }
    
    .news-detail-title-section,
    .news-detail-content {
        padding: 20px;
    }
    
    .news-summary {
        flex-direction: column;
        gap: 15px;
        padding: 20px;
    }
    
    .summary-icon {
        align-self: flex-start;
    }
    
    .article-content {
        font-size: 1rem;
    }
    
    .share-buttons {
        gap: 10px;
    }
    
    .share-btn {
        padding: 10px 16px;
        font-size: 0.85rem;
    }
    
    .related-articles-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .section-title {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 10px;
    }
}

@media (max-width: 576px) {
    .news-detail-title {
        font-size: 1.5rem;
    }
    
    .news-detail-image {
        height: 200px;
    }
    
    .news-detail-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .news-detail-title-section,
    .news-detail-content {
        padding: 15px;
    }
    
    .article-content h2 {
        font-size: 1.5rem;
    }
    
    .article-content h3 {
        font-size: 1.3rem;
    }
    
    .share-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .share-btn {
        width: 200px;
        justify-content: center;
    }
    
    .tags-list {
        justify-content: center;
    }
}
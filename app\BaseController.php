<?php
declare (strict_types = 1);

namespace app;

use think\App;
use think\exception\ValidateException;
use think\Validate;
use think\facade\View;
use app\model\ProductCategory;
use app\model\Product;
use app\service\ConfigService;

/**
 * 控制器基础类
 */
abstract class BaseController
{
    /**
     * Request实例
     * @var \think\Request
     */
    protected $request;

    /**
     * 应用实例
     * @var \think\App
     */
    protected $app;

    /**
     * 是否批量验证
     * @var bool
     */
    protected $batchValidate = false;

    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [];

    /**
     * 构造方法
     * @access public
     * @param  App  $app  应用对象
     */
    public function __construct(App $app)
    {
        $this->app     = $app;
        $this->request = $this->app->request;

        // 控制器初始化
        $this->initialize();
    }

    // 初始化
    protected function initialize()
    {
        // 为前端页面提供全局数据
        $this->assignGlobalData();
    }

    /**
     * 分配全局数据给模板
     */
    protected function assignGlobalData()
    {
        // 只为前端页面加载产品数据，避免后台页面不必要的查询
        $currentController = $this->request->controller();
        $isAdminController = strpos(strtolower($currentController), 'admin') !== false;

        if (!$isAdminController) {
            try {
                // 获取站点配置
                $siteConfig = ConfigService::getAll();
                
                // 获取产品分类数据（按分类分组）
                $headerProductData = $this->getHeaderProductData();
                
                // 获取解决方案菜单数据（用于footer）
                $footerSolutions = $this->getFooterSolutionsData();
                
                // 获取产品分类数据（用于footer）
                $footerProductCategories = $this->getFooterProductCategoriesData();
                
                // 获取DIY页面数据（用于footer）
                $footerDiyPages = $this->getFooterDiyPagesData();

                // 分配给模板
                View::assign([
                    'siteConfig' => $siteConfig,
                    'headerProductData' => $headerProductData,
                    'footerSolutions' => $footerSolutions,
                    'footerProductCategories' => $footerProductCategories,
                    'footerDiyPages' => $footerDiyPages
                ]);
            } catch (\Exception $e) {
                // 如果获取数据失败，提供空数据避免页面报错
                View::assign([
                    'siteConfig' => [],
                    'headerProductData' => [],
                    'footerSolutions' => [],
                    'footerProductCategories' => [],
                    'footerDiyPages' => []
                ]);
            }
        } else {
            // 后台页面也需要站点配置，但不需要前端数据
            try {
                $siteConfig = ConfigService::getAll();
                View::assign([
                    'siteConfig' => $siteConfig,
                    'headerProductData' => [],
                    'footerSolutions' => [],
                    'footerProductCategories' => [],
                    'footerDiyPages' => []
                ]);
            } catch (\Exception $e) {
                View::assign([
                    'siteConfig' => [],
                    'headerProductData' => [],
                    'footerSolutions' => [],
                    'footerProductCategories' => [],
                    'footerDiyPages' => []
                ]);
            }
        }
    }

    /**
     * 获取头部产品菜单数据
     */
    protected function getHeaderProductData()
    {
        // 获取启用的顶级分类（最多4个）
        $categories = ProductCategory::where('status', 1)
            ->where('parent_id', 0)
            ->order('sort_order', 'asc')
            ->limit(4)
            ->select();

        $headerMenuData = [];

        foreach ($categories as $category) {
            // 获取该分类下的产品（最多3个）
            $products = Product::where('status', 1)
                ->where('category_id', $category->id)
                ->order('sort_order', 'desc')
                ->order('id', 'desc')
                ->limit(3)
                ->field('id,name,slug,icon,short_description')
                ->select();

            $headerMenuData[] = [
                'category' => $category,
                'products' => $products
            ];
        }

        return $headerMenuData;
    }

    /**
     * 获取footer解决方案数据
     */
    protected function getFooterSolutionsData()
    {
        try {
            // 从sys_menu表获取解决方案菜单数据
            $solutionsMenu = \think\facade\Db::table('sys_menu')
                ->where('name', '解决方案')
                ->where('status', 1)
                ->find();
            
            if ($solutionsMenu) {
                // 获取解决方案的子菜单
                $solutions = \think\facade\Db::table('sys_menu')
                    ->where('parent_id', $solutionsMenu['id'])
                    ->where('status', 1)
                    ->order('sort_order', 'asc')
                    ->limit(8) // 限制显示8个
                    ->select();
                
                return $solutions->toArray();
            }
            
            return [];
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 获取footer产品分类数据
     */
    protected function getFooterProductCategoriesData()
    {
        try {
            // 获取启用的产品分类
            $categories = ProductCategory::where('status', 1)
                ->where('parent_id', 0)
                ->order('sort_order', 'asc')
                ->limit(5) // 限制显示5个
                ->select();
            
            return $categories->toArray();
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 获取footer DIY页面数据
     */
    protected function getFooterDiyPagesData()
    {
        try {
            // 从page_templates表获取启用的普通页面（type为page）
            $pages = \think\facade\Db::table('page_templates')
                ->where('status', 1)
                ->where('slug', '<>', '')
                ->where('type', 'page') // 只获取type为page的普通页面
                ->order('sort_order', 'desc')
                ->order('updated_at', 'desc')
                ->limit(5) // 限制显示5个
                ->field('id,name,slug,description,type,updated_at')
                ->select();
            
            return $pages->toArray();
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 验证数据
     * @access protected
     * @param  array        $data     数据
     * @param  string|array $validate 验证器名或者验证规则数组
     * @param  array        $message  提示信息
     * @param  bool         $batch    是否批量验证
     * @return array|string|true
     * @throws ValidateException
     */
    protected function validate(array $data, string|array $validate, array $message = [], bool $batch = false)
    {
        if (is_array($validate)) {
            $v = new Validate();
            $v->rule($validate);
        } else {
            if (strpos($validate, '.')) {
                // 支持场景
                [$validate, $scene] = explode('.', $validate);
            }
            $class = false !== strpos($validate, '\\') ? $validate : $this->app->parseClass('validate', $validate);
            $v     = new $class();
            if (!empty($scene)) {
                $v->scene($scene);
            }
        }

        $v->message($message);

        // 是否批量验证
        if ($batch || $this->batchValidate) {
            $v->batch(true);
        }

        return $v->failException(true)->check($data);
    }

}

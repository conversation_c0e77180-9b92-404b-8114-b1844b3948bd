# 🚀 QiyeDIY企业建站系统 - 技术方案详细设计

**三只鱼网络科技 | 韩总 | 2024-12-19**
**项目代号：QiyeDIY - 新一代企业DIY建站系统**
**技术栈：ThinkPHP8 + Vue3 + Nuxt3 + GrapesJS**

---

## 📋 技术架构总览

### 🏗️ 系统架构图

```
┌─────────────────────────────────────────────────────────────────────┐
│                          用户访问层                                    │
│  Web浏览器 | 移动端浏览器 | 微信小程序 | APP内嵌页面                    │
├─────────────────────────────────────────────────────────────────────┤
│                          CDN加速层                                     │
│  静态资源CDN | 图片CDN | 视频CDN | 全球节点加速                        │
├─────────────────────────────────────────────────────────────────────┤
│                         负载均衡层                                     │
│  Nginx负载均衡 | SSL终端 | 请求分发 | 健康检查                         │
├─────────────────────────────────────────────────────────────────────┤
│                         应用服务层                                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐      │
│  │   前端展示层     │  │   管理后台层     │  │   后端API层      │      │
│  │   (Nuxt3)       │  │   (Vue3+TS)     │  │  (ThinkPHP8)    │      │
│  │                 │  │                 │  │                 │      │
│  │ • SSR渲染       │  │ • 可视化编辑器   │  │ • RESTful API   │      │
│  │ • 响应式设计     │  │ • 内容管理      │  │ • JWT认证       │      │
│  │ • SEO优化       │  │ • 权限管理      │  │ • 权限控制      │      │
│  │ • 页面缓存       │  │ • 数据统计      │  │ • 业务逻辑      │      │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘      │
├─────────────────────────────────────────────────────────────────────┤
│                         数据存储层                                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐      │
│  │   主数据库       │  │   缓存数据库     │  │   文件存储       │      │
│  │   (MySQL8)      │  │   (Redis7)      │  │   (本地/OSS)     │      │
│  │                 │  │                 │  │                 │      │
│  │ • 业务数据       │  │ • 会话缓存      │  │ • 图片文件      │      │
│  │ • 用户数据       │  │ • 页面缓存      │  │ • 视频文件      │      │
│  │ • 内容数据       │  │ • 数据缓存      │  │ • 文档文件      │      │
│  │ • 配置数据       │  │ • 队列任务      │  │ • 备份文件      │      │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘      │
├─────────────────────────────────────────────────────────────────────┤
│                         基础设施层                                     │
│  Docker容器 | Kubernetes编排 | 监控告警 | 日志收集 | 自动化部署        │
└─────────────────────────────────────────────────────────────────────┘
```

### 🔧 技术选型说明

#### 后端技术栈
- **ThinkPHP 8.0**: 现代化PHP框架，支持注解路由、依赖注入、中间件等特性
- **MySQL 8.0**: 高性能关系型数据库，支持JSON字段、窗口函数等新特性
- **Redis 7.0**: 内存数据库，用于缓存、会话存储、消息队列
- **JWT**: 无状态身份认证，支持分布式部署
- **Swagger**: API文档自动生成，提升开发效率

#### 前端管理后台技术栈
- **Vue 3.4**: 组合式API，更好的TypeScript支持，性能提升
- **TypeScript**: 类型安全，提升代码质量和开发效率
- **Vite 5.0**: 极速构建工具，热更新快，开发体验好
- **Element Plus**: 企业级UI组件库，组件丰富，文档完善
- **Pinia**: 轻量级状态管理，替代Vuex，更好的TypeScript支持
- **GrapesJS**: 专业可视化编辑器，功能强大，可扩展性强

#### 前端展示技术栈
- **Nuxt 3.8**: Vue.js全栈框架，SSR/SSG支持，SEO友好
- **TailwindCSS**: 原子化CSS框架，快速构建响应式界面
- **Headless UI**: 无样式组件库，完全可定制
- **Framer Motion**: 强大的动画库，提升用户体验

#### DIY编辑器选型
经过深入调研，推荐使用 **GrapesJS** 作为核心DIY编辑器：

**GrapesJS优势**：
1. **功能完整**: 拖拽编辑、样式管理、响应式设计、代码编辑
2. **可扩展性强**: 丰富的插件生态，支持自定义组件和插件
3. **社区活跃**: GitHub 19k+ stars，文档完善，问题解决快
4. **Vue集成友好**: 可以很好地集成到Vue项目中
5. **商业级稳定**: 被众多商业项目采用，稳定性有保障

**核心插件选择**：
- `grapesjs-blocks-basic`: 基础组件块
- `grapesjs-plugin-forms`: 表单组件
- `grapesjs-component-countdown`: 倒计时组件
- `grapesjs-plugin-export`: 导出功能
- `grapesjs-tui-image-editor`: 图片编辑器
- `grapesjs-style-gradient`: 渐变样式编辑
- `grapesjs-plugin-ckeditor`: 富文本编辑器集成

---

## 🗄️ 数据库设计详解

### 📊 数据库架构设计

#### 设计原则
1. **规范化设计**: 遵循第三范式，减少数据冗余
2. **性能优化**: 合理使用索引，优化查询性能
3. **扩展性**: 预留扩展字段，支持业务发展
4. **安全性**: 敏感数据加密，权限控制严格
5. **一致性**: 使用外键约束，保证数据一致性

#### 核心表结构详解

##### 1. 用户和权限管理模块

```sql
-- 用户表
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `email_verified_at` timestamp NULL DEFAULT NULL COMMENT '邮箱验证时间',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `login_count` int unsigned NOT NULL DEFAULT '0' COMMENT '登录次数',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_username_unique` (`username`),
  UNIQUE KEY `users_email_unique` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 角色表
CREATE TABLE `roles` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `slug` varchar(50) NOT NULL COMMENT '角色标识',
  `description` text COMMENT '角色描述',
  `permissions` json DEFAULT NULL COMMENT '权限列表',
  `is_default` tinyint NOT NULL DEFAULT '0' COMMENT '是否默认角色',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `roles_slug_unique` (`slug`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 用户角色关联表
CREATE TABLE `user_roles` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `role_id` bigint unsigned NOT NULL COMMENT '角色ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_roles_user_role_unique` (`user_id`,`role_id`),
  KEY `fk_user_roles_role_id` (`role_id`),
  CONSTRAINT `fk_user_roles_role_id` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_roles_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- 权限表
CREATE TABLE `permissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `name` varchar(100) NOT NULL COMMENT '权限名称',
  `slug` varchar(100) NOT NULL COMMENT '权限标识',
  `description` text COMMENT '权限描述',
  `module` varchar(50) NOT NULL COMMENT '所属模块',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `resource` varchar(100) DEFAULT NULL COMMENT '资源标识',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `permissions_slug_unique` (`slug`),
  KEY `idx_module` (`module`),
  KEY `idx_action` (`action`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';
```

##### 2. DIY系统核心模块

```sql
-- DIY页面表
CREATE TABLE `diy_pages` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '页面ID',
  `title` varchar(200) NOT NULL COMMENT '页面标题',
  `slug` varchar(200) NOT NULL COMMENT '页面别名',
  `description` text COMMENT '页面描述',
  `content` longtext COMMENT '页面内容(JSON格式)',
  `template_id` bigint unsigned DEFAULT NULL COMMENT '模板ID',
  `layout` varchar(50) DEFAULT 'default' COMMENT '布局类型',
  `theme` varchar(50) DEFAULT 'default' COMMENT '主题样式',
  `custom_css` text COMMENT '自定义CSS',
  `custom_js` text COMMENT '自定义JS',
  `seo_title` varchar(200) DEFAULT NULL COMMENT 'SEO标题',
  `seo_description` text COMMENT 'SEO描述',
  `seo_keywords` varchar(500) DEFAULT NULL COMMENT 'SEO关键词',
  `og_image` varchar(255) DEFAULT NULL COMMENT 'OG图片',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态:0草稿,1发布,2下线',
  `is_home` tinyint NOT NULL DEFAULT '0' COMMENT '是否首页',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `view_count` int unsigned NOT NULL DEFAULT '0' COMMENT '浏览次数',
  `created_by` bigint unsigned DEFAULT NULL COMMENT '创建者',
  `updated_by` bigint unsigned DEFAULT NULL COMMENT '更新者',
  `published_at` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `diy_pages_slug_unique` (`slug`),
  KEY `idx_status` (`status`),
  KEY `idx_is_home` (`is_home`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_created_by` (`created_by`),
  KEY `fk_diy_pages_template_id` (`template_id`),
  CONSTRAINT `fk_diy_pages_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_diy_pages_template_id` FOREIGN KEY (`template_id`) REFERENCES `diy_templates` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='DIY页面表';

-- DIY组件表
CREATE TABLE `diy_components` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '组件ID',
  `name` varchar(100) NOT NULL COMMENT '组件名称',
  `slug` varchar(100) NOT NULL COMMENT '组件标识',
  `type` varchar(50) NOT NULL COMMENT '组件类型',
  `category` varchar(50) NOT NULL COMMENT '组件分类',
  `description` text COMMENT '组件描述',
  `icon` varchar(100) DEFAULT NULL COMMENT '组件图标',
  `preview` varchar(255) DEFAULT NULL COMMENT '预览图',
  `config` json DEFAULT NULL COMMENT '组件配置',
  `default_props` json DEFAULT NULL COMMENT '默认属性',
  `style_config` json DEFAULT NULL COMMENT '样式配置',
  `is_system` tinyint NOT NULL DEFAULT '0' COMMENT '是否系统组件',
  `is_active` tinyint NOT NULL DEFAULT '1' COMMENT '是否启用',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `usage_count` int unsigned NOT NULL DEFAULT '0' COMMENT '使用次数',
  `created_by` bigint unsigned DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `diy_components_slug_unique` (`slug`),
  KEY `idx_type` (`type`),
  KEY `idx_category` (`category`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `fk_diy_components_created_by` (`created_by`),
  CONSTRAINT `fk_diy_components_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='DIY组件表';

-- DIY模板表
CREATE TABLE `diy_templates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `slug` varchar(100) NOT NULL COMMENT '模板标识',
  `description` text COMMENT '模板描述',
  `preview` varchar(255) DEFAULT NULL COMMENT '预览图',
  `thumbnail` varchar(255) DEFAULT NULL COMMENT '缩略图',
  `content` longtext COMMENT '模板内容(JSON格式)',
  `category` varchar(50) NOT NULL COMMENT '模板分类',
  `tags` json DEFAULT NULL COMMENT '模板标签',
  `is_system` tinyint NOT NULL DEFAULT '0' COMMENT '是否系统模板',
  `is_free` tinyint NOT NULL DEFAULT '1' COMMENT '是否免费',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '模板价格',
  `download_count` int unsigned NOT NULL DEFAULT '0' COMMENT '下载次数',
  `rating` decimal(3,2) DEFAULT '0.00' COMMENT '评分',
  `rating_count` int unsigned NOT NULL DEFAULT '0' COMMENT '评分人数',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `created_by` bigint unsigned DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `diy_templates_slug_unique` (`slug`),
  KEY `idx_category` (`category`),
  KEY `idx_is_free` (`is_free`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_rating` (`rating`),
  KEY `fk_diy_templates_created_by` (`created_by`),
  CONSTRAINT `fk_diy_templates_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='DIY模板表';

-- DIY页面区块表
CREATE TABLE `diy_page_blocks` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '区块ID',
  `page_id` bigint unsigned NOT NULL COMMENT '页面ID',
  `component_id` bigint unsigned NOT NULL COMMENT '组件ID',
  `block_id` varchar(100) NOT NULL COMMENT '区块标识',
  `parent_id` bigint unsigned DEFAULT NULL COMMENT '父区块ID',
  `props` json DEFAULT NULL COMMENT '组件属性',
  `styles` json DEFAULT NULL COMMENT '样式配置',
  `children` json DEFAULT NULL COMMENT '子组件',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `is_visible` tinyint NOT NULL DEFAULT '1' COMMENT '是否可见',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_page_id` (`page_id`),
  KEY `idx_component_id` (`component_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_sort_order` (`sort_order`),
  CONSTRAINT `fk_diy_page_blocks_component_id` FOREIGN KEY (`component_id`) REFERENCES `diy_components` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_diy_page_blocks_page_id` FOREIGN KEY (`page_id`) REFERENCES `diy_pages` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_diy_page_blocks_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `diy_page_blocks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='DIY页面区块表';
```
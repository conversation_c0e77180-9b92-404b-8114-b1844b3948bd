/**
 * 图片上传弹窗组件使用示例
 * 作者: AI龙头韩哥
 * 版本: 1.0.0
 */

// ========================================
// 基本使用示例
// ========================================

// 1. 最简单的使用方式
function basicExample() {
    const uploader = createImageUploader({
        onSelect: function(files) {
            console.log('选择了文件:', files);
        }
    });
    
    // 显示弹窗
    uploader.show();
}

// 2. 完整配置示例
function fullConfigExample() {
    const uploader = createImageUploader({
        // 基本配置
        multiple: true,                    // 是否支持多选
        maxFiles: 10,                     // 最大文件数量
        maxSize: 5 * 1024 * 1024,        // 最大文件大小 (5MB)
        allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
        
        // 上传配置
        uploadUrl: '/admin/upload',       // 上传接口
        uploadField: 'upload',            // 上传字段名
        
        // 回调函数
        onSelect: function(files, allFiles) {
            console.log('新选择的文件:', files);
            console.log('所有文件:', allFiles);
        },
        onUpload: function(uploadedFiles) {
            console.log('上传成功的文件:', uploadedFiles);
            // uploadedFiles 格式: [{ file: File, url: string, response: object }]
        },
        onError: function(error) {
            console.error('上传出错:', error);
        },
        onClose: function() {
            console.log('弹窗已关闭');
        }
    });
    
    uploader.show();
}

// ========================================
// 在不同场景中的使用示例
// ========================================

// 3. 单张图片选择（头像上传）
function avatarUploadExample() {
    const uploader = createImageUploader({
        multiple: false,
        maxFiles: 1,
        maxSize: 2 * 1024 * 1024, // 2MB
        onSelect: function(files) {
            if (files.length > 0) {
                const file = files[0];
                // 预览头像
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('avatar-preview').src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        },
        onUpload: function(uploadedFiles) {
            if (uploadedFiles.length > 0) {
                const avatarUrl = uploadedFiles[0].url;
                // 更新用户头像
                updateUserAvatar(avatarUrl);
            }
        }
    });
    
    // 绑定到头像点击事件
    document.getElementById('avatar-upload-btn').addEventListener('click', function() {
        uploader.show();
    });
}

// 4. 商品图片上传（多张图片）
function productImagesExample() {
    const uploader = createImageUploader({
        multiple: true,
        maxFiles: 20,
        maxSize: 10 * 1024 * 1024, // 10MB
        onSelect: function(files) {
            console.log(`选择了 ${files.length} 张商品图片`);
        },
        onUpload: function(uploadedFiles) {
            // 将上传的图片添加到商品图片列表
            uploadedFiles.forEach(fileData => {
                addProductImage(fileData.url);
            });
            
            // 关闭弹窗
            uploader.close();
        }
    });
    
    // 绑定到添加图片按钮
    document.getElementById('add-product-images').addEventListener('click', function() {
        uploader.show();
    });
}

// 5. 富文本编辑器集成
function editorIntegrationExample() {
    let editor; // 假设这是你的编辑器实例
    
    const uploader = createImageUploader({
        multiple: true,
        maxFiles: 50,
        onUpload: function(uploadedFiles) {
            // 将图片插入到编辑器中
            uploadedFiles.forEach(fileData => {
                insertImageToEditor(editor, fileData.url);
            });
            uploader.close();
        }
    });
    
    // 添加到编辑器工具栏
    function addImageButtonToEditor() {
        const toolbar = document.querySelector('.editor-toolbar');
        const imageBtn = document.createElement('button');
        imageBtn.innerHTML = '<i class="fas fa-images"></i> 插入图片';
        imageBtn.addEventListener('click', function() {
            uploader.show();
        });
        toolbar.appendChild(imageBtn);
    }
    
    function insertImageToEditor(editor, imageUrl) {
        // 根据你的编辑器类型实现图片插入逻辑
        const imageHtml = `<img src="${imageUrl}" alt="插入的图片" style="max-width: 100%;">`;
        editor.insertHTML(imageHtml);
    }
}

// ========================================
// 高级使用示例
// ========================================

// 6. 自定义上传处理
function customUploadExample() {
    const uploader = createImageUploader({
        // 不设置 uploadUrl，手动处理上传
        onSelect: function(files) {
            // 手动上传每个文件
            files.forEach(file => {
                customUploadFile(file);
            });
        }
    });
    
    function customUploadFile(file) {
        const formData = new FormData();
        formData.append('image', file);
        formData.append('category', 'product');
        formData.append('user_id', getCurrentUserId());
        
        fetch('/api/upload', {
            method: 'POST',
            body: formData,
            headers: {
                'Authorization': 'Bearer ' + getAuthToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('上传成功:', data.url);
                // 处理上传成功
            } else {
                console.error('上传失败:', data.message);
            }
        })
        .catch(error => {
            console.error('上传出错:', error);
        });
    }
}

// 7. 图片压缩和处理
function imageProcessingExample() {
    const uploader = createImageUploader({
        onSelect: function(files) {
            // 对选择的图片进行压缩处理
            files.forEach(file => {
                compressImage(file, function(compressedFile) {
                    console.log('原始大小:', file.size, '压缩后:', compressedFile.size);
                    // 可以替换原文件或添加到列表
                });
            });
        }
    });
    
    function compressImage(file, callback) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        
        img.onload = function() {
            // 计算压缩后的尺寸
            const maxWidth = 1920;
            const maxHeight = 1080;
            let { width, height } = img;
            
            if (width > maxWidth || height > maxHeight) {
                const ratio = Math.min(maxWidth / width, maxHeight / height);
                width *= ratio;
                height *= ratio;
            }
            
            canvas.width = width;
            canvas.height = height;
            
            // 绘制压缩后的图片
            ctx.drawImage(img, 0, 0, width, height);
            
            // 转换为Blob
            canvas.toBlob(callback, 'image/jpeg', 0.8);
        };
        
        img.src = URL.createObjectURL(file);
    }
}

// ========================================
// 组件方法使用示例
// ========================================

// 8. 组件方法调用示例
function methodsExample() {
    const uploader = createImageUploader();
    
    // 显示弹窗
    uploader.show();
    
    // 隐藏弹窗
    uploader.close();
    
    // 获取选中的文件
    const selectedFiles = uploader.getSelectedFiles();
    console.log('选中的文件:', selectedFiles);
    
    // 获取已上传的文件
    const uploadedFiles = uploader.getUploadedFiles();
    console.log('已上传的文件:', uploadedFiles);
    
    // 重置组件
    uploader.reset();
    
    // 销毁组件
    uploader.destroy();
}

// ========================================
// 样式自定义示例
// ========================================

// 9. 自定义样式
function customStyleExample() {
    // 在CSS中覆盖默认样式
    const customCSS = `
        .image-uploader-modal {
            max-width: 1200px !important;
        }
        
        .upload-zone {
            min-height: 400px !important;
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%) !important;
        }
        
        .preview-grid {
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)) !important;
        }
    `;
    
    // 动态添加样式
    const style = document.createElement('style');
    style.textContent = customCSS;
    document.head.appendChild(style);
}

// ========================================
// 错误处理示例
// ========================================

// 10. 完整的错误处理
function errorHandlingExample() {
    const uploader = createImageUploader({
        onError: function(error) {
            // 统一错误处理
            console.error('上传组件错误:', error);
            
            // 显示用户友好的错误信息
            showUserMessage('图片上传失败，请重试', 'error');
            
            // 发送错误报告
            sendErrorReport(error);
        },
        
        onSelect: function(files, allFiles) {
            // 验证文件
            const validFiles = files.filter(file => {
                if (file.size > 10 * 1024 * 1024) {
                    showUserMessage(`文件 ${file.name} 过大，已跳过`, 'warning');
                    return false;
                }
                return true;
            });
            
            if (validFiles.length !== files.length) {
                console.log(`过滤了 ${files.length - validFiles.length} 个无效文件`);
            }
        }
    });
    
    function showUserMessage(message, type) {
        // 实现用户消息显示逻辑
        console.log(`${type.toUpperCase()}: ${message}`);
    }
    
    function sendErrorReport(error) {
        // 发送错误报告到服务器
        fetch('/api/error-report', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                error: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent
            })
        });
    }
}

// ========================================
// 导出示例函数（可选）
// ========================================

if (typeof window !== 'undefined') {
    window.ImageUploaderExamples = {
        basic: basicExample,
        fullConfig: fullConfigExample,
        avatar: avatarUploadExample,
        product: productImagesExample,
        editor: editorIntegrationExample,
        custom: customUploadExample,
        processing: imageProcessingExample,
        methods: methodsExample,
        style: customStyleExample,
        error: errorHandlingExample
    };
} 
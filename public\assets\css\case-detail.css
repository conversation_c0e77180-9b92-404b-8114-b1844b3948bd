/* 案例详情页面样式 - 优化色调版本 */

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 主容器 */
.case-detail-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    color: #ffffff;
    position: relative;
    overflow-x: hidden;
}

/* 背景装饰 */
.case-detail-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.bg-gradient-primary {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 15% 25%, rgba(120, 119, 198, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 85% 75%, rgba(255, 107, 157, 0.12) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(120, 119, 198, 0.08) 0%, transparent 70%);
    animation: gradientShift 25s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
    50% { transform: scale(1.1) rotate(180deg); opacity: 0.8; }
}

.bg-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(255, 107, 157, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(120, 119, 198, 0.4), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.2), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(120, 119, 198, 0.3), transparent);
    background-repeat: repeat;
    background-size: 150px 100px;
    animation: particleMove 30s linear infinite;
}

@keyframes particleMove {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-100px); }
}

.bg-circles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.circle {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.1), rgba(255, 107, 157, 0.1));
    animation: circleFloat 20s ease-in-out infinite;
}

.circle-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    left: 5%;
    animation-delay: 0s;
}

.circle-2 {
    width: 200px;
    height: 200px;
    top: 60%;
    right: 10%;
    animation-delay: 5s;
}

.circle-3 {
    width: 150px;
    height: 150px;
    top: 30%;
    right: 30%;
    animation-delay: 10s;
}

.circle-4 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 15s;
}

@keyframes circleFloat {
    0%, 100% { transform: translateY(0px) scale(1); opacity: 0.3; }
    50% { transform: translateY(-30px) scale(1.1); opacity: 0.6; }
}

/* 案例标题区域 */
.case-header-section {
    padding: 100px 0 60px;
    position: relative;
    z-index: 10;
    background: linear-gradient(135deg, rgba(15, 15, 35, 0.9), rgba(26, 26, 46, 0.8));
}

.case-3d-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.floating-case-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.geometric-shape {
    position: absolute;
    animation: shapeFloat 15s ease-in-out infinite;
}

.shape-circle-1 {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 107, 157, 0.6), rgba(120, 119, 198, 0.6));
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-circle-2 {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.6), rgba(255, 107, 157, 0.6));
    top: 70%;
    right: 15%;
    animation-delay: 5s;
}

.shape-triangle-1 {
    width: 0;
    height: 0;
    border-left: 30px solid transparent;
    border-right: 30px solid transparent;
    border-bottom: 50px solid rgba(255, 107, 157, 0.6);
    top: 40%;
    right: 25%;
    animation-delay: 8s;
}

.shape-square-1 {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.6), rgba(255, 107, 157, 0.6));
    transform: rotate(45deg);
    top: 60%;
    left: 20%;
    animation-delay: 12s;
}

.particle-system {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: particleFloat 20s linear infinite;
}

.particle-1 {
    top: 10%;
    left: 15%;
    animation-delay: 0s;
    animation-duration: 18s;
}

.particle-2 {
    top: 25%;
    left: 80%;
    animation-delay: 2s;
    animation-duration: 22s;
}

.particle-3 {
    top: 40%;
    left: 30%;
    animation-delay: 4s;
    animation-duration: 16s;
}

.particle-4 {
    top: 55%;
    left: 70%;
    animation-delay: 6s;
    animation-duration: 24s;
}

.particle-5 {
    top: 70%;
    left: 20%;
    animation-delay: 8s;
    animation-duration: 20s;
}

.particle-6 {
    top: 85%;
    left: 60%;
    animation-delay: 10s;
    animation-duration: 18s;
}

.particle-7 {
    top: 15%;
    left: 50%;
    animation-delay: 12s;
    animation-duration: 22s;
}

.particle-8 {
    top: 35%;
    left: 85%;
    animation-delay: 14s;
    animation-duration: 16s;
}

.particle-9 {
    top: 50%;
    left: 10%;
    animation-delay: 16s;
    animation-duration: 24s;
}

.particle-10 {
    top: 65%;
    left: 45%;
    animation-delay: 18s;
    animation-duration: 20s;
}

.particle-11 {
    top: 80%;
    left: 75%;
    animation-delay: 20s;
    animation-duration: 18s;
}

.particle-12 {
    top: 95%;
    left: 35%;
    animation-delay: 22s;
    animation-duration: 22s;
}

.case-header-content {
    text-align: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.case-title-wrapper {
    margin-bottom: 40px;
}

.case-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #ffffff, #7877c6, #ff6b9d);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleGlow 3s ease-in-out infinite alternate;
}

.case-subtitle {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    max-width: 800px;
    margin: 0 auto;
}

.case-meta-info {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 25px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: fadeInUp 1s ease-out;
}

.meta-item:nth-child(1) { animation-delay: 0.5s; }
.meta-item:nth-child(2) { animation-delay: 0.6s; }
.meta-item:nth-child(3) { animation-delay: 0.7s; }

.meta-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(120, 119, 198, 0.3);
}

.meta-item i {
    font-size: 1.2rem;
    color: #ff6b9d;
}

.meta-item span {
    font-size: 1rem;
    color: #ffffff;
    font-weight: 500;
}

/* 导航区域 */
.case-nav-wrapper {
    padding: 20px 0;
    background: rgba(15, 15, 35, 0.9);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(120, 119, 198, 0.2);
}

.case-nav-wrapper .case-breadcrumb {
    max-width: 1160px;
    margin: 0 auto;
    padding: 0 20px;
}

.case-breadcrumb {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.breadcrumb-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    padding: 8px 15px;
    border-radius: 20px;
    transition: all 0.3s ease;

}

.breadcrumb-link:hover {
    color: #ffffff;
    background: rgba(120, 119, 198, 0.2);
    transform: translateY(-2px);
}

.separator {
    color: rgba(255, 255, 255, 0.4);
    font-size: 0.8rem;
}

.breadcrumb-current {
    color: #7877c6;
    font-weight: 600;
    padding: 8px 15px;
    background: rgba(120, 119, 198, 0.1);
    border-radius: 20px;
    border: 1px solid rgba(120, 119, 198, 0.3);
}

/* 主要内容区域 */
.case-main-section {
    padding: 80px 0;
    position: relative;
    z-index: 10;
}

.case-content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    max-width: 1300px;
    margin: 0 auto;
    padding: 0 20px;
}

.case-image-section {
    position: relative;
}

.image-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    background: rgba(120, 119, 198, 0.1);
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(120, 119, 198, 0.3);
    box-shadow: 0 0 20px rgba(120, 119, 198, 0.3);
    transition: all 0.3s ease;
}

.image-container:hover {
    /* 移除悬停发光效果 */
}

.main-image {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    background: #1a1a2e;
    height: 300px;
    width: 100%;
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
}

/* 移除图片悬停缩放效果 */

/* 移除图片悬停遮罩效果 */

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.placeholder-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    color: rgba(255, 255, 255, 0.7);
    border: 2px dashed rgba(120, 119, 198, 0.4);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.placeholder-image:hover {
    border-color: rgba(255, 107, 157, 0.6);
    color: rgba(255, 255, 255, 0.9);
}

.placeholder-image i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
    color: rgba(120, 119, 198, 0.5);
}

.placeholder-image span {
    font-size: 1.1rem;
    font-weight: 500;
}

.case-info-section {
    position: relative;
}

.info-card {
    background: rgba(120, 119, 198, 0.1);
    border-radius: 20px;
    padding: 30px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(120, 119, 198, 0.3);
    box-shadow: 0 0 20px rgba(120, 119, 198, 0.3);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.1), rgba(255, 107, 157, 0.1));
    opacity: 0;
    transition: all 0.3s ease;
    z-index: -1;
}

.info-card:hover {
    /* 移除悬停发光效果 */
}

.info-card:hover::before {
    /* 移除悬停发光效果 */
}

.card-header {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(120, 119, 198, 0.2);
}

.card-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.5rem;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
}

.card-title i {
    color: #ff6b9d;
    font-size: 1.3rem;
}

.card-content {
    position: relative;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px 20px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(120, 119, 198, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.info-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(135deg, #7877c6, #ff6b9d);
    transform: scaleY(0);
    transition: all 0.3s ease;
}

.info-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateX(5px);
    border-color: rgba(120, 119, 198, 0.4);
    box-shadow: 0 5px 15px rgba(120, 119, 198, 0.2);
}

.info-item:hover::before {
    transform: scaleY(1);
}

.info-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.3), rgba(255, 107, 157, 0.2));
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    border: 1px solid rgba(120, 119, 198, 0.3);
    transition: all 0.3s ease;
}

.info-icon i {
    color: #7877c6;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.info-item:hover .info-icon {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.5), rgba(255, 107, 157, 0.3));
    border-color: rgba(120, 119, 198, 0.5);
    transform: scale(1.05);
}

.info-item:hover .info-icon i {
    color: #ff6b9d;
    transform: scale(1.1);
}

.info-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.info-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

.info-value {
    font-size: 1rem;
    color: #ffffff;
    font-weight: 600;
}

.project-link {
    color: #7877c6;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
    position: relative;
}

.project-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, #7877c6, #ff6b9d);
    transition: all 0.3s ease;
}

.project-link:hover {
    color: #ff6b9d;
    transform: translateX(3px);
}

.project-link:hover::after {
    width: 100%;
}

/* 项目详情描述 */
.case-description-section {
    position: relative;
    z-index: 10;
}

.case-description-section .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.case-description-section .case-content-grid {
    display: block;
    max-width: 1160px;
    margin: 0 auto;
    padding: 0 20px;
}

.description-card {
    background: rgba(120, 119, 198, 0.1);
    border-radius: 20px;
    padding: 40px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(120, 119, 198, 0.3);
    box-shadow: 0 0 20px rgba(120, 119, 198, 0.3);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.description-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.1), rgba(255, 107, 157, 0.1));
    opacity: 0;
    transition: all 0.3s ease;
    z-index: -1;
}

.description-card:hover {
    /* 移除悬停发光效果 */
}

.description-card:hover::before {
    /* 移除悬停发光效果 */
}

.description-content {
    color: rgba(255, 255, 255, 0.95);
    line-height: 1.8;
    font-size: 1.1rem;
}

.description-content p {
    margin-bottom: 20px;
}

.description-content p:last-child {
    margin-bottom: 0;
}

/* 操作按钮区域 */
.case-actions-section {
    padding: 60px 0;
    position: relative;
    z-index: 10;
}

.actions-wrapper {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    padding: 15px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
    backdrop-filter: blur(10px);
}

.action-btn.primary-btn {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.3), rgba(255, 107, 157, 0.2));
    border: 1px solid rgba(120, 119, 198, 0.4);
    color: #ffffff;
    box-shadow: 0 10px 30px rgba(120, 119, 198, 0.3);
}

.action-btn.primary-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.5), rgba(255, 107, 157, 0.3));
    opacity: 0;
    transition: all 0.3s ease;
    z-index: -1;
}

.action-btn.primary-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(120, 119, 198, 0.4);
    border-color: rgba(120, 119, 198, 0.5);
}

.action-btn.primary-btn:hover::before {
    opacity: 1;
}

.action-btn.secondary-btn {
    background: rgba(255, 255, 255, 0.05);
    color: #ffffff;
    border: 1px solid rgba(120, 119, 198, 0.2);
}

.action-btn.secondary-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.2), rgba(255, 107, 157, 0.2));
    opacity: 0;
    transition: all 0.3s ease;
    z-index: -1;
}

.action-btn.secondary-btn:hover {
    transform: translateY(-3px);
    border-color: rgba(120, 119, 198, 0.4);
    box-shadow: 0 10px 30px rgba(120, 119, 198, 0.2);
}

.action-btn.secondary-btn:hover::before {
    opacity: 1;
}

/* 图片灯箱 */
.lightbox {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(15, 15, 35, 0.95), rgba(26, 26, 46, 0.95));
    backdrop-filter: blur(20px);
    animation: lightboxFadeIn 0.3s ease-out;
}

@keyframes lightboxFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(20px);
    }
}

.lightbox-content {
    position: relative;
    margin: auto;
    padding: 20px;
    width: 90%;
    max-width: 1200px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lightbox-close {
    position: absolute;
    top: 20px;
    right: 35px;
    color: #ffffff;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    z-index: 10000;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(26, 26, 46, 0.9);
    border-radius: 50%;
    transition: all 0.3s ease;
    border: 2px solid rgba(120, 119, 198, 0.4);
    backdrop-filter: blur(10px);
}

.lightbox-close:hover {
    background: rgba(255, 107, 157, 0.8);
    transform: scale(1.1);
    border-color: rgba(255, 107, 157, 0.8);
    box-shadow: 0 0 25px rgba(255, 107, 157, 0.5);
}

#lightbox-image {
    max-width: 100%;
    max-height: 90%;
    object-fit: contain;
    border-radius: 10px;
    box-shadow: 
        0 30px 100px rgba(0, 0, 0, 0.7),
        0 0 50px rgba(120, 119, 198, 0.3);
    border: 2px solid rgba(120, 119, 198, 0.4);
    animation: imageZoomIn 0.3s ease-out;
}

@keyframes imageZoomIn {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

#lightbox-image:hover {
    transform: scale(1.02);
    transition: all 0.3s ease;
    border-color: rgba(255, 107, 157, 0.6);
    box-shadow: 
        0 35px 120px rgba(0, 0, 0, 0.8),
        0 0 60px rgba(255, 107, 157, 0.4);
}

.btn-glow {
    display: none;
}

/* 动画关键帧 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes titleGlow {
    0%, 100% {
        text-shadow: 0 0 20px rgba(120, 119, 198, 0.5);
    }
    50% {
        text-shadow: 0 0 30px rgba(255, 107, 157, 0.7), 0 0 40px rgba(120, 119, 198, 0.5);
    }
}

@keyframes breadcrumbSlide {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes shapeFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-20px) rotate(90deg);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-10px) rotate(180deg);
        opacity: 1;
    }
    75% {
        transform: translateY(-30px) rotate(270deg);
        opacity: 0.8;
    }
}

@keyframes particleFloat {
    0% {
        transform: translateY(0px) translateX(0px);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) translateX(20px);
        opacity: 0;
    }
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .case-content-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .case-title {
        font-size: 2.8rem;
    }

    .main-image,
    .info-card,
    .description-card {
        margin: 0 20px;
    }
}

@media (max-width: 768px) {
    .case-breadcrumb {
        justify-content: center;
        text-align: center;
    }

    .breadcrumb-link,
    .breadcrumb-current {
        font-size: 0.9rem;
        padding: 6px 12px;
    }

    .geometric-shape {
        display: none;
    }

    .particle-7,
    .particle-8,
    .particle-9,
    .particle-10,
    .particle-11,
    .particle-12 {
        display: none;
    }

    .case-title {
        font-size: 2.2rem;
        line-height: 1.2;
    }

    .case-subtitle {
        font-size: 1rem;
    }

    .case-meta-info {
        gap: 20px;
    }

    .meta-item {
        padding: 12px 20px;
        font-size: 0.9rem;
    }

    .case-content-grid {
        gap: 30px;
        padding: 0 15px;
    }

    .main-image,
    .info-card,
    .description-card {
        margin: 0;
    }

    .card-title {
        font-size: 1.3rem;
    }

    .info-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .info-item {
        padding: 12px;
        gap: 12px;
    }

    .info-item:hover {
        transform: none;
    }

    .info-icon {
        width: 35px;
        height: 35px;
    }

    .description-content {
        font-size: 1rem;
    }

    .actions-wrapper {
        gap: 20px;
        padding: 0 20px;
    }

    .action-btn {
        padding: 12px 24px;
        font-size: 0.9rem;
    }

    .lightbox-close {
        top: 10px;
        right: 15px;
        font-size: 30px;
        width: 40px;
        height: 40px;
    }

    #lightbox-image {
        max-height: 80%;
    }

    .lightbox-content {
        padding: 10px;
    }
}

@media (max-width: 480px) {
    .case-title {
        font-size: 1.8rem;
    }

    .case-subtitle {
        font-size: 0.9rem;
    }

    .case-meta-info {
        flex-direction: column;
        align-items: center;
    }

    .meta-item {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .main-image,
    .info-card,
    .description-card {
        margin: 0 10px;
    }

    .card-title {
        font-size: 1.2rem;
    }

    .info-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .info-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .info-icon {
        width: 30px;
        height: 30px;
    }

    .description-content {
        font-size: 0.95rem;
    }

    .action-btn {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    .lightbox-close {
        font-size: 25px;
        width: 35px;
        height: 35px;
    }

    #lightbox-image {
        max-height: 70%;
    }

    .lightbox-content {
        width: 95%;
        padding: 5px;
    }
}
/* 后台管理系统样式 - 酷炫科技风 */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

/* 引入通用组件样式 */
/* @import url('admin/common.css'); */

* {
    box-sizing: border-box;
}

body {
    font-family: 'Rajdhani', sans-serif;
    background: #0a0a0a;
    color: #fff;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    position: relative;
}

/* 动态背景 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
    animation: backgroundShift 15s ease-in-out infinite alternate;
    z-index: -1;
}

@keyframes backgroundShift {
    0% { transform: scale(1) rotate(0deg); }
    100% { transform: scale(1.05) rotate(2deg); }
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 56px; /* 保持在导航栏下方 */
    bottom: 0;
    left: 0;
    z-index: 1025; /* 确保在导航栏下方但高于其他内容 */
    width: 240px;
    padding: 0;
    background: linear-gradient(180deg,
        rgba(15, 15, 15, 0.98) 0%,
        rgba(20, 20, 30, 0.98) 50%,
        rgba(15, 15, 20, 0.98) 100%);
    backdrop-filter: blur(25px);
    border-right: 2px solid rgba(120, 119, 198, 0.4);
    box-shadow: 6px 0 30px rgba(0, 0, 0, 0.4);
    overflow-y: auto;
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 56px);
    padding: 1rem 0;
    overflow-x: hidden;
    overflow-y: auto;
}

/* 侧边栏滚动条样式 */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(15, 15, 15, 0.3);
}

.sidebar::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg,
        rgba(120, 119, 198, 0.6),
        rgba(255, 119, 198, 0.6));
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg,
        rgba(120, 119, 198, 0.8),
        rgba(255, 119, 198, 0.8));
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.85);
    padding: 1rem 1rem; /* 进一步减小内边距 */
    border-radius: 6px; /* 进一步减小圆角 */
    font-weight: 500;
    font-size: 14px; /* 减小字体 */
    transition: all 0.3s ease;
    position: relative;
    margin: 3px 20px; /* 进一步增加左右外边距 */
    display: flex;
    align-items: center;
    text-decoration: none;
    border: 1px solid transparent;
}

/* 去掉列表样式 */
.sidebar .nav,
.sidebar .nav-item,
.sidebar ul,
.sidebar li {
    list-style: none !important;
    margin: 0;
    padding: 0;
}

.sidebar .nav-item {
    margin: 0;
    padding: 0;
}

.sidebar .nav-item::before {
    display: none !important;
}

.sidebar .nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(180deg,
        rgba(120, 119, 198, 0.8),
        rgba(255, 119, 198, 0.8),
        rgba(120, 219, 255, 0.8));
    transform: scaleY(0);
    transition: transform 0.3s ease;
    border-radius: 0 3px 3px 0;
}

.sidebar .nav-link:hover {
    color: #fff;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.2) 0%,
        rgba(255, 119, 198, 0.15) 100%);
    text-shadow: 0 0 10px rgba(120, 119, 198, 0.6);
    border-color: rgba(120, 119, 198, 0.4);
    box-shadow: 0 1px 6px rgba(120, 119, 198, 0.1);
}

.sidebar .nav-link:hover::before {
    transform: scaleY(1);
}

.sidebar .nav-link.active {
    color: #fff;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.4) 0%,
        rgba(255, 119, 198, 0.3) 50%,
        rgba(120, 219, 255, 0.4) 100%);
    text-shadow: 0 0 15px rgba(120, 119, 198, 1);
    box-shadow:
        0 2px 8px rgba(120, 119, 198, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border-color: rgba(120, 119, 198, 0.6);
}

.sidebar .nav-link.active::before {
    transform: scaleY(1);
}

.sidebar .nav-link i {
    margin-right: 0.8rem; /* 进一步减小图标右边距 */
    font-size: 16px; /* 进一步减小图标大小 */
    width: 18px; /* 进一步减小图标容器宽度 */
    text-align: center;
    transition: all 0.3s ease;
    filter: drop-shadow(0 0 6px rgba(120, 119, 198, 0.3));
    display: inline-block;
    flex-shrink: 0; /* 防止图标收缩 */
}

.sidebar .nav-link:hover i,
.sidebar .nav-link.active i {
    filter: drop-shadow(0 0 12px rgba(120, 119, 198, 0.8));
    transform: scale(1.1);
}

.sidebar .badge {
    background: linear-gradient(135deg,
        rgba(255, 119, 198, 0.8),
        rgba(120, 219, 255, 0.8));
    color: #fff;
    font-size: 11px;
    padding: 3px 6px;
    border-radius: 10px;
    margin-left: auto;
}

/* 主要内容区域 */
main, .main-content {
    margin-left: 280px;
    padding: 80px 20px 20px 20px; /* 增加顶部padding为固定导航栏留空间 */
    background: transparent;
    position: relative;
    z-index: 1;
    min-height: calc(100vh - 56px);
    width: calc(100% - 280px);
    box-sizing: border-box;
    /* 防止边框闪动 */
    will-change: auto;
    backface-visibility: hidden;
    transform: translateZ(0);
}

main h1, main h2, main h3, main h4, main h5, main h6,
.main-content h1, .main-content h2, .main-content h3, .main-content h4, .main-content h5, .main-content h6 {
    color: #fff;
    font-family: 'Orbitron', monospace;
    text-shadow: 0 0 10px rgba(120, 119, 198, 0.5);
}

/* 容器样式优化 */
.container-fluid {
    padding-left: 0;
    padding-right: 0;
}

/* 行和列的间距优化 */
.row {
    margin-left: 0;
    margin-right: 0;
}

.row.mb-4 {
    margin-bottom: 2rem !important;
}

.border-bottom {
    border-bottom: 1px solid rgba(120, 119, 198, 0.3) !important;
}

/* 卡片样式 */
.card {
    background: rgba(15, 15, 15, 0.8) !important;
    border: 1px solid rgba(120, 119, 198, 0.3) !important;
    border-radius: 15px !important;
    backdrop-filter: blur(20px);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(120, 119, 198, 0.1) !important;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    /* 防止边框闪动 */
    will-change: box-shadow;
    backface-visibility: hidden;
    transform: translateZ(0);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(120, 119, 198, 0.1),
        transparent);
    transition: left 0.8s ease;
}

.card:hover::before {
    left: 100%;
}

.card:hover {
    box-shadow:
        0 15px 40px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(120, 119, 198, 0.2);
}

.card-header {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.2) 0%,
        rgba(255, 119, 198, 0.1) 50%,
        rgba(120, 219, 255, 0.2) 100%) !important;
    border-bottom: 1px solid rgba(120, 119, 198, 0.3) !important;
    border-radius: 15px 15px 0 0 !important;
    color: #fff !important;
}

.card-body {
    background: transparent !important;
    color: #fff !important;
    position: relative;
    z-index: 2;
}

/* 统计卡片特殊样式 */
.card.shadow.h-100.py-2 {
    padding: 1.5rem !important;
}

.card.shadow.h-100.py-2 .card-body {
    padding: 0 !important;
}

/* 行间距优化 */
.row.no-gutters {
    margin: 0;
}

.col-auto {
    padding: 0;
}

.col.mr-2 {
    margin-right: 1rem !important;
}

/* 边框颜色 - 科技风格 */
.border-left-primary {
    border-left: 0.25rem solid rgba(120, 119, 198, 0.8) !important;
    box-shadow: -4px 0 15px rgba(120, 119, 198, 0.3);
}

.border-left-success {
    border-left: 0.25rem solid rgba(120, 219, 255, 0.8) !important;
    box-shadow: -4px 0 15px rgba(120, 219, 255, 0.3);
}

.border-left-info {
    border-left: 0.25rem solid rgba(255, 119, 198, 0.8) !important;
    box-shadow: -4px 0 15px rgba(255, 119, 198, 0.3);
}

.border-left-warning {
    border-left: 0.25rem solid rgba(255, 200, 87, 0.8) !important;
    box-shadow: -4px 0 15px rgba(255, 200, 87, 0.3);
}

/* 文本颜色 */
.text-gray-800 {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 600;
}

.text-gray-300 {
    color: rgba(255, 255, 255, 0.6) !important;
}

.text-primary {
    color: rgba(120, 119, 198, 1) !important;
    text-shadow: 0 0 10px rgba(120, 119, 198, 0.5);
    font-weight: 600;
}

.text-success {
    color: rgba(120, 219, 255, 1) !important;
    text-shadow: 0 0 10px rgba(120, 219, 255, 0.5);
    font-weight: 600;
}

.text-info {
    color: rgba(255, 119, 198, 1) !important;
    text-shadow: 0 0 10px rgba(255, 119, 198, 0.5);
    font-weight: 600;
}

.text-warning {
    color: rgba(255, 200, 87, 1) !important;
    text-shadow: 0 0 10px rgba(255, 200, 87, 0.5);
    font-weight: 600;
}

/* 字体大小类 */
.text-xs {
    font-size: 0.75rem !important;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.h5 {
    font-size: 1.5rem !important;
    font-weight: 700;
}

.font-weight-bold {
    font-weight: 700 !important;
}

/* 统一按钮样式系统 */
.btn {
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
    border: 1px solid;
    cursor: pointer;
    font-size: 14px;
    padding: 12px 20px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn i {
    transition: transform 0.3s ease;
    filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.3));
}

.btn:hover i {
    transform: scale(1.1);
}

/* 主要按钮 - 统一紫色渐变 */
.btn-primary, .btn-add, .btn-submit {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8) 0%,
        rgba(139, 124, 246, 0.8) 50%,
        rgba(120, 219, 255, 0.8) 100%) !important;
    border-color: rgba(120, 119, 198, 0.6) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.btn-primary:hover, .btn-add:hover, .btn-submit:hover {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 1) 0%,
        rgba(139, 124, 246, 1) 50%,
        rgba(120, 219, 255, 1) 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.4);
    color: #ffffff !important;
    text-decoration: none;
}

/* 编辑按钮 - 统一蓝色渐变 */
.btn-edit, .action-btn.btn-edit, .btn-action.btn-edit {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.8) 0%,
        rgba(37, 99, 235, 0.8) 50%,
        rgba(29, 78, 216, 0.8) 100%) !important;
    border-color: rgba(59, 130, 246, 0.6) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.btn-edit:hover, .action-btn.btn-edit:hover, .btn-action.btn-edit:hover {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 1) 0%,
        rgba(37, 99, 235, 1) 50%,
        rgba(29, 78, 216, 1) 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    color: #ffffff !important;
    text-decoration: none;
}

/* 删除按钮 - 统一红色渐变 */
.btn-delete, .action-btn.btn-delete, .btn-action.btn-delete {
    background: linear-gradient(135deg,
        rgba(239, 68, 68, 0.8) 0%,
        rgba(220, 38, 38, 0.8) 50%,
        rgba(185, 28, 28, 0.8) 100%) !important;
    border-color: rgba(239, 68, 68, 0.6) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.btn-delete:hover, .action-btn.btn-delete:hover, .btn-action.btn-delete:hover {
    background: linear-gradient(135deg,
        rgba(239, 68, 68, 1) 0%,
        rgba(220, 38, 38, 1) 50%,
        rgba(185, 28, 28, 1) 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
    color: #ffffff !important;
    text-decoration: none;
}

/* 查看按钮 - 统一绿色渐变 */
.btn-view, .btn-success {
    background: linear-gradient(135deg,
        rgba(34, 197, 94, 0.8) 0%,
        rgba(16, 185, 129, 0.8) 50%,
        rgba(5, 150, 105, 0.8) 100%) !important;
    border-color: rgba(34, 197, 94, 0.6) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.btn-view:hover, .btn-success:hover {
    background: linear-gradient(135deg,
        rgba(34, 197, 94, 1) 0%,
        rgba(16, 185, 129, 1) 50%,
        rgba(5, 150, 105, 1) 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
    color: #ffffff !important;
    text-decoration: none;
}



/* 次要按钮 */
.btn-secondary, .btn-cancel {
    background: rgba(75, 85, 99, 0.8) !important;
    border-color: rgba(107, 114, 128, 0.6) !important;
    color: rgba(255, 255, 255, 0.9) !important;
}

.btn-secondary:hover, .btn-cancel:hover {
    background: rgba(75, 85, 99, 1) !important;
    border-color: rgba(107, 114, 128, 0.8) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(75, 85, 99, 0.4);
    color: #ffffff !important;
    text-decoration: none;
}

/* 轮廓按钮 */
.btn-outline-secondary {
    background: transparent !important;
    border-color: rgba(120, 119, 198, 0.5) !important;
    color: rgba(120, 119, 198, 1) !important;
    backdrop-filter: blur(10px);
}

.btn-outline-secondary:hover {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.2) 0%,
        rgba(139, 124, 246, 0.2) 100%) !important;
    border-color: rgba(120, 119, 198, 0.8) !important;
    color: #ffffff !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.3);
    text-decoration: none;
}

/* 按钮尺寸 */
.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
    border-radius: 8px;
}

.btn-lg {
    padding: 16px 32px;
    font-size: 16px;
    border-radius: 12px;
}

/* 圆形按钮 */
.btn-circle {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-circle.btn-sm {
    width: 32px;
    height: 32px;
}

.btn-circle.btn-lg {
    width: 52px;
    height: 52px;
}

/* 选项卡样式 - 系统设置页面 */
.settings-tabs {
    border-bottom: 2px solid rgba(120, 119, 198, 0.3) !important;
    background: transparent !important;
    margin-bottom: 0 !important;
}

.settings-tabs .nav-item {
    margin-bottom: -2px;
}

.settings-tabs .nav-link {
    background: rgba(15, 15, 15, 0.4) !important;
    border: 1px solid rgba(120, 119, 198, 0.2) !important;
    border-bottom: none !important;
    border-radius: 8px 8px 0 0 !important;
    color: rgba(255, 255, 255, 0.7) !important;
    padding: 12px 20px !important;
    margin-right: 4px !important;
    transition: all 0.3s ease !important;
    position: relative;
    overflow: hidden;
    font-weight: 500;
    text-decoration: none;
}

.settings-tabs .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(120, 119, 198, 0.2),
        transparent);
    transition: left 0.6s ease;
}

.settings-tabs .nav-link:hover::before {
    left: 100%;
}

.settings-tabs .nav-link:hover {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.3) 0%,
        rgba(255, 119, 198, 0.2) 100%) !important;
    color: #fff !important;
    border-color: rgba(120, 119, 198, 0.5) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(120, 119, 198, 0.2);
}

.settings-tabs .nav-link.active {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.6) 0%,
        rgba(255, 119, 198, 0.4) 50%,
        rgba(120, 219, 255, 0.6) 100%) !important;
    color: #fff !important;
    border-color: rgba(120, 119, 198, 0.8) !important;
    box-shadow:
        0 6px 20px rgba(120, 119, 198, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    text-shadow: 0 0 10px rgba(120, 119, 198, 0.8);
}

.settings-tabs .nav-link i {
    margin-right: 8px;
    font-size: 16px;
    filter: drop-shadow(0 0 6px rgba(120, 119, 198, 0.4));
    transition: all 0.3s ease;
}

.settings-tabs .nav-link:hover i,
.settings-tabs .nav-link.active i {
    filter: drop-shadow(0 0 12px rgba(120, 119, 198, 0.8));
    transform: scale(1.1);
}

/* 选项卡内容区域 */
.tab-content {
    background: transparent !important;
    padding: 0 !important;
}

.tab-pane {
    background: transparent !important;
    padding: 20px !important;
    border-radius: 0 0 15px 15px !important;
    min-height: 400px;
}

.tab-pane.active {
    animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 表格样式 */
.table {
    background: rgba(15, 15, 15, 0.6) !important;
    color: #fff !important;
    border-radius: 10px;
    overflow: hidden;
}

.table th {
    border-top: none !important;
    border-bottom: 1px solid rgba(120, 119, 198, 0.3) !important;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9) !important;
    background: rgba(120, 119, 198, 0.1) !important;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.table td {
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: rgba(255, 255, 255, 0.8) !important;
    vertical-align: middle;
}

.table-bordered {
    border: 1px solid rgba(120, 119, 198, 0.3) !important;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.table-responsive {
    border-radius: 10px;
    overflow: hidden;
}

/* 导航栏样式 */
.navbar {
    background: linear-gradient(135deg,
        rgba(15, 15, 15, 0.98) 0%,
        rgba(25, 25, 35, 0.98) 50%,
        rgba(15, 15, 25, 0.98) 100%) !important;
    backdrop-filter: blur(25px);
    border-bottom: 2px solid rgba(120, 119, 198, 0.4);
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.4),
        0 0 40px rgba(120, 119, 198, 0.15);
    position: fixed !important;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1030;
    height: 56px;
    padding: 0 1rem;
}

.navbar .container-fluid {
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    width: 100%;
}

.navbar .navbar-nav {
    display: flex;
    align-items: center;
    margin: 0;
    margin-left: auto; /* 确保用户菜单靠右 */
    flex-shrink: 0; /* 防止收缩 */
}

.navbar .navbar-nav .nav-item {
    margin: 0;
}

/* 左侧区域 */
.navbar .navbar-left {
    display: flex;
    align-items: center;
    flex: 1;
}

/* 移动端切换按钮 */
#sidebarToggle {
    background: none !important;
    border: none !important;
    color: #fff !important;
    font-size: 1.1rem;
    padding: 0.5rem !important;
    margin-right: 0.5rem;
    transition: all 0.3s ease;
}

#sidebarToggle:hover {
    color: rgba(120, 119, 198, 1) !important;
    text-shadow: 0 0 10px rgba(120, 119, 198, 0.8);
    transform: scale(1.1);
}

.navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        transparent,
        rgba(120, 119, 198, 0.1),
        transparent);
    animation: navShine 3s linear infinite;
    pointer-events: none;
}

@keyframes navShine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.navbar-brand {
    font-family: 'Orbitron', monospace !important;
    font-weight: 700 !important;
    color: #fff !important;
    text-shadow: 0 0 20px rgba(120, 119, 198, 0.8);
    font-size: 1.2rem !important;
    display: flex;
    align-items: center;
    margin-left: 0;
    white-space: nowrap;
    text-decoration: none !important;
    padding: 0.5rem 0.8rem;
}

.navbar-brand:hover {
    color: #fff !important;
    text-decoration: none !important;
}

.navbar-brand i {
    margin-right: 0.5rem;
    font-size: 1rem;
    filter: drop-shadow(0 0 10px rgba(120, 119, 198, 0.6));
    flex-shrink: 0;
}

.navbar-brand span {
    font-size: 1.1rem;
    white-space: nowrap;
}

/* 页面装修器表单样式优化 */
.form-section {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    backdrop-filter: blur(10px);
}

.section-header {
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section-title {
    color: rgba(255, 255, 255, 0.95);
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-title i {
    color: #667eea;
    font-size: 16px;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-label.required::after {
    content: '*';
    color: #ef4444;
    margin-left: 4px;
}

.form-label i {
    color: #667eea;
    font-size: 12px;
    width: 14px;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    color: rgba(255, 255, 255, 0.95);
    font-size: 14px;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: #667eea;
    background: rgba(255, 255, 255, 0.12);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input::placeholder,
.form-textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-textarea {
    min-height: 80px;
    resize: vertical;
    font-family: inherit;
}

.form-help {
    margin-top: 6px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    line-height: 1.4;
}



.form-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    padding-top: 24px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 24px;
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .form-row .form-group {
        margin-bottom: 20px;
    }

    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .form-actions .btn-primary,
    .form-actions .btn-success,
    .form-actions .btn-secondary {
        margin-bottom: 8px;
    }
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    border-radius: 8px;
    padding: 0.6rem 1rem !important;
    margin: 0 0.2rem;
    display: flex;
    align-items: center;
    white-space: nowrap;
    text-decoration: none !important;
    max-width: 200px;
}

.navbar-nav .nav-link:hover {
    color: rgba(120, 119, 198, 1) !important;
    text-shadow: 0 0 10px rgba(120, 119, 198, 0.5);
    background: rgba(120, 119, 198, 0.1);
    text-decoration: none !important;
}

.navbar-nav .nav-link i {
    margin-right: 0.4rem;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.navbar-nav .nav-link span {
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.dropdown-menu {
    background: rgba(255, 255, 255, 0.98) !important; /* 白色背景 */
    backdrop-filter: blur(20px);
    border: 2px solid rgba(120, 119, 198, 0.4) !important;
    border-radius: 12px !important;
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.5) !important,
        0 0 20px rgba(120, 119, 198, 0.3) !important;
    min-width: 180px !important;
    padding: 0.5rem 0 !important;
    display: none !important; /* 默认隐藏 */
    position: absolute !important;
    top: 100% !important;
    right: 0 !important;
    z-index: 1050 !important;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown-menu.show {
    display: block !important;
    opacity: 1;
    transform: translateY(0);
}

.dropdown-item {
    color: rgba(25, 25, 35, 0.9) !important; /* 深色文字 */
    transition: all 0.3s ease;
    padding: 0.8rem 1.2rem;
    border-radius: 8px;
    margin: 4px 8px;
    display: flex;
    align-items: center;
}

.dropdown-item:hover {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.15) 0%,
        rgba(255, 119, 198, 0.1) 100%) !important;
    color: rgba(120, 119, 198, 1) !important;
    transform: translateX(4px);
    text-decoration: none !important;
}

.dropdown-item i {
    margin-right: 0.6rem;
    font-size: 0.9rem;
    width: 16px;
    text-align: center;
    flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 767.98px) {
    .sidebar {
        top: 56px; /* 保持在导航栏下方 */
        left: -280px;
        width: 280px;
        transition: left 0.3s ease;
        z-index: 1025;
        box-shadow: 6px 0 30px rgba(0, 0, 0, 0.6);
    }

    .sidebar.show {
        left: 0;
    }

    /* 移动端遮罩层 */
    .sidebar-overlay {
        position: fixed;
        top: 56px;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1024;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .sidebar.show + .sidebar-overlay,
    .sidebar-overlay.show {
        opacity: 1;
        visibility: visible;
    }

    main, .main-content {
        margin-left: 0;
        padding: 80px 15px 20px 15px; /* 增加顶部padding为固定导航栏留空间 */
        width: 100%;
    }
}

/* 状态标签 */
.badge {
    font-size: 0.75em;
    padding: 0.4em 0.8em;
    border-radius: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8),
        rgba(255, 119, 198, 0.8)) !important;
    color: #fff !important;
}

.badge-success {
    background: linear-gradient(135deg,
        rgba(120, 219, 255, 0.8),
        rgba(120, 255, 200, 0.8)) !important;
    color: #fff !important;
}

.badge-info {
    background: linear-gradient(135deg,
        rgba(255, 119, 198, 0.8),
        rgba(255, 200, 87, 0.8)) !important;
    color: #fff !important;
}

.badge-secondary {
    background: linear-gradient(135deg,
        rgba(100, 100, 120, 0.8),
        rgba(150, 150, 170, 0.8)) !important;
    color: #fff !important;
}

.badge-danger {
    background: linear-gradient(135deg,
        rgba(255, 100, 100, 0.8),
        rgba(255, 150, 150, 0.8)) !important;
    color: #fff !important;
}

/* 表单样式 */
.form-control {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #fff !important;
    border-radius: 8px;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(120, 119, 198, 0.3);
    border-radius: 50%;
    border-top-color: rgba(120, 119, 198, 1);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 额外的酷炫效果 */
.text-center.py-4 {
    background: rgba(15, 15, 15, 0.5);
    border-radius: 15px;
    border: 1px solid rgba(120, 119, 198, 0.2);
    margin: 2rem 0;
    padding: 3rem 2rem !important;
}

.text-muted {
    color: rgba(255, 255, 255, 0.6) !important;
}

/* 空状态样式优化 */
.text-center h5 {
    color: rgba(255, 255, 255, 0.8) !important;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 500;
    margin-top: 1rem;
}

/* 按钮组样式 */
.btn-toolbar {
    margin-bottom: 0 !important;
}

.btn-group {
    margin-right: 0.5rem !important;
}

/* 图标发光效果 */
.fa-2x, .fa-3x {
    filter: drop-shadow(0 0 10px rgba(120, 119, 198, 0.3));
    transition: all 0.3s ease;
}

.fa-2x:hover, .fa-3x:hover {
    filter: drop-shadow(0 0 15px rgba(120, 119, 198, 0.6));
    transform: scale(1.1);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(15, 15, 15, 0.5);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg,
        rgba(120, 119, 198, 0.8),
        rgba(255, 119, 198, 0.8));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg,
        rgba(120, 119, 198, 1),
        rgba(255, 119, 198, 1));
}

/* 波纹效果 */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* 卡片悬浮增强效果 */
.card-hover {
    transform: translateY(-8px) !important;
    box-shadow: 0 20px 40px rgba(120, 119, 198, 0.3) !important;
}

/* 按钮相对定位以支持波纹效果 */
.btn, .nav-link {
    position: relative;
    overflow: hidden;
}

/* 数字计数动画 */
.text-gray-800 {
    font-variant-numeric: tabular-nums;
    transition: all 0.3s ease;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .card:hover {
        transform: none;
    }

    .sidebar .nav-link:hover {
        transform: none;
    }

    .card-hover {
        transform: none !important;
    }

    /* 移动端侧边栏切换按钮 */
    #sidebarToggle {
        background: none !important;
        border: none !important;
        color: #fff !important;
        font-size: 1.2rem;
        padding: 0.5rem !important;
        transition: all 0.3s ease;
    }

    #sidebarToggle:hover {
        color: rgba(120, 119, 198, 1) !important;
        text-shadow: 0 0 10px rgba(120, 119, 198, 0.8);
    }
}

/* 仪表盘容器样式 */
.dashboard-container {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.15) 0%,
        rgba(255, 119, 198, 0.08) 100%);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2.5rem;
    backdrop-filter: blur(15px);
    box-shadow:
        0 12px 40px rgba(120, 119, 198, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.dashboard-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(120, 119, 198, 0.1),
        transparent);
    transition: left 2s ease;
}

.dashboard-container:hover::before {
    left: 100%;
}

/* 仪表盘头部样式 */
.dashboard-header {
    padding-bottom: 1.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid rgba(120, 119, 198, 0.2);
    position: relative;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    min-height: 60px;
}

.dashboard-header .h2 {
    margin: 0 !important;
    font-size: 2rem;
    font-weight: 600;
    color: rgba(120, 119, 198, 1);
    text-shadow: 0 0 20px rgba(120, 119, 198, 0.3);
    z-index: 2;
    text-align: center;
    flex: 1;
}

.dashboard-header .btn-toolbar {
    margin-left: auto;
    margin-bottom: 0 !important;
    z-index: 3;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .dashboard-header .h2 {
        flex: none;
        margin-bottom: 1rem !important;
    }

    .dashboard-header .btn-toolbar {
        margin-left: 0;
    }
}

/* 页面标题区域优化 - 保留原有样式作为备用 */
.d-flex.justify-content-between.flex-wrap.flex-md-nowrap.align-items-center.pt-3.pb-2.mb-3.border-bottom {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.15) 0%,
        rgba(255, 119, 198, 0.08) 100%) !important;
    border: 1px solid rgba(120, 119, 198, 0.3) !important;
    border-radius: 15px !important;
    padding: 2rem 2.5rem !important;
    margin-bottom: 2.5rem !important;
    backdrop-filter: blur(15px);
    box-shadow:
        0 8px 32px rgba(120, 119, 198, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    position: relative;
    overflow: hidden;
}

.d-flex.justify-content-between.flex-wrap.flex-md-nowrap.align-items-center.pt-3.pb-2.mb-3.border-bottom::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(120, 119, 198, 0.1),
        transparent);
    transition: left 2s ease;
}

.d-flex.justify-content-between.flex-wrap.flex-md-nowrap.align-items-center.pt-3.pb-2.mb-3.border-bottom:hover::before {
    left: 100%;
}

.h2 {
    color: #fff !important;
    font-family: 'Orbitron', monospace !important;
    font-weight: 700 !important;
    font-size: 2.2rem !important;
    text-shadow: 0 0 25px rgba(120, 119, 198, 0.8) !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    position: relative;
}

.h2::before {
    content: '';
    width: 5px;
    height: 2.5rem;
    background: linear-gradient(180deg,
        rgba(120, 119, 198, 1),
        rgba(255, 119, 198, 1));
    border-radius: 3px;
    margin-right: 1.2rem;
    box-shadow: 0 0 15px rgba(120, 119, 198, 0.7);
    animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
    from {
        box-shadow: 0 0 15px rgba(120, 119, 198, 0.7);
    }
    to {
        box-shadow: 0 0 25px rgba(120, 119, 198, 1);
    }
}

/* 最新联系表单区域优化 */
.card.shadow.mb-4 {
    background: linear-gradient(135deg,
        rgba(25, 25, 35, 0.95) 0%,
        rgba(35, 35, 45, 0.95) 100%) !important;
    border: 1px solid rgba(120, 119, 198, 0.3) !important;
    border-radius: 18px !important;
    backdrop-filter: blur(20px);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(120, 119, 198, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    overflow: hidden;
    position: relative;
}

.card.shadow.mb-4::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg,
        rgba(120, 119, 198, 1),
        rgba(255, 119, 198, 1),
        rgba(120, 219, 255, 1));
    animation: gradient-flow 3s ease-in-out infinite;
}

@keyframes gradient-flow {
    0%, 100% {
        background: linear-gradient(90deg,
            rgba(120, 119, 198, 1),
            rgba(255, 119, 198, 1),
            rgba(120, 219, 255, 1));
    }
    50% {
        background: linear-gradient(90deg,
            rgba(120, 219, 255, 1),
            rgba(120, 119, 198, 1),
            rgba(255, 119, 198, 1));
    }
}

.card-header.py-3 {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.2) 0%,
        rgba(255, 119, 198, 0.1) 100%) !important;
    border-bottom: 1px solid rgba(120, 119, 198, 0.3) !important;
    border-radius: 18px 18px 0 0 !important;
    padding: 1.5rem 2rem !important;
}

.card-header h6 {
    color: #fff !important;
    font-family: 'Orbitron', monospace !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
    text-shadow: 0 0 15px rgba(120, 119, 198, 0.6) !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
}

.card-header h6::before {
    content: '';
    margin-right: 0.8rem;
    font-size: 1.2rem;
    filter: drop-shadow(0 0 10px rgba(120, 119, 198, 0.5));
}

.card-body {
    background: transparent !important;
    padding: 2rem !important;
}

/* 空状态优化 */
.text-center.py-4 {
    background: linear-gradient(135deg,
        rgba(15, 15, 25, 0.8) 0%,
        rgba(25, 25, 35, 0.8) 100%) !important;
    border: 1px solid rgba(120, 119, 198, 0.2) !important;
    border-radius: 15px !important;
    margin: 2rem 0 !important;
    padding: 4rem 2rem !important;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.text-center.py-4::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle,
        rgba(120, 119, 198, 0.1) 0%,
        transparent 70%);
    transform: translate(-50%, -50%);
    animation: pulse-bg 3s ease-in-out infinite;
}

@keyframes pulse-bg {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.5;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.8;
    }
}

.fa-inbox.fa-3x {
    color: rgba(120, 119, 198, 0.8) !important;
    filter: drop-shadow(0 0 20px rgba(120, 119, 198, 0.5)) !important;
    position: relative;
    z-index: 1;
}

/* 查看网站按钮优化 */
.btn-outline-secondary {
    color: rgba(120, 119, 198, 1) !important;
    border-color: rgba(120, 119, 198, 0.5) !important;
    background: rgba(120, 119, 198, 0.1) !important;
    border-radius: 10px !important;
    padding: 0.6rem 1.2rem !important;
    font-weight: 500 !important;
    font-size: 0.9rem !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(120, 119, 198, 0.2) !important;
    position: relative;
    overflow: hidden;
}

.btn-outline-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(120, 119, 198, 0.2),
        transparent);
    transition: left 0.5s ease;
}

.btn-outline-secondary:hover {
    color: #fff !important;
    border-color: rgba(120, 119, 198, 0.8) !important;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8) 0%,
        rgba(255, 119, 198, 0.6) 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(120, 119, 198, 0.4) !important;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5) !important;
}

.btn-outline-secondary:hover::before {
    left: 100%;
}

.btn-outline-secondary i {
    margin-right: 0.5rem;
    filter: drop-shadow(0 0 5px rgba(120, 119, 198, 0.5));
}

/* 选项卡样式 */
.nav-tabs {
    border-bottom: none !important;
    margin-bottom: 0 !important;
    background: transparent !important;
    padding: 0 !important;
    display: flex !important;
    flex-wrap: nowrap !important;
    justify-content: flex-start !important;
    list-style: none !important;
}

.nav-tabs .nav-item {
    margin-bottom: 0 !important;
    display: flex !important;
}

.nav-tabs .nav-link {
    border: 1px solid rgba(120, 119, 198, 0.3) !important;
    border-bottom: 2px solid transparent !important;
    color: rgba(255, 255, 255, 0.7) !important;
    padding: 12px 20px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    border-radius: 8px 8px 0 0 !important;
    margin: 0 2px !important;
    background: rgba(15, 15, 15, 0.6) !important;
    position: relative !important;
    overflow: hidden !important;
    text-decoration: none !important;
    white-space: nowrap !important;
    flex: 0 0 auto !important;
}

.nav-tabs .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(120, 119, 198, 0.1),
        transparent);
    transition: left 0.5s ease;
}

.nav-tabs .nav-link:hover {
    border-color: rgba(120, 119, 198, 0.6) !important;
    color: #00d4ff !important;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.3) 0%,
        rgba(255, 119, 198, 0.2) 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(120, 119, 198, 0.3) !important;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5) !important;
}

.nav-tabs .nav-link:hover::before {
    left: 100%;
}

.nav-tabs .nav-link.active {
    color: #00d4ff !important;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.5) 0%,
        rgba(255, 119, 198, 0.4) 50%,
        rgba(120, 219, 255, 0.5) 100%) !important;
    border-color: rgba(120, 119, 198, 0.8) !important;
    border-bottom-color: transparent !important;
    font-weight: 600 !important;
    box-shadow:
        0 6px 20px rgba(120, 119, 198, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    text-shadow: 0 0 15px rgba(0, 212, 255, 0.8) !important;
    transform: translateY(-2px) !important;
}

.nav-tabs .nav-link.active::before {
    left: 100%;
}

/* 选项卡图标样式 */
.nav-tabs .nav-link i {
    margin-right: 0.5rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    filter: drop-shadow(0 0 5px rgba(120, 119, 198, 0.3));
}

.nav-tabs .nav-link:hover i,
.nav-tabs .nav-link.active i {
    filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.6));
    transform: scale(1.1);
}

/* 卡片头部选项卡特殊样式 */
.card-header-tabs {
    border-bottom: none !important;
    margin-bottom: 0 !important;
    background: transparent !important;
    padding: 0 !important;
}

.card-header {
    background: transparent !important;
    border-bottom: none !important;
    padding: 1rem 1rem 0 1rem !important;
}

.card-body {
    padding-top: 0 !important;
}

/* 选项卡内容区域 */
.tab-content {
    background: transparent !important;
    border-radius: 0 !important;
    /* padding: 25px !important; */
    border: none !important;
    backdrop-filter: none !important;
    box-shadow: none !important;
    margin-top: 0 !important;
}

.tab-pane {
    color: rgba(255, 255, 255, 0.9);
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tab-pane.active {
    display: block;
    opacity: 1;
}

.tab-pane.show.active {
    display: block;
    opacity: 1;
}

.tab-pane.fade {
    transition: opacity 0.15s linear;
}

.tab-pane.fade.show {
    opacity: 1;
}

/* 表单样式优化 */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin-bottom: 0.5rem;
    display: block;
    font-size: 0.95rem;
}

.form-control {
    background: rgba(15, 15, 15, 0.8) !important;
    border: 1px solid rgba(120, 119, 198, 0.3) !important;
    border-radius: 8px !important;
    color: rgba(255, 255, 255, 0.9) !important;
    padding: 0.75rem 1rem !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(10px);
}

/* 表单控件focus效果 - 蓝色发光 - 最高优先级 */
.card .form-control:focus,
.tab-pane .form-control:focus,
input.form-control:focus,
textarea.form-control:focus,
select.form-control:focus,
.form-control:focus {
    background: rgba(15, 15, 15, 0.9) !important;
    border: 2px solid rgba(66, 165, 245, 1) !important;
    box-shadow:
        0 0 0 0.3rem rgba(66, 165, 245, 0.4) !important,
        0 0 15px rgba(66, 165, 245, 0.6) !important,
        0 0 25px rgba(66, 165, 245, 0.3) !important,
        inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
    color: #fff !important;
    transform: translateY(-2px) !important;
    outline: none !important;
    transition: all 0.3s ease !important;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
}

textarea.form-control {
    min-height: 120px;
    resize: vertical;
}

/* 网站描述文本框特殊样式 */
#site_description {
    min-height: 150px !important;
    font-size: 0.95rem !important;
    line-height: 1.6 !important;
}

/* META描述文本框样式 */
#meta_description {
    min-height: 130px !important;
    font-size: 0.95rem !important;
    line-height: 1.6 !important;
}

/* 统计代码文本框样式 */
#analytics_code {
    min-height: 180px !important;
    font-family: 'Courier New', monospace !important;
    font-size: 0.9rem !important;
    line-height: 1.5 !important;
}

/* 小文本样式 */
.small, small {
    color: rgba(255, 255, 255, 0.6) !important;
    font-size: 0.85rem;
}



/* 表单验证样式 */
.form-control.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.form-control.is-valid {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}


/* 内容头部样式 */
.content-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(120, 119, 198, 0.3);
}

.content-header h1 {
    margin: 0;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    color: #fff;
    text-shadow: 0 0 20px rgba(120, 119, 198, 0.6);
}

.content-header h1 i {
    margin-right: 0.5rem;
    color: rgba(120, 119, 198, 0.8);
    filter: drop-shadow(0 0 10px rgba(120, 119, 198, 0.5));
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-tabs {
        flex-wrap: wrap !important;
        padding: 5px !important;
    }

    .nav-tabs .nav-link {
        padding: 8px 12px !important;
        font-size: 0.9rem !important;
        margin: 2px 1px !important;
        flex: 1 1 auto !important;
        text-align: center !important;
    }

    .nav-tabs .nav-link i {
        margin-right: 0.3rem !important;
        font-size: 0.9rem !important;
    }

    .tab-content {
        padding: 15px !important;
    }

    .form-group .row {
        margin: 0 !important;
    }

    .form-group .col-md-6 {
        padding: 0 5px !important;
        margin-bottom: 1rem !important;
    }
}

@media (max-width: 576px) {
    .nav-tabs .nav-link {
        padding: 6px 8px;
        font-size: 0.85rem;
    }

    .nav-tabs .nav-link span {
        display: none;
    }

    .nav-tabs .nav-link i {
        margin-right: 0;
        font-size: 1.1rem;
    }
}

/* 消息提示样式 - 科技风格 */


/* Button元素选项卡特殊样式 */
.nav-tabs button.nav-link {
    border: 1px solid rgba(120, 119, 198, 0.3) !important;
    border-bottom: 2px solid transparent !important;
    color: rgba(255, 255, 255, 0.7) !important;
    padding: 12px 20px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    border-radius: 8px 8px 0 0 !important;
    margin: 0 2px !important;
    background: rgba(15, 15, 15, 0.6) !important;
    position: relative !important;
    overflow: hidden !important;
    text-decoration: none !important;
    white-space: nowrap !important;
    flex: 0 0 auto !important;
    cursor: pointer !important;
    font-family: inherit !important;
    font-size: inherit !important;
    line-height: inherit !important;
}

.nav-tabs button.nav-link:hover {
    border-color: rgba(120, 119, 198, 0.6) !important;
    color: #00d4ff !important;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.3) 0%,
        rgba(255, 119, 198, 0.2) 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(120, 119, 198, 0.3) !important;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5) !important;
}

.nav-tabs button.nav-link.active {
    color: #00d4ff !important;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.5) 0%,
        rgba(255, 119, 198, 0.4) 50%,
        rgba(120, 219, 255, 0.5) 100%) !important;
    border-color: rgba(120, 119, 198, 0.8) !important;
    border-bottom-color: transparent !important;
    font-weight: 600 !important;
    box-shadow:
        0 6px 20px rgba(120, 119, 198, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    text-shadow: 0 0 15px rgba(0, 212, 255, 0.8) !important;
    transform: translateY(-2px) !important;
}

/* 通用动画效果 */
@keyframes shimmer {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

@keyframes pulse-glow {
    0%, 100% {
        opacity: 0.5;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 通用工具类 */
.animate-shimmer {
    animation: shimmer 3s ease-in-out infinite;
}

.animate-pulse {
    animation: pulse-glow 2s ease-in-out infinite;
}

.animate-slide-up {
    animation: slideInUp 0.4s ease-out;
}

.animate-slide-down {
    animation: slideInDown 0.4s ease-out;
}

.animate-fade-scale {
    animation: fadeInScale 0.3s ease-out;
}

/* 通用容器样式 */
.admin-container {
    background: rgba(15, 15, 15, 0.95);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 20px;
    backdrop-filter: blur(25px);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.4),
        0 0 40px rgba(120, 119, 198, 0.1);
    overflow: hidden;
    position: relative;
    margin-bottom: 30px;
}

.admin-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
        rgba(120, 119, 198, 0.8),
        rgba(255, 119, 198, 0.8),
        rgba(120, 219, 255, 0.8));
    animation: shimmer 3s ease-in-out infinite;
}

/* 通用头部样式 */
.admin-header {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.15) 0%,
        rgba(255, 119, 198, 0.1) 50%,
        rgba(120, 219, 255, 0.15) 100%);
    padding: 30px;
    border-bottom: 1px solid rgba(120, 119, 198, 0.2);
}

.admin-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.admin-title-section {
    display: flex;
    align-items: center;
    gap: 20px;
}

.admin-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.3),
        rgba(255, 119, 198, 0.3));
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #fff;
    text-shadow: 0 0 20px rgba(120, 119, 198, 0.8);
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.3);
}

.admin-title {
    font-family: 'Orbitron', monospace;
    font-size: 28px;
    font-weight: 700;
    color: #fff;
    margin: 0;
    text-shadow: 0 0 20px rgba(120, 119, 198, 0.6);
}

.admin-subtitle {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.7);
    margin: 5px 0 0 0;
    font-weight: 400;
    line-height: 1.5;
}

/* 通用主体样式 */
.admin-body {
    padding: 30px;
}

/* 退出登录确认对话框样式 */
.logout-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.logout-modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.logout-modal {
    background: rgba(20, 20, 30, 0.95);
    border: 2px solid rgba(120, 119, 198, 0.4);
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(20px);
    max-width: 400px;
    width: 90%;
    transform: scale(0.8) translateY(-20px);
    transition: all 0.3s ease;
}

.logout-modal-overlay.show .logout-modal {
    transform: scale(1) translateY(0);
}

.logout-modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(120, 119, 198, 0.3);
    display: flex;
    align-items: center;
    gap: 12px;
}

.logout-modal-header i {
    font-size: 24px;
    color: #ff6b6b;
}

.logout-modal-header h4 {
    margin: 0;
    color: #fff;
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    text-shadow: 0 0 15px rgba(120, 119, 198, 0.5);
}

.logout-modal-body {
    padding: 1.5rem;
    text-align: center;
}

.logout-modal-body p {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
}

.logout-modal-footer {
    padding: 1rem 1.5rem 1.5rem;
    display: flex;
    justify-content: center;
    gap: 15px;
}

.logout-modal-footer button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.logout-modal-footer .btn-cancel {
    background: #6c757d;
    color: white;
}

.logout-modal-footer .btn-cancel:hover {
    background: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
}

.logout-modal-footer .btn-confirm {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
}

.logout-modal-footer .btn-confirm:hover {
    background: linear-gradient(135deg, #ff5252, #e53935);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
}

/* 响应式设计 - 对话框 */
@media (max-width: 768px) {
    .logout-modal {
        width: 95%;
        margin: 20px;
    }

    .logout-modal-footer {
        flex-direction: column;
    }

    .logout-modal-footer button {
        width: 100%;
        justify-content: center;
    }
}

/* 页面装修器专用样式 */
.type-filter-tabs {
    padding: 15px 20px;
}

.tabs-container1 {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    background: rgba(15, 15, 15, 0.6);
    padding: 20px;
    border-radius: 15px;
    border: 1px solid rgba(120, 119, 198, 0.3);
    backdrop-filter: blur(20px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.tab-item {
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: rgba(30, 30, 40, 0.8);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 10px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    cursor: pointer;
    overflow: hidden;
}

.tab-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(120, 119, 198, 0.2),
        transparent);
    transition: left 0.5s ease;
}

.tab-item:hover::before {
    left: 100%;
}

.tab-item:hover {
    color: #ffffff;
    background: rgba(120, 119, 198, 0.2);
    border-color: rgba(120, 119, 198, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(120, 119, 198, 0.2);
    text-decoration: none;
}

.tab-item.active {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.4) 0%,
        rgba(139, 124, 246, 0.4) 50%,
        rgba(120, 219, 255, 0.4) 100%);
    border-color: rgba(120, 119, 198, 0.6);
    color: #ffffff;
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.3);
    text-shadow: 0 0 10px rgba(120, 119, 198, 0.6);
}

.tab-item.active::before {
    left: 100%;
}

.tab-item i {
    font-size: 16px;
    filter: drop-shadow(0 0 6px rgba(120, 119, 198, 0.3));
    transition: all 0.3s ease;
}

.tab-item:hover i,
.tab-item.active i {
    filter: drop-shadow(0 0 12px rgba(120, 119, 198, 0.8));
    transform: scale(1.1);
}

.tab-item .count {
    background: rgba(120, 119, 198, 0.3);
    color: rgba(255, 255, 255, 0.9);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    margin-left: 4px;
    transition: all 0.3s ease;
}

.tab-item:hover .count,
.tab-item.active .count {
    background: rgba(120, 119, 198, 0.6);
    color: #ffffff;
    box-shadow: 0 0 10px rgba(120, 119, 198, 0.4);
}

.tab-item-wrapper {
    position: relative;
    display: inline-block;
}

.add-type-btn {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg,
        rgba(34, 197, 94, 0.8) 0%,
        rgba(22, 163, 74, 0.8) 100%);
    border: 2px solid rgba(34, 197, 94, 0.6);
    border-radius: 50%;
    color: #ffffff;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    opacity: 0;
    transform: scale(0.8);
    z-index: 10;
}

.tab-item-wrapper:hover .add-type-btn {
    opacity: 1;
    transform: scale(1);
}

.add-type-btn:hover {
    background: linear-gradient(135deg,
        rgba(34, 197, 94, 1) 0%,
        rgba(22, 163, 74, 1) 100%);
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(34, 197, 94, 0.4);
    color: #ffffff;
    text-decoration: none;
}

/* 头部操作按钮组 */
.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* 推荐标识 */
.featured-badge {
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

/* 模板列表样式 */
.template-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.template-badges {
    display: flex;
    align-items: center;
    gap: 8px;
}

.badge-type {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
}

/* 页面装修器表单样式优化 */
.form-section {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    backdrop-filter: blur(10px);
}

.section-header {
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section-title {
    color: rgba(255, 255, 255, 0.95);
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-title i {
    color: #667eea;
    font-size: 16px;
}



.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-label.required::after {
    content: '*';
    color: #ef4444;
    margin-left: 4px;
}

.form-label i {
    color: #667eea;
    font-size: 12px;
    width: 14px;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    color: rgba(255, 255, 255, 0.95);
    font-size: 14px;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: #667eea;
    background: rgba(255, 255, 255, 0.12);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input::placeholder,
.form-textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}



.form-help {
    margin-top: 6px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    line-height: 1.4;
}

.form-check {
    display: flex;
    align-items: center;
    gap: 12px;
}

.form-check-input {
    width: 18px;
    height: 18px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-check-input:checked {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}

.form-check-label {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
}

.form-check-label i {
    color: #667eea;
    font-size: 12px;
}

/* 删除确认模态框样式 - 三只鱼网络科技 | 韩总 | 2024-12-19 */
.delete-modal {
    position: absolute !important;
    width: 400px !important;
    max-width: 90vw !important;
    background: transparent !important;
    z-index: 99999 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transform: scale(0.9) !important;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
    pointer-events: none !important;
}

.delete-modal.show {
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
    transform: scale(1) !important;
}

.delete-modal .modal-content {
    background: linear-gradient(135deg,
        rgba(25, 25, 35, 0.98) 0%,
        rgba(35, 35, 45, 0.98) 100%) !important;
    border: 1px solid rgba(120, 119, 198, 0.5) !important;
    border-radius: 10px !important;
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.6),
                0 0 20px rgba(120, 119, 198, 0.3) !important;
    width: 100% !important;
    z-index: 100000 !important;
}

.delete-modal .modal-body {
    padding: 20px 25px !important;
    text-align: center !important;
    border-bottom: 1px solid rgba(120, 119, 198, 0.2) !important;
}

.delete-modal .modal-body p {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 14px !important;
    margin-bottom: 15px !important;
    line-height: 1.5 !important;
}

.delete-modal .delete-item-title {
    background: rgba(231, 76, 60, 0.1) !important;
    border: 1px solid rgba(231, 76, 60, 0.3) !important;
    border-radius: 30px !important;
    padding: 12px 15px !important;
    margin-top: 10px !important;
    color: #ff6b6b !important;
    font-weight: bold !important;
    font-size: 15px !important;
    word-break: break-all !important;
    line-height: 1.4 !important;
}

.delete-modal .modal-header {
    padding: 20px 24px 16px 24px !important;
    border-bottom: 1px solid rgba(120, 119, 198, 0.2) !important;
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
}

.delete-modal .modal-header i {
    font-size: 24px !important;
    color: #ff6b6b !important;
    filter: drop-shadow(0 0 8px rgba(255, 107, 107, 0.5)) !important;
}

.delete-modal .modal-header h4 {
    margin: 0 !important;
    color: #fff !important;
    font-family: 'Orbitron', monospace !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
    text-shadow: 0 0 15px rgba(120, 119, 198, 0.5) !important;
}

.delete-modal .modal-footer {
    padding: 16px 24px 20px 24px !important;
    display: flex !important;
    gap: 12px !important;
    justify-content: flex-end !important;
}

.delete-modal .btn-cancel {
    background: rgba(108, 117, 125, 0.2) !important;
    border: 1px solid rgba(108, 117, 125, 0.5) !important;
    color: #fff !important;
    padding: 8px 16px !important;
    border-radius: 6px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
}

.delete-modal .btn-cancel:hover {
    background: rgba(108, 117, 125, 0.3) !important;
    border-color: rgba(108, 117, 125, 0.7) !important;
    transform: translateY(-1px) !important;
}

.delete-modal .btn-confirm-delete {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    border: 1px solid #dc3545 !important;
    color: #fff !important;
    padding: 8px 16px !important;
    border-radius: 6px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3) !important;
}

.delete-modal .btn-confirm-delete:hover {
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4) !important;
}

.delete-modal .btn-confirm-delete:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    transform: none !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .delete-modal {
        width: 90vw !important;
        max-width: 350px !important;
    }

    .delete-modal .modal-header,
    .delete-modal .modal-footer {
        padding-left: 16px !important;
        padding-right: 16px !important;
    }
}

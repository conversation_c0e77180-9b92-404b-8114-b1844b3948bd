# 🛡️ ThinkPHP6企业级安全防护系统使用指南

**三只鱼网络科技 | 韩总 | 2024-12-19**

## 📋 系统概述

本安全防护系统提供了完整的统一安全解决方案，包括：

- **文件上传安全验证** - 深度文件类型检测、恶意文件扫描
- **SQL注入防护** - 智能SQL注入检测和拦截
- **XSS防护** - 跨站脚本攻击防护
- **CSRF保护** - 跨站请求伪造防护
- **统一安全验证** - 标准化的安全验证规则

## 🏗️ 系统架构

```
安全防护系统
├── 中间件层
│   ├── SqlInjectionMiddleware    # SQL注入防护
│   └── SecurityMiddleware        # 通用安全防护
├── 服务层
│   └── FileSecurityService      # 文件安全服务
├── 验证层
│   └── SecurityValidate         # 安全验证器
└── 配置层
    └── config/security.php      # 安全配置
```

## 🚀 快速开始

### 1. 文件上传安全验证

```php
use app\service\FileSecurityService;

// 控制器中使用
public function upload()
{
    $file = request()->file('image');
    
    // 方式一：使用安全上传服务（推荐）
    $result = FileSecurityService::secureUpload($file, [
        'check_magic' => true,      // 启用魔数检查
        'check_content' => true,    // 启用内容检查
        'strict_mode' => true,      // 严格模式
    ]);
    
    if ($result['success']) {
        // 上传成功
        $fileInfo = $result['data'];
        return json(['code' => 200, 'data' => $fileInfo]);
    } else {
        // 上传失败
        return json(['code' => 400, 'msg' => $result['message']]);
    }
}

// 方式二：仅验证文件安全性
public function validateFile()
{
    $file = request()->file('document');
    
    $validation = FileSecurityService::validateFile($file, [
        'check_extension' => true,
        'check_mime' => true,
        'check_size' => true,
        'check_magic' => true,
        'check_content' => true,
    ]);
    
    if (!$validation['valid']) {
        return json(['code' => 400, 'msg' => implode(', ', $validation['errors'])]);
    }
    
    // 验证通过，继续处理...
}
```

### 2. 使用安全验证器

```php
use app\validate\SecurityValidate;

// 控制器中使用
public function saveContent()
{
    $data = request()->param();
    
    // 验证单个字段
    $validate = new SecurityValidate();
    if (!$validate->scene('content')->check($data)) {
        return json(['code' => 400, 'msg' => $validate->getError()]);
    }
    
    // 批量验证数据安全性
    $securityCheck = SecurityValidate::validateDataSecurity($data, [
        'title' => 'checkXss',
        'content' => 'checkSqlInjection|checkXss',
        'email' => 'checkEmailSafe',
    ]);
    
    if (!$securityCheck['valid']) {
        return json(['code' => 400, 'msg' => '数据安全检查失败', 'errors' => $securityCheck['errors']]);
    }
    
    // 数据安全，继续处理...
}
```

### 3. 密码安全处理

```php
use app\validate\SecurityValidate;

// 注册用户
public function register()
{
    $password = request()->param('password');
    
    // 验证密码强度
    $validate = new SecurityValidate();
    if (!$validate->checkPasswordStrength($password, '', [])) {
        return json(['code' => 400, 'msg' => '密码强度不够']);
    }
    
    // 安全哈希密码
    $hashedPassword = SecurityValidate::hashPassword($password);
    
    // 保存用户...
}

// 用户登录
public function login()
{
    $password = request()->param('password');
    $user = User::where('username', request()->param('username'))->find();
    
    if ($user && SecurityValidate::verifyPassword($password, $user->password)) {
        // 登录成功
        return json(['code' => 200, 'msg' => '登录成功']);
    } else {
        return json(['code' => 400, 'msg' => '用户名或密码错误']);
    }
}
```

## ⚙️ 配置说明

### 文件上传安全配置

```php
// config/security.php
'upload' => [
    'enable_security_check' => true,        // 启用安全检查
    'allowed_extensions' => ['jpg', 'png'], // 允许的扩展名
    'max_size' => 5 * 1024 * 1024,         // 最大文件大小
    'check_magic_number' => true,           // 检查文件魔数
    'check_file_content' => true,           // 检查文件内容
    'strict_mode' => true,                  // 严格模式
],
```

### SQL注入防护配置

```php
// config/security.php
'sql_injection' => [
    'enable' => true,           // 启用SQL注入防护
    'strict_mode' => false,     // 严格模式
    'check_headers' => true,    // 检查请求头
    'check_cookies' => true,    // 检查Cookie
    'action' => 'block',        // 处理方式：block/redirect/clean
],
```

## 🔧 高级用法

### 1. 自定义文件安全检查

```php
// 扩展文件安全服务
class CustomFileSecurityService extends FileSecurityService
{
    // 添加自定义验证规则
    public static function validateCustomFile($file)
    {
        $result = parent::validateFile($file);
        
        // 添加自定义检查逻辑
        if ($result['valid']) {
            // 自定义业务逻辑检查
            $customCheck = self::customBusinessCheck($file);
            if (!$customCheck['valid']) {
                $result['valid'] = false;
                $result['errors'] = array_merge($result['errors'], $customCheck['errors']);
            }
        }
        
        return $result;
    }
    
    private static function customBusinessCheck($file)
    {
        // 实现自定义检查逻辑
        return ['valid' => true, 'errors' => []];
    }
}
```

### 2. 获取安全统计信息

```php
use app\middleware\SqlInjectionMiddleware;
use app\service\FileSecurityService;

// 获取SQL注入统计
$sqlStats = SqlInjectionMiddleware::getInjectionStats(7); // 最近7天

// 获取文件安全报告
$fileReport = FileSecurityService::getSecurityReport('/path/to/file');
```

### 3. 自定义安全验证规则

```php
// 扩展安全验证器
class CustomSecurityValidate extends SecurityValidate
{
    protected $rule = [
        'custom_field' => 'require|checkCustomSecurity',
    ];
    
    protected function checkCustomSecurity($value, $rule, $data = [])
    {
        // 实现自定义安全检查逻辑
        if (/* 自定义条件 */) {
            return false;
        }
        return true;
    }
}
```

## 🚨 安全事件处理

### 1. 安全日志查看

```bash
# SQL注入日志
tail -f runtime/log/sql_injection_2024-12-19.log

# 安全事件日志
tail -f runtime/log/security_2024-12-19.log
```

### 2. 安全事件响应

```php
// 处理安全事件
public function handleSecurityEvent($event, $data)
{
    switch ($event) {
        case 'sql_injection_detected':
            // 记录IP到黑名单
            $this->addToBlacklist($data['ip']);
            break;
            
        case 'malicious_file_uploaded':
            // 清理恶意文件
            FileSecurityService::cleanMaliciousFile($data['file_path']);
            break;
    }
}
```

## 📊 性能优化

### 1. 缓存配置

```php
// 启用安全检查缓存
'security_cache' => [
    'enable' => true,
    'expire' => 3600, // 1小时
],
```

### 2. 批量处理

```php
// 批量文件验证
$files = request()->file('files');
$batchResult = FileSecurityService::validateMultipleFiles($files);
```

## ⚠️ 注意事项

1. **性能影响**：启用严格模式会增加检查时间，建议根据业务需求调整
2. **误报处理**：某些正常内容可能被误判，可通过白名单配置排除
3. **日志管理**：定期清理安全日志，避免占用过多磁盘空间
4. **配置更新**：修改安全配置后需要清理缓存

## 🔄 升级和维护

### 1. 定期更新安全规则

```php
// 更新危险关键词列表
// 更新文件魔数映射
// 更新恶意模式匹配
```

### 2. 监控和报警

```php
// 设置安全事件监控
// 配置异常报警机制
// 定期安全审计
```

## 📞 技术支持

如有问题请联系：
- **开发者**：韩总
- **公司**：三只鱼网络科技
- **技术栈**：ThinkPHP6企业级应用

---

**安全无小事，防护要到位！** 🛡️

<?php
declare (strict_types = 1);

namespace app\admin\controller;

use think\facade\Db;
use app\validate\SecurityValidate;

/**
 * 后台首页控制器
 */
class Index extends Base
{
    /**
     * 后台首页
     */
    public function index()
    {
        // 获取统计数据
        $stats = $this->getStats();

        // 获取最新联系表单
        $latestContacts = $this->getLatestContacts();

        return view('admin/index', [
            'stats' => $stats,
            'admin_info' => $this->getAdminUser(),
            'latestContacts' => $latestContacts
        ]);
    }
    
    /**
     * 获取统计数据
     */
    private function getStats()
    {
        try {
            $stats = [
                'contact_forms' => Db::name('contact_forms')->count(),
                'new_contact_forms' => Db::name('contact_forms')->where('status', 'new')->count(),
                'products' => Db::name('products')->count(),
                'news' => Db::name('news')->count(),
                'cases' => Db::name('cases')->count()
            ];

            return $stats;
        } catch (\Exception $e) {
            // 如果数据库连接失败，返回模拟数据
            return [
                'contact_forms' => 15,
                'new_contact_forms' => 3,
                'products' => 12,
                'news' => 25,
                'cases' => 8
            ];
        }
    }

    /**
     * 获取最新联系表单
     */
    private function getLatestContacts()
    {
        try {
            $contacts = Db::name('contact_forms')
                ->order('created_at desc')
                ->limit(5)
                ->select();

            return $contacts->toArray();
        } catch (\Exception $e) {
            // 如果数据库连接失败，返回模拟数据
            return [
                [
                    'id' => 1,
                    'name' => '张先生',
                    'email' => '<EMAIL>',
                    'subject' => '产品咨询',
                    'status' => 'new',
                    'created_at' => date('Y-m-d H:i:s', time() - 300)
                ],
                [
                    'id' => 2,
                    'name' => '李女士',
                    'email' => '<EMAIL>',
                    'subject' => '技术支持',
                    'status' => 'read',
                    'created_at' => date('Y-m-d H:i:s', time() - 3600)
                ],
                [
                    'id' => 3,
                    'name' => '王总',
                    'email' => '<EMAIL>',
                    'subject' => '合作洽谈',
                    'status' => 'replied',
                    'created_at' => date('Y-m-d H:i:s', time() - 7200)
                ]
            ];
        }
    }
}


/* 新闻管理专用样式 - 深色科技风 */
.news-container {
    background: rgba(15, 15, 15, 0.95);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 20px;
    backdrop-filter: blur(25px);
    box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.4),
    0 0 40px rgba(120, 119, 198, 0.1);
    overflow: hidden;
    position: relative;
    margin-bottom: 30px;
    }
    
    .news-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
    rgba(120, 119, 198, 0.8),
    rgba(255, 119, 198, 0.8),
    rgba(120, 219, 255, 0.8));
    animation: shimmer 3s ease-in-out infinite;
    }

@keyframes shimmer {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* 消息提示 */


/* 列表头部 */
.list-header, .form-header {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.15) 0%,
        rgba(255, 119, 198, 0.1) 50%,
        rgba(120, 219, 255, 0.15) 100%);
    padding: 30px;
    border-bottom: 1px solid rgba(120, 119, 198, 0.2);
}

.list-header-content, .form-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.list-title-section, .form-title-section {
    display: flex;
    align-items: center;
    gap: 20px;
}

.list-icon, .form-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.3),
        rgba(255, 119, 198, 0.3));
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #fff;
    text-shadow: 0 0 20px rgba(120, 119, 198, 0.8);
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.3);
}

.list-title, .form-title {
    font-family: 'Orbitron', monospace;
    font-size: 28px;
    font-weight: 700;
    color: #fff;
    margin: 0;
    text-shadow: 0 0 20px rgba(120, 119, 198, 0.6);
}

.list-subtitle, .form-subtitle {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.7);
    margin: 5px 0 0 0;
    font-weight: 400;
    line-height: 1.5;
}

.btn-add-custom, .btn-back {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8) 0%,
        rgba(255, 119, 198, 0.8) 100%);
    border: 1px solid rgba(120, 119, 198, 0.5);
    color: #fff;
    padding: 12px 24px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.3);
}

.btn-add-custom:hover, .btn-back:hover {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 1) 0%,
        rgba(255, 119, 198, 1) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.4);
    color: #fff;
}

/* 选项卡样式 */
.tabs-container {

    border-bottom: 1px solid rgba(120, 119, 198, 0.2);
    padding: 0 30px;
    margin-top: 25px;
}

.tabs-nav {
    display: flex;
    gap: 2px;
}

.tab-btn {
    padding: 15px 25px;
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    border-radius: 8px 8px 0 0;
    position: relative;
}

.tab-btn:hover {
    background: rgba(120, 119, 198, 0.1);
    color: rgba(255, 255, 255, 0.9);
}

.tab-btn.active {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.3), rgba(255, 119, 198, 0.2));
    color: #ffffff;
    border-bottom: 2px solid rgba(120, 119, 198, 0.8);
}

.tab-btn i {
    font-size: 16px;
}

/* 头部操作按钮 */
.header-actions {
    display: flex;
    gap: 12px;
}

/* 列表主体 */
.list-body, .form-body {
    padding: 30px;
}

/* 新闻列表 */
.news-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 25px;
    padding: 5px;
}

.news-item {
    background: linear-gradient(135deg, 
        rgba(20, 20, 30, 0.95) 0%, 
        rgba(25, 25, 35, 0.9) 50%,
        rgba(30, 30, 40, 0.95) 100%);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 20px;
    padding: 30px;
    /* 分离边框和阴影的过渡时间 */
    transition-property: transform, box-shadow, border-color, background;
    transition-duration: 0.3s, 0.3s, 0.2s, 0.3s;
    transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(15px);
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 2px 8px rgba(120, 119, 198, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    /* 防止边框闪动 - 增强版 */
    will-change: transform, box-shadow, border-color;
    backface-visibility: hidden;
    transform: translateY(0) translateZ(0);
    transform-origin: center center;
}

.news-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(120, 119, 198, 0.1),
        transparent);
    transition: left 0.8s ease;
}

.news-item:hover::before {
    left: 100%;
}

.news-item:hover {
    border-color: rgba(120, 119, 198, 0.45);
    box-shadow: 
        0 12px 40px rgba(0, 0, 0, 0.32),
        0 5px 22px rgba(120, 119, 198, 0.18),
        0 0 25px rgba(120, 119, 198, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.06);
    transform: translateY(-1px) translateZ(0);
}

.news-content-wrapper {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

/* 新闻缩略图 - 优化版 */
.news-thumbnail {
    width: 140px;
    height: 90px;
    border-radius: 16px;
    overflow: hidden;
    flex-shrink: 0;
    background: linear-gradient(135deg,
        rgba(30, 30, 40, 0.9) 0%,
        rgba(40, 40, 55, 0.9) 100%);
    border: 2px solid rgba(120, 119, 198, 0.3);
    position: relative;
    box-shadow: 
        0 8px 25px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.news-thumbnail::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.1) 0%,
        rgba(255, 119, 198, 0.1) 50%,
        rgba(120, 219, 255, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.news-item:hover .news-thumbnail {
    border-color: rgba(120, 119, 198, 0.4);
    box-shadow: 
        0 10px 28px rgba(120, 119, 198, 0.15),
        0 0 15px rgba(120, 119, 198, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.news-item:hover .news-thumbnail::before {
    opacity: 1;
}

.news-thumb-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    z-index: 2;
}

.news-item:hover .news-thumb-img {
    transform: scale(1.03);
    filter: brightness(1.03) contrast(1.01);
}

.news-thumb-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(120, 119, 198, 0.7);
    font-size: 28px;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.15) 0%,
        rgba(255, 119, 198, 0.15) 50%,
        rgba(120, 219, 255, 0.15) 100%);
    position: relative;
    z-index: 2;
}

.news-thumb-placeholder::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle,
        rgba(120, 119, 198, 0.2) 0%,
        transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow {
    0%, 100% {
        opacity: 0.5;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.1);
    }
}

/* 新闻信息 */
.news-info {
    flex: 1;
    min-width: 0;
}

.news-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    gap: 15px;
}

.news-title {
    font-size: 18px;
    font-weight: 600;
    color: #fff;
    margin: 0;
    line-height: 1.4;
    flex: 1;
}

.news-badges {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.badge {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.badge-featured {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 152, 0, 0.2));
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.badge-category {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.2), rgba(255, 119, 198, 0.2));
    color: rgba(120, 119, 198, 0.9);
    border: 1px solid rgba(120, 119, 198, 0.3);
}

.news-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 12px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 13px;
}

.meta-item i {
    color: rgba(120, 119, 198, 0.7);
    width: 14px;
}

.news-summary {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
}

/* 新闻操作 */
.news-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: flex-end;
    min-width: 120px;
}

.status-toggle {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.status-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 26px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(60, 60, 80, 0.8);
    border: 2px solid rgba(80, 80, 100, 0.6);
    border-radius: 26px;
    transition: all 0.3s ease;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 2px;
    top: 2px;
    background: linear-gradient(135deg, #ffffff, #e8e8e8);
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

input:checked + .slider {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.9), rgba(255, 119, 198, 0.7));
    border-color: rgba(120, 119, 198, 0.8);
    box-shadow: 0 0 15px rgba(120, 119, 198, 0.4);
}

input:checked + .slider:before {
    transform: translateX(24px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.action-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
}

.btn-action {
    width: 42px;
    height: 42px;
    border-radius: 12px;
    border: 2px solid;
    background: linear-gradient(135deg,
        rgba(20, 20, 30, 0.9) 0%,
        rgba(30, 30, 45, 0.9) 100%);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    font-size: 16px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.btn-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent);
    transition: left 0.5s ease;
}

.btn-action:hover::before {
    left: 100%;
}

.btn-action i {
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
    filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.3));
}
}

/* 新闻管理操作按钮样式 - 确保与admin.css统一 */
.btn-action {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    border: 1px solid;
    border-radius: 10px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    min-width: 85px;
    justify-content: center;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent);
    transition: left 0.5s ease;
}

.btn-action:hover::before {
    left: 100%;
}

.btn-action i {
    transition: transform 0.3s ease;
    filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.3));
}

.btn-action:hover i {
    transform: scale(1.1);
}

/* 编辑按钮 - 统一蓝色渐变 */
.btn-action.btn-edit, .btn-edit {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.8) 0%,
        rgba(37, 99, 235, 0.8) 50%,
        rgba(29, 78, 216, 0.8) 100%) !important;
    border-color: rgba(59, 130, 246, 0.6) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.btn-action.btn-edit:hover, .btn-edit:hover {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 1) 0%,
        rgba(37, 99, 235, 1) 50%,
        rgba(29, 78, 216, 1) 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    color: #ffffff !important;
    text-decoration: none;
}

/* 删除按钮 - 统一红色渐变 */
.btn-action.btn-delete, .btn-delete {
    background: linear-gradient(135deg,
        rgba(239, 68, 68, 0.8) 0%,
        rgba(220, 38, 38, 0.8) 50%,
        rgba(185, 28, 28, 0.8) 100%) !important;
    border-color: rgba(239, 68, 68, 0.6) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.btn-action.btn-delete:hover, .btn-delete:hover {
    background: linear-gradient(135deg,
        rgba(239, 68, 68, 1) 0%,
        rgba(220, 38, 38, 1) 50%,
        rgba(185, 28, 28, 1) 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
    color: #ffffff !important;
    text-decoration: none;
}

@keyframes shake {
    0%, 100% { transform: scale(1.2) rotate(-5deg) translateX(0); }
    25% { transform: scale(1.2) rotate(-5deg) translateX(-1px); }
    75% { transform: scale(1.2) rotate(-5deg) translateX(1px); }
}

.btn-action.btn-delete.disabled {
    opacity: 0.4 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
    filter: grayscale(1) !important;
    transform: none !important;
}

.btn-action.btn-delete.disabled:hover {
    transform: none !important;
    box-shadow: none !important;
}

/* 分类列表样式 */
.categories-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
    padding: 5px;
}

.category-item {
    background: linear-gradient(135deg, 
        rgba(20, 20, 30, 0.95) 0%, 
        rgba(25, 25, 35, 0.9) 50%,
        rgba(30, 30, 40, 0.95) 100%);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 20px;
    padding: 30px;
    /* 分离边框和阴影的过渡时间 */
    transition-property: transform, box-shadow, border-color, background;
    transition-duration: 0.3s, 0.3s, 0.2s, 0.3s;
    transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(15px);
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 2px 8px rgba(120, 119, 198, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    /* 防止边框闪动 - 增强版 */
    will-change: transform, box-shadow, border-color;
    backface-visibility: hidden;
    transform: translateY(0) translateZ(0);
    transform-origin: center center;
}

.category-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(120, 119, 198, 0.1),
        transparent);
    transition: left 0.8s ease;
}

.category-item:hover::before {
    left: 100%;
}

.category-item:hover {
    border-color: rgba(120, 119, 198, 0.45);
    box-shadow: 
        0 12px 40px rgba(0, 0, 0, 0.32),
        0 5px 22px rgba(120, 119, 198, 0.18),
        0 0 25px rgba(120, 119, 198, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.06);
    transform: translateY(-1px) translateZ(0);
}

.category-content-wrapper {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

/* 分类图标 */
.category-icon {
    width: 70px;
    height: 70px;
    border-radius: 16px;
    overflow: hidden;
    flex-shrink: 0;
    background: linear-gradient(135deg,
        rgba(30, 30, 40, 0.9) 0%,
        rgba(40, 40, 55, 0.9) 100%);
    border: 2px solid rgba(120, 119, 198, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(120, 119, 198, 0.7);
    font-size: 28px;
    box-shadow: 
        0 8px 25px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.category-item:hover .category-icon {
    border-color: rgba(120, 119, 198, 0.4);
    box-shadow: 
        0 10px 28px rgba(120, 119, 198, 0.15),
        0 0 15px rgba(120, 119, 198, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* 分类信息 */
.category-info {
    flex: 1;
    min-width: 0;
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    gap: 15px;
}

.category-name {
    font-size: 20px;
    font-weight: 600;
    color: #fff;
    margin: 0;
    line-height: 1.4;
    flex: 1;
}

.category-badges {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.badge-slug {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.2), rgba(255, 119, 198, 0.2));
    color: rgba(120, 119, 198, 0.9);
    border: 1px solid rgba(120, 119, 198, 0.3);
}

.category-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 12px;
}

.category-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
}

/* 分类操作 */
.category-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: flex-end;
    min-width: 120px;
}

/* 分页样式 */
.pagination-container {
    margin-top: 30px;
    padding-top: 25px;
    border-top: 1px solid rgba(120, 119, 198, 0.2);
}

.pagination-nav {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.page-btn, .page-number {
    padding: 8px 16px;
    border-radius: 8px;
    border: 1px solid rgba(120, 119, 198, 0.3);
    background: rgba(20, 20, 30, 0.8);
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.page-btn:hover, .page-number:hover {
    background: rgba(120, 119, 198, 0.2);
    border-color: rgba(120, 119, 198, 0.5);
    color: #fff;
    transform: translateY(-1px);
}

.page-number.active {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.6), rgba(255, 119, 198, 0.4));
    border-color: rgba(120, 119, 198, 0.6);
    color: #fff;
}

.pagination-info {
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
}

/* 防止边角闪动的额外优化 */
.news-item,
.category-item {
    /* 确保边框渲染稳定 */
    contain: layout style paint;
    isolation: isolate;
    /* 强制GPU加速，避免边框重绘 */
    transform-style: preserve-3d;
    perspective: 1000px;
}

.news-item *,
.category-item * {
    /* 防止子元素影响父元素的边框渲染 */
    pointer-events: auto;
}

.news-item:hover *,
.category-item:hover * {
    /* 悬停时保持子元素的指针事件 */
    pointer-events: auto;
}

/* 边框稳定性增强 */
.news-item::after,
.category-item::after {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    border: 1px solid transparent;
    border-radius: inherit;
    pointer-events: none;
    z-index: -1;
}

/* 新闻页面专用优化 - 防止与其他页面样式冲突 */
.news-container .news-item,
.news-container .category-item {
    /* 确保新闻页面的卡片不受其他页面样式影响 */
    transform: translateY(0) translateZ(0) !important;
}

.news-container .news-item:hover,
.news-container .category-item:hover {
    /* 确保悬停效果的一致性 */
    transform: translateY(-1px) translateZ(0) !important;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: rgba(255, 255, 255, 0.7);
}

.empty-icon {
    font-size: 64px;
    color: rgba(120, 119, 198, 0.4);
    margin-bottom: 20px;
}

.empty-title {
    font-size: 24px;
    font-weight: 600;
    color: #fff;
    margin: 0 0 12px 0;
}

.empty-description {
    font-size: 16px;
    margin: 0 0 30px 0;
    line-height: 1.5;
}

/* 表单样式 */
.form-grid {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.form-section {
    background: rgba(20, 20, 30, 0.6);
    border: 1px solid rgba(120, 119, 198, 0.2);
    border-radius: 16px;
    overflow: hidden;
}

.section-header {
    background: linear-gradient(135deg, rgba(15, 15, 15, 0.5), rgba(25, 25, 35, 0.4));
    padding: 22px 25px;
    border-bottom: 1px solid rgba(120, 119, 198, 0.2);
}

.section-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 18px;
    font-weight: 600;
    color: #fff;
    margin: 0;
}

.section-title i {
    color: rgba(120, 119, 198, 0.8);
    font-size: 16px;
}

.form-section .form-row,
.form-section .form-group {
    padding: 0 25px;
}

.form-section .form-row:first-of-type,
.form-section .form-group:first-of-type {
    padding-top: 35px;
}

.form-section .form-row:last-of-type,
.form-section .form-group:last-of-type {
    padding-bottom: 25px;
}

/* 确保form-row内的form-group不会有额外的padding */
.form-section .form-row .form-group {
    padding: 0;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
    align-items: start;
}

/* 三列布局 */
.form-row.three-columns {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 15px;
}

/* 中等屏幕适配 */
@media (max-width: 1024px) and (min-width: 769px) {
    .form-row.three-columns {
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }

    .form-row.three-columns .form-group:nth-child(3) {
        grid-column: 1 / -1;
    }
}

.form-row:last-child {
    margin-bottom: 0;
}

.form-row .form-group {
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.form-row .form-group .form-label {
    margin-bottom: 8px;
    flex-shrink: 0;
}

.form-row .form-group .form-input,
.form-row .form-group .form-textarea,
.form-row .form-group .form-select {
    flex: 1;
    min-height: 44px;
}

.form-row .form-group .form-help {
    margin-top: 6px;
    flex-shrink: 0;
}

.form-group {
    margin-bottom: 20px;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
}

.form-label.required::after {
    content: '*';
    color: #ff6b6b;
    margin-left: 4px;
}

.form-label i {
    color: rgba(120, 119, 198, 0.8);
    width: 14px;
    flex-shrink: 0;
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    background: rgba(15, 15, 15, 0.6);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 10px;
    padding: 12px 16px;
    color: #fff;
    font-size: 14px;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: rgba(120, 119, 198, 0.6);
    box-shadow: 0 0 0 3px rgba(120, 119, 198, 0.1);
    background: rgba(15, 15, 15, 0.8);
}

.form-input::placeholder,
.form-textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
    font-family: inherit;
}

.form-help {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
    margin-top: 6px;
    line-height: 1.4;
}

/* 文件上传 */
.upload-area {
    position: relative;
    border: 2px dashed rgba(120, 119, 198, 0.3);
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    background: rgba(15, 15, 15, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: rgba(120, 119, 198, 0.5);
    background: rgba(15, 15, 15, 0.5);
}

.form-file {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.upload-placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.upload-placeholder i {
    font-size: 32px;
    color: rgba(120, 119, 198, 0.6);
    margin-bottom: 12px;
}

.upload-placeholder p {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 500;
}

.upload-placeholder small {
    color: rgba(255, 255, 255, 0.5);
    font-size: 12px;
}

.current-image {
    margin-top: 15px;
}

.image-preview {
    margin-top: 8px;
}

.preview-img {
    max-width: 200px;
    max-height: 120px;
    border-radius: 8px;
    border: 1px solid rgba(120, 119, 198, 0.3);
}

/* 复选框样式 */
.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-custom {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(120, 119, 198, 0.4);
    border-radius: 4px;
    background: rgba(15, 15, 15, 0.6);
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.8), rgba(255, 119, 198, 0.6));
    border-color: rgba(120, 119, 198, 0.8);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-size: 12px;
    font-weight: bold;
}

.checkbox-text {
    display: flex;
    align-items: center;
    gap: 8px;
}

.checkbox-text i {
    color: rgba(120, 119, 198, 0.8);
    width: 14px;
}

/* 表单操作按钮 */
.form-actions {
    padding: 30px;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.btn-primary, .btn-secondary, .btn-submit, .btn-cancel {
    padding: 12px 24px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    border: 1px solid;
    position: relative;
    overflow: hidden;
}

.btn-primary, .btn-submit {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.6), rgba(255, 119, 198, 0.4));
    color: #ffffff;
    border-color: rgba(120, 119, 198, 0.6);
    box-shadow: 0 4px 15px rgba(120, 119, 198, 0.2);
}

.btn-primary::before, .btn-submit::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover::before, .btn-submit:hover::before {
    left: 100%;
}

.btn-primary:hover, .btn-submit:hover {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.8), rgba(255, 119, 198, 0.6));
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(120, 119, 198, 0.3);
    color: #ffffff;
}

.btn-secondary, .btn-cancel {
    background: rgba(60, 60, 80, 0.6);
    color: rgba(255, 255, 255, 0.8);
    border-color: rgba(80, 80, 100, 0.6);
}

.btn-secondary::before, .btn-cancel::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent);
    transition: left 0.5s ease;
}

.btn-secondary:hover::before, .btn-cancel:hover::before {
    left: 100%;
}

.btn-secondary:hover, .btn-cancel:hover {
    background: rgba(80, 80, 100, 0.8);
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(60, 60, 80, 0.3);
}

/* 按钮图标动画 */
.btn-primary i, .btn-secondary i, .btn-submit i, .btn-cancel i,
.btn-add-custom i, .btn-back i, .btn-action i {
    transition: transform 0.3s ease;
}

.btn-primary:hover i, .btn-secondary:hover i, .btn-submit:hover i, .btn-cancel:hover i {
    transform: scale(1.1);
}

.btn-add-custom:hover i {
    transform: scale(1.1) rotate(90deg);
}

.btn-back:hover i {
    transform: scale(1.1) translateX(-2px);
}

.btn-action:hover i {
    transform: scale(1.2);
}

/* 日期选择器图标颜色调整 */
.form-input[type="date"]::-webkit-calendar-picker-indicator,
.form-input[type="datetime-local"]::-webkit-calendar-picker-indicator {
    filter: invert(1) hue-rotate(240deg) saturate(1.5) brightness(1.2) !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

.form-input[type="date"]::-webkit-calendar-picker-indicator:hover,
.form-input[type="datetime-local"]::-webkit-calendar-picker-indicator:hover {
    filter: invert(1) hue-rotate(240deg) saturate(2) brightness(1.5) !important;
    transform: scale(1.1) !important;
}

/* 加载动画 */
@keyframes pulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

.loading {
    animation: pulse 1.5s ease-in-out infinite;
}

/* 表单验证样式 */
.form-input.error, .form-textarea.error, .form-select.error {
    border-color: rgba(239, 68, 68, 0.6) !important;
    box-shadow: 0 0 10px rgba(239, 68, 68, 0.3) !important;
}

.form-input.success, .form-textarea.success, .form-select.success {
    border-color: rgba(34, 197, 94, 0.6) !important;
    box-shadow: 0 0 10px rgba(34, 197, 94, 0.3) !important;
}

/* 工具提示样式 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background: rgba(20, 20, 30, 0.95);
    color: #fff;
    text-align: center;
    border-radius: 8px;
    padding: 8px 12px;
    position: absolute;
    z-index: 1000;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
    border: 1px solid rgba(120, 119, 198, 0.3);
    backdrop-filter: blur(10px);
}

.tooltip .tooltiptext::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: rgba(20, 20, 30, 0.95) transparent transparent transparent;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* 滚动条样式 */
.ck-editor__editable::-webkit-scrollbar,
.form-textarea::-webkit-scrollbar {
    width: 8px;
}

.ck-editor__editable::-webkit-scrollbar-track,
.form-textarea::-webkit-scrollbar-track {
    background: rgba(30, 30, 40, 0.5);
    border-radius: 4px;
}

.ck-editor__editable::-webkit-scrollbar-thumb,
.form-textarea::-webkit-scrollbar-thumb {
    background: rgba(120, 119, 198, 0.6);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.ck-editor__editable::-webkit-scrollbar-thumb:hover,
.form-textarea::-webkit-scrollbar-thumb:hover {
    background: rgba(120, 119, 198, 0.8);
}

/* 响应式设计 */
@media (max-width: 900px) {
    .categories-list {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .news-list {
        gap: 20px;
        padding: 0;
    }
    
    .news-item {
        padding: 20px;
        border-radius: 16px;
    }
    
    .news-content-wrapper {
        flex-direction: column;
        gap: 15px;
    }

    .news-thumbnail {
        width: 100%;
        height: 200px;
    }

    .news-actions {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }
    
    .categories-list {
        gap: 20px;
        padding: 0;
    }
    
    .category-item {
        padding: 20px;
        border-radius: 16px;
    }
    
    .category-content-wrapper {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .category-icon {
        width: 60px;
        height: 60px;
        font-size: 24px;
        margin: 0 auto;
    }
    
    .category-actions {
        flex-direction: row;
        justify-content: center;
        min-width: auto;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .form-row.three-columns {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .btn-primary, .btn-secondary, .btn-submit, .btn-cancel {
        justify-content: center;
        width: 100%;
    }
    

}



/* CKEditor 5 编辑器样式适配 */
.ck-editor {
    margin-bottom: 20px;
}

.ck-editor__editable {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* CKEditor 5 内容样式 */
.ck-content h1, .ck-content h2, .ck-content h3,
.ck-content h4, .ck-content h5, .ck-content h6 {
    margin: 1em 0 0.5em 0;
    font-weight: 600;
}

.ck-content p {
    margin: 0.5em 0;
    line-height: 1.6;
}

.ck-content blockquote {
    border-left: 4px solid rgba(120, 119, 198, 0.6);
    background: rgba(120, 119, 198, 0.1);
    padding: 15px 20px;
    margin: 1em 0;
    border-radius: 0 8px 8px 0;
}

.ck-content code {
    background: rgba(120, 119, 198, 0.2);
    color: rgba(120, 219, 255, 0.9);
    padding: 3px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.ck-content pre {
    background: rgba(20, 20, 30, 0.8);
    color: rgba(120, 219, 255, 0.9);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid rgba(120, 119, 198, 0.3);
    overflow-x: auto;
    margin: 1em 0;
}

.ck-content table {
    border-collapse: collapse;
    width: 100%;
    margin: 1em 0;
}

.ck-content table td, .ck-content table th {
    border: 1px solid rgba(120, 119, 198, 0.3);
    padding: 8px 12px;
}

.ck-content table th {
    background: rgba(120, 119, 198, 0.2);
    font-weight: 600;
}

/* 日期输入框样式优化 */
.form-input[type="date"], .form-input[type="datetime-local"] {
    position: relative;
    color: #ffffff !important;
    background: rgba(15, 15, 15, 0.6) !important;
    border: 1px solid rgba(120, 119, 198, 0.3) !important;
}

/* 日期输入框文字颜色 */
.form-input[type="date"]::-webkit-datetime-edit,
.form-input[type="datetime-local"]::-webkit-datetime-edit {
    color: #ffffff !important;
}

.form-input[type="date"]::-webkit-datetime-edit-text,
.form-input[type="datetime-local"]::-webkit-datetime-edit-text {
    color: rgba(255, 255, 255, 0.7) !important;
}

.form-input[type="date"]::-webkit-datetime-edit-month-field,
.form-input[type="date"]::-webkit-datetime-edit-day-field,
.form-input[type="date"]::-webkit-datetime-edit-year-field,
.form-input[type="datetime-local"]::-webkit-datetime-edit-month-field,
.form-input[type="datetime-local"]::-webkit-datetime-edit-day-field,
.form-input[type="datetime-local"]::-webkit-datetime-edit-year-field,
.form-input[type="datetime-local"]::-webkit-datetime-edit-hour-field,
.form-input[type="datetime-local"]::-webkit-datetime-edit-minute-field {
    color: #ffffff !important;
    background: transparent !important;
}

/* ===== 自定义分页样式 ===== */
.custom-pagination-container {
    margin-top: 30px;
    padding: 25px;
    background: linear-gradient(135deg, 
        rgba(20, 20, 30, 0.8) 0%, 
        rgba(25, 25, 35, 0.7) 100%);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 16px;
    backdrop-filter: blur(15px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.custom-pagination-nav {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.pagination-info {
    text-align: left;
    flex-shrink: 0;
    min-width: 200px;
}

.pagination-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    font-weight: 500;
    text-shadow: 0 0 10px rgba(120, 119, 198, 0.3);
}

.pagination-buttons {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    flex: 1;
}

.pagination-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    background: linear-gradient(135deg, 
        rgba(120, 119, 198, 0.2) 0%, 
        rgba(255, 119, 198, 0.15) 100%);
    border: 1px solid rgba(120, 119, 198, 0.4);
    border-radius: 10px;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    min-width: 40px;
    justify-content: center;
}

.pagination-btn:hover {
    background: linear-gradient(135deg, 
        rgba(120, 119, 198, 0.4) 0%, 
        rgba(255, 119, 198, 0.3) 100%);
    border-color: rgba(120, 119, 198, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(120, 119, 198, 0.2);
    color: #ffffff;
}

.pagination-btn.active {
    background: linear-gradient(135deg, 
        rgba(120, 119, 198, 0.8) 0%, 
        rgba(255, 119, 198, 0.6) 100%);
    border-color: rgba(120, 119, 198, 0.8);
    color: #ffffff;
    font-weight: 700;
    box-shadow: 0 6px 25px rgba(120, 119, 198, 0.4);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.pagination-btn.disabled {
    background: rgba(60, 60, 70, 0.3);
    border-color: rgba(120, 119, 198, 0.2);
    color: rgba(255, 255, 255, 0.4);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.pagination-btn.disabled:hover {
    background: rgba(60, 60, 70, 0.3);
    border-color: rgba(120, 119, 198, 0.2);
    color: rgba(255, 255, 255, 0.4);
    transform: none;
    box-shadow: none;
}

.pagination-ellipsis {
    color: rgba(255, 255, 255, 0.5);
    padding: 10px 8px;
    font-weight: bold;
    display: flex;
    align-items: center;
}

/* 自定义分页响应式设计 */
@media (max-width: 768px) {
    .custom-pagination-nav {
        flex-direction: column;
        gap: 15px;
    }
    
    .pagination-info {
        text-align: center;
        min-width: auto;
    }
    
    .pagination-buttons {
        justify-content: center;
        gap: 6px;
    }
    
    .pagination-btn {
        padding: 8px 12px;
        font-size: 12px;
        min-width: 36px;
    }
    
    .pagination-text {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .pagination-buttons {
        gap: 4px;
    }
    
    .pagination-btn {
        padding: 6px 10px;
        font-size: 11px;
        min-width: 32px;
    }
    
    /* 在小屏幕上隐藏首页/末页文字，只显示图标 */
    .pagination-first span:not(.fas),
    .pagination-last span:not(.fas),
    .pagination-prev span:not(.fas),
    .pagination-next span:not(.fas) {
        display: none;
    }
}

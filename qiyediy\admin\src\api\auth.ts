/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 认证API接口
 */

import { http } from '@/utils/http'
import type { 
  LoginCredentials, 
  LoginResponse, 
  RegisterData, 
  RegisterResponse,
  UserInfo,
  ChangePasswordData,
  ForgotPasswordData,
  ResetPasswordData,
  SendCodeData,
  VerifyCodeData,
  BindPhoneData,
  UpdateProfileData,
  CheckResponse
} from '@/types/auth'

/**
 * 认证相关API
 */
export const authApi = {
  /**
   * 用户登录
   */
  login: (data: LoginCredentials): Promise<LoginResponse> => {
    return http.post('/auth/login', data)
  },

  /**
   * 用户注册
   */
  register: (data: RegisterData): Promise<RegisterResponse> => {
    return http.post('/auth/register', data)
  },

  /**
   * 用户登出
   */
  logout: (): Promise<void> => {
    return http.post('/auth/logout')
  },

  /**
   * 刷新Token
   */
  refresh: (): Promise<{ token: string; expires_in: number }> => {
    return http.post('/auth/refresh')
  },

  /**
   * 获取当前用户信息
   */
  me: (): Promise<{
    user: UserInfo
    permissions: string[]
    roles: any[]
  }> => {
    return http.get('/auth/me')
  },

  /**
   * 修改密码
   */
  changePassword: (data: ChangePasswordData): Promise<void> => {
    return http.post('/auth/change-password', data)
  },

  /**
   * 忘记密码 - 发送重置邮件
   */
  forgotPassword: (data: ForgotPasswordData): Promise<void> => {
    return http.post('/auth/forgot-password', data)
  },

  /**
   * 重置密码
   */
  resetPassword: (data: ResetPasswordData): Promise<void> => {
    return http.post('/auth/reset-password', data)
  },

  /**
   * 发送验证码
   */
  sendCode: (data: SendCodeData): Promise<void> => {
    return http.post('/auth/send-code', data)
  },

  /**
   * 验证验证码
   */
  verifyCode: (data: VerifyCodeData): Promise<{ verified: boolean }> => {
    return http.post('/auth/verify-code', data)
  },

  /**
   * 绑定手机号
   */
  bindPhone: (data: BindPhoneData): Promise<void> => {
    return http.post('/auth/bind-phone', data)
  },

  /**
   * 更新个人资料
   */
  updateProfile: (data: UpdateProfileData): Promise<UserInfo> => {
    return http.post('/auth/update-profile', data)
  },

  /**
   * 上传头像
   */
  uploadAvatar: (data: FormData): Promise<{
    avatar: string
    avatar_url: string
  }> => {
    return http.post('/auth/upload-avatar', data, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 检查用户名是否可用
   */
  checkUsername: (username: string): Promise<CheckResponse> => {
    return http.get('/auth/check-username', { params: { username } })
  },

  /**
   * 检查邮箱是否可用
   */
  checkEmail: (email: string): Promise<CheckResponse> => {
    return http.get('/auth/check-email', { params: { email } })
  },

  /**
   * 检查手机号是否可用
   */
  checkPhone: (phone: string): Promise<CheckResponse> => {
    return http.get('/auth/check-phone', { params: { phone } })
  }
}

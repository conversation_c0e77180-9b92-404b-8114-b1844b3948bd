<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 基础控制器
 */

declare(strict_types=1);

namespace app;

use think\App;
use think\exception\ValidateException;
use think\Request;
use think\Response;
use think\Validate;

/**
 * 控制器基础类
 */
abstract class BaseController
{
    /**
     * Request实例
     * @var Request
     */
    protected Request $request;

    /**
     * 应用实例
     * @var App
     */
    protected App $app;

    /**
     * 是否批量验证
     * @var bool
     */
    protected bool $batchValidate = false;

    /**
     * 控制器中间件
     * @var array
     */
    protected array $middleware = [];

    /**
     * 构造方法
     * @param App $app
     */
    public function __construct(App $app)
    {
        $this->app = $app;
        $this->request = $this->app->request;

        // 控制器初始化
        $this->initialize();
    }

    /**
     * 初始化
     */
    protected function initialize(): void
    {
        // 子类可重写此方法
    }

    /**
     * 验证数据
     * @param array $data 数据
     * @param string|array $validate 验证器名或者验证规则数组
     * @param array $message 提示信息
     * @param bool $batch 是否批量验证
     * @return array|string|true
     * @throws ValidateException
     */
    protected function validate(array $data, $validate, array $message = [], bool $batch = false)
    {
        if (is_array($validate)) {
            $v = new Validate();
            $v->rule($validate);
        } else {
            if (strpos($validate, '.')) {
                // 支持场景
                [$validate, $scene] = explode('.', $validate);
            }
            $class = false !== strpos($validate, '\\') ? $validate : $this->app->parseClass('validate', $validate);
            $v = new $class();
            if (!empty($scene)) {
                $v->scene($scene);
            }
        }

        $v->message($message);

        // 是否批量验证
        if ($batch || $this->batchValidate) {
            $v->batch(true);
        }

        return $v->failException(true)->check($data);
    }

    /**
     * 成功响应
     * @param mixed $data 数据
     * @param string $message 消息
     * @param int $code 状态码
     * @return Response
     */
    protected function success($data = [], string $message = 'success', int $code = 200): Response
    {
        return json([
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ]);
    }

    /**
     * 错误响应
     * @param string $message 错误消息
     * @param int $code 错误码
     * @param mixed $data 数据
     * @return Response
     */
    protected function error(string $message = 'error', int $code = 400, $data = []): Response
    {
        return json([
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ]);
    }

    /**
     * 分页响应
     * @param mixed $data 分页数据
     * @param string $message 消息
     * @return Response
     */
    protected function paginate($data, string $message = 'success'): Response
    {
        return json([
            'code' => 200,
            'message' => $message,
            'data' => [
                'list' => $data->items(),
                'total' => $data->total(),
                'per_page' => $data->listRows(),
                'current_page' => $data->currentPage(),
                'last_page' => $data->lastPage(),
                'has_more' => $data->hasPages()
            ],
            'timestamp' => time()
        ]);
    }

    /**
     * 获取请求参数
     * @param string|null $name 参数名
     * @param mixed $default 默认值
     * @return mixed
     */
    protected function param(?string $name = null, $default = null)
    {
        return $this->request->param($name, $default);
    }

    /**
     * 获取POST参数
     * @param string|null $name 参数名
     * @param mixed $default 默认值
     * @return mixed
     */
    protected function post(?string $name = null, $default = null)
    {
        return $this->request->post($name, $default);
    }

    /**
     * 获取GET参数
     * @param string|null $name 参数名
     * @param mixed $default 默认值
     * @return mixed
     */
    protected function get(?string $name = null, $default = null)
    {
        return $this->request->get($name, $default);
    }

    /**
     * 获取分页参数
     * @return array
     */
    protected function getPaginateParams(): array
    {
        return [
            'page' => $this->param('page', 1),
            'per_page' => min($this->param('per_page', 15), 100), // 限制最大每页数量
            'keyword' => $this->param('keyword', ''),
            'sort_field' => $this->param('sort_field', 'id'),
            'sort_order' => $this->param('sort_order', 'desc')
        ];
    }

    /**
     * 获取当前用户ID
     * @return int|null
     */
    protected function getUserId(): ?int
    {
        return $this->request->user['id'] ?? null;
    }

    /**
     * 获取当前用户信息
     * @return array|null
     */
    protected function getUser(): ?array
    {
        return $this->request->user ?? null;
    }

    /**
     * 检查用户权限
     * @param string $permission 权限标识
     * @return bool
     */
    protected function checkPermission(string $permission): bool
    {
        $user = $this->getUser();
        if (!$user) {
            return false;
        }

        // 超级管理员拥有所有权限
        if ($user['is_super_admin'] ?? false) {
            return true;
        }

        // 检查用户权限
        $permissions = $user['permissions'] ?? [];
        return in_array($permission, $permissions);
    }

    /**
     * 记录操作日志
     * @param string $action 操作类型
     * @param string $resource 资源类型
     * @param int|null $resourceId 资源ID
     * @param array $data 操作数据
     * @return void
     */
    protected function logOperation(string $action, string $resource, ?int $resourceId = null, array $data = []): void
    {
        try {
            $logData = [
                'user_id' => $this->getUserId(),
                'action' => $action,
                'resource' => $resource,
                'resource_id' => $resourceId,
                'data' => json_encode($data, JSON_UNESCAPED_UNICODE),
                'ip_address' => get_client_ip(),
                'user_agent' => get_user_agent(),
                'created_at' => date('Y-m-d H:i:s')
            ];

            // 这里可以使用队列异步记录日志
            // Queue::push('LogOperation', $logData);
            
            // 或者直接记录到数据库
            \app\model\Log::create($logData);
        } catch (\Exception $e) {
            // 记录日志失败不应该影响主业务
            trace('记录操作日志失败: ' . $e->getMessage(), 'error');
        }
    }

    /**
     * 批量删除验证
     * @param array $ids ID数组
     * @param int $maxCount 最大删除数量
     * @return void
     * @throws ValidateException
     */
    protected function validateBatchDelete(array $ids, int $maxCount = 100): void
    {
        if (empty($ids)) {
            throw new ValidateException('请选择要删除的数据');
        }

        if (count($ids) > $maxCount) {
            throw new ValidateException("一次最多只能删除{$maxCount}条数据");
        }

        // 验证ID格式
        foreach ($ids as $id) {
            if (!is_numeric($id) || $id <= 0) {
                throw new ValidateException('无效的ID格式');
            }
        }
    }

    /**
     * 处理上传文件
     * @param string $field 字段名
     * @param array $config 配置
     * @return array
     * @throws ValidateException
     */
    protected function handleUpload(string $field, array $config = []): array
    {
        $file = $this->request->file($field);
        if (!$file) {
            throw new ValidateException('请选择要上传的文件');
        }

        // 默认配置
        $defaultConfig = [
            'size' => 10 * 1024 * 1024, // 10MB
            'ext' => 'jpg,jpeg,png,gif,webp',
            'type' => 'image'
        ];
        $config = array_merge($defaultConfig, $config);

        // 验证文件
        $validate = [
            'size' => $config['size'],
            'ext' => $config['ext']
        ];

        if (!$file->checkExt($config['ext'])) {
            throw new ValidateException('文件格式不支持');
        }

        if (!$file->checkSize($config['size'])) {
            throw new ValidateException('文件大小超出限制');
        }

        // 生成文件名和路径
        $filename = generate_filename($file->extension());
        $savePath = upload_path($config['type'], $filename);

        // 保存文件
        $file->move($savePath);

        return [
            'filename' => $filename,
            'original_name' => $file->getOriginalName(),
            'path' => $savePath,
            'size' => $file->getSize(),
            'mime_type' => $file->getMime(),
            'url' => '/' . $savePath
        ];
    }
}

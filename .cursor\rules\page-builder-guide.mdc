---
description: 
globs: 
alwaysApply: false
---
# 页面构建器 (DIY) 开发指南

## 概述
页面构建器是一个可视化的网页设计工具，允许用户通过拖拽组件来构建网页。

## 核心文件结构

### JavaScript 组件文件
- [all.js](mdc:public/diy/js/all.js) - 主要的页面构建器逻辑
- [navbar.js](mdc:public/diy/js/components/navbar.js) - 导航栏组件
- [hero.js](mdc:public/diy/js/components/hero.js) - 英雄区块组件  
- [card.js](mdc:public/diy/js/components/card.js) - 卡片组件
- [footer.js](mdc:public/diy/js/components/footer.js) - 页脚组件
- [section.js](mdc:public/diy/js/components/section.js) - 区块组件
- [stats.js](mdc:public/diy/js/components/stats.js) - 统计数据组件
- [background.js](mdc:public/diy/js/components/background.js) - 背景组件
- [contact.js](mdc:public/diy/js/components/contact.js) - 联系表单组件
- [team.js](mdc:public/diy/js/components/team.js) - 团队展示组件
- [testimonials.js](mdc:public/diy/js/components/testimonials.js) - 客户评价组件
- [textblock.js](mdc:public/diy/js/components/textblock.js) - 文本块组件
- [manager.js](mdc:public/diy/js/components/manager.js) - 组件管理器

### CSS 样式文件
- `public/diy/css/` - 页面构建器样式文件

## 组件开发规范

### 组件基本结构
每个组件都应该包含以下基本结构：

```javascript
const componentName = {
    // 组件基本信息
    name: '组件名称',
    icon: 'fas fa-icon-name',
    category: '组件分类',
    
    // 组件属性配置
    properties: {
        propertyName: {
            type: 'text|color|number|select|boolean',
            label: '属性标签',
            default: '默认值',
            options: [] // 仅select类型需要
        }
    },
    
    // HTML模板
    template: `
        <div class="component-wrapper">
            <!-- 组件HTML结构 -->
        </div>
    `,
    
    // CSS生成函数
    generateCSS: function() {
        const props = this.properties;
        return `
            .component-wrapper {
                /* 基于属性生成的CSS */
            }
        `;
    },
    
    // 组件初始化函数
    init: function() {
        // 组件初始化逻辑
    }
};
```

### 样式管理系统

#### 按需CSS生成
使用 `componentStylesLibrary` 管理组件样式：

```javascript
// 组件样式库
const componentStylesLibrary = {
    navbar: `/* 导航栏样式 */`,
    hero: `/* 英雄区块样式 */`,
    // ... 其他组件样式
};

// 按需生成CSS
function generateUsedComponentStyles() {
    let styles = '';
    usedComponents.forEach(componentType => {
        if (componentStylesLibrary[componentType]) {
            styles += componentStylesLibrary[componentType];
        }
    });
    return styles;
}
```

#### 3D效果样式管理
使用 `effect3DStylesLibrary` 管理3D效果：

```javascript
const effect3DStylesLibrary = {
    bubbles: `/* 气泡效果样式 */`,
    particles: `/* 粒子效果样式 */`,
    geometric: `/* 几何图形效果样式 */`,
    starfield: `/* 星空效果样式 */`,
    waves: `/* 波浪效果样式 */`
};
```

### 属性配置系统

#### 属性类型定义
```javascript
properties: {
    // 文本输入
    title: {
        type: 'text',
        label: '标题',
        default: '默认标题'
    },
    
    // 颜色选择
    backgroundColor: {
        type: 'color',
        label: '背景颜色',
        default: '#ffffff'
    },
    
    // 数值输入
    fontSize: {
        type: 'number',
        label: '字体大小',
        default: 16,
        min: 12,
        max: 48
    },
    
    // 下拉选择
    alignment: {
        type: 'select',
        label: '对齐方式',
        default: 'center',
        options: [
            { value: 'left', label: '左对齐' },
            { value: 'center', label: '居中' },
            { value: 'right', label: '右对齐' }
        ]
    },
    
    // 布尔值
    showShadow: {
        type: 'boolean',
        label: '显示阴影',
        default: true
    }
}
```

### 响应式设计

#### 断点管理
```javascript
const breakpoints = {
    mobile: '768px',
    tablet: '1024px',
    desktop: '1200px'
};

// 响应式CSS生成
function generateResponsiveCSS(component) {
    return `
        .${component.class} {
            /* 默认样式 */
        }
        
        @media (max-width: ${breakpoints.mobile}) {
            .${component.class} {
                /* 移动端样式 */
            }
        }
        
        @media (min-width: ${breakpoints.mobile}) and (max-width: ${breakpoints.tablet}) {
            .${component.class} {
                /* 平板样式 */
            }
        }
    `;
}
```

## 组件开发最佳实践

### 1. 性能优化
- 使用按需CSS生成，避免加载无用样式
- 实现CSS缓存机制，避免重复生成
- 使用事件委托处理大量DOM事件

### 2. 用户体验
- 提供实时预览功能
- 添加拖拽视觉反馈
- 实现撤销/重做功能

### 3. 代码质量
- 遵循模块化开发原则
- 添加详细的注释说明
- 实现错误处理和验证

### 4. 扩展性
- 设计可插拔的组件架构
- 支持自定义组件注册
- 提供组件继承机制

## 动画和效果系统

### 3D效果实现
```javascript
// 3D效果内容生成
function generateAllComponentContent() {
    const effects = ['bubbles', 'particles', 'geometric', 'starfield', 'waves'];
    
    effects.forEach(effect => {
        if (usedEffects.includes(effect)) {
            generateEffectContent(effect);
        }
    });
}

// 单个效果内容生成
function generateEffectContent(effectType) {
    switch(effectType) {
        case 'bubbles':
            return generateBubbles();
        case 'particles':
            return generateParticles();
        // ... 其他效果
    }
}
```

### 动画配置
```javascript
const animationConfig = {
    duration: '2s',
    timing: 'ease-in-out',
    delay: '0s',
    iteration: 'infinite'
};
```

## 数据管理

### 组件数据结构
```javascript
const componentData = {
    id: 'unique-id',
    type: 'component-type',
    properties: {
        // 组件属性值
    },
    position: {
        x: 0,
        y: 0
    },
    size: {
        width: '100%',
        height: 'auto'
    }
};
```

### 页面数据保存
```javascript
const pageData = {
    components: [],
    globalStyles: {},
    settings: {
        responsive: true,
        animations: true
    }
};
```

## 调试和测试

### 调试工具
- 使用 `console.log()` 输出调试信息
- 实现组件状态检查器
- 添加性能监控

### 测试策略
- 单元测试：测试单个组件功能
- 集成测试：测试组件间交互
- 用户测试：测试用户操作流程

## 部署和优化

### 代码压缩
- 压缩JavaScript文件
- 压缩CSS文件
- 优化图片资源

### 缓存策略
- 实现组件缓存
- 使用浏览器缓存
- CDN加速静态资源

## 扩展开发

### 添加新组件
1. 在 `public/diy/js/components/` 目录创建新的组件文件
2. 按照组件结构规范定义组件
3. 在样式库中添加对应样式
4. 注册组件到组件管理器

### 自定义主题
1. 定义主题配置文件
2. 实现主题切换逻辑
3. 支持用户自定义主题

参考 [页面构建器优化文档.md](mdc:页面构建器优化文档.md) 了解详细的优化计划和实施方案。


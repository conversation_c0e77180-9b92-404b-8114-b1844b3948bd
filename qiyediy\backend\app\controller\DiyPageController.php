<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * QiyeDIY企业建站系统 - DIY页面管理控制器
 */

declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\model\DiyPage;
use app\service\DiyPageService;
use app\validate\DiyPageValidate;
use think\Response;

/**
 * DIY页面管理控制器
 */
class DiyPageController extends BaseController
{
    protected DiyPageService $diyPageService;

    public function __construct()
    {
        parent::__construct();
        $this->diyPageService = new DiyPageService();
    }

    /**
     * 页面列表
     * @return Response
     */
    public function index(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.view')) {
                return $this->error('没有权限访问', 403);
            }

            $params = $this->getPaginateParams();
            $result = $this->diyPageService->getList($params);

            // 记录操作日志
            $this->logOperation('view', 'diy_page_list');

            return $this->paginate($result);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 页面详情
     * @param int $id 页面ID
     * @return Response
     */
    public function read(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.view')) {
                return $this->error('没有权限访问', 403);
            }

            $page = $this->diyPageService->getById($id);
            if (!page) {
                return $this->error('页面不存在', 404);
            }

            // 记录操作日志
            $this->logOperation('view', 'diy_page', $id);

            return $this->success($page);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 创建页面
     * @return Response
     */
    public function save(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.create')) {
                return $this->error('没有权限创建页面', 403);
            }

            $data = $this->post();

            // 数据验证
            $this->validate($data, DiyPageValidate::class . '.create');

            // 创建页面
            $page = $this->diyPageService->create($data);

            // 记录操作日志
            $this->logOperation('create', 'diy_page', $page->id, $data);

            return $this->success($page, '页面创建成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 更新页面
     * @param int $id 页面ID
     * @return Response
     */
    public function update(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.update')) {
                return $this->error('没有权限更新页面', 403);
            }

            $data = $this->post();

            // 数据验证
            $this->validate($data, DiyPageValidate::class . '.update');

            // 更新页面
            $page = $this->diyPageService->update($id, $data);

            // 记录操作日志
            $this->logOperation('update', 'diy_page', $id, $data);

            return $this->success($page, '页面更新成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除页面
     * @param int $id 页面ID
     * @return Response
     */
    public function delete(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.delete')) {
                return $this->error('没有权限删除页面', 403);
            }

            // 删除页面
            $this->diyPageService->delete($id);

            // 记录操作日志
            $this->logOperation('delete', 'diy_page', $id);

            return $this->success([], '页面删除成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 批量删除页面
     * @return Response
     */
    public function batchDelete(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.delete')) {
                return $this->error('没有权限删除页面', 403);
            }

            $ids = $this->post('ids', []);
            $this->validateBatchDelete($ids);

            // 批量删除
            $count = $this->diyPageService->batchDelete($ids);

            // 记录操作日志
            $this->logOperation('batch_delete', 'diy_page', null, ['ids' => $ids, 'count' => $count]);

            return $this->success(['count' => $count], "成功删除 {$count} 个页面");

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 发布页面
     * @param int $id 页面ID
     * @return Response
     */
    public function publish(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.publish')) {
                return $this->error('没有权限发布页面', 403);
            }

            $this->diyPageService->publish($id);

            // 记录操作日志
            $this->logOperation('publish', 'diy_page', $id);

            return $this->success([], '页面发布成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 取消发布页面
     * @param int $id 页面ID
     * @return Response
     */
    public function unpublish(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.publish')) {
                return $this->error('没有权限操作', 403);
            }

            $this->diyPageService->unpublish($id);

            // 记录操作日志
            $this->logOperation('unpublish', 'diy_page', $id);

            return $this->success([], '页面已取消发布');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 复制页面
     * @param int $id 页面ID
     * @return Response
     */
    public function copy(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.create')) {
                return $this->error('没有权限复制页面', 403);
            }

            $data = $this->post();
            $page = $this->diyPageService->copy($id, $data);

            // 记录操作日志
            $this->logOperation('copy', 'diy_page', $page->id, ['source_id' => $id]);

            return $this->success($page, '页面复制成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 保存页面内容
     * @param int $id 页面ID
     * @return Response
     */
    public function saveContent(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.update')) {
                return $this->error('没有权限编辑页面', 403);
            }

            $data = $this->post();
            $this->diyPageService->saveContent($id, $data);

            // 记录操作日志
            $this->logOperation('save_content', 'diy_page', $id);

            return $this->success([], '页面内容保存成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 预览页面
     * @param int $id 页面ID
     * @return Response
     */
    public function preview(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.view')) {
                return $this->error('没有权限预览页面', 403);
            }

            $previewData = $this->diyPageService->getPreviewData($id);

            return $this->success($previewData);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取页面统计
     * @return Response
     */
    public function statistics(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.view')) {
                return $this->error('没有权限访问', 403);
            }

            $stats = $this->diyPageService->getStatistics();

            return $this->success($stats);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 导出页面
     * @return Response
     */
    public function export(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.export')) {
                return $this->error('没有权限导出', 403);
            }

            $params = $this->get();
            $filePath = $this->diyPageService->export($params);

            // 记录操作日志
            $this->logOperation('export', 'diy_page_list', null, $params);

            return $this->success(['file_path' => $filePath], '导出成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 导入页面
     * @return Response
     */
    public function import(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('diy.import')) {
                return $this->error('没有权限导入', 403);
            }

            // 处理文件上传
            $fileInfo = $this->handleUpload('file', [
                'size' => 50 * 1024 * 1024, // 50MB
                'ext' => 'json,zip',
                'type' => 'import'
            ]);

            // 导入页面
            $result = $this->diyPageService->import($fileInfo['path']);

            // 记录操作日志
            $this->logOperation('import', 'diy_page_list', null, [
                'file' => $fileInfo['filename'],
                'result' => $result
            ]);

            return $this->success($result, '导入完成');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}

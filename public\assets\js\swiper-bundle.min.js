/**
 * 简化版Swiper - 完整功能实现
 */
(function(global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
    typeof define === 'function' && define.amd ? define(factory) :
    (global = global || self, global.Swiper = factory());
}(this, function() {
    'use strict';

    class Swiper {
        constructor(container, params = {}) {
            this.container = typeof container === 'string' ? document.querySelector(container) : container;
            if (!this.container) {
                console.error('Swiper container not found');
                return;
            }

            this.params = Object.assign({
                direction: 'horizontal',
                speed: 300,
                autoplay: false,
                loop: false,
                effect: 'slide',
                fadeEffect: {
                    crossFade: true
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev'
                },
                on: {}
            }, params);

            this.wrapper = this.container.querySelector('.swiper-wrapper');
            this.slides = Array.from(this.container.querySelectorAll('.swiper-slide'));
            this.activeIndex = 0;
            this.realIndex = 0;
            this.isAnimating = false;
            this.autoplayTimer = null;
            this.eventListeners = {};

            // 初始化
            this.init();
        }

        init() {
            this.setupSlides();
            this.setupPagination();
            this.setupNavigation();
            this.setupAutoplay();
            this.setupEvents();
            
            // 显示第一张幻灯片
            this.updateSlides();
            
            // 触发初始化事件
            this.emit('init');
            
        }

        setupSlides() {
            if (!this.wrapper || this.slides.length === 0) return;

            // 设置wrapper样式
            this.wrapper.style.position = 'relative';
            this.wrapper.style.width = '100%';
            this.wrapper.style.height = '100%';
            this.wrapper.style.display = 'flex';
            this.wrapper.style.transition = `transform ${this.params.speed}ms ease-in-out`;

            this.slides.forEach((slide, index) => {
                // 不使用absolute定位，使用flex布局
                slide.style.position = 'relative';
                slide.style.flexShrink = '0';
                slide.style.width = '100%';
                slide.style.height = '100%';
                slide.style.display = 'flex';
                slide.style.alignItems = 'center';
                slide.style.justifyContent = 'center';

                // 确保所有内容都可见
                slide.style.opacity = '1';
                slide.style.visibility = 'visible';

                // 确保slide内的所有元素都可见
                const heroContent = slide.querySelector('.hero-content');
                if (heroContent) {
                    heroContent.style.display = 'block';
                    heroContent.style.visibility = 'visible';
                    heroContent.style.opacity = '1';
                    heroContent.style.zIndex = '999';
                }
            });

            // 初始位置
            this.wrapper.style.transform = 'translateX(0%)';
        }

        setupPagination() {
            if (!this.params.pagination.el) return;

            const paginationEl = this.container.querySelector(this.params.pagination.el);
            if (!paginationEl) return;

            paginationEl.innerHTML = '';
            
            this.slides.forEach((_, index) => {
                const bullet = document.createElement('span');
                bullet.className = 'swiper-pagination-bullet';
                if (index === 0) bullet.classList.add('swiper-pagination-bullet-active');
                
                if (this.params.pagination.clickable) {
                    bullet.addEventListener('click', () => this.slideTo(index));
                }
                
                paginationEl.appendChild(bullet);
            });

            this.paginationBullets = Array.from(paginationEl.querySelectorAll('.swiper-pagination-bullet'));
        }

        setupNavigation() {
            if (this.params.navigation.nextEl) {
                const nextEl = this.container.querySelector(this.params.navigation.nextEl);
                if (nextEl) {
                    nextEl.addEventListener('click', () => this.slideNext());
                }
            }

            if (this.params.navigation.prevEl) {
                const prevEl = this.container.querySelector(this.params.navigation.prevEl);
                if (prevEl) {
                    prevEl.addEventListener('click', () => this.slidePrev());
                }
            }
        }

        setupAutoplay() {
            if (this.params.autoplay && this.params.autoplay.delay) {
                this.autoplay = {
                    start: () => {
                        if (this.autoplayTimer) return;
                        this.autoplayTimer = setInterval(() => {
                            this.slideNext();
                        }, this.params.autoplay.delay);
                    },
                    stop: () => {
                        if (this.autoplayTimer) {
                            clearInterval(this.autoplayTimer);
                            this.autoplayTimer = null;
                        }
                    }
                };

                // 自动启动
                this.autoplay.start();

                // 鼠标悬停时暂停
                if (!this.params.autoplay.disableOnInteraction) {
                    this.container.addEventListener('mouseenter', () => this.autoplay.stop());
                    this.container.addEventListener('mouseleave', () => this.autoplay.start());
                }
            } else {
                this.autoplay = {
                    start: () => {},
                    stop: () => {}
                };
            }
        }

        setupEvents() {
            // 触摸事件
            let startX = 0;
            let startY = 0;
            let isDragging = false;

            this.container.addEventListener('touchstart', (e) => {
                startX = e.touches[0].clientX;
                startY = e.touches[0].clientY;
                isDragging = true;
            });

            this.container.addEventListener('touchmove', (e) => {
                if (!isDragging) return;
                e.preventDefault();
            });

            this.container.addEventListener('touchend', (e) => {
                if (!isDragging) return;
                isDragging = false;

                const endX = e.changedTouches[0].clientX;
                const endY = e.changedTouches[0].clientY;
                const diffX = startX - endX;
                const diffY = startY - endY;

                if (Math.abs(diffX) > Math.abs(diffY)) {
                    if (Math.abs(diffX) > 50) {
                        if (diffX > 0) {
                            this.slideNext();
                        } else {
                            this.slidePrev();
                        }
                    }
                }
            });

            // 键盘事件
            document.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowLeft') {
                    this.slidePrev();
                } else if (e.key === 'ArrowRight') {
                    this.slideNext();
                }
            });
        }

        slideTo(index) {
            if (this.isAnimating || index === this.activeIndex) return;
            if (index < 0 || index >= this.slides.length) return;

            this.isAnimating = true;
            const oldIndex = this.activeIndex;
            this.activeIndex = index;
            this.realIndex = index;

            console.log(`🎠 切换到幻灯片 ${index + 1}`);

            // 使用wrapper的transform来切换幻灯片
            const translateX = -index * 100;
            this.wrapper.style.transform = `translateX(${translateX}%)`;

            // 确保当前幻灯片的内容可见
            this.slides.forEach((slide, i) => {
                const heroContent = slide.querySelector('.hero-content');
                if (heroContent) {
                    heroContent.style.display = 'block';
                    heroContent.style.visibility = 'visible';
                    heroContent.style.opacity = '1';
                    heroContent.style.zIndex = '999';

                    // 强制显示文字元素
                    const title = heroContent.querySelector('.hero-title');
                    const subtitle = heroContent.querySelector('.hero-subtitle');
                    const description = heroContent.querySelector('.hero-description');

                    [title, subtitle, description].forEach(el => {
                        if (el) {
                            el.style.display = 'block';
                            el.style.visibility = 'visible';
                            el.style.opacity = '1';
                            el.style.color = 'white';
                            el.style.zIndex = '999';
                        }
                    });
                }
            });

            this.updatePagination();
            this.emit('slideChange');

            setTimeout(() => {
                this.isAnimating = false;
            }, this.params.speed);
        }

        slideNext() {
            let nextIndex = this.activeIndex + 1;
            if (nextIndex >= this.slides.length) {
                nextIndex = this.params.loop ? 0 : this.slides.length - 1;
            }
            this.slideTo(nextIndex);
        }

        slidePrev() {
            let prevIndex = this.activeIndex - 1;
            if (prevIndex < 0) {
                prevIndex = this.params.loop ? this.slides.length - 1 : 0;
            }
            this.slideTo(prevIndex);
        }

        updateSlides() {
            this.slideTo(0);
        }

        updatePagination() {
            if (!this.paginationBullets) return;

            this.paginationBullets.forEach((bullet, index) => {
                if (index === this.activeIndex) {
                    bullet.classList.add('swiper-pagination-bullet-active');
                } else {
                    bullet.classList.remove('swiper-pagination-bullet-active');
                }
            });
        }

        // 事件系统
        on(event, callback) {
            if (!this.eventListeners[event]) {
                this.eventListeners[event] = [];
            }
            this.eventListeners[event].push(callback);
        }

        emit(event, ...args) {
            if (this.eventListeners[event]) {
                this.eventListeners[event].forEach(callback => {
                    callback.call(this, ...args);
                });
            }

            // 检查参数中的事件监听器
            if (this.params.on && this.params.on[event]) {
                this.params.on[event].call(this, ...args);
            }
        }

        destroy() {
            if (this.autoplayTimer) {
                clearInterval(this.autoplayTimer);
            }
            this.eventListeners = {};
        }
    }

    return Swiper;
})); 
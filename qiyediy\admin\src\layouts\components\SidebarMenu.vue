<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 侧边栏菜单组件
-->

<template>
  <div class="sidebar-menu">
    <el-menu
      :default-active="activeMenu"
      :collapse="appStore.sidebarCollapsed"
      :unique-opened="true"
      :collapse-transition="false"
      mode="vertical"
      class="sidebar-menu-el"
      @select="handleMenuSelect"
    >
      <template v-for="item in menuList" :key="item.path">
        <!-- 单级菜单 -->
        <el-menu-item
          v-if="!item.children || item.children.length === 0"
          :index="item.path"
          :disabled="item.disabled"
        >
          <el-icon v-if="item.icon">
            <component :is="item.icon" />
          </el-icon>
          <template #title>
            <span>{{ item.title }}</span>
            <el-badge
              v-if="item.badge"
              :value="item.badge"
              :type="item.badgeType || 'primary'"
              class="menu-badge"
            />
          </template>
        </el-menu-item>

        <!-- 多级菜单 -->
        <el-sub-menu
          v-else
          :index="item.path"
          :disabled="item.disabled"
        >
          <template #title>
            <el-icon v-if="item.icon">
              <component :is="item.icon" />
            </el-icon>
            <span>{{ item.title }}</span>
            <el-badge
              v-if="item.badge"
              :value="item.badge"
              :type="item.badgeType || 'primary'"
              class="menu-badge"
            />
          </template>

          <template v-for="child in item.children" :key="child.path">
            <!-- 二级菜单项 -->
            <el-menu-item
              v-if="!child.children || child.children.length === 0"
              :index="child.path"
              :disabled="child.disabled"
            >
              <el-icon v-if="child.icon">
                <component :is="child.icon" />
              </el-icon>
              <template #title>
                <span>{{ child.title }}</span>
                <el-badge
                  v-if="child.badge"
                  :value="child.badge"
                  :type="child.badgeType || 'primary'"
                  class="menu-badge"
                />
              </template>
            </el-menu-item>

            <!-- 三级菜单 -->
            <el-sub-menu
              v-else
              :index="child.path"
              :disabled="child.disabled"
            >
              <template #title>
                <el-icon v-if="child.icon">
                  <component :is="child.icon" />
                </el-icon>
                <span>{{ child.title }}</span>
              </template>

              <el-menu-item
                v-for="grandChild in child.children"
                :key="grandChild.path"
                :index="grandChild.path"
                :disabled="grandChild.disabled"
              >
                <el-icon v-if="grandChild.icon">
                  <component :is="grandChild.icon" />
                </el-icon>
                <template #title>
                  <span>{{ grandChild.title }}</span>
                </template>
              </el-menu-item>
            </el-sub-menu>
          </template>
        </el-sub-menu>
      </template>
    </el-menu>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/store/modules/app'
import { useUserStore } from '@/store/modules/user'
import { useMenuStore } from '@/store/modules/menu'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()
const menuStore = useMenuStore()

// 计算属性
const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta?.activeMenu) {
    return meta.activeMenu as string
  }
  return path
})

const menuList = computed(() => {
  return menuStore.menuList.filter(item => {
    // 检查权限
    if (item.permission && !userStore.hasPermission(item.permission)) {
      return false
    }
    
    // 过滤子菜单
    if (item.children) {
      item.children = item.children.filter(child => {
        if (child.permission && !userStore.hasPermission(child.permission)) {
          return false
        }
        
        // 过滤三级菜单
        if (child.children) {
          child.children = child.children.filter(grandChild => {
            return !grandChild.permission || userStore.hasPermission(grandChild.permission)
          })
        }
        
        return true
      })
    }
    
    return true
  })
})

/**
 * 处理菜单选择
 */
const handleMenuSelect = (index: string) => {
  if (index === route.path) return
  
  // 外部链接
  if (index.startsWith('http')) {
    window.open(index, '_blank')
    return
  }
  
  router.push(index)
}
</script>

<style lang="scss" scoped>
.sidebar-menu {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color-light);
    border-radius: 2px;

    &:hover {
      background: var(--el-border-color);
    }
  }
}

.sidebar-menu-el {
  border: none;
  background-color: transparent;

  :deep(.el-menu-item) {
    height: 48px;
    line-height: 48px;
    margin: 4px 12px;
    border-radius: 6px;
    color: var(--el-text-color-regular);
    transition: all 0.3s ease;

    &:hover {
      background-color: var(--el-fill-color-light);
      color: var(--el-text-color-primary);
    }

    &.is-active {
      background-color: var(--el-color-primary);
      color: #fff;

      .el-icon {
        color: #fff;
      }
    }

    .el-icon {
      margin-right: 8px;
      font-size: 16px;
      color: var(--el-text-color-secondary);
      transition: color 0.3s ease;
    }
  }

  :deep(.el-sub-menu) {
    .el-sub-menu__title {
      height: 48px;
      line-height: 48px;
      margin: 4px 12px;
      border-radius: 6px;
      color: var(--el-text-color-regular);
      transition: all 0.3s ease;

      &:hover {
        background-color: var(--el-fill-color-light);
        color: var(--el-text-color-primary);
      }

      .el-icon {
        margin-right: 8px;
        font-size: 16px;
        color: var(--el-text-color-secondary);
        transition: color 0.3s ease;
      }

      .el-sub-menu__icon-arrow {
        margin-top: -3px;
        color: var(--el-text-color-placeholder);
      }
    }

    &.is-opened {
      .el-sub-menu__title {
        background-color: var(--el-fill-color-light);
        color: var(--el-text-color-primary);
      }
    }

    .el-menu {
      background-color: transparent;

      .el-menu-item {
        margin: 2px 24px;
        padding-left: 40px !important;
        font-size: 13px;

        &.is-active {
          background-color: var(--el-color-primary-light-9);
          color: var(--el-color-primary);

          &::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background-color: var(--el-color-primary);
          }
        }
      }

      .el-sub-menu {
        .el-sub-menu__title {
          margin: 2px 24px;
          padding-left: 40px !important;
          font-size: 13px;
        }

        .el-menu-item {
          padding-left: 56px !important;
        }
      }
    }
  }

  // 折叠状态样式
  &.el-menu--collapse {
    width: 64px;

    :deep(.el-menu-item),
    :deep(.el-sub-menu__title) {
      margin: 4px 8px;
      text-align: center;
      padding: 0 !important;

      .el-icon {
        margin-right: 0;
        font-size: 18px;
      }

      span,
      .el-sub-menu__icon-arrow {
        display: none;
      }
    }

    :deep(.el-sub-menu) {
      .el-menu {
        display: none;
      }
    }
  }
}

.menu-badge {
  margin-left: auto;
  margin-right: 8px;

  :deep(.el-badge__content) {
    font-size: 10px;
    height: 16px;
    line-height: 16px;
    padding: 0 4px;
    min-width: 16px;
  }
}

// 暗色主题适配
:deep(.dark) {
  .sidebar-menu-el {
    .el-menu-item {
      &:hover {
        background-color: var(--el-fill-color);
      }

      &.is-active {
        background-color: var(--el-color-primary);
      }
    }

    .el-sub-menu {
      .el-sub-menu__title {
        &:hover {
          background-color: var(--el-fill-color);
        }
      }

      &.is-opened {
        .el-sub-menu__title {
          background-color: var(--el-fill-color);
        }
      }

      .el-menu {
        .el-menu-item {
          &.is-active {
            background-color: var(--el-color-primary-dark-2);
          }
        }
      }
    }
  }
}
</style>

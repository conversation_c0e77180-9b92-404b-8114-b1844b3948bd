/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * QiyeDIY企业建站系统 - SEO优化组合式函数
 */

import { computed, watch } from 'vue'
import type { DiyPage } from '~/types/diy'

interface SEOConfig {
  title?: string
  description?: string
  keywords?: string
  image?: string
  url?: string
  type?: string
  siteName?: string
  locale?: string
  author?: string
  publishedTime?: string
  modifiedTime?: string
  section?: string
  tags?: string[]
  canonical?: string
  robots?: string
  structuredData?: Record<string, any>
}

/**
 * SEO优化组合式函数
 */
export const useSEO = () => {
  const route = useRoute()
  const runtimeConfig = useRuntimeConfig()
  
  /**
   * 设置页面SEO信息
   */
  const setSEO = (config: SEOConfig) => {
    const {
      title,
      description,
      keywords,
      image,
      url,
      type = 'website',
      siteName = 'QiyeDIY企业建站系统',
      locale = 'zh_CN',
      author,
      publishedTime,
      modifiedTime,
      section,
      tags,
      canonical,
      robots = 'index,follow',
      structuredData
    } = config
    
    // 构建完整URL
    const fullUrl = url || `${runtimeConfig.public.siteUrl}${route.path}`
    const fullImage = image ? (image.startsWith('http') ? image : `${runtimeConfig.public.siteUrl}${image}`) : `${runtimeConfig.public.siteUrl}/images/og-default.jpg`
    
    // 设置基础SEO
    useSeoMeta({
      title,
      description,
      keywords,
      author,
      robots,
      
      // Open Graph
      ogTitle: title,
      ogDescription: description,
      ogImage: fullImage,
      ogUrl: fullUrl,
      ogType: type,
      ogSiteName: siteName,
      ogLocale: locale,
      
      // Twitter Card
      twitterCard: 'summary_large_image',
      twitterTitle: title,
      twitterDescription: description,
      twitterImage: fullImage,
      twitterSite: '@qiyediy',
      twitterCreator: '@qiyediy',
      
      // Article specific (for blog posts)
      ...(type === 'article' && {
        articleAuthor: author,
        articlePublishedTime: publishedTime,
        articleModifiedTime: modifiedTime,
        articleSection: section,
        articleTag: tags
      })
    })
    
    // 设置canonical链接
    if (canonical) {
      useHead({
        link: [
          { rel: 'canonical', href: canonical }
        ]
      })
    }
    
    // 设置结构化数据
    if (structuredData) {
      useJsonld(structuredData)
    }
  }
  
  /**
   * 为DIY页面设置SEO
   */
  const setPageSEO = (page: DiyPage) => {
    const config: SEOConfig = {
      title: page.seo_title || page.title,
      description: page.seo_description || page.description,
      keywords: page.seo_keywords,
      image: page.seo_image,
      url: `${runtimeConfig.public.siteUrl}/${page.slug}`,
      type: page.page_type === 'article' ? 'article' : 'website',
      author: page.author_name,
      publishedTime: page.created_at,
      modifiedTime: page.updated_at,
      section: page.category_name,
      tags: page.tags,
      canonical: page.canonical_url
    }
    
    // 生成结构化数据
    const structuredData = generateStructuredData(page)
    if (structuredData) {
      config.structuredData = structuredData
    }
    
    setSEO(config)
  }
  
  /**
   * 生成结构化数据
   */
  const generateStructuredData = (page: DiyPage) => {
    const baseData = {
      '@context': 'https://schema.org',
      '@type': 'WebPage',
      name: page.title,
      description: page.description,
      url: `${runtimeConfig.public.siteUrl}/${page.slug}`,
      inLanguage: 'zh-CN',
      isPartOf: {
        '@type': 'WebSite',
        name: 'QiyeDIY企业建站系统',
        url: runtimeConfig.public.siteUrl
      }
    }
    
    // 根据页面类型生成不同的结构化数据
    switch (page.page_type) {
      case 'article':
        return {
          ...baseData,
          '@type': 'Article',
          headline: page.title,
          author: {
            '@type': 'Person',
            name: page.author_name || '管理员'
          },
          datePublished: page.created_at,
          dateModified: page.updated_at,
          publisher: {
            '@type': 'Organization',
            name: 'QiyeDIY',
            logo: {
              '@type': 'ImageObject',
              url: `${runtimeConfig.public.siteUrl}/images/logo.png`
            }
          },
          mainEntityOfPage: {
            '@type': 'WebPage',
            '@id': `${runtimeConfig.public.siteUrl}/${page.slug}`
          }
        }
        
      case 'product':
        return {
          ...baseData,
          '@type': 'Product',
          name: page.title,
          description: page.description,
          image: page.seo_image || `${runtimeConfig.public.siteUrl}/images/product-default.jpg`,
          brand: {
            '@type': 'Brand',
            name: 'QiyeDIY'
          }
        }
        
      case 'service':
        return {
          ...baseData,
          '@type': 'Service',
          name: page.title,
          description: page.description,
          provider: {
            '@type': 'Organization',
            name: 'QiyeDIY'
          }
        }
        
      case 'organization':
        return {
          ...baseData,
          '@type': 'Organization',
          name: page.title,
          description: page.description,
          url: `${runtimeConfig.public.siteUrl}/${page.slug}`,
          logo: page.seo_image || `${runtimeConfig.public.siteUrl}/images/logo.png`
        }
        
      default:
        return baseData
    }
  }
  
  /**
   * 设置面包屑导航结构化数据
   */
  const setBreadcrumbStructuredData = (breadcrumbs: Array<{ name: string, url: string }>) => {
    const structuredData = {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: breadcrumbs.map((item, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        name: item.name,
        item: item.url.startsWith('http') ? item.url : `${runtimeConfig.public.siteUrl}${item.url}`
      }))
    }
    
    useJsonld(structuredData)
  }
  
  /**
   * 设置网站搜索框结构化数据
   */
  const setSearchBoxStructuredData = () => {
    const structuredData = {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      url: runtimeConfig.public.siteUrl,
      potentialAction: {
        '@type': 'SearchAction',
        target: {
          '@type': 'EntryPoint',
          urlTemplate: `${runtimeConfig.public.siteUrl}/search?q={search_term_string}`
        },
        'query-input': 'required name=search_term_string'
      }
    }
    
    useJsonld(structuredData)
  }
  
  /**
   * 设置组织信息结构化数据
   */
  const setOrganizationStructuredData = (orgData: {
    name: string
    url: string
    logo: string
    description?: string
    address?: string
    phone?: string
    email?: string
    socialLinks?: string[]
  }) => {
    const structuredData = {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: orgData.name,
      url: orgData.url,
      logo: orgData.logo,
      description: orgData.description,
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: orgData.phone,
        email: orgData.email,
        contactType: 'customer service'
      },
      address: orgData.address ? {
        '@type': 'PostalAddress',
        addressLocality: orgData.address
      } : undefined,
      sameAs: orgData.socialLinks
    }
    
    useJsonld(structuredData)
  }
  
  /**
   * 生成sitemap数据
   */
  const generateSitemap = async () => {
    try {
      const { data: pages } = await $fetch('/api/pages/sitemap')
      
      const urls = pages.map((page: any) => ({
        loc: `${runtimeConfig.public.siteUrl}/${page.slug}`,
        lastmod: page.updated_at,
        changefreq: page.change_frequency || 'weekly',
        priority: page.priority || 0.5
      }))
      
      return urls
    } catch (error) {
      console.error('生成sitemap失败:', error)
      return []
    }
  }
  
  /**
   * 预加载关键资源
   */
  const preloadResources = (resources: Array<{ href: string, as: string, type?: string }>) => {
    useHead({
      link: resources.map(resource => ({
        rel: 'preload',
        href: resource.href,
        as: resource.as,
        type: resource.type
      }))
    })
  }
  
  /**
   * 设置DNS预解析
   */
  const setDNSPrefetch = (domains: string[]) => {
    useHead({
      link: domains.map(domain => ({
        rel: 'dns-prefetch',
        href: domain
      }))
    })
  }
  
  /**
   * 设置预连接
   */
  const setPreconnect = (urls: string[]) => {
    useHead({
      link: urls.map(url => ({
        rel: 'preconnect',
        href: url,
        crossorigin: 'anonymous'
      }))
    })
  }
  
  return {
    setSEO,
    setPageSEO,
    setBreadcrumbStructuredData,
    setSearchBoxStructuredData,
    setOrganizationStructuredData,
    generateSitemap,
    preloadResources,
    setDNSPrefetch,
    setPreconnect
  }
}

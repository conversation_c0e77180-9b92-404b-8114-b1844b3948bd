---
description: 
globs: 
alwaysApply: false
---
# 数据库设计与操作规范

## 数据库配置

### 连接配置
数据库连接配置位于 [database.php](mdc:config/database.php) 文件中。

### 支持的数据库
- MySQL (推荐)
- PostgreSQL
- SQLite
- SQL Server

## 数据表设计规范

### 命名规范
1. **表名**: 使用小写字母和下划线，复数形式
   - 正确: `users`, `news_categories`, `user_profiles`
   - 错误: `User`, `newsCategory`, `userProfile`

2. **字段名**: 使用小写字母和下划线
   - 正确: `user_id`, `created_at`, `is_active`
   - 错误: `userId`, `createdAt`, `isActive`

3. **索引名**: 使用表名_字段名_索引类型格式
   - 主键: `pk_表名`
   - 外键: `fk_表名_字段名`
   - 普通索引: `idx_表名_字段名`
   - 唯一索引: `uk_表名_字段名`

### 标准字段

#### 必备字段
```sql
-- 主键
id INT(11) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,

-- 时间戳字段
create_time INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
update_time INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',

-- 软删除字段
delete_time INT(11) UNSIGNED DEFAULT NULL COMMENT '删除时间',

-- 状态字段
status TINYINT(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=禁用,1=启用'
```

#### 常用字段类型
```sql
-- 字符串字段
title VARCHAR(255) NOT NULL DEFAULT '' COMMENT '标题',
content TEXT COMMENT '内容',
description TEXT COMMENT '描述',

-- 数值字段
sort_order INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
views INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '浏览量',
price DECIMAL(10,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '价格',

-- 枚举字段
type ENUM('type1','type2','type3') NOT NULL DEFAULT 'type1' COMMENT '类型',

-- JSON字段 (MySQL 5.7+)
extra_data JSON COMMENT '扩展数据'
```

## 数据表结构示例

### 新闻表 (news)
```sql
CREATE TABLE `news` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `category_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '分类ID',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '标题',
  `slug` varchar(255) NOT NULL DEFAULT '' COMMENT 'URL别名',
  `summary` text COMMENT '摘要',
  `content` longtext COMMENT '内容',
  `image` varchar(500) NOT NULL DEFAULT '' COMMENT '封面图片',
  `tags` varchar(500) NOT NULL DEFAULT '' COMMENT '标签',
  `author` varchar(100) NOT NULL DEFAULT '' COMMENT '作者',
  `views` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '浏览量',
  `is_featured` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否推荐',
  `published_at` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '发布时间',
  `sort_order` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` int(11) unsigned DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_news_slug` (`slug`),
  KEY `idx_news_category_id` (`category_id`),
  KEY `idx_news_status` (`status`),
  KEY `idx_news_published_at` (`published_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新闻表';
```

### 新闻分类表 (news_categories)
```sql
CREATE TABLE `news_categories` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '父级ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '分类名称',
  `slug` varchar(100) NOT NULL DEFAULT '' COMMENT 'URL别名',
  `description` text COMMENT '分类描述',
  `image` varchar(500) NOT NULL DEFAULT '' COMMENT '分类图片',
  `sort_order` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` int(11) unsigned DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_categories_slug` (`slug`),
  KEY `idx_categories_parent_id` (`parent_id`),
  KEY `idx_categories_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新闻分类表';
```

## 模型开发规范

### 基础模型结构
```php
<?php
namespace app\model;

use think\Model;
use think\model\concern\SoftDelete;

class News extends Model
{
    use SoftDelete;
    
    // 表名
    protected $name = 'news';
    
    // 主键
    protected $pk = 'id';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    protected $deleteTime = 'delete_time';
    
    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'category_id' => 'integer',
        'views' => 'integer',
        'is_featured' => 'boolean',
        'status' => 'boolean',
        'published_at' => 'timestamp',
        'create_time' => 'timestamp',
        'update_time' => 'timestamp'
    ];
    
    // 字段过滤
    protected $field = [
        'id', 'category_id', 'title', 'slug', 'summary', 
        'content', 'image', 'tags', 'author', 'views', 
        'is_featured', 'published_at', 'sort_order', 
        'status', 'create_time', 'update_time'
    ];
    
    // 关联关系
    public function category()
    {
        return $this->belongsTo(NewsCategory::class, 'category_id');
    }
    
    // 查询范围
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }
    
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', 1);
    }
    
    // 获取器
    public function getImageAttr($value)
    {
        return $value ? '/storage/' . $value : '';
    }
    
    public function getTagsAttr($value)
    {
        return $value ? explode(',', $value) : [];
    }
    
    // 修改器
    public function setTagsAttr($value)
    {
        return is_array($value) ? implode(',', $value) : $value;
    }
    
    // 静态方法
    public static function getLatestNews($limit = 10)
    {
        return self::active()
            ->order('published_at', 'desc')
            ->limit($limit)
            ->select();
    }
    
    public static function getFeaturedNews($limit = 5)
    {
        return self::active()
            ->featured()
            ->order('sort_order', 'desc')
            ->order('published_at', 'desc')
            ->limit($limit)
            ->select();
    }
}
```

### 关联关系定义

#### 一对一关系
```php
// 用户详情
public function profile()
{
    return $this->hasOne(UserProfile::class, 'user_id');
}
```

#### 一对多关系
```php
// 分类下的新闻
public function news()
{
    return $this->hasMany(News::class, 'category_id');
}
```

#### 多对多关系
```php
// 文章标签
public function tags()
{
    return $this->belongsToMany(Tag::class, 'news_tags', 'news_id', 'tag_id');
}
```

## 数据库迁移

### 创建迁移文件
```bash
php think make:migration CreateNewsTable
```

### 迁移文件结构
```php
<?php
use think\migration\Migrator;
use think\migration\db\Column;

class CreateNewsTable extends Migrator
{
    public function change()
    {
        $table = $this->table('news', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci',
            'comment' => '新闻表'
        ]);
        
        $table->addColumn('id', 'integer', [
            'identity' => true,
            'signed' => false,
            'comment' => '主键ID'
        ])
        ->addColumn('title', 'string', [
            'limit' => 255,
            'default' => '',
            'comment' => '标题'
        ])
        ->addColumn('content', 'text', [
            'null' => true,
            'comment' => '内容'
        ])
        ->addColumn('status', 'boolean', [
            'default' => 1,
            'comment' => '状态'
        ])
        ->addColumn('create_time', 'integer', [
            'signed' => false,
            'default' => 0,
            'comment' => '创建时间'
        ])
        ->addColumn('update_time', 'integer', [
            'signed' => false,
            'default' => 0,
            'comment' => '更新时间'
        ])
        ->addIndex(['status'], ['name' => 'idx_news_status'])
        ->create();
    }
}
```

### 执行迁移
```bash
# 执行迁移
php think migrate:run

# 回滚迁移
php think migrate:rollback

# 查看迁移状态
php think migrate:status
```

## 查询优化

### 索引优化
1. **主键索引**: 自动创建，无需手动添加
2. **外键索引**: 为外键字段添加索引
3. **查询索引**: 为经常查询的字段添加索引
4. **复合索引**: 为多字段查询添加复合索引

### 查询优化技巧
```php
// 1. 使用字段选择，避免SELECT *
$news = News::field('id,title,summary,image')
    ->where('status', 1)
    ->select();

// 2. 使用预加载避免N+1查询
$news = News::with('category')
    ->where('status', 1)
    ->select();

// 3. 使用分页查询大数据集
$news = News::where('status', 1)
    ->paginate(10);

// 4. 使用缓存减少数据库查询
$news = cache('latest_news', function() {
    return News::getLatestNews(10);
}, 3600);

// 5. 使用原生SQL处理复杂查询
$result = Db::query('SELECT * FROM news WHERE MATCH(title,content) AGAINST(? IN BOOLEAN MODE)', [$keyword]);
```

## 数据备份与恢复

### 备份策略
1. **定期备份**: 每日自动备份
2. **增量备份**: 备份变更数据
3. **远程备份**: 备份到云存储

### 备份命令
```bash
# MySQL备份
mysqldump -u username -p database_name > backup.sql

# 恢复数据
mysql -u username -p database_name < backup.sql
```

## 性能监控

### 慢查询监控
```sql
-- 开启慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;

-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query%';
```

### 查询分析
```sql
-- 分析查询执行计划
EXPLAIN SELECT * FROM news WHERE status = 1;

-- 查看表状态
SHOW TABLE STATUS LIKE 'news';
```

## 安全规范

### SQL注入防护
```php
// 使用参数绑定
$news = News::where('id', $id)->find();

// 避免直接拼接SQL
// 错误示例
$sql = "SELECT * FROM news WHERE id = " . $id;

// 正确示例
$news = Db::query('SELECT * FROM news WHERE id = ?', [$id]);
```

### 数据验证
```php
// 使用验证器
$validate = new \app\validate\NewsValidate();
if (!$validate->check($data)) {
    throw new \Exception($validate->getError());
}
```

### 权限控制
```php
// 数据库用户权限最小化
// 应用用户只给必要的权限：SELECT, INSERT, UPDATE, DELETE
// 避免给予DROP, CREATE等危险权限
```

参考 [website.sql](mdc:website.sql) 了解完整的数据库结构。


<?php

namespace app\middleware;

use think\Request;
use think\Response;

/**
 * 后台管理员身份验证中间件
 */
class AdminAuth
{
    /**
     * 处理请求
     *
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle(Request $request, \Closure $next)
    {
        // 确保session已启动
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        $controller = $request->controller();
        $action = $request->action();

        // 登录相关页面不需要检查
        if ($controller === 'Login' || 
            ($controller === 'login' && in_array($action, ['index', 'doLogin']))) {
            return $next($request);
        }

        // 检查是否已登录
        if (!isset($_SESSION['admin_user']) || empty($_SESSION['admin_user'])) {
            if ($request->isAjax()) {
                return json([
                    'code' => 0,
                    'msg' => '请先登录',
                    'data' => [],
                    'url' => '/admin/login'
                ]);
            } else {
                return redirect('/admin/login');
            }
        }

        // 验证session有效性
        $adminUser = $_SESSION['admin_user'];
        if (!isset($adminUser['id']) || !isset($adminUser['username'])) {
            // session数据不完整，清除并重新登录
            unset($_SESSION['admin_user']);
            
            if ($request->isAjax()) {
                return json([
                    'code' => 0,
                    'msg' => '登录状态已失效，请重新登录',
                    'data' => [],
                    'url' => '/admin/login'
                ]);
            } else {
                return redirect('/admin/login');
            }
        }

        // 检查登录时间是否过期（可选，24小时过期）
        $loginTime = $adminUser['login_time'] ?? 0;
        $expireTime = 24 * 60 * 60; // 24小时
        
        if (time() - $loginTime > $expireTime) {
            unset($_SESSION['admin_user']);
            
            if ($request->isAjax()) {
                return json([
                    'code' => 0,
                    'msg' => '登录已过期，请重新登录',
                    'data' => [],
                    'url' => '/admin/login'
                ]);
            } else {
                return redirect('/admin/login');
            }
        }

        // 更新最后活动时间
        $_SESSION['admin_user']['last_activity'] = time();

        return $next($request);
    }
} 
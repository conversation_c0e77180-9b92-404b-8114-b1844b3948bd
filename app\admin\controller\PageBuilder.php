<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\admin\controller\Base as BaseController;
use app\model\PageTemplate;
use think\facade\Request;
use think\facade\Session;
use think\facade\Db;

/**
 * 页面装修控制器
 */
class PageBuilder extends BaseController
{
    /**
     * 页面装修主页面
     */
    public function index()
    {
        // 获取操作类型和参数
        $action = Request::param('action', 'list');
        $id = Request::param('id', 0, 'intval');

        // 修复：如果URL中有参数但Request::param()获取不到，尝试从$_GET获取
        if (isset($_GET['action']) && $_GET['action'] !== $action) {
            $action = $_GET['action'];
        }
        if (isset($_GET['id']) && $_GET['id'] != $id) {
            $id = intval($_GET['id']);
        }

        // 设置分页查询参数
        $queryParams = [
            'action' => $action
        ];

        // 处理POST请求
        if (Request::isPost()) {
            return $this->handlePost();
        }

        // 处理删除操作
        if ($action === 'delete' && $id > 0) {
            return $this->delete($id);
        }

        // 初始化数据
        $templatesList = [];
        $editData = null;
        $totalItems = 0;
        $typeStats = [];
        $typeFilter = '';

        if ($action === 'list') {
            // 分页参数
            $page = Request::param('page', 1, 'intval');
            $itemsPerPage = 10;

            // 获取类型筛选参数
            $typeFilter = Request::param('type', '', 'trim');

            // 构建查询
            $query = PageTemplate::order('updated_at', 'desc')->order('id', 'desc');

            // 如果有类型筛选，添加条件
            if (!empty($typeFilter) && $typeFilter !== 'all') {
                $query->where('type', $typeFilter);
            }

            // 获取模板列表
            $totalItems = $query->count();
            $templatesList = $query->paginate([
                'list_rows' => $itemsPerPage,
                'page' => $page,
                'query' => $queryParams
            ]);

            // 获取各类型的统计数据
            $typeStats = $this->getTypeStats();
        } elseif (($action === 'edit' || $action === 'design') && $id > 0) {
            // 获取编辑数据
            $editData = PageTemplate::find($id);
            if (!$editData) {
                Session::flash('message', '模板不存在');
                Session::flash('messageType', 'error');
                return redirect('/admin/page-builder');
            }
        } elseif ($action === 'add') {
            // 添加模板时，初始化空的编辑数据
            $editData = null;

            // 获取预设类型参数
            $presetType = Request::param('preset_type', '', 'trim');
            if (!empty($presetType) && array_key_exists($presetType, PageTemplate::getTypes())) {
                $editData = (object)['type' => $presetType];
            }
        }

        return view('admin/page_builder', [
            'action' => $action,
            'templatesList' => $templatesList,
            'editData' => $editData,
            'totalItems' => $totalItems,
            'typeStats' => $typeStats,
            'currentType' => $typeFilter,
            'templateTypes' => PageTemplate::getTypes(),
            'message' => Session::pull('message') ?: '',
            'messageType' => Session::pull('messageType') ?: 'info'
        ]);
    }

    /**
     * 处理POST请求
     */
    private function handlePost()
    {
        $action = Request::param('action');

        switch ($action) {
            case 'add':
            case 'edit':
                return $this->save();

            case 'save_design':
                return $this->saveDesign();

            case 'load_design':
                return $this->loadDesign();

            case 'toggle_status':
                return $this->toggleStatus();

            case 'clear_cache':
                return $this->clearAllCache();

            default:
                return json(['success' => false, 'message' => '无效的操作']);
        }
    }

    /**
     * 保存模板基础信息
     */
    private function save()
    {
        $data = Request::param();
        $action = $data['action'];
        $id = isset($data['id']) ? intval($data['id']) : 0;

        // 验证必填字段
        if (empty($data['name'])) {
            Session::flash('message', '请输入模板名称');
            Session::flash('messageType', 'error');
            return redirect()->restore();
        }

        if (empty($data['slug'])) {
            Session::flash('message', '页面URL标识不能为空');
            Session::flash('messageType', 'error');
            return redirect()->restore();
        }

        // 验证slug格式
        if (!preg_match('/^[a-zA-Z0-9\-_]+$/', $data['slug'])) {
            Session::flash('message', 'URL标识只能包含字母、数字、连字符和下划线');
            Session::flash('messageType', 'error');
            return redirect()->restore();
        }

        // 检查slug是否重复
        $existingTemplate = PageTemplate::where('slug', $data['slug']);
        if ($action === 'edit' && $id > 0) {
            $existingTemplate->where('id', '<>', $id);
        }
        if ($existingTemplate->find()) {
            Session::flash('message', 'URL标识已存在，请使用其他标识');
            Session::flash('messageType', 'error');
            return redirect()->restore();
        }

        try {
            // 准备保存数据
            $saveData = [
                'name' => trim($data['name']),
                'slug' => trim($data['slug']),
                'description' => trim($data['description'] ?? ''),
                'type' => $data['type'] ?? 'page',
                'status' => isset($data['status']) ? 1 : 0,
            ];

            if ($action === 'add') {
                // 添加新模板
                $saveData['config'] = json_encode(['components' => [], 'styles' => []]);
                $result = PageTemplate::create($saveData);
                $message = '模板添加成功';
            } else {
                // 编辑模板
                if ($id <= 0) {
                    Session::flash('message', '无效的模板ID');
                    Session::flash('messageType', 'error');
                    return redirect()->restore();
                }

                $template = PageTemplate::find($id);
                if (!$template) {
                    Session::flash('message', '模板不存在');
                    Session::flash('messageType', 'error');
                    return redirect()->restore();
                }

                $result = $template->save($saveData);
                $message = '模板更新成功';
            }

            if ($result) {
                Session::flash('message', $message);
                Session::flash('messageType', 'success');
                return redirect('/admin/page-builder');
            } else {
                Session::flash('message', '保存失败，请重试');
                Session::flash('messageType', 'error');
                return redirect()->restore();
            }

        } catch (\Exception $e) {
            Session::flash('message', '保存失败：' . $e->getMessage());
            Session::flash('messageType', 'error');
            return redirect()->restore();
        }
    }

    /**
     * 保存设计配置
     */
    private function saveDesign()
    {
        $id = Request::param('id', 0, 'intval');
        $config = Request::param('config', '');
        $html = Request::param('html', '');
        $css = Request::param('css', '');
        $name = Request::param('name', ''); // 接收模板标题

        if ($id <= 0) {
            return json(['success' => false, 'message' => '无效的模板ID']);
        }

        try {
            $template = PageTemplate::find($id);
            if (!$template) {
                return json(['success' => false, 'message' => '模板不存在']);
            }

            $updateData = [
                'config' => $config,
                'html_content' => $html,
                'css_content' => $css,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // 如果提供了模板标题，则更新
            if (!empty($name)) {
                $updateData['name'] = trim($name);
            }

            $result = $template->save($updateData);

            if ($result) {
                // 清除相关缓存
                $this->clearPageCache($template->slug);

                return json(['success' => true, 'message' => '保存成功']);
            } else {
                return json(['success' => false, 'message' => '保存失败']);
            }

        } catch (\Exception $e) {
            return json(['success' => false, 'message' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 清除页面相关缓存
     */
    private function clearPageCache($slug = null)
    {
        try {
            // 清除页面列表缓存
            \think\facade\Cache::delete('diy_pages_list');
            \think\facade\Cache::delete('diy_page_stats');

            // 清除按类型缓存
            $types = ['page', 'home', 'product', 'news', 'case', 'contact', 'about'];
            foreach ($types as $type) {
                \think\facade\Cache::delete('diy_pages_by_type_' . $type);
            }
            \think\facade\Cache::delete('diy_pages_by_type_all');

            // 如果指定了slug，清除该页面的详情缓存
            if ($slug) {
                \think\facade\Cache::delete('diy_page_detail_' . $slug);
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 清除所有页面缓存
     */
    private function clearAllCache()
    {
        try {
            // 清除所有页面相关缓存
            $this->clearPageCache();

            // 清除所有页面详情缓存
            $slugs = PageTemplate::where('status', 1)->column('slug');
            foreach ($slugs as $slug) {
                \think\facade\Cache::delete('diy_page_detail_' . $slug);
            }

            return json(['success' => true, 'message' => '缓存清除成功']);
        } catch (\Exception $e) {
            return json(['success' => false, 'message' => '清除缓存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 加载设计配置
     */
    private function loadDesign()
    {
        $id = Request::param('id', 0, 'intval');

        if ($id <= 0) {
            return json(['success' => false, 'message' => '无效的模板ID']);
        }

        try {
            $template = PageTemplate::find($id);
            if (!$template) {
                return json(['success' => false, 'message' => '模板不存在']);
            }

            return json([
                'success' => true,
                'data' => [
                    'config' => $template->config ?: '{}',
                    'html' => $template->html_content ?: '',
                    'css' => $template->css_content ?: ''
                ]
            ]);

        } catch (\Exception $e) {
            return json(['success' => false, 'message' => '加载失败：' . $e->getMessage()]);
        }
    }

    /**
     * 切换状态
     */
    private function toggleStatus()
    {
        $id = Request::param('id', 0, 'intval');
        $status = Request::param('status', 0, 'intval');

        if ($id <= 0) {
            return json(['success' => false, 'message' => '无效的ID']);
        }

        try {
            $template = PageTemplate::find($id);
            if (!$template) {
                return json(['success' => false, 'message' => '模板不存在']);
            }

            $result = $template->save(['status' => $status]);

            if ($result) {
                return json(['success' => true, 'message' => '状态更新成功']);
            } else {
                return json(['success' => false, 'message' => '状态更新失败']);
            }

        } catch (\Exception $e) {
            return json(['success' => false, 'message' => '操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除模板
     */
    private function delete($id)
    {
        try {
            $template = PageTemplate::find($id);
            if (!$template) {
                Session::flash('message', '模板不存在');
                Session::flash('messageType', 'error');
                return redirect('/admin/page-builder');
            }

            $result = $template->delete();

            if ($result) {
                Session::flash('message', '模板删除成功');
                Session::flash('messageType', 'success');
            } else {
                Session::flash('message', '删除失败，请重试');
                Session::flash('messageType', 'error');
            }

        } catch (\Exception $e) {
            Session::flash('message', '删除失败：' . $e->getMessage());
            Session::flash('messageType', 'error');
        }

        return redirect('/admin/page-builder');
    }

    /**
     * 获取各类型统计数据
     */
    private function getTypeStats()
    {
        $stats = [];
        $types = PageTemplate::getTypes();

        // 获取总数
        $stats['all'] = PageTemplate::count();

        // 获取各类型数量
        foreach ($types as $typeKey => $typeName) {
            $stats[$typeKey] = PageTemplate::where('type', $typeKey)->count();
        }

        return $stats;
    }

    /**
     * 预览模板
     */
    public function preview($id)
    {
        try {
            $template = PageTemplate::find($id);
            if (!$template) {
                return '<h1>模板不存在</h1>';
            }

            $html = $template->html_content ?: '<h1>模板内容为空</h1><p>请先设计模板内容</p>';
            $css = $template->css_content ?: '';

            // 构建完整的HTML页面
            $fullHtml = "<!DOCTYPE html>
<html lang=\"zh-CN\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>预览: {$template->name}</title>
    <link rel=\"stylesheet\" href=\"/assets/css/bootstrap.min.css\">
    <link rel=\"stylesheet\" href=\"/assets/css/style.css\">
    <style>{$css}</style>
</head>
<body>
    {$html}
    <script src=\"/assets/js/jquery.min.js\"></script>
    <script src=\"/assets/js/bootstrap.bundle.min.js\"></script>
</body>
</html>";

            return response($fullHtml)->header(['Content-Type' => 'text/html']);

        } catch (\Exception $e) {
            return '<h1>预览失败</h1><p>' . $e->getMessage() . '</p>';
        }
    }
}

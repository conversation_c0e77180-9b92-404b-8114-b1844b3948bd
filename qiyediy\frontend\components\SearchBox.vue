<!--
  三只鱼网络科技 | 韩总 | 2024-12-20
  QiyeDIY企业建站系统 - 搜索框组件
-->

<template>
  <div class="search-box" :class="{ 'search-active': isSearchActive }">
    <!-- 搜索输入框 -->
    <div class="search-input-wrapper">
      <input
        ref="searchInput"
        v-model="searchKeyword"
        type="text"
        class="search-input"
        :placeholder="placeholder"
        @input="onInput"
        @focus="onFocus"
        @blur="onBlur"
        @keydown.enter="onSearch"
        @keydown.up="onArrowUp"
        @keydown.down="onArrowDown"
        @keydown.esc="onEscape"
      />
      
      <!-- 搜索按钮 -->
      <button 
        class="search-btn"
        @click="onSearch"
        :disabled="!searchKeyword.trim()"
      >
        <Icon name="search" />
      </button>
      
      <!-- 清空按钮 -->
      <button 
        v-if="searchKeyword"
        class="clear-btn"
        @click="onClear"
      >
        <Icon name="close" />
      </button>
    </div>
    
    <!-- 搜索建议下拉框 -->
    <div 
      v-if="showSuggestions && (suggestions.length || hotKeywords.length || searchHistory.length)"
      class="search-suggestions"
    >
      <!-- 搜索建议 -->
      <div v-if="suggestions.length" class="suggestion-group">
        <div class="suggestion-title">搜索建议</div>
        <div
          v-for="(suggestion, index) in suggestions"
          :key="`suggestion-${index}`"
          class="suggestion-item"
          :class="{ 'suggestion-active': selectedIndex === index }"
          @click="onSelectSuggestion(suggestion)"
          @mouseenter="selectedIndex = index"
        >
          <Icon name="search" class="suggestion-icon" />
          <span v-html="highlightKeyword(suggestion, searchKeyword)"></span>
        </div>
      </div>
      
      <!-- 搜索历史 -->
      <div v-if="searchHistory.length && !searchKeyword" class="suggestion-group">
        <div class="suggestion-title">
          <span>搜索历史</span>
          <button class="clear-history-btn" @click="onClearHistory">
            <Icon name="delete" />
          </button>
        </div>
        <div
          v-for="(history, index) in searchHistory"
          :key="`history-${index}`"
          class="suggestion-item"
          :class="{ 'suggestion-active': selectedIndex === suggestions.length + index }"
          @click="onSelectSuggestion(history.keyword)"
          @mouseenter="selectedIndex = suggestions.length + index"
        >
          <Icon name="history" class="suggestion-icon" />
          <span>{{ history.keyword }}</span>
          <span class="search-count">{{ history.count }}</span>
        </div>
      </div>
      
      <!-- 热门搜索 -->
      <div v-if="hotKeywords.length && !searchKeyword" class="suggestion-group">
        <div class="suggestion-title">热门搜索</div>
        <div class="hot-keywords">
          <span
            v-for="(keyword, index) in hotKeywords"
            :key="`hot-${index}`"
            class="hot-keyword"
            @click="onSelectSuggestion(keyword.keyword)"
          >
            {{ keyword.keyword }}
          </span>
        </div>
      </div>
    </div>
    
    <!-- 搜索结果快速预览 -->
    <div 
      v-if="showQuickResults && quickResults.length"
      class="quick-results"
    >
      <div class="quick-results-header">
        <span>快速预览</span>
        <NuxtLink :to="`/search?q=${encodeURIComponent(searchKeyword)}`" class="view-all-btn">
          查看全部结果
        </NuxtLink>
      </div>
      
      <div class="quick-results-list">
        <div
          v-for="result in quickResults"
          :key="`${result.type}-${result.id}`"
          class="quick-result-item"
          @click="onSelectResult(result)"
        >
          <div class="result-type">{{ getResultTypeLabel(result.type) }}</div>
          <div class="result-title" v-html="result.title"></div>
          <div class="result-description" v-html="result.description"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { debounce } from 'lodash-es'

interface SearchResult {
  id: number
  type: string
  title: string
  description: string
  url?: string
}

interface SearchHistory {
  keyword: string
  count: number
  last_search_at: string
}

interface HotKeyword {
  keyword: string
  count: number
}

const props = defineProps({
  placeholder: {
    type: String,
    default: '搜索页面、模板、用户...'
  },
  showQuickResults: {
    type: Boolean,
    default: true
  },
  maxSuggestions: {
    type: Number,
    default: 8
  }
})

const emit = defineEmits(['search', 'select'])

const router = useRouter()

// 响应式数据
const searchInput = ref<HTMLInputElement>()
const searchKeyword = ref('')
const isSearchActive = ref(false)
const showSuggestions = ref(false)
const selectedIndex = ref(-1)

const suggestions = ref<string[]>([])
const searchHistory = ref<SearchHistory[]>([])
const hotKeywords = ref<HotKeyword[]>([])
const quickResults = ref<SearchResult[]>([])

const loading = ref(false)

// 计算属性
const showQuickResults = computed(() => {
  return props.showQuickResults && searchKeyword.value.length >= 2
})

// 防抖搜索建议
const debouncedGetSuggestions = debounce(async (keyword: string) => {
  if (!keyword.trim()) {
    suggestions.value = []
    quickResults.value = []
    return
  }

  try {
    loading.value = true
    
    // 获取搜索建议
    const suggestionsResponse = await $fetch('/api/search/suggest', {
      params: { keyword, limit: props.maxSuggestions }
    })
    
    if (suggestionsResponse.code === 200) {
      suggestions.value = suggestionsResponse.data
    }
    
    // 获取快速搜索结果
    if (showQuickResults.value) {
      const quickResponse = await $fetch('/api/search/search', {
        params: { keyword, per_page: 5 }
      })
      
      if (quickResponse.code === 200) {
        quickResults.value = quickResponse.data.data
      }
    }
    
  } catch (error) {
    console.error('获取搜索建议失败:', error)
  } finally {
    loading.value = false
  }
}, 300)

// 事件处理
const onInput = () => {
  selectedIndex.value = -1
  debouncedGetSuggestions(searchKeyword.value)
}

const onFocus = async () => {
  isSearchActive.value = true
  showSuggestions.value = true
  
  // 加载搜索历史和热门关键词
  if (!searchKeyword.value) {
    await loadSearchData()
  }
}

const onBlur = () => {
  // 延迟隐藏，允许点击建议项
  setTimeout(() => {
    isSearchActive.value = false
    showSuggestions.value = false
  }, 200)
}

const onSearch = () => {
  const keyword = searchKeyword.value.trim()
  if (!keyword) return
  
  // 记录搜索历史
  recordSearch(keyword)
  
  // 触发搜索事件
  emit('search', keyword)
  
  // 跳转到搜索结果页
  router.push(`/search?q=${encodeURIComponent(keyword)}`)
  
  // 隐藏建议
  showSuggestions.value = false
  searchInput.value?.blur()
}

const onClear = () => {
  searchKeyword.value = ''
  suggestions.value = []
  quickResults.value = []
  selectedIndex.value = -1
  searchInput.value?.focus()
}

const onArrowUp = (event: KeyboardEvent) => {
  event.preventDefault()
  const totalItems = suggestions.value.length + searchHistory.value.length
  if (totalItems === 0) return
  
  selectedIndex.value = selectedIndex.value <= 0 
    ? totalItems - 1 
    : selectedIndex.value - 1
}

const onArrowDown = (event: KeyboardEvent) => {
  event.preventDefault()
  const totalItems = suggestions.value.length + searchHistory.value.length
  if (totalItems === 0) return
  
  selectedIndex.value = selectedIndex.value >= totalItems - 1 
    ? 0 
    : selectedIndex.value + 1
}

const onEscape = () => {
  showSuggestions.value = false
  searchInput.value?.blur()
}

const onSelectSuggestion = (suggestion: string) => {
  searchKeyword.value = suggestion
  onSearch()
}

const onSelectResult = (result: SearchResult) => {
  emit('select', result)
  
  if (result.url) {
    router.push(result.url)
  } else {
    // 根据类型生成URL
    const url = generateResultUrl(result)
    if (url) {
      router.push(url)
    }
  }
  
  showSuggestions.value = false
}

const onClearHistory = async () => {
  try {
    await $fetch('/api/search/history', { method: 'DELETE' })
    searchHistory.value = []
  } catch (error) {
    console.error('清空搜索历史失败:', error)
  }
}

// 辅助方法
const highlightKeyword = (text: string, keyword: string): string => {
  if (!keyword) return text
  
  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

const getResultTypeLabel = (type: string): string => {
  const labels: Record<string, string> = {
    page: '页面',
    template: '模板',
    user: '用户'
  }
  return labels[type] || type
}

const generateResultUrl = (result: SearchResult): string => {
  switch (result.type) {
    case 'page':
      return `/pages/${result.id}`
    case 'template':
      return `/templates/${result.id}`
    case 'user':
      return `/users/${result.id}`
    default:
      return ''
  }
}

const loadSearchData = async () => {
  try {
    // 加载搜索历史
    const historyResponse = await $fetch('/api/search/history')
    if (historyResponse.code === 200) {
      searchHistory.value = historyResponse.data.slice(0, 5)
    }
    
    // 加载热门关键词
    const hotResponse = await $fetch('/api/search/hot')
    if (hotResponse.code === 200) {
      hotKeywords.value = hotResponse.data.slice(0, 8)
    }
    
  } catch (error) {
    console.error('加载搜索数据失败:', error)
  }
}

const recordSearch = async (keyword: string) => {
  try {
    await $fetch('/api/search/search', {
      method: 'POST',
      body: { keyword }
    })
  } catch (error) {
    console.error('记录搜索失败:', error)
  }
}

// 键盘快捷键
const handleGlobalKeydown = (event: KeyboardEvent) => {
  // Ctrl/Cmd + K 聚焦搜索框
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault()
    searchInput.value?.focus()
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleGlobalKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleGlobalKeydown)
})

// 监听路由变化，清空搜索
watch(() => router.currentRoute.value.path, () => {
  if (!router.currentRoute.value.path.startsWith('/search')) {
    searchKeyword.value = ''
    suggestions.value = []
    quickResults.value = []
  }
})
</script>

<style lang="scss" scoped>
.search-box {
  position: relative;
  width: 100%;
  max-width: 600px;
  
  &.search-active {
    .search-input {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  }
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  height: 48px;
  padding: 0 120px 0 20px;
  border: 2px solid #e2e8f0;
  border-radius: 24px;
  font-size: 16px;
  background: white;
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
  
  &::placeholder {
    color: #a0aec0;
  }
}

.search-btn {
  position: absolute;
  right: 8px;
  width: 40px;
  height: 32px;
  border: none;
  border-radius: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.clear-btn {
  position: absolute;
  right: 56px;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 50%;
  background: #e2e8f0;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background: #cbd5e0;
  }
}

.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 400px;
  overflow-y: auto;
  margin-top: 8px;
}

.suggestion-group {
  padding: 12px 0;
  
  &:not(:last-child) {
    border-bottom: 1px solid #f1f5f9;
  }
}

.suggestion-title {
  padding: 0 16px 8px;
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.clear-history-btn {
  border: none;
  background: none;
  color: #94a3b8;
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
  
  &:hover {
    color: #ef4444;
    background: #fef2f2;
  }
}

.suggestion-item {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover,
  &.suggestion-active {
    background: #f8fafc;
  }
  
  .suggestion-icon {
    width: 16px;
    height: 16px;
    margin-right: 12px;
    color: #94a3b8;
  }
  
  .search-count {
    margin-left: auto;
    font-size: 12px;
    color: #94a3b8;
    background: #f1f5f9;
    padding: 2px 6px;
    border-radius: 10px;
  }
}

.hot-keywords {
  padding: 0 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.hot-keyword {
  padding: 4px 12px;
  background: #f1f5f9;
  border-radius: 16px;
  font-size: 14px;
  color: #475569;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #e2e8f0;
    color: #334155;
  }
}

.quick-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 8px;
}

.quick-results-header {
  padding: 12px 16px;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.view-all-btn {
  color: #667eea;
  text-decoration: none;
  font-size: 12px;
  
  &:hover {
    text-decoration: underline;
  }
}

.quick-results-list {
  max-height: 300px;
  overflow-y: auto;
}

.quick-result-item {
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover {
    background: #f8fafc;
  }
  
  &:not(:last-child) {
    border-bottom: 1px solid #f8fafc;
  }
}

.result-type {
  font-size: 11px;
  color: #667eea;
  background: #eef2ff;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
  margin-bottom: 4px;
}

.result-title {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 2px;
  
  :deep(mark) {
    background: #fef3c7;
    color: #92400e;
    padding: 0 2px;
    border-radius: 2px;
  }
}

.result-description {
  font-size: 12px;
  color: #64748b;
  line-height: 1.4;
  
  :deep(mark) {
    background: #fef3c7;
    color: #92400e;
    padding: 0 2px;
    border-radius: 2px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .search-input {
    height: 44px;
    font-size: 16px; // 防止iOS缩放
  }
  
  .search-suggestions,
  .quick-results {
    left: -16px;
    right: -16px;
  }
}
</style>

/**
 * 区块组件 - 重构版
 * 现代化通用内容区块，支持多种风格和高级自定义
 * 控制台样式写在style.css，前端预览样式写在all.css
 * 优化版本：确保每个功能完美运行
 */

// 预设风格模板 - 扩展版
const sectionStyles = {
    modern: {
        name: '现代简约',
        bgType: 'gradient',
        bgValue: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        titleColor: '#ffffff',
        textColor: '#f7fafc',
        iconColor: '#ffffff',
        titleSize: 36,
        contentSize: 18,
        borderRadius: 16,
        shadow: true,
        shadowIntensity: 0.15,
        animation: 'fadeInUp',
        btnPrimaryColor: '#ffffff',
        btnSecondaryColor: '#e2e8f0'
    },
    business: {
        name: '商务专业',
        bgType: 'solid',
        bgValue: '#2d3748',
        titleColor: '#ffffff',
        textColor: '#e2e8f0',
        iconColor: '#4299e1',
        titleSize: 32,
        contentSize: 16,
        borderRadius: 8,
        shadow: true,
        shadowIntensity: 0.2,
        animation: 'slideInLeft',
        btnPrimaryColor: '#ffffff',
        btnSecondaryColor: '#4299e1'
    },
    creative: {
        name: '创意活力',
        bgType: 'gradient',
        bgValue: 'linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3)',
        titleColor: '#ffffff',
        textColor: '#ffffff',
        iconColor: '#ffffff',
        titleSize: 40,
        contentSize: 18,
        borderRadius: 20,
        shadow: true,
        shadowIntensity: 0.25,
        animation: 'bounceIn',
        btnPrimaryColor: '#2d3748',
        btnSecondaryColor: '#ffffff'
    },
    minimal: {
        name: '极简风格',
        bgType: 'solid',
        bgValue: '#ffffff',
        titleColor: '#2d3748',
        textColor: '#4a5568',
        iconColor: '#667eea',
        titleSize: 28,
        contentSize: 16,
        borderRadius: 0,
        shadow: false,
        shadowIntensity: 0,
        animation: 'fadeIn',
        btnPrimaryColor: '#2d3748',
        btnSecondaryColor: '#667eea'
    },
    tech: {
        name: '科技感',
        bgType: 'gradient',
        bgValue: 'linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%)',
        titleColor: '#00d4ff',
        textColor: '#e2e8f0',
        iconColor: '#00d4ff',
        titleSize: 34,
        contentSize: 17,
        borderRadius: 12,
        shadow: true,
        shadowIntensity: 0.3,
        animation: 'slideInRight',
        btnPrimaryColor: '#00d4ff',
        btnSecondaryColor: '#ffffff'
    },
    nature: {
        name: '自然绿意',
        bgType: 'gradient',
        bgValue: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)',
        titleColor: '#ffffff',
        textColor: '#f0fff4',
        iconColor: '#ffffff',
        titleSize: 35,
        contentSize: 17,
        borderRadius: 15,
        shadow: true,
        shadowIntensity: 0.18,
        animation: 'fadeInUp',
        btnPrimaryColor: '#2d3748',
        btnSecondaryColor: '#ffffff'
    },
    sunset: {
        name: '日落橙',
        bgType: 'gradient',
        bgValue: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        titleColor: '#ffffff',
        textColor: '#fef5e7',
        iconColor: '#ffffff',
        titleSize: 38,
        contentSize: 18,
        borderRadius: 18,
        shadow: true,
        shadowIntensity: 0.2,
        animation: 'zoomIn',
        btnPrimaryColor: '#2d3748',
        btnSecondaryColor: '#ffffff'
    },
    ocean: {
        name: '海洋蓝',
        bgType: 'gradient',
        bgValue: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #74b9ff 100%)',
        titleColor: '#ffffff',
        textColor: '#ddeeff',
        iconColor: '#74b9ff',
        titleSize: 34,
        contentSize: 17,
        borderRadius: 14,
        shadow: true,
        shadowIntensity: 0.22,
        animation: 'slideInLeft',
        btnPrimaryColor: '#74b9ff',
        btnSecondaryColor: '#ffffff'
    },
    elegant: {
        name: '优雅紫',
        bgType: 'gradient',
        bgValue: 'linear-gradient(135deg, #8360c3 0%, #2ebf91 100%)',
        titleColor: '#ffffff',
        textColor: '#f8f9ff',
        iconColor: '#d4b5ff',
        titleSize: 33,
        contentSize: 16,
        borderRadius: 12,
        shadow: true,
        shadowIntensity: 0.18,
        animation: 'fadeIn',
        btnPrimaryColor: '#d4b5ff',
        btnSecondaryColor: '#ffffff'
    },
    warm: {
        name: '温暖金',
        bgType: 'gradient',
        bgValue: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
        titleColor: '#8b4513',
        textColor: '#5d4037',
        iconColor: '#ff8a65',
        titleSize: 32,
        contentSize: 16,
        borderRadius: 10,
        shadow: true,
        shadowIntensity: 0.12,
        animation: 'bounceIn',
        btnPrimaryColor: '#ff8a65',
        btnSecondaryColor: '#8b4513'
    }
};

// 区块组件模板 - 重构版
const sectionComponent = {
    name: '区块',
    html: `<div class="section-component">
        <div class="section-icon">
            <i class="icon-placeholder">✨</i>
        </div>
        <h2 class="section-title">精彩内容标题</h2>
        <p class="section-content">这里是区块内容，可以放置任何文字信息。支持多行文本，让您的内容更加丰富精彩。</p>
        <div class="section-buttons">
            <a href="#" class="section-btn primary">了解更多</a>
            <a href="#" class="section-btn secondary">联系我们</a>
        </div>
    </div>`,
    properties: {
        // 内容设置
        title: '精彩内容标题',
        content: '这里是区块内容，可以放置任何文字信息。支持多行文本，让您的内容更加丰富精彩。',
        showIcon: true,
        iconText: '✨',
        showButtons: true,
        primaryBtnText: '了解更多',
        secondaryBtnText: '联系我们',
        primaryBtnLink: '#',
        secondaryBtnLink: '#',

        // 样式设置
        stylePreset: 'modern',
        bgType: 'gradient', // solid, gradient, image
        bgValue: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        bgImages: [
            'https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-4.0.3&auto=format&fit=crop&w=1974&q=80',
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
            'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2071&q=80',
            'https://images.unsplash.com/photo-1519904981063-b0cf448d479e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80'
        ],
        selectedBgImage: 0,
        titleColor: '#ffffff',
        textColor: '#f7fafc',
        iconColor: '#ffffff',

        // 尺寸设置
        padding: 80,
        titleSize: 36,
        contentSize: 18,
        iconSize: 48,
        maxWidth: 1200,
        minHeight: 300,

        // 布局设置
        contentAlign: 'center', // 内容对齐方式
        textAlign: 'center', // 整体对齐方式（保持兼容性）
        borderRadius: 16, // 背景圆角
        btnBorderRadius: 10, // 按钮圆角
        shadow: false,
        shadowIntensity: 0.15,

        // 动画设置
        animation: 'fadeInUp',
        animationDelay: 0,
        animationDuration: 0.8,

        // 按钮样式 - 从默认快速样式获取
        btnPrimaryColor: sectionStyles.modern.btnPrimaryColor,
        btnSecondaryColor: sectionStyles.modern.btnSecondaryColor,
        // 高级设置
        border: false,
        borderWidth: 2,
        borderColor: '#e2e8f0',

        // 响应式设置
        mobilePadding: 40
    },

    // 生成动态CSS的方法 - 重构版
    generateCSS: function(componentId) {
        const props = this.properties;
        return generateSectionCSS(componentId, props);
    }
};

// 生成区块组件属性面板
function generateSectionProperties(component) {
    const props = sectionComponent.properties;

    // 生成风格预设选项
    let styleOptions = '';
    Object.keys(sectionStyles).forEach(key => {
        const style = sectionStyles[key];
        styleOptions += `<option value="${key}" ${props.stylePreset === key ? 'selected' : ''}>${style.name}</option>`;
    });

    // 生成背景类型选项
    let bgTypeOptions = `
        <option value="solid" ${props.bgType === 'solid' ? 'selected' : ''}>纯色背景</option>
        <option value="gradient" ${props.bgType === 'gradient' ? 'selected' : ''}>渐变背景</option>
        <option value="image" ${props.bgType === 'image' ? 'selected' : ''}>图片背景</option>
    `;

    // 生成动画选项
    let animationOptions = `
        <option value="none" ${props.animation === 'none' ? 'selected' : ''}>无动画</option>
        <option value="fadeIn" ${props.animation === 'fadeIn' ? 'selected' : ''}>淡入</option>
        <option value="fadeInUp" ${props.animation === 'fadeInUp' ? 'selected' : ''}>向上淡入</option>
        <option value="slideInLeft" ${props.animation === 'slideInLeft' ? 'selected' : ''}>左侧滑入</option>
        <option value="slideInRight" ${props.animation === 'slideInRight' ? 'selected' : ''}>右侧滑入</option>
        <option value="bounceIn" ${props.animation === 'bounceIn' ? 'selected' : ''}>弹跳进入</option>
        <option value="zoomIn" ${props.animation === 'zoomIn' ? 'selected' : ''}>缩放进入</option>
    `;

    let html = `
        <!-- 风格预设 -->
        <div class="property-section">
            <h4 class="section-title">风格预设</h4>
            <div class="property-group">
                <label class="property-label">快速样式</label>
                <div class="section-style-presets">
                    ${Object.keys(sectionStyles).map(key => {
                        const style = sectionStyles[key];
                        const isSelected = props.stylePreset === key;
                        return `
                        <div class="section-style-preset ${isSelected ? 'selected' : ''}"
                             style="background: ${style.bgValue}; color: ${style.titleColor};"
                             onclick="applySectionStyle('${component.id}', '${key}')"
                             title="${style.name}">
                            <span>${style.name}</span>
                        </div>
                        `;
                    }).join('')}
                </div>
            </div>
        </div>

        <!-- 内容设置 -->
        <div class="property-section">
            <h4 class="section-title">内容设置</h4>

            <div class="property-group">
                <label class="property-label">标题</label>
                <input type="text" class="property-input" value="${props.title}" placeholder="输入标题文字"
                       onchange="updateSectionProperty('${component.id}', 'title', this.value)"
                       oninput="updateSectionProperty('${component.id}', 'title', this.value)">
            </div>

            <div class="property-group">
                <label class="property-label">内容描述</label>
                <textarea class="property-input" rows="3" placeholder="输入内容描述，支持多行文本"
                          onchange="updateSectionProperty('${component.id}', 'content', this.value)"
                          oninput="updateSectionProperty('${component.id}', 'content', this.value)">${props.content}</textarea>
            </div>

            <div class="property-group">
                <label class="property-label">显示选项</label>
                <div class="section-checkbox-group">
                    <div class="section-checkbox-item">
                        <input type="checkbox" id="showIcon-${component.id}" ${props.showIcon ? 'checked' : ''}
                               onchange="updateSectionProperty('${component.id}', 'showIcon', this.checked)">
                        <label for="showIcon-${component.id}">✨ 显示图标</label>
                    </div>
                    <div class="section-checkbox-item">
                        <input type="checkbox" id="showButtons-${component.id}" ${props.showButtons ? 'checked' : ''}
                               onchange="updateSectionProperty('${component.id}', 'showButtons', this.checked)">
                        <label for="showButtons-${component.id}">🔘 显示按钮</label>
                    </div>

                </div>
            </div>

            ${props.showIcon ? `
                <div class="property-group">
                    <label class="property-label">图标符号</label>
                    <div style="display: flex; gap: 8px; align-items: center;">
                        <input type="text" class="property-input" value="${props.iconText}" placeholder="图标符号"
                               onchange="updateSectionProperty('${component.id}', 'iconText', this.value)" style="flex: 1;">
                        <button onclick="openIconSelector('${component.id}')"
                                style="padding: 8px 12px; background: #667eea; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; white-space: nowrap;">
                            选择图标
                        </button>
                    </div>
                </div>
                
                <div class="property-group">
                    <label class="property-label">图标大小</label>
                    <div class="section-range-control">
                        <input type="range" class="section-range-slider" value="${props.iconSize}" min="20" max="80" step="5"
                               oninput="this.nextElementSibling.textContent = this.value + 'px'; updateSectionProperty('${component.id}', 'iconSize', this.value)">
                        <span class="section-range-value">${props.iconSize}px</span>
                    </div>
                </div>
            ` : ''}

            ${props.showButtons ? `
                <div class="property-group">
                    <label class="property-label">按钮文字</label>
                    <div style="display: flex; gap: 8px; margin-bottom: 8px;">
                        <input type="text" class="property-input" value="${props.primaryBtnText}" placeholder="主按钮文字"
                               onchange="updateSectionProperty('${component.id}', 'primaryBtnText', this.value)" style="flex: 1;">
                        <input type="text" class="property-input" value="${props.secondaryBtnText}" placeholder="副按钮文字"
                               onchange="updateSectionProperty('${component.id}', 'secondaryBtnText', this.value)" style="flex: 1;">
                    </div>
                    <div style="display: flex; gap: 8px;">
                        <input type="text" class="property-input" value="${props.primaryBtnLink}" placeholder="主按钮链接"
                               onchange="updateSectionProperty('${component.id}', 'primaryBtnLink', this.value)" style="flex: 1;">
                        <input type="text" class="property-input" value="${props.secondaryBtnLink}" placeholder="副按钮链接"
                               onchange="updateSectionProperty('${component.id}', 'secondaryBtnLink', this.value)" style="flex: 1;">
                    </div>
                </div>
            ` : ''}
        </div>

        <!-- 背景设置 -->
        <div class="property-section">
            <h4 class="section-title">背景设置</h4>

            <div class="property-group">
                <label class="property-label">背景类型</label>
                <select class="property-input" onchange="updateSectionProperty('${component.id}', 'bgType', this.value)"
                        style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border: 1px solid #dee2e6;">
                    ${bgTypeOptions}
                </select>
            </div>

            <div class="property-group">
                <div id="bg-controls-${component.id}">
                    ${props.bgType === 'solid' ? `
                        <label class="property-label">背景颜色</label>
                        <div style="display: flex; gap: 8px; align-items: center;">
                            <input type="color" id="colorPicker-${component.id}" class="property-input" value="${getSolidColor(props)}"
                                   onchange="updateSectionColorPicker('${component.id}', this.value)"
                                   style="width: 60px; height: 40px; border-radius: 8px; border: 2px solid #dee2e6;">
                            <input type="text" id="colorInput-${component.id}" class="property-input" value="${getSolidColor(props)}" placeholder="#ffffff"
                                   oninput="updateSectionColorInput('${component.id}', this.value)"
                                   pattern="^#[0-9A-Fa-f]{6}$"
                                   style="flex: 1; font-family: monospace; text-transform: uppercase;">
                        </div>
                        <div style="margin-top: 8px;">
                            <small style="color: #6c757d;">💡 请输入6位颜色代码，如：#FF5733</small>
                        </div>
                    ` : props.bgType === 'gradient' ? `
                        <label class="property-label">渐变效果</label>
                        <div class="bg-preview" style="background: ${props.bgValue}; height: 60px; border-radius: 6px; border: 1px solid #e9ecef; margin-bottom: 8px;"></div>
                        <div style="margin-top: 8px;">
                            <small style="color: #6c757d;">💡 当前渐变效果预览</small>
                        </div>
                    ` : `
                        <label class="property-label">背景图片选择</label>
                        <div class="bg-options">
                            ${props.bgImages.map((image, index) => `
                                <div class="bg-option ${props.selectedBgImage === index ? 'selected' : ''}"
                                     style="background: url('${image}') center/cover"
                                     onclick="updateSectionBgImage('${component.id}', ${index})"
                                     title="背景图片 ${index + 1}">
                                </div>
                            `).join('')}
                        </div>
                        <div class="bg-preview" style="background: url('${props.bgImages[props.selectedBgImage]}') center/cover; height: 60px; border-radius: 6px; border: 1px solid #e9ecef;"></div>
                        <div style="margin-top: 8px;">
                            <small style="color: #6c757d;">💡 点击上方图片选择背景，支持高质量图片</small>
                        </div>
                    `}
                </div>
            </div>


        </div>

        <!-- 颜色设置 -->
        <div class="property-section">
            <h4 class="section-title">颜色设置</h4>

            <div class="property-group">
                <div class="section-color-row">
                    <div class="section-color-item">
                        <label>📝 标题色</label>
                        <input type="color" class="property-input" value="${props.titleColor}"
                               onchange="updateSectionProperty('${component.id}', 'titleColor', this.value)">
                    </div>
                    <div class="section-color-item">
                        <label>📄 文字色</label>
                        <input type="color" class="property-input" value="${props.textColor}"
                               onchange="updateSectionProperty('${component.id}', 'textColor', this.value)">
                    </div>
                    <div class="section-color-item">
                        <label>✨ 图标色</label>
                        <input type="color" class="property-input" value="${props.iconColor}"
                               onchange="updateSectionProperty('${component.id}', 'iconColor', this.value)">
                    </div>
                </div>
            </div>

            ${props.showButtons ? `
                <div class="property-group">
                    <label class="property-label">主按钮颜色</label>
                    <div style="display: flex; gap: 8px; align-items: center;">
                        <input type="color" class="property-input" value="${props.btnPrimaryColor}"
                               onchange="updateSectionProperty('${component.id}', 'btnPrimaryColor', this.value)"
                               style="width: 60px; height: 40px; border-radius: 8px; border: 2px solid #dee2e6;">
                        <input type="text" class="property-input" value="${props.btnPrimaryColor}" placeholder="#ffffff"
                               onchange="updateSectionProperty('${component.id}', 'btnPrimaryColor', this.value)"
                               style="flex: 1; font-family: monospace; font-size: 14px;">
                    </div>
                </div>

                <div class="property-group">
                    <label class="property-label">副按钮颜色</label>
                    <div style="display: flex; gap: 8px; align-items: center;">
                        <input type="color" class="property-input" value="${props.btnSecondaryColor}"
                               onchange="updateSectionProperty('${component.id}', 'btnSecondaryColor', this.value)"
                               style="width: 60px; height: 40px; border-radius: 8px; border: 2px solid #dee2e6;">
                        <input type="text" class="property-input" value="${props.btnSecondaryColor}" placeholder="#667eea"
                               onchange="updateSectionProperty('${component.id}', 'btnSecondaryColor', this.value)"
                               style="flex: 1; font-family: monospace; font-size: 14px;">
                    </div>
                </div>
            ` : ''}
        </div>

        <!-- 尺寸设置 -->
        <div class="property-section">
            <h4 class="section-title">尺寸设置</h4>

            <div class="property-group">
                <label class="property-label">内边距</label>
                <div class="section-range-control">
                    <input type="range" class="section-range-slider" value="${props.padding}" min="20" max="200" step="5"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateSectionProperty('${component.id}', 'padding', this.value)">
                    <span class="section-range-value">${props.padding}px</span>
                </div>
            </div>



            <div class="property-group">
                <label class="property-label">最大宽度</label>
                <div class="section-range-control">
                    <input type="range" class="section-range-slider" value="${props.maxWidth}" min="400" max="1400"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateSectionProperty('${component.id}', 'maxWidth', this.value)">
                    <span class="section-range-value">${props.maxWidth}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">标题大小</label>
                <div class="section-range-control">
                    <input type="range" class="section-range-slider" value="${props.titleSize}" min="16" max="72"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateSectionProperty('${component.id}', 'titleSize', this.value)">
                    <span class="section-range-value">${props.titleSize}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">内容大小</label>
                <div class="section-range-control">
                    <input type="range" class="section-range-slider" value="${props.contentSize}" min="12" max="32"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateSectionProperty('${component.id}', 'contentSize', this.value)">
                    <span class="section-range-value">${props.contentSize}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">移动端内边距</label>
                <div class="section-range-control">
                    <input type="range" class="section-range-slider" value="${props.mobilePadding}" min="20" max="100"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateSectionProperty('${component.id}', 'mobilePadding', this.value)">
                    <span class="section-range-value">${props.mobilePadding}px</span>
                </div>
            </div>


        </div>

        <!-- 布局设置 -->
        <div class="property-section">
            <h4 class="section-title">布局设置</h4>



            <div class="property-group">
                <label class="property-label">内容对齐</label>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 6px;">
                    <button class="section-align-btn ${(props.contentAlign || props.textAlign) === 'left' ? 'active' : ''}"
                            onclick="updateSectionProperty('${component.id}', 'contentAlign', 'left')">
                        左对齐
                    </button>
                    <button class="section-align-btn ${(props.contentAlign || props.textAlign) === 'center' ? 'active' : ''}"
                            onclick="updateSectionProperty('${component.id}', 'contentAlign', 'center')">
                        居中
                    </button>
                    <button class="section-align-btn ${(props.contentAlign || props.textAlign) === 'right' ? 'active' : ''}"
                            onclick="updateSectionProperty('${component.id}', 'contentAlign', 'right')">
                        右对齐
                    </button>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">背景圆角</label>
                <div class="section-range-control">
                    <input type="range" class="section-range-slider" value="${props.borderRadius}" min="0" max="50"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateSectionProperty('${component.id}', 'borderRadius', this.value)">
                    <span class="section-range-value">${props.borderRadius}px</span>
                </div>
            </div>

            ${props.showButtons ? `
                <div class="property-group">
                    <label class="property-label">按钮圆角</label>
                    <div class="section-range-control">
                        <input type="range" class="section-range-slider" value="${props.btnBorderRadius !== undefined ? props.btnBorderRadius : 10}" min="0" max="30" step="5"
                               oninput="this.nextElementSibling.textContent = this.value + 'px'; updateSectionProperty('${component.id}', 'btnBorderRadius', this.value)">
                        <span class="section-range-value">${props.btnBorderRadius !== undefined ? props.btnBorderRadius : 10}px</span>
                    </div>
                </div>
            ` : ''}
        </div>

        <!-- 效果设置 -->
        <div class="property-section">
            <h4 class="section-title">效果设置</h4>

            <div class="property-group">
                <label class="property-label">视觉效果</label>
                <div class="checkbox-group" style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                    <div class="checkbox-item" style="display: flex; align-items: center; gap: 8px;">
                        <input type="checkbox" id="shadow-${component.id}" ${props.shadow ? 'checked' : ''}
                               onchange="updateSectionProperty('${component.id}', 'shadow', this.checked)">
                        <label for="shadow-${component.id}" style="cursor: pointer; font-size: 14px;">阴影效果</label>
                    </div>
                    <div class="checkbox-item" style="display: flex; align-items: center; gap: 8px;">
                        <input type="checkbox" id="border-${component.id}" ${props.border ? 'checked' : ''}
                               onchange="updateSectionProperty('${component.id}', 'border', this.checked)">
                        <label for="border-${component.id}" style="cursor: pointer; font-size: 14px;">边框效果</label>
                    </div>
                </div>
            </div>

            ${props.shadow ? `
                <div class="property-group">
                    <label class="property-label">阴影强度</label>
                    <div class="section-range-control">
                        <input type="range" class="section-range-slider" value="${props.shadowIntensity || 0.15}" min="0" max="0.5" step="0.05"
                               oninput="this.nextElementSibling.textContent = Math.round(this.value * 100) + '%'; updateSectionProperty('${component.id}', 'shadowIntensity', this.value)">
                        <span class="section-range-value">${Math.round((props.shadowIntensity || 0.15) * 100)}%</span>
                    </div>
                </div>
            ` : ''}

            ${props.border ? `
                <div class="property-group">
                    <label class="property-label">边框宽度</label>
                    <div class="section-range-control">
                        <input type="range" class="section-range-slider" value="${props.borderWidth !== undefined ? props.borderWidth : 2}" min="0" max="50" step="5"
                               oninput="this.nextElementSibling.textContent = this.value + 'px'; updateSectionProperty('${component.id}', 'borderWidth', this.value)">
                        <span class="section-range-value">${props.borderWidth !== undefined ? props.borderWidth : 2}px</span>
                    </div>
                </div>

                <div class="property-group">
                    <label class="property-label">边框颜色</label>
                    <div style="display: flex; gap: 8px; align-items: center;">
                        <input type="color" class="property-input" value="${props.borderColor}"
                               onchange="updateSectionProperty('${component.id}', 'borderColor', this.value)"
                               style="width: 60px; height: 40px; border-radius: 8px; border: 2px solid #dee2e6;">
                        <input type="text" class="property-input" value="${props.borderColor}" placeholder="#e2e8f0"
                               onchange="updateSectionProperty('${component.id}', 'borderColor', this.value)"
                               style="flex: 1; font-family: monospace; font-size: 14px;">
                    </div>
                </div>
            ` : ''}

            <div class="property-group">
                <label class="property-label">入场动画</label>
                <select class="property-input" onchange="updateSectionProperty('${component.id}', 'animation', this.value)"
                        style="width: 100%; padding: 10px 12px; font-size: 14px;">
                    ${animationOptions}
                </select>
            </div>

            <div class="property-group">
                <label class="property-label">动画时长</label>
                <div class="section-range-control">
                    <input type="range" class="section-range-slider" value="${props.animationDuration}" min="0.3" max="2" step="0.1"
                           oninput="this.nextElementSibling.textContent = this.value + 's'; updateSectionProperty('${component.id}', 'animationDuration', this.value)">
                    <span class="section-range-value">${props.animationDuration}s</span>
                </div>
            </div>

            ${props.animation !== 'none' ? `
                <div class="property-group">
                    <label class="property-label">延迟时间</label>
                    <div class="section-range-control">
                        <input type="range" class="section-range-slider" value="${props.animationDelay}" min="0" max="2" step="0.1"
                               oninput="this.nextElementSibling.textContent = this.value + 's'; updateSectionProperty('${component.id}', 'animationDelay', this.value)">
                        <span class="section-range-value">${props.animationDelay}s</span>
                    </div>
                </div>
            ` : ''}
        </div>

        <!-- 使用提示 -->
        <div class="section-usage-tips">
            <h4>使用提示</h4>
            <div>
                <p>• 使用风格预设快速应用主题样式</p>

                <p>• 调整响应式设置优化移动端显示</p>
                <p>• 合理使用动画提升页面活力</p>
            </div>
        </div>
    `;

    return html;
}

// 应用风格预设 - 优化版
function applySectionStyle(componentId, styleKey) {
    const component = document.getElementById(componentId);
    const style = sectionStyles[styleKey];
    const props = sectionComponent.properties;

    if (style && component) {
        // 应用预设样式 - 包含更多属性
        Object.assign(props, {
            stylePreset: styleKey,
            bgType: style.bgType,
            bgValue: style.bgValue,
            titleColor: style.titleColor,
            textColor: style.textColor,
            iconColor: style.iconColor,
            titleSize: style.titleSize,
            contentSize: style.contentSize,
            borderRadius: style.borderRadius,
            shadow: style.shadow,
            shadowIntensity: style.shadowIntensity,
            animation: style.animation,
            btnPrimaryColor: style.btnPrimaryColor,
            btnSecondaryColor: style.btnSecondaryColor
        });

        // 使用优化的更新方法
        updateSectionDisplay(component, props);

        // 更新动态CSS
        updateSectionCSS(componentId, props);

        // 重新生成属性面板以反映变化
        updatePropertiesPanel(component);
    }
}

// 智能更新属性面板 - 避免不必要的重新生成
function updatePropertiesPanel(component) {
    const propertiesPanel = document.getElementById('properties-panel');
    if (propertiesPanel && propertiesPanel.dataset.componentId === component.id) {
        propertiesPanel.innerHTML = generateSectionProperties(component);
    }
}

// 更新特定控件状态 - 新增函数
function updateControlStates(componentId, property, value) {
    
    // 更新内容对齐按钮状态
    if (property === 'contentAlign') {
        // 查找所有内容对齐按钮
        const alignButtons = document.querySelectorAll('.section-align-btn[onclick*="contentAlign"]');
        alignButtons.forEach(btn => {
            btn.classList.remove('active');
            // 检查按钮的onclick属性是否包含当前值
            const onclickStr = btn.getAttribute('onclick');
            if (onclickStr && onclickStr.includes(`'${value}'`)) {
                btn.classList.add('active');
            }
        });
    }
    
    // 更新滑块显示值
    if (['padding', 'maxWidth', 'titleSize', 'contentSize', 'iconSize', 'borderRadius', 'btnBorderRadius', 'mobilePadding', 'borderWidth'].includes(property)) {
        // 查找对应的滑块和显示值
        const sliders = document.querySelectorAll(`input[type="range"]`);
        sliders.forEach(slider => {
            const onchangeStr = slider.getAttribute('onchange') || '';
            const oninputStr = slider.getAttribute('oninput') || '';
            
            // 检查是否是目标属性的滑块
            if (onchangeStr.includes(property) || oninputStr.includes(property)) {
                slider.value = value;
                
                // 更新显示值
                const valueDisplay = slider.nextElementSibling;
                if (valueDisplay) {
                    if (valueDisplay.classList.contains('section-range-value') || 
                        valueDisplay.classList.contains('range-value')) {
                        if (['shadowIntensity', 'animationDuration', 'animationDelay'].includes(property)) {
                            // 特殊格式的属性
                            if (property === 'shadowIntensity') {
                                valueDisplay.textContent = Math.round(value * 100) + '%';
                            } else {
                                valueDisplay.textContent = value + 's';
                            }
                        } else {
                            // 默认px单位
                            valueDisplay.textContent = value + 'px';
                        }
                    }
                }
            }
        });
    }
    
    // 更新特殊格式的滑块
    if (['shadowIntensity', 'animationDuration', 'animationDelay'].includes(property)) {
        const sliders = document.querySelectorAll(`input[type="range"]`);
        sliders.forEach(slider => {
            const onchangeStr = slider.getAttribute('onchange') || '';
            const oninputStr = slider.getAttribute('oninput') || '';
            
            if (onchangeStr.includes(property) || oninputStr.includes(property)) {
                slider.value = value;
                const valueDisplay = slider.nextElementSibling;
                if (valueDisplay) {
                    if (property === 'shadowIntensity') {
                        valueDisplay.textContent = Math.round(value * 100) + '%';
                    } else {
                        valueDisplay.textContent = value + 's';
                    }
                }
            }
        });
    }
}

// 更新区块动态CSS
function updateSectionCSS(componentId, props) {
    // 移除旧的样式
    const oldStyle = document.getElementById(`section-style-${componentId}`);
    if (oldStyle) {
        oldStyle.remove();
    }

    // 创建新的样式
    const style = document.createElement('style');
    style.id = `section-style-${componentId}`;
    style.textContent = generateSectionCSS(componentId, props);
    document.head.appendChild(style);

    // 添加按钮悬停效果
    updateSectionButtonStyles(componentId, props);
}

// 生成Section组件CSS - 修复版
function generateSectionCSS(componentId, props) {
    // 处理背景样式 - 修复版
    let backgroundStyle = '';
    if (props.bgType === 'image') {
        // 优先使用bgImages数组中的图片
        const imageUrl = props.bgImages && props.bgImages[props.selectedBgImage]
            ? props.bgImages[props.selectedBgImage]
            : props.bgValue;
        backgroundStyle = `url(${imageUrl})`;
    } else {
        backgroundStyle = props.bgValue;
    }

    // 处理可选样式
    let shadowStyle = '';
    if (props.shadow) {
        shadowStyle = `box-shadow: 0 10px 40px rgba(0,0,0,${props.shadowIntensity || 0.15});`;
    }

    let borderStyle = '';
    if (props.border) {
        borderStyle = `border: ${props.borderWidth !== undefined ? props.borderWidth : 1}px solid ${props.borderColor || '#e2e8f0'};`;
    }

    let animationStyle = '';
    if (props.animation && props.animation !== 'none') {
        animationStyle = `animation: ${props.animation} ${props.animationDuration || 1}s ease-out ${props.animationDelay || 0}s both;`;
    }

    return `
        #${componentId} .section-component {
            background: ${backgroundStyle};
            background-size: ${props.bgType === 'image' ? 'cover' : 'auto'};
            background-position: ${props.bgType === 'image' ? 'center' : 'initial'};
            background-repeat: ${props.bgType === 'image' ? 'no-repeat' : 'initial'};
            color: ${props.textColor};
            padding: ${props.padding}px 40px;
            text-align: ${props.textAlign};
            max-width: ${props.maxWidth}px;
            min-height: ${props.minHeight}px;
            margin: 20px auto;
            border-radius: ${props.borderRadius !== undefined ? props.borderRadius : 0}px !important;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            ${shadowStyle}
            ${borderStyle}
            ${animationStyle}
        }

        #${componentId} .section-title {
            color: ${props.titleColor};
            font-size: ${props.titleSize}px;
            margin-bottom: 20px;
            font-weight: 700;
            line-height: 1.2;
            text-align: ${props.textAlign};
        }

        #${componentId} .section-content {
            color: ${props.textColor};
            font-size: ${props.contentSize}px;
            line-height: 1.6;
            margin-bottom: ${props.showButtons ? '30px' : '0'};
            opacity: 0.9;
            text-align: ${props.contentAlign || props.textAlign};
        }

        #${componentId} .section-icon {
            display: ${props.showIcon ? 'block' : 'none'};
            margin-bottom: 20px;
        }

        #${componentId} .section-icon i {
            font-size: ${props.iconSize}px;
            color: ${props.iconColor};
            display: inline-block;
            font-style: normal !important;
            text-align: center;
        }

        #${componentId} .section-buttons {
            display: ${props.showButtons ? 'flex' : 'none'};
            gap: 15px;
            justify-content: ${props.textAlign === 'center' ? 'center' : props.textAlign === 'right' ? 'flex-end' : 'flex-start'};
            flex-wrap: wrap;
        }

        @media (max-width: 768px) {
            #${componentId} .section-component {
                padding: ${props.mobilePadding}px 20px;
            }

            #${componentId} .section-title {
                font-size: ${Math.max(props.titleSize - 8, 20)}px;
            }

            #${componentId} .section-content {
                font-size: ${Math.max(props.contentSize - 2, 14)}px;
            }

            #${componentId} .section-buttons {
                justify-content: center;
                flex-direction: column;
                align-items: center;
            }
        }
    `;
}

// 更新按钮悬停样式 - 简化版（只处理光柱效果）
function updateSectionButtonStyles(componentId, props) {
    // 移除旧的按钮样式
    const oldBtnStyle = document.getElementById(`section-btn-style-${componentId}`);
    if (oldBtnStyle) {
        oldBtnStyle.remove();
    }

    if (!props.showButtons) return;

    // 创建新的按钮样式（只处理悬停效果）
    const style = document.createElement('style');
    style.id = `section-btn-style-${componentId}`;

    // 只添加悬停效果
    let buttonCSS = `
        #${componentId} .section-btn:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
            filter: brightness(1.05) !important;
        }
    `;

    style.textContent = buttonCSS;
    document.head.appendChild(style);
}



// 辅助函数：根据背景色自动计算对比色
function getContrastColor(hexColor) {
    // 移除 # 号
    const color = hexColor.replace('#', '');

    // 转换为 RGB
    const r = parseInt(color.substr(0, 2), 16);
    const g = parseInt(color.substr(2, 2), 16);
    const b = parseInt(color.substr(4, 2), 16);

    // 计算亮度 (使用 YIQ 公式)
    const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;

    // 根据亮度返回黑色或白色
    return (yiq >= 128) ? '#2d3748' : '#ffffff';
}

// 更新区块属性 - 优化版
function updateSectionProperty(componentId, property, value) {
    const component = document.getElementById(componentId);
    const props = sectionComponent.properties;

    if (!component) {
        return;
    }

    // 类型转换和验证
    if (typeof value === 'boolean') {
        props[property] = value;
    } else if (['padding', 'titleSize', 'contentSize', 'maxWidth', 'borderRadius', 'btnBorderRadius', 'iconSize', 'minHeight', 'borderWidth', 'animationDelay', 'mobilePadding'].includes(property)) {
        props[property] = Math.max(parseInt(value) || 0, 0);
    } else if (['overlayOpacity', 'shadowIntensity', 'animationDuration'].includes(property)) {
        props[property] = Math.max(parseFloat(value) || 0, 0);
    } else {
        props[property] = value;
    }

    // 智能更新策略 - 根据属性类型选择更新方式
    if (property === 'bgType') {
        // 背景类型切换时，重置背景值避免参数污染
        if (value === 'solid') {
            props.bgValue = '#ffffff'; // 默认白色
        } else if (value === 'gradient') {
            props.bgValue = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'; // 默认渐变
        } else if (value === 'image') {
            props.bgValue = props.bgImages[props.selectedBgImage || 0]; // 默认第一张图片
        }

        updateBackgroundControls(componentId, value, props);
        updateSectionCSS(componentId, props);
    } else if (['bgValue', 'bgImage'].includes(property)) {
        // 背景值变化时，需要同时更新DOM和CSS
        updateSectionBackground(component, props);
        updateSectionCSS(componentId, props);
    } else if (['showIcon', 'iconText', 'iconSize', 'iconColor'].includes(property)) {
        updateSectionIcon(component, props);
        updateSectionCSS(componentId, props);
    } else if (['showButtons', 'primaryBtnText', 'secondaryBtnText', 'primaryBtnLink', 'secondaryBtnLink', 'btnPrimaryColor', 'btnSecondaryColor', 'btnBorderRadius'].includes(property)) {
        updateSectionButtons(component, props);
        updateSectionButtonStyles(componentId, props);
    } else if (['title', 'titleColor', 'titleSize'].includes(property)) {
        updateSectionTitle(component, props);
        updateSectionCSS(componentId, props);
    } else if (['content', 'textColor', 'contentSize', 'contentAlign'].includes(property)) {
        updateSectionContent(component, props);
        updateSectionCSS(componentId, props);
    } else if (['shadow', 'shadowIntensity', 'border', 'borderWidth', 'borderColor', 'borderRadius', 'btnBorderRadius', 'padding', 'maxWidth', 'mobilePadding'].includes(property)) {
        // 对于这些属性，需要同时更新CSS和基础样式
        updateSectionCSS(componentId, props);
        updateSectionBasicStyles(component, props);
    } else if (['animation', 'animationDelay', 'animationDuration'].includes(property)) {
        updateSectionAnimation(component, props);
        updateSectionCSS(componentId, props);
    } else {
        // 全量更新
        updateSectionDisplay(component, props);
        updateSectionCSS(componentId, props);
    }

                // 强制重绘组件以确保样式生效
    const contentDiv = component.querySelector('.section-component');
    if (contentDiv) {
        // 使用更温和的方式触发重绘
        const currentTransform = contentDiv.style.transform;
        contentDiv.style.transform = 'translateZ(0)';
        requestAnimationFrame(() => {
            contentDiv.style.transform = currentTransform;
        });
    }

    // 更新控件状态
    updateControlStates(componentId, property, value);
    
    // 特定属性需要重新生成属性面板
    if (['bgType', 'showIcon', 'showButtons', 'stylePreset', 'shadow', 'border'].includes(property)) {
        updatePropertiesPanel(component);
    }
}

// 更新背景控件
function updateBackgroundControls(componentId, bgType, props) {
    const bgControls = document.getElementById(`bg-controls-${componentId}`);
    if (!bgControls) return;

    if (bgType === 'solid') {
        // 切换到纯色背景时，重置为默认白色（不传递其他类型的参数）
        const solidColor = getSolidColor(props);
        if (props.bgValue !== solidColor) {
            props.bgValue = solidColor;
        }

        bgControls.innerHTML = `
            <label class="property-label">背景颜色</label>
            <div style="display: flex; gap: 8px; align-items: center;">
                <input type="color" id="colorPicker-${componentId}" class="property-input" value="${solidColor}"
                       onchange="updateSectionColorPicker('${componentId}', this.value)"
                       style="width: 60px; height: 40px; border-radius: 8px; border: 2px solid #dee2e6;">
                <input type="text" id="colorInput-${componentId}" class="property-input" value="${solidColor}" placeholder="#ffffff"
                       oninput="updateSectionColorInput('${componentId}', this.value)"
                       pattern="^#[0-9A-Fa-f]{6}$"
                       style="flex: 1; font-family: monospace; text-transform: uppercase;">
            </div>
            <div style="margin-top: 8px;">
                <small style="color: #6c757d;">💡 请输入6位颜色代码，如：#FF5733</small>
            </div>
        `;
    } else if (bgType === 'gradient') {
        // 切换到渐变背景时，使用默认渐变（不传递其他类型的参数）
        if (!props.bgValue || !props.bgValue.includes('gradient')) {
            props.bgValue = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        }

        bgControls.innerHTML = `
            <label class="property-label">渐变效果</label>
            <div class="bg-preview" style="background: ${props.bgValue}; height: 60px; border-radius: 6px; border: 1px solid #e9ecef; margin-bottom: 8px;"></div>
            <div style="margin-top: 8px;">
                <small style="color: #6c757d;">💡 当前渐变效果预览</small>
            </div>
        `;
    } else {
        bgControls.innerHTML = `
            <label class="property-label">背景图片选择</label>
            <div class="bg-options">
                ${props.bgImages.map((image, index) => `
                    <div class="bg-option ${props.selectedBgImage === index ? 'selected' : ''}"
                         style="background: url('${image}') center/cover"
                         onclick="updateSectionBgImage('${componentId}', ${index})"
                         title="背景图片 ${index + 1}">
                    </div>
                `).join('')}
            </div>
            <div class="bg-preview" style="background: url('${props.bgImages[props.selectedBgImage]}') center/cover; height: 60px; border-radius: 6px; border: 1px solid #e9ecef;"></div>
            <div style="margin-top: 8px;">
                <small style="color: #6c757d;">💡 点击上方图片选择背景，支持高质量图片</small>
            </div>
        `;
    }
}

// 获取纯色背景颜色（过滤掉渐变和图片的值）
function getSolidColor(props) {
    // 如果当前是纯色背景且是有效的颜色代码，返回该值
    if (props.bgType === 'solid' && props.bgValue && props.bgValue.match(/^#[0-9A-Fa-f]{6}$/)) {
        return props.bgValue;
    }
    // 否则返回默认白色
    return '#ffffff';
}

// 更新颜色选择器
function updateSectionColorPicker(componentId, color) {
    const colorInput = document.getElementById(`colorInput-${componentId}`);
    if (colorInput) {
        colorInput.value = color.toUpperCase();
    }
    updateSectionProperty(componentId, 'bgValue', color);
}

// 更新颜色输入框
function updateSectionColorInput(componentId, color) {
    // 验证颜色代码格式
    const colorRegex = /^#[0-9A-Fa-f]{6}$/;
    const input = document.getElementById(`colorInput-${componentId}`);
    const colorPicker = document.getElementById(`colorPicker-${componentId}`);

    if (input) {
        // 转换为大写
        const upperColor = color.toUpperCase();
        if (input.value !== upperColor) {
            input.value = upperColor;
        }

        // 如果是有效的颜色代码，同步到颜色选择器和背景
        if (colorRegex.test(upperColor)) {
            if (colorPicker) {
                colorPicker.value = upperColor;
            }
            updateSectionProperty(componentId, 'bgValue', upperColor);
            input.style.borderColor = '#28a745'; // 绿色边框表示有效
        } else {
            input.style.borderColor = '#dc3545'; // 红色边框表示无效
        }
    }
}

// 更新section背景图片
function updateSectionBgImage(componentId, imageIndex) {
    const component = document.getElementById(componentId);
    const props = sectionComponent.properties;

    if (!component) return;

    // 更新选中的背景图片索引
    props.selectedBgImage = imageIndex;

    // 确保背景类型为图片
    props.bgType = 'image';

    // 更新背景值为选中的图片
    props.bgValue = props.bgImages[imageIndex];

    // 更新显示
    updateSectionDisplay(component, props);
    updateSectionCSS(componentId, props);

    // 更新属性面板中的选中状态
    updatePropertiesPanel(component);
}

// 模块化更新函数 - 只更新背景（修复版）
function updateSectionBackground(component, props) {
    const contentDiv = component.querySelector('.section-component');
    if (!contentDiv) return;

    // 清除所有背景相关样式，避免冲突
    contentDiv.style.background = '';
    contentDiv.style.backgroundImage = '';
    contentDiv.style.backgroundColor = '';
    contentDiv.style.backgroundSize = '';
    contentDiv.style.backgroundPosition = '';
    contentDiv.style.backgroundRepeat = '';

    // 根据背景类型设置样式
    if (props.bgType === 'image') {
        // 图片背景 - 优先使用bgImages数组中的图片
        const imageUrl = props.bgImages && props.bgImages[props.selectedBgImage]
            ? props.bgImages[props.selectedBgImage]
            : props.bgValue;
        contentDiv.style.backgroundImage = `url(${imageUrl})`;
        contentDiv.style.backgroundSize = 'cover';
        contentDiv.style.backgroundPosition = 'center';
        contentDiv.style.backgroundRepeat = 'no-repeat';
        contentDiv.style.backgroundColor = 'transparent';
    } else if (props.bgType === 'gradient' || props.bgType === 'solid') {
        // 渐变或纯色背景
        contentDiv.style.background = props.bgValue;
    } else {
        // 无背景
        contentDiv.style.background = 'transparent';
    }
}

// 模块化更新函数 - 只更新图标
function updateSectionIcon(component, props) {
    const icon = component.querySelector('.section-icon');
    if (!icon) return;

    icon.style.display = props.showIcon ? 'block' : 'none';
    const iconElement = icon.querySelector('i');
    if (iconElement) {
        iconElement.textContent = props.iconText;
        iconElement.style.fontSize = `${props.iconSize}px`;
        iconElement.style.color = props.iconColor;
    }
}

// 模块化更新函数 - 只更新标题
function updateSectionTitle(component, props) {
    const title = component.querySelector('.section-title');
    if (!title) return;

    title.textContent = props.title;
    title.style.color = props.titleColor;
    title.style.fontSize = `${props.titleSize}px`;
    title.style.textAlign = props.textAlign;
}

// 模块化更新函数 - 只更新内容
function updateSectionContent(component, props) {
    const content = component.querySelector('.section-content');
    if (!content) return;

    content.textContent = props.content;
    content.style.fontSize = `${props.contentSize}px`;
    content.style.marginBottom = props.showButtons ? '30px' : '0';
    content.style.textAlign = props.contentAlign || props.textAlign;
}

// 模块化更新函数 - 只更新按钮（借鉴英雄区方式）
function updateSectionButtons(component, props) {
    const buttons = component.querySelector('.section-buttons');
    if (!buttons) return;

    buttons.style.display = props.showButtons ? 'flex' : 'none';

    if (props.showButtons) {
        // 直接重新生成按钮HTML，包含内联样式
        buttons.innerHTML = `
            <a href="${props.primaryBtnLink || '#'}" class="section-btn primary"
               target="${props.primaryBtnLink && props.primaryBtnLink !== '#' ? '_blank' : '_self'}"
               style="
                   background: ${props.btnPrimaryColor};
                   color: ${getContrastColor(props.btnPrimaryColor)};
                   border-color: ${props.btnPrimaryColor};
                   border-radius: ${props.btnBorderRadius !== undefined ? props.btnBorderRadius : 10}px;
               ">${props.primaryBtnText}</a>
            <a href="${props.secondaryBtnLink || '#'}" class="section-btn secondary"
               target="${props.secondaryBtnLink && props.secondaryBtnLink !== '#' ? '_blank' : '_self'}"
               style="
                   background: transparent;
                   color: ${props.btnSecondaryColor};
                   border-color: ${props.btnSecondaryColor};
                   border-radius: ${props.btnBorderRadius !== undefined ? props.btnBorderRadius : 10}px;
               ">${props.secondaryBtnText}</a>
        `;

        buttons.style.gap = '15px';
        buttons.style.justifyContent = props.textAlign === 'center' ? 'center' :
                                      props.textAlign === 'right' ? 'flex-end' : 'flex-start';
    }
}

// 模块化更新函数 - 只更新动画
function updateSectionAnimation(component, props) {
    const contentDiv = component.querySelector('.section-component');
    if (!contentDiv) return;

    if (props.animation && props.animation !== 'none') {
        contentDiv.style.animation = `${props.animation} ${props.animationDuration}s ease-out ${props.animationDelay}s both`;
    } else {
        contentDiv.style.animation = 'none';
    }
}

// 更新区块显示 - 优化版（全量更新）
function updateSectionDisplay(component, props) {
    if (!component) return;

    // 使用模块化更新函数
    updateSectionBackground(component, props);
    updateSectionTitle(component, props);
    updateSectionContent(component, props);
    updateSectionIcon(component, props);
    updateSectionButtons(component, props);
    updateSectionAnimation(component, props);

    // 更新基础样式
    updateSectionBasicStyles(component, props);
}

// 更新基础样式
function updateSectionBasicStyles(component, props) {
    const contentDiv = component.querySelector('.section-component');
    if (!contentDiv) {
        return;
    }

    // 基础布局样式
    contentDiv.style.color = props.textColor;
    contentDiv.style.padding = `${props.padding}px 40px`;
    contentDiv.style.textAlign = props.textAlign;
    contentDiv.style.maxWidth = `${props.maxWidth}px`;
    contentDiv.style.minHeight = `${props.minHeight}px`;
    contentDiv.style.margin = '20px auto';
    contentDiv.style.borderRadius = `${props.borderRadius}px`;
    contentDiv.style.position = 'relative';
    contentDiv.style.overflow = 'hidden';
    contentDiv.style.transition = 'all 0.3s ease';

    // 阴影效果
    if (props.shadow) {
        const shadowIntensity = props.shadowIntensity || 0.10;
        contentDiv.style.boxShadow = `0 10px 40px rgba(0,0,0,${shadowIntensity})`;
    } else {
        contentDiv.style.boxShadow = 'none';
    }

    // 边框效果
    if (props.border) {
        contentDiv.style.border = `${props.borderWidth}px solid ${props.borderColor}`;
    } else {
        contentDiv.style.border = 'none';
    }

    // 悬停效果已移除
}

// 初始化区块组件 - 新增函数
function initializeSectionComponent(componentId) {
    const component = document.getElementById(componentId);
    if (!component) return;

    const props = sectionComponent.properties;

    // 初始化显示
    updateSectionDisplay(component, props);

    // 初始化CSS
    updateSectionCSS(componentId, props);

    // 确保按钮样式被正确应用
    updateSectionButtonStyles(componentId, props);

    // 添加事件监听器
    addSectionEventListeners();
}

// 添加事件监听器
function addSectionEventListeners() {
    // 预留扩展功能
}

// 清理区块组件资源
function cleanupSectionComponent(componentId) {
    // 移除动态样式
    const styles = document.querySelectorAll(`#section-style-${componentId}, #section-btn-style-${componentId}`);
    styles.forEach(style => style.remove());

    // 移除事件监听器（通过重新克隆节点）
    const component = document.getElementById(componentId);
    if (component) {
        const newComponent = component.cloneNode(true);
        component.parentNode.replaceChild(newComponent, component);
    }
}

// 获取区块组件配置 - 用于导出
function getSectionComponentConfig(componentId) {
    const component = document.getElementById(componentId);
    if (!component) return null;

    return {
        id: componentId,
        type: 'section',
        properties: { ...sectionComponent.properties },
        html: component.innerHTML
    };
}

// 应用区块组件配置 - 用于导入
function applySectionComponentConfig(componentId, config) {
    const component = document.getElementById(componentId);
    if (!component || !config) return;

    // 应用属性
    Object.assign(sectionComponent.properties, config.properties);

    // 更新显示
    updateSectionDisplay(component, sectionComponent.properties);
    updateSectionCSS(componentId, sectionComponent.properties);

    // 重新初始化
    addSectionEventListeners();
}

// Section组件初始化函数 - 重构版
function initSectionComponent() {
    // 确保动画样式已加载
    if (!document.getElementById('section-animations')) {
        const animationStyle = document.createElement('style');
        animationStyle.id = 'section-animations';
        animationStyle.textContent = `
            /* Section组件动画样式 */
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(animationStyle);
    }
}

// 图标库数据
const iconLibrary = {
    symbols: {
        name: '符号图标',
        icons: ['✨', '⭐', '🌟', '💫', '⚡', '🔥', '💥', '💢', '💨', '💦', '💧', '🌈', '☀️', '🌙', '⭐', '🌠', '☁️', '⛅', '🌤️', '🌦️', '🌧️', '⛈️', '🌩️', '❄️', '☃️', '⛄', '🌨️', '💎', '🔮', '🎯']
    },
    objects: {
        name: '物品图标',
        icons: ['📱', '💻', '🖥️', '⌨️', '🖱️', '🖨️', '📷', '📹', '🎥', '📞', '☎️', '📠', '📺', '📻', '🎵', '🎶', '🎤', '🎧', '📢', '📣', '📯', '🔔', '🔕', '📯', '🎺', '📻', '📱', '📲', '☎️', '📞']
    }
};

// 打开图标选择器
function openIconSelector(componentId) {
    // 创建弹窗容器
    const modal = document.createElement('div');
    modal.id = 'icon-selector-modal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
    `;
    
    // 创建弹窗内容
    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
        background: white;
        border-radius: 12px;
        padding: 20px;
        max-width: 600px;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 10px 40px rgba(0,0,0,0.2);
    `;
    
    // 生成图标选择器HTML
    let html = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h3 style="margin: 0; color: #2d3748;">📚 选择图标</h3>
            <button onclick="closeIconSelector()" style="background: #e53e3e; color: white; border: none; border-radius: 6px; padding: 6px 12px; cursor: pointer;">✕ 关闭</button>
        </div>
    `;
    
    // 生成图标分类
    Object.keys(iconLibrary).forEach(categoryKey => {
        const category = iconLibrary[categoryKey];
        html += `
            <div style="margin-bottom: 20px;">
                <h4 style="margin: 0 0 10px 0; color: #4a5568; font-size: 14px;">${category.name}</h4>
                <div style="display: grid; grid-template-columns: repeat(10, 1fr); gap: 8px;">
                    ${category.icons.map(icon => `
                        <button onclick="selectIcon('${componentId}', '${icon}')" 
                                style="padding: 8px; border: 2px solid #e2e8f0; border-radius: 6px; background: white; cursor: pointer; font-size: 20px; transition: all 0.2s ease;"
                                onmouseover="this.style.borderColor='#667eea'; this.style.background='#f7fafc';"
                                onmouseout="this.style.borderColor='#e2e8f0'; this.style.background='white';">
                            ${icon}
                        </button>
                    `).join('')}
                </div>
            </div>
        `;
    });
    
    modalContent.innerHTML = html;
    modal.appendChild(modalContent);
    document.body.appendChild(modal);
    
    // 点击背景关闭弹窗
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeIconSelector();
        }
    });
}

// 选择图标
function selectIcon(componentId, icon) {
    updateSectionProperty(componentId, 'iconText', icon);
    closeIconSelector();
}

// 关闭图标选择器
function closeIconSelector() {
    const modal = document.getElementById('icon-selector-modal');
    if (modal) {
        modal.remove();
    }
}

// 注册组件到组件管理器
if (typeof ComponentManager !== 'undefined') {
    ComponentManager.register('section', sectionComponent, generateSectionProperties, updateSectionDisplay);
}

// 自动初始化
if (typeof window !== 'undefined') {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initSectionComponent);
    } else {
        initSectionComponent();
    }
}

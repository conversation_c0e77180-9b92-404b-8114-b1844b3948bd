<?php
// +----------------------------------------------------------------------
// | API路由配置
// +----------------------------------------------------------------------

use think\facade\Route;

// API路由组
Route::group('api', function () {
    
    // DIY页面相关API
    Route::get('diy-pages', 'app\api\controller\Pages@diyPages');
    Route::get('pages-by-type', 'app\api\controller\Pages@pagesByType');
    Route::get('page-detail', 'app\api\controller\Pages@pageDetail');
    Route::get('page-stats', 'app\api\controller\Pages@pageStats');
    Route::post('clear-cache', 'app\api\controller\Pages@clearCache');

    // DIY组件数据API
    Route::get('pages/getDiyData', 'app\api\controller\Pages@getDiyData');

    // 系统菜单API
    Route::get('sys-menus', 'app\api\controller\Menu@index');

})->allowCrossDomain();

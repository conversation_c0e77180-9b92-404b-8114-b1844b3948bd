/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 标签页状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { RouteLocationNormalized } from 'vue-router'

export interface TagView {
  path: string
  name?: string
  title: string
  fullPath: string
  query?: Record<string, any>
  params?: Record<string, any>
  meta?: Record<string, any>
  affix?: boolean
}

export const useTagsViewStore = defineStore('tagsView', () => {
  // 访问过的视图
  const visitedViews = ref<TagView[]>([])
  
  // 缓存的视图
  const cachedViews = ref<string[]>([])

  // 计算属性
  const visitedViewsCount = computed(() => visitedViews.value.length)
  const cachedViewsCount = computed(() => cachedViews.value.length)

  /**
   * 添加访问过的视图
   */
  const addVisitedView = (route: RouteLocationNormalized) => {
    // 检查是否已存在
    const existingIndex = visitedViews.value.findIndex(v => v.path === route.path)
    
    const view: TagView = {
      path: route.path,
      name: route.name as string,
      title: (route.meta?.title as string) || route.name as string || '未命名页面',
      fullPath: route.fullPath,
      query: route.query,
      params: route.params,
      meta: route.meta,
      affix: route.meta?.affix as boolean
    }

    if (existingIndex !== -1) {
      // 更新现有视图
      visitedViews.value[existingIndex] = view
    } else {
      // 添加新视图
      visitedViews.value.push(view)
    }
  }

  /**
   * 添加缓存视图
   */
  const addCachedView = (route: RouteLocationNormalized) => {
    const name = route.name as string
    if (name && route.meta?.keepAlive && !cachedViews.value.includes(name)) {
      cachedViews.value.push(name)
    }
  }

  /**
   * 删除访问过的视图
   */
  const delVisitedView = (view: TagView): Promise<TagView[]> => {
    return new Promise(resolve => {
      const index = visitedViews.value.findIndex(v => v.path === view.path)
      if (index !== -1) {
        visitedViews.value.splice(index, 1)
      }
      resolve([...visitedViews.value])
    })
  }

  /**
   * 删除缓存视图
   */
  const delCachedView = (view: TagView): Promise<string[]> => {
    return new Promise(resolve => {
      const name = view.name
      if (name) {
        const index = cachedViews.value.indexOf(name)
        if (index !== -1) {
          cachedViews.value.splice(index, 1)
        }
      }
      resolve([...cachedViews.value])
    })
  }

  /**
   * 删除其他访问过的视图
   */
  const delOthersVisitedViews = (view: TagView): Promise<TagView[]> => {
    return new Promise(resolve => {
      visitedViews.value = visitedViews.value.filter(v => {
        return v.affix || v.path === view.path
      })
      resolve([...visitedViews.value])
    })
  }

  /**
   * 删除其他缓存视图
   */
  const delOthersCachedViews = (view: TagView): Promise<string[]> => {
    return new Promise(resolve => {
      const name = view.name
      if (name) {
        cachedViews.value = cachedViews.value.filter(cacheName => cacheName === name)
      } else {
        cachedViews.value = []
      }
      resolve([...cachedViews.value])
    })
  }

  /**
   * 删除所有访问过的视图
   */
  const delAllVisitedViews = (): Promise<TagView[]> => {
    return new Promise(resolve => {
      // 保留固定的标签
      visitedViews.value = visitedViews.value.filter(view => view.affix)
      resolve([...visitedViews.value])
    })
  }

  /**
   * 删除所有缓存视图
   */
  const delAllCachedViews = (): Promise<string[]> => {
    return new Promise(resolve => {
      cachedViews.value = []
      resolve([...cachedViews.value])
    })
  }

  /**
   * 更新访问过的视图
   */
  const updateVisitedView = (view: TagView) => {
    const index = visitedViews.value.findIndex(v => v.path === view.path)
    if (index !== -1) {
      visitedViews.value[index] = { ...visitedViews.value[index], ...view }
    }
  }

  /**
   * 添加视图（同时添加到访问过的和缓存的）
   */
  const addView = (route: RouteLocationNormalized) => {
    addVisitedView(route)
    addCachedView(route)
  }

  /**
   * 删除视图（同时从访问过的和缓存的中删除）
   */
  const delView = (view: TagView): Promise<{ visitedViews: TagView[]; cachedViews: string[] }> => {
    return new Promise(resolve => {
      Promise.all([
        delVisitedView(view),
        delCachedView(view)
      ]).then(([visitedViews, cachedViews]) => {
        resolve({ visitedViews, cachedViews })
      })
    })
  }

  /**
   * 删除其他视图
   */
  const delOthersViews = (view: TagView): Promise<{ visitedViews: TagView[]; cachedViews: string[] }> => {
    return new Promise(resolve => {
      Promise.all([
        delOthersVisitedViews(view),
        delOthersCachedViews(view)
      ]).then(([visitedViews, cachedViews]) => {
        resolve({ visitedViews, cachedViews })
      })
    })
  }

  /**
   * 删除所有视图
   */
  const delAllViews = (): Promise<{ visitedViews: TagView[]; cachedViews: string[] }> => {
    return new Promise(resolve => {
      Promise.all([
        delAllVisitedViews(),
        delAllCachedViews()
      ]).then(([visitedViews, cachedViews]) => {
        resolve({ visitedViews, cachedViews })
      })
    })
  }

  /**
   * 初始化固定标签
   */
  const initAffixTags = (routes: any[]) => {
    const affixTags: TagView[] = []
    
    const findAffixTags = (routes: any[], basePath = '') => {
      routes.forEach(route => {
        if (route.meta?.affix) {
          const tagPath = basePath + route.path
          affixTags.push({
            path: tagPath,
            name: route.name,
            title: route.meta?.title || route.name || '未命名页面',
            fullPath: tagPath,
            meta: route.meta,
            affix: true
          })
        }
        
        if (route.children) {
          findAffixTags(route.children, basePath + route.path)
        }
      })
    }
    
    findAffixTags(routes)
    
    // 添加固定标签到访问过的视图中
    affixTags.forEach(tag => {
      if (!visitedViews.value.find(v => v.path === tag.path)) {
        visitedViews.value.push(tag)
      }
    })
  }

  /**
   * 获取标签索引
   */
  const getTagIndex = (path: string): number => {
    return visitedViews.value.findIndex(v => v.path === path)
  }

  /**
   * 检查是否为活动标签
   */
  const isActiveTag = (route: RouteLocationNormalized, tag: TagView): boolean => {
    return tag.path === route.path
  }

  /**
   * 检查是否为固定标签
   */
  const isAffixTag = (tag: TagView): boolean => {
    return !!tag.affix
  }

  return {
    // 状态
    visitedViews: readonly(visitedViews),
    cachedViews: readonly(cachedViews),
    
    // 计算属性
    visitedViewsCount,
    cachedViewsCount,
    
    // 方法
    addView,
    addVisitedView,
    addCachedView,
    delView,
    delVisitedView,
    delCachedView,
    delOthersViews,
    delOthersVisitedViews,
    delOthersCachedViews,
    delAllViews,
    delAllVisitedViews,
    delAllCachedViews,
    updateVisitedView,
    initAffixTags,
    getTagIndex,
    isActiveTag,
    isAffixTag
  }
})

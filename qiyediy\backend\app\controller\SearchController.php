<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * QiyeDIY企业建站系统 - 搜索控制器
 */

declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\service\SearchService;
use think\Request;
use think\Response;

/**
 * 搜索控制器
 */
class SearchController extends BaseController
{
    protected SearchService $searchService;

    public function __construct()
    {
        parent::__construct();
        $this->searchService = new SearchService();
    }

    /**
     * 全站搜索
     * @param Request $request
     * @return Response
     */
    public function search(Request $request): Response
    {
        try {
            $params = $request->only([
                'keyword',
                'type',
                'page',
                'per_page',
                'sort',
                'order'
            ]);

            // 参数验证
            $this->validate($params, [
                'keyword|搜索关键词' => 'require|length:1,100',
                'type|搜索类型' => 'in:all,page,template,user',
                'page|页码' => 'integer|egt:1',
                'per_page|每页数量' => 'integer|between:1,100',
                'sort|排序字段' => 'in:relevance,created_at,updated_at,views',
                'order|排序方向' => 'in:asc,desc'
            ]);

            $result = $this->searchService->search($params);

            return $this->success('搜索成功', $result);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 搜索建议
     * @param Request $request
     * @return Response
     */
    public function suggest(Request $request): Response
    {
        try {
            $keyword = $request->param('keyword', '');
            $limit = (int)$request->param('limit', 10);

            if (empty($keyword)) {
                return $this->success('获取成功', []);
            }

            $suggestions = $this->searchService->getSuggestions($keyword, $limit);

            return $this->success('获取成功', $suggestions);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 热门搜索
     * @param Request $request
     * @return Response
     */
    public function hot(Request $request): Response
    {
        try {
            $limit = (int)$request->param('limit', 10);
            $period = $request->param('period', 'week'); // day, week, month

            $hotKeywords = $this->searchService->getHotKeywords($limit, $period);

            return $this->success('获取成功', $hotKeywords);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 搜索历史
     * @param Request $request
     * @return Response
     */
    public function history(Request $request): Response
    {
        try {
            $userId = $this->getCurrentUserId();
            $limit = (int)$request->param('limit', 20);

            $history = $this->searchService->getSearchHistory($userId, $limit);

            return $this->success('获取成功', $history);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 清空搜索历史
     * @param Request $request
     * @return Response
     */
    public function clearHistory(Request $request): Response
    {
        try {
            $userId = $this->getCurrentUserId();

            $this->searchService->clearSearchHistory($userId);

            return $this->success('清空成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 搜索统计
     * @param Request $request
     * @return Response
     */
    public function statistics(Request $request): Response
    {
        try {
            $params = $request->only([
                'start_date',
                'end_date',
                'type'
            ]);

            $stats = $this->searchService->getSearchStatistics($params);

            return $this->success('获取成功', $stats);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 高级搜索
     * @param Request $request
     * @return Response
     */
    public function advanced(Request $request): Response
    {
        try {
            $params = $request->only([
                'title',
                'content',
                'author',
                'tags',
                'category',
                'date_from',
                'date_to',
                'status',
                'page',
                'per_page'
            ]);

            $result = $this->searchService->advancedSearch($params);

            return $this->success('搜索成功', $result);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 搜索过滤器选项
     * @return Response
     */
    public function filters(): Response
    {
        try {
            $filters = $this->searchService->getSearchFilters();

            return $this->success('获取成功', $filters);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 保存搜索
     * @param Request $request
     * @return Response
     */
    public function save(Request $request): Response
    {
        try {
            $params = $request->only([
                'name',
                'keyword',
                'filters',
                'description'
            ]);

            // 参数验证
            $this->validate($params, [
                'name|搜索名称' => 'require|length:1,50',
                'keyword|搜索关键词' => 'require|length:1,100'
            ]);

            $userId = $this->getCurrentUserId();
            $params['user_id'] = $userId;

            $result = $this->searchService->saveSearch($params);

            return $this->success('保存成功', $result);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取保存的搜索
     * @param Request $request
     * @return Response
     */
    public function saved(Request $request): Response
    {
        try {
            $userId = $this->getCurrentUserId();
            $page = (int)$request->param('page', 1);
            $perPage = (int)$request->param('per_page', 20);

            $result = $this->searchService->getSavedSearches($userId, $page, $perPage);

            return $this->success('获取成功', $result);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除保存的搜索
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function deleteSaved(Request $request, int $id): Response
    {
        try {
            $userId = $this->getCurrentUserId();

            $this->searchService->deleteSavedSearch($id, $userId);

            return $this->success('删除成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 搜索导出
     * @param Request $request
     * @return Response
     */
    public function export(Request $request): Response
    {
        try {
            $params = $request->only([
                'keyword',
                'type',
                'format',
                'fields'
            ]);

            // 参数验证
            $this->validate($params, [
                'keyword|搜索关键词' => 'require|length:1,100',
                'format|导出格式' => 'in:csv,excel,json',
                'type|搜索类型' => 'in:all,page,template,user'
            ]);

            $result = $this->searchService->exportSearchResults($params);

            return $this->success('导出成功', $result);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 重建搜索索引
     * @param Request $request
     * @return Response
     */
    public function reindex(Request $request): Response
    {
        try {
            $type = $request->param('type', 'all');

            $result = $this->searchService->rebuildIndex($type);

            return $this->success('重建索引成功', $result);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 搜索配置
     * @param Request $request
     * @return Response
     */
    public function config(Request $request): Response
    {
        try {
            if ($request->isPost()) {
                // 更新配置
                $config = $request->param('config', []);
                
                $this->searchService->updateSearchConfig($config);
                
                return $this->success('配置更新成功');
            } else {
                // 获取配置
                $config = $this->searchService->getSearchConfig();
                
                return $this->success('获取成功', $config);
            }

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}

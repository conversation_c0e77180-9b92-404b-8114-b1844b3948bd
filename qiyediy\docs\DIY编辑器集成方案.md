# 🎨 QiyeDIY - 专业DIY编辑器集成方案

**三只鱼网络科技 | 韩总 | 2024-12-19**
**核心技术：GrapesJS + Vue3 + 自定义组件生态**

---

## 📋 编辑器选型分析

### 🔍 主流DIY编辑器对比

| 编辑器 | 优势 | 劣势 | 适用场景 | 推荐指数 |
|--------|------|------|----------|----------|
| **GrapesJS** | 功能完整、可扩展性强、社区活跃 | 学习曲线陡峭 | 企业级应用 | ⭐⭐⭐⭐⭐ |
| **Craft.js** | React生态、组件化设计 | 生态相对较小 | React项目 | ⭐⭐⭐⭐ |
| **Editor.js** | 轻量级、块编辑器 | 功能相对简单 | 内容编辑 | ⭐⭐⭐ |
| **Quill** | 富文本编辑 | 不支持页面布局 | 文档编辑 | ⭐⭐ |
| **TinyMCE** | 功能丰富 | 主要用于富文本 | 内容管理 | ⭐⭐ |

### 🏆 GrapesJS选择理由

**技术优势**：
1. **功能完整** - 拖拽编辑、样式管理、响应式设计、代码编辑
2. **可扩展性强** - 丰富的插件生态，支持自定义组件和插件
3. **社区活跃** - GitHub 19k+ stars，文档完善，问题解决快
4. **Vue集成友好** - 可以很好地集成到Vue项目中
5. **商业级稳定** - 被众多商业项目采用，稳定性有保障

**业务匹配度**：
- ✅ 支持企业级复杂页面设计
- ✅ 响应式设计自动适配
- ✅ 组件化开发模式
- ✅ 可视化样式编辑
- ✅ 代码导出和导入
- ✅ 多设备预览功能

---

## 🏗️ 技术架构设计

### 📐 整体架构图

```
┌─────────────────────────────────────────────────────────────────────┐
│                          DIY编辑器架构                                │
├─────────────────────────────────────────────────────────────────────┤
│                          用户交互层                                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐      │
│  │   工具栏区域     │  │   画布编辑区     │  │   属性面板区     │      │
│  │                 │  │                 │  │                 │      │
│  │ • 组件库        │  │ • 拖拽编辑      │  │ • 属性编辑      │      │
│  │ • 模板库        │  │ • 实时预览      │  │ • 样式编辑      │      │
│  │ • 操作工具      │  │ • 响应式视图    │  │ • 事件绑定      │      │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘      │
├─────────────────────────────────────────────────────────────────────┤
│                          核心引擎层                                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐      │
│  │   GrapesJS核心   │  │   组件管理器     │  │   样式管理器     │      │
│  │                 │  │                 │  │                 │      │
│  │ • DOM操作       │  │ • 组件注册      │  │ • CSS生成       │      │
│  │ • 事件系统      │  │ • 组件渲染      │  │ • 样式编辑      │      │
│  │ • 命令系统      │  │ • 属性管理      │  │ • 响应式处理    │      │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘      │
├─────────────────────────────────────────────────────────────────────┤
│                          扩展插件层                                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐      │
│  │   基础插件       │  │   业务插件       │  │   自定义插件     │      │
│  │                 │  │                 │  │                 │      │
│  │ • 基础组件      │  │ • 企业组件      │  │ • 特殊功能      │      │
│  │ • 表单组件      │  │ • 数据绑定      │  │ • 第三方集成    │      │
│  │ • 导航组件      │  │ • API集成       │  │ • 自定义扩展    │      │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘      │
├─────────────────────────────────────────────────────────────────────┤
│                          数据存储层                                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐      │
│  │   页面数据       │  │   组件数据       │  │   样式数据       │      │
│  │                 │  │                 │  │                 │      │
│  │ • 页面结构      │  │ • 组件配置      │  │ • CSS规则       │      │
│  │ • 元数据        │  │ • 属性值        │  │ • 响应式样式    │      │
│  │ • SEO信息       │  │ • 事件绑定      │  │ • 动画效果      │      │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘      │
└─────────────────────────────────────────────────────────────────────┘
```

### 🔧 核心技术栈

**前端技术**：
- **Vue 3.4** - 组合式API，更好的TypeScript支持
- **TypeScript** - 类型安全，提升开发效率
- **GrapesJS 0.21** - 核心编辑器引擎
- **Element Plus** - UI组件库
- **Pinia** - 状态管理
- **Vite** - 构建工具

**GrapesJS插件生态**：
- `grapesjs-blocks-basic` - 基础组件块
- `grapesjs-plugin-forms` - 表单组件
- `grapesjs-component-countdown` - 倒计时组件
- `grapesjs-plugin-export` - 导出功能
- `grapesjs-tui-image-editor` - 图片编辑器
- `grapesjs-style-gradient` - 渐变样式编辑
- `grapesjs-plugin-ckeditor` - 富文本编辑器集成

---

## 🧩 组件系统设计

### 📦 组件分类体系

#### 1. 基础组件 (Basic Components)
```typescript
const basicComponents = [
  {
    id: 'text',
    name: '文本',
    category: 'basic',
    icon: 'el-icon-edit',
    description: '基础文本组件，支持富文本编辑'
  },
  {
    id: 'image',
    name: '图片',
    category: 'basic',
    icon: 'el-icon-picture',
    description: '图片展示组件，支持多种显示模式'
  },
  {
    id: 'button',
    name: '按钮',
    category: 'basic',
    icon: 'el-icon-mouse',
    description: '交互按钮组件，支持多种样式和事件'
  },
  {
    id: 'video',
    name: '视频',
    category: 'basic',
    icon: 'el-icon-video-play',
    description: '视频播放组件，支持多种视频格式'
  },
  {
    id: 'audio',
    name: '音频',
    category: 'basic',
    icon: 'el-icon-headset',
    description: '音频播放组件，支持音频文件播放'
  }
]
```

#### 2. 布局组件 (Layout Components)
```typescript
const layoutComponents = [
  {
    id: 'container',
    name: '容器',
    category: 'layout',
    icon: 'el-icon-menu',
    description: '布局容器，支持响应式网格系统'
  },
  {
    id: 'row',
    name: '行',
    category: 'layout',
    icon: 'el-icon-minus',
    description: '水平布局行，支持多列排列'
  },
  {
    id: 'column',
    name: '列',
    category: 'layout',
    icon: 'el-icon-more',
    description: '垂直布局列，支持弹性布局'
  },
  {
    id: 'divider',
    name: '分割线',
    category: 'layout',
    icon: 'el-icon-remove',
    description: '内容分割线，支持多种样式'
  },
  {
    id: 'spacer',
    name: '间距',
    category: 'layout',
    icon: 'el-icon-sort',
    description: '空白间距组件，用于调整布局'
  }
]
```

#### 3. 业务组件 (Business Components)
```typescript
const businessComponents = [
  {
    id: 'product-card',
    name: '产品卡片',
    category: 'business',
    icon: 'el-icon-goods',
    description: '产品展示卡片，支持数据绑定'
  },
  {
    id: 'news-list',
    name: '新闻列表',
    category: 'business',
    icon: 'el-icon-document',
    description: '新闻文章列表，支持分页和筛选'
  },
  {
    id: 'case-showcase',
    name: '案例展示',
    category: 'business',
    icon: 'el-icon-trophy',
    description: '客户案例展示，支持多种布局'
  },
  {
    id: 'team-member',
    name: '团队成员',
    category: 'business',
    icon: 'el-icon-user',
    description: '团队成员介绍，支持头像和信息'
  },
  {
    id: 'contact-form',
    name: '联系表单',
    category: 'business',
    icon: 'el-icon-message',
    description: '客户联系表单，支持字段自定义'
  }
]
```

#### 4. 复合组件 (Composite Components)
```typescript
const compositeComponents = [
  {
    id: 'header',
    name: '页面头部',
    category: 'composite',
    icon: 'el-icon-top',
    description: '网站头部导航，包含Logo和菜单'
  },
  {
    id: 'footer',
    name: '页面尾部',
    category: 'composite',
    icon: 'el-icon-bottom',
    description: '网站尾部信息，包含版权和链接'
  },
  {
    id: 'hero-section',
    name: '英雄区块',
    category: 'composite',
    icon: 'el-icon-star-on',
    description: '首页英雄区块，包含标题和CTA'
  },
  {
    id: 'feature-section',
    name: '特色区块',
    category: 'composite',
    icon: 'el-icon-medal',
    description: '特色功能展示区块'
  }
]
```

### 🔧 组件开发规范

#### 1. 组件配置结构
```typescript
interface ComponentConfig {
  // 基础信息
  id: string
  name: string
  category: string
  icon: string
  description: string

  // 组件属性
  props: ComponentProps

  // 样式配置
  styles: StyleConfig

  // 事件配置
  events: EventConfig

  // 数据绑定
  dataBinding?: DataBindingConfig

  // 响应式配置
  responsive: ResponsiveConfig
}

interface ComponentProps {
  [key: string]: {
    type: 'string' | 'number' | 'boolean' | 'array' | 'object'
    default: any
    label: string
    description?: string
    options?: Array<{ label: string; value: any }>
    validation?: ValidationRule[]
  }
}
```

#### 2. 组件注册示例
```typescript
// 文本组件注册
export const registerTextComponent = (editor: any) => {
  editor.DomComponents.addType('text-component', {
    model: {
      defaults: {
        tagName: 'div',
        classes: ['text-component'],
        traits: [
          {
            type: 'text',
            name: 'content',
            label: '文本内容',
            changeProp: 1
          },
          {
            type: 'select',
            name: 'tag',
            label: '标签类型',
            options: [
              { value: 'p', name: '段落' },
              { value: 'h1', name: '标题1' },
              { value: 'h2', name: '标题2' },
              { value: 'h3', name: '标题3' },
              { value: 'span', name: '行内文本' }
            ],
            changeProp: 1
          },
          {
            type: 'slider',
            name: 'fontSize',
            label: '字体大小',
            min: 12,
            max: 72,
            step: 1,
            changeProp: 1
          }
        ],
        content: '请输入文本内容',
        tag: 'p',
        fontSize: 16
      }
    },
    view: {
      onRender() {
        const model = this.model
        const content = model.get('content')
        const tag = model.get('tag')
        const fontSize = model.get('fontSize')

        this.el.innerHTML = `<${tag} style="font-size: ${fontSize}px">${content}</${tag}>`
      }
    }
  })

  // 添加到组件面板
  editor.BlockManager.add('text-component', {
    label: '文本',
    category: '基础组件',
    content: { type: 'text-component' },
    media: '<i class="el-icon-edit"></i>'
  })
}
```

---

## 🎨 编辑器界面设计

### 📱 界面布局结构

```vue
<template>
  <div class="diy-editor">
    <!-- 顶部工具栏 -->
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button @click="undo">撤销</el-button>
          <el-button @click="redo">重做</el-button>
        </el-button-group>
        <el-button-group>
          <el-button @click="preview">预览</el-button>
          <el-button @click="save">保存</el-button>
          <el-button type="primary" @click="publish">发布</el-button>
        </el-button-group>
      </div>

      <div class="toolbar-center">
        <el-radio-group v-model="deviceMode">
          <el-radio-button label="desktop">桌面</el-radio-button>
          <el-radio-button label="tablet">平板</el-radio-button>
          <el-radio-button label="mobile">手机</el-radio-button>
        </el-radio-group>
      </div>

      <div class="toolbar-right">
        <el-button @click="exportCode">导出代码</el-button>
        <el-button @click="importTemplate">导入模板</el-button>
      </div>
    </div>

    <!-- 主要编辑区域 -->
    <div class="editor-main">
      <!-- 左侧组件面板 -->
      <div class="editor-sidebar-left">
        <el-tabs v-model="leftActiveTab">
          <el-tab-pane label="组件" name="components">
            <ComponentPanel />
          </el-tab-pane>
          <el-tab-pane label="模板" name="templates">
            <TemplatePanel />
          </el-tab-pane>
          <el-tab-pane label="页面" name="pages">
            <PagePanel />
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 中间画布区域 -->
      <div class="editor-canvas">
        <div class="canvas-container" :class="deviceClass">
          <div id="gjs-editor"></div>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="editor-sidebar-right">
        <el-tabs v-model="rightActiveTab">
          <el-tab-pane label="属性" name="traits">
            <TraitsPanel />
          </el-tab-pane>
          <el-tab-pane label="样式" name="styles">
            <StylesPanel />
          </el-tab-pane>
          <el-tab-pane label="图层" name="layers">
            <LayersPanel />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>
```

### 🎛️ 核心功能模块

#### 1. 组件面板 (ComponentPanel)
```vue
<template>
  <div class="component-panel">
    <div class="component-search">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索组件..."
        prefix-icon="el-icon-search"
        clearable
      />
    </div>

    <div class="component-categories">
      <el-collapse v-model="activeCategories">
        <el-collapse-item
          v-for="category in filteredCategories"
          :key="category.id"
          :title="category.name"
          :name="category.id"
        >
          <div class="component-grid">
            <div
              v-for="component in category.components"
              :key="component.id"
              class="component-item"
              draggable="true"
              @dragstart="onDragStart(component, $event)"
              @click="addComponent(component)"
            >
              <div class="component-icon">
                <i :class="component.icon"></i>
              </div>
              <div class="component-name">{{ component.name }}</div>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script setup lang="ts">
const searchKeyword = ref('')
const activeCategories = ref(['basic', 'layout'])

const componentCategories = [
  {
    id: 'basic',
    name: '基础组件',
    components: basicComponents
  },
  {
    id: 'layout',
    name: '布局组件',
    components: layoutComponents
  },
  {
    id: 'business',
    name: '业务组件',
    components: businessComponents
  },
  {
    id: 'composite',
    name: '复合组件',
    components: compositeComponents
  }
]

const filteredCategories = computed(() => {
  if (!searchKeyword.value) return componentCategories

  return componentCategories.map(category => ({
    ...category,
    components: category.components.filter(component =>
      component.name.includes(searchKeyword.value) ||
      component.description.includes(searchKeyword.value)
    )
  })).filter(category => category.components.length > 0)
})

const addComponent = (component: Component) => {
  const editor = useEditorStore().editor
  editor.runCommand('core:component-add', { component })
}
</script>
```

#### 2. 属性面板 (TraitsPanel)
```vue
<template>
  <div class="traits-panel">
    <div v-if="selectedComponent" class="traits-content">
      <div class="traits-header">
        <h4>{{ selectedComponent.getName() }}</h4>
        <el-button size="small" @click="deleteComponent">删除</el-button>
      </div>

      <el-form label-position="top">
        <el-form-item
          v-for="trait in componentTraits"
          :key="trait.name"
          :label="trait.label"
        >
          <!-- 文本输入 -->
          <el-input
            v-if="trait.type === 'text'"
            :model-value="trait.value"
            @input="updateTrait(trait.name, $event)"
          />

          <!-- 数字输入 -->
          <el-input-number
            v-else-if="trait.type === 'number'"
            :model-value="trait.value"
            @change="updateTrait(trait.name, $event)"
          />

          <!-- 选择器 -->
          <el-select
            v-else-if="trait.type === 'select'"
            :model-value="trait.value"
            @change="updateTrait(trait.name, $event)"
          >
            <el-option
              v-for="option in trait.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>

          <!-- 开关 -->
          <el-switch
            v-else-if="trait.type === 'checkbox'"
            :model-value="trait.value"
            @change="updateTrait(trait.name, $event)"
          />

          <!-- 颜色选择器 -->
          <el-color-picker
            v-else-if="trait.type === 'color'"
            :model-value="trait.value"
            @change="updateTrait(trait.name, $event)"
          />
        </el-form-item>
      </el-form>
    </div>

    <div v-else class="no-selection">
      <el-empty description="请选择一个组件" />
    </div>
  </div>
</template>

<script setup lang="ts">
const editorStore = useEditorStore()
const selectedComponent = computed(() => editorStore.selectedComponent)

const componentTraits = computed(() => {
  if (!selectedComponent.value) return []

  return selectedComponent.value.getTraits().map(trait => ({
    name: trait.get('name'),
    label: trait.get('label'),
    type: trait.get('type'),
    value: trait.getTargetValue(),
    options: trait.get('options') || []
  }))
})

const updateTrait = (name: string, value: any) => {
  if (selectedComponent.value) {
    selectedComponent.value.addAttributes({ [name]: value })
  }
}

const deleteComponent = () => {
  if (selectedComponent.value) {
    selectedComponent.value.remove()
  }
}
</script>
```

#### 3. 样式面板 (StylesPanel)
```vue
<template>
  <div class="styles-panel">
    <div v-if="selectedComponent" class="styles-content">
      <el-collapse v-model="activeStyleGroups">
        <!-- 尺寸和位置 -->
        <el-collapse-item title="尺寸和位置" name="dimension">
          <div class="style-group">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="宽度">
                  <el-input
                    :model-value="styles.width"
                    @input="updateStyle('width', $event)"
                    placeholder="auto"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="高度">
                  <el-input
                    :model-value="styles.height"
                    @input="updateStyle('height', $event)"
                    placeholder="auto"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-collapse-item>

        <!-- 文字样式 -->
        <el-collapse-item title="文字样式" name="typography">
          <div class="style-group">
            <el-form-item label="字体大小">
              <el-slider
                :model-value="parseInt(styles.fontSize) || 16"
                :min="12"
                :max="72"
                @change="updateStyle('fontSize', $event + 'px')"
              />
            </el-form-item>

            <el-form-item label="字体颜色">
              <el-color-picker
                :model-value="styles.color"
                @change="updateStyle('color', $event)"
              />
            </el-form-item>

            <el-form-item label="文字对齐">
              <el-radio-group
                :model-value="styles.textAlign"
                @change="updateStyle('textAlign', $event)"
              >
                <el-radio-button label="left">左对齐</el-radio-button>
                <el-radio-button label="center">居中</el-radio-button>
                <el-radio-button label="right">右对齐</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </div>
        </el-collapse-item>

        <!-- 背景样式 -->
        <el-collapse-item title="背景样式" name="background">
          <div class="style-group">
            <el-form-item label="背景颜色">
              <el-color-picker
                :model-value="styles.backgroundColor"
                @change="updateStyle('backgroundColor', $event)"
              />
            </el-form-item>

            <el-form-item label="背景图片">
              <ImageUploader
                :value="styles.backgroundImage"
                @change="updateStyle('backgroundImage', `url(${$event})`)"
              />
            </el-form-item>
          </div>
        </el-collapse-item>

        <!-- 边框样式 -->
        <el-collapse-item title="边框样式" name="border">
          <div class="style-group">
            <el-form-item label="边框宽度">
              <el-input-number
                :model-value="parseInt(styles.borderWidth) || 0"
                :min="0"
                :max="20"
                @change="updateStyle('borderWidth', $event + 'px')"
              />
            </el-form-item>

            <el-form-item label="边框颜色">
              <el-color-picker
                :model-value="styles.borderColor"
                @change="updateStyle('borderColor', $event)"
              />
            </el-form-item>

            <el-form-item label="圆角">
              <el-input-number
                :model-value="parseInt(styles.borderRadius) || 0"
                :min="0"
                :max="50"
                @change="updateStyle('borderRadius', $event + 'px')"
              />
            </el-form-item>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <div v-else class="no-selection">
      <el-empty description="请选择一个组件" />
    </div>
  </div>
</template>

<script setup lang="ts">
const editorStore = useEditorStore()
const selectedComponent = computed(() => editorStore.selectedComponent)
const activeStyleGroups = ref(['dimension', 'typography'])

const styles = computed(() => {
  if (!selectedComponent.value) return {}

  const styleManager = editorStore.editor.StyleManager
  const styles = {}

  styleManager.getProperties().forEach(property => {
    const name = property.get('property')
    styles[name] = selectedComponent.value.getStyle()[name] || ''
  })

  return styles
})

const updateStyle = (property: string, value: string) => {
  if (selectedComponent.value) {
    selectedComponent.value.addStyle({ [property]: value })
  }
}
</script>
```
<?php
/**
 * 三只鱼网络科技 | 韩总 | 2025-06-10
 * 应用路由配置 - ThinkPHP6企业级应用
 */

// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

// 前台路由
Route::get('/', 'index/index');
Route::get('/index', 'index/index');

// 解决方案路由 - 具体路由放在前面
Route::get('/solutions/:slug', 'index/solutionDetail')->pattern(['slug' => '[a-zA-Z0-9\-_]+']);
Route::get('/solutions', 'index/solutions');

// 新闻路由 - 更明确的路由定义
Route::get('/news/ajax-list', 'index/ajaxList'); // AJAX获取新闻列表
Route::post('/news/view/:id', 'index/newsView')->pattern(['id' => '\d+']);
Route::get('/news/category/:category', 'index/newsCategory')->pattern(['category' => '[a-zA-Z0-9\-_]+']); // 新闻分类页面
Route::get('/news/detail/:slug', 'index/newsDetail')->pattern(['slug' => '[a-zA-Z0-9\-_]+']);
Route::get('/news/:slug', 'index/newsDetail')->pattern(['slug' => '[a-zA-Z0-9\-_]+']);
Route::get('/news', 'index/news');

// 案例路由
Route::get('/ajax-cases', 'index/ajaxCases'); // AJAX获取案例列表
Route::get('/cases/ajax-list', 'index/casesAjaxList'); // AJAX获取案例列表
Route::get('/cases/detail/:slug', 'index/caseDetail')->pattern(['slug' => '[a-zA-Z0-9\-_]+']);
Route::get('/cases/:slug', 'index/caseDetail')->pattern(['slug' => '[a-zA-Z0-9\-_]+']);
Route::get('/cases', 'index/cases');

// 产品路由
Route::get('/products/search', 'products/search'); // 产品搜索
Route::get('/products/detail/:id', 'products/detail')->pattern(['id' => '\d+']); // 产品详情（ID）
Route::get('/products/:slug', 'products/detail')->pattern(['slug' => '[a-zA-Z0-9\-_]+']); // 产品详情（slug）
Route::get('/products', 'products/index'); // 产品列表

// 联系我们路由
Route::get('/contact', 'contact/index');
Route::post('/contact/submit', 'contact/submit');

// DIY页面展示路由
Route::get('/page/:slug', 'index/diyPage')->pattern(['slug' => '[a-zA-Z0-9\-_]+']);
Route::get('/diy-preview/:id', 'index/diyPreview')->pattern(['id' => '\d+']);

// API路由组
Route::group('api', function () {
    // DIY页面相关API
    Route::get('diy-pages', 'app\api\controller\Pages@diyPages');
    Route::get('pages-by-type', 'app\api\controller\Pages@pagesByType');
    Route::get('page-detail', 'app\api\controller\Pages@pageDetail');
    Route::get('page-stats', 'app\api\controller\Pages@pageStats');
    Route::post('clear-cache', 'app\api\controller\Pages@clearCache');
})->allowCrossDomain();

<style>
/**
 * 消息提示样式 - 三只鱼网络科技 | 韩总 | 2024-12-19
 * 简洁统一的消息提示系统 - ThinkPHP6企业级应用
 */

/* 消息提示基础样式 - 后台深色科技风完美居中 */
.alert {
    position: fixed !important;
    top: 80px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    padding: 16px 24px !important;
    margin: 0 !important;
    border-radius: 12px !important;
    display: flex !important;
    align-items: center !important;
    gap: 14px !important;
    backdrop-filter: blur(25px) !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.6),
        0 2px 8px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    animation: slideInDown 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    z-index: 99999 !important;
    min-width: 340px !important;
    max-width: 520px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    font-family: '<PERSON><PERSON><PERSON>', 'Microsoft YaHei', sans-serif !important;
    letter-spacing: 0.3px !important;
    /* 确保在所有容器中都能完美居中 */
    margin-left: auto !important;
    margin-right: auto !important;
}

/* 消息图标 - 科技发光效果 */
.alert-icon {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 28px !important;
    height: 28px !important;
    border-radius: 50% !important;
    flex-shrink: 0 !important;
    font-size: 15px !important;
    position: relative !important;
    transition: all 0.3s ease !important;
}

.alert-icon::before {
    content: '' !important;
    position: absolute !important;
    top: -2px !important;
    left: -2px !important;
    right: -2px !important;
    bottom: -2px !important;
    border-radius: 50% !important;
    background: inherit !important;
    opacity: 0.3 !important;
    animation: pulse 2s infinite !important;
}

/* 消息内容 */
.alert-content {
    flex: 1 !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* 成功提示 - 按钮式紫色渐变风格 */
.alert-success {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8) 0%,
        rgba(255, 119, 198, 0.6) 50%,
        rgba(120, 219, 255, 0.7) 100%) !important;
    border: 1px solid rgba(120, 119, 198, 0.6) !important;
    color: #ffffff !important;
    box-shadow: 0 4px 15px rgba(120, 119, 198, 0.3) !important;
}

.alert-success .alert-icon {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.9),
        rgba(240, 240, 255, 1)) !important;
    color: rgba(120, 119, 198, 0.9) !important;
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.4) !important;
}

/* 错误提示 - 后台深色科技风 */
.alert-danger {
    background: linear-gradient(135deg,
        rgba(15, 15, 15, 0.95) 0%,
        rgba(45, 25, 25, 0.95) 100%) !important;
    border: 1px solid rgba(255, 119, 119, 0.4) !important;
    color: #ffb3b3 !important;
}

.alert-danger .alert-icon {
    background: linear-gradient(135deg,
        rgba(255, 119, 119, 0.9),
        rgba(220, 80, 80, 1)) !important;
    color: #ffffff !important;
    box-shadow: 0 0 15px rgba(255, 119, 119, 0.5) !important;
}

/* 警告提示 - 后台深色科技风 */
.alert-warning {
    background: linear-gradient(135deg,
        rgba(15, 15, 15, 0.95) 0%,
        rgba(45, 35, 15, 0.95) 100%) !important;
    border: 1px solid rgba(255, 193, 7, 0.4) !important;
    color: #ffd54f !important;
}

.alert-warning .alert-icon {
    background: linear-gradient(135deg,
        rgba(255, 193, 7, 0.9),
        rgba(255, 152, 0, 1)) !important;
    color: #ffffff !important;
    box-shadow: 0 0 15px rgba(255, 193, 7, 0.5) !important;
}

/* 信息提示 - 后台深色科技风 */
.alert-info {
    background: linear-gradient(135deg,
        rgba(15, 15, 15, 0.95) 0%,
        rgba(25, 25, 45, 0.95) 100%) !important;
    border: 1px solid rgba(120, 119, 198, 0.4) !important;
    color: #b3b3ff !important;
}

.alert-info .alert-icon {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.9),
        rgba(120, 219, 255, 0.9)) !important;
    color: #ffffff !important;
    box-shadow: 0 0 15px rgba(120, 119, 198, 0.5) !important;
}

/* 动画效果 - 科技感流畅动画 */
@keyframes slideInDown {
    0% {
        transform: translateX(-50%) translateY(-100%);
        opacity: 0;
        filter: blur(4px);
    }
    50% {
        transform: translateX(-50%) translateY(-10px);
        opacity: 0.8;
        filter: blur(1px);
    }
    100% {
        transform: translateX(-50%) translateY(0);
        opacity: 1;
        filter: blur(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.15);
        opacity: 0.6;
    }
}

/* 悬停效果 - 科技感交互 */
.alert:hover {
    transform: translateX(-50%) translateY(-3px) !important;
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.7),
        0 4px 12px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

/* 响应式设计 - 移动端优化 */
@media (max-width: 768px) {
    .alert {
        min-width: 300px !important;
        max-width: 92vw !important;
        padding: 14px 18px !important;
        gap: 10px !important;
        font-size: 13px !important;
        border-radius: 10px !important;
        top: 60px !important;
        /* 移动端确保居中 */
        left: 50% !important;
        transform: translateX(-50%) !important;
    }

    .alert-icon {
        width: 24px !important;
        height: 24px !important;
        font-size: 13px !important;
    }

    .alert-icon::before {
        top: -1px !important;
        left: -1px !important;
        right: -1px !important;
        bottom: -1px !important;
    }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
    .alert {
        min-width: 280px !important;
        max-width: 95vw !important;
        padding: 12px 16px !important;
        gap: 8px !important;
        font-size: 12px !important;
        top: 50px !important;
        /* 超小屏幕确保居中 */
        left: 50% !important;
        transform: translateX(-50%) !important;
    }

    .alert-icon {
        width: 22px !important;
        height: 22px !important;
        font-size: 12px !important;
    }
}
</style> 
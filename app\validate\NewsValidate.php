<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * 新闻验证器 - ThinkPHP6企业级应用
 * 功能：新闻数据验证和安全过滤
 */

namespace app\validate;

class NewsValidate extends BaseValidate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'title' => 'require|length:1,200|checkSql',
        'slug' => 'require|length:1,200|alphaNum|checkSql',
        'category_id' => 'require|integer|gt:0',
        'summary' => 'length:0,500|checkSql',
        'content' => 'checkSql',
        'author' => 'length:0,50|checkSql',
        'tags' => 'length:0,200|checkSql',
        'sort_order' => 'integer|egt:0',
        'status' => 'in:0,1',
        'is_featured' => 'in:0,1',
        'published_at' => 'date',
        'image_url' => 'url|checkSql',
    ];
    
    /**
     * 验证消息
     */
    protected $message = [
        'title.require' => '新闻标题不能为空',
        'title.length' => '新闻标题长度不能超过200个字符',
        'title.checkSql' => '新闻标题包含非法字符',
        'slug.require' => '新闻别名不能为空',
        'slug.length' => '新闻别名长度不能超过200个字符',
        'slug.alphaNum' => '新闻别名只能包含字母和数字',
        'slug.checkSql' => '新闻别名包含非法字符',
        'category_id.require' => '请选择新闻分类',
        'category_id.integer' => '新闻分类ID必须为整数',
        'category_id.gt' => '新闻分类ID必须大于0',
        'summary.length' => '新闻摘要长度不能超过500个字符',
        'summary.checkSql' => '新闻摘要包含非法字符',
        'content.checkSql' => '新闻内容包含非法字符',
        'author.length' => '作者长度不能超过50个字符',
        'author.checkSql' => '作者包含非法字符',
        'tags.length' => '标签长度不能超过200个字符',
        'tags.checkSql' => '标签包含非法字符',
        'sort_order.integer' => '排序必须为整数',
        'sort_order.egt' => '排序必须大于等于0',
        'status.in' => '状态值无效',
        'is_featured.in' => '推荐状态值无效',
        'published_at.date' => '发布时间格式无效',
        'image_url.url' => '图片URL格式无效',
        'image_url.checkSql' => '图片URL包含非法字符',
    ];
    
    /**
     * 验证场景
     */
    protected $scene = [
        'add' => ['title', 'category_id', 'summary', 'content', 'author', 'tags', 'sort_order', 'status', 'is_featured', 'published_at', 'image_url'],
        'edit' => ['title', 'category_id', 'summary', 'content', 'author', 'tags', 'sort_order', 'status', 'is_featured', 'published_at', 'image_url'],
    ];
    
    /**
     * 自定义验证规则：SQL注入检查
     */
    protected function checkSql($value, $rule, $data = [])
    {
        if (!$this->checkSqlInjection($value)) {
            return '输入内容包含非法字符';
        }
        return true;
    }
    
    /**
     * 验证文件上传
     */
    public function validateFileUpload($file)
    {
        if (!$file) {
            return '请选择要上传的文件';
        }
        
        // 检查文件大小（5MB限制）
        if ($file->getSize() > 5 * 1024 * 1024) {
            return '文件大小不能超过5MB';
        }
        
        // 检查文件扩展名
        if (!$this->checkFileExtension($file->getOriginalName())) {
            return '只允许上传jpg、jpeg、png、gif、webp格式的图片';
        }
        
        // 检查MIME类型
        if (!$this->checkImageMimeType($file->getMime())) {
            return '文件类型不正确，只允许上传图片文件';
        }
        
        // 检查文件名安全性
        if (!$this->checkFilename($file->getOriginalName())) {
            return '文件名包含非法字符';
        }
        
        return true;
    }
}

<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 角色管理页面
-->

<template>
  <div class="role-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">角色管理</h2>
        <p class="page-description">管理系统角色和权限配置</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="handleAdd" v-if="userStore.hasPermission('role.create')">
          新增角色
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="角色名称/标识"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item label="默认角色">
          <el-select v-model="searchForm.is_default" placeholder="全部" clearable style="width: 120px">
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
          <el-button :icon="Refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <!-- 表格工具栏 -->
      <div class="table-toolbar">
        <div class="toolbar-left">
          <el-button
            type="danger"
            :icon="Delete"
            :disabled="!selectedIds.length"
            @click="handleBatchDelete"
            v-if="userStore.hasPermission('role.delete')"
          >
            批量删除
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-tooltip content="刷新数据">
            <el-button :icon="Refresh" circle @click="loadData" />
          </el-tooltip>
          <el-tooltip content="权限管理">
            <el-button :icon="Key" circle @click="showPermissionDialog = true" />
          </el-tooltip>
        </div>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        stripe
        class="role-table"
      >
        <el-table-column type="selection" width="50" />
        
        <el-table-column prop="id" label="ID" width="80" sortable="custom" />
        
        <el-table-column prop="name" label="角色名称" min-width="150">
          <template #default="{ row }">
            <div class="role-name">
              <span class="name">{{ row.name }}</span>
              <el-tag v-if="row.is_default" type="success" size="small" class="default-tag">
                默认
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="slug" label="角色标识" width="150">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.slug }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="描述" min-width="200">
          <template #default="{ row }">
            {{ row.description || '-' }}
          </template>
        </el-table-column>
        
        <el-table-column label="权限数量" width="100">
          <template #default="{ row }">
            <el-tag type="primary" size="small">
              {{ row.permissions?.length || 0 }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="用户数量" width="100">
          <template #default="{ row }">
            <el-link type="primary" @click="handleViewUsers(row)">
              {{ row.user_count || 0 }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="sort_order" label="排序" width="80" sortable="custom" />
        
        <el-table-column prop="created_at" label="创建时间" width="160" sortable="custom">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              :icon="Edit"
              @click="handleEdit(row)"
              v-if="userStore.hasPermission('role.update')"
            >
              编辑
            </el-button>
            <el-button
              type="warning"
              size="small"
              :icon="Key"
              @click="handlePermissions(row)"
              v-if="userStore.hasPermission('role.assign_permissions')"
            >
              权限
            </el-button>
            <el-button
              type="info"
              size="small"
              :icon="CopyDocument"
              @click="handleCopy(row)"
              v-if="userStore.hasPermission('role.create')"
            >
              复制
            </el-button>
            <el-button
              type="danger"
              size="small"
              :icon="Delete"
              @click="handleDelete(row)"
              v-if="userStore.hasPermission('role.delete') && !row.is_default"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadData"
          @current-change="loadData"
        />
      </div>
    </el-card>

    <!-- 角色表单对话框 -->
    <RoleFormDialog
      v-model="showRoleDialog"
      :role-data="currentRole"
      :is-edit="isEdit"
      @success="handleFormSuccess"
    />

    <!-- 权限配置对话框 -->
    <PermissionDialog
      v-model="showPermissionDialog"
      :role-data="currentRole"
      @success="handlePermissionSuccess"
    />

    <!-- 复制角色对话框 -->
    <CopyRoleDialog
      v-model="showCopyDialog"
      :role-data="currentRole"
      @success="handleCopySuccess"
    />

    <!-- 角色用户列表对话框 -->
    <RoleUsersDialog
      v-model="showUsersDialog"
      :role-data="currentRole"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { roleApi } from '@/api/role'
import type { Role } from '@/types/auth'
import {
  Plus, Search, Refresh, Delete, Edit, Key, CopyDocument
} from '@element-plus/icons-vue'
import RoleFormDialog from './components/RoleFormDialog.vue'
import PermissionDialog from './components/PermissionDialog.vue'
import CopyRoleDialog from './components/CopyRoleDialog.vue'
import RoleUsersDialog from './components/RoleUsersDialog.vue'

const userStore = useUserStore()

// 状态
const loading = ref(false)
const tableData = ref<Role[]>([])
const selectedIds = ref<number[]>([])
const showRoleDialog = ref(false)
const showPermissionDialog = ref(false)
const showCopyDialog = ref(false)
const showUsersDialog = ref(false)
const currentRole = ref<Role | null>(null)
const isEdit = ref(false)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  is_default: ''
})

// 分页
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 排序
const sortParams = reactive({
  sort_field: 'sort_order',
  sort_order: 'asc'
})

/**
 * 加载数据
 */
const loadData = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.current,
      per_page: pagination.size,
      keyword: searchForm.keyword,
      is_default: searchForm.is_default,
      ...sortParams
    }
    
    const response = await roleApi.getList(params)
    tableData.value = response.data.list
    pagination.total = response.data.total
    
  } catch (error) {
    console.error('加载角色数据失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 搜索
 */
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

/**
 * 重置搜索
 */
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    is_default: ''
  })
  pagination.current = 1
  loadData()
}

/**
 * 新增角色
 */
const handleAdd = () => {
  currentRole.value = null
  isEdit.value = false
  showRoleDialog.value = true
}

/**
 * 编辑角色
 */
const handleEdit = (row: Role) => {
  currentRole.value = { ...row }
  isEdit.value = true
  showRoleDialog.value = true
}

/**
 * 删除角色
 */
const handleDelete = async (row: Role) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色 "${row.name}" 吗？`,
      '删除确认',
      { type: 'warning' }
    )
    
    await roleApi.delete(row.id)
    ElMessage.success('删除成功')
    loadData()
    
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  }
}

/**
 * 批量删除
 */
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedIds.value.length} 个角色吗？`,
      '批量删除确认',
      { type: 'warning' }
    )
    
    await roleApi.batchDelete(selectedIds.value)
    ElMessage.success('批量删除成功')
    selectedIds.value = []
    loadData()
    
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '批量删除失败')
    }
  }
}

/**
 * 权限配置
 */
const handlePermissions = (row: Role) => {
  currentRole.value = { ...row }
  showPermissionDialog.value = true
}

/**
 * 复制角色
 */
const handleCopy = (row: Role) => {
  currentRole.value = { ...row }
  showCopyDialog.value = true
}

/**
 * 查看角色用户
 */
const handleViewUsers = (row: Role) => {
  currentRole.value = { ...row }
  showUsersDialog.value = true
}

/**
 * 选择变化
 */
const handleSelectionChange = (selection: Role[]) => {
  selectedIds.value = selection.map(item => item.id)
}

/**
 * 排序变化
 */
const handleSortChange = ({ prop, order }: any) => {
  if (order) {
    sortParams.sort_field = prop
    sortParams.sort_order = order === 'ascending' ? 'asc' : 'desc'
  } else {
    sortParams.sort_field = 'sort_order'
    sortParams.sort_order = 'asc'
  }
  loadData()
}

/**
 * 表单成功回调
 */
const handleFormSuccess = () => {
  showRoleDialog.value = false
  loadData()
}

/**
 * 权限配置成功回调
 */
const handlePermissionSuccess = () => {
  showPermissionDialog.value = false
  loadData()
}

/**
 * 复制成功回调
 */
const handleCopySuccess = () => {
  showCopyDialog.value = false
  loadData()
}

/**
 * 格式化日期
 */
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.role-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left {
  .page-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 8px 0;
  }
  
  .page-description {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin: 0;
  }
}

.search-card {
  margin-bottom: 20px;
  border-radius: 8px;
  
  .search-form {
    margin-bottom: 0;
  }
}

.table-card {
  border-radius: 8px;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.toolbar-left {
  display: flex;
  gap: 8px;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.role-table {
  .role-name {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .name {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
    
    .default-tag {
      font-size: 10px;
    }
  }
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

// 响应式设计
@media (max-width: 768px) {
  .role-list-container {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .search-form {
    :deep(.el-form-item) {
      margin-bottom: 16px;
    }
  }
  
  .table-toolbar {
    flex-direction: column;
    gap: 12px;
  }
  
  .role-table {
    :deep(.el-table__body-wrapper) {
      overflow-x: auto;
    }
  }
}
</style>

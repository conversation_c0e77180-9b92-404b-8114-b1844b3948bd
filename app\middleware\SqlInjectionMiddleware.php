<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * SQL注入防护中间件 - ThinkPHP6企业级应用
 * 功能：统一SQL注入检测、参数过滤、危险查询拦截
 */

namespace app\middleware;

use think\facade\Config;
use think\facade\Log;
use think\Response;

class SqlInjectionMiddleware
{
    /**
     * SQL注入危险关键词
     */
    private static $dangerousKeywords = [
        // 基础SQL关键词
        'union', 'select', 'insert', 'update', 'delete', 'drop', 'create', 'alter',
        'truncate', 'replace', 'handler', 'load_file', 'outfile', 'dumpfile',
        
        // 函数和操作符
        'concat', 'char', 'ascii', 'substring', 'length', 'version', 'database',
        'user', 'system_user', 'session_user', 'current_user', 'connection_id',
        'load_file', 'into', 'outfile', 'dumpfile', 'hex', 'unhex', 'md5',
        
        // 注释符号
        '--', '/*', '*/', '#',
        
        // 逻辑操作符
        'and', 'or', 'xor', 'not', 'between', 'like', 'rlike', 'regexp',
        
        // 特殊字符组合
        '||', '&&', '|', '&', '^', '~', '<<', '>>', 
        
        // 时间延迟函数
        'sleep', 'benchmark', 'pg_sleep', 'waitfor',
        
        // 信息收集函数
        'information_schema', 'mysql', 'performance_schema', 'sys',
        'pg_database', 'pg_user', 'pg_tables', 'pg_version',
        
        // 执行函数
        'exec', 'execute', 'sp_executesql', 'xp_cmdshell',
    ];

    /**
     * SQL注入模式
     */
    private static $injectionPatterns = [
        // 联合查询注入
        '/union\s+select/i',
        '/union\s+all\s+select/i',
        
        // 布尔盲注
        '/\s+and\s+\d+\s*=\s*\d+/i',
        '/\s+or\s+\d+\s*=\s*\d+/i',
        '/\s+and\s+\d+\s*<>\s*\d+/i',
        '/\s+or\s+\d+\s*<>\s*\d+/i',
        
        // 时间盲注
        '/sleep\s*\(\s*\d+\s*\)/i',
        '/benchmark\s*\(/i',
        '/pg_sleep\s*\(/i',
        '/waitfor\s+delay/i',
        
        // 报错注入
        '/extractvalue\s*\(/i',
        '/updatexml\s*\(/i',
        '/floor\s*\(\s*rand\s*\(/i',
        
        // 堆叠注入
        '/;\s*drop\s+/i',
        '/;\s*delete\s+/i',
        '/;\s*update\s+/i',
        '/;\s*insert\s+/i',
        
        // 注释绕过
        '/\/\*.*?\*\//s',
        '/--\s+/i',
        '/#.*$/m',
        
        // 编码绕过
        '/char\s*\(/i',
        '/ascii\s*\(/i',
        '/hex\s*\(/i',
        '/unhex\s*\(/i',
        
        // 函数注入
        '/load_file\s*\(/i',
        '/into\s+outfile/i',
        '/into\s+dumpfile/i',
        
        // 信息收集
        '/information_schema\./i',
        '/mysql\./i',
        '/performance_schema\./i',
        '/sys\./i',
        
        // 特殊字符
        '/\'\s*or\s*\'/i',
        '/\'\s*and\s*\'/i',
        '/\'\s*union\s*\'/i',
        '/\"\s*or\s*\"/i',
        '/\"\s*and\s*\"/i',
        '/\"\s*union\s*\"/i',
    ];

    /**
     * 处理请求
     */
    public function handle($request, \Closure $next)
    {
        $config = Config::get('security.sql_injection', []);
        
        // 检查是否启用SQL注入防护
        if (!($config['enable'] ?? true)) {
            return $next($request);
        }
        
        // 检查排除路由
        $path = $request->pathinfo();
        $except = $config['except'] ?? [];
        foreach ($except as $pattern) {
            if (fnmatch($pattern, $path)) {
                return $next($request);
            }
        }
        
        // 获取所有请求参数
        $params = $request->param();
        $headers = $request->header();
        $cookies = $request->cookie();
        
        // 检查参数
        if ($this->detectSqlInjection($params, 'params')) {
            return $this->handleSqlInjectionDetected($request, 'parameters');
        }
        
        // 检查请求头
        if ($config['check_headers'] ?? false) {
            if ($this->detectSqlInjection($headers, 'headers')) {
                return $this->handleSqlInjectionDetected($request, 'headers');
            }
        }
        
        // 检查Cookie
        if ($config['check_cookies'] ?? false) {
            if ($this->detectSqlInjection($cookies, 'cookies')) {
                return $this->handleSqlInjectionDetected($request, 'cookies');
            }
        }
        
        // 检查URL路径
        if ($config['check_path'] ?? true) {
            if ($this->detectSqlInjectionInString($path)) {
                return $this->handleSqlInjectionDetected($request, 'path');
            }
        }
        
        return $next($request);
    }

    /**
     * 检测SQL注入
     */
    private function detectSqlInjection($data, $source = 'unknown')
    {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                if ($this->detectSqlInjection($value, $source)) {
                    return true;
                }
                // 也检查键名
                if (is_string($key) && $this->detectSqlInjectionInString($key)) {
                    return true;
                }
            }
        } elseif (is_string($data)) {
            return $this->detectSqlInjectionInString($data);
        }
        
        return false;
    }

    /**
     * 检测字符串中的SQL注入
     */
    private function detectSqlInjectionInString($string)
    {
        if (empty($string) || !is_string($string)) {
            return false;
        }
        
        // URL解码
        $decodedString = urldecode($string);
        
        // 转换为小写进行检查
        $lowerString = strtolower($decodedString);
        
        // 1. 检查危险关键词
        foreach (self::$dangerousKeywords as $keyword) {
            if (strpos($lowerString, $keyword) !== false) {
                // 进一步验证是否为真正的SQL注入
                if ($this->isLikelySqlInjection($decodedString, $keyword)) {
                    return true;
                }
            }
        }
        
        // 2. 检查注入模式
        foreach (self::$injectionPatterns as $pattern) {
            if (preg_match($pattern, $decodedString)) {
                return true;
            }
        }
        
        // 3. 检查特殊字符组合
        if ($this->checkSpecialCharacterCombinations($decodedString)) {
            return true;
        }
        
        return false;
    }

    /**
     * 判断是否可能是SQL注入
     */
    private function isLikelySqlInjection($string, $keyword)
    {
        $config = Config::get('security.sql_injection', []);
        $strictMode = $config['strict_mode'] ?? false;
        
        if ($strictMode) {
            return true; // 严格模式下发现关键词就认为是注入
        }
        
        // 宽松模式下进行更精确的判断
        $suspiciousPatterns = [
            '/\b' . preg_quote($keyword, '/') . '\b.*[\'";]/i',
            '/[\'";].*\b' . preg_quote($keyword, '/') . '\b/i',
            '/\b' . preg_quote($keyword, '/') . '\b.*\s+(and|or|where|from|into|values)\s+/i',
            '/\s+(and|or|where|from|into|values)\s+.*\b' . preg_quote($keyword, '/') . '\b/i',
        ];
        
        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $string)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查特殊字符组合
     */
    private function checkSpecialCharacterCombinations($string)
    {
        $dangerousCombinations = [
            // 引号和分号组合
            '/[\'"];/',
            // 引号和注释组合
            '/[\'"].*--/',
            '/[\'"].*\/\*/',
            '/[\'"].*#/',
            // 多个引号
            '/[\'"][^\'\"]*[\'"][^\'\"]*[\'"]/',
            // 括号和引号组合
            '/\([^)]*[\'"][^)]*\)/',
            // 特殊操作符组合
            '/[=<>!]+\s*[\'"]/',
            '/[\'\"]\s*[=<>!]+/',
        ];
        
        foreach ($dangerousCombinations as $pattern) {
            if (preg_match($pattern, $string)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 处理检测到SQL注入
     */
    private function handleSqlInjectionDetected($request, $source)
    {
        $config = Config::get('security.sql_injection', []);
        
        // 记录安全日志
        $this->logSqlInjectionAttempt($request, $source);
        
        // 获取响应方式
        $action = $config['action'] ?? 'block';
        
        switch ($action) {
            case 'block':
                return Response::create('SQL Injection Detected', 'html', 403);
                
            case 'redirect':
                $redirectUrl = $config['redirect_url'] ?? '/';
                return Response::create('', 'html', 302)->header('Location', $redirectUrl);
                
            case 'clean':
                // 清理参数并继续
                $this->cleanSqlInjectionParams($request);
                return null; // 继续处理请求
                
            default:
                return Response::create('Access Denied', 'html', 403);
        }
    }

    /**
     * 记录SQL注入尝试
     */
    private function logSqlInjectionAttempt($request, $source)
    {
        $logData = [
            'type' => 'sql_injection_attempt',
            'source' => $source,
            'ip' => $request->ip(),
            'user_agent' => $request->header('User-Agent'),
            'url' => $request->url(true),
            'method' => $request->method(),
            'params' => $request->param(),
            'headers' => $request->header(),
            'timestamp' => date('Y-m-d H:i:s'),
        ];
        
        // 写入安全日志
        Log::write(json_encode($logData), 'security');
        
        // 写入专门的SQL注入日志
        $logFile = runtime_path() . 'log/sql_injection_' . date('Y-m-d') . '.log';
        file_put_contents($logFile, json_encode($logData) . "\n", FILE_APPEND | LOCK_EX);
    }

    /**
     * 清理SQL注入参数
     */
    private function cleanSqlInjectionParams($request)
    {
        $params = $request->param();
        $cleanedParams = $this->recursiveCleanSqlInjection($params);
        
        // 更新请求参数
        $request->withParam($cleanedParams);
    }

    /**
     * 递归清理SQL注入
     */
    private function recursiveCleanSqlInjection($data)
    {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->recursiveCleanSqlInjection($value);
            }
        } elseif (is_string($data)) {
            // 移除危险关键词
            foreach (self::$dangerousKeywords as $keyword) {
                $data = preg_replace('/\b' . preg_quote($keyword, '/') . '\b/i', '', $data);
            }
            
            // 移除特殊字符
            $data = preg_replace('/[\'";#]/', '', $data);
            $data = preg_replace('/--.*$/', '', $data);
            $data = preg_replace('/\/\*.*?\*\//', '', $data);
            
            // 清理多余空格
            $data = preg_replace('/\s+/', ' ', trim($data));
        }
        
        return $data;
    }

    /**
     * 获取SQL注入统计
     */
    public static function getInjectionStats($days = 7)
    {
        $stats = [
            'total_attempts' => 0,
            'blocked_attempts' => 0,
            'sources' => [],
            'top_ips' => [],
            'daily_stats' => []
        ];
        
        for ($i = 0; $i < $days; $i++) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $logFile = runtime_path() . "log/sql_injection_{$date}.log";
            
            if (file_exists($logFile)) {
                $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                $dayStats = ['date' => $date, 'count' => count($lines)];
                $stats['daily_stats'][] = $dayStats;
                $stats['total_attempts'] += count($lines);
                
                foreach ($lines as $line) {
                    $data = json_decode($line, true);
                    if ($data) {
                        // 统计来源
                        $source = $data['source'] ?? 'unknown';
                        $stats['sources'][$source] = ($stats['sources'][$source] ?? 0) + 1;
                        
                        // 统计IP
                        $ip = $data['ip'] ?? 'unknown';
                        $stats['top_ips'][$ip] = ($stats['top_ips'][$ip] ?? 0) + 1;
                    }
                }
            }
        }
        
        // 排序
        arsort($stats['sources']);
        arsort($stats['top_ips']);
        
        // 只保留前10
        $stats['top_ips'] = array_slice($stats['top_ips'], 0, 10, true);
        
        return $stats;
    }
}

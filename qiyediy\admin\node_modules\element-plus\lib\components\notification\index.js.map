{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/notification/index.ts"], "sourcesContent": ["import { withInstallFunction } from '@element-plus/utils'\n\nimport Notify from './src/notify'\n\nexport const ElNotification = withInstallFunction(Notify, '$notify')\nexport default ElNotification\n\nexport * from './src/notification'\n"], "names": ["withInstallFunction", "Notify"], "mappings": ";;;;;;;;AAEY,MAAC,cAAc,GAAGA,2BAAmB,CAACC,iBAAM,EAAE,SAAS;;;;;;;;"}
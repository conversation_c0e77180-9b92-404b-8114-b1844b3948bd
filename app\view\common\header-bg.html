<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <title>{$pageTitle|default='首页'} - {$siteConfig.site_name|default='三只鱼网络'}</title>
    <meta name="description" content="{$pageDescription|default=$siteConfig.site_description|default='专注于为企业提供专业、可靠、高效的数字化转型解决方案'}">
    <meta name="keywords" content="{$pageKeywords|default=$siteConfig.site_keywords|default='数字化转型,企业解决方案,技术服务,创新科技,专业团队'}">
    
    <!-- Open Graph -->
    <meta property="og:title" content="{$pageTitle|default=$siteConfig.site_name|default='三只鱼网络'}">
    <meta property="og:description" content="{$pageDescription|default=$siteConfig.site_description|default='专注于为企业提供专业、可靠、高效的数字化转型解决方案'}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{$Request.domain}">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{:asset('assets/images/favicon.ico')}">
    
    <!-- CSS -->
    <link rel="stylesheet" href="{:asset('assets/css/bootstrap.min.css')}">
    <link rel="stylesheet" href="{:asset('assets/css/all.min.css')}">
    <link rel="stylesheet" href="{:asset('assets/css/animate.css')}">
    <link rel="stylesheet" href="{:asset('assets/css/swiper-bundle.min.css')}">
    <link rel="stylesheet" href="{:asset('assets/css/style.css')}">
    <link rel="stylesheet" href="{:asset('assets/css/contact.css')}">

    <!-- Lucide Icons - 本地版本 -->
    <script src="{:asset('assets/js/lucide.js')}"></script>
    
    <!-- 导航菜单层级修复 -->
    <link rel="stylesheet" href="{:asset('assets/css/nav-fix.css')}">

    <!-- 导航栏样式 -->
    <style>
    /* ===== 下拉菜单基础样式 ===== */
    .dropdown-menu {
        background: #ffffff;
        border: none;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        border-radius: 12px;
        padding: 12px 0;
    }

    .dropdown-item {
        padding: 10px 20px;
        font-size: 14px;
        transition: all 0.2s ease;
        border: none;
        background: transparent;
    }

    .dropdown-item:hover {
        background: rgba(0, 123, 255, 0.08);
        color: #007bff;
    }

    /* ===== 大型下拉菜单容器 ===== */
    .mega-dropdown {
        position: relative;
    }

    .mega-dropdown .dropdown-menu {
        display: none;
        position: fixed;
        top: 80px;
        left: 50%;
        transform: translateX(-50%);
        margin: 0;
        border-radius: 16px;
        background: white;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(0, 0, 0, 0.08);
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        pointer-events: none;
        width: 1200px;
        max-width: 90vw;
        max-height: 80vh;
        overflow-y: auto;
    }

    /* 下拉菜单显示状态 - 通过JavaScript控制 */
    .mega-dropdown.show .dropdown-menu {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: auto !important;
    }

    /* 确保菜单内容有正确的显示状态 */
    .mega-dropdown .dropdown-menu {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
        pointer-events: none !important;
    }

    /* 当菜单被JavaScript激活时的样式 */
    .mega-dropdown .dropdown-menu[style*="display: block"] {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: auto !important;
    }

    /* 增加菜单和触发器之间的连接区域 */
    .mega-dropdown::before {
        content: '';
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        height: 20px;
        background: transparent;
        z-index: 9999;
        pointer-events: auto;
    }

    /* ===== 简单可靠的三角形指示器 ===== */
    .mega-dropdown .dropdown-menu::before {
        content: '';
        position: absolute;
        top: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-bottom: 8px solid white;
        z-index: 1001;
    }

    /* 三角形阴影 */
    .mega-dropdown .dropdown-menu::after {
        content: '';
        position: absolute;
        top: -9px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 9px solid transparent;
        border-right: 9px solid transparent;
        border-bottom: 9px solid rgba(0, 0, 0, 0.1);
        z-index: 1000;
    }

    /* ===== 下拉箭头指示器 ===== */
    .dropdown-toggle::after {
        display: inline-block;
        margin-left: 6px;
        vertical-align: middle;
        content: "▼";
        font-size: 10px;
        transition: transform 0.3s ease;
        line-height: 1;
        color: rgba(255, 255, 255, 0.8);
    }

    .dropdown-toggle[aria-expanded="true"]::after,
    .mega-dropdown.show .dropdown-toggle::after {
        transform: rotate(180deg);
    }

    /* 解决方案菜单特殊样式 */
    .solutions-menu {
        padding: 0;
    }

    .solutions-menu .mega-menu-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 25px 30px;
        text-align: center;
        color: white;
        border-radius: 16px 16px 0 0;
    }

    .solutions-menu .mega-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 8px;
        color: white;
    }

    .solutions-menu .mega-subtitle {
        font-size: 0.9rem;
        opacity: 0.9;
        margin: 0;
        color: white;
    }

    /* 解决方案网格布局 */
    .solutions-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 15px;
        padding: 20px;
        background: white;
    }

    .solution-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 15px 12px;
        border-radius: 12px;
        transition: all 0.3s ease;
        cursor: pointer;
        text-decoration: none;
        color: inherit;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        min-height: 100px;
        justify-content: flex-start;
    }

    .solution-item:hover {
        background: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.12);
        text-decoration: none;
        color: inherit;
        border-color: rgba(102, 126, 234, 0.3);
    }

    .solution-item .solution-icon {
        width: 50px;
        height: 50px;
        background: transparent;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 8px;
        transition: all 0.3s ease;
        flex-shrink: 0;
    }

    .solution-item .solution-icon img {
        width: 36px;
        height: 36px;
        object-fit: contain;
    }

    .solution-item:hover .solution-icon {
        transform: scale(1.1);
    }

    .solution-item .solution-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 100%;
    }

    .solution-item .solution-content h4 {
        font-size: 0.9rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
        line-height: 1.3;
    }

    .solution-item .solution-content p {
        font-size: 0.75rem;
        color: #666;
        line-height: 1.3;
        margin: 0;
    }

    /* 菜单底部 */
    .mega-menu-footer {
        text-align: center;
        padding-top: 25px;
        border-top: 1px solid rgba(0, 0, 0, 0.08);
        margin-top: 20px;
    }

    .mega-menu-footer .btn {
        padding: 12px 30px;
        font-size: 0.95rem;
        font-weight: 600;
        border-radius: 25px;
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        color: white;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.25);
    }

    .mega-menu-footer .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(0, 123, 255, 0.35);
        background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
        text-decoration: none;
        color: white;
    }

    /* ===== 产品介绍菜单样式 ===== */
    .products-menu {
        padding: 0;
        z-index: 10000 !important; /* 比解决方案菜单更高的z-index */
    }

    .products-header {
        background: #f8f9fa;
        padding: 25px 30px;
        border-bottom: 1px solid #e9ecef;
        border-radius: 16px 16px 0 0;
    }

    /* 产品网格布局 */
    .products-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 25px;
        padding: 30px;
        background: white;
        min-width: 800px;
    }

    .products-column {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .product-item {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        border-radius: 10px;
        transition: all 0.3s ease;
        cursor: pointer;
        background: white;
        border: 1px solid rgba(102, 126, 234, 0.1);
        text-decoration: none;
        color: inherit;
        margin-bottom: 8px;
    }

    .product-item:hover {
        background: rgba(102, 126, 234, 0.12);
        border-color: rgba(102, 126, 234, 0.25);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
        text-decoration: none;
        color: inherit;
    }

    .product-icon {
        width: 35px;
        height: 35px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        flex-shrink: 0;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .product-icon.purple {
        background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
        color: white;
    }

    .product-icon i {
        font-size: 18px;
        color: white;
        line-height: 1;
        width: auto;
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    /* Lucide图标样式 */
    .product-icon svg {
        width: 18px;
        height: 18px;
        color: white;
        stroke: white;
        stroke-width: 2;
    }

    .product-item:hover .product-icon {
        transform: scale(1.1);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.35);
    }

    .product-item:hover .product-icon i {
        transform: translate(-50%, -50%) scale(1);
    }

    .product-name {
        font-size: 0.9rem;
        font-weight: 500;
        color: #333;
        line-height: 1.4;
        transition: color 0.3s ease;
    }

    .product-item:hover .product-name {
        color: #667eea;
    }

    .product-item-placeholder {
        padding: 12px 15px;
        color: #999;
        font-size: 0.9rem;
        text-align: center;
        font-style: italic;
    }

    /* ===== 菜单分类标题 ===== */
    .mega-menu-category {
        font-size: 1.1rem;
        font-weight: 600;
        color: #007bff;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 2px solid rgba(0, 123, 255, 0.2);
        position: relative;
        text-align: center;
    }

    .mega-menu-category::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 2px;
        background: #007bff;
        border-radius: 1px;
    }

    /* 响应式调整 */
    @media (max-width: 1200px) {
        .products-grid {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    @media (max-width: 900px) {
        .products-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 600px) {
        .products-grid {
            grid-template-columns: 1fr;
        }
    }

    /* ===== 导航栏基础样式增强 ===== */
    .header {
        position: relative;
        z-index: 1000;
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        backdrop-filter: blur(10px);
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.15);
    }

    .navbar {
        position: relative;
        z-index: 1001;
        background: #2f4c99f5 !important
    }

    /* 导航链接样式调整 */
    .navbar-nav .nav-link {
        color: rgba(255, 255, 255, 0.9) !important;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .navbar-nav .nav-link:hover {
        color: #ffffff !important;
        text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
    }

    .navbar-brand img {
        filter: brightness(1.1);
    }

    /* 按钮样式调整 */
    .btn-outline-light {
        border-color: rgba(255, 255, 255, 0.3);
        color: rgba(255, 255, 255, 0.9);
    }

    .btn-outline-light:hover {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.5);
        color: #ffffff;
    }

    .btn-primary {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
        color: #ffffff;
    }

    .btn-primary:hover {
        background: rgba(255, 255, 255, 0.25);
        border-color: rgba(255, 255, 255, 0.5);
        color: #ffffff;
    }

    /* 移动端导航切换按钮 */
    .navbar-toggler {
        border-color: rgba(255, 255, 255, 0.3);
    }

    .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    }

    .navbar-toggler:focus {
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
    }

    /* 确保所有下拉菜单都有足够高的z-index */
    .mega-dropdown .dropdown-menu {
        z-index: 10001 !important;
    }

    .solutions-menu {
        z-index: 10002 !important;
    }

    .products-menu {
        z-index: 10003 !important;
    }

    /* 确保三角形指示器也有正确的z-index */
    .mega-dropdown .dropdown-menu::before {
        z-index: 10004;
    }

    .mega-dropdown .dropdown-menu::after {
        z-index: 10003;
    }

/* 产品网格布局 */
.products-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
    padding: 30px;
}

.products-column {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.product-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-radius: 10px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
    border: 1px solid rgba(102, 126, 234, 0.1);
    text-decoration: none;
    color: inherit;
}

.product-item:hover {
    background: rgba(102, 126, 234, 0.12);
    border-color: rgba(102, 126, 234, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
    text-decoration: none;
    color: inherit;
}

.product-icon {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.product-icon.purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
}

.product-icon i {
    font-size: 18px;
    color: white;
}    
    </style>
</head>
<body class="{$bodyClass|default='home-page'}">
    <!-- 导航栏 -->
    <header class="header">
        <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="/">
                <img src="{:asset('assets/images/logo.png')}" alt="{$siteConfig.site_name|default='三只鱼网络'}" height="40">
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto" id="main-nav">
                    <!-- 动态菜单将在这里加载 -->
                </ul>

                <!-- 右侧按钮组 -->
                <div class="navbar-actions ms-3">
                    <a href="#" class="search-btn me-2"><i data-lucide="search"></i></a>
                    <a href="/login" class="btn btn-outline-light btn-sm me-2">登录</a>
                    <a href="/register" class="btn btn-primary btn-sm">注册</a>
                </div>
            </div>
        </div>
    </nav>
</header>

    <!-- 主要内容 -->
    <main>

<script>
// 菜单加载 - 添加调试信息

// URL格式化函数
function formatUrl(url) {
    if (!url || url === '#') {
        return '#';
    }

    // 转换为字符串并清理
    url = String(url).trim();

    // 如果是完整的URL（包含协议），直接返回
    if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
    }

    // 如果已经是正确的相对路径（以/开头），直接返回
    if (url.startsWith('/')) {
        return url;
    }

    // 如果是相对路径，添加/前缀
    return '/' + url;
}

document.addEventListener('DOMContentLoaded', function() {
    loadMenus();
});

function loadMenus() {
    fetch('/api/sys-menus')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data && data.success && data.data) {
                renderMenus(data.data);
            } else {
            }
        })
        .catch(error => {
            console.error('菜单加载失败:', error);
            console.error('错误详情:', error.message);
        });
}

function renderMenus(menus) {
    const nav = document.getElementById('main-nav');
    if (!nav) {
        console.error('找不到导航容器 #main-nav');
        return;
    }

    // 清空现有菜单（保留首页）
    const existing = nav.querySelectorAll('li:not(:first-child)');
    existing.forEach(item => item.remove());

    // 添加新菜单
    let hasMorePages = false;
    let hasContact = false;
    
    menus.forEach((menu, index) => {
        const li = createMenuItem(menu);
        nav.appendChild(li);

        // 检查是否已经有"更多页面"和"联系我们"菜单
        if (menu.name === '更多页面') {
            hasMorePages = true;
            // 为动态的"更多页面"菜单添加id，以便DIY页面加载
            const dropdownMenu = li.querySelector('.dropdown-menu');
            if (dropdownMenu && !dropdownMenu.id) {
                dropdownMenu.id = 'diy-pages-menu';
            }
            // 检查是否有DIY页面内容，没有的话隐藏这个菜单项
            checkDiyPagesAndToggleMenu(li);
        }
        if (menu.name === '联系我们') {
            hasContact = true;
        }
    });

    // 只有当动态菜单中没有"更多页面"时才检查是否需要添加固定的"更多页面"菜单
    if (!hasMorePages) {
        // 先检查是否有DIY页面，有的话才添加"更多页面"菜单
        checkAndAddMorePagesMenu(nav);
    }

    // 只有当动态菜单中没有"联系我们"时才添加固定的"联系我们"菜单
    if (!hasContact) {
        const contact = document.createElement('li');
        contact.className = 'nav-item';
        contact.innerHTML = '<a class="nav-link" href="/contact">联系我们</a>';
        nav.appendChild(contact);
    }


    // 初始化下拉菜单事件
    initDropdownEvents();

    // 确保Lucide图标正确渲染
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

}

function createMenuItem(menu) {
    const li = document.createElement('li');
    const url = formatUrl(menu.url || menu.link_value || '#');

    if (menu.children && menu.children.length > 0) {
        // 检查是否是特殊的mega-dropdown菜单
        if (menu.name === '解决方案') {
            return createSolutionsMenuItem(menu, url);
        } else if (menu.name === '产品介绍') {
            return createProductsMenuItem(menu, url);
        } else {
            // 普通下拉菜单
            li.className = 'nav-item dropdown';

            // 创建主链接
            const mainLink = document.createElement('a');
            mainLink.className = 'nav-link dropdown-toggle';
            mainLink.href = url;
            mainLink.setAttribute('role', 'button');
            mainLink.setAttribute('aria-expanded', 'false');
            mainLink.textContent = menu.name;

            // 创建下拉菜单
            const dropdownMenu = document.createElement('ul');
            dropdownMenu.className = 'dropdown-menu';
            dropdownMenu.innerHTML = renderSubMenus(menu.children);

            li.appendChild(mainLink);
            li.appendChild(dropdownMenu);

            // 添加悬停事件
            li.addEventListener('mouseenter', function() {
                const dropdown = li.querySelector('.dropdown-menu');
                if (dropdown) {
                    dropdown.style.display = 'block';
                    li.classList.add('show');
                }
            });

            li.addEventListener('mouseleave', function() {
                const dropdown = li.querySelector('.dropdown-menu');
                if (dropdown) {
                    dropdown.style.display = 'none';
                    li.classList.remove('show');
                }
            });

        }
    } else {
        // 普通菜单项
        li.className = 'nav-item';

        const link = document.createElement('a');
        link.className = 'nav-link';
        link.href = url;
        link.textContent = menu.name;
        li.appendChild(link);
    }

    return li;
}

// 创建解决方案菜单项
function createSolutionsMenuItem(menu, url) {
    const li = document.createElement('li');
    li.className = 'nav-item mega-dropdown';

    const mainLink = document.createElement('a');
    mainLink.className = 'nav-link dropdown-toggle';
    mainLink.href = url;
    mainLink.id = 'solutionsDropdown';
    mainLink.setAttribute('role', 'button');
    mainLink.setAttribute('aria-expanded', 'false');
    mainLink.textContent = menu.name;

    const dropdownMenu = document.createElement('div');
    dropdownMenu.className = 'dropdown-menu solutions-menu';
    
    // 动态生成解决方案网格
    let solutionsHtml = '<div class="solutions-grid">';
    
    if (menu.children && menu.children.length > 0) {
        // 使用真实的子菜单数据
        menu.children.forEach(solution => {
            const solutionUrl = formatUrl(solution.url || solution.link_value || '#');
            const iconPath = solution.icon || '/assets/images/nav/default.png';
            const description = solution.description || solution.remark || '专业的解决方案';
            
            solutionsHtml += `
                <a href="${solutionUrl}" class="solution-item">
                    <div class="solution-icon">
                        <img src="${iconPath}" alt="${solution.name}">
                    </div>
                    <div class="solution-content">
                        <h4>${solution.name}</h4>
                        <p>${description}</p>
                    </div>
                </a>
            `;
        });
    }
    
    solutionsHtml += '</div>';
    dropdownMenu.innerHTML = solutionsHtml;

    li.appendChild(mainLink);
    li.appendChild(dropdownMenu);

    // 添加mega-dropdown悬停事件
    addMegaDropdownEvents(li);

    return li;
}

// 创建产品介绍菜单项
function createProductsMenuItem(menu, url) {
    const li = document.createElement('li');
    li.className = 'nav-item mega-dropdown';

    const mainLink = document.createElement('a');
    mainLink.className = 'nav-link dropdown-toggle';
    mainLink.href = url;
    mainLink.id = 'productsDropdown';
    mainLink.setAttribute('role', 'button');
    mainLink.setAttribute('aria-expanded', 'false');
    mainLink.textContent = menu.name;

    const dropdownMenu = document.createElement('div');
    dropdownMenu.className = 'dropdown-menu products-menu';
    
    // 动态生成产品网格
    let productsHtml = '<div class="products-grid">';
    
    if (menu.children && menu.children.length > 0) {
        // 使用真实的子菜单数据，每个子菜单作为一个分类
        menu.children.forEach(category => {
            productsHtml += '<div class="products-column">';
            productsHtml += `<h6 class="mega-menu-category">${category.name}</h6>`;
            
            // 如果分类有子项目（产品），显示产品列表
            if (category.children && category.children.length > 0) {
                category.children.forEach(product => {
                    const productUrl = formatUrl(product.url || product.link_value || '#');
                    const productIcon = product.icon || 'fas fa-box';
                    
                    productsHtml += `
                        <a href="${productUrl}" class="product-item">
                            <div class="product-icon purple">
                                <i class="${productIcon}"></i>
                            </div>
                            <span class="product-name">${product.name}</span>
                        </a>
                    `;
                });
            } else {
                // 如果分类没有子产品，将分类本身作为产品显示
                const categoryUrl = formatUrl(category.url || category.link_value || '#');
                const categoryIcon = category.icon || 'fas fa-box';
                
                productsHtml += `
                    <a href="${categoryUrl}" class="product-item">
                        <div class="product-icon purple">
                            <i class="${categoryIcon}"></i>
                        </div>
                        <span class="product-name">${category.name}</span>
                    </a>
                `;
            }
            
            productsHtml += '</div>';
        });
    }
    
    productsHtml += '</div>';
    dropdownMenu.innerHTML = productsHtml;

    li.appendChild(mainLink);
    li.appendChild(dropdownMenu);

    // 添加mega-dropdown悬停事件
    addMegaDropdownEvents(li);

    return li;
}

// 添加mega-dropdown悬停事件
function addMegaDropdownEvents(dropdown) {
        const menu = dropdown.querySelector('.dropdown-menu');
        const toggle = dropdown.querySelector('.dropdown-toggle');
        let hoverTimer = null;
        let isMenuHovered = false;
        let isToggleHovered = false;
        
        // 显示菜单的函数
        function showMenu() {
            if (menu) {
                clearTimeout(hoverTimer);
                menu.style.display = 'block';
                menu.style.opacity = '1';
                menu.style.visibility = 'visible';
                menu.style.pointerEvents = 'auto';
                dropdown.classList.add('show');
                toggle.setAttribute('aria-expanded', 'true');
            }
        }
        
        // 隐藏菜单的函数
        function hideMenu() {
            if (menu) {
                hoverTimer = setTimeout(function() {
                    if (!isMenuHovered && !isToggleHovered) {
                        menu.style.display = 'none';
                        menu.style.opacity = '0';
                        menu.style.visibility = 'hidden';
                        menu.style.pointerEvents = 'none';
                        dropdown.classList.remove('show');
                        toggle.setAttribute('aria-expanded', 'false');
                    }
                }, 150);
            }
        }
        
        // 触发器悬停事件
        toggle.addEventListener('mouseenter', function() {
            isToggleHovered = true;
            showMenu();
        });
        
        toggle.addEventListener('mouseleave', function() {
            isToggleHovered = false;
            hideMenu();
        });
        
        // 菜单悬停事件
        if (menu) {
            menu.addEventListener('mouseenter', function() {
                isMenuHovered = true;
                clearTimeout(hoverTimer);
            });
            
            menu.addEventListener('mouseleave', function() {
                isMenuHovered = false;
                hideMenu();
            });
        }
        
        // 点击触发器时阻止默认行为
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            if (menu.style.display === 'block') {
                hideMenu();
                isToggleHovered = false;
                isMenuHovered = false;
            } else {
                showMenu();
            }
        });
}

function renderSubMenus(children) {
    const items = children.map(child => {
        const url = formatUrl(child.url || child.link_value || '#');

        const li = document.createElement('li');

        if (child.children && child.children.length > 0) {
            // 有三级菜单的二级菜单项
            li.className = 'dropdown-submenu';

            const link = document.createElement('a');
            link.className = 'dropdown-item';
            link.href = url;
            link.textContent = child.name + ' ▶';

            const submenu = document.createElement('ul');
            submenu.className = 'dropdown-menu submenu';

            child.children.forEach(grandChild => {
                const grandUrl = formatUrl(grandChild.url || grandChild.link_value || '#');
                const grandLi = document.createElement('li');
                const grandLink = document.createElement('a');
                grandLink.className = 'dropdown-item';
                grandLink.href = grandUrl;
                grandLink.textContent = grandChild.name;
                grandLi.appendChild(grandLink);
                submenu.appendChild(grandLi);

            });

            li.appendChild(link);
            li.appendChild(submenu);

        } else {
            // 普通二级菜单项
            const link = document.createElement('a');
            link.className = 'dropdown-item';
            link.href = url;
            link.textContent = child.name;
            li.appendChild(link);

        }

        return li.outerHTML;
    });

    return items.join('');
}

function initDropdownEvents() {
    // Bootstrap下拉菜单自动处理，但需要处理三级菜单
    document.querySelectorAll('.dropdown-submenu').forEach(submenu => {
        const toggle = submenu.querySelector('.dropdown-item');
        const menu = submenu.querySelector('.dropdown-menu');

        if (toggle && menu) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // 切换显示状态
                menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
            });

            // 鼠标悬停显示三级菜单
            submenu.addEventListener('mouseenter', function() {
                menu.style.display = 'block';
            });

            submenu.addEventListener('mouseleave', function() {
                menu.style.display = 'none';
            });
        }
    });

    // 点击页面其他地方时关闭所有mega-dropdown菜单
    document.addEventListener('click', function(e) {
        // 检查点击的目标是否在下拉菜单内
        const isDropdownClick = e.target.closest('.mega-dropdown');
        
        if (!isDropdownClick) {
            const megaDropdowns = document.querySelectorAll('.mega-dropdown');
            megaDropdowns.forEach(function(dropdown) {
                const menu = dropdown.querySelector('.dropdown-menu');
                const toggle = dropdown.querySelector('.dropdown-toggle');
                
                if (menu) {
                    menu.style.display = 'none';
                    menu.style.opacity = '0';
                    menu.style.visibility = 'hidden';
                    menu.style.pointerEvents = 'none';
                    dropdown.classList.remove('show');
                    toggle.setAttribute('aria-expanded', 'false');
                }
            });
        }
    });
    }

// 检查DIY页面并切换菜单显示
function checkDiyPagesAndToggleMenu(menuItem) {
    fetch('/api/diy-pages')
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            // 如果没有DIY页面，隐藏菜单项
            if (!data.success || !data.pages || data.pages.length === 0) {
                menuItem.style.display = 'none';
            } else {
                // 有DIY页面，加载内容
                loadDiyPagesToMenu();
            }
        })
        .catch(error => {
            console.error('检查DIY页面失败:', error);
            // 检查失败时隐藏菜单项
            menuItem.style.display = 'none';
        });
}

// 检查并添加"更多页面"菜单
function checkAndAddMorePagesMenu(nav) {
    // 先检查是否有DIY页面
    fetch('/api/diy-pages')
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            // 只有当有DIY页面时才添加"更多页面"菜单
            if (data.success && data.pages && data.pages.length > 0) {
                const morePages = document.createElement('li');
                morePages.className = 'nav-item dropdown';
                morePages.innerHTML = `
                    <a class="nav-link dropdown-toggle" href="#" id="diyPagesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        更多页面
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="diyPagesDropdown" id="diy-pages-menu">
                    </ul>
                `;
                nav.appendChild(morePages);

                // 为"更多页面"添加悬停事件
                morePages.addEventListener('mouseenter', function() {
                    const dropdown = morePages.querySelector('.dropdown-menu');
                    if (dropdown) {
                        dropdown.style.display = 'block';
                        morePages.classList.add('show');
                    }
                });

                morePages.addEventListener('mouseleave', function() {
                    const dropdown = morePages.querySelector('.dropdown-menu');
                    if (dropdown) {
                        dropdown.style.display = 'none';
                        morePages.classList.remove('show');
                    }
                });

                // 加载DIY页面内容
                loadDiyPagesToMenu();
            }
        })
        .catch(error => {
            console.error('检查DIY页面失败:', error);
            // 如果检查失败，不添加"更多页面"菜单
        });
}

// 加载DIY页面到导航菜单
function loadDiyPagesToMenu() {

    // 获取菜单容器
    const menuContainer = document.getElementById('diy-pages-menu');
    if (!menuContainer) {
        console.error('未找到DIY页面菜单容器');
        return;
    }

    // 发起API请求
    fetch('/api/diy-pages')
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败: ' + response.status);
            }
            return response.json();
        })
        .then(data => {

            if (data.success && data.pages && data.pages.length > 0) {
                // 清空现有的DIY页面项（保留默认的"关于我们"）
                const existingDiyItems = menuContainer.querySelectorAll('.diy-page-item');
                existingDiyItems.forEach(item => item.remove());

                // 不添加分隔线，保持菜单简洁

                // 添加DIY页面链接
                data.pages.forEach(page => {
                    const li = document.createElement('li');
                    li.className = 'diy-page-item';
                    li.innerHTML = `
                        <a class="dropdown-item" href="/page/${page.slug}" title="${page.description || page.name}">
                            <i class="fas fa-file-alt me-2"></i>
                            ${page.name}
                        </a>
                    `;
                    menuContainer.appendChild(li);
                });

            } else {
            }
        })
        .catch(error => {
            console.error('加载DIY页面失败:', error);

            // 在菜单中显示错误提示（仅在开发环境）
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                const li = document.createElement('li');
                li.innerHTML = '<span class="dropdown-item text-muted">加载页面失败</span>';
                menuContainer.appendChild(li);
            }
        });
}
</script>


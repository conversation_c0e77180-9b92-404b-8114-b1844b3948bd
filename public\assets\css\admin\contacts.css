/* 联系表单管理专用样式 - 深色科技风 */
.contacts-container {
    background: rgba(15, 15, 15, 0.95);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 20px;
    backdrop-filter: blur(25px);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.4),
        0 0 40px rgba(120, 119, 198, 0.1);
    overflow: hidden;
    position: relative;
    margin-bottom: 30px;
}

.contacts-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
        rgba(120, 119, 198, 0.8),
        rgba(255, 119, 198, 0.8),
        rgba(120, 219, 255, 0.8));
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* 列表头部样式 */
.list-header {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.15) 0%,
        rgba(255, 119, 198, 0.1) 50%,
        rgba(120, 219, 255, 0.15) 100%);
    padding: 30px;
    border-bottom: 1px solid rgba(120, 119, 198, 0.2);
}

.list-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.list-title-section {
    display: flex;
    align-items: center;
    gap: 20px;
}

.list-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.2) 0%,
        rgba(255, 119, 198, 0.2) 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(120, 119, 198, 0.3);
}

.list-icon i {
    font-size: 24px;
    color: rgba(120, 119, 198, 1);
}

.list-title-text h4 {
    margin: 0 0 8px 0;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    color: #fff;
    font-size: 1.5rem;
    text-shadow: 0 0 20px rgba(120, 119, 198, 0.6);
}

.list-subtitle {
    margin: 0;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.95rem;
    line-height: 1.4;
}

/* 状态统计样式 */
.status-stats {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

.stat-item {
    background: rgba(20, 20, 30, 0.8);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 12px;
    padding: 15px 20px;
    text-align: center;
    min-width: 120px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.2);
    border-color: rgba(120, 119, 198, 0.5);
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: #fff;
    font-family: 'Orbitron', monospace;
    text-shadow: 0 0 10px rgba(120, 119, 198, 0.5);
}

.stat-label {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 搜索和筛选样式 */
.search-filters {
    background: rgba(20, 20, 30, 0.6);
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 25px;
    border: 1px solid rgba(120, 119, 198, 0.2);
}

.filter-row {
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 8px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    font-size: 0.9rem;
}

.filter-group input,
.filter-group select {
    width: 100%;
    background: rgba(15, 15, 15, 0.8) !important;
    border: 1px solid rgba(120, 119, 198, 0.3) !important;
    border-radius: 8px !important;
    color: rgba(255, 255, 255, 0.9) !important;
    padding: 12px 15px !important;
    transition: all 0.3s ease !important;
}

.filter-group input:focus,
.filter-group select:focus {
    border-color: rgba(120, 119, 198, 0.6) !important;
    box-shadow: 0 0 15px rgba(120, 119, 198, 0.2) !important;
    outline: none !important;
}

.filter-actions {
    display: flex;
    gap: 10px;
    align-items: end;
}

.btn-filter {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8) 0%,
        rgba(255, 119, 198, 0.8) 100%) !important;
    border: 1px solid rgba(120, 119, 198, 0.5) !important;
    color: #fff !important;
    padding: 12px 20px !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.btn-filter:hover {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 1) 0%,
        rgba(255, 119, 198, 1) 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.4);
}

.btn-clear {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: rgba(255, 255, 255, 0.8) !important;
}

.btn-clear:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    color: #fff !important;
}

/* 联系表单卡片样式 */
.contacts-list {
    display: grid;
    gap: 20px;
}

.contact-card {
    background: rgba(20, 20, 30, 0.8);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 15px;
    padding: 25px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg,
        rgba(120, 119, 198, 0.8) 0%,
        rgba(255, 119, 198, 0.8) 50%,
        rgba(120, 219, 255, 0.8) 100%);
}

.contact-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(120, 119, 198, 0.2);
    border-color: rgba(120, 119, 198, 0.5);
}

.contact-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.contact-info {
    flex: 1;
}

.contact-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: #fff;
    margin: 0 0 8px 0;
    font-family: 'Orbitron', monospace;
}

.contact-email {
    color: rgba(120, 119, 198, 1);
    font-size: 0.95rem;
    margin: 0 0 5px 0;
}

.contact-phone {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin: 0;
}

.contact-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-new {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-read {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.status-replied {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-closed {
    background: rgba(107, 114, 128, 0.2);
    color: #6b7280;
    border: 1px solid rgba(107, 114, 128, 0.3);
}

.contact-subject {
    font-size: 1rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    margin: 0 0 15px 0;
    line-height: 1.4;
}

.contact-message {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0 0 20px 0;
    max-height: 60px;
    overflow: hidden;
    position: relative;
}

.contact-message::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(transparent, rgba(20, 20, 30, 0.8));
}

.contact-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-top: 15px;
    border-top: 1px solid rgba(120, 119, 198, 0.2);
    flex-wrap: wrap;
    gap: 10px;
}

.contact-date {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.contact-company {
    color: rgba(255, 119, 198, 0.8);
    font-size: 0.9rem;
    font-weight: 500;
}

.contact-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-action {
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    border: 1px solid;
}

/* 联系表单操作按钮样式 - 确保与admin.css统一 */
.btn-action {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 14px;
    border: 1px solid;
    border-radius: 10px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    min-width: 75px;
    justify-content: center;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent);
    transition: left 0.5s ease;
}

.btn-action:hover::before {
    left: 100%;
}

.btn-action i {
    transition: transform 0.3s ease;
    filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.3));
}

.btn-action:hover i {
    transform: scale(1.1);
}

/* 查看按钮 - 统一绿色渐变 */
.btn-action.btn-view, .btn-view {
    background: linear-gradient(135deg,
        rgba(34, 197, 94, 0.8) 0%,
        rgba(16, 185, 129, 0.8) 50%,
        rgba(5, 150, 105, 0.8) 100%) !important;
    border-color: rgba(34, 197, 94, 0.6) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.btn-action.btn-view:hover, .btn-view:hover {
    background: linear-gradient(135deg,
        rgba(34, 197, 94, 1) 0%,
        rgba(16, 185, 129, 1) 50%,
        rgba(5, 150, 105, 1) 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
    color: #ffffff !important;
    text-decoration: none;
}

/* 编辑按钮 - 统一蓝色渐变 */
.btn-action.btn-edit, .btn-edit {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.8) 0%,
        rgba(37, 99, 235, 0.8) 50%,
        rgba(29, 78, 216, 0.8) 100%) !important;
    border-color: rgba(59, 130, 246, 0.6) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.btn-action.btn-edit:hover, .btn-edit:hover {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 1) 0%,
        rgba(37, 99, 235, 1) 50%,
        rgba(29, 78, 216, 1) 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    color: #ffffff !important;
    text-decoration: none;
}

/* 删除按钮 - 统一红色渐变 */
.btn-action.btn-delete, .btn-delete {
    background: linear-gradient(135deg,
        rgba(239, 68, 68, 0.8) 0%,
        rgba(220, 38, 38, 0.8) 50%,
        rgba(185, 28, 28, 0.8) 100%) !important;
    border-color: rgba(239, 68, 68, 0.6) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.btn-action.btn-delete:hover, .btn-delete:hover {
    background: linear-gradient(135deg,
        rgba(239, 68, 68, 1) 0%,
        rgba(220, 38, 38, 1) 50%,
        rgba(185, 28, 28, 1) 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
    color: #ffffff !important;
    text-decoration: none;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: rgba(255, 255, 255, 0.6);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 10px 0;
    color: rgba(255, 255, 255, 0.8);
}

.empty-subtitle {
    font-size: 1rem;
    margin: 0;
    line-height: 1.5;
}

    /* 详情页面样式 */
    .contact-detail {
        max-width: 100%;
    }
    
    .detail-section {
        background: rgba(20, 20, 30, 0.6);
        border: 1px solid rgba(120, 119, 198, 0.3);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        backdrop-filter: blur(10px);
    }
    
    .section-title {
        color: #fff;
        font-family: 'Orbitron', monospace;
        font-weight: 600;
        margin: 0 0 20px 0;
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 1.1rem;
        text-shadow: 0 0 10px rgba(120, 119, 198, 0.5);
    }
    
    .section-title i {
        color: rgba(120, 119, 198, 1);
    }
    
    .detail-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }
    
    .detail-item {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    
    .detail-item.full-width {
        grid-column: 1 / -1;
    }
    
    .detail-item label {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .detail-value {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1rem;
        line-height: 1.5;
        padding: 12px 15px;
        background: rgba(15, 15, 15, 0.6);
        border: 1px solid rgba(120, 119, 198, 0.2);
        border-radius: 8px;
    }
    
    .subject-value {
        font-weight: 500;
        color: #fff;
    }
    
    .message-value {
        min-height: 100px;
        white-space: pre-wrap;
    }
    
    .reply-value {
        background: rgba(34, 197, 94, 0.1);
        border-color: rgba(34, 197, 94, 0.3);
        color: rgba(34, 197, 94, 1);
    }
    
    .user-agent {
        font-size: 0.85rem;
        color: rgba(255, 255, 255, 0.6);
        word-break: break-all;
    }
    
    .email-link, .phone-link {
        color: rgba(120, 119, 198, 1);
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .email-link:hover, .phone-link:hover {
        color: rgba(255, 119, 198, 1);
        text-decoration: underline;
    }
    
    .detail-actions {
        display: flex;
        gap: 15px;
        margin-top: 30px;
        flex-wrap: wrap;
    }
    
    /* 编辑表单样式 */
    .original-info {
        background: rgba(15, 15, 15, 0.6);
        border: 1px solid rgba(120, 119, 198, 0.2);
        border-radius: 10px;
        padding: 20px;
    }
    
    .info-row {
        display: flex;
        margin-bottom: 12px;
        align-items: flex-start;
        gap: 10px;
    }
    
    .info-row.full-width {
        flex-direction: column;
        gap: 8px;
    }
    
    .info-label {
        color: rgba(255, 255, 255, 0.7);
        font-weight: 500;
        min-width: 80px;
        font-size: 0.9rem;
    }
    
    .info-value {
        color: rgba(255, 255, 255, 0.9);
        flex: 1;
    }
    
    .info-message {
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.5;
        white-space: pre-wrap;
        background: rgba(20, 20, 30, 0.8);
        padding: 15px;
        border-radius: 8px;
        border: 1px solid rgba(120, 119, 198, 0.2);
    }
    
    .edit-form {
        max-width: 100%;
    }
    
    .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .form-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    
    .form-group label {
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
        font-size: 0.95rem;
    }
    
    .form-text {
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.85rem;
        margin-top: 5px;
    }
    
    .form-actions {
        display: flex;
        gap: 15px;
        margin-top: 25px;
        flex-wrap: wrap;
    }
    
    /* 自定义删除确认模态框样式已移至 admin.css 统一管理 */



    /* 回复确认模态框特殊样式 */
    .reply-icon {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
        box-shadow: 0 10px 30px rgba(59, 130, 246, 0.4) !important;
        animation: pulse-info 2s ease-in-out infinite !important;
    }

    @keyframes pulse-info {
        0%, 100% {
            transform: scale(1);
            box-shadow: 0 10px 30px rgba(59, 130, 246, 0.4);
        }
        50% {
            transform: scale(1.05);
            box-shadow: 0 15px 40px rgba(59, 130, 246, 0.6);
        }
    }

    .reply-warning {
        background: rgba(59, 130, 246, 0.1) !important;
        border-color: rgba(59, 130, 246, 0.3) !important;
        color: rgba(59, 130, 246, 0.9) !important;
    }

    .reply-confirm {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
        border-color: rgba(59, 130, 246, 0.6) !important;
        box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3) !important;
    }

    .reply-confirm:hover {
        background: linear-gradient(135deg, #2563eb, #1e40af) !important;
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4) !important;
    }

    /* 回复模态框特殊样式 */
    .reply-modal {
        max-width: 600px !important;
    }

    .current-status-info {
        background: rgba(15, 15, 15, 0.8);
        border: 1px solid rgba(120, 119, 198, 0.3);
        border-radius: 10px;
        padding: 15px;
        margin: 15px 0;
    }

    .status-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }

    .status-row:last-child {
        margin-bottom: 0;
    }

    .status-label {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
        font-weight: 500;
    }

    .current-status-value {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 600;
        padding: 4px 12px;
        background: rgba(120, 119, 198, 0.2);
        border-radius: 15px;
        font-size: 0.85rem;
    }

    .new-status-value {
        color: rgba(34, 197, 94, 1);
        font-weight: 600;
        padding: 4px 12px;
        background: rgba(34, 197, 94, 0.2);
        border: 1px solid rgba(34, 197, 94, 0.3);
        border-radius: 15px;
        font-size: 0.85rem;
    }

    .reply-preview {
        background: rgba(15, 15, 15, 0.8);
        border: 1px solid rgba(59, 130, 246, 0.3);
        border-radius: 10px;
        padding: 15px;
        margin: 15px 0;
    }

    .preview-label {
        color: rgba(59, 130, 246, 1);
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .preview-content {
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.5;
        max-height: 120px;
        overflow-y: auto;
        padding: 12px;
        background: rgba(20, 20, 30, 0.8);
        border-radius: 8px;
        border: 1px solid rgba(59, 130, 246, 0.2);
        white-space: pre-wrap;
        word-wrap: break-word;
    }

    .preview-content::-webkit-scrollbar {
        width: 6px;
    }

    .preview-content::-webkit-scrollbar-track {
        background: rgba(20, 20, 30, 0.5);
        border-radius: 3px;
    }

    .preview-content::-webkit-scrollbar-thumb {
        background: rgba(59, 130, 246, 0.5);
        border-radius: 3px;
    }

    .preview-content::-webkit-scrollbar-thumb:hover {
        background: rgba(59, 130, 246, 0.7);
    }

/* ===== 自定义分页样式 ===== */
.custom-pagination-container {
    margin-top: 30px;
    padding: 25px;
    background: linear-gradient(135deg, 
        rgba(20, 20, 30, 0.8) 0%, 
        rgba(25, 25, 35, 0.7) 100%);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 16px;
    backdrop-filter: blur(15px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.custom-pagination-nav {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.pagination-info {
    text-align: left;
    flex-shrink: 0;
    min-width: 200px;
}

.pagination-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    font-weight: 500;
    text-shadow: 0 0 10px rgba(120, 119, 198, 0.3);
}

.pagination-buttons {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    flex: 1;
}

.pagination-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    background: linear-gradient(135deg, 
        rgba(120, 119, 198, 0.2) 0%, 
        rgba(255, 119, 198, 0.15) 100%);
    border: 1px solid rgba(120, 119, 198, 0.4);
    border-radius: 10px;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    min-width: 40px;
    justify-content: center;
}

.pagination-btn:hover {
    background: linear-gradient(135deg, 
        rgba(120, 119, 198, 0.4) 0%, 
        rgba(255, 119, 198, 0.3) 100%);
    border-color: rgba(120, 119, 198, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(120, 119, 198, 0.2);
    color: #ffffff;
}

.pagination-btn.active {
    background: linear-gradient(135deg, 
        rgba(120, 119, 198, 0.8) 0%, 
        rgba(255, 119, 198, 0.6) 100%);
    border-color: rgba(120, 119, 198, 0.8);
    color: #ffffff;
    font-weight: 700;
    box-shadow: 0 6px 25px rgba(120, 119, 198, 0.4);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.pagination-btn.disabled {
    background: rgba(60, 60, 70, 0.3);
    border-color: rgba(120, 119, 198, 0.2);
    color: rgba(255, 255, 255, 0.4);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.pagination-btn.disabled:hover {
    background: rgba(60, 60, 70, 0.3);
    border-color: rgba(120, 119, 198, 0.2);
    color: rgba(255, 255, 255, 0.4);
    transform: none;
    box-shadow: none;
}

.pagination-ellipsis {
    color: rgba(255, 255, 255, 0.5);
    padding: 10px 8px;
    font-weight: bold;
    display: flex;
    align-items: center;
}

/* 自定义分页响应式设计 */
@media (max-width: 768px) {
    .custom-pagination-nav {
        flex-direction: column;
        gap: 15px;
    }
    
    .pagination-info {
        text-align: center;
        min-width: auto;
    }
    
    .pagination-buttons {
        justify-content: center;
        gap: 6px;
    }
    
    .pagination-btn {
        padding: 8px 12px;
        font-size: 12px;
        min-width: 36px;
    }
    
    .pagination-text {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .pagination-buttons {
        gap: 4px;
    }
    
    .pagination-btn {
        padding: 6px 10px;
        font-size: 11px;
        min-width: 32px;
    }
    
    /* 在小屏幕上隐藏首页/末页文字，只显示图标 */
    .pagination-first span:not(.fas),
    .pagination-last span:not(.fas),
    .pagination-prev span:not(.fas),
    .pagination-next span:not(.fas) {
        display: none;
    }
}

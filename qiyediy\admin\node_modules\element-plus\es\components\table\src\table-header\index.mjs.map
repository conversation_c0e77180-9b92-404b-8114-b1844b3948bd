{"version": 3, "file": "index.mjs", "sources": ["../../../../../../../packages/components/table/src/table-header/index.ts"], "sourcesContent": ["// @ts-nocheck\nimport {\n  defineComponent,\n  getCurrentInstance,\n  h,\n  inject,\n  nextTick,\n  onMounted,\n  reactive,\n  ref,\n  watch,\n} from 'vue'\nimport ElCheckbox from '@element-plus/components/checkbox'\nimport { useNamespace } from '@element-plus/hooks'\nimport FilterPanel from '../filter-panel.vue'\nimport useLayoutObserver from '../layout-observer'\nimport { TABLE_INJECTION_KEY } from '../tokens'\nimport useEvent from './event-helper'\nimport useStyle from './style.helper'\nimport useUtils from './utils-helper'\nimport type { ComponentInternalInstance, PropType, Ref } from 'vue'\nimport type { DefaultRow, Sort } from '../table/defaults'\nimport type { Store } from '../store'\n\nexport interface TableHeader extends ComponentInternalInstance {\n  state: {\n    onColumnsChange\n    onScrollableChange\n  }\n  filterPanels: Ref<unknown>\n}\nexport interface TableHeaderProps<T> {\n  fixed: string\n  store: Store<T>\n  border: boolean\n  defaultSort: Sort\n  allowDragLastColumn: boolean\n}\n\nexport default defineComponent({\n  name: 'ElTableHeader',\n  components: {\n    ElCheckbox,\n  },\n  props: {\n    fixed: {\n      type: String,\n      default: '',\n    },\n    store: {\n      required: true,\n      type: Object as PropType<TableHeaderProps<DefaultRow>['store']>,\n    },\n    border: Boolean,\n    defaultSort: {\n      type: Object as PropType<TableHeaderProps<DefaultRow>['defaultSort']>,\n      default: () => {\n        return {\n          prop: '',\n          order: '',\n        }\n      },\n    },\n    appendFilterPanelTo: {\n      type: String,\n    },\n    allowDragLastColumn: {\n      type: Boolean,\n    },\n  },\n  setup(props, { emit }) {\n    const instance = getCurrentInstance() as TableHeader\n    const parent = inject(TABLE_INJECTION_KEY)\n    const ns = useNamespace('table')\n    const filterPanels = ref({})\n    const { onColumnsChange, onScrollableChange } = useLayoutObserver(parent!)\n\n    const isTableLayoutAuto = parent?.props.tableLayout === 'auto'\n    const saveIndexSelection = reactive(new Map())\n    const theadRef = ref()\n\n    const updateFixedColumnStyle = () => {\n      setTimeout(() => {\n        if (saveIndexSelection.size > 0) {\n          saveIndexSelection.forEach((column, key) => {\n            const el = theadRef.value.querySelector(\n              `.${key.replace(/\\s/g, '.')}`\n            )\n            if (el) {\n              const width = el.getBoundingClientRect().width\n              column.width = width\n            }\n          })\n          saveIndexSelection.clear()\n        }\n      })\n    }\n\n    watch(saveIndexSelection, updateFixedColumnStyle)\n\n    onMounted(async () => {\n      // Need double await, because updateColumns is executed after nextTick for now\n      await nextTick()\n      await nextTick()\n      const { prop, order } = props.defaultSort\n      parent?.store.commit('sort', { prop, order, init: true })\n\n      updateFixedColumnStyle()\n    })\n\n    const {\n      handleHeaderClick,\n      handleHeaderContextMenu,\n      handleMouseDown,\n      handleMouseMove,\n      handleMouseOut,\n      handleSortClick,\n      handleFilterClick,\n    } = useEvent(props as TableHeaderProps<unknown>, emit)\n    const {\n      getHeaderRowStyle,\n      getHeaderRowClass,\n      getHeaderCellStyle,\n      getHeaderCellClass,\n    } = useStyle(props as TableHeaderProps<unknown>)\n    const { isGroup, toggleAllSelection, columnRows } = useUtils(\n      props as TableHeaderProps<unknown>\n    )\n\n    instance.state = {\n      onColumnsChange,\n      onScrollableChange,\n    }\n    instance.filterPanels = filterPanels\n\n    return {\n      ns,\n      filterPanels,\n      onColumnsChange,\n      onScrollableChange,\n      columnRows,\n      getHeaderRowClass,\n      getHeaderRowStyle,\n      getHeaderCellClass,\n      getHeaderCellStyle,\n      handleHeaderClick,\n      handleHeaderContextMenu,\n      handleMouseDown,\n      handleMouseMove,\n      handleMouseOut,\n      handleSortClick,\n      handleFilterClick,\n      isGroup,\n      toggleAllSelection,\n      saveIndexSelection,\n      isTableLayoutAuto,\n      theadRef,\n      updateFixedColumnStyle,\n    }\n  },\n  render() {\n    const {\n      ns,\n      isGroup,\n      columnRows,\n      getHeaderCellStyle,\n      getHeaderCellClass,\n      getHeaderRowClass,\n      getHeaderRowStyle,\n      handleHeaderClick,\n      handleHeaderContextMenu,\n      handleMouseDown,\n      handleMouseMove,\n      handleSortClick,\n      handleMouseOut,\n      store,\n      $parent,\n      saveIndexSelection,\n      isTableLayoutAuto,\n    } = this\n    let rowSpan = 1\n    return h(\n      'thead',\n      {\n        ref: 'theadRef',\n        class: { [ns.is('group')]: isGroup },\n      },\n      columnRows.map((subColumns, rowIndex) =>\n        h(\n          'tr',\n          {\n            class: getHeaderRowClass(rowIndex),\n            key: rowIndex,\n            style: getHeaderRowStyle(rowIndex),\n          },\n          subColumns.map((column, cellIndex) => {\n            if (column.rowSpan > rowSpan) {\n              rowSpan = column.rowSpan\n            }\n            const _class = getHeaderCellClass(\n              rowIndex,\n              cellIndex,\n              subColumns,\n              column\n            )\n            if (isTableLayoutAuto && column.fixed) {\n              saveIndexSelection.set(_class, column)\n            }\n            return h(\n              'th',\n              {\n                class: _class,\n                colspan: column.colSpan,\n                key: `${column.id}-thead`,\n                rowspan: column.rowSpan,\n                style: getHeaderCellStyle(\n                  rowIndex,\n                  cellIndex,\n                  subColumns,\n                  column\n                ),\n                onClick: ($event) => {\n                  if ($event.currentTarget.classList.contains('noclick')) {\n                    return\n                  }\n                  handleHeaderClick($event, column)\n                },\n                onContextmenu: ($event) =>\n                  handleHeaderContextMenu($event, column),\n                onMousedown: ($event) => handleMouseDown($event, column),\n                onMousemove: ($event) => handleMouseMove($event, column),\n                onMouseout: handleMouseOut,\n              },\n              [\n                h(\n                  'div',\n                  {\n                    class: [\n                      'cell',\n                      column.filteredValue && column.filteredValue.length > 0\n                        ? 'highlight'\n                        : '',\n                    ],\n                  },\n                  [\n                    column.renderHeader\n                      ? column.renderHeader({\n                          column,\n                          $index: cellIndex,\n                          store,\n                          _self: $parent,\n                        })\n                      : column.label,\n                    column.sortable &&\n                      h(\n                        'span',\n                        {\n                          onClick: ($event) => handleSortClick($event, column),\n                          class: 'caret-wrapper',\n                        },\n                        [\n                          h('i', {\n                            onClick: ($event) =>\n                              handleSortClick($event, column, 'ascending'),\n                            class: 'sort-caret ascending',\n                          }),\n                          h('i', {\n                            onClick: ($event) =>\n                              handleSortClick($event, column, 'descending'),\n                            class: 'sort-caret descending',\n                          }),\n                        ]\n                      ),\n                    column.filterable &&\n                      h(\n                        FilterPanel,\n                        {\n                          store,\n                          placement: column.filterPlacement || 'bottom-start',\n                          appendTo: $parent.appendFilterPanelTo,\n                          column,\n                          upDataColumn: (key, value) => {\n                            column[key] = value\n                          },\n                        },\n                        {\n                          'filter-icon': () =>\n                            column.renderFilterIcon\n                              ? column.renderFilterIcon({\n                                  filterOpened: column.filterOpened,\n                                })\n                              : null,\n                        }\n                      ),\n                  ]\n                ),\n              ]\n            )\n          })\n        )\n      )\n    )\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;;AAmBA,kBAAe,eAAe,CAAC;AAC/B,EAAE,IAAI,EAAE,eAAe;AACvB,EAAE,UAAU,EAAE;AACd,IAAI,UAAU;AACd,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,KAAK,EAAE;AACX,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,OAAO,EAAE,EAAE;AACjB,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,QAAQ,EAAE,IAAI;AACpB,MAAM,IAAI,EAAE,MAAM;AAClB,KAAK;AACL,IAAI,MAAM,EAAE,OAAO;AACnB,IAAI,WAAW,EAAE;AACjB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,OAAO,EAAE,MAAM;AACrB,QAAQ,OAAO;AACf,UAAU,IAAI,EAAE,EAAE;AAClB,UAAU,KAAK,EAAE,EAAE;AACnB,SAAS,CAAC;AACV,OAAO;AACP,KAAK;AACL,IAAI,mBAAmB,EAAE;AACzB,MAAM,IAAI,EAAE,MAAM;AAClB,KAAK;AACL,IAAI,mBAAmB,EAAE;AACzB,MAAM,IAAI,EAAE,OAAO;AACnB,KAAK;AACL,GAAG;AACH,EAAE,KAAK,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE;AACzB,IAAI,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC;AAC1C,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAC/C,IAAI,MAAM,EAAE,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;AACrC,IAAI,MAAM,YAAY,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AACjC,IAAI,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;AAC9E,IAAI,MAAM,iBAAiB,GAAG,CAAC,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,MAAM,MAAM,CAAC;AAC9F,IAAI,MAAM,kBAAkB,GAAG,QAAQ,iBAAiB,IAAI,GAAG,EAAE,CAAC,CAAC;AACnE,IAAI,MAAM,QAAQ,GAAG,GAAG,EAAE,CAAC;AAC3B,IAAI,MAAM,sBAAsB,GAAG,MAAM;AACzC,MAAM,UAAU,CAAC,MAAM;AACvB,QAAQ,IAAI,kBAAkB,CAAC,IAAI,GAAG,CAAC,EAAE;AACzC,UAAU,kBAAkB,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,GAAG,KAAK;AACtD,YAAY,MAAM,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACnF,YAAY,IAAI,EAAE,EAAE;AACpB,cAAc,MAAM,KAAK,GAAG,EAAE,CAAC,qBAAqB,EAAE,CAAC,KAAK,CAAC;AAC7D,cAAc,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;AACnC,aAAa;AACb,WAAW,CAAC,CAAC;AACb,UAAU,kBAAkB,CAAC,KAAK,EAAE,CAAC;AACrC,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK,CAAC;AACN,IAAI,KAAK,CAAC,kBAAkB,EAAE,sBAAsB,CAAC,CAAC;AACtD,IAAI,SAAS,CAAC,YAAY;AAC1B,MAAM,MAAM,QAAQ,EAAE,CAAC;AACvB,MAAM,MAAM,QAAQ,EAAE,CAAC;AACvB,MAAM,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC,WAAW,CAAC;AAChD,MAAM,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACzF,MAAM,sBAAsB,EAAE,CAAC;AAC/B,KAAK,CAAC,CAAC;AACP,IAAI,MAAM;AACV,MAAM,iBAAiB;AACvB,MAAM,uBAAuB;AAC7B,MAAM,eAAe;AACrB,MAAM,eAAe;AACrB,MAAM,cAAc;AACpB,MAAM,eAAe;AACrB,MAAM,iBAAiB;AACvB,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC9B,IAAI,MAAM;AACV,MAAM,iBAAiB;AACvB,MAAM,iBAAiB;AACvB,MAAM,kBAAkB;AACxB,MAAM,kBAAkB;AACxB,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACxB,IAAI,MAAM,EAAE,OAAO,EAAE,kBAAkB,EAAE,UAAU,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACxE,IAAI,QAAQ,CAAC,KAAK,GAAG;AACrB,MAAM,eAAe;AACrB,MAAM,kBAAkB;AACxB,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,YAAY,GAAG,YAAY,CAAC;AACzC,IAAI,OAAO;AACX,MAAM,EAAE;AACR,MAAM,YAAY;AAClB,MAAM,eAAe;AACrB,MAAM,kBAAkB;AACxB,MAAM,UAAU;AAChB,MAAM,iBAAiB;AACvB,MAAM,iBAAiB;AACvB,MAAM,kBAAkB;AACxB,MAAM,kBAAkB;AACxB,MAAM,iBAAiB;AACvB,MAAM,uBAAuB;AAC7B,MAAM,eAAe;AACrB,MAAM,eAAe;AACrB,MAAM,cAAc;AACpB,MAAM,eAAe;AACrB,MAAM,iBAAiB;AACvB,MAAM,OAAO;AACb,MAAM,kBAAkB;AACxB,MAAM,kBAAkB;AACxB,MAAM,iBAAiB;AACvB,MAAM,QAAQ;AACd,MAAM,sBAAsB;AAC5B,KAAK,CAAC;AACN,GAAG;AACH,EAAE,MAAM,GAAG;AACX,IAAI,MAAM;AACV,MAAM,EAAE;AACR,MAAM,OAAO;AACb,MAAM,UAAU;AAChB,MAAM,kBAAkB;AACxB,MAAM,kBAAkB;AACxB,MAAM,iBAAiB;AACvB,MAAM,iBAAiB;AACvB,MAAM,iBAAiB;AACvB,MAAM,uBAAuB;AAC7B,MAAM,eAAe;AACrB,MAAM,eAAe;AACrB,MAAM,eAAe;AACrB,MAAM,cAAc;AACpB,MAAM,KAAK;AACX,MAAM,OAAO;AACb,MAAM,kBAAkB;AACxB,MAAM,iBAAiB;AACvB,KAAK,GAAG,IAAI,CAAC;AACb,IAAI,IAAI,OAAO,GAAG,CAAC,CAAC;AACpB,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE;AACtB,MAAM,GAAG,EAAE,UAAU;AACrB,MAAM,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,OAAO,EAAE;AAC1C,KAAK,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,KAAK,CAAC,CAAC,IAAI,EAAE;AACxD,MAAM,KAAK,EAAE,iBAAiB,CAAC,QAAQ,CAAC;AACxC,MAAM,GAAG,EAAE,QAAQ;AACnB,MAAM,KAAK,EAAE,iBAAiB,CAAC,QAAQ,CAAC;AACxC,KAAK,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,SAAS,KAAK;AAC7C,MAAM,IAAI,MAAM,CAAC,OAAO,GAAG,OAAO,EAAE;AACpC,QAAQ,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;AACjC,OAAO;AACP,MAAM,MAAM,MAAM,GAAG,kBAAkB,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;AACjF,MAAM,IAAI,iBAAiB,IAAI,MAAM,CAAC,KAAK,EAAE;AAC7C,QAAQ,kBAAkB,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC/C,OAAO;AACP,MAAM,OAAO,CAAC,CAAC,IAAI,EAAE;AACrB,QAAQ,KAAK,EAAE,MAAM;AACrB,QAAQ,OAAO,EAAE,MAAM,CAAC,OAAO;AAC/B,QAAQ,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC;AACjC,QAAQ,OAAO,EAAE,MAAM,CAAC,OAAO;AAC/B,QAAQ,KAAK,EAAE,kBAAkB,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,CAAC;AAC1E,QAAQ,OAAO,EAAE,CAAC,MAAM,KAAK;AAC7B,UAAU,IAAI,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;AAClE,YAAY,OAAO;AACnB,WAAW;AACX,UAAU,iBAAiB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC5C,SAAS;AACT,QAAQ,aAAa,EAAE,CAAC,MAAM,KAAK,uBAAuB,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1E,QAAQ,WAAW,EAAE,CAAC,MAAM,KAAK,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC;AAChE,QAAQ,WAAW,EAAE,CAAC,MAAM,KAAK,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC;AAChE,QAAQ,UAAU,EAAE,cAAc;AAClC,OAAO,EAAE;AACT,QAAQ,CAAC,CAAC,KAAK,EAAE;AACjB,UAAU,KAAK,EAAE;AACjB,YAAY,MAAM;AAClB,YAAY,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,GAAG,WAAW,GAAG,EAAE;AACtF,WAAW;AACX,SAAS,EAAE;AACX,UAAU,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;AACpD,YAAY,MAAM;AAClB,YAAY,MAAM,EAAE,SAAS;AAC7B,YAAY,KAAK;AACjB,YAAY,KAAK,EAAE,OAAO;AAC1B,WAAW,CAAC,GAAG,MAAM,CAAC,KAAK;AAC3B,UAAU,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAC,MAAM,EAAE;AACvC,YAAY,OAAO,EAAE,CAAC,MAAM,KAAK,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC;AAChE,YAAY,KAAK,EAAE,eAAe;AAClC,WAAW,EAAE;AACb,YAAY,CAAC,CAAC,GAAG,EAAE;AACnB,cAAc,OAAO,EAAE,CAAC,MAAM,KAAK,eAAe,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,CAAC;AAC/E,cAAc,KAAK,EAAE,sBAAsB;AAC3C,aAAa,CAAC;AACd,YAAY,CAAC,CAAC,GAAG,EAAE;AACnB,cAAc,OAAO,EAAE,CAAC,MAAM,KAAK,eAAe,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,CAAC;AAChF,cAAc,KAAK,EAAE,uBAAuB;AAC5C,aAAa,CAAC;AACd,WAAW,CAAC;AACZ,UAAU,MAAM,CAAC,UAAU,IAAI,CAAC,CAAC,WAAW,EAAE;AAC9C,YAAY,KAAK;AACjB,YAAY,SAAS,EAAE,MAAM,CAAC,eAAe,IAAI,cAAc;AAC/D,YAAY,QAAQ,EAAE,OAAO,CAAC,mBAAmB;AACjD,YAAY,MAAM;AAClB,YAAY,YAAY,EAAE,CAAC,GAAG,EAAE,KAAK,KAAK;AAC1C,cAAc,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAClC,aAAa;AACb,WAAW,EAAE;AACb,YAAY,aAAa,EAAE,MAAM,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,CAAC;AACnF,cAAc,YAAY,EAAE,MAAM,CAAC,YAAY;AAC/C,aAAa,CAAC,GAAG,IAAI;AACrB,WAAW,CAAC;AACZ,SAAS,CAAC;AACV,OAAO,CAAC,CAAC;AACT,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACV,GAAG;AACH,CAAC,CAAC;;;;"}
<!--
  三只鱼网络科技 | 韩总 | 2024-12-20
  QiyeDIY企业建站系统 - DIY组件渲染器
-->

<template>
  <div 
    class="diy-component"
    :class="componentClasses"
    @click="handleSelect"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- 组件工具栏 -->
    <div v-if="selected || hovered" class="component-toolbar">
      <div class="toolbar-left">
        <span class="component-type">{{ getComponentTypeName(component.type) }}</span>
      </div>
      <div class="toolbar-right">
        <el-button-group size="small">
          <el-button :icon="Edit" @click.stop="handleEdit" />
          <el-button :icon="CopyDocument" @click.stop="handleCopy" />
          <el-button :icon="ArrowUp" @click.stop="handleMoveUp" :disabled="!canMoveUp" />
          <el-button :icon="ArrowDown" @click.stop="handleMoveDown" :disabled="!canMoveDown" />
          <el-button :icon="Delete" type="danger" @click.stop="handleDelete" />
        </el-button-group>
      </div>
    </div>

    <!-- 组件内容渲染 -->
    <div class="component-content" :style="componentStyles">
      <!-- 文本组件 -->
      <div v-if="component.type === 'text'" class="text-component">
        <div 
          v-html="component.content.text || '请输入文本内容'"
          :style="textStyles"
        ></div>
      </div>

      <!-- 图片组件 -->
      <div v-else-if="component.type === 'image'" class="image-component">
        <img 
          :src="component.content.src || '/images/placeholder.png'"
          :alt="component.content.alt || '图片'"
          :style="imageStyles"
          @error="handleImageError"
        />
      </div>

      <!-- 按钮组件 -->
      <div v-else-if="component.type === 'button'" class="button-component">
        <el-button
          :type="component.config.type || 'primary'"
          :size="component.config.size || 'default'"
          :plain="component.config.plain"
          :round="component.config.round"
          :circle="component.config.circle"
          :disabled="component.config.disabled"
          :style="buttonStyles"
          @click="handleButtonClick"
        >
          {{ component.content.text || '按钮' }}
        </el-button>
      </div>

      <!-- 容器组件 -->
      <div v-else-if="component.type === 'container'" class="container-component" :style="containerStyles">
        <DiyComponent
          v-for="child in component.children || []"
          :key="child.id"
          :component="child"
          :selected="selectedChildId === child.id"
          @select="handleChildSelect"
          @update="handleChildUpdate"
          @delete="handleChildDelete"
        />
        <div v-if="!component.children?.length" class="empty-container">
          <el-empty description="拖拽组件到这里" :image-size="60" />
        </div>
      </div>

      <!-- 分割线组件 -->
      <div v-else-if="component.type === 'divider'" class="divider-component">
        <el-divider 
          :direction="component.config.direction || 'horizontal'"
          :content-position="component.config.position || 'center'"
          :style="dividerStyles"
        >
          {{ component.content.text }}
        </el-divider>
      </div>

      <!-- 视频组件 -->
      <div v-else-if="component.type === 'video'" class="video-component">
        <video 
          :src="component.content.src"
          :poster="component.content.poster"
          :controls="component.config.controls !== false"
          :autoplay="component.config.autoplay"
          :loop="component.config.loop"
          :muted="component.config.muted"
          :style="videoStyles"
        >
          您的浏览器不支持视频播放
        </video>
      </div>

      <!-- 表单组件 -->
      <div v-else-if="component.type === 'form'" class="form-component">
        <el-form :style="formStyles">
          <el-form-item
            v-for="field in component.content.fields || []"
            :key="field.name"
            :label="field.label"
            :required="field.required"
          >
            <el-input
              v-if="field.type === 'input'"
              :placeholder="field.placeholder"
              :disabled="true"
            />
            <el-input
              v-else-if="field.type === 'textarea'"
              type="textarea"
              :rows="field.rows || 3"
              :placeholder="field.placeholder"
              :disabled="true"
            />
            <el-select
              v-else-if="field.type === 'select'"
              :placeholder="field.placeholder"
              :disabled="true"
            >
              <el-option
                v-for="option in field.options || []"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :disabled="true">
              {{ component.content.submitText || '提交' }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 未知组件类型 -->
      <div v-else class="unknown-component">
        <el-alert
          title="未知组件类型"
          :description="`组件类型: ${component.type}`"
          type="warning"
          show-icon
          :closable="false"
        />
      </div>
    </div>

    <!-- 拖拽指示器 -->
    <div v-if="selected" class="drag-indicators">
      <div class="drag-handle top" @mousedown="handleDragStart('top')"></div>
      <div class="drag-handle bottom" @mousedown="handleDragStart('bottom')"></div>
      <div class="drag-handle left" @mousedown="handleDragStart('left')"></div>
      <div class="drag-handle right" @mousedown="handleDragStart('right')"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { DiyComponent as DiyComponentType } from '@/api/diy'
import {
  Edit, CopyDocument, ArrowUp, ArrowDown, Delete
} from '@element-plus/icons-vue'

interface Props {
  component: DiyComponentType
  selected?: boolean
  canMoveUp?: boolean
  canMoveDown?: boolean
}

interface Emits {
  (e: 'select', component: DiyComponentType): void
  (e: 'update', component: DiyComponentType): void
  (e: 'delete', component: DiyComponentType): void
  (e: 'copy', component: DiyComponentType): void
  (e: 'move', component: DiyComponentType, direction: 'up' | 'down'): void
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  canMoveUp: true,
  canMoveDown: true
})

const emit = defineEmits<Emits>()

// 状态
const hovered = ref(false)
const selectedChildId = ref<number | null>(null)

// 计算属性
const componentClasses = computed(() => {
  return {
    'is-selected': props.selected,
    'is-hovered': hovered.value,
    [`component-${props.component.type}`]: true
  }
})

const componentStyles = computed(() => {
  const config = props.component.config || {}
  const styles: Record<string, any> = {}
  
  // 通用样式
  if (config.width) styles.width = config.width
  if (config.height) styles.height = config.height
  if (config.margin) styles.margin = config.margin
  if (config.padding) styles.padding = config.padding
  if (config.background) styles.background = config.background
  if (config.border) styles.border = config.border
  if (config.borderRadius) styles.borderRadius = config.borderRadius
  if (config.boxShadow) styles.boxShadow = config.boxShadow
  
  return styles
})

const textStyles = computed(() => {
  const config = props.component.config || {}
  const styles: Record<string, any> = {}
  
  if (config.fontSize) styles.fontSize = config.fontSize
  if (config.color) styles.color = config.color
  if (config.textAlign) styles.textAlign = config.textAlign
  if (config.lineHeight) styles.lineHeight = config.lineHeight
  if (config.fontWeight) styles.fontWeight = config.fontWeight
  if (config.fontFamily) styles.fontFamily = config.fontFamily
  
  return styles
})

const imageStyles = computed(() => {
  const config = props.component.config || {}
  const styles: Record<string, any> = {}
  
  if (config.width) styles.width = config.width
  if (config.height) styles.height = config.height
  if (config.objectFit) styles.objectFit = config.objectFit
  if (config.borderRadius) styles.borderRadius = config.borderRadius
  
  return styles
})

const buttonStyles = computed(() => {
  const config = props.component.config || {}
  const styles: Record<string, any> = {}
  
  if (config.width) styles.width = config.width
  if (config.height) styles.height = config.height
  if (config.fontSize) styles.fontSize = config.fontSize
  if (config.backgroundColor) styles.backgroundColor = config.backgroundColor
  if (config.color) styles.color = config.color
  if (config.borderRadius) styles.borderRadius = config.borderRadius
  
  return styles
})

const containerStyles = computed(() => {
  const config = props.component.config || {}
  const styles: Record<string, any> = {}
  
  if (config.display) styles.display = config.display
  if (config.flexDirection) styles.flexDirection = config.flexDirection
  if (config.justifyContent) styles.justifyContent = config.justifyContent
  if (config.alignItems) styles.alignItems = config.alignItems
  if (config.gap) styles.gap = config.gap
  if (config.minHeight) styles.minHeight = config.minHeight
  
  return styles
})

const dividerStyles = computed(() => {
  const config = props.component.config || {}
  const styles: Record<string, any> = {}
  
  if (config.color) styles.borderColor = config.color
  if (config.width) styles.borderWidth = config.width
  if (config.style) styles.borderStyle = config.style
  
  return styles
})

const videoStyles = computed(() => {
  const config = props.component.config || {}
  const styles: Record<string, any> = {}
  
  if (config.width) styles.width = config.width
  if (config.height) styles.height = config.height
  if (config.objectFit) styles.objectFit = config.objectFit
  
  return styles
})

const formStyles = computed(() => {
  const config = props.component.config || {}
  const styles: Record<string, any> = {}
  
  if (config.maxWidth) styles.maxWidth = config.maxWidth
  if (config.labelWidth) styles['--el-form-label-width'] = config.labelWidth
  
  return styles
})

/**
 * 获取组件类型名称
 */
const getComponentTypeName = (type: string): string => {
  const typeNames = {
    text: '文本',
    image: '图片',
    button: '按钮',
    container: '容器',
    divider: '分割线',
    video: '视频',
    form: '表单'
  }
  return typeNames[type] || type
}

/**
 * 处理选择
 */
const handleSelect = () => {
  emit('select', props.component)
}

/**
 * 处理鼠标进入
 */
const handleMouseEnter = () => {
  hovered.value = true
}

/**
 * 处理鼠标离开
 */
const handleMouseLeave = () => {
  hovered.value = false
}

/**
 * 处理编辑
 */
const handleEdit = () => {
  emit('select', props.component)
}

/**
 * 处理复制
 */
const handleCopy = () => {
  emit('copy', props.component)
}

/**
 * 处理上移
 */
const handleMoveUp = () => {
  emit('move', props.component, 'up')
}

/**
 * 处理下移
 */
const handleMoveDown = () => {
  emit('move', props.component, 'down')
}

/**
 * 处理删除
 */
const handleDelete = () => {
  emit('delete', props.component)
}

/**
 * 处理按钮点击
 */
const handleButtonClick = () => {
  const link = props.component.content.link
  if (link) {
    // 在编辑器中不执行跳转，只是提示
    ElMessage.info(`按钮链接: ${link}`)
  }
}

/**
 * 处理图片错误
 */
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/image-error.png'
}

/**
 * 处理子组件选择
 */
const handleChildSelect = (child: DiyComponentType) => {
  selectedChildId.value = child.id
  emit('select', child)
}

/**
 * 处理子组件更新
 */
const handleChildUpdate = (child: DiyComponentType) => {
  emit('update', child)
}

/**
 * 处理子组件删除
 */
const handleChildDelete = (child: DiyComponentType) => {
  emit('delete', child)
}

/**
 * 处理拖拽开始
 */
const handleDragStart = (direction: string) => {
  // 拖拽调整大小的逻辑
  console.log('开始拖拽调整大小:', direction)
}
</script>

<style lang="scss" scoped>
.diy-component {
  position: relative;
  min-height: 20px;
  transition: all 0.3s ease;
  
  &.is-selected {
    outline: 2px solid #409eff;
    outline-offset: 2px;
  }
  
  &.is-hovered:not(.is-selected) {
    outline: 1px dashed #409eff;
    outline-offset: 1px;
  }
}

.component-toolbar {
  position: absolute;
  top: -40px;
  left: 0;
  right: 0;
  height: 32px;
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px;
  border-radius: 4px 4px 0 0;
  font-size: 12px;
  z-index: 10;
  
  .component-type {
    font-weight: 500;
  }
  
  :deep(.el-button-group) {
    .el-button {
      padding: 2px 4px;
      font-size: 12px;
      border-color: rgba(255, 255, 255, 0.3);
      
      &:not(.is-disabled):hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }
}

.component-content {
  position: relative;
  min-height: inherit;
}

// 组件特定样式
.text-component {
  min-height: 20px;
  
  &:empty::before {
    content: '请输入文本内容';
    color: #ccc;
  }
}

.image-component {
  img {
    max-width: 100%;
    height: auto;
    display: block;
  }
}

.button-component {
  display: inline-block;
}

.container-component {
  min-height: 100px;
  border: 1px dashed #e5e5e5;
  position: relative;
  
  .empty-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #ccc;
  }
}

.divider-component {
  margin: 16px 0;
}

.video-component {
  video {
    max-width: 100%;
    height: auto;
  }
}

.form-component {
  :deep(.el-form-item) {
    margin-bottom: 16px;
  }
}

.unknown-component {
  padding: 16px;
}

// 拖拽指示器
.drag-indicators {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  
  .drag-handle {
    position: absolute;
    background: #409eff;
    pointer-events: auto;
    cursor: pointer;
    
    &.top {
      top: -3px;
      left: 50%;
      transform: translateX(-50%);
      width: 20px;
      height: 6px;
      cursor: n-resize;
    }
    
    &.bottom {
      bottom: -3px;
      left: 50%;
      transform: translateX(-50%);
      width: 20px;
      height: 6px;
      cursor: s-resize;
    }
    
    &.left {
      left: -3px;
      top: 50%;
      transform: translateY(-50%);
      width: 6px;
      height: 20px;
      cursor: w-resize;
    }
    
    &.right {
      right: -3px;
      top: 50%;
      transform: translateY(-50%);
      width: 6px;
      height: 20px;
      cursor: e-resize;
    }
  }
}
</style>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QiyeDIY管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f7fa;
            min-height: 100vh;
        }
        
        .header {
            background: #2c3e50;
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .card h2 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.25rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }
        
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 0.25rem;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }
        
        .menu-item {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
            cursor: pointer;
            transition: transform 0.3s;
        }
        
        .menu-item:hover {
            transform: translateY(-2px);
        }
        
        .menu-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background: #27ae60;
        }
        
        .status-offline {
            background: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>QiyeDIY管理后台</h1>
    </div>
    
    <div class="container">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div class="stat-label">网站数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div class="stat-label">用户数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div class="stat-label">模板数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div class="stat-label">今日访问</div>
            </div>
        </div>
        
        <div class="card">
            <h2>系统状态</h2>
            <p><span class="status-indicator status-online"></span>后端API: <span id="api-status">检测中...</span></p>
            <p><span class="status-indicator status-online"></span>前端服务: 运行正常</p>
            <p><span class="status-indicator status-online"></span>管理后台: 运行正常</p>
        </div>
        
        <div class="card">
            <h2>快速操作</h2>
            <a href="http://localhost:8000/api/test" class="btn" target="_blank">测试API</a>
            <a href="http://localhost:3000" class="btn btn-success" target="_blank">查看前端</a>
            <a href="#" class="btn">创建网站</a>
            <a href="#" class="btn">管理模板</a>
        </div>
        
        <div class="card">
            <h2>功能菜单</h2>
            <div class="menu-grid">
                <div class="menu-item">
                    <div class="menu-icon">🏠</div>
                    <div>网站管理</div>
                </div>
                <div class="menu-item">
                    <div class="menu-icon">👥</div>
                    <div>用户管理</div>
                </div>
                <div class="menu-item">
                    <div class="menu-icon">🎨</div>
                    <div>模板管理</div>
                </div>
                <div class="menu-item">
                    <div class="menu-icon">📊</div>
                    <div>数据统计</div>
                </div>
                <div class="menu-item">
                    <div class="menu-icon">⚙️</div>
                    <div>系统设置</div>
                </div>
                <div class="menu-item">
                    <div class="menu-icon">📝</div>
                    <div>内容管理</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 检测API状态
        fetch('http://localhost:8000/api/test')
            .then(response => response.json())
            .then(data => {
                document.getElementById('api-status').textContent = '运行正常';
            })
            .catch(error => {
                document.getElementById('api-status').textContent = '连接失败';
                document.querySelector('.status-indicator').className = 'status-indicator status-offline';
            });
    </script>
</body>
</html>

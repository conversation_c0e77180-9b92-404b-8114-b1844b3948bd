/**
 * 图片上传弹窗组件样式
 * 深色科技风主题
 * 作者: AI龙头韩哥
 * 版本: 1.0.0
 */

/* 弹窗遮罩层 */
.image-uploader-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-uploader-overlay.show {
    opacity: 1;
}

/* 弹窗主体 */
.image-uploader-modal {
    background: linear-gradient(135deg, 
        rgba(25, 25, 35, 0.95) 0%, 
        rgba(35, 35, 50, 0.95) 50%, 
        rgba(25, 25, 35, 0.95) 100%);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 15px;
    box-shadow: 
        0 20px 60px rgba(0, 0, 0, 0.5),
        0 0 30px rgba(120, 119, 198, 0.2);
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transform: scale(0.9) translateY(20px);
    transition: transform 0.3s ease;
}

.image-uploader-overlay.show .image-uploader-modal {
    transform: scale(1) translateY(0);
}

/* 弹窗头部 */
.image-uploader-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 25px;
    background: linear-gradient(135deg, 
        rgba(120, 119, 198, 0.15) 0%, 
        rgba(255, 119, 198, 0.1) 50%, 
        rgba(120, 219, 255, 0.15) 100%);
    border-bottom: 1px solid rgba(120, 119, 198, 0.3);
    flex-shrink: 0;
}

.header-title {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    font-family: 'Orbitron', monospace;
}

.header-title i {
    color: rgba(120, 219, 255, 0.9);
    font-size: 20px;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.header-actions button {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    background: rgba(60, 60, 80, 0.6);
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.header-actions button:hover {
    background: rgba(120, 119, 198, 0.4);
    color: #ffffff;
    transform: scale(1.05);
}

/* 弹窗内容区域 */
.image-uploader-content {
    flex: 1;
    padding: 25px;
    overflow-y: auto;
    min-height: 400px;
}

/* 上传区域 */
.upload-zone {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 350px;
    border: 2px dashed rgba(120, 119, 198, 0.4);
    border-radius: 12px;
    background: rgba(30, 30, 40, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.upload-zone:hover {
    border-color: rgba(120, 119, 198, 0.6);
    background: rgba(30, 30, 40, 0.5);
    transform: scale(1.02);
}

.upload-zone.drag-over {
    border-color: rgba(120, 219, 255, 0.8);
    background: rgba(120, 219, 255, 0.1);
    box-shadow: 0 0 20px rgba(120, 219, 255, 0.3);
}

.upload-zone-content {
    text-align: center;
    color: #ffffff;
    z-index: 1;
}

.upload-icon {
    font-size: 48px;
    color: rgba(120, 219, 255, 0.8);
    margin-bottom: 20px;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.upload-text h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #ffffff;
    font-family: 'Orbitron', monospace;
}

.upload-text p {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 8px;
    line-height: 1.5;
}

.btn-select-files {
    margin-top: 20px;
    padding: 12px 24px;
    background: linear-gradient(135deg, 
        rgba(120, 119, 198, 0.8) 0%, 
        rgba(255, 119, 198, 0.6) 100%);
    border: none;
    border-radius: 8px;
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(120, 119, 198, 0.3);
}

.btn-select-files:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(120, 119, 198, 0.4);
}

/* 模式选择选项卡 */
.mode-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(120, 119, 198, 0.2);
}

.mode-tab {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
}

.mode-tab:hover {
    color: rgba(255, 255, 255, 0.9);
    background: rgba(120, 119, 198, 0.1);
}

.mode-tab.active {
    color: rgba(120, 219, 255, 0.9);
    border-bottom-color: rgba(120, 219, 255, 0.8);
    background: rgba(120, 219, 255, 0.1);
}

.mode-tab i {
    font-size: 16px;
}

/* 图片选择区域 */
.image-selector-area {
    animation: fadeIn 0.5s ease;
}

.selector-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(30, 30, 40, 0.6);
    border-radius: 8px;
    border: 1px solid rgba(120, 119, 198, 0.2);
    flex-wrap: nowrap;
    gap: 15px;
    flex-direction: row;
    min-height: 60px;
}

/* 选择限制警告提示 */
.selection-limit-warning {
    width: 100%;
    background: linear-gradient(135deg, rgba(255, 152, 0, 0.15) 0%, rgba(255, 193, 7, 0.1) 100%);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 6px;
    padding: 12px 15px;
    margin: 15px 0;
    text-align: center;
}

.selection-limit-warning .warning-content {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 193, 7, 0.9);
    font-size: 13px;
    font-weight: 500;
}

.selection-limit-warning .warning-content i {
    font-size: 14px;
    color: rgba(255, 193, 7, 1);
}

/* 警告高亮动画 */
@keyframes warningPulse {
    0% {
        background: linear-gradient(135deg, rgba(255, 152, 0, 0.15) 0%, rgba(255, 193, 7, 0.1) 100%);
        border-color: rgba(255, 193, 7, 0.3);
        transform: scale(1);
    }
    50% {
        background: linear-gradient(135deg, rgba(255, 152, 0, 0.3) 0%, rgba(255, 193, 7, 0.25) 100%);
        border-color: rgba(255, 193, 7, 0.6);
        transform: scale(1.02);
    }
    100% {
        background: linear-gradient(135deg, rgba(255, 152, 0, 0.15) 0%, rgba(255, 193, 7, 0.1) 100%);
        border-color: rgba(255, 193, 7, 0.3);
        transform: scale(1);
    }
}

/* 选择器筛选和信息区域 */
.selector-filters-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    flex-wrap: wrap;
    gap: 15px;
}

.selector-filters {
    display: flex;
    align-items: center;
    gap: 12px;
}

.selector-group-filter,
.selector-search {
    padding: 8px 12px;
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 6px;
    background: rgba(60, 60, 80, 0.6);
    color: #ffffff;
    font-size: 13px;
    outline: none;
    transition: all 0.3s ease;
}

.selector-group-filter:focus,
.selector-search:focus {
    border-color: rgba(120, 119, 198, 0.6);
    background: rgba(60, 60, 80, 0.8);
}

.selector-search {
    min-width: 200px;
}

.btn-search {
    padding: 8px 12px;
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 6px;
    background: rgba(120, 119, 198, 0.6);
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-search:hover {
    background: rgba(120, 119, 198, 0.8);
    transform: translateY(-1px);
}

.selector-info {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    margin-left: 15px;
    white-space: nowrap;
    flex-shrink: 0;
}

.selector-info strong {
    color: rgba(120, 219, 255, 0.9);
    font-weight: 600;
}

/* 图片选择网格 */
.selector-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;
    margin-bottom: 20px;
}

.selector-item {
    position: relative;
    background: rgba(30, 30, 40, 0.6);
    border: 2px solid rgba(120, 119, 198, 0.2);
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    animation: slideIn 0.5s ease;
}

.selector-item:hover {
    border-color: rgba(120, 119, 198, 0.5);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.selector-item.selected {
    border-color: rgba(120, 219, 255, 0.8);
    box-shadow: 0 0 15px rgba(120, 219, 255, 0.3);
}

.selector-item-image {
    width: 100%;
    height: 120px;
    overflow: hidden;
    position: relative;
}

.selector-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.selector-item:hover .selector-item-image img {
    transform: scale(1.05);
}

.selector-item-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.selector-item:hover .selector-item-overlay {
    opacity: 1;
}

.selector-item.selected .selector-item-overlay {
    opacity: 1;
    background: rgba(120, 219, 255, 0.3);
}

.selector-item-check {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(120, 219, 255, 0.9);
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: bold;
    transform: scale(0);
    transition: transform 0.3s ease;
}

.selector-item.selected .selector-item-check {
    transform: scale(1);
}

.selector-item-info {
    padding: 8px;
}

.selector-item-name {
    font-size: 11px;
    color: #ffffff;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
}

.selector-item-meta {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.6);
    display: flex;
    justify-content: space-between;
}

/* 选择顺序标识 */
.selector-item-order {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(120, 219, 255, 0.9), rgba(120, 119, 198, 0.9));
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: bold;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.selector-item.selected .selector-item-order {
    opacity: 1;
    transform: scale(1);
}

/* 分页 */
.selector-pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 15px;
}

.pagination-btn {
    padding: 6px 12px;
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 6px;
    background: rgba(60, 60, 80, 0.6);
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
    background: rgba(120, 119, 198, 0.4);
    border-color: rgba(120, 119, 198, 0.6);
    color: #ffffff;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-btn.active {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.8), rgba(255, 119, 198, 0.6));
    border-color: rgba(120, 119, 198, 0.8);
    color: #ffffff;
}

.pagination-info {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    margin: 0 10px;
}

/* 预览区域 */
.preview-area {
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(120, 119, 198, 0.2);
}

.preview-title {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    font-family: 'Orbitron', monospace;
}

.preview-title i {
    color: rgba(120, 219, 255, 0.9);
}

.preview-actions {
    display: flex;
    gap: 10px;
}

.preview-actions button {
    padding: 8px 16px;
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 6px;
    background: rgba(60, 60, 80, 0.6);
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
    font-weight: 500;
    min-height: 32px;
}

.preview-actions button:hover {
    background: rgba(120, 119, 198, 0.4);
    border-color: rgba(120, 119, 198, 0.6);
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(120, 119, 198, 0.2);
}

.preview-actions .btn-add-more {
    background: linear-gradient(135deg, 
        rgba(76, 175, 80, 0.7) 0%, 
        rgba(139, 195, 74, 0.7) 100%);
    border-color: rgba(76, 175, 80, 0.5);
}

.preview-actions .btn-add-more:hover {
    background: linear-gradient(135deg, 
        rgba(76, 175, 80, 0.9) 0%, 
        rgba(139, 195, 74, 0.9) 100%);
    border-color: rgba(76, 175, 80, 0.7);
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.preview-actions .btn-clear-all {
    background: linear-gradient(135deg, 
        rgba(244, 67, 54, 0.7) 0%, 
        rgba(233, 30, 99, 0.7) 100%);
    border-color: rgba(244, 67, 54, 0.5);
}

.preview-actions .btn-clear-all:hover {
    background: linear-gradient(135deg, 
        rgba(244, 67, 54, 0.9) 0%, 
        rgba(233, 30, 99, 0.9) 100%);
    border-color: rgba(244, 67, 54, 0.7);
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
}

/* 预览网格 */
.preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    max-height: 400px;
    overflow-y: auto;
    padding-right: 10px;
}

/* 自定义滚动条 */
.preview-grid::-webkit-scrollbar {
    width: 8px;
}

.preview-grid::-webkit-scrollbar-track {
    background: rgba(30, 30, 40, 0.5);
    border-radius: 4px;
}

.preview-grid::-webkit-scrollbar-thumb {
    background: rgba(120, 119, 198, 0.6);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.preview-grid::-webkit-scrollbar-thumb:hover {
    background: rgba(120, 119, 198, 0.8);
}

/* 预览项 */
.preview-item {
    background: rgba(30, 30, 40, 0.6);
    border: 1px solid rgba(120, 119, 198, 0.2);
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    animation: slideIn 0.5s ease;
}

@keyframes slideIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
}

.preview-item:hover {
    border-color: rgba(120, 119, 198, 0.5);
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.preview-image {
    position: relative;
    width: 100%;
    height: 150px;
    overflow: hidden;
}

.preview-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.preview-item:hover .preview-image img {
    transform: scale(1.1);
}

.preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.preview-item:hover .preview-overlay {
    opacity: 1;
}

.preview-actions {
    display: flex;
    gap: 8px;
}

.preview-overlay .preview-actions {
    display: flex;
    gap: 8px;
}

.preview-overlay .preview-actions button {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    background: rgba(120, 119, 198, 0.8);
    color: #ffffff;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 12px;
    backdrop-filter: blur(5px);
}

.preview-overlay .preview-actions button:hover {
    background: rgba(120, 219, 255, 0.8);
    transform: scale(1.1);
    box-shadow: 0 0 10px rgba(120, 219, 255, 0.4);
}

.preview-overlay .preview-actions button:disabled {
    background: rgba(60, 60, 80, 0.5);
    color: rgba(255, 255, 255, 0.3);
    cursor: not-allowed;
    transform: none;
}

.preview-overlay .preview-actions .btn-move-up {
    background: rgba(33, 150, 243, 0.8);
}

.preview-overlay .preview-actions .btn-move-up:hover:not(:disabled) {
    background: rgba(33, 150, 243, 1);
    box-shadow: 0 0 10px rgba(33, 150, 243, 0.4);
}

.preview-overlay .preview-actions .btn-move-down {
    background: rgba(33, 150, 243, 0.8);
}

.preview-overlay .preview-actions .btn-move-down:hover:not(:disabled) {
    background: rgba(33, 150, 243, 1);
    box-shadow: 0 0 10px rgba(33, 150, 243, 0.4);
}

.preview-overlay .preview-actions .btn-remove {
    background: rgba(244, 67, 54, 0.8);
}

.preview-overlay .preview-actions .btn-remove:hover {
    background: rgba(244, 67, 54, 1);
    box-shadow: 0 0 10px rgba(244, 67, 54, 0.4);
}

.preview-overlay .preview-actions .btn-preview {
    background: rgba(76, 175, 80, 0.8);
}

.preview-overlay .preview-actions .btn-preview:hover {
    background: rgba(76, 175, 80, 1);
    box-shadow: 0 0 10px rgba(76, 175, 80, 0.4);
}

.preview-info {
    padding: 12px;
}

.file-name {
    font-size: 13px;
    font-weight: 500;
    color: #ffffff;
    margin-bottom: 6px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-size {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 4px;
}

.file-order {
    font-size: 11px;
    color: rgba(120, 219, 255, 0.8);
    font-weight: 500;
    background: rgba(120, 219, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid rgba(120, 219, 255, 0.3);
    display: inline-block;
    text-align: center;
    min-width: 40px;
}

/* 弹窗底部 */
.image-uploader-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 25px;
    background: rgba(20, 20, 30, 0.8);
    border-top: 1px solid rgba(120, 119, 198, 0.3);
    flex-shrink: 0;
}

.footer-info {
    display: flex;
    align-items: center;
    gap: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

.footer-info strong {
    color: rgba(120, 219, 255, 0.9);
    font-weight: 600;
}

.footer-actions {
    display: flex;
    gap: 12px;
}

.footer-actions button {
    padding: 10px 20px;
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    min-width: 100px;
    justify-content: center;
}

.btn-cancel {
    background: rgba(60, 60, 80, 0.6);
    color: rgba(255, 255, 255, 0.8);
}

.btn-cancel:hover {
    background: rgba(80, 80, 100, 0.8);
    color: #ffffff;
}

.btn-confirm {
    background: linear-gradient(135deg, 
        rgba(76, 175, 80, 0.8) 0%, 
        rgba(139, 195, 74, 0.8) 100%);
    color: #ffffff;
    border-color: rgba(76, 175, 80, 0.5);
}

.btn-confirm:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.btn-confirm:disabled {
    background: rgba(60, 60, 80, 0.5);
    color: rgba(255, 255, 255, 0.3);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-upload {
    background: linear-gradient(135deg, 
        rgba(120, 119, 198, 0.8) 0%, 
        rgba(255, 119, 198, 0.6) 100%);
    color: #ffffff;
    border-color: rgba(120, 119, 198, 0.5);
}

.btn-upload:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(120, 119, 198, 0.3);
}

.btn-upload:disabled {
    background: rgba(60, 60, 80, 0.5);
    color: rgba(255, 255, 255, 0.3);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .image-uploader-modal {
        width: 95%;
        max-height: 95vh;
    }
    
    .image-uploader-header {
        padding: 15px 20px;
    }
    
    .header-title {
        font-size: 16px;
    }
    
    .image-uploader-content {
        padding: 20px;
    }
    
    .upload-zone {
        min-height: 250px;
    }
    
    .upload-icon {
        font-size: 36px;
    }
    
    .upload-text h3 {
        font-size: 18px;
    }
    
    .preview-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .preview-image {
        height: 120px;
    }
    
    .preview-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .preview-actions {
        width: 100%;
        justify-content: center;
    }
    
    .preview-actions button {
        padding: 6px 12px;
        font-size: 11px;
    }
    
    .image-uploader-footer {
        padding: 15px 20px;
        flex-direction: column;
        gap: 15px;
    }
    
    .footer-info {
        justify-content: center;
        text-align: center;
    }
    
    .footer-actions {
        width: 100%;
        justify-content: center;
    }
    
    .footer-actions button {
        flex: 1;
        max-width: 120px;
    }
}

@media (max-width: 480px) {
    .image-uploader-modal {
        width: 98%;
        max-height: 98vh;
    }
    
    .preview-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
    }
    
    .preview-image {
        height: 100px;
    }
    
    .preview-header .preview-actions button {
        padding: 4px 8px;
        font-size: 10px;
        min-height: 28px;
    }
    
    .preview-overlay .preview-actions button {
        width: 28px;
        height: 28px;
        font-size: 10px;
    }
    
    .upload-text h3 {
        font-size: 16px;
    }
    
    .upload-text p {
        font-size: 12px;
    }
    
    .footer-actions button {
        padding: 8px 12px;
        font-size: 12px;
        min-width: 80px;
    }
    
    .file-order {
        font-size: 10px;
        padding: 1px 4px;
        min-width: 30px;
    }
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #ffffff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 成功/错误状态 */
.upload-success {
    border-color: rgba(76, 175, 80, 0.6) !important;
    background: rgba(76, 175, 80, 0.1) !important;
}

.upload-error {
    border-color: rgba(244, 67, 54, 0.6) !important;
    background: rgba(244, 67, 54, 0.1) !important;
}

/* 工具提示 */
.tooltip {
    position: relative;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: #ffffff;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}

/* ===== 增强版功能样式 ===== */

/* 选择器操作按钮 */
.selector-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: nowrap;
    justify-content: flex-start;
}

.btn-action {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
    font-weight: 500;
    min-width: 70px;
    white-space: nowrap;
    flex-shrink: 0;
}

.btn-action:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.btn-action.danger {
    background: rgba(220, 53, 69, 0.2);
    border-color: rgba(220, 53, 69, 0.4);
    color: #ff6b7a;
}

.btn-action.danger:hover {
    background: rgba(220, 53, 69, 0.3);
    border-color: rgba(220, 53, 69, 0.6);
    color: #ffffff;
}

/* 图片操作按钮 */
.selector-item-actions {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 2;
}

.selector-item:hover .selector-item-actions {
    opacity: 1;
}

.item-action-btn {
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: #ffffff;
    width: 28px;
    height: 28px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    backdrop-filter: blur(5px);
}

.item-action-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
}

.item-action-btn.delete-btn:hover {
    background: rgba(220, 53, 69, 0.8);
    color: #ffffff;
}

.item-action-btn.move-btn:hover {
    background: rgba(120, 119, 198, 0.8);
    color: #ffffff;
}

/* 分组名称标签 */
.selector-item-meta .group-name {
    background: rgba(120, 119, 198, 0.3);
    color: #7877c6;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
    white-space: nowrap;
}

/* 对话框样式 */
.move-dialog-overlay,
.confirm-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10001;
    animation: fadeIn 0.3s ease;
}

.move-dialog,
.confirm-dialog {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    width: 90%;
    max-width: 400px;
    border: 1px solid rgba(120, 119, 198, 0.3);
    animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.move-dialog-header,
.confirm-dialog-header {
    padding: 16px 20px;
    border-bottom: 1px solid rgba(120, 119, 198, 0.3);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(15, 52, 96, 0.5);
    border-radius: 12px 12px 0 0;
}

.move-dialog-header h3,
.confirm-dialog-header h3 {
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.move-dialog-close,
.confirm-dialog-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 20px;
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.move-dialog-close:hover,
.confirm-dialog-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

.move-dialog-content,
.confirm-dialog-content {
    padding: 20px;
}

.move-dialog-content p,
.confirm-dialog-content p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 16px;
    font-size: 14px;
    line-height: 1.5;
}

.move-group-select {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    padding: 10px 12px;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.move-group-select:focus {
    outline: none;
    border-color: #7877c6;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 10px rgba(120, 119, 198, 0.3);
}

.move-group-select option {
    background: #1a1a2e;
    color: #ffffff;
}

.move-dialog-footer,
.confirm-dialog-footer {
    padding: 16px 20px;
    border-top: 1px solid rgba(120, 119, 198, 0.3);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    background: rgba(15, 52, 96, 0.3);
    border-radius: 0 0 12px 12px;
}

.move-dialog-footer .btn-cancel,
.move-dialog-footer .btn-confirm,
.confirm-dialog-footer .btn-cancel,
.confirm-dialog-footer .btn-confirm {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;
    font-weight: 500;
}

.move-dialog-footer .btn-cancel,
.confirm-dialog-footer .btn-cancel {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
}

.move-dialog-footer .btn-cancel:hover,
.confirm-dialog-footer .btn-cancel:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
}

.move-dialog-footer .btn-confirm,
.confirm-dialog-footer .btn-confirm {
    background: linear-gradient(135deg, #7877c6 0%, #5a5a9e 100%);
    color: #ffffff;
}

.move-dialog-footer .btn-confirm:hover,
.confirm-dialog-footer .btn-confirm:hover {
    background: linear-gradient(135deg, #8a89d4 0%, #6b6bac 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(120, 119, 198, 0.3);
}

/* 危险操作按钮样式 */
.confirm-dialog-footer .btn-confirm.danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.confirm-dialog-footer .btn-confirm.danger:hover {
    background: linear-gradient(135deg, #e04555 0%, #d02843 100%);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

/* 增强的确认按钮样式 */
.btn-confirm.active {
    background: linear-gradient(135deg, #7877c6 0%, #5a5a9e 100%) !important;
    border-color: #7877c6 !important;
    color: #ffffff !important;
    cursor: pointer !important;
    box-shadow: 0 0 15px rgba(120, 119, 198, 0.4);
}

.btn-confirm.active:hover {
    background: linear-gradient(135deg, #8a89d4 0%, #6b6bac 100%) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 20px rgba(120, 119, 198, 0.5);
}

/* 空状态样式增强 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: rgba(255, 255, 255, 0.5);
    animation: fadeIn 0.5s ease;
}

.empty-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.3;
    animation: float 3s ease-in-out infinite;
}

.empty-state h3 {
    font-size: 18px;
    margin-bottom: 8px;
    color: rgba(255, 255, 255, 0.7);
    font-family: 'Orbitron', monospace;
}

.empty-state p {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.5);
    line-height: 1.5;
}

/* 响应式增强 */
@media (max-width: 768px) {
    .selector-actions {
        justify-content: center;
    }
    
    .btn-action {
        flex: 1;
        min-width: 80px;
        justify-content: center;
    }
    
    .selector-item-actions {
        top: 4px;
        right: 4px;
        gap: 2px;
    }
    
    .item-action-btn {
        width: 24px;
        height: 24px;
        font-size: 10px;
    }
    
    .move-dialog {
        width: 95%;
        margin: 0 10px;
    }
    
    .move-dialog-header {
        padding: 12px 16px;
    }
    
    .move-dialog-content {
        padding: 16px;
    }
    
    .move-dialog-footer,
    .confirm-dialog-footer {
        padding: 12px 16px;
        flex-direction: column;
        gap: 8px;
    }
    
    .move-dialog-footer .btn-cancel,
    .move-dialog-footer .btn-confirm,
    .confirm-dialog-footer .btn-cancel,
    .confirm-dialog-footer .btn-confirm {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .selector-actions {
        flex-direction: column;
    }
    
    .btn-action {
        width: 100%;
    }
    
    .empty-icon {
        font-size: 48px;
    }
    
    .empty-state h3 {
        font-size: 16px;
    }
    
    .empty-state p {
        font-size: 13px;
    }
} 
<?php
/**
 * 三只鱼网络科技 | 韩总 | 2025-06-11
 * 系统菜单模型 - ThinkPHP6企业级应用
 */

namespace app\model;

use think\Model;

class SysMenu extends Model
{
    // 设置表名
    protected $table = 'sys_menu';

    // 设置主键
    protected $pk = 'id';

    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'parent_id'   => 'int',
        'name'        => 'string',
        'link_type'   => 'string',
        'link_value'  => 'string',
        'url'         => 'string',
        'icon'        => 'string',
        'sort_order'  => 'int',
        'status'      => 'int',
        'remark'      => 'string',
        'description' => 'string',
        'created_at'  => 'datetime',
        'updated_at'  => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 类型转换
    protected $type = [
        'id'         => 'integer',
        'parent_id'  => 'integer',
        'sort_order' => 'integer',
        'status'     => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // 字段默认值
    protected $insert = [
        'parent_id'  => 0,
        'sort_order' => 0,
        'status'     => 1,
    ];

    /**
     * 关联子菜单
     */
    public function children()
    {
        return $this->hasMany(SysMenu::class, 'parent_id', 'id')
            ->where('status', 1)
            ->order('sort_order', 'desc');
    }

    /**
     * 关联父菜单
     */
    public function parent()
    {
        return $this->belongsTo(SysMenu::class, 'parent_id', 'id');
    }

    /**
     * 获取启用的菜单
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', 1);
    }

    /**
     * 获取顶级菜单
     */
    public function scopeTopLevel($query)
    {
        return $query->where('parent_id', 0);
    }

    /**
     * 按排序字段排序
     */
    public function scopeOrdered($query)
    {
        return $query->order('sort_order', 'desc')->order('id', 'asc');
    }

    /**
     * 获取菜单树形结构
     */
    public static function getMenuTree($parentId = 0, $onlyEnabled = true)
    {
        $query = self::where('parent_id', $parentId);
        
        if ($onlyEnabled) {
            $query->where('status', 1);
        }
        
        $menus = $query->order('sort_order', 'desc')
                      ->order('id', 'asc')
                      ->select();

        foreach ($menus as $menu) {
            $menu->children = self::getMenuTree($menu->id, $onlyEnabled);
        }

        return $menus;
    }

    /**
     * 构建菜单URL
     */
    public function getUrlAttribute($value)
    {
        if (!empty($value)) {
            return $value;
        }

        return $this->buildMenuUrl();
    }

    /**
     * 构建菜单URL
     */
    public function buildMenuUrl()
    {
        switch ($this->link_type) {
            case 'url':
                return $this->link_value;
            case 'page':
                // 页面类型：从选项中查找对应的slug
                $pageOptions = [
                    'home' => '/',
                    'contact' => '/contact',
                    'products' => '/products',
                    'solutions' => '/solutions',
                    'cases' => '/cases',
                    'news' => '/news'
                ];
                return $pageOptions[$this->link_value] ?? $this->link_value;
            case 'product_category':
                return '/products?category=' . $this->link_value;
            case 'product':
                return '/products/' . $this->link_value;
            case 'solution':
                return '/solutions/' . $this->link_value;
            case 'case':
                return '/cases/' . $this->link_value;
            case 'news_category':
                return '/news/category/' . $this->link_value;
            case 'news':
                return '/news/' . $this->link_value;
            case 'template':
                return '/page/' . $this->link_value;
            default:
                return '#';
        }
    }

    /**
     * 获取链接类型文本
     */
    public function getLinkTypeTextAttribute()
    {
        $types = [
            'url' => '自定义链接',
            'page' => '系统页面',
            'product_category' => '产品分类',
            'product' => '产品详情',
            'solution' => '解决方案',
            'case' => '客户案例',
            'news_category' => '新闻分类',
            'news' => '新闻详情',
            'template' => 'DIY页面'
        ];
        
        return $types[$this->link_type] ?? $this->link_type;
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        return $this->status == 1 ? '启用' : '禁用';
    }

    /**
     * 检查是否有子菜单
     */
    public function hasChildren()
    {
        return $this->children()->count() > 0;
    }

    /**
     * 获取所有子菜单ID（包括子子菜单）
     */
    public function getAllChildrenIds()
    {
        $ids = [];
        $children = $this->children;
        
        foreach ($children as $child) {
            $ids[] = $child->id;
            $ids = array_merge($ids, $child->getAllChildrenIds());
        }
        
        return $ids;
    }

    /**
     * 验证是否可以删除
     */
    public function canDelete()
    {
        return !$this->hasChildren();
    }

    /**
     * 获取面包屑导航
     */
    public function getBreadcrumb()
    {
        $breadcrumb = [];
        $current = $this;
        
        while ($current) {
            array_unshift($breadcrumb, [
                'id' => $current->id,
                'name' => $current->name,
                'url' => $current->buildMenuUrl()
            ]);
            $current = $current->parent;
        }
        
        return $breadcrumb;
    }

    /**
     * 获取菜单层级
     */
    public function getLevel()
    {
        $level = 1;
        $current = $this->parent;
        
        while ($current) {
            $level++;
            $current = $current->parent;
        }
        
        return $level;
    }
} 
{assign name="pageTitle" value="$pageData.pageTitle" /}

{include file="common/header"}

<!-- 🚀 超级科技感Hero区域 -->
<section class="quantum-hero-zone">
    <!-- 多维背景系统 -->
    <div class="quantum-bg-system">
        <!-- 主背景图层 -->
        <div class="bg-primary-layer" style="background-image: url('{:asset(\"assets/images/banner1.png\")}')"></div>

        <!-- 全息网格 -->
        <div class="holographic-grid">
            <div class="grid-matrix"></div>
            <div class="grid-scanlines"></div>
        </div>

        <!-- 量子粒子场 -->
        <div class="quantum-particles" id="quantumField"></div>

        <!-- 能量脉冲波 -->
        <div class="energy-pulse-waves">
            <div class="pulse-wave wave-alpha"></div>
            <div class="pulse-wave wave-beta"></div>
            <div class="pulse-wave wave-gamma"></div>
        </div>

        <!-- 数字雨效果 -->
        <div class="digital-rain" id="digitalRain"></div>

        <!-- 深度渐变遮罩 -->
        <div class="depth-gradient-mask"></div>
    </div>

    <!-- 核心内容区域 -->
    <div class="quantum-content-core">
        <div class="container-fluid px-4">
            <div class="row justify-content-center">
                <div class="col-xl-11 col-lg-12">

                    <!-- 🌟 超级主标题系统 -->
                    <div class="mega-title-matrix text-center" data-aos="fade-up" data-aos-delay="200">
                        <h1 class="quantum-mega-title text-center">

                            <div class="title-segment segment-1 text-center">
                                <span class="segment-bg-effect"></span>
                                <span class="segment-text">解决方案</span>
                                <span class="segment-energy-trail"></span>
                            </div>
                        </h1>
                        <div class="title-quantum-subtitle text-center">
                            <span class="subtitle-main">重新定义企业数字化转型边界</span>
                            <span class="subtitle-accent">让不可能成为可能</span>
                        </div>
                        <div class="title-hologram-effect"></div>
                    </div>

                    <!-- ⚡ 核心技术能力矩阵 -->
                    <div class="tech-capability-matrix" data-aos="fade-up" data-aos-delay="300">
                        <div class="capability-grid-system">
                            <div class="tech-node" data-tech="AI">
                                <div class="node-core">
                                    <div class="core-icon">
                                        <i class="fas fa-brain"></i>
                                    </div>
                                    <div class="core-pulse"></div>
                                </div>
                                <div class="node-label">人工智能</div>
                                <div class="node-energy-field"></div>
                                <div class="node-connections"></div>
                            </div>

                            <div class="tech-node" data-tech="QUANTUM">
                                <div class="node-core">
                                    <div class="core-icon">
                                        <i class="fas fa-atom"></i>
                                    </div>
                                    <div class="core-pulse"></div>
                                </div>
                                <div class="node-label">电商系统</div>
                                <div class="node-energy-field"></div>
                                <div class="node-connections"></div>
                            </div>

                            <div class="tech-node" data-tech="BLOCKCHAIN">
                                <div class="node-core">
                                    <div class="core-icon">
                                        <i class="fas fa-cube"></i>
                                    </div>
                                    <div class="core-pulse"></div>
                                </div>
                                <div class="node-label">区块链</div>
                                <div class="node-energy-field"></div>
                                <div class="node-connections"></div>
                            </div>

                            <div class="tech-node" data-tech="IOT">
                                <div class="node-core">
                                    <div class="core-icon">
                                        <i class="fas fa-network-wired"></i>
                                    </div>
                                    <div class="core-pulse"></div>
                                </div>
                                <div class="node-label">物联网</div>
                                <div class="node-energy-field"></div>
                                <div class="node-connections"></div>
                            </div>
                        </div>
                        <div class="matrix-connection-lines"></div>
                    </div>

                    <!-- 🎮 量子行动控制台 -->
                    <div class="quantum-action-console" data-aos="fade-up" data-aos-delay="500">
                        <div class="console-interface">
                            <a href="#quantum-solutions" class="btn-quantum-primary">
                                <div class="btn-quantum-core">
                                    <span class="btn-text">启动探索模式</span>
                                    <div class="btn-icon">
                                        <i class="fas fa-rocket"></i>
                                    </div>
                                </div>
                                <div class="btn-energy-matrix"></div>
                                <div class="btn-quantum-particles"></div>
                                <div class="btn-hologram-border"></div>
                            </a>

                            <a href="/contact" class="btn-quantum-secondary">
                                <div class="btn-quantum-core">
                                    <span class="btn-text">连接专家</span>
                                    <div class="btn-icon">
                                        <i class="fas fa-satellite-dish"></i>
                                    </div>
                                </div>
                                <div class="btn-neural-network"></div>
                                <div class="btn-signal-waves"></div>
                            </a>
                        </div>
                    </div>

                    <!-- 🌀 量子滚动传送器 -->
                    <div class="quantum-scroll-portal" data-aos="fade-up" data-aos-delay="600">
                        <div class="portal-core">
                            <div class="portal-rings">
                                <div class="p-ring p-ring-1"></div>
                                <div class="p-ring p-ring-2"></div>
                                <div class="p-ring p-ring-3"></div>
                            </div>
                            <div class="portal-center">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>
                        <div class="portal-text">传送至解决方案维度</div>
                        <div class="portal-energy-field"></div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- 🔮 边缘量子装饰 -->
    <div class="quantum-edge-decorations">
        <div class="edge-quantum-element edge-q-1"></div>
        <div class="edge-quantum-element edge-q-2"></div>
        <div class="edge-quantum-element edge-q-3"></div>
        <div class="edge-quantum-element edge-q-4"></div>
        <div class="quantum-field-lines"></div>
    </div>
</section>

<!-- 🚀 解决方案展示区域 -->
<section id="quantum-solutions" class="solutions-showcase-zone">
    <!-- 背景装饰系统 -->
    <div class="showcase-bg-system">
        <div class="neural-network-bg"></div>
        <div class="data-stream-lines"></div>
        <div class="floating-particles"></div>
    </div>

    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-xl-10 col-lg-11">

                <!-- 🎯 区域标题 -->
                <div class="section-title-matrix text-center" data-aos="fade-up" data-aos-delay="100">
                    <h2 class="section-mega-title">
                        <span class="title-glow-effect">企业级解决方案矩阵</span>
                    </h2>
                    <p class="section-subtitle">
                        基于前沿技术架构，为不同行业量身定制的数字化转型解决方案
                    </p>
                    <div class="title-energy-line"></div>
                </div>

                <!-- 🌐 解决方案网格 -->
                <div class="solutions-grid-matrix" data-aos="fade-up" data-aos-delay="200">
                    {volist name="solutions" id="solution"}
                    <div class="solution-quantum-card" data-aos="zoom-in" data-aos-delay="{$key * 100}">
                        <!-- 卡片能量边框 -->
                        <div class="card-energy-border"></div>

                        <!-- 卡片内容核心 -->
                        <div class="card-content-core">
                            <!-- 图标区域 -->
                            <div class="solution-icon-zone">
                                <div class="icon-quantum-core">
                                    {switch name="$solution.name"}
                                        {case value="社交分销"}
                                            <i class="fas fa-share-alt"></i>
                                        {/case}
                                        {case value="智能多门店"}
                                            <i class="fas fa-store"></i>
                                        {/case}
                                        {case value="大型多商户"}
                                            <i class="fas fa-building"></i>
                                        {/case}
                                        {case value="大货批发"}
                                            <i class="fas fa-boxes"></i>
                                        {/case}
                                        {case value="平台级供货商"}
                                            <i class="fas fa-truck"></i>
                                        {/case}
                                        {case value="本地生活服务"}
                                            <i class="fas fa-map-marker-alt"></i>
                                        {/case}
                                        {case value="企业数字化"}
                                            <i class="fas fa-digital-tachograph"></i>
                                        {/case}
                                        {case value="定制化方案"}
                                            <i class="fas fa-cogs"></i>
                                        {/case}
                                        {default /}
                                            <i class="fas fa-lightbulb"></i>
                                    {/switch}
                                    <div class="icon-pulse-ring"></div>
                                </div>
                                <div class="icon-energy-field"></div>
                            </div>

                            <!-- 标题区域 -->
                            <div class="solution-title-zone">
                                <h3 class="solution-title">{$solution.name}</h3>
                                <div class="title-underline-effect"></div>
                            </div>

                            <!-- 描述区域 -->
                            <div class="solution-desc-zone">
                                <p class="solution-description">
                                    {$solution.short_description|default=$solution.description}
                                </p>
                            </div>

                            <!-- 特性标签 -->
                            <div class="solution-features-zone">
                                <div class="features-grid">
                                    {switch name="$solution.name"}
                                        {case value="社交分销"}
                                            <span class="feature-tag">零成本获客</span>
                                            <span class="feature-tag">裂变增长</span>
                                            <span class="feature-tag">精准营销</span>
                                        {/case}
                                        {case value="智能多门店"}
                                            <span class="feature-tag">统一管理</span>
                                            <span class="feature-tag">实时同步</span>
                                            <span class="feature-tag">数据分析</span>
                                        {/case}
                                        {case value="大型多商户"}
                                            <span class="feature-tag">高并发</span>
                                            <span class="feature-tag">商户自管</span>
                                            <span class="feature-tag">安全保障</span>
                                        {/case}
                                        {case value="大货批发"}
                                            <span class="feature-tag">大宗交易</span>
                                            <span class="feature-tag">供应链</span>
                                            <span class="feature-tag">信用体系</span>
                                        {/case}
                                        {case value="平台级供货商"}
                                            <span class="feature-tag">AI驱动</span>
                                            <span class="feature-tag">智能补货</span>
                                            <span class="feature-tag">质量控制</span>
                                        {/case}
                                        {case value="本地生活服务"}
                                            <span class="feature-tag">LBS定位</span>
                                            <span class="feature-tag">同城配送</span>
                                            <span class="feature-tag">生活服务</span>
                                        {/case}
                                        {case value="企业数字化"}
                                            <span class="feature-tag">数字转型</span>
                                            <span class="feature-tag">流程优化</span>
                                            <span class="feature-tag">智能决策</span>
                                        {/case}
                                        {case value="定制化方案"}
                                            <span class="feature-tag">个性定制</span>
                                            <span class="feature-tag">专业服务</span>
                                            <span class="feature-tag">技术支持</span>
                                        {/case}
                                        {default /}
                                            <span class="feature-tag">智能化</span>
                                            <span class="feature-tag">高效率</span>
                                            <span class="feature-tag">可扩展</span>
                                    {/switch}
                                </div>
                            </div>

                            <!-- 行动按钮 -->
                            <div class="solution-action-zone">
                                <a href="/solutions/{$solution.slug}" class="btn-solution-explore">
                                    <span class="btn-text">深度探索</span>
                                    <div class="btn-quantum-arrow">
                                        <i class="fas fa-arrow-right"></i>
                                    </div>
                                    <div class="btn-hover-effect"></div>
                                </a>
                            </div>
                        </div>

                        <!-- 卡片悬浮效果 -->
                        <div class="card-hover-glow"></div>
                        <div class="card-data-streams"></div>
                    </div>
                    {/volist}
                </div>

                <!-- 🎮 底部行动区域 -->
                <div class="bottom-action-matrix text-center" data-aos="fade-up" data-aos-delay="600">
                    <div class="action-content-core">
                        <h3 class="action-title">
                            <span class="title-quantum-effect">需要定制化解决方案？</span>
                        </h3>
                        <p class="action-subtitle">
                            我们的专家团队随时为您提供专业的技术咨询和定制化服务
                        </p>
                        <div class="action-buttons-group">
                            <a href="/contact" class="btn-action-primary">
                                <div class="btn-core">
                                    <span class="btn-text">联系专家</span>
                                    <i class="fas fa-user-tie"></i>
                                </div>
                                <div class="btn-energy-wave"></div>
                            </a>
                            <a href="/cases" class="btn-action-secondary">
                                <div class="btn-core">
                                    <span class="btn-text">查看案例</span>
                                    <i class="fas fa-briefcase"></i>
                                </div>
                                <div class="btn-neural-effect"></div>
                            </a>
                        </div>
                    </div>
                    <div class="action-bg-effects"></div>
                </div>

            </div>
        </div>
    </div>

    <!-- 区域装饰元素 -->
    <div class="showcase-decorations">
        <div class="deco-element deco-1"></div>
        <div class="deco-element deco-2"></div>
        <div class="deco-element deco-3"></div>
    </div>
</section>

<!-- 🎨 解决方案页面样式 -->
<style>

/* ===== 量子Hero区域 ===== */
.quantum-hero-zone {
    position: relative;
    min-height: 100vh;
    align-items: center;
    overflow: hidden;
    background: radial-gradient(ellipse at center, #0a0a0a 0%, #000000 100%);
}

/* 多维背景系统 */
.quantum-bg-system {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.bg-primary-layer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0.15;
    filter: blur(1px);
}

/* 全息网格 */
.holographic-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.3;
}

.grid-matrix {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 20s linear infinite;
}

.grid-scanlines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        0deg,
        transparent,
        transparent 2px,
        rgba(0, 255, 255, 0.03) 2px,
        rgba(0, 255, 255, 0.03) 4px
    );
    animation: scanlineMove 3s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

@keyframes scanlineMove {
    0% { transform: translateY(0); }
    100% { transform: translateY(4px); }
}

/* 量子粒子场 */
.quantum-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 30%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(255, 0, 255, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(0, 255, 127, 0.06) 0%, transparent 50%);
    animation: quantumFloat 25s ease-in-out infinite;
}

@keyframes quantumFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-30px) rotate(90deg); }
    50% { transform: translateY(-15px) rotate(180deg); }
    75% { transform: translateY(-45px) rotate(270deg); }
}

/* 能量脉冲波 */
.energy-pulse-waves {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.pulse-wave {
    position: absolute;
    width: 200px;
    height: 200px;
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 50%;
    animation: pulseExpand 4s ease-out infinite;
}

.wave-alpha {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.wave-beta {
    top: 60%;
    right: 15%;
    animation-delay: 1.3s;
    border-color: rgba(255, 0, 255, 0.3);
}

.wave-gamma {
    bottom: 30%;
    left: 30%;
    animation-delay: 2.6s;
    border-color: rgba(0, 255, 127, 0.3);
}

@keyframes pulseExpand {
    0% {
        transform: scale(0.5);
        opacity: 1;
    }
    100% {
        transform: scale(3);
        opacity: 0;
    }
}

/* 深度渐变遮罩 */
.depth-gradient-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.8) 0%,
        rgba(10, 20, 40, 0.6) 30%,
        rgba(20, 40, 80, 0.4) 70%,
        rgba(0, 0, 0, 0.8) 100%
    );
    z-index: 2;
}

/* 核心内容区域 */
.quantum-content-core {
    position: relative;
    z-index: 10;
    padding: 120px 0 80px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 80vh;
}

.quantum-content-core .container-fluid {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
}

.quantum-content-core .row {
    justify-content: center;
    align-items: center;
}

.quantum-content-core .col-xl-11,
.quantum-content-core .col-lg-12 {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}



/* 超级主标题系统 */
.mega-title-matrix {
    margin-bottom: 4rem;
    position: relative;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.quantum-mega-title {
    font-family: 'Orbitron', monospace;
    font-size: 5rem;
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 2rem;
    position: relative;
    text-align: center;
    width: 100%;
}

.title-segment {
    display: flex;
    position: relative;
    margin-bottom: 1rem;
    overflow: hidden;
    text-align: center;
    width: 100%;
    justify-content: center;
    align-items: center;
}

.segment-bg-effect {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
    animation: segmentScan 3s ease-in-out infinite;
    z-index: 1;
}

.segment-text {
    position: relative;
    z-index: 2;
    background: linear-gradient(135deg, #8282e1 0%, #ffffff 50%, #1a1a2e 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
    animation: textGlow 2s ease-in-out infinite alternate;
    display: block;
    text-align: center;
    width: 100%;
    flex: 1;
    font-weight: 700;
    

}

.segment-energy-trail {
    position: absolute;
    top: 50%;
    right: -50px;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, #00ffff, transparent);
    transform: translateY(-50%);
    animation: energyTrail 2s ease-in-out infinite;
    pointer-events: none;
}

@keyframes segmentScan {
    0%, 100% { left: -100%; }
    50% { left: 100%; }
}

@keyframes textGlow {
    0% { filter: brightness(1) drop-shadow(0 0 10px rgba(0, 255, 255, 0.3)); }
    100% { filter: brightness(1.2) drop-shadow(0 0 20px rgba(0, 255, 255, 0.6)); }
}

@keyframes energyTrail {
    0%, 100% { opacity: 0; transform: translateY(-50%) translateX(0); }
    50% { opacity: 1; transform: translateY(-50%) translateX(20px); }
}

.title-quantum-subtitle {
    text-align: center;
    margin-bottom: 2rem;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.subtitle-main {
    display: block;
    font-size: 1.4rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0.5rem;
    font-weight: 300;
    text-align: center;
    width: 100%;
}

.subtitle-accent {
    display: block;
    font-size: 1.1rem;
    color: #00ffff;
    font-weight: 500;
    text-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
    text-align: center;
    width: 100%;
}

.title-hologram-effect {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        45deg,
        transparent 30%,
        rgba(0, 255, 255, 0.1) 50%,
        transparent 70%
    );
    animation: hologramShift 4s ease-in-out infinite;
    pointer-events: none;
}

@keyframes hologramShift {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

/* 响应式标题居中 */
@media (max-width: 768px) {
    .quantum-mega-title {
        font-size: 3.5rem;
    }

    .title-segment {
        margin-bottom: 0.5rem;
    }

    .subtitle-main {
        font-size: 1.2rem;
    }

    .subtitle-accent {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .quantum-mega-title {
        font-size: 2.5rem;
    }

    .subtitle-main {
        font-size: 1rem;
    }

    .subtitle-accent {
        font-size: 0.9rem;
    }
}

/* 核心技术能力矩阵 */
.tech-capability-matrix {
    margin-bottom: 4rem;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.capability-grid-system {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    justify-items: center;
    align-items: center;
}

.tech-node {
    position: relative;
    background: rgba(0, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem 1.5rem;
    text-align: center;
    transition: all 0.4s ease;
    overflow: hidden;
}

.tech-node::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.tech-node:hover::before {
    left: 100%;
}

.tech-node:hover {
    transform: translateY(-10px);
    border-color: rgba(0, 255, 255, 0.5);
    box-shadow: 0 20px 40px rgba(0, 255, 255, 0.2);
}

.node-core {
    position: relative;
    width: 60px;
    height: 60px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.core-icon i {
    font-size: 1.5rem;
    color: white;
}

.core-pulse {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border: 2px solid rgba(0, 255, 255, 0.5);
    border-radius: 50%;
    animation: corePulse 2s ease-in-out infinite;
}

@keyframes corePulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.5; }
}

.node-label {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 1rem;
}

.node-energy-field {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120%;
    height: 120%;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.tech-node:hover .node-energy-field {
    opacity: 1;
}

.matrix-connection-lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.matrix-connection-lines::before,
.matrix-connection-lines::after {
    content: '';
    position: absolute;
    background: linear-gradient(90deg, transparent 0%, rgba(0, 255, 255, 0.3) 50%, transparent 100%);
    height: 1px;
    animation: connectionPulse 3s ease-in-out infinite;
}

.matrix-connection-lines::before {
    top: 30%;
    left: 0;
    right: 0;
}

.matrix-connection-lines::after {
    bottom: 30%;
    left: 0;
    right: 0;
    animation-delay: 1.5s;
}

@keyframes connectionPulse {
    0%, 100% { opacity: 0; transform: scaleX(0); }
    50% { opacity: 1; transform: scaleX(1); }
}



/* 量子行动控制台 */
.quantum-action-console {
    margin-bottom: 4rem;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.console-interface {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
    width: 100%;
}

.btn-quantum-primary,
.btn-quantum-secondary {
    position: relative;
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    border-radius: 50px;
    padding: 1.5rem 3rem;
    font-size: 1.1rem;
    font-weight: 700;
    transition: all 0.4s ease;
    overflow: hidden;
    min-width: 250px;
    justify-content: center;
}

.btn-quantum-primary {
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    color: white;
}

.btn-quantum-secondary {
    background: transparent;
    color: #00ffff;
    border: 2px solid rgba(0, 255, 255, 0.5);
}

.btn-quantum-core {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.btn-icon {
    transition: transform 0.3s ease;
}

.btn-quantum-primary:hover .btn-icon,
.btn-quantum-secondary:hover .btn-icon {
    transform: translateX(5px);
}

.btn-quantum-primary:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 255, 255, 0.4);
    color: white;
}

.btn-quantum-secondary:hover {
    background: rgba(0, 255, 255, 0.1);
    border-color: rgba(0, 255, 255, 0.8);
    transform: translateY(-5px);
    color: #00ffff;
}

.btn-energy-matrix {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff7f, #ffd700);
    border-radius: 50px;
    z-index: -1;
    opacity: 0;
    animation: energyMatrix 3s ease-in-out infinite;
}

@keyframes energyMatrix {
    0%, 100% { opacity: 0; }
    50% { opacity: 0.3; }
}

.btn-quantum-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    animation: btnParticles 2s ease-in-out infinite;
    z-index: 1;
}

@keyframes btnParticles {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

.btn-hologram-border {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    animation: hologramBorder 4s ease-in-out infinite;
}

@keyframes hologramBorder {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.05); }
}

.btn-neural-network {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 30%, rgba(0, 255, 255, 0.1) 50%, transparent 70%);
    animation: neuralPulse 3s ease-in-out infinite;
}

@keyframes neuralPulse {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

.btn-signal-waves {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid rgba(0, 255, 255, 0.5);
    border-radius: 50%;
    animation: signalWaves 2s ease-out infinite;
}

@keyframes signalWaves {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
    }
}

/* 量子滚动传送器 */
.quantum-scroll-portal {
    position: absolute;
    bottom: -80px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    animation: portalFloat 3s ease-in-out infinite;
}

.portal-core {
    position: relative;
    width: 60px;
    height: 60px;
    margin: 0 auto 1rem;
}

.portal-rings {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.p-ring {
    position: absolute;
    border: 1px solid rgba(0, 255, 255, 0.4);
    border-radius: 50%;
    animation: portalRingRotate 4s linear infinite;
}

.p-ring-1 {
    top: 5px;
    left: 5px;
    right: 5px;
    bottom: 5px;
    animation-duration: 2s;
}

.p-ring-2 {
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    animation-duration: 3s;
    animation-direction: reverse;
}

.p-ring-3 {
    top: 15px;
    left: 15px;
    right: 15px;
    bottom: 15px;
    animation-duration: 4s;
}

.portal-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.2rem;
    color: #00ffff;
    animation: portalPulse 2s ease-in-out infinite;
}

@keyframes portalFloat {
    0%, 100% { transform: translateX(-50%) translateY(0); }
    50% { transform: translateX(-50%) translateY(-10px); }
}

@keyframes portalRingRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes portalPulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.2); }
}

.portal-text {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.portal-energy-field {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: portalEnergyField 3s ease-in-out infinite;
}

@keyframes portalEnergyField {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.3); }
}

/* 边缘量子装饰 */
.quantum-edge-decorations {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 5;
    pointer-events: none;
}

.edge-quantum-element {
    position: absolute;
    width: 100px;
    height: 100px;
    border: 1px solid rgba(0, 255, 255, 0.2);
    animation: edgeElementFloat 8s ease-in-out infinite;
}

.edge-q-1 {
    top: 10%;
    left: 5%;
    border-radius: 50%;
    animation-delay: 0s;
}

.edge-q-2 {
    top: 20%;
    right: 8%;
    transform: rotate(45deg);
    animation-delay: 2s;
}

.edge-q-3 {
    bottom: 30%;
    left: 10%;
    border-radius: 20px;
    animation-delay: 4s;
}

.edge-q-4 {
    bottom: 15%;
    right: 5%;
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    animation-delay: 6s;
}

@keyframes edgeElementFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.3; }
    50% { transform: translateY(-30px) rotate(180deg); opacity: 0.8; }
}

.quantum-field-lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 49%, rgba(0, 255, 255, 0.1) 50%, transparent 51%),
        linear-gradient(-45deg, transparent 49%, rgba(255, 0, 255, 0.1) 50%, transparent 51%);
    background-size: 200px 200px;
    animation: fieldLinesMove 10s linear infinite;
}

@keyframes fieldLinesMove {
    0% { background-position: 0 0, 0 0; }
    100% { background-position: 200px 200px, -200px 200px; }
}

/* ===== 解决方案展示区域样式 ===== */
.solutions-showcase-zone {
    position: relative;
    padding: 120px 0;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    overflow: hidden;
}

/* 背景装饰系统 */
.showcase-bg-system {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.neural-network-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 0, 255, 0.08) 0%, transparent 50%);
    animation: neuralPulse 8s ease-in-out infinite;
}

.data-stream-lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 48%, rgba(0, 255, 255, 0.1) 49%, rgba(0, 255, 255, 0.1) 51%, transparent 52%),
        linear-gradient(-45deg, transparent 48%, rgba(255, 0, 255, 0.08) 49%, rgba(255, 0, 255, 0.08) 51%, transparent 52%);
    background-size: 100px 100px;
    animation: streamFlow 15s linear infinite;
}

.floating-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 30%, rgba(0, 255, 127, 0.05) 0%, transparent 40%),
        radial-gradient(circle at 80% 70%, rgba(255, 127, 0, 0.05) 0%, transparent 40%);
    animation: particleFloat 12s ease-in-out infinite;
}

@keyframes neuralPulse {
    0%, 100% { opacity: 0.6; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.05); }
}

@keyframes streamFlow {
    0% { background-position: 0 0, 0 0; }
    100% { background-position: 100px 100px, -100px 100px; }
}

@keyframes particleFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(-10px) rotate(240deg); }
}

/* 区域标题样式 */
.section-title-matrix {
    position: relative;
    z-index: 10;
    margin-bottom: 4rem;
}

.section-mega-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    font-family: 'Orbitron', monospace;
}

.title-glow-effect {
    background: linear-gradient(135deg, #ffffff 0%, #00ffff 50%, #ff00ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
    animation: titleGlow 3s ease-in-out infinite alternate;
}

.section-subtitle {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.title-energy-line {
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, transparent, #00ffff, transparent);
    margin: 0 auto;
    animation: energyLinePulse 2s ease-in-out infinite;
}

@keyframes titleGlow {
    0% { filter: brightness(1) drop-shadow(0 0 20px rgba(0, 255, 255, 0.3)); }
    100% { filter: brightness(1.3) drop-shadow(0 0 40px rgba(0, 255, 255, 0.6)); }
}

@keyframes energyLinePulse {
    0%, 100% { opacity: 0.5; transform: scaleX(1); }
    50% { opacity: 1; transform: scaleX(1.5); }
}

/* 解决方案网格 */
.solutions-grid-matrix {
    position: relative;
    z-index: 10;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
    margin-bottom: 5rem;
}

/* 解决方案卡片 */
.solution-quantum-card {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2.5rem 2rem;
    transition: all 0.4s ease;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.solution-quantum-card:hover {
    transform: translateY(-10px);
    border-color: rgba(0, 255, 255, 0.5);
    box-shadow: 0 25px 50px rgba(0, 255, 255, 0.2);
}

.card-energy-border {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff7f, #ffd700);
    border-radius: 20px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.solution-quantum-card:hover .card-energy-border {
    opacity: 0.3;
}

.card-content-core {
    position: relative;
    z-index: 2;
}

/* 图标区域 */
.solution-icon-zone {
    position: relative;
    text-align: center;
    margin-bottom: 2rem;
}

.icon-quantum-core {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto;
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
}

.icon-pulse-ring {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border: 2px solid rgba(0, 255, 255, 0.4);
    border-radius: 50%;
    animation: iconPulse 2s ease-in-out infinite;
}

.icon-energy-field {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.solution-quantum-card:hover .icon-energy-field {
    opacity: 1;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); opacity: 0.6; }
    50% { transform: scale(1.1); opacity: 1; }
}

/* 标题区域 */
.solution-title-zone {
    text-align: center;
    margin-bottom: 1.5rem;
}

.solution-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.5rem;
    font-family: 'Orbitron', monospace;
}

.title-underline-effect {
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00ffff, transparent);
    margin: 0 auto;
    animation: underlinePulse 2s ease-in-out infinite;
}

@keyframes underlinePulse {
    0%, 100% { opacity: 0.5; transform: scaleX(1); }
    50% { opacity: 1; transform: scaleX(1.2); }
}

/* 描述区域 */
.solution-desc-zone {
    margin-bottom: 2rem;
}

.solution-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    font-size: 1rem;
    text-align: center;
}

/* 特性标签 */
.solution-features-zone {
    margin-bottom: 2rem;
}

.features-grid {
    display: flex;
    justify-content: center;
    gap: 0.8rem;
    flex-wrap: wrap;
}

.feature-tag {
    padding: 0.4rem 1rem;
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 20px;
    color: #00ffff;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.feature-tag:hover {
    background: rgba(0, 255, 255, 0.2);
    border-color: rgba(0, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* 行动按钮 */
.solution-action-zone {
    text-align: center;
}

.btn-solution-explore {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1rem 2rem;
    background: transparent;
    border: 2px solid rgba(0, 255, 255, 0.4);
    border-radius: 30px;
    color: #00ffff;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.4s ease;
    overflow: hidden;
}

.btn-solution-explore:hover {
    border-color: rgba(0, 255, 255, 0.8);
    color: #ffffff;
    transform: translateY(-2px);
}

.btn-quantum-arrow {
    transition: transform 0.3s ease;
}

.btn-solution-explore:hover .btn-quantum-arrow {
    transform: translateX(5px);
}

.btn-hover-effect {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.btn-solution-explore:hover .btn-hover-effect {
    left: 100%;
}

/* 卡片悬浮效果 */
.card-hover-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.solution-quantum-card:hover .card-hover-glow {
    opacity: 1;
}

.card-data-streams {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 48%, rgba(0, 255, 255, 0.05) 49%, rgba(0, 255, 255, 0.05) 51%, transparent 52%);
    background-size: 20px 20px;
    animation: cardStreamFlow 8s linear infinite;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.solution-quantum-card:hover .card-data-streams {
    opacity: 1;
}

@keyframes cardStreamFlow {
    0% { background-position: 0 0; }
    100% { background-position: 20px 20px; }
}

/* 底部行动区域 */
.bottom-action-matrix {
    position: relative;
    z-index: 10;
    padding: 3rem 2rem;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 20px;
    border: 1px solid rgba(0, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.action-content-core {
    position: relative;
    z-index: 2;
}

.action-title {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    font-family: 'Orbitron', monospace;
}

.title-quantum-effect {
    background: linear-gradient(135deg, #ffffff 0%, #00ffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.action-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2.5rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.action-buttons-group {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.btn-action-primary,
.btn-action-secondary {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1.2rem 2.5rem;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.4s ease;
    overflow: hidden;
}

.btn-action-primary {
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    color: white;
}

.btn-action-secondary {
    background: transparent;
    border: 2px solid rgba(0, 255, 255, 0.5);
    color: #00ffff;
}

.btn-action-primary:hover,
.btn-action-secondary:hover {
    transform: translateY(-3px);
    color: white;
}

.btn-action-primary:hover {
    box-shadow: 0 15px 30px rgba(0, 255, 255, 0.4);
}

.btn-action-secondary:hover {
    background: rgba(0, 255, 255, 0.1);
    border-color: rgba(0, 255, 255, 0.8);
}

.btn-energy-wave {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transition: all 0.6s ease;
}

.btn-action-primary:hover .btn-energy-wave {
    width: 200%;
    height: 200%;
}

.btn-neural-effect {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.btn-action-secondary:hover .btn-neural-effect {
    left: 100%;
}

.action-bg-effects {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 0, 255, 0.05) 0%, transparent 50%);
    animation: actionBgPulse 4s ease-in-out infinite;
}

@keyframes actionBgPulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* 区域装饰元素 */
.showcase-decorations {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 5;
    pointer-events: none;
}

.deco-element {
    position: absolute;
    width: 60px;
    height: 60px;
    border: 1px solid rgba(0, 255, 255, 0.2);
    animation: decoFloat 6s ease-in-out infinite;
}

.deco-1 {
    top: 10%;
    left: 5%;
    border-radius: 50%;
    animation-delay: 0s;
}

.deco-2 {
    top: 70%;
    right: 8%;
    transform: rotate(45deg);
    animation-delay: 2s;
}

.deco-3 {
    bottom: 20%;
    left: 10%;
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    animation-delay: 4s;
}

@keyframes decoFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.3; }
    50% { transform: translateY(-20px) rotate(180deg); opacity: 0.8; }
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .quantum-mega-title {
        font-size: 4rem;
    }

    .capability-grid-system {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .quantum-content-core {
        padding: 100px 0 60px;
        min-height: 70vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .quantum-mega-title {
        font-size: 2.8rem;
    }

    .title-segment {
        margin-bottom: 0.5rem;
    }

    .subtitle-main {
        font-size: 1.1rem;
    }

    .subtitle-accent {
        font-size: 1rem;
    }

    .capability-grid-system {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .console-interface {
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
    }

    .btn-quantum-primary,
    .btn-quantum-secondary {
        width: 100%;
        max-width: 300px;
        padding: 1.2rem 2rem;
    }

    /* 解决方案展示区域移动端适配 */
    .solutions-showcase-zone {
        padding: 80px 0;
    }

    .section-mega-title {
        font-size: 2.5rem;
    }

    .section-subtitle {
        font-size: 1.1rem;
        padding: 0 1rem;
    }

    .solutions-grid-matrix {
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 0 1rem;
    }

    .solution-quantum-card {
        padding: 2rem 1.5rem;
    }

    .icon-quantum-core {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .solution-title {
        font-size: 1.3rem;
    }

    .action-title {
        font-size: 1.8rem;
    }

    .action-buttons-group {
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
    }

    .btn-action-primary,
    .btn-action-secondary {
        width: 100%;
        max-width: 280px;
        padding: 1rem 2rem;
    }

    /* 移动端禁用复杂动画 */
    .quantum-particles,
    .holographic-grid,
    .energy-pulse-waves,
    .digital-rain,
    .neural-network-bg,
    .data-stream-lines,
    .floating-particles {
        display: none;
    }

    .bg-primary-layer {
        opacity: 0.1;
    }

    .showcase-bg-system {
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(26, 26, 46, 0.6) 100%);
    }
}

@media (max-width: 480px) {
    .quantum-content-core {
        padding: 80px 0 50px;
        min-height: 60vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .quantum-mega-title {
        font-size: 2.2rem;
    }

    /* 解决方案展示区域小屏幕适配 */
    .solutions-showcase-zone {
        padding: 60px 0;
    }

    .section-mega-title {
        font-size: 2rem;
    }

    .section-subtitle {
        font-size: 1rem;
        padding: 0 1.5rem;
    }

    .solutions-grid-matrix {
        padding: 0 1.5rem;
        gap: 1.5rem;
    }

    .solution-quantum-card {
        padding: 1.5rem 1rem;
    }

    .icon-quantum-core {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .solution-title {
        font-size: 1.2rem;
    }

    .solution-description {
        font-size: 0.9rem;
    }

    .feature-tag {
        padding: 0.3rem 0.8rem;
        font-size: 0.8rem;
    }

    .btn-solution-explore {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    .bottom-action-matrix {
        padding: 2rem 1rem;
        margin: 0 1rem;
    }

    .action-title {
        font-size: 1.5rem;
    }

    .action-subtitle {
        font-size: 1rem;
    }

    .btn-action-primary,
    .btn-action-secondary {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }
}
</style>

<!-- 🚀 量子级JavaScript系统 -->
<script src="assets/js/aos.js"></script>
<script>

// 初始化AOS动画系统
AOS.init({
    duration: 1000,
    easing: 'ease-out-cubic',
    once: true,
    offset: 100,
    delay: 100
});

// 解决方案页面系统
document.addEventListener('DOMContentLoaded', function() {
    // 添加页面类名
    document.body.classList.add('solutions-page', 'page-loaded');

    // 平滑滚动
    initSmoothScrolling();

    // 初始化解决方案链接
    initSolutionLinks();
});

// 平滑滚动
function initSmoothScrolling() {
    const scrollLinks = document.querySelectorAll('a[href^="#"]');

    scrollLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            // 只对锚点链接进行平滑滚动处理
            const href = this.getAttribute('href');
            if (href.startsWith('#') && href.length > 1) {
                e.preventDefault();
                const targetId = href.substring(1);
                const target = document.getElementById(targetId);

                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });
    });
}

// 确保解决方案链接正常工作
function initSolutionLinks() {
    const solutionLinks = document.querySelectorAll('.btn-solution-explore');

    solutionLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            // 确保链接正常跳转，不阻止默认行为
            const href = this.getAttribute('href');
            if (href && href.startsWith('/solutions/')) {
                // 让链接正常跳转
                window.location.href = href;
            }
        });
    });
}

// 页面加载完成
window.addEventListener('load', function() {
    document.body.style.opacity = '1';
    document.body.classList.add('page-loaded');
});

// 页面加载样式
const loadStyle = document.createElement('style');
loadStyle.textContent = `
    body.solutions-page {
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
    }

    body.solutions-page.page-loaded {
        opacity: 1;
    }
`;
document.head.appendChild(loadStyle);
</script>

{include file="common/footer"}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - 后台管理系统</title>

    <!-- CSS -->
    {include file="admin/common/css"}
</head>
<body>
    <!-- 顶部导航 -->
    {include file="admin/common/header"}
    <style>
        /* 设置页面样式 */
        .settings-container {
            max-width: 100%;
            margin: 0;
            padding: 0;
        }
        

        
        /* 页面标题 */
        .page-header {
            background: rgba(20, 20, 30, 0.6);
            border: 1px solid rgba(120, 119, 198, 0.3);
            border-radius: 15px;
            padding: 25px 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }
        
        .header-content {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .header-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, rgba(120, 119, 198, 0.8), rgba(255, 119, 198, 0.6));
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            box-shadow: 0 8px 25px rgba(120, 119, 198, 0.3);
        }
        
        .page-title {
            font-family: 'Orbitron', monospace;
            font-size: 28px;
            font-weight: 700;
            color: #fff;
            margin: 0 0 8px 0;
            text-shadow: 0 0 20px rgba(120, 119, 198, 0.6);
        }
        
        .page-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 16px;
            margin: 0;
            font-weight: 400;
        }
        
        /* 选项卡容器 */
        .settings-tabs-container {
            background: transparent;
            border-radius: 0;
            overflow: visible;
        }
        
        /* 选项卡导航 */
        .tabs-header {
            margin-bottom: 30px;
        }
        
        .tab-nav {
            display: flex;
            gap: 8px;
            background: rgba(15, 15, 15, 0.4);
            padding: 8px;
            border-radius: 15px;
            border: 1px solid rgba(120, 119, 198, 0.2);
            backdrop-filter: blur(10px);
        }
        
        .tab-btn {
            background: transparent;
            border: none;
            padding: 12px 20px;
            border-radius: 10px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 120px;
            justify-content: center;
        }
        
        .tab-btn:hover {
            background: rgba(120, 119, 198, 0.1);
            color: rgba(255, 255, 255, 0.9);
        }
        
        .tab-btn.active {
            background: linear-gradient(135deg, rgba(120, 119, 198, 0.6), rgba(255, 119, 198, 0.4));
            color: #ffffff;
            box-shadow: 0 4px 15px rgba(120, 119, 198, 0.3);
        }
        
        .tab-btn i {
            font-size: 16px;
        }
        
        /* 选项卡内容 */
        .tabs-content {
            position: relative;
        }
        
        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease-in-out;
        }
        
        .tab-content.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* 设置卡片 */
        .settings-card {
            background: rgba(20, 20, 30, 0.6);
            border: 1px solid rgba(120, 119, 198, 0.3);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            overflow: hidden;
        }
        
        .settings-card .card-header {
            padding: 30px 35px 25px 35px;
            border-bottom: 1px solid rgba(120, 119, 198, 0.3);
            background: linear-gradient(135deg, rgba(15, 15, 15, 0.5), rgba(25, 25, 35, 0.4));
            position: relative;
            margin-bottom: 5px;
        }
        
        .settings-card .card-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 35px;
            right: 35px;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(120, 119, 198, 0.6), transparent);
        }
        
        .card-title {
            display: flex;
            align-items: center;
            gap: 15px;
            font-family: 'Orbitron', monospace;
            font-size: 22px;
            font-weight: 700;
            color: #fff;
            margin: 0 0 12px 0;
            text-shadow: 0 0 20px rgba(120, 119, 198, 0.6);
        }
        
        .card-title i {
            font-size: 20px;
            color: rgba(120, 119, 198, 0.9);
            background: rgba(120, 119, 198, 0.1);
            padding: 8px;
            border-radius: 8px;
            border: 1px solid rgba(120, 119, 198, 0.3);
        }
        
        .card-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 15px;
            margin: 0;
            padding-left: 35px; /* 与card-body的padding对齐 */
            margin-left: -35px; /* 抵消header的padding */
            margin-right: -35px; /* 抵消header的padding */
            padding-right: 35px;
            padding-bottom: 8px;
            font-weight: 400;
            line-height: 1.5;
        }
        
        .settings-card .card-body {
            padding: 35px;
            margin-top: 10px;
        }
        
        /* 表单样式 */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-label {
            display: flex;
            align-items: center;
            gap: 10px;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .form-label i {
            color: rgba(120, 119, 198, 0.8);
            width: 16px;
            flex-shrink: 0;
        }
        
        .form-input,
        .form-textarea {
            width: 100%;
            background: rgba(15, 15, 15, 0.6);
            border: 1px solid rgba(120, 119, 198, 0.3);
            border-radius: 10px;
            padding: 12px 16px;
            color: #fff;
            font-size: 14px;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }
        
        .form-input:focus,
        .form-textarea:focus {
            outline: none;
            border-color: rgba(120, 119, 198, 0.6);
            box-shadow: 0 0 0 3px rgba(120, 119, 198, 0.1);
            background: rgba(15, 15, 15, 0.8);
        }
        
        .form-input::placeholder,
        .form-textarea::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .form-help {
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
            margin-top: 6px;
            line-height: 1.4;
        }
        
        /* 开关切换器样式 */
        .switch-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 20px;
            background: rgba(15, 15, 15, 0.3);
            border: 1px solid rgba(120, 119, 198, 0.2);
            border-radius: 12px;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }
        
        .switch-container:hover {
            background: rgba(15, 15, 15, 0.5);
            border-color: rgba(120, 119, 198, 0.3);
        }
        
        .switch-label {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .switch-container:hover .switch-label {
            color: rgba(255, 255, 255, 1);
        }
        
        /* 开关本体 */
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 32px;
            flex-shrink: 0;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        /* 开关轨道 */
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(60, 60, 80, 0.8);
            border: 2px solid rgba(80, 80, 100, 0.6);
            border-radius: 32px;
            transition: all 0.3s ease;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        /* 开关圆球 */
        .slider:before {
            position: absolute;
            content: "";
            height: 24px;
            width: 24px;
            left: 2px;
            top: 2px;
            background: linear-gradient(135deg, #ffffff, #e8e8e8);
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow:
                0 2px 4px rgba(0, 0, 0, 0.3),
                0 1px 2px rgba(0, 0, 0, 0.2);
        }
        
        /* 悬停效果 */
        .switch:hover .slider {
            background: rgba(70, 70, 90, 0.9);
            border-color: rgba(90, 90, 110, 0.7);
        }
        
        .switch:hover .slider:before {
            box-shadow:
                0 3px 6px rgba(0, 0, 0, 0.4),
                0 1px 3px rgba(0, 0, 0, 0.3);
        }
        
        /* 选中状态 */
        input:checked + .slider {
            background: linear-gradient(135deg, rgba(120, 119, 198, 0.9), rgba(255, 119, 198, 0.7));
            border-color: rgba(120, 119, 198, 0.8);
            box-shadow:
                inset 0 2px 4px rgba(0, 0, 0, 0.1),
                0 0 15px rgba(120, 119, 198, 0.4),
                0 2px 8px rgba(120, 119, 198, 0.2);
        }
        
        input:checked + .slider:before {
            transform: translateX(28px);
            background: linear-gradient(135deg, #ffffff, #f0f0f0);
            box-shadow:
                0 2px 6px rgba(0, 0, 0, 0.2),
                0 1px 3px rgba(0, 0, 0, 0.1),
                0 0 8px rgba(255, 255, 255, 0.3);
        }
        
        /* 选中时容器效果 */
        .switch-container:has(input:checked) {
            background: rgba(120, 119, 198, 0.08);
            border-color: rgba(120, 119, 198, 0.3);
        }
        
        .switch-container:has(input:checked) .switch-label {
            color: rgba(255, 255, 255, 1);
        }
        
        /* 表单操作按钮 */
        .form-actions {
            padding-top: 20px;
            border-top: 1px solid rgba(120, 119, 198, 0.2);
            display: flex;
            justify-content: flex-end;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, rgba(120, 119, 198, 0.6), rgba(255, 119, 198, 0.4));
            color: #ffffff;
            border: 1px solid rgba(120, 119, 198, 0.6);
            padding: 12px 24px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            box-shadow: 0 4px 15px rgba(120, 119, 198, 0.2);
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, rgba(120, 119, 198, 0.8), rgba(255, 119, 198, 0.6));
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(120, 119, 198, 0.3);
            color: #ffffff;
            text-decoration: none;
        }
        
        .btn-primary i {
            font-size: 14px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
        
            .tab-nav {
                flex-direction: column;
                gap: 4px;
            }
        
            .tab-btn {
                justify-content: flex-start;
                min-width: auto;
            }
        
            .form-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        
            .settings-card .card-header {
                padding: 25px 25px 20px 25px;
            }
        
            .settings-card .card-body {
                padding: 25px;
                margin-top: 5px;
            }
        
            .card-subtitle {
                padding-left: 25px !important;
                margin-left: -25px !important;
                margin-right: -25px !important;
                padding-right: 25px !important;
            }
        }
        </style>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            {include file="admin/common/sidebar"}

            <!-- 主要内容 -->
            <main class="main-content">
                <!-- 内容头部 -->
                <div class="content-header">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-cog"></i> 系统设置
                    </h1>
                </div>

                <!-- 页面内容区域 -->
                <div class="content-body">
                    <div class="settings-container">
                        <!-- 引用统一消息组件 -->
                        {include file="admin/common/message"}

                        <!-- 设置选项卡 -->
                        <div class="settings-tabs-container">
                            <div class="tabs-header">
                                <div class="tab-nav">
                                    <button class="tab-btn active" data-tab="basic">
                                        <i class="fas fa-info-circle"></i>
                                        <span>基本信息</span>
                                    </button>
                                    <button class="tab-btn" data-tab="contact">
                                        <i class="fas fa-address-book"></i>
                                        <span>联系信息</span>
                                    </button>
                                    <button class="tab-btn" data-tab="seo">
                                        <i class="fas fa-search"></i>
                                        <span>SEO设置</span>
                                    </button>
                                    <button class="tab-btn" data-tab="system">
                                        <i class="fas fa-cogs"></i>
                                        <span>系统配置</span>
                                    </button>
                                </div>
                            </div>

                            <div class="tabs-content">
                                <!-- 基本信息设置 -->
                                <div class="tab-content active" id="basic">
                                    <div class="settings-card">
                                        <div class="card-header">
                                            <div class="card-title">
                                                <i class="fas fa-info-circle"></i>
                                                <span>基本信息设置</span>
                                            </div>
                                            <p class="card-subtitle">配置网站的基本信息和标识</p>
                                        </div>
                                        <div class="card-body">
                                            <form method="POST" class="settings-form">
                                                <div class="form-grid">
                                                    <div class="form-group">
                                                        <label for="site_name" class="form-label">
                                                            <i class="fas fa-globe"></i>
                                                            <span>网站名称</span>
                                                        </label>
                                                        <input type="text" class="form-input" id="site_name" name="site_name"
                                                               value="{$currentSettings.site_name|default='三只鱼网络'}"
                                                               placeholder="请输入网站名称" required>
                                                    </div>

                                                    <div class="form-group">
                                                        <label for="site_url" class="form-label">
                                                            <i class="fas fa-link"></i>
                                                            <span>网站URL</span>
                                                        </label>
                                                        <input type="url" class="form-input" id="site_url" name="site_url"
                                                               value="{$currentSettings.site_url|default='http://localhost'}"
                                                               placeholder="https://example.com" required>
                                                    </div>

                                                    <div class="form-group full-width">
                                                        <label for="site_title" class="form-label">
                                                            <i class="fas fa-heading"></i>
                                                            <span>网站标题</span>
                                                        </label>
                                                        <input type="text" class="form-input" id="site_title" name="site_title"
                                                               value="{$currentSettings.site_title|default='三只鱼网络 - 专业网络服务提供商'}"
                                                               placeholder="请输入网站标题" required>
                                                    </div>

                                                    <div class="form-group full-width">
                                                        <label for="site_description" class="form-label">
                                                            <i class="fas fa-align-left"></i>
                                                            <span>网站描述</span>
                                                        </label>
                                                        <textarea class="form-textarea" id="site_description" name="site_description"
                                                                  rows="4" placeholder="请输入网站描述" required>{$currentSettings.site_description|default='专业的网络服务提供商，提供网站建设、系统开发、网络营销等服务'}</textarea>
                                                    </div>

                                                    <div class="form-group full-width">
                                                        <label for="site_keywords" class="form-label">
                                                            <i class="fas fa-tags"></i>
                                                            <span>网站关键词</span>
                                                        </label>
                                                        <input type="text" class="form-input" id="site_keywords" name="site_keywords"
                                                               value="{$currentSettings.site_keywords|default='网站建设,系统开发,网络营销'}"
                                                               placeholder="用英文逗号分隔多个关键词">
                                                        <div class="form-help">用于SEO优化的关键词，多个关键词用英文逗号分隔</div>
                                                    </div>
                                                </div>

                                                <div class="form-actions">
                                                    <button type="submit" name="basic_settings" class="btn-primary">
                                                        <i class="fas fa-save"></i>
                                                        <span>保存基本信息</span>
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- 联系信息设置 -->
                                <div class="tab-content" id="contact">
                                    <div class="settings-card">
                                        <div class="card-header">
                                            <div class="card-title">
                                                <i class="fas fa-address-book"></i>
                                                <span>联系信息设置</span>
                                            </div>
                                            <p class="card-subtitle">配置公司联系方式和地址信息</p>
                                        </div>
                                        <div class="card-body">
                                            <form method="POST" class="settings-form">
                                                <div class="form-grid">
                                                    <div class="form-group">
                                                        <label for="company_name" class="form-label">
                                                            <i class="fas fa-building"></i>
                                                            <span>公司名称</span>
                                                        </label>
                                                        <input type="text" class="form-input" id="company_name" name="company_name"
                                                               value="{$currentSettings.company_name|default='三只鱼网络科技有限公司'}"
                                                               placeholder="请输入公司名称" required>
                                                    </div>

                                                    <div class="form-group">
                                                        <label for="company_phone" class="form-label">
                                                            <i class="fas fa-phone"></i>
                                                            <span>联系电话</span>
                                                        </label>
                                                        <input type="text" class="form-input" id="company_phone" name="company_phone"
                                                               value="{$currentSettings.company_phone|default='************'}"
                                                               placeholder="请输入联系电话" required>
                                                    </div>

                                                    <div class="form-group full-width">
                                                        <label for="company_address" class="form-label">
                                                            <i class="fas fa-map-marker-alt"></i>
                                                            <span>公司地址</span>
                                                        </label>
                                                        <input type="text" class="form-input" id="company_address" name="company_address"
                                                               value="{$currentSettings.company_address|default='北京市朝阳区科技园区'}"
                                                               placeholder="请输入公司地址" required>
                                                    </div>

                                                    <div class="form-group">
                                                        <label for="company_email" class="form-label">
                                                            <i class="fas fa-envelope"></i>
                                                            <span>联系邮箱</span>
                                                        </label>
                                                        <input type="email" class="form-input" id="company_email" name="company_email"
                                                               value="{$currentSettings.company_email|default='<EMAIL>'}"
                                                               placeholder="请输入联系邮箱" required>
                                                    </div>

                                                    <div class="form-group">
                                                        <label for="company_qq" class="form-label">
                                                            <i class="fab fa-qq"></i>
                                                            <span>QQ号码</span>
                                                        </label>
                                                        <input type="text" class="form-input" id="company_qq" name="company_qq"
                                                               value="{$currentSettings.company_qq|default=''}"
                                                               placeholder="请输入QQ号码">
                                                    </div>

                                                    <div class="form-group">
                                                        <label for="company_wechat" class="form-label">
                                                            <i class="fab fa-weixin"></i>
                                                            <span>微信号</span>
                                                        </label>
                                                        <input type="text" class="form-input" id="company_wechat" name="company_wechat"
                                                               value="{$currentSettings.company_wechat|default=''}"
                                                               placeholder="请输入微信号">
                                                    </div>
                                                </div>

                                                <div class="form-actions">
                                                    <button type="submit" name="contact_settings" class="btn-primary">
                                                        <i class="fas fa-save"></i>
                                                        <span>保存联系信息</span>
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- SEO设置 -->
                                <div class="tab-content" id="seo">
                                    <div class="settings-card">
                                        <div class="card-header">
                                            <div class="card-title">
                                                <i class="fas fa-search"></i>
                                                <span>SEO设置</span>
                                            </div>
                                            <p class="card-subtitle">配置搜索引擎优化和网站统计</p>
                                        </div>
                                        <div class="card-body">
                                            <form method="POST" class="settings-form">
                                                <div class="form-grid">
                                                    <div class="form-group full-width">
                                                        <label for="meta_keywords" class="form-label">
                                                            <i class="fas fa-tags"></i>
                                                            <span>META关键词</span>
                                                        </label>
                                                        <input type="text" class="form-input" id="meta_keywords" name="meta_keywords"
                                                               value="{$currentSettings.meta_keywords|default=''}"
                                                               placeholder="用英文逗号分隔多个关键词">
                                                        <div class="form-help">用于搜索引擎优化的关键词，多个关键词用英文逗号分隔</div>
                                                    </div>

                                                    <div class="form-group full-width">
                                                        <label for="meta_description" class="form-label">
                                                            <i class="fas fa-align-left"></i>
                                                            <span>META描述</span>
                                                        </label>
                                                        <textarea class="form-textarea" id="meta_description" name="meta_description" rows="3"
                                                                  placeholder="网站的简短描述，用于搜索引擎显示">{$currentSettings.meta_description|default=''}</textarea>
                                                        <div class="form-help">建议长度在150-160个字符之间，用于搜索结果页面显示</div>
                                                    </div>

                                                    <div class="form-group full-width">
                                                        <label for="analytics_code" class="form-label">
                                                            <i class="fas fa-chart-line"></i>
                                                            <span>统计代码</span>
                                                        </label>
                                                        <textarea class="form-textarea" id="analytics_code" name="analytics_code" rows="6"
                                                                  placeholder="Google Analytics 或百度统计等统计代码">{$currentSettings.analytics_code|default=''}</textarea>
                                                        <div class="form-help">将会插入到页面的 &lt;head&gt; 标签中，支持Google Analytics、百度统计等</div>
                                                    </div>

                                                    <div class="form-group">
                                                        <label for="baidu_verify" class="form-label">
                                                            <i class="fas fa-shield-alt"></i>
                                                            <span>百度验证码</span>
                                                        </label>
                                                        <input type="text" class="form-input" id="baidu_verify" name="baidu_verify"
                                                               value="{$currentSettings.baidu_verify|default=''}"
                                                               placeholder="百度站长工具验证码">
                                                        <div class="form-help">用于百度站长工具验证网站所有权</div>
                                                    </div>
                                                </div>

                                                <div class="form-actions">
                                                    <button type="submit" name="seo_settings" class="btn-primary">
                                                        <i class="fas fa-save"></i>
                                                        <span>保存SEO设置</span>
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- 系统配置 -->
                                <div class="tab-content" id="system">
                                    <div class="settings-card">
                                        <div class="card-header">
                                            <div class="card-title">
                                                <i class="fas fa-cogs"></i>
                                                <span>系统配置</span>
                                            </div>
                                            <p class="card-subtitle">配置系统运行参数和性能选项</p>
                                        </div>
                                        <div class="card-body">
                                            <form method="POST" class="settings-form">
                                                <div class="form-grid">
                                                    <div class="form-group">
                                                        <label for="max_file_size" class="form-label">
                                                            <i class="fas fa-upload"></i>
                                                            <span>最大文件上传大小 (MB)</span>
                                                        </label>
                                                        <input type="number" class="form-input" id="max_file_size" name="max_file_size"
                                                               value="{$currentSettings.max_file_size|default='5'}"
                                                               min="1" max="100" placeholder="5" required>
                                                        <div class="form-help">设置单个文件的最大上传大小，范围1-100MB</div>
                                                    </div>

                                                    <div class="form-group">
                                                        <label for="items_per_page" class="form-label">
                                                            <i class="fas fa-list"></i>
                                                            <span>每页显示条数</span>
                                                        </label>
                                                        <input type="number" class="form-input" id="items_per_page" name="items_per_page"
                                                               value="{$currentSettings.items_per_page|default='10'}"
                                                               min="5" max="100" placeholder="10" required>
                                                        <div class="form-help">设置列表页面每页显示的记录数量</div>
                                                    </div>

                                                    <div class="form-group full-width">
                                                        <label class="form-label">
                                                            <i class="fas fa-rocket"></i>
                                                            <span>缓存设置</span>
                                                        </label>
                                                        <label class="switch">
                                                            <input type="checkbox" id="cache_enabled" name="cache_enabled"
                                                                   {if condition="($currentSettings.cache_enabled ?? '1') == '1'"}checked{/if}>
                                                            <span class="slider"></span>
                                                        </label>
                                                        <div class="form-help">启用后可提高网站访问速度，减少数据库查询</div>
                                                    </div>

                                                    <div class="form-group">
                                                        <label for="cache_time" class="form-label">
                                                            <i class="fas fa-clock"></i>
                                                            <span>缓存时间 (秒)</span>
                                                        </label>
                                                        <input type="number" class="form-input" id="cache_time" name="cache_time"
                                                               value="{$currentSettings.cache_time|default='3600'}"
                                                               min="60" max="86400" placeholder="3600" required>
                                                        <div class="form-help">缓存数据的有效时间，默认3600秒(1小时)</div>
                                                    </div>
                                                </div>

                                                <div class="form-actions">
                                                    <button type="submit" name="system_settings" class="btn-primary">
                                                        <i class="fas fa-save"></i>
                                                        <span>保存系统配置</span>
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- JavaScript -->
    {include file="admin/common/js"}

    <script>
        $(document).ready(function() {
            // 显示从后端传递的消息
            {if condition="$message"}
            showMessage('{$message}', '{$messageType}');
            {/if}

            // 选项卡切换
            $('.tab-btn').on('click', function() {
                const tabId = $(this).data('tab');

                // 移除所有活动状态
                $('.tab-btn').removeClass('active');
                $('.tab-content').removeClass('active');

                // 添加当前活动状态
                $(this).addClass('active');
                $('#' + tabId).addClass('active');
            });

            // 侧边栏切换（移动端）
            $('#sidebarToggle').on('click', function() {
                $('.sidebar').toggleClass('show');
                $('.sidebar-overlay').toggleClass('show');
            });

            // 点击遮罩层隐藏侧边栏
            $('.sidebar-overlay').on('click', function() {
                $('.sidebar').removeClass('show');
                $('.sidebar-overlay').removeClass('show');
            });


        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - 三只鱼网络</title>
    {include file="admin/common/css"}
    <link rel="stylesheet" href="/assets/css/admin/index.css">
</head>
<body>
    <!-- 顶部导航 -->
    {include file="admin/common/header"}

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            {include file="admin/common/sidebar"}

            <!-- 主要内容 -->
            <main class="main-content">
                <!-- 内容头部 -->
                <div class="content-header">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-tachometer-alt"></i> 仪表盘
                    </h1>
                </div>

                <!-- 页面内容区域 -->
                <div class="content-body">
                    <!-- 统计卡片 -->
                    <div class="stats-grid">
                        <!-- 联系表单统计 -->
                        <div class="stat-card">
                            <div class="stat-card-content">
                                <div class="stat-icon stat-icon-primary">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-label">联系表单</div>
                                    <div class="stat-value">{$stats['contact_forms']|default=0}</div>
                                    <div class="stat-detail">
                                        {if condition="isset($stats['new_contact_forms']) && $stats['new_contact_forms'] > 0"}
                                            <span class="stat-badge stat-badge-new">{$stats['new_contact_forms']} 条新消息</span>
                                        {else/}
                                            <span class="stat-badge stat-badge-normal">全部已处理</span>
                                        {/if}
                                    </div>
                                </div>
                            </div>
                            <div class="stat-card-footer">
                                <a href="/admin/contacts" class="stat-link">
                                    <span>查看详情</span>
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>

                        <!-- 产品统计 -->
                        <div class="stat-card">
                            <div class="stat-card-content">
                                <div class="stat-icon stat-icon-success">
                                    <i class="fas fa-cube"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-label">产品数量</div>
                                    <div class="stat-value">{$stats['products']|default=0}</div>
                                    <div class="stat-detail">
                                        <span class="stat-badge stat-badge-normal">已发布产品</span>
                                    </div>
                                </div>
                            </div>
                            <div class="stat-card-footer">
                                <a href="/admin/products" class="stat-link">
                                    <span>管理产品</span>
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>

                        <!-- 新闻统计 -->
                        <div class="stat-card">
                            <div class="stat-card-content">
                                <div class="stat-icon stat-icon-info">
                                    <i class="fas fa-newspaper"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-label">新闻文章</div>
                                    <div class="stat-value">{$stats['news']|default=0}</div>
                                    <div class="stat-detail">
                                        <span class="stat-badge stat-badge-normal">已发布文章</span>
                                    </div>
                                </div>
                            </div>
                            <div class="stat-card-footer">
                                <a href="/admin/news" class="stat-link">
                                    <span>管理新闻</span>
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>

                        <!-- 客户案例统计 -->
                        <div class="stat-card">
                            <div class="stat-card-content">
                                <div class="stat-icon stat-icon-warning">
                                    <i class="fas fa-briefcase"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-label">客户案例</div>
                                    <div class="stat-value">{$stats.cases|default=0}</div>
                                    <div class="stat-detail">
                                        <span class="stat-badge stat-badge-normal">成功案例</span>
                                    </div>
                                </div>
                            </div>
                            <div class="stat-card-footer">
                                <a href="/admin/cases" class="stat-link">
                                    <span>管理案例</span>
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 最新联系表单 -->
                    <div class="contacts-section">
                        <div class="section-header">
                            <div class="section-title-area">
                                <div class="section-icon">
                                    <i class="fas fa-envelope-open"></i>
                                </div>
                                <div class="section-title-text">
                                    <h3 class="section-title">最新联系表单</h3>
                                    <p class="section-subtitle">最近收到的客户咨询信息</p>
                                </div>
                            </div>
                            <div class="section-actions">
                                <a href="/admin/contacts" class="btn-view-all">
                                    <span>查看全部</span>
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>

                        <div class="contacts-content">
                            {if condition="!empty($latestContacts)"}
                                <div class="contacts-list">
                                    {foreach $latestContacts as $contact}
                                        <div class="contact-item">
                                            <div class="contact-avatar">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div class="contact-info">
                                                <div class="contact-header">
                                                    <h4 class="contact-name">{$contact.name}</h4>
                                                    <div class="contact-status-actions">
                                                        <div class="contact-status">
                                                            {switch $contact.status}
                                                                {case value="new"}
                                                                    <span class="status-badge status-new">
                                                                        <i class="fas fa-circle"></i>
                                                                        新消息
                                                                    </span>
                                                                {/case}
                                                                {case value="read"}
                                                                    <span class="status-badge status-read">
                                                                        <i class="fas fa-eye"></i>
                                                                        已读
                                                                    </span>
                                                                {/case}
                                                                {case value="replied"}
                                                                    <span class="status-badge status-replied">
                                                                        <i class="fas fa-reply"></i>
                                                                        已回复
                                                                    </span>
                                                                {/case}
                                                                {case value="closed"}
                                                                    <span class="status-badge status-closed">
                                                                        <i class="fas fa-check-circle"></i>
                                                                        已关闭
                                                                    </span>
                                                                {/case}
                                                                {default /}
                                                                    <span class="status-badge status-unknown">
                                                                        <i class="fas fa-question-circle"></i>
                                                                        未知
                                                                    </span>
                                                            {/switch}
                                                        </div>
                                                        <div class="contact-actions">
                                                            <a href="/admin/contacts?action=view&id={$contact.id}" class="btn-action btn-view">
                                                                <i class="fas fa-eye"></i>
                                                                <span>查看</span>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="contact-details">
                                                    <div class="contact-email">
                                                        <i class="fas fa-envelope"></i>
                                                        <span>{$contact.email}</span>
                                                    </div>
                                                    <div class="contact-subject">
                                                        <i class="fas fa-tag"></i>
                                                        <span>{$contact.subject}</span>
                                                    </div>
                                                    <div class="contact-time">
                                                        <i class="fas fa-clock"></i>
                                                        <span>{$contact.created_at|date='Y-m-d H:i'}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    {/foreach}
                                </div>
                            {else/}
                                <div class="empty-state">
                                    <div class="empty-icon">
                                        <i class="fas fa-inbox"></i>
                                    </div>
                                    <h3 class="empty-title">暂无联系表单</h3>
                                    <p class="empty-subtitle">还没有收到任何客户咨询信息</p>
                                </div>
                            {/if}
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/assets/js/jquery.min.js"></script>
    <script src="/assets/js/bootstrap.bundle.min.js"></script>
    <script src="/assets/js/admin.js"></script>

    <script>
        $(document).ready(function() {
            // 侧边栏切换（移动端）
            $('#sidebarToggle').on('click', function() {
                $('.sidebar').toggleClass('show');
                $('.sidebar-overlay').toggleClass('show');
            });

            // 点击遮罩层隐藏侧边栏
            $('.sidebar-overlay').on('click', function() {
                $('.sidebar').removeClass('show');
                $('.sidebar-overlay').removeClass('show');
            });

            // 数字动画
            $('.stat-value').each(function() {
                const $this = $(this);
                const finalNumber = parseInt($this.text()) || 0;

                if (finalNumber > 0) {
                    $this.text('0');
                    $({ counter: 0 }).animate({ counter: finalNumber }, {
                        duration: 2000,
                        easing: 'swing',
                        step: function() {
                            $this.text(Math.ceil(this.counter));
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>

<?php /*a:4:{s:53:"D:\EServer\core\www\san.com\app\view\index\index.html";i:1749495872;s:55:"D:\EServer\core\www\san.com\app\view\common\header.html";i:1749772617;s:55:"D:\EServer\core\www\san.com\app\view\common\styles.html";i:1748797665;s:55:"D:\EServer\core\www\san.com\app\view\common\footer.html";i:1749617057;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <title><?php echo htmlentities((string) (isset($pageTitle) && ($pageTitle !== '')?$pageTitle:'首页')); ?> - <?php echo htmlentities((string) (isset($siteConfig['site_name']) && ($siteConfig['site_name'] !== '')?$siteConfig['site_name']:'三只鱼网络')); ?></title>
    <meta name="description" content="<?php echo htmlentities((string) ((isset($pageDescription) && ($pageDescription !== '')?$pageDescription:$siteConfig['site_description']) ?: '专注于为企业提供专业、可靠、高效的数字化转型解决方案')); ?>">
    <meta name="keywords" content="<?php echo htmlentities((string) ((isset($pageKeywords) && ($pageKeywords !== '')?$pageKeywords:$siteConfig['site_keywords']) ?: '数字化转型,企业解决方案,技术服务,创新科技,专业团队')); ?>">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?php echo htmlentities((string) ((isset($pageTitle) && ($pageTitle !== '')?$pageTitle:$siteConfig['site_name']) ?: '三只鱼网络')); ?>">
    <meta property="og:description" content="<?php echo htmlentities((string) ((isset($pageDescription) && ($pageDescription !== '')?$pageDescription:$siteConfig['site_description']) ?: '专注于为企业提供专业、可靠、高效的数字化转型解决方案')); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo htmlentities((string) app('request')->domain()); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo asset('assets/images/favicon.ico'); ?>">
    
    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo asset('assets/css/bootstrap.min.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset('assets/css/all.min.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset('assets/css/animate.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset('assets/css/swiper-bundle.min.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset('assets/css/style.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset('assets/css/contact.css'); ?>">

    <!-- Lucide Icons - 本地版本 -->
    <script src="<?php echo asset('assets/js/lucide.js'); ?>"></script>
    
    <!-- 导航菜单层级修复 -->
    <link rel="stylesheet" href="<?php echo asset('assets/css/nav-fix.css'); ?>">

    <!-- 导航栏样式 -->
    <style>
    /* ===== 下拉菜单基础样式 ===== */
    .dropdown-menu {
        background: #ffffff;
        border: none;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        border-radius: 12px;
        padding: 12px 0;
    }

    .dropdown-item {
        padding: 10px 20px;
        font-size: 14px;
        transition: all 0.2s ease;
        border: none;
        background: transparent;
    }

    .dropdown-item:hover {
        background: rgba(0, 123, 255, 0.08);
        color: #007bff;
    }

    /* ===== 大型下拉菜单容器 ===== */
    .mega-dropdown {
        position: relative;
    }

    .mega-dropdown .dropdown-menu {
        display: none;
        position: fixed;
        top: 80px;
        left: 50%;
        transform: translateX(-50%);
        margin: 0;
        border-radius: 16px;
        background: white;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        border: none;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        pointer-events: none;
        width: 1200px;
        max-width: 90vw;
        max-height: 80vh;
        overflow-y: auto;
    }

    /* 下拉菜单显示状态 - 通过JavaScript控制 */
    .mega-dropdown.show .dropdown-menu {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: auto !important;
    }

    /* 确保菜单内容有正确的显示状态 */
    .dropdown-menu {
        display: none;
        opacity: 0;
        visibility: hidden;
        pointer-events: none;
    }

    /* 当菜单被JavaScript激活时的样式 */
    .dropdown-menu.show,
    .dropdown:hover .dropdown-menu {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: auto !important;
    }

    /* 增加菜单和触发器之间的连接区域 */
    .mega-dropdown::before {
        content: '';
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        height: 20px;
        background: transparent;
        z-index: 9999;
        pointer-events: auto;
    }

    /* ===== 简单可靠的三角形指示器 ===== */
    .mega-dropdown .dropdown-menu::before {
        content: '';
        position: absolute;
        top: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-bottom: 8px solid white;
        z-index: 1001;
    }

    /* 三角形阴影 */
    .mega-dropdown .dropdown-menu::after {
        content: '';
        position: absolute;
        top: -9px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 9px solid transparent;
        border-right: 9px solid transparent;
        border-bottom: 9px solid rgba(0, 0, 0, 0.1);
        z-index: 1000;
    }

    /* ===== 下拉箭头指示器 ===== */
    .dropdown-toggle::after {
        display: inline-block;
        margin-left: 6px;
        vertical-align: middle;
        content: "▼";
        font-size: 10px;
        transition: transform 0.3s ease;
        line-height: 1;
    }

    .dropdown-toggle[aria-expanded="true"]::after,
    .mega-dropdown.show .dropdown-toggle::after {
        transform: rotate(180deg);
    }

    /* 解决方案菜单特殊样式 */
    .solutions-menu {
        padding: 0;
    }

    .solutions-menu .mega-menu-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 25px 30px;
        text-align: center;
        color: white;
        border-radius: 16px 16px 0 0;
    }

    .solutions-menu .mega-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 8px;
        color: white;
    }

    .solutions-menu .mega-subtitle {
        font-size: 0.9rem;
        opacity: 0.9;
        margin: 0;
        color: white;
    }

    /* 解决方案网格布局 */
    .solutions-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
        padding: 30px;
        background: white;
    }

    .solution-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 20px 15px;
        border-radius: 12px;
        transition: all 0.3s ease;
        cursor: pointer;
        text-decoration: none;
        color: inherit;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        min-height: 140px;
        justify-content: flex-start;
    }

    .solution-item:hover {
        background: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.12);
        text-decoration: none;
        color: inherit;
        border-color: rgba(102, 126, 234, 0.3);
    }

    .solution-item .solution-icon {
        width: 64px;
        height: 64px;
        background: transparent;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 12px;
        transition: all 0.3s ease;
        flex-shrink: 0;
    }

    .solution-item .solution-icon img {
        width: 48px;
        height: 48px;
        object-fit: contain;
    }

    .solution-item:hover .solution-icon {
        transform: scale(1.1);
    }

    .solution-item .solution-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 100%;
    }

    .solution-item .solution-content h4 {
        font-size: 1rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        line-height: 1.3;
    }

    .solution-item .solution-content p {
        font-size: 0.8rem;
        color: #666;
        line-height: 1.4;
        margin: 0;
    }

    /* 产品网格布局修正 */
    .products-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 25px;
        padding: 30px;
        background: white;
        min-width: 800px;
    }

    .products-column {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .product-item {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        border-radius: 10px;
        transition: all 0.3s ease;
        cursor: pointer;
        background: white;
        border: 1px solid rgba(102, 126, 234, 0.1);
        text-decoration: none;
        color: inherit;
        margin-bottom: 8px;
    }

    .product-item:hover {
        background: rgba(102, 126, 234, 0.12);
        border-color: rgba(102, 126, 234, 0.25);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
        text-decoration: none;
        color: inherit;
    }

    .product-icon {
        width: 35px;
        height: 35px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        flex-shrink: 0;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .product-icon.purple {
        background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
        color: white;
    }

    .product-icon i {
        font-size: 18px;
        color: white;
        line-height: 1;
        width: auto;
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    /* Lucide图标样式 */
    .product-icon svg {
        width: 18px;
        height: 18px;
        color: white;
        stroke: white;
        stroke-width: 2;
    }

    .product-item:hover .product-icon {
        transform: scale(1.1);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.35);
    }

    .product-item:hover .product-icon i {
        transform: translate(-50%, -50%) scale(1);
    }

    .product-name {
        font-size: 0.9rem;
        font-weight: 500;
        color: #333;
        line-height: 1.4;
        transition: color 0.3s ease;
    }

    .product-item:hover .product-name {
        color: #667eea;
    }

    .product-item-placeholder {
        padding: 12px 15px;
        color: #999;
        font-size: 0.9rem;
        text-align: center;
        font-style: italic;
    }

    /* 菜单底部 */
    .mega-menu-footer {
        text-align: center;
        padding-top: 25px;
        border-top: 1px solid rgba(0, 0, 0, 0.08);
        margin-top: 20px;
    }

    .mega-menu-footer .btn {
        padding: 12px 30px;
        font-size: 0.95rem;
        font-weight: 600;
        border-radius: 25px;
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        color: white;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.25);
    }

    .mega-menu-footer .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(0, 123, 255, 0.35);
        background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
        text-decoration: none;
        color: white;
    }

    /* ===== 产品介绍菜单样式 ===== */
    .products-menu {
        padding: 0;
        z-index: 10000 !important; /* 比解决方案菜单更高的z-index */
    }

    .products-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 30px;
        padding: 30px;
        background: white;
        min-width: 800px;
    }

    .products-column {
        display: flex;
        flex-direction: column;
    }

    .mega-menu-category {
        font-size: 1.1rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #667eea;
        text-align: center;
    }

    .product-item {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        border-radius: 8px;
        transition: all 0.3s ease;
        text-decoration: none;
        color: #333;
        margin-bottom: 8px;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
    }

    .product-item:hover {
        background: white;
        transform: translateX(5px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.15);
        text-decoration: none;
        color: #667eea;
        border-color: #667eea;
    }

    .product-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        transition: all 0.3s ease;
        flex-shrink: 0;
        position: relative;
        overflow: hidden;
    }

    .product-icon.purple {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .product-icon i {
        font-size: 18px;
        line-height: 1;
        width: auto;
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .product-item:hover .product-icon {
        transform: scale(1.1);
    }

    .product-item:hover .product-icon i {
        transform: translate(-50%, -50%) scale(1);
    }

    .product-name {
        font-size: 0.95rem;
        font-weight: 600;
        color: inherit;
    }

    .product-item-placeholder {
        padding: 12px 15px;
        color: #999;
        font-size: 0.9rem;
        text-align: center;
        font-style: italic;
    }

    /* ===== 导航栏基础样式增强 ===== */
    .header {
        position: relative;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    }

    .navbar {
        position: relative;
        z-index: 1001;
    }

    /* 确保所有下拉菜单都有足够高的z-index */
    .mega-dropdown .dropdown-menu {
        z-index: 10001 !important;
    }

    .solutions-menu {
        z-index: 10002 !important;
    }

    .products-menu {
        z-index: 10003 !important;
    }

    /* 确保三角形指示器也有正确的z-index */
    .mega-dropdown .dropdown-menu::before {
        z-index: 10004;
    }

    .mega-dropdown .dropdown-menu::after {
        z-index: 10003;
    }

    /* ===== 三级菜单样式 ===== */
    .dropdown-submenu {
        position: relative;
    }

    .dropdown-submenu .dropdown-menu {
        position: absolute;
        top: 0;
        left: 100%;
        margin-top: -1px;
        display: none;
        min-width: 200px;
    }

    .dropdown-submenu:hover .dropdown-menu {
        display: block;
    }

    .dropdown-submenu .dropdown-toggle::after {
        content: "▶";
        float: right;
        margin-top: 2px;
        border: none;
    }

    /* 响应式处理 */
    @media (max-width: 991px) {
        .dropdown-submenu .dropdown-menu {
            position: static;
            float: none;
            width: auto;
            margin-top: 0;
            background-color: #f8f9fa;
            border: none;
            box-shadow: none;
            padding-left: 20px;
        }
    }
    </style>
</head>
<body class="<?php echo htmlentities((string) (isset($bodyClass) && ($bodyClass !== '')?$bodyClass:'home-page')); ?>">
    <!-- 导航栏 -->
    <header class="header">
        <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="/">
                <img src="<?php echo asset('assets/images/logo.png'); ?>" alt="<?php echo htmlentities((string) (isset($siteConfig['site_name']) && ($siteConfig['site_name'] !== '')?$siteConfig['site_name']:'三只鱼网络')); ?>" height="40">
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto" id="main-nav">
                    <!-- 动态菜单将在这里加载 -->
                </ul>


                <!-- 右侧按钮组 -->
                <div class="navbar-actions ms-3">
                    <a href="#" class="search-btn me-2"><i data-lucide="search"></i></a>
                    <a href="/login" class="btn btn-outline-light btn-sm me-2">登录</a>
                    <a href="/register" class="btn btn-primary btn-sm">注册</a>
                </div>
            </div>
        </div>
    </nav>
</header>

    <!-- 主要内容 -->
    <main>

<script>
// 菜单加载 - 添加调试信息

// URL格式化函数
function formatUrl(url) {
    if (!url || url === '#') {
        return '#';
    }

    // 转换为字符串并清理
    url = String(url).trim();

    // 如果是完整的URL（包含协议），直接返回
    if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
    }

    // 如果已经是正确的相对路径（以/开头），直接返回
    if (url.startsWith('/')) {
        return url;
    }

    // 如果是相对路径，添加/前缀
    return '/' + url;
}

document.addEventListener('DOMContentLoaded', function() {
    loadMenus();
});

function loadMenus() {
    fetch('/api/sys-menus')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {

            if (data && data.success && data.data) {
                renderMenus(data.data);
            } else {
            }
        })
        .catch(error => {
            console.error('菜单加载失败:', error);
            console.error('错误详情:', error.message);
        });
}

function renderMenus(menus) {
    const nav = document.getElementById('main-nav');
    if (!nav) {
        console.error('找不到导航容器 #main-nav');
        return;
    }

    // 清空现有菜单（保留首页）
    const existing = nav.querySelectorAll('li:not(:first-child)');
    existing.forEach(item => item.remove());

    // 添加新菜单
    let hasMorePages = false;
    let hasContact = false;
    
    menus.forEach((menu, index) => {
        const li = createMenuItem(menu);
        nav.appendChild(li);
        
        // 检查是否已经有"更多页面"和"联系我们"菜单
        if (menu.name === '更多页面') {
            hasMorePages = true;
            // 为动态的"更多页面"菜单添加id，以便DIY页面加载
            const dropdownMenu = li.querySelector('.dropdown-menu');
            if (dropdownMenu && !dropdownMenu.id) {
                dropdownMenu.id = 'diy-pages-menu';
            }
            // 检查是否有DIY页面内容，没有的话隐藏这个菜单项
            checkDiyPagesAndToggleMenu(li);
        }
        if (menu.name === '联系我们') {
            hasContact = true;
        }
    });

    // 只有当动态菜单中没有"更多页面"时才检查是否需要添加固定的"更多页面"菜单
    if (!hasMorePages) {
        // 先检查是否有DIY页面，有的话才添加"更多页面"菜单
        checkAndAddMorePagesMenu(nav);
    }

    // 只有当动态菜单中没有"联系我们"时才添加固定的"联系我们"菜单
    if (!hasContact) {
        const contact = document.createElement('li');
        contact.className = 'nav-item';
        contact.innerHTML = '<a class="nav-link" href="/contact">联系我们</a>';
        nav.appendChild(contact);
    }


    // 初始化下拉菜单事件
    initDropdownEvents();

    // 确保Lucide图标正确渲染
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
}

function createMenuItem(menu) {
    const li = document.createElement('li');
    const url = formatUrl(menu.url || menu.link_value || '#');

    if (menu.children && menu.children.length > 0) {
        // 检查是否是特殊的mega-dropdown菜单
        if (menu.name === '解决方案') {
            return createSolutionsMenuItem(menu, url);
        } else if (menu.name === '产品介绍') {
            return createProductsMenuItem(menu, url);
        } else {
            // 普通下拉菜单
            li.className = 'nav-item dropdown';

            // 创建主链接
            const mainLink = document.createElement('a');
            mainLink.className = 'nav-link dropdown-toggle';
            mainLink.href = url;
            mainLink.setAttribute('role', 'button');
            mainLink.setAttribute('aria-expanded', 'false');
            mainLink.textContent = menu.name;

            // 创建下拉菜单
            const dropdownMenu = document.createElement('ul');
            dropdownMenu.className = 'dropdown-menu';
            dropdownMenu.innerHTML = renderSubMenus(menu.children);

            li.appendChild(mainLink);
            li.appendChild(dropdownMenu);

            // 添加悬停事件
            li.addEventListener('mouseenter', function() {
                const dropdown = li.querySelector('.dropdown-menu');
                if (dropdown) {
                    dropdown.style.display = 'block';
                    li.classList.add('show');
                }
            });

            li.addEventListener('mouseleave', function() {
                const dropdown = li.querySelector('.dropdown-menu');
                if (dropdown) {
                    dropdown.style.display = 'none';
                    li.classList.remove('show');
                }
            });

        }
    } else {
        // 普通菜单项
        li.className = 'nav-item';

        const link = document.createElement('a');
        link.className = 'nav-link';
        link.href = url;
        link.textContent = menu.name;
        li.appendChild(link);
    }

    return li;
}

// 创建解决方案菜单项
function createSolutionsMenuItem(menu, url) {
    const li = document.createElement('li');
    li.className = 'nav-item mega-dropdown';

    const mainLink = document.createElement('a');
    mainLink.className = 'nav-link dropdown-toggle';
    mainLink.href = url;
    mainLink.id = 'solutionsDropdown';
    mainLink.setAttribute('role', 'button');
    mainLink.setAttribute('aria-expanded', 'false');
    mainLink.textContent = menu.name;

    const dropdownMenu = document.createElement('div');
    dropdownMenu.className = 'dropdown-menu solutions-menu';
    
    // 动态生成解决方案网格
    let solutionsHtml = '<div class="solutions-grid">';
    
    if (menu.children && menu.children.length > 0) {
        // 使用真实的子菜单数据
        menu.children.forEach(solution => {
            const solutionUrl = formatUrl(solution.url || solution.link_value || '#');
            const iconPath = solution.icon || '/assets/images/nav/default.png';
            const description = solution.description || solution.remark || '专业的解决方案';
            
            solutionsHtml += `
                <a href="${solutionUrl}" class="solution-item">
                    <div class="solution-icon">
                        <img src="${iconPath}" alt="${solution.name}">
                    </div>
                    <div class="solution-content">
                        <h4>${solution.name}</h4>
                        <p>${description}</p>
                    </div>
                </a>
            `;
        });
    }
    
    solutionsHtml += '</div>';
    dropdownMenu.innerHTML = solutionsHtml;

    li.appendChild(mainLink);
    li.appendChild(dropdownMenu);

    // 添加mega-dropdown悬停事件
    addMegaDropdownEvents(li);

    return li;
}

// 创建产品介绍菜单项
function createProductsMenuItem(menu, url) {
    const li = document.createElement('li');
    li.className = 'nav-item mega-dropdown';

    const mainLink = document.createElement('a');
    mainLink.className = 'nav-link dropdown-toggle';
    mainLink.href = url;
    mainLink.id = 'productsDropdown';
    mainLink.setAttribute('role', 'button');
    mainLink.setAttribute('aria-expanded', 'false');
    mainLink.textContent = menu.name;

    const dropdownMenu = document.createElement('div');
    dropdownMenu.className = 'dropdown-menu products-menu';
    
    // 动态生成产品网格
    let productsHtml = '<div class="products-grid">';
    
    if (menu.children && menu.children.length > 0) {
        // 使用真实的子菜单数据，每个子菜单作为一个分类
        menu.children.forEach(category => {
            productsHtml += '<div class="products-column">';
            productsHtml += `<h6 class="mega-menu-category">${category.name}</h6>`;
            
            // 如果分类有子项目（产品），显示产品列表
            if (category.children && category.children.length > 0) {
                category.children.forEach(product => {
                    const productUrl = formatUrl(product.url || product.link_value || '#');
                    const productIcon = product.icon || 'fas fa-box';
                    
                    productsHtml += `
                        <a href="${productUrl}" class="product-item">
                            <div class="product-icon purple">
                                <i class="${productIcon}"></i>
                            </div>
                            <span class="product-name">${product.name}</span>
                        </a>
                    `;
                });
            } else {
                // 如果分类没有子产品，将分类本身作为产品显示
                const categoryUrl = formatUrl(category.url || category.link_value || '#');
                const categoryIcon = category.icon || 'fas fa-box';
                
                productsHtml += `
                    <a href="${categoryUrl}" class="product-item">
                        <div class="product-icon purple">
                            <i class="${categoryIcon}"></i>
                        </div>
                        <span class="product-name">${category.name}</span>
                    </a>
                `;
            }
            
            productsHtml += '</div>';
        });
    }
    
    productsHtml += '</div>';
    dropdownMenu.innerHTML = productsHtml;

    li.appendChild(mainLink);
    li.appendChild(dropdownMenu);

    // 添加mega-dropdown悬停事件
    addMegaDropdownEvents(li);

    return li;
}

// 添加mega-dropdown悬停事件
function addMegaDropdownEvents(dropdown) {
    const menu = dropdown.querySelector('.dropdown-menu');
    const toggle = dropdown.querySelector('.dropdown-toggle');
    let hoverTimer = null;
    let isMenuHovered = false;
    let isToggleHovered = false;
    
    // 显示菜单的函数
    function showMenu() {
        if (menu) {
            clearTimeout(hoverTimer);
            menu.style.display = 'block';
            menu.style.opacity = '1';
            menu.style.visibility = 'visible';
            menu.style.pointerEvents = 'auto';
            dropdown.classList.add('show');
            toggle.setAttribute('aria-expanded', 'true');
        }
    }
    
    // 隐藏菜单的函数
    function hideMenu() {
        if (menu) {
            hoverTimer = setTimeout(function() {
                if (!isMenuHovered && !isToggleHovered) {
                    menu.style.display = 'none';
                    menu.style.opacity = '0';
                    menu.style.visibility = 'hidden';
                    menu.style.pointerEvents = 'none';
                    dropdown.classList.remove('show');
                    toggle.setAttribute('aria-expanded', 'false');
                }
            }, 150);
        }
    }
    
    // 触发器悬停事件
    toggle.addEventListener('mouseenter', function() {
        isToggleHovered = true;
        showMenu();
    });
    
    toggle.addEventListener('mouseleave', function() {
        isToggleHovered = false;
        hideMenu();
    });
    
    // 菜单悬停事件
    if (menu) {
        menu.addEventListener('mouseenter', function() {
            isMenuHovered = true;
            clearTimeout(hoverTimer);
        });
        
        menu.addEventListener('mouseleave', function() {
            isMenuHovered = false;
            hideMenu();
        });
    }
    
    // 点击触发器时阻止默认行为
    toggle.addEventListener('click', function(e) {
        e.preventDefault();
        if (menu.style.display === 'block') {
            hideMenu();
            isToggleHovered = false;
            isMenuHovered = false;
        } else {
            showMenu();
        }
    });
}

function renderSubMenus(children) {
    const items = children.map(child => {
        const url = formatUrl(child.url || child.link_value || '#');

        const li = document.createElement('li');

        if (child.children && child.children.length > 0) {
            // 有三级菜单的二级菜单项
            li.className = 'dropdown-submenu';

            const link = document.createElement('a');
            link.className = 'dropdown-item';
            link.href = url;
            link.textContent = child.name + ' ▶';

            const submenu = document.createElement('ul');
            submenu.className = 'dropdown-menu submenu';

            child.children.forEach(grandChild => {
                const grandUrl = formatUrl(grandChild.url || grandChild.link_value || '#');
                const grandLi = document.createElement('li');
                const grandLink = document.createElement('a');
                grandLink.className = 'dropdown-item';
                grandLink.href = grandUrl;
                grandLink.textContent = grandChild.name;
                grandLi.appendChild(grandLink);
                submenu.appendChild(grandLi);

            });

            li.appendChild(link);
            li.appendChild(submenu);

        } else {
            // 普通二级菜单项
            const link = document.createElement('a');
            link.className = 'dropdown-item';
            link.href = url;
            link.textContent = child.name;
            li.appendChild(link);

        }

        return li.outerHTML;
    });

    return items.join('');
}

function initDropdownEvents() {
    // Bootstrap下拉菜单自动处理，但需要处理三级菜单
    document.querySelectorAll('.dropdown-submenu').forEach(submenu => {
        const toggle = submenu.querySelector('.dropdown-item');
        const menu = submenu.querySelector('.dropdown-menu');

        if (toggle && menu) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // 切换显示状态
                menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
            });

            // 鼠标悬停显示三级菜单
            submenu.addEventListener('mouseenter', function() {
                menu.style.display = 'block';
            });

            submenu.addEventListener('mouseleave', function() {
                menu.style.display = 'none';
            });
        }
    });

    // 点击页面其他地方时关闭所有mega-dropdown菜单
    document.addEventListener('click', function(e) {
        // 检查点击的目标是否在下拉菜单内
        const isDropdownClick = e.target.closest('.mega-dropdown');
        
        if (!isDropdownClick) {
            const megaDropdowns = document.querySelectorAll('.mega-dropdown');
            megaDropdowns.forEach(function(dropdown) {
                const menu = dropdown.querySelector('.dropdown-menu');
                const toggle = dropdown.querySelector('.dropdown-toggle');
                
                if (menu) {
                    menu.style.display = 'none';
                    menu.style.opacity = '0';
                    menu.style.visibility = 'hidden';
                    menu.style.pointerEvents = 'none';
                    dropdown.classList.remove('show');
                    toggle.setAttribute('aria-expanded', 'false');
                }
            });
        }
    });
}

// 检查DIY页面并切换菜单显示
function checkDiyPagesAndToggleMenu(menuItem) {
    fetch('/api/diy-pages')
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            // 如果没有DIY页面，隐藏菜单项
            if (!data.success || !data.pages || data.pages.length === 0) {
                menuItem.style.display = 'none';
            } else {
                // 有DIY页面，加载内容
                loadDiyPagesToMenu();
            }
        })
        .catch(error => {
            console.error('检查DIY页面失败:', error);
            // 检查失败时隐藏菜单项
            menuItem.style.display = 'none';
        });
}

// 检查并添加"更多页面"菜单
function checkAndAddMorePagesMenu(nav) {
    // 先检查是否有DIY页面
    fetch('/api/diy-pages')
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            // 只有当有DIY页面时才添加"更多页面"菜单
            if (data.success && data.pages && data.pages.length > 0) {
                const morePages = document.createElement('li');
                morePages.className = 'nav-item dropdown';
                morePages.innerHTML = `
                    <a class="nav-link dropdown-toggle" href="#" id="diyPagesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        更多页面
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="diyPagesDropdown" id="diy-pages-menu">
                    </ul>
                `;
                nav.appendChild(morePages);

                // 为"更多页面"添加悬停事件
                morePages.addEventListener('mouseenter', function() {
                    const dropdown = morePages.querySelector('.dropdown-menu');
                    if (dropdown) {
                        dropdown.style.display = 'block';
                        morePages.classList.add('show');
                    }
                });

                morePages.addEventListener('mouseleave', function() {
                    const dropdown = morePages.querySelector('.dropdown-menu');
                    if (dropdown) {
                        dropdown.style.display = 'none';
                        morePages.classList.remove('show');
                    }
                });

                // 加载DIY页面内容
                loadDiyPagesToMenu();
            }
        })
        .catch(error => {
            console.error('检查DIY页面失败:', error);
            // 如果检查失败，不添加"更多页面"菜单
        });
}

// 加载DIY页面到导航菜单
function loadDiyPagesToMenu() {
    // 获取菜单容器
    const menuContainer = document.getElementById('diy-pages-menu');
    if (!menuContainer) {
        console.error('未找到DIY页面菜单容器');
        return;
    }

    // 发起API请求
    fetch('/api/diy-pages')
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.pages && data.pages.length > 0) {
                // 清空现有的DIY页面项
                const existingDiyItems = menuContainer.querySelectorAll('.diy-page-item');
                existingDiyItems.forEach(item => item.remove());

                // 添加DIY页面链接
                data.pages.forEach(page => {
                    const li = document.createElement('li');
                    li.className = 'diy-page-item';
                    li.innerHTML = `
                        <a class="dropdown-item" href="/page/${page.slug}" title="${page.description || page.name}">
                            <i class="fas fa-file-alt me-2"></i>
                            ${page.name}
                        </a>
                    `;
                    menuContainer.appendChild(li);
                });
            }
        })
        .catch(error => {
            console.error('加载DIY页面失败:', error);
        });
}
</script>
<!-- 公共样式文件 -->
<style>
/* ===== Header和Navbar样式 ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar {
    padding: 12px 0 !important;
    background: transparent !important;
    border: none !important;
    transition: background 0.3s ease, backdrop-filter 0.3s ease !important;
    min-height: 70px !important;
    display: flex !important;
    align-items: center !important;
}

/* 确保navbar容器正确布局 */
.navbar .container {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    width: 100% !important;
}

/* 滚动时header背景 */
header.scrolled {
    background: rgba(47, 76, 153, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    box-shadow: 0 2px 20px rgba(47, 76, 153, 0.3) !important;
}

/* 确保navbar也有背景 */
header.scrolled .navbar,
header.scrolled .navbar-expand-lg,
header.scrolled .navbar-light {
    background: #2f4c99f5 !important;
    background-color: #2f4c99f5 !important;
}

.navbar-brand {
    font-size: 1.6rem !important;
    font-weight: 600 !important;
    color: white !important;
    text-decoration: none !important;
    display: flex !important;
    align-items: center !important;
    background: none !important;
    -webkit-background-clip: unset !important;
    -webkit-text-fill-color: white !important;
    background-clip: unset !important;
}

.navbar-brand .logo {
    height: 45px !important;
    width: auto !important;
    margin-right: 10px !important;
    filter: brightness(0) invert(1) !important;
    transition: all 0.3s ease !important;
    vertical-align: middle !important;
}

/* 滚动时Logo颜色变化 */
header.scrolled .navbar-brand .logo {
    filter: none !important;
}

/* 导航菜单容器对齐 */
.navbar-nav {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    margin: 0 !important;
    list-style: none !important;
    padding: 0 !important;
}

/* 导航链接样式 */
.navbar-nav .nav-item {
    margin: 0 5px !important;
}

.navbar-nav .nav-link {
    color: white !important;
    font-weight: 400 !important;
    padding: 12px 20px !important;
    position: relative !important;
    transition: color 0.3s ease !important;
    font-size: 15px !important;
    background: none !important;
    border: none !important;
    display: flex !important;
    align-items: center !important;
}

/* 导航链接悬停效果 */
.navbar-nav .nav-link:hover {
    color: #ff6b35 !important;
}

/* 导航链接下划线效果 - 排除下拉菜单项 */
.navbar-nav .nav-link:not(.dropdown-toggle)::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:not(.dropdown-toggle):hover::after {
    width: 80%;
}

/* 激活状态的导航链接样式 */
.navbar-nav .nav-link.active {
    color: #ff6b35 !important;
}

.navbar-nav .nav-link.active:not(.dropdown-toggle)::after {
    width: 80%;
    background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
}

/* 右侧按钮组对齐 */
.navbar-actions {
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
    margin-left: 20px !important;
    height: 45px !important;
    flex-shrink: 0 !important;
}

.search-btn {
    color: white !important;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    text-decoration: none;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white !important;
    text-decoration: none;
}

.search-btn svg {
    width: 16px;
    height: 16px;
    stroke: currentColor;
    stroke-width: 2;
}

/* 登录注册按钮样式 */
.navbar-actions .btn {
    padding: 8px 18px !important;
    font-size: 14px !important;
    border-radius: 4px !important;
    font-weight: 400 !important;
    transition: color 0.3s ease, background 0.3s ease, border-color 0.3s ease !important;
    text-decoration: none !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.navbar-actions .btn-outline-light {
    color: white !important;
    background: transparent !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
}

.navbar-actions .btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: white !important;
}

.navbar-actions .btn-primary {
    background: #ff6b35 !important;
    border-color: #ff6b35 !important;
    color: white !important;
}

.navbar-actions .btn-primary:hover {
    background: #e55a2b !important;
    border-color: #e55a2b !important;
}

/* 滚动时按钮样式调整 */
header.scrolled .navbar-actions .btn-outline-light {
    color: white !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
}

header.scrolled .navbar-actions .btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: white !important;
}

header.scrolled .navbar-actions .btn-primary {
    background: #ff6b35 !important;
    border-color: #ff6b35 !important;
    color: white !important;
}

header.scrolled .navbar-actions .btn-primary:hover {
    background: #e55a2b !important;
    border-color: #e55a2b !important;
    color: white !important;
}

/* 强制确保滚动时按钮样式不被覆盖 */
header.scrolled .btn-primary,
header.scrolled .navbar-actions .btn-primary,
header.scrolled .navbar .btn-primary {
    background: #ff6b35 !important;
    background-color: #ff6b35 !important;
    border-color: #ff6b35 !important;
    color: white !important;
}

header.scrolled .btn-primary:hover,
header.scrolled .navbar-actions .btn-primary:hover,
header.scrolled .navbar .btn-primary:hover {
    background: #e55a2b !important;
    background-color: #e55a2b !important;
    border-color: #e55a2b !important;
    color: white !important;
}

/* 移动端菜单按钮样式 */
.navbar-toggler {
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    padding: 4px 8px !important;
    transition: all 0.3s ease !important;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
    transition: all 0.3s ease !important;
}

/* 确保主要内容不被固定header遮挡 */
main {
    padding-top: 70px;
}

/* 首页轮播图需要全屏显示，移除顶部间距 */
.home-page main {
    padding-top: 0;
}

/* ===== 产品菜单特殊样式 ===== */
.products-menu {
    padding: 0;
}

.products-header {
    background: #f8f9fa;
    padding: 25px 30px;
    border-bottom: 1px solid #e9ecef;
    border-radius: 16px 16px 0 0;
}

/* 产品网格布局 */
.products-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
    padding: 30px;
}

.products-column {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.product-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-radius: 10px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
    border: 1px solid rgba(102, 126, 234, 0.1);
    text-decoration: none;
    color: inherit;
}

.product-item:hover {
    background: rgba(102, 126, 234, 0.12);
    border-color: rgba(102, 126, 234, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
    text-decoration: none;
    color: inherit;
}

.product-icon {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.product-icon.purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
}

.product-icon i {
    font-size: 18px;
    color: white;
}

/* Lucide图标样式 */
.product-icon svg {
    width: 18px;
    height: 18px;
    color: white;
    stroke: white;
    stroke-width: 2;
}

.product-item:hover .product-icon {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.35);
}

.product-name {
    font-size: 0.9rem;
    font-weight: 500;
    color: #333;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.product-item:hover .product-name {
    color: #667eea;
}

/* ===== 菜单分类标题 ===== */
.mega-menu-category {
    font-size: 1.1rem;
    font-weight: 600;
    color: #007bff;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid rgba(0, 123, 255, 0.2);
    position: relative;
}

.mega-menu-category::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 30px;
    height: 2px;
    background: #007bff;
    border-radius: 1px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .solutions-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 900px) {
    .solutions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 600px) {
    .solutions-grid {
        grid-template-columns: 1fr;
    }
    .products-grid {
        grid-template-columns: 1fr;
    }
}
</style>

    <!-- 轮播图区域 - 动态渐变背景 -->
    <?php $initialBg = '/assets/images/tu1.png'; if(!empty($banners) && !empty($banners[0]['image'])): $initialBg = $banners[0]['image']; ?>
    <?php endif; ?>

    <section class="hero-section bg-slide-1" id="heroSection" style="background-image: url('<?php echo asset("assets/images/tu1.png"); ?>'); background-size: cover; background-position: center center; background-repeat: no-repeat;">
        <!-- 动态背景层 -->
        <div class="hero-background" style="background-image: url('<?php echo asset("assets/images/tu1.png"); ?>'); background-size: cover; background-position: center center; background-repeat: no-repeat;"></div>

        <!-- 遮罩层 -->
        <div class="hero-overlay"></div>
        
        <!-- 装饰元素 -->
        <div class="hero-decorations">
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
            <div class="floating-icon icon-1"><i data-lucide="code"></i></div>
            <div class="floating-icon icon-2"><i data-lucide="settings"></i></div>
            <div class="floating-icon icon-3"><i data-lucide="trending-up"></i></div>
            <div class="floating-icon icon-4"><i data-lucide="globe"></i></div>
            <div class="floating-icon icon-5"><i data-lucide="smartphone"></i></div>
        </div>
        
        <div class="hero-slider">
            <div class="swiper-wrapper">
                <?php if($useCustomBanners): ?>
                    <!-- 从数据库读取的轮播图 -->
                    <?php if(is_array($banners) || $banners instanceof \think\Collection || $banners instanceof \think\Paginator): $index = 0; $__LIST__ = $banners;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$banner): $mod = ($index % 2 );++$index;?>
                    <div class="swiper-slide" data-bg-index="<?php echo htmlentities((string) $index); ?>" style="background-image: url('<?php echo htmlentities((string) $banner['image']); ?>'); background-size: cover; background-position: center;">
                        <div class="container">
                            <div class="row align-items-center min-vh-100">
                                <div class="col-lg-8 col-md-10 col-sm-12 mx-auto">
                                    <div class="hero-content text-center">
                                        <h1 class="hero-title animated fadeInUp"><?php echo htmlentities((string) $banner['title']); ?></h1>
                                        <?php if(!empty($banner['subtitle'])): ?>
                                        <p class="hero-subtitle animated fadeInUp delay-1s"><?php echo htmlentities((string) $banner['subtitle']); ?></p>
                                        <?php endif; if(!empty($banner['description'])): ?>
                                        <p class="hero-description animated fadeInUp delay-1s"><?php echo htmlentities((string) $banner['description']); ?></p>
                                        <?php endif; ?>
                                        <div class="hero-buttons animated fadeInUp delay-2s">
                                            <?php if(!empty($banner['link_url'])): ?>
                                            <a href="<?php echo htmlentities((string) $banner['link_url']); ?>" class="btn btn-primary btn-lg" target="<?php echo htmlentities((string) (isset($banner['link_target']) && ($banner['link_target'] !== '')?$banner['link_target']:'_self')); ?>">了解详情</a>
                                            <?php else: ?>
                                            <a href="/solutions" class="btn btn-primary btn-lg">了解方案</a>
                                            <?php endif; ?>
                                            <a href="/contact" class="btn btn-outline-light btn-lg">免费咨询</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; endif; else: echo "" ;endif; else: ?>
                    <!-- 默认轮播图1 -->
                    <div class="swiper-slide" data-bg-index="1">
                        <div class="container">
                            <div class="row align-items-center min-vh-100">
                                <div class="col-lg-8 col-md-10 col-sm-12 mx-auto">
                                    <div class="hero-content text-center">
                                        <h1 class="hero-title">专业企业建站解决方案</h1>
                                        <p class="hero-subtitle">助力企业数字化转型，打造专业品牌形象</p>
                                        <p class="hero-description">提供一站式企业官网建设服务，从设计到开发，从上线到运维，全程专业支持</p>
                                        <div class="hero-buttons">
                                            <a href="/solutions" class="btn btn-primary btn-lg">了解方案</a>
                                            <a href="/contact" class="btn btn-outline-light btn-lg">免费咨询</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 默认轮播图2 -->
                    <div class="swiper-slide" data-bg-index="2">
                        <div class="container">
                            <div class="row align-items-center min-vh-100">
                                <div class="col-lg-8 col-md-10 col-sm-12 mx-auto">
                                    <div class="hero-content text-center">
                                        <h1 class="hero-title animated fadeInUp">智能支付系统集成</h1>
                                        <p class="hero-subtitle animated fadeInUp delay-1s">安全便捷的支付解决方案</p>
                                        <p class="hero-description animated fadeInUp delay-1s">支持多种支付方式，提供完整的支付生态系统，让交易更简单安全</p>
                                        <div class="hero-buttons animated fadeInUp delay-2s">
                                            <a href="/solutions" class="btn btn-primary btn-lg">查看详情</a>
                                            <a href="/contact" class="btn btn-outline-light btn-lg">立即体验</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 默认轮播图3 -->
                    <div class="swiper-slide" data-bg-index="3">
                        <div class="container">
                            <div class="row align-items-center min-vh-100">
                                <div class="col-lg-8 col-md-10 col-sm-12 mx-auto">
                                    <div class="hero-content text-center">
                                        <h1 class="hero-title animated fadeInUp">云端SAAS服务平台</h1>
                                        <p class="hero-subtitle animated fadeInUp delay-1s">高效稳定的云端应用服务</p>
                                        <p class="hero-description animated fadeInUp delay-1s">基于云计算技术，提供弹性扩展的SAAS服务，助力企业快速发展</p>
                                        <div class="hero-buttons animated fadeInUp delay-2s">
                                            <a href="/solutions" class="btn btn-primary btn-lg">产品介绍</a>
                                            <a href="/contact" class="btn btn-outline-light btn-lg">申请试用</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 默认轮播图4 -->
                    <div class="swiper-slide" data-bg-index="4">
                        <div class="container">
                            <div class="row align-items-center min-vh-100">
                                <div class="col-lg-8 col-md-10 col-sm-12 mx-auto">
                                    <div class="hero-content text-center">
                                        <h1 class="hero-title animated fadeInUp">专业技术支持服务</h1>
                                        <p class="hero-subtitle animated fadeInUp delay-1s">7×24小时贴心服务保障</p>
                                        <p class="hero-description animated fadeInUp delay-1s">专业技术团队提供全方位支持，确保系统稳定运行，让您无后顾之忧</p>
                                        <div class="hero-buttons animated fadeInUp delay-2s">
                                            <a href="/about" class="btn btn-primary btn-lg">服务详情</a>
                                            <a href="/contact" class="btn btn-outline-light btn-lg">联系客服</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- 分页器 -->
            <div class="swiper-pagination"></div>
            
            <!-- 导航按钮 -->
            <div class="swiper-button-next"></div>
            <div class="swiper-button-prev"></div>
        </div>
    </section>

    <!-- 特色服务区域 - 使用ceo-home-vip-bg.png作为背景 -->
    <section class="features-section py-5" style="background-image: url('<?php echo asset("assets/images/ceo-home-vip-bg.png"); ?>'); background-size: cover; background-attachment: local; position: relative;">
        <!-- 背景遮罩 -->
        <div class="features-overlay"></div>
        
        <div class="container" style="position: relative; z-index: 2;">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="section-title" style="color: white;">为什么选择我们</h2>
                    <p class="section-subtitle" style="color: rgba(255,255,255,0.9);">专业、可靠、高效的企业级解决方案</p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-item text-center feature-item-overlay">
                        <div class="feature-icon">
                            <i data-lucide="rocket"></i>
                        </div>
                        <h5 style="color: white;">快速部署</h5>
                        <p style="color: rgba(255,255,255,0.8);">专业团队快速响应，高效部署实施，让您的项目快速上线</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-item text-center feature-item-overlay">
                        <div class="feature-icon">
                            <i data-lucide="shield-check"></i>
                        </div>
                        <h5 style="color: white;">安全可靠</h5>
                        <p style="color: rgba(255,255,255,0.8);">企业级安全保障，多重防护机制，确保数据安全无忧</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-item text-center feature-item-overlay">
                        <div class="feature-icon">
                            <i data-lucide="settings"></i>
                        </div>
                        <h5 style="color: white;">定制开发</h5>
                        <p style="color: rgba(255,255,255,0.8);">根据业务需求量身定制，满足个性化要求，提供最佳解决方案</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-item text-center feature-item-overlay">
                        <div class="feature-icon">
                            <i data-lucide="headphones"></i>
                        </div>
                        <h5 style="color: white;">7×24服务</h5>
                        <p style="color: rgba(255,255,255,0.8);">全天候技术支持，专业客服团队，及时响应解决问题</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-item text-center feature-item-overlay">
                        <div class="feature-icon">
                            <i data-lucide="trending-up"></i>
                        </div>
                        <h5 style="color: white;">数据分析</h5>
                        <p style="color: rgba(255,255,255,0.8);">智能数据分析，深度挖掘业务价值，助力决策优化</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-item text-center feature-item-overlay">
                        <div class="feature-icon">
                            <i data-lucide="users"></i>
                        </div>
                        <h5 style="color: white;">团队协作</h5>
                        <p style="color: rgba(255,255,255,0.8);">高效团队协作工具，提升工作效率，促进团队沟通</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 企业展示区域 -->
    <section class="company-showcase" style="background-image: url('<?php echo asset("assets/images/shangjairuzhu.jpg"); ?>'); background-size: cover; background-position: center; background-repeat: no-repeat; background-attachment: fixed; position: relative; image-rendering: -webkit-optimize-contrast; image-rendering: crisp-edges; image-rendering: optimizeQuality;">
        <div class="hero-overlay"></div>
        <div class="container">
            <div class="row align-items-center" style="min-height: 500px;">
                <div class="col-lg-6">
                    <div class="showcase-content" style="position: relative; z-index: 2; color: white;">
                        <h2 class="section-title" style="color: white;">企业实力展示</h2>
                        <p class="section-subtitle" style="color: rgba(255,255,255,0.9);">专业团队，卓越服务，值得信赖的合作伙伴</p>
                        <div class="showcase-features">
                            <div class="showcase-item" style="margin-bottom: 15px;">
                                <i class="fa fa-check-circle" style="color: var(--accent-color); margin-right: 10px;"></i>
                                <span>10年+行业经验</span>
                            </div>
                            <div class="showcase-item" style="margin-bottom: 15px;">
                                <i class="fa fa-check-circle" style="color: var(--accent-color); margin-right: 10px;"></i>
                                <span>500+成功案例</span>
                            </div>
                            <div class="showcase-item" style="margin-bottom: 15px;">
                                <i class="fa fa-check-circle" style="color: var(--accent-color); margin-right: 10px;"></i>
                                <span>专业技术团队</span>
                            </div>
                            <div class="showcase-item" style="margin-bottom: 15px;">
                                <i class="fa fa-check-circle" style="color: var(--accent-color); margin-right: 10px;"></i>
                                <span>7×24技术支持</span>
                            </div>
                        </div>
                        <div style="margin-top: 30px;">
                            <a href="/about" class="btn btn-primary btn-lg">了解更多</a>
                            <a href="/contact" class="btn btn-outline-primary btn-lg" style="border-color: white; color: white; margin-left: 15px;">联系我们</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="showcase-image" style="position: relative; z-index: 2; text-align: center;">
                        <img src="<?php echo asset('assets/images/ceo-apply-bg.png'); ?>" alt="企业展示" class="img-fluid" style="border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.3); max-width: 90%;">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 解决方案区域 -->
    <?php if(!empty($solutions)): ?>
    <section class="solutions-section py-5 bg-light">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="section-title">解决方案</h2>
                    <p class="section-subtitle">全行业解决方案，满足不同业务需求</p>
                </div>
            </div>

            <div class="solutions-grid">
                <?php if(is_array($solutions) || $solutions instanceof \think\Collection || $solutions instanceof \think\Paginator): $index = 0; $__LIST__ = $solutions;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$solution): $mod = ($index % 2 );++$index;?>
                    <a href="/solutions/<?php echo htmlentities((string) $solution['slug']); ?>" class="solution-item">
                        <?php if(!empty($solution['image'])): ?>
                            <img src="<?php echo htmlentities((string) $solution['image']); ?>" class="solution-image" alt="<?php echo htmlentities((string) $solution['name']); ?>">
                        <?php endif; ?>
                        <div class="solution-content">
                            <div class="solution-icon">
                                <?php if(!empty($solution['icon'])): ?>
                                    <img src="<?php echo htmlentities((string) $solution['icon']); ?>" alt="<?php echo htmlentities((string) $solution['name']); ?>">
                                <?php else: ?>
                                    <i class="fa fa-cube"></i>
                                <?php endif; ?>
                            </div>
                            <h5 class="card-title"><?php echo htmlentities((string) $solution['name']); ?></h5>
                            <p class="card-text"><?php echo htmlentities((string) $solution['short_description']); ?></p>
                            <a href="/solutions/<?php echo htmlentities((string) $solution['slug']); ?>" class="btn btn-primary">了解详情</a>
                        </div>
                    </a>
                <?php endforeach; endif; else: echo "" ;endif; ?>
            </div>

            <div class="text-center mt-4">
                <a href="/solutions" class="btn btn-outline-primary btn-lg">查看全部解决方案</a>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- 新闻动态区域 -->
    <section class="news-section" style="background-image: url('<?php echo asset("assets/images/ceo-apply-bg1.png"); ?>'); position: relative;">
        <!-- 背景覆盖层 -->
        <div class="news-overlay"></div>
        <div class="container" style="position: relative; z-index: 2;">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <div class="section-header">
                        <h2 class="section-title">最新动态</h2>
                        <p class="section-subtitle">关注我们的最新资讯和行业动态</p>
                        <div class="section-divider"></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <?php if(!empty($latestNews)): if(is_array($latestNews) || $latestNews instanceof \think\Collection || $latestNews instanceof \think\Paginator): $index = 0; $__LIST__ = $latestNews;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$news): $mod = ($index % 2 );++$index;?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="news-card">
                                <div class="news-image">
                                    <?php if(!empty($news['image'])): ?>
                                        <img src="<?php echo htmlentities((string) $news['image']); ?>" alt="<?php echo htmlentities((string) $news['title']); ?>" class="img-fluid">
                                    <?php else: 
                                            $newsImages = ['news1.png', 'news2.png', 'news3.png'];
                                            $imageIndex = ($index - 1) % 3;
                                         ?>
                                        <img src="<?php echo asset('assets/images/' . $newsImages[$imageIndex]); ?>" alt="<?php echo htmlentities((string) $news['title']); ?>" class="img-fluid">
                                    <?php endif; ?>
                                    <div class="news-overlay">
                                        <div class="news-date">
                                            <span class="day"><?php echo htmlentities((string) date('d',!is_numeric((isset($news['published_at']) && ($news['published_at'] !== '')?$news['published_at']:$news['created_at']))? strtotime((isset($news['published_at']) && ($news['published_at'] !== '')?$news['published_at']:$news['created_at'])) : (isset($news['published_at']) && ($news['published_at'] !== '')?$news['published_at']:$news['created_at']))); ?></span>
                                            <span class="month"><?php echo htmlentities((string) date('M',!is_numeric((isset($news['published_at']) && ($news['published_at'] !== '')?$news['published_at']:$news['created_at']))? strtotime((isset($news['published_at']) && ($news['published_at'] !== '')?$news['published_at']:$news['created_at'])) : (isset($news['published_at']) && ($news['published_at'] !== '')?$news['published_at']:$news['created_at']))); ?></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="news-content">
                                    <div class="news-meta">
                                        <span class="news-category">
                                            <i class="fa fa-tag"></i>
                                            <?php echo htmlentities((string) (isset($news['category']['name']) && ($news['category']['name'] !== '')?$news['category']['name']:'公司动态')); ?>
                                        </span>
                                        <span class="news-time">
                                            <i class="fa fa-clock-o"></i>
                                            <?php echo htmlentities((string) date('Y-m-d',!is_numeric((isset($news['published_at']) && ($news['published_at'] !== '')?$news['published_at']:$news['created_at']))? strtotime((isset($news['published_at']) && ($news['published_at'] !== '')?$news['published_at']:$news['created_at'])) : (isset($news['published_at']) && ($news['published_at'] !== '')?$news['published_at']:$news['created_at']))); ?>
                                        </span>
                                    </div>
                                    <h5 class="news-title">
                                        <a href="<?php echo htmlentities((string) (isset($news['detail_url']) && ($news['detail_url'] !== '')?$news['detail_url']:'/news/' . $news['slug'])); ?>"><?php echo htmlentities((string) $news['title']); ?></a>
                                    </h5>
                                    <p class="news-excerpt"><?php echo htmlentities((string) mb_substr(strip_tags((isset($news['summary']) && ($news['summary'] !== '')?$news['summary']:$news['content'])),0,100,'UTF-8')); ?>...</p>
                                    <div class="news-footer">
                                        <a href="<?php echo htmlentities((string) (isset($news['detail_url']) && ($news['detail_url'] !== '')?$news['detail_url']:'/news/' . $news['slug'])); ?>" class="read-more-btn">
                                            阅读更多 <i class="fa fa-arrow-right"></i>
                                        </a>
                                        <div class="news-stats">
                                            <span><i class="fa fa-eye"></i> <?php echo htmlentities((string) (isset($news['views']) && ($news['views'] !== '')?$news['views']:0)); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; endif; else: echo "" ;endif; else: ?>
                    <!-- 默认新闻展示 -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="news-card">
                            <div class="news-image">
                                <img src="<?php echo asset('assets/images/news1.png'); ?>" alt="企业数字化转型" class="img-fluid">
                                <div class="news-overlay">
                                    <div class="news-date">
                                        <span class="day"><?php echo date('d'); ?></span>
                                        <span class="month"><?php echo date('M'); ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="news-content">
                                <div class="news-meta">
                                    <span class="news-category"><i class="fa fa-tag"></i> 行业资讯</span>
                                    <span class="news-time"><i class="fa fa-clock-o"></i> <?php echo date('Y-m-d'); ?></span>
                                </div>
                                <h5 class="news-title">
                                    <a href="/news">企业数字化转型的关键要素</a>
                                </h5>
                                <p class="news-excerpt">随着数字化时代的到来，企业数字化转型已成为提升竞争力的重要途径。本文将深入探讨数字化转型的核心要素...</p>
                                <div class="news-footer">
                                    <a href="/news" class="read-more-btn">
                                        阅读更多 <i class="fa fa-arrow-right"></i>
                                    </a>
                                    <div class="news-stats">
                                        <span><i class="fa fa-eye"></i> 1,234</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <div class="text-center mt-5">
                <a href="/news" class="btn btn-primary btn-lg news-more-btn">
                    <i class="fa fa-newspaper-o"></i> 查看更多动态
                </a>
            </div>
        </div>
    </section>

    <!-- 数据统计区域 - 重新设计 -->
    <section class="stats-section">
        <div class="stats-background">
            <img src="<?php echo asset('assets/images/index_section6_bg.png'); ?>" alt="统计背景" class="stats-bg-image">
            <div class="stats-overlay"></div>
        </div>

        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <div class="stats-header">
                        <h2 class="stats-title">实力见证</h2>
                        <p class="stats-subtitle">用数据说话，用实力证明</p>
                        <div class="stats-divider"></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <img src="<?php echo asset('assets/images/footer-top-01.png'); ?>" alt="成功案例" class="stat-icon-img">
                        </div>
                        <div class="stat-content">
                            <h3 class="counter" data-count="500">100</h3>
                            <span class="stat-plus">+</span>
                            <p class="stat-label">成功案例</p>
                            <div class="stat-description">覆盖多个行业领域</div>
                        </div>
                        <div class="stat-decoration"></div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <img src="<?php echo asset('assets/images/footer-top-02.png'); ?>" alt="合作客户" class="stat-icon-img">
                        </div>
                        <div class="stat-content">
                            <h3 class="counter" data-count="1000">30</h3>
                            <span class="stat-plus">+</span>
                            <p class="stat-label">合作客户</p>
                            <div class="stat-description">遍布全国各地</div>
                        </div>
                        <div class="stat-decoration"></div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <img src="<?php echo asset('assets/images/footer-top-03.png'); ?>" alt="专业团队" class="stat-icon-img">
                        </div>
                        <div class="stat-content">
                            <h3 class="counter" data-count="50">10</h3>
                            <span class="stat-plus">+</span>
                            <p class="stat-label">专业团队</p>
                            <div class="stat-description">资深技术专家</div>
                        </div>
                        <div class="stat-decoration"></div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <img src="<?php echo asset('assets/images/footer-top-04.png'); ?>" alt="服务年限" class="stat-icon-img">
                        </div>
                        <div class="stat-content">
                            <h3 class="counter" data-count="10">22</h3>
                            <span class="stat-plus">+</span>
                            <p class="stat-label">服务年限</p>
                            <div class="stat-description">行业经验丰富</div>
                        </div>
                        <div class="stat-decoration"></div>
                    </div>
                </div>
            </div>

            <!-- 添加信任标识 -->
            <div class="row mt-5">
                <div class="col-12">
                    <div class="trust-indicators">
                        <div class="trust-item">
                            <i class="fa fa-shield"></i>
                            <span>安全可靠</span>
                        </div>
                        <div class="trust-item">
                            <i class="fa fa-clock-o"></i>
                            <span>7×24服务</span>
                        </div>
                        <div class="trust-item">
                            <i class="fa fa-certificate"></i>
                            <span>权威认证</span>
                        </div>
                        <div class="trust-item">
                            <i class="fa fa-thumbs-up"></i>
                            <span>客户满意</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



<script>
// 首页专用JavaScript - 只处理Lucide图标初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化Lucide图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});
</script>

</main>

    <!-- 页脚 -->
    <footer class="footer">
        <!-- 主要页脚内容 -->
        <div class="footer-main">
            <div class="container">
                <div class="row">
                    <!-- 公司信息 -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="footer-widget">
                            <div class="footer-logo">
                                <img src="<?php echo asset('assets/images/logo.png'); ?>" alt="<?php echo htmlentities((string) (isset($siteConfig['site_name']) && ($siteConfig['site_name'] !== '')?$siteConfig['site_name']:'三只鱼网络')); ?>">
                            </div>
                            <p class="footer-desc">
                                <?php echo htmlentities((string) (isset($siteConfig['site_description']) && ($siteConfig['site_description'] !== '')?$siteConfig['site_description']:'我们提供专业的企业解决方案，助力企业数字化转型，打造专业品牌形象，提供一站式服务支持。')); ?>
                            </p>
                            
                            <!-- 微信二维码 -->
                            <div class="footer-qrcode">
                                <img src="<?php echo asset('assets/images/weixin.png'); ?>" alt="微信二维码" class="qrcode-img">
                                <p class="qrcode-text">扫码关注微信</p>
                            </div>
                            
                            <div class="footer-social">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 解决方案 -->
                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">解决方案</h5>
                            <ul class="footer-links">
                                <?php if(is_array($footerSolutions) || $footerSolutions instanceof \think\Collection || $footerSolutions instanceof \think\Paginator): $i = 0; $__LIST__ = $footerSolutions;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$solution): $mod = ($i % 2 );++$i;?>
                                <li><a href="/solutions/<?php echo htmlentities((string) $solution['link_value']); ?>"><?php echo htmlentities((string) $solution['name']); ?></a></li>
                                <?php endforeach; endif; else: echo "" ;endif; if(empty($footerSolutions) || (($footerSolutions instanceof \think\Collection || $footerSolutions instanceof \think\Paginator ) && $footerSolutions->isEmpty())): ?>
                                <li><a href="/solutions">暂无解决方案</a></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 产品分类 -->
                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">产品分类</h5>
                            <ul class="footer-links">
                                <?php if(is_array($footerProductCategories) || $footerProductCategories instanceof \think\Collection || $footerProductCategories instanceof \think\Paginator): $i = 0; $__LIST__ = $footerProductCategories;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$category): $mod = ($i % 2 );++$i;?>
                                <li><a href="/products?category=<?php echo htmlentities((string) $category['slug']); ?>"><?php echo htmlentities((string) $category['name']); ?></a></li>
                                <?php endforeach; endif; else: echo "" ;endif; if(empty($footerProductCategories) || (($footerProductCategories instanceof \think\Collection || $footerProductCategories instanceof \think\Paginator ) && $footerProductCategories->isEmpty())): ?>
                                <li><a href="/products">暂无产品分类</a></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 普通页面 -->
                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">帮助中心</h5>
                            <ul class="footer-links">
                                <?php if(is_array($footerDiyPages) || $footerDiyPages instanceof \think\Collection || $footerDiyPages instanceof \think\Paginator): $i = 0; $__LIST__ = $footerDiyPages;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$page): $mod = ($i % 2 );++$i;?>
                                <li><a href="/page/<?php echo htmlentities((string) $page['slug']); ?>"><?php echo htmlentities((string) $page['name']); ?></a></li>
                                <?php endforeach; endif; else: echo "" ;endif; if(empty($footerDiyPages) || (($footerDiyPages instanceof \think\Collection || $footerDiyPages instanceof \think\Paginator ) && $footerDiyPages->isEmpty())): ?>
                                <li><a href="/about">关于我们</a></li>
                                <li><a href="/contact">联系我们</a></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 联系信息 -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">联系我们</h5>
                            <div class="footer-contact">
                                <div class="contact-item">
                                    <i data-lucide="map-pin"></i>
                                    <span><?php echo htmlentities((string) (isset($siteConfig['company_address']) && ($siteConfig['company_address'] !== '')?$siteConfig['company_address']:'北京市朝阳区科技园区')); ?></span>
                                </div>
                                <div class="contact-item">
                                    <i data-lucide="phone"></i>
                                    <span><?php echo htmlentities((string) (isset($siteConfig['company_phone']) && ($siteConfig['company_phone'] !== '')?$siteConfig['company_phone']:'1800001111')); ?></span>
                                </div>
                                <div class="contact-item">
                                    <i data-lucide="mail"></i>
                                    <span><?php echo htmlentities((string) (isset($siteConfig['company_email']) && ($siteConfig['company_email'] !== '')?$siteConfig['company_email']:'<EMAIL>')); ?></span>
                                </div>
                                <div class="contact-item">
                                    <i data-lucide="message-square"></i>
                                    <span><?php echo htmlentities((string) (isset($siteConfig['company_qq']) && ($siteConfig['company_qq'] !== '')?$siteConfig['company_qq']:'*********')); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 版权信息 -->
        <div class="footer-bottom">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="copyright">
                            <p>&copy; <?php echo date('Y'); ?> <?php echo htmlentities((string) (isset($siteConfig['site_name']) && ($siteConfig['site_name'] !== '')?$siteConfig['site_name']:'三只鱼科技有限公司')); ?>. 版权所有</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="footer-links-bottom">
                            <a href="/privacy">隐私政策</a>
                            <a href="/terms">服务条款</a>
                            <a href="/sitemap">网站地图</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- 返回顶部按钮 -->
    <div class="back-to-top" id="backToTop">
        <i class="fa fa-angle-up"></i>
    </div>

    <!-- 在线客服 -->
    <div class="online-service">
        <div class="service-btn" id="serviceBtn">
            <i data-lucide="message-circle"></i>
            <span>在线客服</span>
        </div>
        <div class="service-panel" id="servicePanel">
            <div class="service-header">
                <h6>在线客服</h6>
                <button class="close-btn" id="closeService">&times;</button>
            </div>
            <div class="service-content">
                <div class="service-item">
                    <div class="service-icon qq-icon">
                        <i data-lucide="message-circle"></i>
                    </div>
                    <div class="service-info">
                        <h6>QQ咨询</h6>
                        <p><?php echo htmlentities((string) (isset($siteConfig['company_qq']) && ($siteConfig['company_qq'] !== '')?$siteConfig['company_qq']:'*********')); ?></p>
                    </div>
                    <a href="http://wpa.qq.com/msgrd?v=3&uin=<?php echo htmlentities((string) (isset($siteConfig['company_qq']) && ($siteConfig['company_qq'] !== '')?$siteConfig['company_qq']:'*********')); ?>&site=qq&menu=yes" target="_blank" class="btn btn-sm btn-primary">咨询</a>
                </div>
                <div class="service-item">
                    <div class="service-icon wechat-icon">
                        <i data-lucide="smartphone"></i>
                    </div>
                    <div class="service-info">
                        <h6>微信咨询</h6>
                        <p><?php echo htmlentities((string) (isset($siteConfig['company_wechat']) && ($siteConfig['company_wechat'] !== '')?$siteConfig['company_wechat']:'hsdj37')); ?></p>
                    </div>
                    <button class="btn btn-sm btn-success" onclick="showWechatQR()">扫码</button>
                </div>
                <div class="service-item">
                    <div class="service-icon phone-icon">
                        <i data-lucide="phone"></i>
                    </div>
                    <div class="service-info">
                        <h6>电话咨询</h6>
                        <p><?php echo htmlentities((string) (isset($siteConfig['company_phone']) && ($siteConfig['company_phone'] !== '')?$siteConfig['company_phone']:'************')); ?></p>
                    </div>
                    <a href="tel:<?php echo htmlentities((string) (isset($siteConfig['company_phone']) && ($siteConfig['company_phone'] !== '')?$siteConfig['company_phone']:'************')); ?>" class="btn btn-sm btn-warning">拨打</a>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="<?php echo asset('assets/js/jquery.min.js'); ?>"></script>
    <script src="<?php echo asset('assets/js/bootstrap.bundle.min.js'); ?>"></script> 
    <script src="<?php echo asset('assets/js/swiper-bundle.min.js'); ?>"></script>
    <script src="<?php echo asset('assets/js/main.js'); ?>"></script>
    <script src="<?php echo asset('assets/js/hero-slider.js'); ?>"></script>
    <script src="<?php echo asset('assets/js/scroll-handler.js'); ?>"></script>

    <!-- 初始化Lucide图标 -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    });
    </script>

</body>
</html>


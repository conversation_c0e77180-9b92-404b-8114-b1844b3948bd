{"version": 3, "sources": ["out-editor/vs/editor/editor.main.nls.zh-tw.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\ndefine(\"vs/editor/editor.main.nls.zh-tw\", {\n\t\"vs/base/browser/ui/actionbar/actionViewItems\": [\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/base/browser/ui/findinput/findInput\": [\n\t\t\"輸入\",\n\t],\n\t\"vs/base/browser/ui/findinput/findInputToggles\": [\n\t\t\"大小寫須相符\",\n\t\t\"全字拼寫須相符\",\n\t\t\"使用規則運算式\",\n\t],\n\t\"vs/base/browser/ui/findinput/replaceInput\": [\n\t\t\"輸入\",\n\t\t\"保留大小寫\",\n\t],\n\t\"vs/base/browser/ui/hover/hoverWidget\": [\n\t\t\"使用 {0} 在可存取檢視中檢查此項目。\",\n\t\t\"透過目前無法透過按鍵繫結關係觸發的開啟可存取檢視命令，在可存取檢視中檢查此項目。\",\n\t],\n\t\"vs/base/browser/ui/iconLabel/iconLabelHover\": [\n\t\t\"正在載入...\",\n\t],\n\t\"vs/base/browser/ui/inputbox/inputBox\": [\n\t\t\"錯誤: {0}\",\n\t\t\"警告: {0}\",\n\t\t\"資訊: {0}\",\n\t\t\" 或 {0} 以取得歷程記錄\",\n\t\t\" ({0} 以取得歷程記錄)\",\n\t\t\"已清除輸入\",\n\t],\n\t\"vs/base/browser/ui/keybindingLabel/keybindingLabel\": [\n\t\t\"未繫結\",\n\t],\n\t\"vs/base/browser/ui/selectBox/selectBoxCustom\": [\n\t\t\"選取方塊\",\n\t],\n\t\"vs/base/browser/ui/toolbar/toolbar\": [\n\t\t\"更多操作\",\n\t],\n\t\"vs/base/browser/ui/tree/abstractTree\": [\n\t\t\"篩選\",\n\t\t\"模糊比對\",\n\t\t\"要篩選的類型\",\n\t\t\"要搜尋的類型\",\n\t\t\"要搜尋的類型\",\n\t\t\"關閉\",\n\t\t\"找不到任何元素。\",\n\t],\n\t\"vs/base/common/actions\": [\n\t\t\"(空的)\",\n\t],\n\t\"vs/base/common/errorMessage\": [\n\t\t\"{0}: {1}\",\n\t\t\"發生系統錯誤 ({0})\",\n\t\t\"發生未知的錯誤。如需詳細資訊，請參閱記錄檔。\",\n\t\t\"發生未知的錯誤。如需詳細資訊，請參閱記錄檔。\",\n\t\t\"{0} (總計 {1} 個錯誤)\",\n\t\t\"發生未知的錯誤。如需詳細資訊，請參閱記錄檔。\",\n\t],\n\t\"vs/base/common/keybindingLabels\": [\n\t\t\"Ctrl\",\n\t\t\"Shift\",\n\t\t\"Alt\",\n\t\t\"Windows\",\n\t\t\"Ctrl\",\n\t\t\"Shift\",\n\t\t\"Alt\",\n\t\t\"超級鍵\",\n\t\t\"Control\",\n\t\t\"Shift\",\n\t\t\"選項\",\n\t\t\"命令\",\n\t\t\"Control\",\n\t\t\"Shift\",\n\t\t\"Alt\",\n\t\t\"Windows\",\n\t\t\"Control\",\n\t\t\"Shift\",\n\t\t\"Alt\",\n\t\t\"超級鍵\",\n\t],\n\t\"vs/base/common/platform\": [\n\t\t\"_\",\n\t],\n\t\"vs/editor/browser/controller/textAreaHandler\": [\n\t\t\"編輯器\",\n\t\t\"目前無法存取此編輯器。\",\n\t\t\"{0} 若要啟用螢幕助讀程式最佳化模式，請使用 {1}\",\n\t\t\"{0} 若要啟用螢幕助讀程式最佳化模式，，請使用 {1} 開啟快速挑選，然後執行 [切換螢幕助讀程式協助工具模式] 命令，該模式目前無法透過鍵盤觸發。\",\n\t\t\"{0} 請使用 {1} 存取按鍵繫結關係編輯器並加以執行，以為 [切換螢幕助讀程式協助工具模式] 命令指派按鍵繫結關係。\",\n\t],\n\t\"vs/editor/browser/coreCommands\": [\n\t\t\"即使行的長度過長，仍要堅持至結尾\",\n\t\t\"即使行的長度過長，仍要堅持至結尾\",\n\t\t\"已移除次要資料指標\",\n\t],\n\t\"vs/editor/browser/editorExtensions\": [\n\t\t\"復原(&&U)\",\n\t\t\"復原\",\n\t\t\"取消復原(&&R)\",\n\t\t\"重做\",\n\t\t\"全選(&&S)\",\n\t\t\"全選\",\n\t],\n\t\"vs/editor/browser/widget/codeEditorWidget\": [\n\t\t\"游標數目已限制為 {0}。請考慮使用 [尋找和取代](https://code.visualstudio.com/docs/editor/codebasics#_find-and-replace) 進行較大型的變更，或增加編輯器的多重游標限制設定。\",\n\t\t\"增加多重游標限制\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/accessibleDiffViewer\": [\n\t\t\"易存取差異檢視器中的 [插入] 圖示。\",\n\t\t\"易存取差異檢視器中的 [移除] 圖示。\",\n\t\t\"易存取差異檢視器中的 [關閉] 圖示。\",\n\t\t\"關閉\",\n\t\t\"可存取的 Diff 檢視器。使用向上和向下箭頭來瀏覽。\",\n\t\t\"未變更任一行\",\n\t\t\"已變更 1 行\",\n\t\t\"已變更 {0} 行\",\n\t\t\"{1} 項差異中的第 {0} 項: 原始行 {2}、{3}，修改行 {4}、{5}\",\n\t\t\"空白\",\n\t\t\"{0} 未變更行 {1}\",\n\t\t\"{0} 原始行 {1} 修改的行 {2}\",\n\t\t\"+ {0} 修改行 {1}\",\n\t\t\"- {0} 原始行 {1}\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/colors\": [\n\t\t\"在 Diff 編輯器中移動的文字的框線色彩。\",\n\t\t\"在 Diff 編輯器中移動的文字的作用中框線色彩。\",\n\t\t\"未變更的區域小工具周圍的陰影色彩。\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/decorations\": [\n\t\t\"Diff 編輯器中用於插入的線條裝飾。\",\n\t\t\"Diff 編輯器中用於移除的線條裝飾。\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/diffEditor.contribution\": [\n\t\t\"切換摺疊未變更的區域\",\n\t\t\"切換顯示移動的程式碼區塊\",\n\t\t\"當空間有限時切換使用內嵌檢視\",\n\t\t\"空間有限時使用內嵌檢視\",\n\t\t\"顯示移動的程式碼區塊\",\n\t\t\"Diff 編輯器\",\n\t\t\"切換側邊\",\n\t\t\"結束比較移動\",\n\t\t\"摺疊所有未變更的區域\",\n\t\t\"顯示所有未變更的區域\",\n\t\t\"可存取的 Diff 檢視器\",\n\t\t\"移至下一個差異\",\n\t\t\"開啟易存取差異檢視器\",\n\t\t\"移至上一個差異\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/diffEditorDecorations\": [\n\t\t\"還原選取的變更\",\n\t\t\"還原變更\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/diffEditorEditors\": [\n\t\t\" 使用 {0} 以開啟協助工具說明。\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/hideUnchangedRegionsFeature\": [\n\t\t\"摺疊未變更的區域\",\n\t\t\"按一下或拖曳以在上方顯示更多內容\",\n\t\t\"顯示未變更的區域\",\n\t\t\"按一下或拖曳以在下方顯示更多內容\",\n\t\t\"{0} 條隱藏行\",\n\t\t\"按兩下以展開\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/inlineDiffDeletedCodeMargin\": [\n\t\t\"複製已刪除的行\",\n\t\t\"複製已刪除的行\",\n\t\t\"複製變更的行\",\n\t\t\"複製變更的行\",\n\t\t\"複製已刪除的行 （{0}）\",\n\t\t\"複製變更的行 ({0})\",\n\t\t\"還原此變更\",\n\t],\n\t\"vs/editor/browser/widget/diffEditor/movedBlocksLines\": [\n\t\t\"行 {0}-{1} 的程式碼已移動，且有所變更\",\n\t\t\"行 {0}-{1} 的程式碼已移動，且有所變更\",\n\t\t\"程式碼已移至行 {0}-{1}\",\n\t\t\"行 {0}-{1} 的程式碼已移動\",\n\t],\n\t\"vs/editor/browser/widget/multiDiffEditorWidget/colors\": [\n\t\t\"Diff 編輯器標頭的背景色彩\",\n\t],\n\t\"vs/editor/common/config/editorConfigurationSchema\": [\n\t\t\"編輯器\",\n\t\t\"與 Tab 相等的空格數量。當 {0} 已開啟時，會根據檔案內容覆寫此設定。\",\n\t\t\"用於縮排或 \\'tabSize\\' 使用 `\\\"editor.tabSize\\\"` 值的空格數目。當 \\'#editor.detectIndentation#\\' 開啟時，會根據檔案內容覆寫這個設定。\",\n\t\t\"在按 `Tab` 時插入空格。當 {0} 開啟時，會根據檔案內容覆寫此設定。\",\n\t\t\"根據檔案內容，控制當檔案開啟時，是否自動偵測 {0} 和 {1}。\",\n\t\t\"移除尾端自動插入的空白字元。\",\n\t\t\"針對大型檔案停用部分高記憶體需求功能的特殊處理方式。\",\n\t\t\"關閉 Word 型建議。\",\n\t\t\"僅建議來自使用中文件中的字組。\",\n\t\t\"建議來自所有已開啟文件中，語言相同的字組。\",\n\t\t\"建議來自所有已開啟文件中的字組。\",\n\t\t\"控制是否應該根據文件中的文字來計算完成，以及從哪些文件計算。\",\n\t\t\"所有彩色主題皆已啟用語意醒目提示。\",\n\t\t\"所有彩色主題皆已停用語意醒目提示。\",\n\t\t\"語意醒目提示由目前之彩色佈景主題的 \\'semanticHighlighting\\' 設定所設定。\",\n\t\t\"控制 semanticHighlighting 是否會為支援的語言顯示。\",\n\t\t\"即使按兩下內容或按 `Escape`，仍保持瞄孔編輯器開啟。\",\n\t\t\"因效能的緣故，不會將超過此高度的行 Token 化\",\n\t\t\"控制權杖化是否應該在 Web 工作者上非同步進行。\",\n\t\t\"控制是否應該記錄非同步權杖化。僅適用偵錯。\",\n\t\t\"控制是否應使用舊版背景 Token 化來驗證非同步 Token 化。可能會減慢 Token 化的速度。僅用於偵錯。\",\n\t\t\"定義增加或減少縮排的括弧符號。\",\n\t\t\"左括弧字元或字串順序。\",\n\t\t\"右括弧字元或字串順序。\",\n\t\t\"定義當括弧配對著色已啟用時，由其巢狀層級著色的括弧配對。\",\n\t\t\"左括弧字元或字串順序。\",\n\t\t\"右括弧字元或字串順序。\",\n\t\t\"取消 Diff 計算前的逾時限制 (毫秒)。若無逾時，請使用 0。\",\n\t\t\"要計算差異的檔案大小上限 (MB)。使用 0 表示無限制。\",\n\t\t\"控制 Diff 編輯器要並排或內嵌顯示 Diff。\",\n\t\t\"如果差異編輯器寬度小於此值，則使用內嵌檢視。\",\n\t\t\"如果啟用且編輯器寬度太小，則會使用內嵌檢視。\",\n\t\t\"啟用時，Diff 編輯器會在其字元邊緣顯示箭頭，以還原變更。\",\n\t\t\"啟用時，Diff 編輯器會忽略前置或後置空格的變更。\",\n\t\t\"控制 Diff 編輯器是否要為新增/移除的變更顯示 +/- 標記。\",\n\t\t\"控制編輯器是否顯示 codelens。\",\n\t\t\"一律不換行。\",\n\t\t\"依檢視區寬度換行。\",\n\t\t\"將依據 {0} 設定自動換行。\",\n\t\t\"使用舊版差異演算法。\",\n\t\t\"使用進階版差異演算法。\",\n\t\t\"控制差異編輯器是否顯示未變更的區域。\",\n\t\t\"控制未變更區域的使用行數。\",\n\t\t\"控制未變更區域的最小使用行數。\",\n\t\t\"控制比較未變更的區域時，要使用多少行作為內容。\",\n\t\t\"控制 Diff 編輯器是否應該顯示偵測到的程式碼移動。\",\n\t\t\"控制差異編輯器是否顯示空白裝飾項目，以查看插入或刪除字元的位置。\",\n\t],\n\t\"vs/editor/common/config/editorOptions\": [\n\t\t\"使用平台 API 以偵測螢幕助讀程式附加。\",\n\t\t\"使用螢幕助讀程式將使用方式最佳化。\",\n\t\t\"假設螢幕助讀程式未連結。\",\n\t\t\"控制 UI 是否應於已為螢幕助讀程式最佳化的模式中執行。\",\n\t\t\"控制是否要在註解時插入空白字元。\",\n\t\t\"控制是否應以行註解的切換、新增或移除動作，忽略空白的行。\",\n\t\t\"控制複製時不選取任何項目是否會複製目前程式行。\",\n\t\t\"控制在輸入期間是否要跳過游標來尋找相符的項目。\",\n\t\t\"永不從編輯器選取範圍中植入搜尋字串。\",\n\t\t\"一律從編輯器選取範圍中植入搜尋字串，包括游標位置的字。\",\n\t\t\"只有來自編輯器選取範圍中的植入搜尋字串。\",\n\t\t\"控制 [尋找小工具] 中的搜尋字串是否來自編輯器選取項目。\",\n\t\t\"永不自動開啟 [在選取範圍中尋找] (預設)。\",\n\t\t\"一律自動開啟 [在選取範圍中尋找]。\",\n\t\t\"選取多行內容時，自動開啟 [在選取範圍中尋找]。\",\n\t\t\"控制自動開啟 [在選取範圍中尋找] 的條件。\",\n\t\t\"控制尋找小工具是否在 macOS 上讀取或修改共用尋找剪貼簿。\",\n\t\t\"控制尋找小工具是否應在編輯器頂端額外新增行。若為 true，當您可看到尋找小工具時，您的捲動範圍會超過第一行。\",\n\t\t\"當再也找不到其他相符項目時，控制是否自動從開頭 (或結尾) 重新開始搜尋。\",\n\t\t\"啟用/停用連字字型 (\\'calt\\' 和 \\'liga\\' 字型功能)。將此項變更為字串，以精確控制 \\'font-feature-settings\\' CSS 屬性。\",\n\t\t\"明確的 \\'font-feature-settings\\' CSS 屬性。如果只需要開啟/關閉連字，可以改為傳遞布林值。\",\n\t\t\"設定連字字型或字型功能。可以是布林值以啟用/停用連字，或代表 CSS \\'font-feature-settings\\' 屬性的字串。\",\n\t\t\"啟用/停用從 font-weight 到 font-variation-settings 的轉換。將此設定變更為字串，以更精細地控制 \\'font-variation-settings\\' CSS 屬性。\",\n\t\t\"明確的 \\'font-variation-settings\\' CSS 屬性。如果只需要將 font-weight 轉換為 font-variation-settings，可以改為傳遞布林值。\",\n\t\t\"設定字型變化。可以是布林值，以啟用/停用從 font-weight 到 font-variation-settings 的轉換，或是字串，做為 CSS \\'font-variation-settings\\' 屬性的值。\",\n\t\t\"控制字型大小 (像素)。\",\n\t\t\"只允許「一般」及「粗體」關鍵字，或介於 1 到 1000 之間的數值。\",\n\t\t\"控制字型粗細。接受「一般」及「粗體」關鍵字，或介於 1 到 1000 之間的數值。\",\n\t\t\"顯示結果的預覽檢視 (預設)\",\n\t\t\"移至主要結果並顯示預覽檢視\",\n\t\t\"前往主要結果，並對其他人啟用無預覽瀏覽\",\n\t\t\"此設定已淘汰，請改用 \\'editor.editor.gotoLocation.multipleDefinitions\\' 或 \\'editor.editor.gotoLocation.multipleImplementations\\' 等單獨設定。\",\n\t\t\"控制 \\'Go to Definition\\' 命令在有多個目標位置存在時的行為。\",\n\t\t\"控制 \\'Go to Type Definition\\' 命令在有多個目標位置存在時的行為。\",\n\t\t\"控制 \\'Go to Declaration\\' 命令在有多個目標位置存在時的行為。\",\n\t\t\"控制 \\'Go to Implementations\\' 命令在有多個目標位置存在時的行為。\",\n\t\t\"控制 \\'Go to References\\' 命令在有多個目標位置存在時的行為。\",\n\t\t\"當 \\'Go to Definition\\' 的結果為目前位置時，正在執行的替代命令識別碼。\",\n\t\t\"當 \\'Go to Type Definition\\' 的結果為目前位置時，正在執行的替代命令識別碼。\",\n\t\t\"當 \\'Go to Declaration\\' 的結果為目前位置時，正在執行的替代命令識別碼。\",\n\t\t\"當 \\'Go to Implementation\\' 的結果為目前位置時，正在執行的替代命令識別碼。\",\n\t\t\"當 \\'Go to Reference\\' 的結果為目前位置時，正在執行的替代命令識別碼。\",\n\t\t\"控制是否顯示暫留。\",\n\t\t\"控制暫留顯示的延遲時間 (以毫秒為單位)。\",\n\t\t\"控制當滑鼠移過時，是否應保持顯示暫留。\",\n\t\t\"控制暫留隱藏的延遲時間 (以毫秒為單位)。需要啟用 `editor.hover.sticky`。\",\n\t\t\"如果有空間，則偏好在行上方顯示游標。\",\n\t\t\"假設所有字元的寬度均相同。這是一種快速的演算法，適用於等寬字型，以及字符寬度相同的部分指令碼 (例如拉丁文字元)。\",\n\t\t\"將外圍點計算委派給瀏覽器。這是緩慢的演算法，如果檔案較大可能會導致凍結，但在所有情況下都正常運作。\",\n\t\t\"控制計算外圍點的演算法。請注意，在協助工具模式中，會使用進階來獲得最佳體驗。\",\n\t\t\"在編輯器中啟用程式碼動作燈泡。\",\n\t\t\"請勿顯示 AI 圖示。\",\n\t\t\"當程式碼動作選單包含 AI 動作，但僅在程式碼上顯示 AI 圖示。\",\n\t\t\"當程式碼動作選單包含 AI 動作時，在程式碼和空行上顯示 AI 圖示。\",\n\t\t\"當程式碼動作選單包含 AI 作業時，將 AI 圖示與燈泡一起顯示。\",\n\t\t\"在編輯器頂端捲動期間顯示巢狀的目前範圍。\",\n\t\t\"定義要顯示的自黏線數目上限。\",\n\t\t\"定義要用於判斷要黏住的線條的模型。如果大綱模型不存在，則會回到摺疊提供者模型，其會回到縮排模型。這三種情況中會遵守此順序。\",\n\t\t\"使用編輯器的水平捲軸，啟用自黏捲動的捲動。\",\n\t\t\"啟用編輯器中的內嵌提示。\",\n\t\t\"已啟用內嵌提示\",\n\t\t\"預設會顯示內嵌提示，並在按住 {0} 時隱藏\",\n\t\t\"預設會隱藏內嵌提示，並在按住 {0} 時顯示\",\n\t\t\"已停用內嵌提示\",\n\t\t\"控制編輯器中內嵌提示的字型大小。當設定的值小於 {1} 或大於編輯器字型大小時，則會使用{0} 預設值。\",\n\t\t\"控制編輯器中，內嵌提示的字型家族。設定為空白時，則會使用 {0}。\",\n\t\t\"在編輯器中啟用的內嵌提示周圍的填補。\",\n\t\t\"控制行高。\\r\\n - 使用 0 從字型大小自動計算行高。\\r\\n - 使用介於 0 和 8 之間的值作為字型大小的乘數。\\r\\n - 大於或等於 8 的值將用來作為有效值。\",\n\t\t\"控制是否會顯示縮圖\",\n\t\t\"控制是否會自動隱藏縮圖。\",\n\t\t\"縮圖大小與編輯器內容相同 (且可能會捲動)。\",\n\t\t\"縮圖會視需要伸縮，以填滿該編輯器的高度 (無捲動)。\",\n\t\t\"縮圖將視需要縮小，一律不會大於該編輯器 (無捲動)。\",\n\t\t\"控制縮圖的大小。\",\n\t\t\"控制要在哪端呈現縮圖。\",\n\t\t\"控制何時顯示迷你地圖滑桿。\",\n\t\t\"縮圖內所繪製的內容大小: 1、2 或 3。\",\n\t\t\"顯示行中的實際字元，而不是色彩區塊。\",\n\t\t\"限制縮圖的寬度，最多顯示某個數目的列。\",\n\t\t\"控制編輯器上邊緣與第一行之間的空格數。\",\n\t\t\"控制編輯器下邊緣與最後一行之間的空格數。\",\n\t\t\"啟用快顯，在您鍵入的同時顯示參數文件和類型資訊。\",\n\t\t\"控制提示功能表是否在清單結尾時循環或關閉。\",\n\t\t\"快速建議會顯示在建議小工具內\",\n\t\t\"快速建議會顯示為浮水印文字\",\n\t\t\"已停用快速建議\",\n\t\t\"允許在字串內顯示即時建議。\",\n\t\t\"允許在註解中顯示即時建議。\",\n\t\t\"允許在字串與註解以外之處顯示即時建議。\",\n\t\t\"控制輸入時是否應自動顯示建議。這可控制在註解、字串及其他程式碼中的輸入。可設定快速建議以隱形浮出文字或建議小工具顯示。另外也請注意 \\'{0}\\'-設定，其會控制建議是否由特殊字元所觸發。\",\n\t\t\"不顯示行號。\",\n\t\t\"行號以絕對值顯示。\",\n\t\t\"行號以目前游標的相對值顯示。\",\n\t\t\"每 10 行顯示行號。\",\n\t\t\"控制行號的顯示。\",\n\t\t\"這個編輯器尺規會轉譯的等寬字元數。\",\n\t\t\"此編輯器尺規的色彩。\",\n\t\t\"在某個數目的等寬字元之後顯示垂直尺規。如有多個尺規，就會使用多個值。若陣列空白，就不會繪製任何尺規。\",\n\t\t\"垂直捲軸只有在必要時才可見。\",\n\t\t\"垂直捲軸永遠可見。\",\n\t\t\"垂直捲軸永遠隱藏。\",\n\t\t\"控制項垂直捲軸的可見度。\",\n\t\t\"水平捲軸只有在必要時才可見。\",\n\t\t\"水平捲軸永遠可見。\",\n\t\t\"水平捲軸永遠隱藏。\",\n\t\t\"控制項水平捲軸的可見度。\",\n\t\t\"垂直捲軸的寬度。\",\n\t\t\"水平捲軸的高度。\",\n\t\t\"控制項按一下是否按頁面滾動或跳到按一下位置。\",\n\t\t\"設定時，水平捲軸不會增加編輯器內容的大小。\",\n\t\t\"控制是否醒目提示所有非基本的 ASCII 字元。只有介於 U+0020和 U+007E、tab、換行和歸位字元之間的字元會視為基本 ASCII。\",\n\t\t\"控制是否只保留空格或完全沒有寬度之字元的醒目提示。\",\n\t\t\"控制是否醒目提示與基本 ASCII 字元混淆的字元，但目前使用者地區設定中通用的字元除外。\",\n\t\t\"控制註解中的字元是否也應受到 Unicode 醒目提示。\",\n\t\t\"控制字串中的字元是否也應受到 Unicode 醒目提示。\",\n\t\t\"定義未醒目提示的允許字元。\",\n\t\t\"不會將允許地區設置中常見的 Unicode 字元強調顯示。\",\n\t\t\"控制是否要在編輯器中自動顯示內嵌建議。\",\n\t\t\"每當顯示內嵌建議時，顯示內嵌建議工具列。\",\n\t\t\"每當游標停留在內嵌建議上方時，顯示內嵌建議工具列。\",\n\t\t\"永不顯示內嵌建議工具列。\",\n\t\t\"控制何時顯示內嵌建議工具列。\",\n\t\t\"控制內嵌建議如何與建議小工具互動。如果啟用，有可用的內嵌建議時，不會自動顯示建議小工具。\",\n\t\t\"控制是否啟用成對方括弧著色。使用 {0} 覆寫括弧亮顯顏色。\",\n\t\t\"控制每個括弧類型是否有自己的獨立色彩集區。\",\n\t\t\"啟用括弧配對輔助線。\",\n\t\t\"只啟用使用中括弧組的括弧配對輔助線。\",\n\t\t\"停用括弧配對輔助線。\",\n\t\t\"控制是否啟用成對方括弧指南。\",\n\t\t\"啟用水平輔助線作為垂直括弧配對輔助線的新增功能。\",\n\t\t\"只啟用使用中括弧配對的水平輔助線。\",\n\t\t\"停用水平括弧配對輔助線。\",\n\t\t\"控制是否啟用水平成對方括弧輔助線。\",\n\t\t\"控制編輯器是否應醒目提示使用中的成對括弧。\",\n\t\t\"控制編輯器是否應顯示縮排輔助線。\",\n\t\t\"醒目提示使用中的縮排輔助線。\",\n\t\t\"即使醒目提示括弧輔助線，仍醒目提示使用中的縮排輔助線。\",\n\t\t\"不要醒目提示使用中的縮排輔助線。\",\n\t\t\"控制編輯器是否應醒目提示使用中的縮排輔助線。\",\n\t\t\"插入建議而不覆寫游標旁的文字。\",\n\t\t\"插入建議並覆寫游標旁的文字。\",\n\t\t\"控制是否要在接受完成時覆寫字組。請注意，這取決於加入此功能的延伸模組。\",\n\t\t\"控制對於拚錯字是否進行篩選和排序其建議\",\n\t\t\"控制排序是否偏好游標附近的字組。\",\n\t\t\"控制記錄的建議選取項目是否在多個工作區和視窗間共用 (需要 `#editor.suggestSelection#`)。\",\n\t\t\"自動觸發 IntelliSense 時一律選取建議。\",\n\t\t\"自動觸發 IntelliSense 時永不選取建議。\",\n\t\t\"只有在從觸發字元觸發 IntelliSense 時，才選取建議。\",\n\t\t\"只有在您輸入時觸發 IntelliSense 時，才選取建議。\",\n\t\t\"控制小工具顯示時是否選取建議。請注意，這只適用於(\\'#editor.quickSuggestions#\\' 和 \\'#editor.suggestOnTriggerCharacters#\\') 自動觸發的建議，而且一律會在明確叫用時選取建議，例如透過 \\'Ctrl+Space\\'。\",\n\t\t\"控制正在使用的程式碼片段是否會避免快速建議。\",\n\t\t\"控制要在建議中顯示或隱藏圖示。\",\n\t\t\"控制建議小工具底下的狀態列可見度。\",\n\t\t\"控制是否要在編輯器中預覽建議結果。\",\n\t\t\"控制建議詳細資料是以內嵌於標籤的方式顯示，還是只在詳細資料小工具中顯示。\",\n\t\t\"此設定已淘汰。建議小工具現可調整大小。\",\n\t\t\"此設定已淘汰，請改用 \\'editor.suggest.showKeywords\\' 或 \\'editor.suggest.showSnippets\\' 等單獨設定。\",\n\t\t\"啟用時，IntelliSense 顯示「方法」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「函式」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「建構函式」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「已取代」建議。\",\n\t\t\"啟用時，IntelliSense 篩選會要求第一個字元符合文字開頭，例如 `Console` 或 `WebCoNtext` 上的 `c`，但不是 `description` 上的 _not_。停用時，IntelliSense 會顯示更多結果，但仍會依相符品質排序結果。\",\n\t\t\"啟用時，IntelliSense 顯示「欄位」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「變數」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「類別」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「結構」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「介面」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「模組」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「屬性」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「事件」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「運算子」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「單位」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「值」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「常數」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「列舉」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「enumMember」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「關鍵字」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「文字」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「色彩」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「檔案」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「參考」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「customcolor」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「資料夾」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「typeParameter」建議。\",\n\t\t\"啟用時，IntelliSense 顯示「程式碼片段」建議。\",\n\t\t\"啟用之後，IntelliSense 會顯示 `user`-suggestions。\",\n\t\t\"啟用時，IntelliSense 會顯示 `issues`-suggestions。\",\n\t\t\"是否應一律選取前置和後置的空白字元。\",\n\t\t\"是否應該選取子詞 (例如 \\'fooBar\\' 或 \\'foo_bar\\' 中的 \\'foo\\')。\",\n\t\t\"無縮排。換行從第 1 列開始。\",\n\t\t\"換行的縮排會與父行相同。\",\n\t\t\"換行的縮排為父行 +1。\",\n\t\t\"換行縮排為父行 +2。\",\n\t\t\"控制換行的縮排。\",\n\t\t\"控制您是否可以按住 `Shift` 鍵，將檔案拖放到文字編輯器中 (而非在編輯器中開啟檔案)。\",\n\t\t\"控制將檔案放入編輯器時是否顯示小工具。此小工具可讓您控制檔案的置放方式。\",\n\t\t\"將檔案放入編輯器後顯示置放選取器小工具。\",\n\t\t\"永不顯示置放選取器小工具。改為一律使用預設置放提供者。\",\n\t\t\"控制是否可以以不同方式貼上內容。\",\n\t\t\"控制將內容貼上至編輯器時是否顯示小工具。此小工具可讓您控制檔案的貼上方式。\",\n\t\t\"將內容貼上編輯器後顯示貼上選取器小工具。\",\n\t\t\"永不顯示貼上選取器小工具。而是一律使用預設的貼上行為。\",\n\t\t\"控制是否透過提交字元接受建議。例如在 JavaScript 中，分號 (\\';\\') 可以是接受建議並鍵入該字元的提交字元。\",\n\t\t\"在建議進行文字變更時，僅透過 `Enter` 接受建議。\",\n\t\t\"控制除了 \\'Tab\\' 外，是否也透過 \\'Enter\\' 接受建議。這有助於避免混淆要插入新行或接受建議。\",\n\t\t\"控制編輯器中可一次由螢幕助讀程式讀出的行數。偵測到螢幕助讀程式時會自動預設為 500。警告: 若數字超過預設，可能會對效能有所影響。\",\n\t\t\"編輯器內容\",\n\t\t\"控制螢幕助讀程式是否宣告內嵌建議。\",\n\t\t\"使用語言配置確定何時自動關閉括號。\",\n\t\t\"僅當游標位於空白的左側時自動關閉括號。\",\n\t\t\"控制編輯器是否應在使用者新增左括弧後，自動加上右括弧。\",\n\t\t\"使用語言配置確定何時自動關閉註解。\",\n\t\t\"僅當游標位於空白的左側時自動關閉註解。\",\n\t\t\"控制使用者新增開啟的註解之後，編輯器是否應該自動關閉註解。\",\n\t\t\"僅在自動插入相鄰的右引號或括弧時，才將其移除。\",\n\t\t\"控制編輯器是否應在刪除時移除相鄰的右引號或括弧。\",\n\t\t\"僅在自動插入右引號或括號時，才在其上方鍵入。\",\n\t\t\"控制編輯器是否應在右引號或括號上鍵入。\",\n\t\t\"使用語言配置確定何時自動關閉引號。\",\n\t\t\"僅當游標位於空白的左側時自動關閉引號。\",\n\t\t\"控制編輯器是否應在使用者新增開始引號後，自動加上關閉引號。\",\n\t\t\"編輯器不會自動插入縮排。\",\n\t\t\"編輯器會保留目前行的縮排。\",\n\t\t\"編輯器會保留目前行的縮排並接受語言定義的括號。\",\n\t\t\"編輯器會目前行的縮排、接受語言定義的括號並叫用語言定義的特殊 onEnterRules。\",\n\t\t\"編輯器會保留目前行的縮排、接受語言定義的括號並叫用語言定義的特殊 onEnterRules 並接受語言定義的 indentationRules。\",\n\t\t\"控制編輯器是否應在使用者鍵入、貼上、移動或縮排行時自動調整縮排。\",\n\t\t\"使用語言組態來決定何時自動環繞選取項目。\",\n\t\t\"用引號括住，而非使用括弧。\",\n\t\t\"用括弧括住，而非使用引號。 \",\n\t\t\"控制編輯器是否應在鍵入引號或括弧時自動包圍選取範圍。\",\n\t\t\"當使用空格進行縮排時，會模擬定位字元的選取表現方式。選取範圍會依循定位停駐點。\",\n\t\t\"控制編輯器是否顯示 codelens。\",\n\t\t\"控制 CodeLens 的字型家族。\",\n\t\t\"控制 CodeLens 的字型大小 (像素)。設定為 0 時，會使用 90% 的 `#editor.fontSize#`。\",\n\t\t\"控制編輯器是否應轉譯內嵌色彩裝飾項目與色彩選擇器。\",\n\t\t\"讓色彩選擇器在按一下和停駐色彩在裝飾項目上時出現\",\n\t\t\"讓色彩選擇器在停駐色彩裝飾項目時出現\",\n\t\t\"讓色彩選擇器在按一下色彩裝飾項目時出現\",\n\t\t\"控制條件，讓色彩選擇器從色彩裝飾項目出現\",\n\t\t\"控制一次可在編輯器中呈現的色彩裝飾項目最大數目。\",\n\t\t\"啟用即可以滑鼠與按鍵選取進行資料行選取。\",\n\t\t\"控制語法醒目提示是否應複製到剪貼簿。\",\n\t\t\"控制資料指標動畫樣式。\",\n\t\t\"平滑插入號動畫已停用。\",\n\t\t\"只有當使用者使用明確手勢移動游標時，才會啟用平滑插入號動畫。\",\n\t\t\"永遠啟用平滑插入號動畫。\",\n\t\t\"控制是否應啟用平滑插入點動畫。 \",\n\t\t\"控制資料指標樣式。\",\n\t\t\"控制游標上下周圍可顯示的前置線 (最小為 0) 和後置線 (最小為 1) 的最小數目。在某些編輯器中稱為 \\'scrollOff\\' 或 \\'scrollOffset\\'。\",\n\t\t\"只有通過鍵盤或 API 觸發時，才會施行 `cursorSurroundingLines`。\",\n\t\t\"一律強制執行 `cursorSurroundingLines`\",\n\t\t\"控制應強制執行 `#cursorSurroundingLines#` 的時機。\",\n\t\t\"控制游標寬度，當 `#editor.cursorStyle#` 設定為 `line` 時。\",\n\t\t\"控制編輯器是否允許透過拖放來移動選取項目。\",\n\t\t\"使用新的 svg 轉譯方法。\",\n\t\t\"使用具有字型字元的新轉譯方法。\",\n\t\t\"使用穩定轉譯方法。\",\n\t\t\"控制是否使用新的實驗性方法來呈現空白字元。\",\n\t\t\"按下 `Alt` 時的捲動速度乘數。\",\n\t\t\"控制編輯器是否啟用程式碼摺疊功能。\",\n\t\t\"使用語言特定摺疊策略 (如果可用)，否則使用縮排式策略。\",\n\t\t\"使用縮排式摺疊策略。\",\n\t\t\"控制計算資料夾範圍的策略。\",\n\t\t\"控制編輯器是否應將折疊的範圍醒目提示。\",\n\t\t\"控制編輯器是否會自動摺疊匯入範圍。\",\n\t\t\"可摺疊區域的數目上限。增加此值可能會造成當目前的來源有大量可摺疊區域時，編輯器的回應速度變慢。\",\n\t\t\"控制按一下已折疊行後方的空白內容是否會展開行。\",\n\t\t\"控制字型家族。\",\n\t\t\"控制編輯器是否應自動為貼上的內容設定格式。必須有可用的格式器，而且格式器應能夠為文件中的一個範圍設定格式。\",\n\t\t\"控制編輯器是否應自動在鍵入後設定行的格式。\",\n\t\t\"控制編輯器是否應轉譯垂直字符邊界。字符邊界最常用來進行偵錯。\",\n\t\t\"控制游標是否應隱藏在概觀尺規中。\",\n\t\t\"控制字母間距 (像素)。\",\n\t\t\"控制編輯器是否已啟用連結編輯。相關符號 (例如 HTML 標籤) 會根據語言在編輯時更新。\",\n\t\t\"控制編輯器是否應偵測連結並使其可供點選。\",\n\t\t\"將符合的括號醒目提示。\",\n\t\t\"要用於滑鼠滾輪捲動事件 `deltaX` 和 `deltaY` 的乘數。\",\n\t\t\"使用滑鼠滾輪並按住 `Ctrl` 時，縮放編輯器的字型\",\n\t\t\"在多個游標重疊時將其合併。\",\n\t\t\"對應Windows和Linux的\\'Control\\'與對應 macOS 的\\'Command\\'。\",\n\t\t\"對應Windows和Linux的\\'Alt\\'與對應macOS的\\'Option\\'。\",\n\t\t\"用於在滑鼠新增多個游標的修飾元。[移至定義] 和 [開啟連結] 滑鼠手勢會加以適應，以避免與 [多個游標的修飾元](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier) 相衝突。\",\n\t\t\"每個游標都會貼上一行文字。\",\n\t\t\"每個游標都會貼上全文。\",\n\t\t\"當已貼上文字的行數與游標數相符時控制貼上功能。\",\n\t\t\"控制一次可在作用中編輯器中的游標數目上限。\",\n\t\t\"不強調顯示出現項目。\",\n\t\t\"僅強調顯示目前檔案中的出現項目。\",\n\t\t\"實驗: 跨所有有效的開啟檔案強調顯示出現項目。\",\n\t\t\"控制是否應跨開啟的檔案強調顯示出現項目。\",\n\t\t\"控制是否應在概觀尺規周圍繪製框線。\",\n\t\t\"開啟預覽時焦點樹狀\",\n\t\t\"開啟時聚焦編輯器\",\n\t\t\"控制要聚焦內嵌編輯器或預覽小工具中的樹系。\",\n\t\t\"控制「前往定義」滑鼠手勢，是否一律開啟瞄核小工具。\",\n\t\t\"控制在快速建議顯示後的延遲 (以毫秒為單位)。\",\n\t\t\"控制編輯器是否會自動依類型重新命名。\",\n\t\t\"已淘汰，請改用 `editor.linkedEditing`。\",\n\t\t\"控制編輯器是否應顯示控制字元。\",\n\t\t\"在檔案結尾為新行時，呈現最後一行的號碼。\",\n\t\t\"醒目提示裝訂邊和目前的行。\",\n\t\t\"控制編輯器如何顯示目前行的醒目提示。\",\n\t\t\"控制當聚焦於編輯器時，編輯器是否應僅轉譯目前行的醒目提示。\",\n\t\t\"轉譯空白字元，但文字之間的單一空格除外。\",\n\t\t\"只轉譯所選文字的空白字元。\",\n\t\t\"只轉譯結尾空白字元。\",\n\t\t\"控制編輯器應如何轉譯空白字元。\",\n\t\t\"控制選取範圍是否有圓角\",\n\t\t\"控制編輯器水平捲動的額外字元數。\",\n\t\t\"控制編輯器是否捲動到最後一行之外。\",\n\t\t\"同時進行垂直與水平捲動時，僅沿主軸捲動。避免在軌跡板上進行垂直捲動時發生水平漂移。\",\n\t\t\"控制是否支援 Linux 主要剪貼簿。\",\n\t\t\"控制編輯器是否應醒目提示與選取項目類似的相符項目。\",\n\t\t\"一律顯示摺疊控制項。\",\n\t\t\"永不顯示摺疊控制項與減少裝訂邊大小。\",\n\t\t\"僅當滑鼠懸停在活動列上時，才顯示折疊功能。\",\n\t\t\"控制摺疊控制項在裝訂邊上的顯示時機。\",\n\t\t\"控制未使用程式碼的淡出。\",\n\t\t\"控制已刪除的淘汰變數。\",\n\t\t\"將程式碼片段建議顯示於其他建議的頂端。\",\n\t\t\"將程式碼片段建議顯示於其他建議的下方。\",\n\t\t\"將程式碼片段建議與其他建議一同顯示。\",\n\t\t\"不顯示程式碼片段建議。\",\n\t\t\"控制程式碼片段是否隨其他建議顯示，以及其排序方式。\",\n\t\t\"控制編輯器是否會使用動畫捲動\",\n\t\t\"控制當顯示內嵌完成時，是否應提供協助工具提示給螢幕助讀程式使用者。\",\n\t\t\"建議小工具的字型大小。當設定為 {0} 時，則會使用 {1} 的值。\",\n\t\t\"建議小工具的行高。當設定為 {0} 時，則會使用 {1} 的值。最小值為 8。\",\n\t\t\"控制建議是否應在鍵入觸發字元時自動顯示。\",\n\t\t\"一律選取第一個建議。\",\n\t\t\"除非進一步鍵入選取了建議，否則選取最近的建議，例如 `console.| -> console.log`，原因是最近完成了 `log`。\",\n\t\t\"根據先前已完成該建議的前置詞選取建議，例如 `co -> console` 和 `con -> const`。\",\n\t\t\"控制在顯示建議清單時如何預先選取建議。\",\n\t\t\"按 Tab 時，Tab 完成會插入最符合的建議。\",\n\t\t\"停用 tab 鍵自動完成。\",\n\t\t\"在程式碼片段的首碼相符時使用 Tab 完成。未啟用 \\'quickSuggestions\\' 時效果最佳。\",\n\t\t\"啟用 tab 鍵自動完成。\",\n\t\t\"自動移除異常的行結束字元。\",\n\t\t\"忽略異常的行結束字元。\",\n\t\t\"要移除之異常的行結束字元提示。\",\n\t\t\"移除可能導致問題的異常行結束字元。\",\n\t\t\"插入和刪除接在定位停駐點後的空白字元。\",\n\t\t\"使用預設的分行符號規則。\",\n\t\t\"中文/日文/韓文 (CJK) 文字不應該使用斷字。非中日韓的文字行為與一般文字相同。\",\n\t\t\"控制用於中文/日文/韓文 (CJK) 文字的斷字規則。\",\n\t\t\"在執行文字相關導覽或作業時要用作文字分隔符號的字元\",\n\t\t\"一律不換行。\",\n\t\t\"依檢視區寬度換行。\",\n\t\t\"於 \\'#editor.wordWrapColumn#\\' 換行。\",\n\t\t\"當檢視區縮至最小並設定 \\'#editor.wordWrapColumn#\\' 時換行。\",\n\t\t\"控制如何換行。\",\n\t\t\"當 `#editor.wordWrap#` 為 `wordWrapColumn` 或 `bounded` 時，控制編輯器中的資料行換行。\",\n\t\t\"控制是否應使用預設的文件色彩提供者顯示內嵌色彩裝飾\",\n\t\t\"控制編輯器是否接收索引標籤，或將其延遲至工作台進行流覽。\",\n\t],\n\t\"vs/editor/common/core/editorColorRegistry\": [\n\t\t\"目前游標位置行的反白顯示背景色彩。\",\n\t\t\"目前游標位置行之周圍框線的背景色彩。\",\n\t\t\"醒目提示範圍的背景色彩，例如快速開啟並尋找功能。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"反白顯示範圍周圍邊框的背景顏色。\",\n\t\t\"醒目提示符號的背景色彩，相似於前往下一個定義或前往下一個/上一個符號。色彩必須透明，以免隱藏底層裝飾。\",\n\t\t\"醒目提示周圍的邊界背景色彩。\",\n\t\t\"編輯器游標的色彩。\",\n\t\t\"編輯器游標的背景色彩。允許自訂區塊游標重疊的字元色彩。\",\n\t\t\"編輯器中空白字元的色彩。\",\n\t\t\"編輯器行號的色彩。\",\n\t\t\"編輯器縮排輔助線的色彩。\",\n\t\t\"\\'editorIndentGuide.background\\' 已被取代。請改用 \\'editorIndentGuide.background1\\'。\",\n\t\t\"使用中編輯器縮排輔助線的色彩。\",\n\t\t\"\\'editorIndentGuide.activeBackground\\' 已被取代。請改用 \\'editorIndentGuide.activeBackground1\\'。\",\n\t\t\"編輯器縮排輔助線的色彩 (1)。\",\n\t\t\"編輯器縮排輔助線的色彩 (2)。\",\n\t\t\"編輯器縮排輔助線的色彩 (3)。\",\n\t\t\"編輯器縮排輔助線的色彩 (4)。\",\n\t\t\"編輯器縮排輔助線的色彩 (5)。\",\n\t\t\"編輯器縮排輔助線的色彩 (6)。\",\n\t\t\"使用中編輯器縮排輔助線的色彩 (1)。\",\n\t\t\"使用中編輯器縮排輔助線的色彩 (2)。\",\n\t\t\"使用中編輯器縮排輔助線的色彩 (3)。\",\n\t\t\"使用中編輯器縮排輔助線的色彩 (4)。\",\n\t\t\"使用中編輯器縮排輔助線的色彩 (5)。\",\n\t\t\"使用中編輯器縮排輔助線的色彩 (6)。\",\n\t\t\"編輯器使用中行號的色彩\",\n\t\t\"Id 已取代。請改用 \\'editorLineNumber.activeForeground\\' 。\",\n\t\t\"編輯器使用中行號的色彩\",\n\t\t\"editor.renderFinalNewline 設定為暗灰色時，最終編輯器線條的色彩。\",\n\t\t\"編輯器尺規的色彩\",\n\t\t\"編輯器程式碼濾鏡的前景色彩\",\n\t\t\"成對括號背景色彩\",\n\t\t\"成對括號邊框色彩\",\n\t\t\"預覽檢視編輯器尺規的邊框色彩.\",\n\t\t\"編輯器概觀尺規的背景色彩。\",\n\t\t\"編輯器邊框的背景顏色,包含行號與字形圖示的邊框.\",\n\t\t\"編輯器中不必要 (未使用) 原始程式碼的框線色彩。\",\n\t\t\"編輯器中不必要 (未使用) 原始程式碼的不透明度。例如 \\\"#000000c0” 會以 75% 的不透明度轉譯程式碼。針對高對比主題，使用 \\'editorUnnecessaryCode.border\\' 主題色彩可為不必要的程式碼加上底線，而不是將其變淡。\",\n\t\t\"編輯器中浮水印文字的邊框色彩。\",\n\t\t\"編輯器中浮水印文字的前景色彩。\",\n\t\t\"編輯器中浮水印文字的背景色彩。\",\n\t\t\"範圍醒目提示的概觀尺規標記色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"錯誤的概觀尺規標記色彩。\",\n\t\t\"警示的概觀尺規標記色彩。\",\n\t\t\"資訊的概觀尺規標記色彩。\",\n\t\t\"括弧 (1) 的前景色彩。需要啟用成對方括弧著色。\",\n\t\t\"括弧 (2) 的前景色彩。需要啟用成對方括弧著色。\",\n\t\t\"括弧 (3) 的前景色彩。需要啟用成對方括弧著色。\",\n\t\t\"括弧 (4) 的前景色彩。需要啟用成對方括弧著色。\",\n\t\t\"括弧 (5) 的前景色彩。需要啟用成對方括弧著色。\",\n\t\t\"括弧 (6) 的前景色彩。需要啟用成對方括弧著色。\",\n\t\t\"未預期括弧的前景色彩。\",\n\t\t\"非使用中括弧配對輔助線 (1) 的背景色彩。需要啟用括弧配對輔助線。\",\n\t\t\"非使用中括弧配對輔助線 (2) 的背景色彩。需要啟用括弧配對輔助線。\",\n\t\t\"非使用中括弧配對輔助線 (3) 的背景色彩。需要啟用括弧配對輔助線。\",\n\t\t\"非使用中括弧配對輔助線 (4) 的背景色彩。需要啟用括弧配對輔助線。\",\n\t\t\"非使用中括弧配對輔助線 (5) 的背景色彩。需要啟用括弧配對輔助線。\",\n\t\t\"非使用中括弧配對輔助線 (6) 的背景色彩。需要啟用括弧配對輔助線。\",\n\t\t\"使用中括弧配對輔助線 (1) 的背景色彩。需要啟用括弧配對輔助線。\",\n\t\t\"使用中括弧配對輔助線 (2) 的背景色彩。需要啟用括弧配對輔助線。\",\n\t\t\"使用中括弧配對輔助線 (3) 的背景色彩。需要啟用括弧配對輔助線。\",\n\t\t\"使用中括弧配對輔助線 (4) 的背景色彩。需要啟用括弧配對輔助線。\",\n\t\t\"使用中括弧配對輔助線 (5) 的背景色彩。需要啟用括弧配對輔助線。\",\n\t\t\"使用中括弧配對輔助線 (6) 的背景色彩。需要啟用括弧配對輔助線。\",\n\t\t\"用來醒目提示 Unicode 字元的框線色彩。\",\n\t\t\"用來醒目提示 Unicode 字元的背景色彩。\",\n\t],\n\t\"vs/editor/common/editorContextKeys\": [\n\t\t\"編輯器文字是否有焦點 (游標閃爍)\",\n\t\t\"編輯器或編輯器小工具是否有焦點 (例如焦點位於 [尋找] 小工具中)\",\n\t\t\"編輯器或 RTF 輸入是否有焦點 (游標閃爍)\",\n\t\t\"編輯器是否為唯讀\",\n\t\t\"內容是否為 Diff 編輯器\",\n\t\t\"內容是否為內嵌 Diff 編輯器\",\n\t\t\"內容是否為 Diff 編輯器\",\n\t\t\"是否摺疊多重 Diff 編輯器中的所有檔案\",\n\t\t\"Diff 編輯器是否有變更\",\n\t\t\"是否選取移動的程式碼區塊進行比較\",\n\t\t\"是否顯示易存取差異檢視器\",\n\t\t\"是否已達到差異編輯器並排呈現內嵌中斷點\",\n\t\t\"\\'editor.columnSelection\\' 是否已啟用\",\n\t\t\"編輯器是否有選取文字\",\n\t\t\"編輯器是否有多個選取項目\",\n\t\t\"\\'Tab\\' 是否會將焦點移出編輯器\",\n\t\t\"編輯器暫留是否顯示\",\n\t\t\"編輯器暫留是否聚焦\",\n\t\t\"自黏捲動是否聚焦\",\n\t\t\"自黏捲動是否顯示\",\n\t\t\"是否顯示獨立的顏色選擇器\",\n\t\t\"獨立的顏色選擇器是否聚焦\",\n\t\t\"編輯器是否為較大編輯器的一部分 (例如筆記本)\",\n\t\t\"編輯器的語言識別碼\",\n\t\t\"編輯器是否有完成項目提供者\",\n\t\t\"編輯器是否有程式碼動作提供者\",\n\t\t\"編輯器是否有 CodeLens 提供者\",\n\t\t\"編輯器是否有定義提供者\",\n\t\t\"編輯器是否有宣告提供者\",\n\t\t\"編輯器是否有實作提供者\",\n\t\t\"編輯器是否有型別定義提供者\",\n\t\t\"編輯器是否有暫留提供者\",\n\t\t\"編輯器是否有文件醒目提示提供者\",\n\t\t\"編輯器是否有文件符號提供者\",\n\t\t\"編輯器是否有參考提供者\",\n\t\t\"編輯器是否有重新命名提供者\",\n\t\t\"編輯器是否有簽章說明提供者\",\n\t\t\"編輯器是否有內嵌提示提供者\",\n\t\t\"編輯器是否有文件格式化提供者\",\n\t\t\"編輯器是否有文件選取項目格式化提供者\",\n\t\t\"編輯器是否有多個文件格式化提供者\",\n\t\t\"編輯器是否有多個文件選取項目格式化提供者\",\n\t],\n\t\"vs/editor/common/languages\": [\n\t\t\"陣列\",\n\t\t\"布林值\",\n\t\t\"類別\",\n\t\t\"常數\",\n\t\t\"建構函式\",\n\t\t\"列舉\",\n\t\t\"列舉成員\",\n\t\t\"事件\",\n\t\t\"欄位\",\n\t\t\"檔案\",\n\t\t\"函式\",\n\t\t\"介面\",\n\t\t\"索引鍵\",\n\t\t\"方法\",\n\t\t\"模組\",\n\t\t\"命名空間\",\n\t\t\"null\",\n\t\t\"數字\",\n\t\t\"物件\",\n\t\t\"運算子\",\n\t\t\"套件\",\n\t\t\"屬性\",\n\t\t\"字串\",\n\t\t\"結構\",\n\t\t\"型別參數\",\n\t\t\"變數\",\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/editor/common/languages/modesRegistry\": [\n\t\t\"純文字\",\n\t],\n\t\"vs/editor/common/model/editStack\": [\n\t\t\"正在鍵入\",\n\t],\n\t\"vs/editor/common/standaloneStrings\": [\n\t\t\"開發人員: 檢查權杖\",\n\t\t\"前往行/欄...\",\n\t\t\"顯示所有快速存取提供者\",\n\t\t\"命令選擇區\",\n\t\t\"顯示並執行命令\",\n\t\t\"移至符號...\",\n\t\t\"前往符號 (依類別)...\",\n\t\t\"編輯器內容\",\n\t\t\"按 Alt+F1 可取得協助工具選項。\",\n\t\t\"切換高對比佈景主題\",\n\t\t\"已在 {1} 檔案中進行 {0} 項編輯\",\n\t],\n\t\"vs/editor/common/viewLayout/viewLineRenderer\": [\n\t\t\"顯示更多 ({0})\",\n\t\t\"{0} chars\",\n\t],\n\t\"vs/editor/contrib/anchorSelect/browser/anchorSelect\": [\n\t\t\"選取範圍錨點\",\n\t\t\"設定錨點為 {0}:{1}\",\n\t\t\"設定選取範圍錨點\",\n\t\t\"前往選取範圍錨點\",\n\t\t\"選取從錨點到游標之間的範圍\",\n\t\t\"取消選取範圍錨點\",\n\t],\n\t\"vs/editor/contrib/bracketMatching/browser/bracketMatching\": [\n\t\t\"成對括弧的概觀尺規標記色彩。\",\n\t\t\"移至方括弧\",\n\t\t\"選取至括弧\",\n\t\t\"移除括弧\",\n\t\t\"前往括弧(&&B)\",\n\t\t\"選取內部文字，並包含括弧或大括弧\",\n\t],\n\t\"vs/editor/contrib/caretOperations/browser/caretOperations\": [\n\t\t\"將所選文字向左移動\",\n\t\t\"將所選文字向右移動\",\n\t],\n\t\"vs/editor/contrib/caretOperations/browser/transpose\": [\n\t\t\"調換字母\",\n\t],\n\t\"vs/editor/contrib/clipboard/browser/clipboard\": [\n\t\t\"剪下(&&T)\",\n\t\t\"剪下\",\n\t\t\"剪下\",\n\t\t\"剪下\",\n\t\t\"複製(&&C)\",\n\t\t\"複製\",\n\t\t\"複製\",\n\t\t\"複製\",\n\t\t\"複製為\",\n\t\t\"複製為\",\n\t\t\"共用\",\n\t\t\"共用\",\n\t\t\"共用\",\n\t\t\"貼上(&&P)\",\n\t\t\"貼上\",\n\t\t\"貼上\",\n\t\t\"貼上\",\n\t\t\"隨語法醒目提示複製\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeAction\": [\n\t\t\"套用程式碼動作時發生未知的錯誤\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionCommands\": [\n\t\t\"要執行程式碼動作的種類。\",\n\t\t\"控制要套用傳回動作的時機。\",\n\t\t\"一律套用第一個傳回的程式碼動作。\",\n\t\t\"如果傳回的程式碼動作是唯一動作，則加以套用。\",\n\t\t\"不要套用傳回的程式碼動作。\",\n\t\t\"控制是否僅應傳回偏好的程式碼動作。\",\n\t\t\"快速修復...\",\n\t\t\"沒有可用的程式碼操作\",\n\t\t\"沒有 \\\"{0}\\\" 的偏好程式碼動作\",\n\t\t\"沒有 \\\"{0}\\\" 可用的程式碼動作\",\n\t\t\"沒有可用的偏好程式碼動作\",\n\t\t\"沒有可用的程式碼操作\",\n\t\t\"重構...\",\n\t\t\"沒有適用於 \\'{0}\\' 的偏好重構。\",\n\t\t\"沒有可用的 \\\"{0}\\\" 重構\",\n\t\t\"沒有可用的偏好重構\",\n\t\t\"沒有可用的重構\",\n\t\t\"來源動作...\",\n\t\t\"沒有適用於 \\'{0}\\' 的偏好來源動作\",\n\t\t\"沒有 \\\"{0}\\\" 可用的來源動作\",\n\t\t\"沒有可用的偏好來源動作\",\n\t\t\"沒有可用的來源動作\",\n\t\t\"組織匯入\",\n\t\t\"沒有任何可用的組織匯入動作\",\n\t\t\"全部修正\",\n\t\t\"沒有全部修正動作可用\",\n\t\t\"自動修正...\",\n\t\t\"沒有可用的自動修正\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionContributions\": [\n\t\t\"啟用/停用在 [程式碼動作] 功能表中顯示群組標頭。\",\n\t\t\"目前不在診斷時，啟用/停用顯示行內最近的 [快速修正]。\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionController\": [\n\t\t\"內容: {0} 在行 {1} 和欄 {2}。\",\n\t\t\"隱藏已停用項目\",\n\t\t\"顯示已停用項目\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionMenu\": [\n\t\t\"更多動作...\",\n\t\t\"快速修正\",\n\t\t\"擷取\",\n\t\t\"內嵌\",\n\t\t\"重寫\",\n\t\t\"移動\",\n\t\t\"範圍陳述式\",\n\t\t\"來源動作\",\n\t],\n\t\"vs/editor/contrib/codeAction/browser/lightBulbWidget\": [\n\t\t\"顯示程式碼動作。偏好的快速修正可用 ({0})\",\n\t\t\"顯示程式碼動作 ({0})\",\n\t\t\"顯示程式碼動作\",\n\t\t\"開始內嵌聊天 ({0})\",\n\t\t\"開始內嵌聊天\",\n\t\t\"觸發 AI 動作\",\n\t],\n\t\"vs/editor/contrib/codelens/browser/codelensController\": [\n\t\t\"顯示目前行的 Code Lens 命令\",\n\t\t\"選取命令\",\n\t],\n\t\"vs/editor/contrib/colorPicker/browser/colorPickerWidget\": [\n\t\t\"按一下以切換色彩選項 (rgb/hsl/hex)\",\n\t\t\"要關閉顏色選擇器的圖示\",\n\t],\n\t\"vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions\": [\n\t\t\"顯示或聚焦獨立的顏色選擇器\",\n\t\t\"&&顯示或聚焦獨立的顏色選擇器\",\n\t\t\"隱藏顏色選擇器\",\n\t\t\"使用獨立的顏色選擇器插入顏色\",\n\t],\n\t\"vs/editor/contrib/comment/browser/comment\": [\n\t\t\"切換行註解\",\n\t\t\"切換行註解(&&T)\",\n\t\t\"加入行註解\",\n\t\t\"移除行註解\",\n\t\t\"切換區塊註解\",\n\t\t\"切換區塊註解(&&B)\",\n\t],\n\t\"vs/editor/contrib/contextmenu/browser/contextmenu\": [\n\t\t\"縮圖\",\n\t\t\"轉譯字元\",\n\t\t\"垂直大小\",\n\t\t\"按比例\",\n\t\t\"填滿\",\n\t\t\"最適大小\",\n\t\t\"滑桿\",\n\t\t\"滑鼠移至上方\",\n\t\t\"一律\",\n\t\t\"顯示編輯器內容功能表\",\n\t],\n\t\"vs/editor/contrib/cursorUndo/browser/cursorUndo\": [\n\t\t\"游標復原\",\n\t\t\"游標重做\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution\": [\n\t\t\"貼上為...\",\n\t\t\"要嘗試套用的貼上編輯的識別碼。如果未提供，編輯器將顯示選擇器。\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/copyPasteController\": [\n\t\t\"是否顯示貼上小工具\",\n\t\t\"顯示貼上選項...\",\n\t\t\"正在執行貼上處理常式。按一下以取消\",\n\t\t\"選取貼上動作\",\n\t\t\"執行貼上處理常式\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/defaultProviders\": [\n\t\t\"內建\",\n\t\t\"插入純文字\",\n\t\t\"插入 URI\",\n\t\t\"插入 URI\",\n\t\t\"插入路徑\",\n\t\t\"插入路徑\",\n\t\t\"插入相對路徑\",\n\t\t\"插入相對路徑\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution\": [\n\t\t\"設定預設卸載提供者，以用於指定 MIME 類型的內容。\",\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController\": [\n\t\t\"是否顯示卸除小工具\",\n\t\t\"顯示卸除選項...\",\n\t\t\"正在執行置放處理常式。按一下以取消\",\n\t],\n\t\"vs/editor/contrib/editorState/browser/keybindingCancellation\": [\n\t\t\"編輯器是否執行可取消的作業，例如「預覽參考」\",\n\t],\n\t\"vs/editor/contrib/find/browser/findController\": [\n\t\t\"檔案太大，無法執行取代所有作業。\",\n\t\t\"尋找\",\n\t\t\"尋找(&&F)\",\n\t\t\"覆寫 \\\"Use Regular Expression\\\" 旗標。\\r\\n日後將不會儲存此旗標。\\r\\n0: 不執行任何動作\\r\\n1: True\\r\\n2: False\",\n\t\t\"覆寫 \\\"Match Whole Word\\\" 旗標。\\r\\n日後將不會儲存此旗標。\\r\\n0: 不執行任何動作\\r\\n1: True\\r\\n2: False\",\n\t\t\"覆寫 \\\"Math Case\\\" 旗標。\\r\\n日後將不會儲存此旗標。\\r\\n0: 不執行任何動作\\r\\n1: True\\r\\n2: False\",\n\t\t\"覆寫 \\\"Preserve Case\\\" 旗標。\\r\\n日後將不會儲存此旗標。\\r\\n0: 不執行任何動作\\r\\n1: True\\r\\n2: False\",\n\t\t\"使用引數尋找\",\n\t\t\"尋找選取項目\",\n\t\t\"尋找下一個\",\n\t\t\"尋找上一個\",\n\t\t\"移至相符項目...\",\n\t\t\"沒有相符項目。嘗試搜尋其他項目。\",\n\t\t\"輸入數字以前往特定相符項目 (介於 1 到 {0})\",\n\t\t\"請輸入介於 1 和 {0} 之間的數字。\",\n\t\t\"請輸入介於 1 和 {0} 之間的數字。\",\n\t\t\"尋找下一個選取項目\",\n\t\t\"尋找上一個選取項目\",\n\t\t\"取代\",\n\t\t\"取代(&&R)\",\n\t],\n\t\"vs/editor/contrib/find/browser/findWidget\": [\n\t\t\"編輯器尋找小工具中 [在選取範圍中尋找] 的圖示。\",\n\t\t\"表示編輯器尋找小工具已摺疊的圖示。\",\n\t\t\"表示編輯器尋找小工具已展開的圖示。\",\n\t\t\"編輯器尋找小工具中 [取代] 的圖示。\",\n\t\t\"編輯器尋找小工具中 [全部取代] 的圖示。\",\n\t\t\"編輯器尋找小工具中 [尋找上一個] 的圖示。\",\n\t\t\"編輯器尋找小工具中 [尋找下一個] 的圖示。\",\n\t\t\"尋找/取代\",\n\t\t\"尋找\",\n\t\t\"尋找\",\n\t\t\"上一個相符項目\",\n\t\t\"下一個相符項目\",\n\t\t\"在選取範圍中尋找\",\n\t\t\"關閉\",\n\t\t\"取代\",\n\t\t\"取代\",\n\t\t\"取代\",\n\t\t\"全部取代\",\n\t\t\"切換取代\",\n\t\t\"僅反白顯示前 {0} 筆結果，但所有尋找作業會在完整文字上執行。\",\n\t\t\"{1} 的 {0}\",\n\t\t\"查無結果\",\n\t\t\"找到 {0}\",\n\t\t\"以 \\'{1}\\' 找到 {0}\",\n\t\t\"以 \\'{1}\\' 找到 {0}，位於 {2}\",\n\t\t\"已以 \\'{1}\\' 找到 {0}\",\n\t\t\"Ctrl+Enter 現在會插入分行符號，而不會全部取代。您可以修改 editor.action.replaceAll 的按鍵繫結關係，以覆寫此行為。\",\n\t],\n\t\"vs/editor/contrib/folding/browser/folding\": [\n\t\t\"展開\",\n\t\t\"以遞迴方式展開\",\n\t\t\"摺疊\",\n\t\t\"切換摺疊\",\n\t\t\"以遞迴方式摺疊\",\n\t\t\"摺疊全部區塊註解\",\n\t\t\"摺疊所有區域\",\n\t\t\"展開所有區域\",\n\t\t\"摺疊所選區域以外的所有區域\",\n\t\t\"展開所選區域以外的所有區域\",\n\t\t\"全部摺疊\",\n\t\t\"全部展開\",\n\t\t\"移至父代摺疊\",\n\t\t\"移至上一個摺疊範圍\",\n\t\t\"移至下一個摺疊範圍\",\n\t\t\"從選取範圍建立摺疊範圍\",\n\t\t\"移除手動折疊範圍\",\n\t\t\"摺疊層級 {0}\",\n\t],\n\t\"vs/editor/contrib/folding/browser/foldingDecorations\": [\n\t\t\"已摺疊範圍後的背景色彩。色彩不得處於不透明狀態，以免隱藏底層裝飾。\",\n\t\t\"編輯器裝訂邊的摺疊控制項色彩。\",\n\t\t\"編輯器字符邊界中 [展開的範圍] 的圖示。\",\n\t\t\"編輯器字符邊界中 [摺疊的範圍] 的圖示。\",\n\t\t\"編輯器字符邊界中手動摺疊範圍的圖示。\",\n\t\t\"編輯器字符邊界中手動展開範圍的圖示。\",\n\t],\n\t\"vs/editor/contrib/fontZoom/browser/fontZoom\": [\n\t\t\"編輯器字體放大\",\n\t\t\"編輯器字型縮小\",\n\t\t\"編輯器字體重設縮放\",\n\t],\n\t\"vs/editor/contrib/format/browser/formatActions\": [\n\t\t\"格式化文件\",\n\t\t\"格式化選取範圍\",\n\t],\n\t\"vs/editor/contrib/gotoError/browser/gotoError\": [\n\t\t\"移至下一個問題 (錯誤, 警告, 資訊)\",\n\t\t\"[前往下一個標記] 的圖示。\",\n\t\t\"移至上一個問題 (錯誤, 警告, 資訊)\",\n\t\t\"[前往上一個標記] 的圖示。\",\n\t\t\"移至檔案裡面的下一個問題 (錯誤, 警告, 資訊)\",\n\t\t\"下一個問題(&&P)\",\n\t\t\"移至檔案裡面的上一個問題 (錯誤, 警告, 資訊)\",\n\t\t\"前一個問題(&&P)\",\n\t],\n\t\"vs/editor/contrib/gotoError/browser/gotoErrorWidget\": [\n\t\t\"錯誤\",\n\t\t\"警告\",\n\t\t\"資訊\",\n\t\t\"提示\",\n\t\t\"{0} 於 {1}。\",\n\t\t\"{0} 個問題 (共 {1} 個)\",\n\t\t\"{0} 個問題 (共 {1} 個)\",\n\t\t\"編輯器標記導覽小工具錯誤的色彩。\",\n\t\t\"編輯器標記導覽小工具錯誤標題背景。\",\n\t\t\"編輯器標記導覽小工具警告的色彩。\",\n\t\t\"編輯器標記導覽小工具警告標題背景。\",\n\t\t\"編輯器標記導覽小工具資訊的色彩\",\n\t\t\"編輯器標記導覽小工具資訊標題背景。\",\n\t\t\"編輯器標記導覽小工具的背景。\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/goToCommands\": [\n\t\t\"查看\",\n\t\t\"定義\",\n\t\t\"找不到 \\'{0}\\' 的定義\",\n\t\t\"找不到任何定義\",\n\t\t\"移至定義\",\n\t\t\"移至定義(&&D)\",\n\t\t\"在一側開啟定義\",\n\t\t\"瞄核定義\",\n\t\t\"宣告\",\n\t\t\"找不到 \\'{0}\\' 的宣告 \",\n\t\t\"找不到任何宣告\",\n\t\t\"移至宣告\",\n\t\t\"前往宣告(&&D)\",\n\t\t\"找不到 \\'{0}\\' 的宣告 \",\n\t\t\"找不到任何宣告\",\n\t\t\"預覽宣告\",\n\t\t\"類型定義\",\n\t\t\"找不到 \\'{0}\\' 的任何類型定義\",\n\t\t\"找不到任何類型定義\",\n\t\t\"移至類型定義\",\n\t\t\"前往類型定義(&&T)\",\n\t\t\"預覽類型定義\",\n\t\t\"實作\",\n\t\t\"找不到 \\'{0}\\' 的任何實作\",\n\t\t\"找不到任何實作\",\n\t\t\"前往實作\",\n\t\t\"前往實作(&&I)\",\n\t\t\"查看實作\",\n\t\t\"未找到 \\\"{0}\\\" 的參考\",\n\t\t\"未找到參考\",\n\t\t\"前往參考\",\n\t\t\"前往參考(&&R)\",\n\t\t\"參考\",\n\t\t\"預覽參考\",\n\t\t\"參考\",\n\t\t\"前往任何符號\",\n\t\t\"位置\",\n\t\t\"\\'{0}\\' 沒有結果\",\n\t\t\"參考\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition\": [\n\t\t\"按一下以顯示 {0} 項定義。\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesController\": [\n\t\t\"是否顯示參考瞄核，例如「瞄核參考」或「瞄核定義」\",\n\t\t\"正在載入...\",\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesTree\": [\n\t\t\"{0} 個參考\",\n\t\t\"{0} 個參考\",\n\t\t\"參考\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget\": [\n\t\t\"無法預覽\",\n\t\t\"查無結果\",\n\t\t\"參考\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/referencesModel\": [\n\t\t\"在資料行 {2} 行 {1} 的 {0} 中\",\n\t\t\"在資料行 {3} 行 {2} 的 {1} 的 {0} 中\",\n\t\t\"1 個符號位於 {0}, 完整路徑 {1}\",\n\t\t\"{0} 個符號位於 {1}, 完整路徑 {2}\",\n\t\t\"找不到結果\",\n\t\t\"在 {0} 中找到 1 個符號\",\n\t\t\"在 {1} 中找到 {0} 個符號\",\n\t\t\"在 {1} 個檔案中找到 {0} 個符號\",\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/symbolNavigation\": [\n\t\t\"是否有只能透過鍵盤瀏覽的符號位置。\",\n\t\t\"{1} 的符號 {0}，{2} 為下一個\",\n\t\t\"{1} 的符號 {0}\",\n\t],\n\t\"vs/editor/contrib/hover/browser/hover\": [\n\t\t\"顯示或聚焦暫留\",\n\t\t\"游標暫留將不會自動聚焦。\",\n\t\t\"只有在游標暫留顯示時才會聚焦。\",\n\t\t\"游標暫留出現時將自動聚焦。\",\n\t\t\"顯示定義預覽懸停\",\n\t\t\"向上捲動暫留\",\n\t\t\"向下捲動暫留\",\n\t\t\"向左捲動暫留\",\n\t\t\"向右捲動暫留\",\n\t\t\"上一頁暫留\",\n\t\t\"下一頁暫留\",\n\t\t\"移至上方暫留\",\n\t\t\"移至下方暫留\",\n\t],\n\t\"vs/editor/contrib/hover/browser/markdownHoverParticipant\": [\n\t\t\"正在載入...\",\n\t\t\"由於效能原因，已暫停轉譯。這可透過 `editor.stopRenderingLineAfter` 進行設定。\",\n\t\t\"因效能的緣故，已跳過將長的行 Token 化。您可透過 `editor.maxTokenizationLineLength` 設定。\",\n\t],\n\t\"vs/editor/contrib/hover/browser/markerHoverParticipant\": [\n\t\t\"檢視問題\",\n\t\t\"沒有可用的快速修正\",\n\t\t\"正在檢查快速修正...\",\n\t\t\"沒有可用的快速修正\",\n\t\t\"快速修復...\",\n\t],\n\t\"vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace\": [\n\t\t\"以上一個值取代\",\n\t\t\"以下一個值取代\",\n\t],\n\t\"vs/editor/contrib/indentation/browser/indentation\": [\n\t\t\"將縮排轉換成空格\",\n\t\t\"將縮排轉換成定位點\",\n\t\t\"已設定的定位點大小\",\n\t\t\"預設索引標籤大小\",\n\t\t\"目前的索引標籤大小\",\n\t\t\"選取目前檔案的定位點大小\",\n\t\t\"使用 Tab 進行縮排\",\n\t\t\"使用空格鍵進行縮排\",\n\t\t\"變更索引標籤顯示大小\",\n\t\t\"偵測內容中的縮排\",\n\t\t\"重新將行縮排\",\n\t\t\"重新將選取的行縮排\",\n\t],\n\t\"vs/editor/contrib/inlayHints/browser/inlayHintsHover\": [\n\t\t\"按兩下以插入\",\n\t\t\"cmd + 按一下\",\n\t\t\"ctrl + 按一下\",\n\t\t\"選項 + 按一下\",\n\t\t\"alt + 按一下\",\n\t\t\"前往 [定義] ({0})，按一下滑鼠右鍵以了解更多\",\n\t\t\"移至定義 ({0})\",\n\t\t\"執行命令\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/commands\": [\n\t\t\"顯示下一個內嵌建議\",\n\t\t\"顯示上一個內嵌建議\",\n\t\t\"觸發內嵌建議\",\n\t\t\"接受下一個內嵌建議字組\",\n\t\t\"接受字組\",\n\t\t\"接受下一個內嵌建議行\",\n\t\t\"接受行\",\n\t\t\"接受內嵌建議\",\n\t\t\"接受\",\n\t\t\"隱藏內嵌建議\",\n\t\t\"永遠顯示工具列\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/hoverParticipant\": [\n\t\t\"建議:\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys\": [\n\t\t\"是否顯示內嵌建議\",\n\t\t\"內嵌建議是否以空白字元開頭\",\n\t\t\"內嵌建議的開頭是否為空白，且比 Tab 能插入的字元要小\",\n\t\t\"是否應隱藏目前建議的其他建議\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController\": [\n\t\t\"在可存取檢視中檢查此項目 ({0})\",\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget\": [\n\t\t\"[顯示下一個參數提示] 的圖示。\",\n\t\t\"[顯示上一個參數提示] 的圖示。\",\n\t\t\"{0} ({1})\",\n\t\t\"上一步\",\n\t\t\"下一步\",\n\t],\n\t\"vs/editor/contrib/lineSelection/browser/lineSelection\": [\n\t\t\"展開線條選取範圍\",\n\t],\n\t\"vs/editor/contrib/linesOperations/browser/linesOperations\": [\n\t\t\"將行向上複製\",\n\t\t\"將行向上複製(&&C)\",\n\t\t\"將行向下複製\",\n\t\t\"將行向下複製(&&P)\",\n\t\t\"重複選取項目\",\n\t\t\"重複選取項目(&&D)\",\n\t\t\"上移一行\",\n\t\t\"上移一行(&&V)\",\n\t\t\"下移一行\",\n\t\t\"下移一行(&&L)\",\n\t\t\"遞增排序行\",\n\t\t\"遞減排序行\",\n\t\t\"刪除重複的行\",\n\t\t\"修剪尾端空白\",\n\t\t\"刪除行\",\n\t\t\"縮排行\",\n\t\t\"凸排行\",\n\t\t\"在上方插入行\",\n\t\t\"在下方插入行\",\n\t\t\"左邊全部刪除\",\n\t\t\"刪除所有右方項目\",\n\t\t\"連接線\",\n\t\t\"轉置游標周圍的字元數\",\n\t\t\"轉換到大寫\",\n\t\t\"轉換到小寫\",\n\t\t\"轉換為字首大寫\",\n\t\t\"轉換為底線連接字\",\n\t\t\"轉換為 Camel 案例\",\n\t\t\"轉換成 Kebab Case\",\n\t],\n\t\"vs/editor/contrib/linkedEditing/browser/linkedEditing\": [\n\t\t\"開始連結的編輯\",\n\t\t\"當編輯器自動重新命名類型時的背景色彩。\",\n\t],\n\t\"vs/editor/contrib/links/browser/links\": [\n\t\t\"因為此連結的格式不正確，所以無法開啟: {0}\",\n\t\t\"因為此連結目標遺失，所以無法開啟。\",\n\t\t\"執行命令\",\n\t\t\"追蹤連結\",\n\t\t\"cmd + 按一下\",\n\t\t\"ctrl + 按一下\",\n\t\t\"選項 + 按一下\",\n\t\t\"alt + 按一下\",\n\t\t\"執行命令 {0}\",\n\t\t\"開啟連結\",\n\t],\n\t\"vs/editor/contrib/message/browser/messageController\": [\n\t\t\"編輯器目前是否正在顯示內嵌訊息\",\n\t],\n\t\"vs/editor/contrib/multicursor/browser/multicursor\": [\n\t\t\"新增的資料指標: {0}\",\n\t\t\"新增的資料指標: {0}\",\n\t\t\"在上方加入游標\",\n\t\t\"在上方新增游標(&&A)\",\n\t\t\"在下方加入游標\",\n\t\t\"在下方新增游標(&&D)\",\n\t\t\"在行尾新增游標\",\n\t\t\"在行尾新增游標(&&U)\",\n\t\t\"將游標新增到底部 \",\n\t\t\"將游標新增到頂部\",\n\t\t\"將選取項目加入下一個找到的相符項\",\n\t\t\"新增下一個項目(&&N)\",\n\t\t\"將選取項目加入前一個找到的相符項中\",\n\t\t\"新增上一個項目(&&R)\",\n\t\t\"將最後一個選擇項目移至下一個找到的相符項\",\n\t\t\"將最後一個選擇項目移至前一個找到的相符項\",\n\t\t\"選取所有找到的相符項目\",\n\t\t\"選取所有項目(&&O)\",\n\t\t\"變更所有發生次數\",\n\t\t\"聚焦下一個游標\",\n\t\t\"聚焦下一個游標\",\n\t\t\"聚焦上一個游標\",\n\t\t\"聚焦前一個游標\",\n\t],\n\t\"vs/editor/contrib/parameterHints/browser/parameterHints\": [\n\t\t\"觸發參數提示\",\n\t],\n\t\"vs/editor/contrib/parameterHints/browser/parameterHintsWidget\": [\n\t\t\"[顯示下一個參數提示] 的圖示。\",\n\t\t\"[顯示上一個參數提示] 的圖示。\",\n\t\t\"{0}，提示\",\n\t\t\"參數提示中使用中項目的前景色彩。\",\n\t],\n\t\"vs/editor/contrib/peekView/browser/peekView\": [\n\t\t\"目前的程式碼編輯器是否內嵌於瞄核內\",\n\t\t\"關閉\",\n\t\t\"預覽檢視標題區域的背景色彩。\",\n\t\t\"預覽檢視標題的色彩。\",\n\t\t\"預覽檢視標題資訊的色彩。\",\n\t\t\"預覽檢視之框線與箭頭的色彩。\",\n\t\t\"預覽檢視中結果清單的背景色彩。\",\n\t\t\"預覽檢視結果列表中行節點的前景色彩\",\n\t\t\"預覽檢視結果列表中檔案節點的前景色彩\",\n\t\t\"在預覽檢視之結果清單中選取項目時的背景色彩。\",\n\t\t\"在預覽檢視之結果清單中選取項目時的前景色彩。\",\n\t\t\"預覽檢視編輯器的背景色彩。\",\n\t\t\"預覽檢視編輯器邊框(含行號或字形圖示)的背景色彩。\",\n\t\t\"預覽檢視編輯器中黏性滾動的背景色彩。\",\n\t\t\"在預覽檢視編輯器中比對時的反白顯示色彩。\",\n\t\t\"預覽檢視編輯器中比對時的反白顯示色彩。\",\n\t\t\"在預覽檢視編輯器中比對時的反白顯示邊界。\",\n\t],\n\t\"vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess\": [\n\t\t\"先開啟文字編輯器，前往某一行。\",\n\t\t\"前往第 {0} 行的第 {1} 個字元。\",\n\t\t\"前往第 {0} 行。\",\n\t\t\"目前行: {0}，字元: {1}。請鍵入介於 1 到 {2} 之間行號，導覽至該行。\",\n\t\t\"目前行: {0}，字元: {1}。請鍵入要導覽至的行號。\",\n\t],\n\t\"vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess\": [\n\t\t\"若要前往符號，請先開啟包含符號資訊的文字編輯器。\",\n\t\t\"使用中的文字編輯器不提供符號資訊。\",\n\t\t\"沒有相符的編輯器符號\",\n\t\t\"沒有編輯器符號\",\n\t\t\"開至側邊\",\n\t\t\"開啟到底部\",\n\t\t\"符號 ({0})\",\n\t\t\"屬性 ({0})\",\n\t\t\"方法 ({0})\",\n\t\t\"函式 ({0})\",\n\t\t\"建構函式 ({0})\",\n\t\t\"變數 ({0})\",\n\t\t\"類別 ({0})\",\n\t\t\"結構 ({0})\",\n\t\t\"事件 ({0})\",\n\t\t\"運算子 ({0})\",\n\t\t\"介面 ({0})\",\n\t\t\"命名空間 ({0})\",\n\t\t\"套件 ({0})\",\n\t\t\"型別參數 ({0})\",\n\t\t\"模組 ({0})\",\n\t\t\"屬性 ({0})\",\n\t\t\"列舉 ({0})\",\n\t\t\"列舉成員 ({0})\",\n\t\t\"字串 ({0})\",\n\t\t\"檔案 ({0})\",\n\t\t\"陣列 ({0})\",\n\t\t\"數字 ({0})\",\n\t\t\"布林值 ({0})\",\n\t\t\"物件 ({0})\",\n\t\t\"索引鍵 ({0})\",\n\t\t\"欄位 ({0})\",\n\t\t\"常數 ({0})\",\n\t],\n\t\"vs/editor/contrib/readOnlyMessage/browser/contribution\": [\n\t\t\"無法在唯讀輸入中編輯\",\n\t\t\"無法在唯讀編輯器中編輯\",\n\t],\n\t\"vs/editor/contrib/rename/browser/rename\": [\n\t\t\"沒有結果。\",\n\t\t\"解析重新命名位置時發生未知的錯誤\",\n\t\t\"正在將 \\'{0}\\' 重新命名為 \\'{1}\\'\",\n\t\t\"正在將 {0} 重新命名為 {1}\",\n\t\t\"已成功將 \\'{0}\\' 重新命名為 \\'{1}\\'。摘要: {2}\",\n\t\t\"重命名無法套用編輯\",\n\t\t\"重新命名無法計算編輯\",\n\t\t\"重新命名符號\",\n\t\t\"啟用/停用重新命名前先預覽變更的功能\",\n\t],\n\t\"vs/editor/contrib/rename/browser/renameInputField\": [\n\t\t\"是否顯示重新命名輸入小工具\",\n\t\t\"為輸入重新命名。請鍵入新名稱，然後按 Enter 以提交。\",\n\t\t\"按 {0} 進行重新命名，按 {1} 進行預覽\",\n\t],\n\t\"vs/editor/contrib/smartSelect/browser/smartSelect\": [\n\t\t\"展開選取項目\",\n\t\t\"展開選取範圍(&&E)\",\n\t\t\"縮小選取項目\",\n\t\t\"壓縮選取範圍(&&S)\",\n\t],\n\t\"vs/editor/contrib/snippet/browser/snippetController2\": [\n\t\t\"編輯器目前是否在程式碼片段模式中\",\n\t\t\"在程式碼片段模式中是否有下一個定位停駐點\",\n\t\t\"在程式碼片段模式中是否有上一個定位停駐點\",\n\t\t\"移至下一個預留位置...\",\n\t],\n\t\"vs/editor/contrib/snippet/browser/snippetVariables\": [\n\t\t\"星期天\",\n\t\t\"星期一\",\n\t\t\"星期二\",\n\t\t\"星期三\",\n\t\t\"星期四\",\n\t\t\"星期五\",\n\t\t\"星期六\",\n\t\t\"週日\",\n\t\t\"週一\",\n\t\t\"週二\",\n\t\t\"週三\",\n\t\t\"週四\",\n\t\t\"週五\",\n\t\t\"週六\",\n\t\t\"一月\",\n\t\t\"二月\",\n\t\t\"三月\",\n\t\t\"四月\",\n\t\t\"五月\",\n\t\t\"六月\",\n\t\t\"七月\",\n\t\t\"八月\",\n\t\t\"九月\",\n\t\t\"十月\",\n\t\t\"十一月\",\n\t\t\"十二月\",\n\t\t\"1月\",\n\t\t\"2月\",\n\t\t\"3 月\",\n\t\t\"4月\",\n\t\t\"五月\",\n\t\t\"6月\",\n\t\t\"7 月\",\n\t\t\"8 月\",\n\t\t\"9 月\",\n\t\t\"10 月\",\n\t\t\"11 月\",\n\t\t\"12 月\",\n\t],\n\t\"vs/editor/contrib/stickyScroll/browser/stickyScrollActions\": [\n\t\t\"切換自黏捲動\",\n\t\t\"切換自黏捲動(&&T)\",\n\t\t\"自黏捲動\",\n\t\t\"自黏捲動(&&S)\",\n\t\t\"聚焦自黏捲動\",\n\t\t\"焦點自黏捲動(&&F)\",\n\t\t\"選取下一個自黏捲動行\",\n\t\t\"選取上一個自黏捲動行\",\n\t\t\"移至聚焦的自黏捲動行\",\n\t\t\"選取編輯器\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggest\": [\n\t\t\"是否聚焦任何建議\",\n\t\t\"是否顯示建議詳細資料\",\n\t\t\"是否有多個建議可以挑選\",\n\t\t\"插入目前的建議會產生變更，或已鍵入所有項目\",\n\t\t\"是否在按下 Enter 時插入建議\",\n\t\t\"目前的建議是否有插入和取代行為\",\n\t\t\"預設行為是插入或取代\",\n\t\t\"目前的建議是否支援解決更多詳細資料\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestController\": [\n\t\t\"接受 ‘{0}’ 進行了其他 {1} 項編輯\",\n\t\t\"觸發建議\",\n\t\t\"插入\",\n\t\t\"插入\",\n\t\t\"取代\",\n\t\t\"取代\",\n\t\t\"插入\",\n\t\t\"顯示更少\",\n\t\t\"顯示更多\",\n\t\t\"重設建議小工具大小\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidget\": [\n\t\t\"建議小工具的背景色彩。\",\n\t\t\"建議小工具的邊界色彩。\",\n\t\t\"建議小工具的前景色彩。\",\n\t\t\"建議小工具中所選項目的前景色彩。\",\n\t\t\"建議小工具中所選項目的圖示前景色彩。\",\n\t\t\"建議小工具中所選項目的背景色彩。\",\n\t\t\"建議小工具中相符醒目提示的色彩。\",\n\t\t\"當項目成為焦點時，相符項目的色彩在建議小工具中會醒目顯示。\",\n\t\t\"建議小工具狀態的前景色彩。\",\n\t\t\"正在載入...\",\n\t\t\"無建議。\",\n\t\t\"建議\",\n\t\t\"{0} {1}，{2}\",\n\t\t\"{0} {1}\",\n\t\t\"{0}，{1}\",\n\t\t\"{0}，文件: {1}\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetDetails\": [\n\t\t\"關閉\",\n\t\t\"正在載入...\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetRenderer\": [\n\t\t\"建議小工具中 [更多詳細資訊] 的圖示。\",\n\t\t\"閱讀更多\",\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetStatus\": [\n\t\t\"{0} ({1})\",\n\t],\n\t\"vs/editor/contrib/symbolIcons/browser/symbolIcons\": [\n\t\t\"陣列符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"布林值符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"類別符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"色彩符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"常數符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"建構函式符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"列舉值符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"列舉值成員符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"事件符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"欄位符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"檔案符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"資料夾符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"函式符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"介面符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"索引鍵符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"關鍵字符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"方法符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"模組符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"命名空間符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"Null 符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"數字符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"物件符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"運算子符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"套件符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"屬性符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"參考符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"程式碼片段符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"字串符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"結構符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"文字符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"型別參數符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"單位符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t\t\"變數符號的前景色彩。這些符號會出現在大綱、階層連結和建議小工具中。\",\n\t],\n\t\"vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode\": [\n\t\t\"切換 TAB 鍵移動焦點\",\n\t\t\"按 Tab 現在會將焦點移至下一個可設定焦點的元素。\",\n\t\t\"按 Tab 現在會插入定位字元。\",\n\t],\n\t\"vs/editor/contrib/tokenization/browser/tokenization\": [\n\t\t\"開發人員: 強制重新置放\",\n\t],\n\t\"vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter\": [\n\t\t\"延伸模組編輯器中顯示含有警告訊息的圖示。\",\n\t\t\"此文件包含許多非基本 ASCII Unicode 字元\",\n\t\t\"此文件包含許多不明確的 Unicode 字元\",\n\t\t\"此文件包含許多隱藏的 Unicode 字元\",\n\t\t\"字元 {0} 可能與 ASCII 字元 {1} 混淆，這在原始程式碼中比較常見。\",\n\t\t\"字元 {0} 可能與字元 {1} 混淆，這在原始程式碼中比較常見。\",\n\t\t\"字元 {0} 隱藏。\",\n\t\t\"字元 {0} 不是基本的 ASCII 字元。\",\n\t\t\"調整設定\",\n\t\t\"停用註解中的醒目提示\",\n\t\t\"停用註解中字元的醒目提示\",\n\t\t\"停用字串中的醒目提示\",\n\t\t\"停用字串中字元的醒目提示\",\n\t\t\"停用不明確的醒目提示\",\n\t\t\"停用不明確字元的醒目提示\",\n\t\t\"停用隱藏醒目提示\",\n\t\t\"停用隱藏字元的醒目提示\",\n\t\t\"停用非 ASCII 醒目提示\",\n\t\t\"停用非基本 ASCII 字元的醒目提示\",\n\t\t\"顯示排除選項\",\n\t\t\"排除 {0} (隱藏字元) 的反白顯示\",\n\t\t\"將 {0} 排除在已醒目提示\",\n\t\t\"允許在語言「{0}」中較常用的 Unicode 字元。\",\n\t\t\"設定 Unicode 醒目提示選項\",\n\t],\n\t\"vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators\": [\n\t\t\"異常的行結束字元\",\n\t\t\"偵測到異常的行結束字元\",\n\t\t\"檔案 \\'{0}\\' 包含一或多個異常的行結束字元，例如行分隔符號 (LS) 或段落分隔符號 (PS)。\\r\\n\\r\\n建議您將其從檔案中移除。這可以透過 `editor.unusualLineTerminators` 進行設定。\",\n\t\t\"移除異常的行結束字元(&&R)\",\n\t\t\"忽略\",\n\t],\n\t\"vs/editor/contrib/wordHighlighter/browser/highlightDecorations\": [\n\t\t\"讀取權限期間 (如讀取變數) 符號的背景色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"寫入權限期間 (如寫入變數) 符號的背景色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"符號文字出現的背景色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"讀取存取期間 (例如讀取變數時) 符號的邊框顏色。\",\n\t\t\"寫入存取期間 (例如寫入變數時) 符號的邊框顏色。 \",\n\t\t\"符號文字出現的框線色彩。\",\n\t\t\"符號醒目提示的概觀尺規標記色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"寫入權限符號醒目提示的概觀尺規標記色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"符號文字出現的概觀尺規標記色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t],\n\t\"vs/editor/contrib/wordHighlighter/browser/wordHighlighter\": [\n\t\t\"移至下一個反白符號\",\n\t\t\"移至上一個反白符號\",\n\t\t\"觸發符號反白顯示\",\n\t],\n\t\"vs/editor/contrib/wordOperations/browser/wordOperations\": [\n\t\t\"刪除字組\",\n\t],\n\t\"vs/platform/action/common/actionCommonCategories\": [\n\t\t\"檢視\",\n\t\t\"說明\",\n\t\t\"測試\",\n\t\t\"檔案\",\n\t\t\"喜好設定\",\n\t\t\"開發人員\",\n\t],\n\t\"vs/platform/actionWidget/browser/actionList\": [\n\t\t\"{0} 以套用，{1} 以預覽\",\n\t\t\"{0} 以申請\",\n\t\t\"{0}，停用原因: {1}\",\n\t\t\"動作小工具\",\n\t],\n\t\"vs/platform/actionWidget/browser/actionWidget\": [\n\t\t\"動作列中切換動作項目的背景色彩。\",\n\t\t\"是否顯示動作小工具清單\",\n\t\t\"隱藏動作小工具\",\n\t\t\"選取上一個動作\",\n\t\t\"選取下一個動作\",\n\t\t\"接受選取的動作\",\n\t\t\"預覽選取的動作\",\n\t],\n\t\"vs/platform/actions/browser/menuEntryActionViewItem\": [\n\t\t\"{0} ({1})\",\n\t\t\"{0} ({1})\",\n\t\t\"{0}\\r\\n[{1}] {2}\",\n\t],\n\t\"vs/platform/actions/browser/toolbar\": [\n\t\t\"隱藏\",\n\t\t\"重設功能表\",\n\t],\n\t\"vs/platform/actions/common/menuService\": [\n\t\t\"隱藏 \\'{0}\\'\",\n\t],\n\t\"vs/platform/audioCues/browser/audioCueService\": [\n\t\t\"行上發生錯誤\",\n\t\t\"行上的警告\",\n\t\t\"行上的摺疊區域\",\n\t\t\"行上的中斷點\",\n\t\t\"行上的內嵌建議\",\n\t\t\"終端機快速修正\",\n\t\t\"在中斷點停止偵錯工具\",\n\t\t\"行上沒有嵌入提示\",\n\t\t\"工作完成\",\n\t\t\"工作失敗\",\n\t\t\"終端機命令失敗\",\n\t\t\"終端鈴\",\n\t\t\"Notebook 儲存格已完成\",\n\t\t\"Notebook 儲存格失敗\",\n\t\t\"差異行已插入\",\n\t\t\"差異行已刪除\",\n\t\t\"差異行已修改\",\n\t\t\"聊天要求已傳送\",\n\t\t\"聊天回應已接收\",\n\t\t\"聊天回應擱置中\",\n\t\t\"清除\",\n\t\t\"儲存\",\n\t\t\"格式\",\n\t],\n\t\"vs/platform/configuration/common/configurationRegistry\": [\n\t\t\"預設語言組態覆寫\",\n\t\t\"設定要針對 {0} 語言覆寫的設定。\",\n\t\t\"設定要針對語言覆寫的編輯器設定。\",\n\t\t\"這個設定不支援以語言為根據的組態。\",\n\t\t\"設定要針對語言覆寫的編輯器設定。\",\n\t\t\"這個設定不支援以語言為根據的組態。\",\n\t\t\"無法註冊空白屬性\",\n\t\t\"無法註冊 \\'{0}\\'。這符合用於描述語言專用編輯器設定的屬性模式 \\'\\\\\\\\[.*\\\\\\\\]$\\'。請使用 \\'configurationDefaults\\' 貢獻。\",\n\t\t\"無法註冊 \\'{0}\\'。此屬性已經註冊。\",\n\t\t\"無法註冊 \\'{0}\\'。已向 {2} 註冊關聯的原則 {1}。\",\n\t],\n\t\"vs/platform/contextkey/browser/contextKeyService\": [\n\t\t\"傳回有關內容索引鍵資訊的命令\",\n\t],\n\t\"vs/platform/contextkey/common/contextkey\": [\n\t\t\"空的內容索引鍵運算式\",\n\t\t\"您是否忘記撰寫運算式? 您也可以分別放置 \\'false\\' 或 \\'true\\'，以一律評估為 False 或 True。\",\n\t\t\"\\'not\\' 後為 \\'in\\'。\",\n\t\t\"右括弧 \\')\\'\",\n\t\t\"未預期的權杖\",\n\t\t\"您是否忘記在權杖之前放置 && 或 ||?\",\n\t\t\"運算式未預期的結尾\",\n\t\t\"您是否忘記放置內容金鑰?\",\n\t\t\"預期: {0}\\r\\n收到: \\'{1}\\'。\",\n\t],\n\t\"vs/platform/contextkey/common/contextkeys\": [\n\t\t\"作業系統是否為 macOS\",\n\t\t\"作業系統是否為 Linux\",\n\t\t\"作業系統是否為 Windows\",\n\t\t\"平台是否為網頁瀏覽器\",\n\t\t\"非瀏覽器平台上的作業系統是否為 macOS\",\n\t\t\"作業系統是否為 iOS\",\n\t\t\"平臺是否為行動網頁瀏覽器\",\n\t\t\"VS Code 的品質類型\",\n\t\t\"鍵盤焦點是否位於輸入方塊內\",\n\t],\n\t\"vs/platform/contextkey/common/scanner\": [\n\t\t\"您是指 \\'{0}\\'?\",\n\t\t\"您是指 {0} 或 {1}?\",\n\t\t\"您是指 {0}、{1} 或 {2}?\",\n\t\t\"您是否忘記左括弧或右括弧?\",\n\t\t\"您是否忘記逸出 \\'/\\' (斜線) 字元? 在反斜線前放兩個反斜線以逸出，例如 \\'\\\\\\\\/\\'。\",\n\t],\n\t\"vs/platform/history/browser/contextScopedHistoryWidget\": [\n\t\t\"是否顯示建議\",\n\t],\n\t\"vs/platform/keybinding/common/abstractKeybindingService\": [\n\t\t\"已按下 ({0})。等待第二個套索鍵...\",\n\t\t\"({0}) 已按下。正在等待下一個套索鍵...\",\n\t\t\"按鍵組合 ({0}, {1}) 不是命令。\",\n\t\t\"按鍵組合 ({0}, {1}) 不是命令。\",\n\t],\n\t\"vs/platform/list/browser/listService\": [\n\t\t\"工作台\",\n\t\t\"對應Windows和Linux的\\'Control\\'與對應 macOS 的\\'Command\\'。\",\n\t\t\"對應Windows和Linux的\\'Alt\\'與對應macOS的\\'Option\\'。\",\n\t\t\"透過滑鼠多選，用於在樹狀目錄與清單中新增項目的輔助按鍵 (例如在總管中開啟編輯器 及 SCM 檢視)。\\'在側邊開啟\\' 滑鼠手勢 (若支援) 將會適應以避免和多選輔助按鍵衝突。\",\n\t\t\"控制如何使用滑鼠 (如支援此用法) 開啟樹狀目錄與清單中的項目。若不適用，某些樹狀目錄與清單可能會選擇忽略此設定。\",\n\t\t\"控制在工作台中，清單與樹狀結構是否支援水平捲動。警告: 開啟此設定將會影響效能。\",\n\t\t\"控制按一下捲軸是否逐頁捲動。\",\n\t\t\"控制樹狀結構縮排 (像素)。\",\n\t\t\"控制樹系是否應轉譯縮排輔助線。\",\n\t\t\"控制清單和樹狀結構是否具有平滑捲動。\",\n\t\t\"要用於滑鼠滾輪捲動事件 `deltaX` 和 `deltaY` 的乘數。\",\n\t\t\"按下 `Alt` 時的捲動速度乘數。\",\n\t\t\"搜尋時會醒目提示元素。進一步的向上和向下瀏覽只會周遊已醒目提示的元素。\",\n\t\t\"搜尋時篩選元素。\",\n\t\t\"控制 Workbench 中清單和樹狀結構的預設尋找模式。\",\n\t\t\"比對按鍵輸入的簡易按鍵瀏覽焦點元素。僅比對前置詞。\",\n\t\t\"醒目提示鍵盤瀏覽會醒目提示符合鍵盤輸入的元素。進一步向上或向下瀏覽只會周遊醒目提示的元素。\",\n\t\t\"篩選鍵盤瀏覽會篩掉並隱藏不符合鍵盤輸入的所有元素。\",\n\t\t\"控制 Workbench 中清單和樹狀結構的鍵盤瀏覽樣式。可以是簡易的、醒目提示和篩選。\",\n\t\t\"請改為使用 \\'workbench.list.defaultFindMode\\' 和 \\'workbench.list.typeNavigationMode\\'。\",\n\t\t\"搜尋時使用模糊比對。\",\n\t\t\"搜尋時使用連續比對。\",\n\t\t\"控制在工作台中搜尋清單和樹狀結構時所使用的比對類型。\",\n\t\t\"控制當按下資料夾名稱時，樹狀目錄資料夾的展開方式。請注意，若不適用，某些樹狀目錄和清單可能會選擇忽略此設定。\",\n\t\t\"控制是否要在樹狀中啟動黏性捲動。\",\n\t\t\"控制啟用 `#workbench.tree.enableStickyScroll#` 時，樹狀中顯示的黏性元素數目。\",\n\t\t\"控制工作台中清單和樹狀目錄的類型瀏覽運作方式。設定為 \\'trigger\\' 時，類型瀏覽會在執行 \\'list.triggerTypeNavigation\\' 命令時隨即開始。\",\n\t],\n\t\"vs/platform/markers/common/markers\": [\n\t\t\"錯誤\",\n\t\t\"警告\",\n\t\t\"資訊\",\n\t],\n\t\"vs/platform/quickinput/browser/commandsQuickAccess\": [\n\t\t\"最近使用的\",\n\t\t\"類似的命令\",\n\t\t\"經常使用\",\n\t\t\"其他命令\",\n\t\t\"類似的命令\",\n\t\t\"{0}, {1}\",\n\t\t\"命令 \\'{0}\\' 造成錯誤\",\n\t],\n\t\"vs/platform/quickinput/browser/helpQuickAccess\": [\n\t\t\"{0}, {1}\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInput\": [\n\t\t\"上一頁\",\n\t\t\"按 \\'Enter\\' 鍵確認您的輸入或按 \\'Esc\\' 鍵取消\",\n\t\t\"{0}/{1}\",\n\t\t\"輸入以縮小結果範圍。\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputController\": [\n\t\t\"切換所有核取方塊\",\n\t\t\"{0} 個結果\",\n\t\t\"已選擇 {0}\",\n\t\t\"確定\",\n\t\t\"自訂\",\n\t\t\"背面 ({0})\",\n\t\t\"返回\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputList\": [\n\t\t\"快速輸入\",\n\t],\n\t\"vs/platform/quickinput/browser/quickInputUtils\": [\n\t\t\"按一下以執行命令 ‘{0}’\",\n\t],\n\t\"vs/platform/theme/common/colorRegistry\": [\n\t\t\"整體的前景色彩。僅當未被任何元件覆疊時，才會使用此色彩。\",\n\t\t\"已停用元素的整體前景。只有在元件未覆蓋時，才能使用這個色彩。\",\n\t\t\"整體錯誤訊息的前景色彩。僅當未被任何元件覆蓋時，才會使用此色彩。\",\n\t\t\"提供附加訊息的前景顏色,例如標籤\",\n\t\t\"工作台中圖示的預設色彩。\",\n\t\t\"焦點項目的整體框線色彩。只在沒有任何元件覆寫此色彩時，才會加以使用。\",\n\t\t\"項目周圍的額外框線，可將項目從其他項目中區隔出來以提高對比。\",\n\t\t\"使用中項目周圍的額外邊界，可將項目從其他項目中區隔出來以提高對比。\",\n\t\t\"作業區域選取的背景顏色(例如輸入或文字區域)。請注意，這不適用於編輯器中的選取。\",\n\t\t\"文字分隔符號的顏色。\",\n\t\t\"內文連結的前景色彩\",\n\t\t\"當滑鼠點擊或懸停時，文字中連結的前景色彩。\",\n\t\t\"提示及建議文字的前景色彩。\",\n\t\t\"預先格式化文字區段的背景色彩。\",\n\t\t\"文內引用區塊背景色彩。\",\n\t\t\"引用文字的框線顏色。\",\n\t\t\"文字區塊的背景顏色。\",\n\t\t\"小工具的陰影色彩，例如編輯器中的尋找/取代。\",\n\t\t\"小工具的框線色彩，例如編輯器中的尋找/取代。\",\n\t\t\"輸入方塊的背景。\",\n\t\t\"輸入方塊的前景。\",\n\t\t\"輸入方塊的框線。\",\n\t\t\"輸入欄位中可使用之項目的框線色彩。\",\n\t\t\"在輸入欄位中所啟動選項的背景色彩。\",\n\t\t\"輸入欄位中選項的背景暫留色彩。\",\n\t\t\"在輸入欄位中所啟動選項的前景色彩。\",\n\t\t\"文字輸入替代字符的前景顏色。\",\n\t\t\"資訊嚴重性的輸入驗證背景色彩。\",\n\t\t\"資訊嚴重性的輸入驗證前景色彩。\",\n\t\t\"資訊嚴重性的輸入驗證邊界色彩。\",\n\t\t\"警告嚴重性的輸入驗證背景色彩。\",\n\t\t\"警告嚴重性的輸入驗證前景色彩。\",\n\t\t\"警告嚴重性的輸入驗證邊界色彩。\",\n\t\t\"錯誤嚴重性的輸入驗證背景色彩。\",\n\t\t\"錯誤嚴重性的輸入驗證前景色彩。\",\n\t\t\"錯誤嚴重性的輸入驗證邊界色彩。\",\n\t\t\"下拉式清單的背景。\",\n\t\t\"下拉式清單的背景。\",\n\t\t\"下拉式清單的前景。\",\n\t\t\"下拉式清單的框線。\",\n\t\t\"按鈕前景色彩。\",\n\t\t\"分隔線色彩按鈕。\",\n\t\t\"按鈕背景色彩。\",\n\t\t\"暫留時的按鈕背景色彩。\",\n\t\t\"按鈕框線色彩。\",\n\t\t\"次要按鈕前景色彩。\",\n\t\t\"次要按鈕背景色彩。\",\n\t\t\"滑鼠暫留時的次要按鈕背景色彩。\",\n\t\t\"標記的背景顏色。標記為小型的訊息標籤,例如搜尋結果的數量。\",\n\t\t\"標記的前景顏色。標記為小型的訊息標籤,例如搜尋結果的數量。\",\n\t\t\"指出在捲動該檢視的捲軸陰影。\",\n\t\t\"捲軸滑桿的背景顏色。\",\n\t\t\"動態顯示時捲軸滑桿的背景顏色。\",\n\t\t\"當點擊時捲軸滑桿的背景顏色。\",\n\t\t\"長時間運行進度條的背景色彩.\",\n\t\t\"編輯器中錯誤文字的背景色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"編輯器內錯誤提示線的前景色彩.\",\n\t\t\"如果設定，編輯器中的錯誤會顯示雙底線色彩。\",\n\t\t\"編輯器中警告文字的背景色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"編輯器內警告提示線的前景色彩.\",\n\t\t\"如果設定，編輯器中的警告會顯示雙底線色彩。\",\n\t\t\"編輯器中資訊文字的背景色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"編輯器內資訊提示線的前景色彩\",\n\t\t\"如果設定，編輯器中的提示會顯示雙底線色彩。\",\n\t\t\"編輯器內提示訊息的提示線前景色彩\",\n\t\t\"如果設定，編輯器中的提示會顯示雙底線色彩。\",\n\t\t\"使用中飾帶的框線色彩。\",\n\t\t\"編輯器的背景色彩。\",\n\t\t\"編輯器的預設前景色彩。\",\n\t\t\"編輯器的黏滯卷軸背景色彩\",\n\t\t\"編輯器的游標背景色彩上的黏滯卷軸\",\n\t\t\"編輯器小工具的背景色彩，例如尋找/取代。\",\n\t\t\"編輯器小工具 (例如尋找/取代) 的前景色彩。\",\n\t\t\"編輯器小工具的邊界色彩。小工具選擇擁有邊界或色彩未被小工具覆寫時，才會使用色彩。\",\n\t\t\"編輯器小工具之調整大小列的邊界色彩。只在小工具選擇具有調整大小邊界且未覆寫該色彩時，才使用該色彩。\",\n\t\t\"快速選擇器背景色彩。該快速選擇器小工具是類似命令選擇區的選擇器容器。\",\n\t\t\"快速選擇器前景色彩。快速選擇器小工具是類似命令選擇區等選擇器的容器。\",\n\t\t\"快速選擇器標題背景色彩。快速選擇器小工具是類似命令選擇區的選擇器容器。\",\n\t\t\"分組標籤的快速選擇器色彩。\",\n\t\t\"分組邊界的快速選擇器色彩。\",\n\t\t\"金鑰綁定標籤背景色彩。按鍵綁定標籤用來代表鍵盤快速鍵。\",\n\t\t\"金鑰綁定標籤前景色彩。按鍵綁定標籤用來代表鍵盤快速鍵。\",\n\t\t\"金鑰綁定標籤邊框色彩。按鍵綁定標籤用來代表鍵盤快速鍵。\",\n\t\t\"金鑰綁定標籤邊框底部色彩。按鍵綁定標籤用來代表鍵盤快速鍵。\",\n\t\t\"編輯器選取範圍的色彩。\",\n\t\t\"為選取的文字顏色高對比化\",\n\t\t\"非使用中編輯器內的選取項目色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"與選取項目內容相同之區域的色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"選取時，內容相同之區域的框線色彩。\",\n\t\t\"符合目前搜尋的色彩。\",\n\t\t\"其他搜尋相符項目的色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"限制搜尋之範圍的色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"符合目前搜尋的框線色彩。\",\n\t\t\"符合其他搜尋的框線色彩。\",\n\t\t\"限制搜尋之範圍的框線色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"搜尋編輯器查詢符合的色彩。\",\n\t\t\"搜索編輯器查詢符合的邊框色彩。\",\n\t\t\"搜尋 Viewlet 完成訊息中文字的色彩。\",\n\t\t\"在顯示動態顯示的文字下醒目提示。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"編輯器動態顯示的背景色彩。\",\n\t\t\"編輯器動態顯示的前景色彩。\",\n\t\t\"編輯器動態顯示的框線色彩。\",\n\t\t\"編輯器暫留狀態列的背景色彩。\",\n\t\t\"使用中之連結的色彩。\",\n\t\t\"內嵌提示的前景色彩\",\n\t\t\"內嵌提示的背景色彩\",\n\t\t\"類型內嵌提示的前景色彩\",\n\t\t\"類型內嵌提示的背景色彩\",\n\t\t\"參數內嵌提示的前景色彩\",\n\t\t\"參數內嵌提示的背景色彩\",\n\t\t\"用於燈泡動作圖示的色彩。\",\n\t\t\"用於燈泡自動修正動作圖示的色彩。\",\n\t\t\"燈泡 AI 圖示使用的色彩。\",\n\t\t\"已插入文字的背景色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"已移除文字的背景色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"已插入程式行的背景色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"已移除程式行的背景色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"插入程式行所在邊界的背景色彩。\",\n\t\t\"移除程式行所在邊界的背景色彩。\",\n\t\t\"插入內容的差異概觀尺規前景。\",\n\t\t\"移除內容的差異概觀尺規前景。\",\n\t\t\"插入的文字外框色彩。\",\n\t\t\"移除的文字外框色彩。\",\n\t\t\"兩個文字編輯器之間的框線色彩。\",\n\t\t\"Diff 編輯器的斜紋填滿色彩。斜紋填滿用於並排 Diff 檢視。\",\n\t\t\"Diff 編輯器中未變更區塊的背景色彩。\",\n\t\t\"Diff 編輯器中未變更區塊的前景色彩。\",\n\t\t\"Diff 編輯器中未變更程式碼的背景色彩。\",\n\t\t\"當清單/樹狀為使用中狀態時，焦點項目的清單/樹狀背景色彩。使用中的清單/樹狀有鍵盤焦點，非使用中者則沒有。\",\n\t\t\"當清單/樹狀為使用中狀態時，焦點項目的清單/樹狀前景色彩。使用中的清單/樹狀有鍵盤焦點，非使用中者則沒有。\",\n\t\t\"當清單/樹狀目錄為使用中狀態時，焦點項目的清單/樹狀目錄外框色彩。使用中的清單/樹狀目錄有鍵盤焦點，非使用中者則沒有。\",\n\t\t\"當清單/樹狀目錄為使用中狀態並已選取時，焦點項目的清單/樹狀目錄外框色彩。使用中的清單/樹狀目錄具有鍵盤焦點，非使用中者則沒有。\",\n\t\t\"當清單/樹狀為使用中狀態時，所選項目的清單/樹狀背景色彩。使用中的清單/樹狀有鍵盤焦點，非使用中者則沒有。\",\n\t\t\"當清單/樹狀為使用中狀態時，所選項目的清單/樹狀前景色彩。使用中的清單/樹狀有鍵盤焦點，非使用中者則沒有。\",\n\t\t\"當清單/樹狀為使用中狀態時，所選項目的清單/樹狀圖示前景色彩。使用中的清單/樹狀有鍵盤焦點，非使用中者則沒有。\",\n\t\t\"當清單/樹狀為非使用中狀態時，所選項目的清單/樹狀背景色彩。使用中的清單/樹狀有鍵盤焦點，非使用中者則沒有。\",\n\t\t\"當清單/樹狀為使用中狀態時，所選項目的清單/樹狀前景色彩。使用中的清單/樹狀有鍵盤焦點，非使用中則沒有。\",\n\t\t\"當清單/樹狀為非使用中狀態時，所選項目的清單/樹狀圖示前景色彩。使用中的清單/樹狀有鍵盤焦點，非使用中者則沒有。\",\n\t\t\"當清單/樹狀為非使用中狀態時，焦點項目的清單/樹狀背景色彩。使用中的清單/樹狀有鍵盤焦點，非使用中者則沒有。\",\n\t\t\"當清單/樹狀目錄為非使用中狀態時，焦點項目的清單/樹狀目錄外框色彩。使用中的清單/樹狀目錄有鍵盤焦點，非使用中者則沒有。\",\n\t\t\"使用滑鼠暫留在項目時的清單/樹狀背景。\",\n\t\t\"滑鼠暫留在項目時的清單/樹狀前景。\",\n\t\t\"使用滑鼠四處移動項目時的清單/樹狀拖放背景。\",\n\t\t\"在清單/樹狀內搜尋時，相符醒目提示的清單/樹狀前景色彩。\",\n\t\t\"在清單/樹狀內搜尋時，相符項目的清單/樹狀結構前景色彩會針對主動焦點項目進行強調顯示。\",\n\t\t\"列表/樹狀 無效項目的前景色彩，例如在瀏覽視窗無法解析的根目錄\",\n\t\t\"包含錯誤清單項目的前景色彩\",\n\t\t\"包含警告清單項目的前景色彩\",\n\t\t\"清單和樹狀結構中類型篩選小工具的背景色彩。\",\n\t\t\"清單和樹狀結構中類型篩選小工具的大綱色彩。\",\n\t\t\"在沒有相符項目時，清單和樹狀結構中類型篩選小工具的大綱色彩。\",\n\t\t\"清單和樹狀結構中類型篩選小工具的陰影色彩。\",\n\t\t\"已篩選相符項的背景色彩。\",\n\t\t\"已篩選相符項的框線色彩。\",\n\t\t\"縮排輔助線的樹狀筆觸色彩。\",\n\t\t\"非使用中縮排輔助線的樹狀筆觸色彩。\",\n\t\t\"資料行之間的資料表邊界色彩。\",\n\t\t\"奇數資料表資料列的背景色彩。\",\n\t\t\"已取消強調的清單/樹狀結構前景色彩。\",\n\t\t\"核取方塊小工具的背景色彩。\",\n\t\t\"選取其所處元素時，核取方塊小工具的背景色彩。\",\n\t\t\"核取方塊小工具的前景色彩。\",\n\t\t\"核取方塊小工具的框線色彩。\",\n\t\t\"選取其所處元素時，核取方塊小工具的框線色彩。\",\n\t\t\"請改用 quickInputList.focusBackground\",\n\t\t\"焦點項目的快速選擇器前景色彩。\",\n\t\t\"焦點項目的快速選擇器圖示前景色彩。\",\n\t\t\"焦點項目的快速選擇器背景色彩。\",\n\t\t\"功能表的邊框色彩。\",\n\t\t\"功能表項目的前景色彩。\",\n\t\t\"功能表項目的背景色彩。\",\n\t\t\"功能表中所選功能表項目的前景色彩。\",\n\t\t\"功能表中所選功能表項目的背景色彩。\",\n\t\t\"功能表中所選功能表項目的框線色彩。\",\n\t\t\"功能表中分隔線功能表項目的色彩。\",\n\t\t\"使用滑鼠將游標停留在動作上方時的工具列背景\",\n\t\t\"使用滑鼠將游標停留在動作上方時的工具列外框\",\n\t\t\"將滑鼠移到動作上方時的工具列背景\",\n\t\t\"程式碼片段定位停駐點的反白顯示背景色彩。\",\n\t\t\"程式碼片段定位停駐點的反白顯示邊界色彩。\",\n\t\t\"程式碼片段最終定位停駐點的反白顯示背景色彩。\",\n\t\t\"程式碼片段最終定位停駐點的醒目提示框線色彩。\",\n\t\t\"焦點階層連結項目的色彩。\",\n\t\t\"階層連結的背景色。\",\n\t\t\"焦點階層連結項目的色彩。\",\n\t\t\"所選階層連結項目的色彩。\",\n\t\t\"階層連結項目選擇器的背景色彩。\",\n\t\t\"內嵌合併衝突中目前的標頭背景。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"內嵌合併衝突中的目前內容背景。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"內嵌合併衝突中的傳入標頭背景。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"內嵌合併衝突中的傳入內容背景。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"內嵌合併衝突中的一般上階標頭背景。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"內嵌合併衝突中的一般上階內容背景。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"內嵌合併衝突中標頭及分隔器的邊界色彩。\",\n\t\t\"目前內嵌合併衝突的概觀尺規前景。\",\n\t\t\"傳入內嵌合併衝突的概觀尺規前景。\",\n\t\t\"內嵌合併衝突中的共同上階概觀尺規前景。\",\n\t\t\"尋找相符項目的概觀尺規標記色彩。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"選取項目醒目提示的概觀尺規標記。其不得為不透明色彩，以免隱藏底層裝飾。\",\n\t\t\"用於尋找相符項目的縮圖標記色彩。\",\n\t\t\"重複編輯器選取項目的縮圖標記色彩。\",\n\t\t\"編輯器選取範圍的迷你地圖標記色彩。\",\n\t\t\"資訊的縮圖標記色彩。\",\n\t\t\"警告的縮圖標記色彩。\",\n\t\t\"錯誤的縮圖標記色彩。\",\n\t\t\"縮圖背景色彩。\",\n\t\t\"在縮圖中呈現的前景元素不透明度。例如，\\\"#000000c0\\\" 會以不透明度 75% 轉譯元素。\",\n\t\t\"縮圖滑桿背景色彩。\",\n\t\t\"暫留時的縮圖滑桿背景色彩。\",\n\t\t\"按一下時的縮圖滑桿背景色彩。\",\n\t\t\"用於問題錯誤圖示的色彩。\",\n\t\t\"用於問題警告圖示的色彩。\",\n\t\t\"用於問題資訊圖示的色彩。\",\n\t\t\"圖表中使用的前景色彩。\",\n\t\t\"用於圖表中水平線的色彩。\",\n\t\t\"圖表視覺效果中所使用的紅色。\",\n\t\t\"圖表視覺效果中所使用的藍色。\",\n\t\t\"圖表視覺效果中所使用的黃色。\",\n\t\t\"圖表視覺效果中所使用的橙色。\",\n\t\t\"圖表視覺效果中所使用的綠色。\",\n\t\t\"圖表視覺效果中所使用的紫色。\",\n\t],\n\t\"vs/platform/theme/common/iconRegistry\": [\n\t\t\"要使用的字型識別碼。如未設定，就會使用最先定義的字型。\",\n\t\t\"與圖示定義建立關聯的字型字元。\",\n\t\t\"小工具中關閉動作的圖示。\",\n\t\t\"移至上一個編輯器位置的圖示。\",\n\t\t\"移至下一個編輯器位置的圖示。\",\n\t],\n\t\"vs/platform/undoRedo/common/undoRedoService\": [\n\t\t\"已在磁碟上關閉並修改以下檔案: {0}。\",\n\t\t\"下列檔案已使用不相容的方式修改: {0}。\",\n\t\t\"無法復原所有檔案的 \\'{0}\\'。{1}\",\n\t\t\"無法復原所有檔案的 \\'{0}\\'。{1}\",\n\t\t\"因為已對 {1} 進行變更，所以無法復原所有檔案的 \\'{0}\\'\",\n\t\t\"因為 {1} 中已經有正在執行的復原或重做作業，所以無法為所有檔案復原 \\'{0}\\'\",\n\t\t\"因為同時發生其他復原或重做作業，所以無法為所有檔案復原 \\'{0}\\'\",\n\t\t\"要復原所有檔案的 \\'{0}\\' 嗎?\",\n\t\t\"在 {0} 個檔案中復原(&&U)\",\n\t\t\"復原此檔案(&&F)\",\n\t\t\"因為已經有正在執行的復原或重做作業，所以無法復原 \\'{0}\\'。\",\n\t\t\"要復原 \\'{0}\\' 嗎?\",\n\t\t\"是(&&Y)\",\n\t\t\"否\",\n\t\t\"無法復原所有檔案的 \\'{0}\\'。{1}\",\n\t\t\"無法復原所有檔案的 \\'{0}\\'。{1}\",\n\t\t\"因為已對 {1} 進行變更，所以無法復原所有檔案的 \\'{0}\\'\",\n\t\t\"因為 {1} 中已經有正在執行的復原或重做作業，所以無法為所有檔案重做 \\'{0}\\'\",\n\t\t\"因為同時發生其他復原或重做作業，所以無法為所有檔案重做 \\'{0}\\'\",\n\t\t\"因為已經有正在執行的復原或重做作業，所以無法重做 \\'{0}\\'。\",\n\t],\n\t\"vs/platform/workspace/common/workspace\": [\n\t\t\"Code 工作區\",\n\t]\n});"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DAOA,OAAO,kCAAmC,CACzC,+CAAgD,CAC/C,WACD,EACA,yCAA0C,CACzC,cACD,EACA,gDAAiD,CAChD,uCACA,6CACA,4CACD,EACA,4CAA6C,CAC5C,eACA,gCACD,EACA,uCAAwC,CACvC,kGACA,kPACD,EACA,8CAA+C,CAC9C,6BACD,EACA,uCAAwC,CACvC,oBACA,oBACA,oBACA,yDACA,oDACA,gCACD,EACA,qDAAsD,CACrD,oBACD,EACA,+CAAgD,CAC/C,0BACD,EACA,qCAAsC,CACrC,0BACD,EACA,uCAAwC,CACvC,eACA,2BACA,uCACA,uCACA,uCACA,eACA,kDACD,EACA,yBAA0B,CACzB,gBACD,EACA,8BAA+B,CAC9B,WACA,6CACA,uIACA,uIACA,4CACA,sIACD,EACA,kCAAmC,CAClC,OACA,QACA,MACA,UACA,OACA,QACA,MACA,qBACA,UACA,QACA,eACA,eACA,UACA,QACA,MACA,UACA,UACA,QACA,MACA,oBACD,EACA,0BAA2B,CAC1B,GACD,EACA,+CAAgD,CAC/C,qBACA,qEACA,6HACA,oYACA,ySACD,EACA,iCAAkC,CACjC,mGACA,mGACA,wDACD,EACA,qCAAsC,CACrC,oBACA,eACA,gCACA,eACA,oBACA,cACD,EACA,4CAA6C,CAC5C,2VACA,kDACD,EACA,2DAA4D,CAC3D,iGACA,iGACA,iGACA,eACA,uIACA,uCACA,8BACA,gCACA,4HACA,eACA,mCACA,0DACA,+BACA,8BACD,EACA,6CAA8C,CAC7C,yGACA,2HACA,wGACD,EACA,kDAAmD,CAClD,4FACA,2FACD,EACA,8DAA+D,CAC9D,+DACA,2EACA,uFACA,qEACA,+DACA,0BACA,2BACA,uCACA,+DACA,+DACA,mDACA,6CACA,+DACA,4CACD,EACA,4DAA6D,CAC5D,6CACA,0BACD,EACA,wDAAyD,CACxD,gFACD,EACA,kEAAmE,CAClE,mDACA,mGACA,mDACA,mGACA,+BACA,sCACD,EACA,kEAAmE,CAClE,6CACA,6CACA,uCACA,uCACA,6DACA,6CACA,gCACD,EACA,uDAAwD,CACvD,gGACA,gGACA,qDACA,2DACD,EACA,wDAAyD,CACxD,mEACD,EACA,oDAAqD,CACpD,qBACA,qLACA,wQACA,2KACA,4JACA,uFACA,+JACA,6CACA,6FACA,iIACA,mGACA,uLACA,yGACA,yGACA,qKACA,6GACA,0IACA,sHACA,gIACA,iIACA,gPACA,6FACA,qEACA,qEACA,2KACA,qEACA,qEACA,kJACA,yIACA,kGACA,uIACA,uIACA,8JACA,sIACA,6IACA,wEACA,uCACA,yDACA,oEACA,+DACA,qEACA,+GACA,iFACA,6FACA,6IACA,uIACA,kMACD,EACA,wCAAyC,CACxC,wGACA,yGACA,2EACA,uJACA,mGACA,2KACA,6IACA,6IACA,+GACA,qKACA,2HACA,6JACA,+GACA,gGACA,oIACA,mHACA,0JACA,oTACA,6MACA,6OACA,yMACA,mPACA,6PACA,mOACA,uSACA,4DACA,wKACA,4MACA,wEACA,iFACA,qHACA,iNACA,yIACA,8IACA,0IACA,8IACA,yIACA,uKACA,4KACA,wKACA,2KACA,sKACA,yDACA,kHACA,qHACA,sKACA,+GACA,0UACA,ySACA,uOACA,6FACA,iDACA,iKACA,6KACA,iKACA,2HACA,uFACA,iXACA,iIACA,2EACA,6CACA,8GACA,8GACA,6CACA,8QACA,qLACA,+GACA;AAAA;AAAA;AAAA,wGACA,yDACA,2EACA,wHACA,gJACA,gJACA,mDACA,qEACA,iFACA,8FACA,+GACA,qHACA,qHACA,2HACA,mJACA,iIACA,uFACA,iFACA,6CACA,iFACA,iFACA,qHACA,wgBACA,uCACA,yDACA,uFACA,iDACA,mDACA,yGACA,+DACA,+SACA,uFACA,yDACA,yDACA,2EACA,uFACA,yDACA,yDACA,2EACA,mDACA,mDACA,uIACA,iIACA,6RACA,yJACA,8OACA,8HACA,8HACA,iFACA,oIACA,qHACA,2HACA,yJACA,2EACA,uFACA,2QACA,8JACA,iIACA,+DACA,+GACA,+DACA,uFACA,mJACA,yGACA,2EACA,yGACA,iIACA,mGACA,uFACA,qKACA,mGACA,uIACA,6FACA,uFACA,qNACA,qHACA,mGACA,0MACA,yFACA,yFACA,6HACA,uHACA,yZACA,uIACA,6FACA,yGACA,yGACA,2NACA,qHACA,uKACA,8FACA,8FACA,0GACA,oGACA,2aACA,8FACA,8FACA,8FACA,8FACA,8FACA,8FACA,8FACA,8FACA,oGACA,8FACA,wFACA,8FACA,8FACA,4FACA,oGACA,8FACA,8FACA,8FACA,8FACA,6FACA,oGACA,+FACA,gHACA,yFACA,qFACA,+GACA,qHACA,8EACA,2EACA,4DACA,sDACA,mDACA,iOACA,2NACA,2HACA,qKACA,mGACA,iOACA,2HACA,qKACA,4QACA,8HACA,qOACA,iXACA,iCACA,yGACA,yGACA,qHACA,qKACA,yGACA,qHACA,iLACA,6IACA,mJACA,uIACA,qHACA,yGACA,qHACA,iLACA,2EACA,iFACA,6IACA,0MACA,wRACA,mMACA,2HACA,iFACA,kFACA,+JACA,6OACA,wEACA,6DACA,oKACA,yJACA,mJACA,+GACA,qHACA,2HACA,mJACA,2HACA,+GACA,qEACA,qEACA,uLACA,2EACA,8FACA,yDACA,uSACA,iIACA,gEACA,iGACA,iHACA,iIACA,8DACA,6FACA,yDACA,iIACA,4EACA,yGACA,4JACA,+DACA,iFACA,qHACA,yGACA,6RACA,6IACA,6CACA,iUACA,iIACA,uLACA,mGACA,4DACA,+NACA,2HACA,qEACA,uHACA,6HACA,iFACA,8FACA,uFACA,4YACA,iFACA,qEACA,6IACA,iIACA,+DACA,mGACA,mIACA,2HACA,yGACA,yDACA,mDACA,iIACA,yJACA,8HACA,+GACA,0EACA,6FACA,2HACA,iFACA,+GACA,iLACA,2HACA,iFACA,+DACA,6FACA,qEACA,mGACA,yGACA,yPACA,kFACA,yJACA,+DACA,+GACA,iIACA,+GACA,2EACA,qEACA,qHACA,qHACA,+GACA,qEACA,yJACA,uFACA,yMACA,6JACA,iLACA,2HACA,+DACA,sPACA,6KACA,qHACA,sGACA,wDACA,wLACA,wDACA,iFACA,qEACA,6FACA,yGACA,qHACA,2EACA,kNACA,wHACA,yJACA,uCACA,yDACA,sDACA,wHACA,6CACA,iKACA,yJACA,0KACD,EACA,4CAA6C,CAC5C,yGACA,+GACA,qQACA,mGACA,gTACA,uFACA,yDACA,qKACA,2EACA,yDACA,2EACA,wHACA,6FACA,oIACA,+EACA,+EACA,+EACA,+EACA,+EACA,+EACA,iGACA,iGACA,iGACA,iGACA,iGACA,iGACA,qEACA,2FACA,qEACA,+IACA,mDACA,iFACA,mDACA,mDACA,wFACA,iFACA,yIACA,qIACA,8eACA,6FACA,6FACA,6FACA,qNACA,2EACA,2EACA,2EACA,gIACA,gIACA,gIACA,gIACA,gIACA,gIACA,qEACA,sLACA,sLACA,sLACA,sLACA,sLACA,sLACA,gLACA,gLACA,gLACA,gLACA,gLACA,gLACA,gGACA,+FACD,EACA,qCAAsC,CACrC,0FACA,4KACA,qGACA,mDACA,yDACA,qEACA,yDACA,mGACA,wDACA,mGACA,2EACA,qHACA,0DACA,+DACA,2EACA,2EACA,yDACA,yDACA,mDACA,mDACA,2EACA,2EACA,8HACA,yDACA,iFACA,uFACA,mEACA,qEACA,qEACA,qEACA,iFACA,qEACA,6FACA,iFACA,qEACA,iFACA,iFACA,iFACA,uFACA,+GACA,mGACA,0HACD,EACA,6BAA8B,CAC7B,eACA,qBACA,eACA,eACA,2BACA,eACA,2BACA,eACA,eACA,eACA,eACA,eACA,qBACA,eACA,eACA,2BACA,OACA,eACA,eACA,qBACA,eACA,eACA,eACA,eACA,2BACA,eACA,WACD,EACA,2CAA4C,CAC3C,oBACD,EACA,mCAAoC,CACnC,0BACD,EACA,qCAAsC,CACrC,qDACA,+BACA,qEACA,iCACA,6CACA,8BACA,mDACA,iCACA,6EACA,yDACA,wEACD,EACA,+CAAgD,CAC/C,iCACA,WACD,EACA,sDAAuD,CACtD,uCACA,yCACA,mDACA,mDACA,iFACA,kDACD,EACA,4DAA6D,CAC5D,uFACA,iCACA,iCACA,2BACA,gCACA,kGACD,EACA,4DAA6D,CAC5D,yDACA,wDACD,EACA,sDAAuD,CACtD,0BACD,EACA,gDAAiD,CAChD,oBACA,eACA,eACA,eACA,oBACA,eACA,eACA,eACA,qBACA,qBACA,eACA,eACA,eACA,oBACA,eACA,eACA,eACA,wDACD,EACA,kDAAmD,CAClD,4FACD,EACA,0DAA2D,CAC1D,2EACA,iFACA,mGACA,uIACA,iFACA,yGACA,8BACA,+DACA,sEACA,sEACA,2EACA,+DACA,kBACA,4EACA,oDACA,yDACA,6CACA,8BACA,kFACA,gEACA,qEACA,yDACA,2BACA,iFACA,2BACA,+DACA,8BACA,wDACD,EACA,+DAAgE,CAC/D,sIACA,sJACD,EACA,4DAA6D,CAC5D,4DACA,6CACA,4CACD,EACA,sDAAuD,CACtD,8BACA,2BACA,eACA,eACA,eACA,eACA,iCACA,0BACD,EACA,uDAAwD,CACvD,+GACA,mDACA,6CACA,6CACA,uCACA,8BACD,EACA,wDAAyD,CACxD,8DACA,0BACD,EACA,0DAA2D,CAC1D,6EACA,oEACD,EACA,qEAAsE,CACrE,iFACA,mFACA,6CACA,sFACD,EACA,4CAA6C,CAC5C,iCACA,sCACA,iCACA,iCACA,uCACA,2CACD,EACA,oDAAqD,CACpD,eACA,2BACA,2BACA,qBACA,eACA,2BACA,eACA,uCACA,eACA,8DACD,EACA,kDAAmD,CAClD,2BACA,0BACD,EACA,kEAAmE,CAClE,wBACA,4LACD,EACA,gEAAiE,CAChE,yDACA,0CACA,yGACA,uCACA,kDACD,EACA,6DAA8D,CAC7D,eACA,iCACA,mBACA,mBACA,2BACA,2BACA,uCACA,sCACD,EACA,uEAAwE,CACvE,sIACD,EACA,qEAAsE,CACrE,yDACA,0CACA,wGACD,EACA,+DAAgE,CAC/D,sIACD,EACA,gDAAiD,CAChD,mGACA,eACA,oBACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA,uCACA,uCACA,iCACA,iCACA,0CACA,mGACA,6GACA,mFACA,mFACA,yDACA,yDACA,eACA,mBACD,EACA,4CAA6C,CAC5C,qIACA,yGACA,yGACA,iGACA,6GACA,mHACA,mHACA,4BACA,eACA,eACA,6CACA,6CACA,mDACA,eACA,eACA,eACA,eACA,2BACA,2BACA,0KACA,iBACA,2BACA,mBACA,gCACA,sDACA,sCACA,2QACD,EACA,4CAA6C,CAC5C,eACA,6CACA,eACA,2BACA,6CACA,mDACA,uCACA,uCACA,iFACA,iFACA,2BACA,2BACA,uCACA,yDACA,yDACA,qEACA,mDACA,8BACD,EACA,uDAAwD,CACvD,yMACA,6FACA,6GACA,6GACA,+GACA,8GACD,EACA,8CAA+C,CAC9C,6CACA,6CACA,wDACD,EACA,iDAAkD,CACjD,iCACA,4CACD,EACA,gDAAiD,CAChD,wFACA,wEACA,wFACA,wEACA,sHACA,sCACA,sHACA,qCACD,EACA,sDAAuD,CACtD,eACA,eACA,eACA,eACA,uBACA,6CACA,6CACA,mGACA,yGACA,mGACA,yGACA,6FACA,yGACA,sFACD,EACA,oDAAqD,CACpD,eACA,eACA,8CACA,6CACA,2BACA,gCACA,6CACA,2BACA,eACA,+CACA,6CACA,2BACA,gCACA,+CACA,6CACA,2BACA,2BACA,sEACA,yDACA,uCACA,4CACA,uCACA,eACA,0DACA,6CACA,2BACA,gCACA,2BACA,8CACA,iCACA,2BACA,gCACA,eACA,2BACA,eACA,uCACA,eACA,iCACA,cACD,EACA,qEAAsE,CACrE,mEACD,EACA,iEAAkE,CACjE,mJACA,8BACA,WACD,EACA,2DAA4D,CAC3D,yBACA,yBACA,cACD,EACA,6DAA8D,CAC7D,2BACA,2BACA,cACD,EACA,uDAAwD,CACvD,4DACA,uEACA,qEACA,uEACA,iCACA,qDACA,uDACA,wEACD,EACA,wDAAyD,CACxD,yGACA,+DACA,4BACD,EACA,wCAAyC,CACxC,6CACA,2EACA,6FACA,iFACA,mDACA,uCACA,uCACA,uCACA,uCACA,iCACA,iCACA,uCACA,sCACD,EACA,2DAA4D,CAC3D,8BACA,wKACA,uLACD,EACA,yDAA0D,CACzD,2BACA,yDACA,sDACA,yDACA,6BACD,EACA,0DAA2D,CAC1D,6CACA,4CACD,EACA,oDAAqD,CACpD,mDACA,yDACA,yDACA,mDACA,yDACA,2EACA,4CACA,yDACA,+DACA,mDACA,uCACA,wDACD,EACA,uDAAwD,CACvD,uCACA,2BACA,4BACA,oCACA,2BACA,kHACA,iCACA,0BACD,EACA,uDAAwD,CACvD,yDACA,yDACA,uCACA,qEACA,2BACA,+DACA,qBACA,uCACA,eACA,uCACA,4CACD,EACA,+DAAgE,CAC/D,eACD,EACA,0EAA2E,CAC1E,mDACA,iFACA,kJACA,sFACD,EACA,0EAA2E,CAC1E,gFACD,EACA,2EAA4E,CAC3E,oFACA,oFACA,YACA,qBACA,oBACD,EACA,wDAAyD,CACxD,kDACD,EACA,4DAA6D,CAC5D,uCACA,4CACA,uCACA,4CACA,uCACA,4CACA,2BACA,gCACA,2BACA,gCACA,iCACA,iCACA,uCACA,uCACA,qBACA,qBACA,qBACA,uCACA,uCACA,uCACA,mDACA,qBACA,+DACA,iCACA,iCACA,6CACA,mDACA,wCACA,+BACD,EACA,wDAAyD,CACxD,6CACA,oHACD,EACA,wCAAyC,CACxC,oHACA,yGACA,2BACA,2BACA,2BACA,4BACA,oCACA,2BACA,+BACA,0BACD,EACA,sDAAuD,CACtD,4FACD,EACA,oDAAqD,CACpD,kDACA,kDACA,6CACA,kDACA,6CACA,kDACA,6CACA,kDACA,oDACA,mDACA,mGACA,kDACA,yGACA,kDACA,2HACA,2HACA,qEACA,4CACA,mDACA,6CACA,6CACA,6CACA,4CACD,EACA,0DAA2D,CAC1D,sCACD,EACA,gEAAiE,CAChE,oFACA,oFACA,wBACA,kGACD,EACA,8CAA+C,CAC9C,yGACA,eACA,uFACA,+DACA,2EACA,uFACA,6FACA,yGACA,+GACA,uIACA,uIACA,iFACA,+IACA,+GACA,2HACA,qHACA,0HACD,EACA,4DAA6D,CAC5D,6FACA,yEACA,sCACA,qKACA,wHACD,EACA,8DAA+D,CAC9D,mJACA,yGACA,+DACA,6CACA,2BACA,iCACA,qBACA,qBACA,qBACA,qBACA,iCACA,qBACA,qBACA,qBACA,qBACA,2BACA,qBACA,iCACA,qBACA,iCACA,qBACA,qBACA,qBACA,iCACA,qBACA,qBACA,qBACA,qBACA,2BACA,qBACA,2BACA,qBACA,oBACD,EACA,yDAA0D,CACzD,+DACA,oEACD,EACA,0CAA2C,CAC1C,iCACA,mGACA,gEACA,4DACA,6FACA,yDACA,+DACA,uCACA,yGACD,EACA,oDAAqD,CACpD,iFACA,8IACA,0FACD,EACA,oDAAqD,CACpD,uCACA,4CACA,uCACA,2CACD,EACA,uDAAwD,CACvD,mGACA,2HACA,2HACA,2DACD,EACA,qDAAsD,CACrD,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,eACA,eACA,eACA,eACA,eACA,eACA,eACA,eACA,eACA,eACA,eACA,eACA,eACA,eACA,eACA,eACA,eACA,qBACA,qBACA,UACA,UACA,WACA,UACA,eACA,UACA,WACA,WACA,WACA,YACA,YACA,WACD,EACA,6DAA8D,CAC7D,uCACA,4CACA,2BACA,gCACA,uCACA,4CACA,+DACA,+DACA,+DACA,gCACD,EACA,4CAA6C,CAC5C,mDACA,+DACA,qEACA,iIACA,sEACA,6FACA,+DACA,wGACD,EACA,sDAAuD,CACtD,qFACA,2BACA,eACA,eACA,eACA,eACA,eACA,2BACA,2BACA,wDACD,EACA,kDAAmD,CAClD,qEACA,qEACA,qEACA,mGACA,+GACA,mGACA,mGACA,iLACA,iFACA,8BACA,2BACA,eACA,mBACA,UACA,eACA,4BACD,EACA,yDAA0D,CACzD,eACA,6BACD,EACA,0DAA2D,CAC1D,uGACA,0BACD,EACA,wDAAyD,CACxD,WACD,EACA,oDAAqD,CACpD,yMACA,+MACA,yMACA,yMACA,yMACA,qNACA,+MACA,2NACA,yMACA,yMACA,yMACA,+MACA,yMACA,yMACA,+MACA,+MACA,yMACA,yMACA,qNACA,kMACA,yMACA,yMACA,+MACA,yMACA,yMACA,yMACA,2NACA,yMACA,yMACA,yMACA,qNACA,yMACA,wMACD,EACA,kEAAmE,CAClE,kDACA,sIACA,yEACD,EACA,sDAAuD,CACtD,gEACD,EACA,kEAAmE,CAClE,2HACA,0FACA,0FACA,oFACA,8JACA,uJACA,sCACA,2EACA,2BACA,+DACA,2EACA,+DACA,2EACA,+DACA,2EACA,mDACA,qEACA,oDACA,kFACA,uCACA,6EACA,8DACA,yGACA,2DACD,EACA,0EAA2E,CAC1E,mDACA,qEACA,kYACA,oEACA,cACD,EACA,iEAAkE,CACjE,2OACA,2OACA,6LACA,qIACA,sIACA,2EACA,qNACA,6OACA,oNACD,EACA,4DAA6D,CAC5D,yDACA,yDACA,kDACD,EACA,0DAA2D,CAC1D,0BACD,EACA,mDAAoD,CACnD,eACA,eACA,eACA,eACA,2BACA,0BACD,EACA,8CAA+C,CAC9C,qDACA,yBACA,yCACA,gCACD,EACA,gDAAiD,CAChD,mGACA,qEACA,6CACA,6CACA,6CACA,6CACA,4CACD,EACA,sDAAuD,CACtD,YACA,YACA;AAAA,UACD,EACA,sCAAuC,CACtC,eACA,gCACD,EACA,yCAA0C,CACzC,oBACD,EACA,gDAAiD,CAChD,uCACA,iCACA,6CACA,uCACA,6CACA,6CACA,+DACA,mDACA,2BACA,2BACA,6CACA,qBACA,gDACA,0CACA,uCACA,uCACA,uCACA,6CACA,6CACA,6CACA,eACA,eACA,cACD,EACA,yDAA0D,CACzD,mDACA,sFACA,mGACA,yGACA,mGACA,yGACA,mDACA,wPACA,uFACA,2GACD,EACA,mDAAoD,CACnD,sFACD,EACA,2CAA4C,CAC3C,+DACA,yMACA,gCACA,yBACA,uCACA,yFACA,yDACA,sEACA;AAAA,0BACD,EACA,4CAA6C,CAC5C,mDACA,mDACA,qDACA,+DACA,mGACA,iDACA,2EACA,yCACA,gFACD,EACA,wCAAyC,CACxC,4BACA,qCACA,8CACA,4EACA,kMACD,EACA,yDAA0D,CACzD,sCACD,EACA,0DAA2D,CAC1D,oFACA,gGACA,qEACA,oEACD,EACA,uCAAwC,CACvC,qBACA,8FACA,uFACA,6bACA,qUACA,yOACA,uFACA,wEACA,6FACA,+GACA,uHACA,4EACA,qNACA,mDACA,0HACA,yJACA,iRACA,yJACA,oNACA,mHACA,+DACA,+DACA,+JACA,uUACA,mGACA,4JACA,mTACD,EACA,qCAAsC,CACrC,eACA,eACA,cACD,EACA,qDAAsD,CACrD,iCACA,iCACA,2BACA,2BACA,iCACA,WACA,6CACD,EACA,iDAAkD,CACjD,UACD,EACA,4CAA6C,CAC5C,qBACA,iGACA,UACA,8DACD,EACA,sDAAuD,CACtD,mDACA,yBACA,yBACA,eACA,eACA,qBACA,cACD,EACA,gDAAiD,CAChD,0BACD,EACA,iDAAkD,CACjD,kEACD,EACA,yCAA0C,CACzC,2KACA,uLACA,mMACA,8FACA,2EACA,+MACA,uLACA,yMACA,yOACA,+DACA,yDACA,iIACA,iFACA,6FACA,qEACA,+DACA,+DACA,kIACA,kIACA,mDACA,mDACA,mDACA,yGACA,yGACA,6FACA,yGACA,uFACA,6FACA,6FACA,6FACA,6FACA,6FACA,6FACA,6FACA,6FACA,6FACA,yDACA,yDACA,yDACA,yDACA,6CACA,mDACA,6CACA,qEACA,6CACA,yDACA,yDACA,6FACA,4KACA,4KACA,uFACA,+DACA,6FACA,uFACA,kFACA,yMACA,wFACA,iIACA,yMACA,wFACA,iIACA,yMACA,uFACA,iIACA,mGACA,iIACA,qEACA,yDACA,qEACA,2EACA,mGACA,sHACA,oHACA,mPACA,ySACA,+MACA,+MACA,qNACA,iFACA,iFACA,qKACA,qKACA,qKACA,iLACA,qEACA,2EACA,qNACA,qNACA,yGACA,+DACA,6LACA,uLACA,2EACA,2EACA,mMACA,iFACA,6FACA,0FACA,qNACA,iFACA,iFACA,iFACA,uFACA,+DACA,yDACA,yDACA,qEACA,qEACA,qEACA,qEACA,2EACA,mGACA,mEACA,uLACA,uLACA,6LACA,6LACA,6FACA,6FACA,uFACA,uFACA,+DACA,+DACA,6FACA,kJACA,kGACA,kGACA,wGACA,kTACA,kTACA,sVACA,oXACA,kTACA,kTACA,8TACA,wTACA,4SACA,oUACA,wTACA,4VACA,gHACA,oGACA,kIACA,iKACA,2PACA,mLACA,iFACA,iFACA,iIACA,iIACA,uLACA,iIACA,2EACA,2EACA,iFACA,yGACA,uFACA,uFACA,0GACA,iFACA,uIACA,iFACA,iFACA,uIACA,oDACA,6FACA,yGACA,6FACA,yDACA,qEACA,qEACA,yGACA,yGACA,yGACA,mGACA,iIACA,iIACA,mGACA,2HACA,2HACA,uIACA,uIACA,2EACA,yDACA,2EACA,2EACA,6FACA,+MACA,+MACA,+MACA,+MACA,2NACA,2NACA,qHACA,mGACA,mGACA,qHACA,qNACA,qNACA,mGACA,yGACA,yGACA,+DACA,+DACA,+DACA,6CACA,wMACA,yDACA,iFACA,uFACA,2EACA,2EACA,2EACA,qEACA,2EACA,uFACA,uFACA,uFACA,uFACA,uFACA,sFACD,EACA,wCAAyC,CACxC,qKACA,6FACA,2EACA,uFACA,sFACD,EACA,8CAA+C,CAC9C,kGACA,wGACA,wEACA,wEACA,sIACA,kMACA,2KACA,iEACA,uDACA,sCACA,+JACA,mCACA,cACA,SACA,wEACA,wEACA,sIACA,kMACA,2KACA,8JACD,EACA,yCAA0C,CACzC,yBACD,CACD,CAAC", "names": [], "file": "editor.main.nls.zh-tw.js"}
/* ===== 科技感联系页面样式 ===== */

/* 科技感英雄区域 */
.tech-hero-section {
    position: relative;
    min-height: 40vh;
    background: linear-gradient(180deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    overflow: hidden;
    display: flex;
    align-items: center;
    margin-top: -120px;
    padding-top: 180px;
    padding-bottom: 60px;
    margin-bottom: 0;
}

.tech-hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

/* 装饰元素 */
.hero-decorations {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
    pointer-events: none;
}

/* 装饰圆圈 */
.decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(79, 195, 247, 0.05);
    animation: float 12s ease-in-out infinite;
    border: 1px solid rgba(79, 195, 247, 0.1);
    transition: all 1s ease;
}

.circle-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.circle-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 4s;
}

.circle-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    right: 25%;
    animation-delay: 8s;
}

/* 浮动图标 */
.floating-icon {
    position: absolute;
    color: rgba(79, 195, 247, 0.8);
    font-size: 1.8rem;
    animation: floatIcon 15s ease-in-out infinite;
    background: rgba(79, 195, 247, 0.1);
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(79, 195, 247, 0.2);
}

.icon-1 {
    top: 35%;
    right: 20%;
    animation-delay: 0s;
}

.icon-2 {
    top: 60%;
    left: 15%;
    animation-delay: 3s;
}

.icon-3 {
    bottom: 10%;
    left: 20%;
    animation-delay: 6s;
}

.icon-4 {
    top: 45%;
    right: 40%;
    animation-delay: 9s;
}

.icon-5 {
    bottom: 5%;
    right: 35%;
    animation-delay: 12s;
}

/* 动画效果 */
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

@keyframes floatIcon {
    0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-15px) scale(1.1);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-10px) scale(1);
        opacity: 1;
    }
    75% {
        transform: translateY(-20px) scale(0.9);
        opacity: 0.7;
    }
}

.tech-grid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(79, 195, 247, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(79, 195, 247, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

.tech-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="%234fc3f7" opacity="0.6"><animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/></circle><circle cx="80" cy="40" r="1" fill="%2329b6f6" opacity="0.4"><animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite"/></circle><circle cx="40" cy="80" r="1" fill="%2381d4fa" opacity="0.5"><animate attributeName="opacity" values="0.5;1;0.5" dur="2.5s" repeatCount="indefinite"/></circle></svg>') repeat;
    background-size: 200px 200px;
    animation: particleFloat 15s linear infinite;
}

@keyframes particleFloat {
    0% { transform: translateY(0); }
    100% { transform: translateY(-200px); }
}



.tech-hero-content {
    position: relative;
    z-index: 10;
    color: white;
}

.tech-icon-wrapper {
    margin-bottom: 1rem;
    margin-top: 1rem;
}

.tech-main-icon {
    width: 80px;
    height: 80px;
    color: #4fc3f7;
    filter: drop-shadow(0 0 20px rgba(79, 195, 247, 0.5));
    animation: iconRotate 10s linear infinite;
}

@keyframes iconRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.tech-hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    margin-top: 1rem;
    text-shadow: 0 0 30px rgba(79, 195, 247, 0.5);
}

.tech-text-gradient {
    background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 50%, #81d4fa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { filter: hue-rotate(0deg); }
    50% { filter: hue-rotate(30deg); }
}

.tech-hero-subtitle {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.tech-breadcrumb {
    margin-top: 1rem;
    text-align: left;
}

.tech-breadcrumb-container {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border-radius: 30px;
    padding: 12px 30px;
    border: 1px solid rgba(79, 195, 247, 0.3);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    max-width: 1100px;
    width: 100%;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}

.tech-breadcrumb-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(79, 195, 247, 0.1), transparent);
    transition: all 0.8s ease;
}

.tech-breadcrumb-container:hover::before {
    left: 100%;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 2;
}

.breadcrumb-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 20px;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.95rem;
    position: relative;
    overflow: hidden;
}

.breadcrumb-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(79, 195, 247, 0.1);
    opacity: 0;
    transition: all 0.3s ease;
    border-radius: 30px;
}

.breadcrumb-link:hover::before {
    opacity: 1;
}

.breadcrumb-link:hover {
    color: #4fc3f7;
    text-shadow: 0 0 15px rgba(79, 195, 247, 0.5);
    transform: translateY(-2px);
}

.breadcrumb-current {
    color: #4fc3f7;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: rgba(79, 195, 247, 0.2);
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.95rem;
    text-shadow: 0 0 10px rgba(79, 195, 247, 0.4);
    box-shadow: 0 0 15px rgba(79, 195, 247, 0.2);
}

.breadcrumb-separator {
    margin: 0 12px;
    color: rgba(255, 255, 255, 0.5);
    display: flex;
    align-items: center;
    position: relative;
    z-index: 2;
}

.separator-icon {
    width: 14px;
    height: 14px;
    animation: separatorPulse 2s ease-in-out infinite;
    filter: drop-shadow(0 0 6px rgba(79, 195, 247, 0.3));
}

@keyframes separatorPulse {
    0%, 100% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.1); }
}

.breadcrumb-icon {
    width: 16px;
    height: 16px;
    filter: drop-shadow(0 0 6px rgba(79, 195, 247, 0.4));
}

/* 响应式设计 */
@media (max-width: 768px) {
    .tech-breadcrumb-container {
        min-width: 280px;
        padding: 10px 25px;
        border-radius: 25px;
    }

    .breadcrumb-link, .breadcrumb-current {
        padding: 6px 12px;
        font-size: 0.9rem;
        gap: 6px;
    }

    .breadcrumb-separator {
        margin: 0 10px;
    }

    .breadcrumb-icon {
        width: 14px;
        height: 14px;
    }

    .separator-icon {
        width: 12px;
        height: 12px;
    }

    .floating-icon {
        font-size: 1.5rem;
        width: 45px;
        height: 45px;
    }

    .decoration-circle {
        opacity: 0.6;
    }
}

@media (max-width: 480px) {
    .tech-breadcrumb-container {
        min-width: 220px;
        padding: 8px 20px;
    }

    .breadcrumb-link, .breadcrumb-current {
        padding: 5px 10px;
        font-size: 0.85rem;
        gap: 5px;
    }

    .breadcrumb-separator {
        margin: 0 8px;
    }

    .floating-icon {
        display: none;
    }

    .decoration-circle {
        opacity: 0.3;
    }
}

/* 科技感联系信息区域 */
.tech-contact-info-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
    overflow: hidden;
}

.tech-contact-info-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="tech-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%234fc3f7" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23tech-pattern)"/></svg>');
    animation: patternMove 30s linear infinite;
}

@keyframes patternMove {
    0% { transform: translateX(0); }
    100% { transform: translateX(100px); }
}

.tech-section-title {
    font-size: 2.8rem;
    font-weight: 700;
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
}

.tech-section-subtitle {
    font-size: 1.1rem;
    color: #64748b;
    margin-bottom: 0;
    position: relative;
    z-index: 2;
}

.tech-contact-card {
    position: relative;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    border: 1px solid rgba(79, 195, 247, 0.2);
    transition: all 0.4s ease;
    overflow: hidden;
    height: 100%;
}

.tech-contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(79, 195, 247, 0.1), transparent);
    transition: all 0.6s ease;
}

.tech-contact-card:hover::before {
    left: 100%;
}

.tech-contact-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(79, 195, 247, 0.2);
    border-color: rgba(79, 195, 247, 0.4);
}

.tech-card-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(79, 195, 247, 0.1), transparent);
    transform: translate(-50%, -50%);
    border-radius: 50%;
    opacity: 0;
    transition: all 0.4s ease;
}

.tech-contact-card:hover .tech-card-glow {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
}

.tech-contact-icon {
    position: relative;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    box-shadow: 0 10px 30px rgba(79, 195, 247, 0.3);
    transition: all 0.4s ease;
}

.tech-contact-icon i {
    width: 32px;
    height: 32px;
    color: white;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.tech-contact-card:hover .tech-contact-icon {
    transform: scale(1.1);
    box-shadow: 0 15px 40px rgba(79, 195, 247, 0.4);
}

.icon-pulse {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border: 2px solid rgba(79, 195, 247, 0.4);
    border-radius: 50%;
    animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
    0% { transform: scale(1); opacity: 1; }
    100% { transform: scale(1.3); opacity: 0; }
}

.tech-contact-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 15px;
}

.tech-contact-text {
    color: #64748b;
    margin-bottom: 0;
    line-height: 1.6;
}

.tech-contact-link {
    color: #4fc3f7;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.tech-contact-link:hover {
    color: #29b6f6;
    text-shadow: 0 0 10px rgba(79, 195, 247, 0.3);
}

.tech-highlight {
    color: #4fc3f7;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(79, 195, 247, 0.2);
}

.tech-card-border {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #4fc3f7, #29b6f6, #81d4fa);
    transform: scaleX(0);
    transition: all 0.4s ease;
}

.tech-contact-card:hover .tech-card-border {
    transform: scaleX(1);
}

/* 科技感表单区域 */
.tech-form-section {
    position: relative;
    padding: 60px 0 100px 0;
    background: linear-gradient(180deg, #16213e 0%, #1a1a2e 20%, #0a0a0a 40%, #1a1a2e 60%, #16213e 80%, #0f3460 100%);
    overflow: hidden;
    margin-top: -40px;
}

.tech-form-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.tech-form-background .tech-grid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(79, 195, 247, 0.06) 1px, transparent 1px),
        linear-gradient(90deg, rgba(79, 195, 247, 0.06) 1px, transparent 1px);
    background-size: 40px 40px;
    animation: gridFlow 12s linear infinite;
}

.tech-form-background .tech-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(3px 3px at 25px 35px, rgba(59, 130, 246, 0.4), transparent),
        radial-gradient(2px 2px at 65px 85px, rgba(139, 92, 246, 0.3), transparent),
        radial-gradient(1px 1px at 105px 45px, rgba(59, 130, 246, 0.5), transparent),
        radial-gradient(2px 2px at 145px 95px, rgba(139, 92, 246, 0.4), transparent),
        radial-gradient(1px 1px at 185px 25px, rgba(59, 130, 246, 0.3), transparent);
    background-repeat: repeat;
    background-size: 220px 120px;
    animation: particleWave 12s ease-in-out infinite;
}

@keyframes gridFlow {
    0% { transform: translate(0, 0); opacity: 0.3; }
    50% { opacity: 0.6; }
    100% { transform: translate(40px, 40px); opacity: 0.3; }
}

@keyframes gridPulse {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.02);
    }
}

@keyframes particleWave {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
        opacity: 0.7;
    }
    25% {
        transform: translateY(-10px) rotate(1deg);
        opacity: 1;
    }
    50% {
        transform: translateY(0) rotate(0deg);
        opacity: 0.8;
    }
    75% {
        transform: translateY(10px) rotate(-1deg);
        opacity: 1;
    }
}

.tech-form-wrapper {
    position: relative;
    z-index: 10;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border-radius: 30px;
    padding: 50px;
    border: 1px solid rgba(79, 195, 247, 0.2);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    max-width: 1100px;
    margin: 0 auto;
}

.tech-form-header {
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.tech-form-icon {
    position: relative;
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tech-form-icon i {
    width: 50px;
    height: 50px;
    color: #4fc3f7;
    filter: drop-shadow(0 0 20px rgba(79, 195, 247, 0.5));
    animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

.icon-orbit {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.orbit-ring {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    border: 1px solid rgba(79, 195, 247, 0.3);
    border-radius: 50%;
    animation: orbitRotate 10s linear infinite;
}

@keyframes orbitRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.orbit-dot {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #4fc3f7;
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(79, 195, 247, 0.8);
}

.orbit-dot-1 {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    animation: orbitRotate 8s linear infinite;
}

.orbit-dot-2 {
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    animation: orbitRotate 8s linear infinite reverse;
}

.orbit-dot-3 {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    animation: orbitRotate 8s linear infinite;
}

.tech-form-title-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border-radius: 15px;
    padding: 20px 50px;
    border: 1px solid rgba(79, 195, 247, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    max-width: 1200px;
    width: 100%;
    text-align: center;
    position: relative;
    overflow: hidden;
    margin: 0 auto;
}

.tech-form-title-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(79, 195, 247, 0.1), transparent);
    transition: all 0.8s ease;
}

.tech-form-title-container:hover::before {
    left: 100%;
}

.tech-form-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 0 30px rgba(79, 195, 247, 0.3);
    position: relative;
    z-index: 2;
}

.tech-form-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1.5rem;
    line-height: 1.6;
    position: relative;
    z-index: 2;
}

.tech-status-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 12px 25px;
    background: rgba(79, 195, 247, 0.2);
    border-radius: 30px;
    border: 1px solid rgba(79, 195, 247, 0.4);
    margin: 0 auto;
    width: fit-content;
    position: relative;
    z-index: 2;
    box-shadow: 0 0 20px rgba(79, 195, 247, 0.3);
}

.status-dot {
    width: 8px;
    height: 8px;
    background: #4fc3f7;
    border-radius: 50%;
    animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.2); }
}

.status-text {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    font-weight: 500;
}

/* 底部融合区域 */
.tech-form-bottom-fusion {
    height: 0;
    background: transparent;
    position: relative;
}

.tech-form-bottom-fusion::before {
    display: none;
}

/* 标题容器响应式设计 */
@media (max-width: 768px) {
    .tech-form-title-container {
        min-width: 300px;
        padding: 15px 30px;
        border-radius: 40px;
    }

    .tech-form-title {
        font-size: 2rem;
    }

    .tech-form-subtitle {
        font-size: 1rem;
    }

    .tech-status-indicator {
        padding: 10px 20px;
        border-radius: 25px;
    }

    .status-text {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .tech-form-title-container {
        min-width: 250px;
        padding: 12px 20px;
    }

    .tech-form-title {
        font-size: 1.6rem;
    }

    .tech-form-subtitle {
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    .tech-status-indicator {
        padding: 8px 15px;
        gap: 8px;
    }

    .status-dot {
        width: 6px;
        height: 6px;
    }

    .status-text {
        font-size: 0.75rem;
    }
}

/* 页脚融合样式覆盖 */
footer.footer {
    margin-top: 0 !important;
    background: linear-gradient(135deg, #0f3460 0%, #16213e 50%, #1a1a2e 100%) !important;
}

.footer-widget {
    box-shadow: none !important;
    border: 1px solid rgba(255, 255, 255, 0.08) !important;
    background: rgba(255, 255, 255, 0.03) !important;
}

.footer-widget:hover {
    box-shadow: none !important;
    transform: none !important;
    background: rgba(255, 255, 255, 0.06) !important;
}

.footer-widget:nth-child(2):hover,
.footer-widget:nth-child(3):hover,
.footer-widget:nth-child(4):hover {
    box-shadow: none !important;
}

.footer-qrcode {
    box-shadow: none !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.footer-qrcode:hover {
    box-shadow: none !important;
    transform: none !important;
}

.qrcode-img {
    box-shadow: none !important;
    border: 2px solid rgba(255, 255, 255, 0.1) !important;
}

.footer-qrcode:hover .qrcode-img {
    box-shadow: none !important;
    transform: none !important;
    border-color: rgba(79, 195, 247, 0.3) !important;
}

/* 在线客服图标样式 */
.service-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    margin-right: 12px;
}

.service-icon i {
    width: 20px;
    height: 20px;
    color: white;
}

.qq-icon {
    background: linear-gradient(135deg, #1e90ff 0%, #0066cc 100%);
}

.wechat-icon {
    background: linear-gradient(135deg, #07c160 0%, #05a050 100%);
}

.phone-icon {
    background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
}

/* 科技感表单网格布局 */
.tech-form-grid {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.tech-form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    align-items: start;
}

.tech-form-row-full {
    grid-template-columns: 1fr;
}

.tech-form-submit {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

/* 科技感表单控件 */
.tech-input-group, .tech-select-group, .tech-textarea-group {
    position: relative;
    margin-bottom: 0;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 15px;
    padding: 8px;
    border: 1px solid rgba(79, 195, 247, 0.1);
    transition: all 0.3s ease;
}

.tech-input-group:hover, .tech-select-group:hover, .tech-textarea-group:hover {
    background: rgba(255, 255, 255, 0.04);
    border-color: rgba(79, 195, 247, 0.2);
    box-shadow: 0 5px 15px rgba(79, 195, 247, 0.1);
}

.tech-input-group.focused, .tech-select-group.focused, .tech-textarea-group.focused {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(79, 195, 247, 0.4);
    box-shadow: 0 8px 25px rgba(79, 195, 247, 0.15);
}

.tech-input-icon {
    position: absolute;
    top: 50%;
    left: 15px;
    transform: translateY(-50%);
    z-index: 3;
    color: rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tech-input-icon i {
    width: 20px;
    height: 20px;
}

.tech-form-control, .tech-form-select, .tech-form-textarea {
    width: 100%;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(79, 195, 247, 0.2);
    border-radius: 10px;
    padding: 15px 15px 15px 55px;
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 2;
    box-shadow: none;
    line-height: 1.4;
    display: flex;
    align-items: center;
}

.tech-form-control:focus, .tech-form-select:focus, .tech-form-textarea:focus {
    outline: none;
    border-color: #4fc3f7;
    box-shadow: 0 0 15px rgba(79, 195, 247, 0.2);
    background: rgba(255, 255, 255, 0.06);
}

.tech-form-control::placeholder, .tech-form-textarea::placeholder {
    color: rgba(255, 255, 255, 0.4);
}

.tech-form-label {
    position: absolute;
    top: 50%;
    left: 55px;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.6);
    font-size: 1rem;
    transition: all 0.3s ease;
    pointer-events: none;
    background: transparent;
    z-index: 1;
}

.tech-input-group.focused .tech-form-label,
.tech-select-group.focused .tech-form-label,
.tech-textarea-group.focused .tech-form-label,
.tech-input-group.has-value .tech-form-label,
.tech-select-group.has-value .tech-form-label,
.tech-textarea-group.has-value .tech-form-label {
    top: -18px;
    left: 15px;
    transform: translateY(0);
    font-size: 0.8rem;
    color: #4fc3f7;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
    padding: 2px 8px;
    border-radius: 4px;
    z-index: 10;
}

.tech-input-group.focused .tech-input-icon,
.tech-select-group.focused .tech-input-icon,
.tech-textarea-group.focused .tech-input-icon {
    color: #4fc3f7;
}

.tech-input-border {
    display: none;
}

.tech-form-select {
    cursor: pointer;
    appearance: none;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%234fc3f7" stroke-width="2"><polyline points="6,9 12,15 18,9"/></svg>');
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 16px;
    color-scheme: dark;

    /* CSS变量定义 */
    --option-bg: #1a1a2e;
    --option-hover-bg: #4fc3f7;
    --option-text: white;
}

/* 下拉选择框选项样式 */
.tech-form-select option {
    background-color: #1a1a2e !important;
    background: #1a1a2e !important;
    color: white !important;
    padding: 12px 15px;
    border: none;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0;
}

.tech-form-select option:hover,
.tech-form-select option:focus,
.tech-form-select option:active {
    background-color: #4fc3f7 !important;
    background: #4fc3f7 !important;
    color: white !important;
    outline: none !important;
}

.tech-form-select option:checked,
.tech-form-select option:selected {
    background-color: #4fc3f7 !important;
    background: #4fc3f7 !important;
    color: white !important;
    font-weight: 600;
}

.tech-form-select option[disabled] {
    color: rgba(255, 255, 255, 0.5) !important;
    background-color: rgba(255, 255, 255, 0.05) !important;
    background: rgba(255, 255, 255, 0.05) !important;
    font-style: italic;
}

/* 强制覆盖浏览器默认样式 */
.tech-form-select option:not([disabled]):hover {
    background-color: #4fc3f7 !important;
    background: #4fc3f7 !important;
    color: white !important;
}

.tech-form-select option:not([disabled]):focus {
    background-color: #4fc3f7 !important;
    background: #4fc3f7 !important;
    color: white !important;
}

/* 修复下拉框展开时的白色背景 */
.tech-form-select:focus {
    background: rgba(255, 255, 255, 0.03);
    border-color: #4fc3f7;
    box-shadow: 0 8px 25px rgba(79, 195, 247, 0.15);
}

/* 针对Firefox的特殊处理 */
@-moz-document url-prefix() {
    .tech-form-select {
        background: rgba(255, 255, 255, 0.03);
        color: white;
    }

    .tech-form-select option {
        background: #1a1a2e !important;
        background-color: #1a1a2e !important;
        color: white !important;
    }

    .tech-form-select option:hover,
    .tech-form-select option:focus {
        background: #4fc3f7 !important;
        background-color: #4fc3f7 !important;
        color: white !important;
    }
}

/* 针对WebKit浏览器的特殊处理 */
@media screen and (-webkit-min-device-pixel-ratio:0) {
    .tech-form-select option {
        background: #1a1a2e !important;
        background-color: #1a1a2e !important;
        color: white !important;
    }

    .tech-form-select option:hover {
        background: #4fc3f7 !important;
        background-color: #4fc3f7 !important;
        color: white !important;
    }
}

/* 通用强制样式 */
select.tech-form-select option {
    background: #1a1a2e !important;
    background-color: #1a1a2e !important;
    color: white !important;
}

select.tech-form-select option:hover,
select.tech-form-select option:focus,
select.tech-form-select option:active {
    background: #4fc3f7 !important;
    background-color: #4fc3f7 !important;
    color: white !important;
}

/* 针对不同浏览器的下拉框样式优化 */
.tech-form-select::-webkit-scrollbar {
    width: 8px;
}

.tech-form-select::-webkit-scrollbar-track {
    background: #1a1a2e;
}

.tech-form-select::-webkit-scrollbar-thumb {
    background: #4fc3f7;
    border-radius: 4px;
}

.tech-form-select::-webkit-scrollbar-thumb:hover {
    background: #29b6f6;
}

.tech-form-textarea {
    resize: vertical;
    min-height: 120px;
    padding-top: 15px;
    display: block;
    align-items: flex-start;
}

.tech-textarea-group .tech-input-icon {
    top: 25px;
    transform: none;
}

.tech-textarea-group .tech-form-label {
    top: 25px;
    transform: translateY(0);
}

.tech-textarea-group.focused .tech-form-label,
.tech-textarea-group.has-value .tech-form-label {
    top: -18px;
    transform: translateY(0);
}

.tech-char-counter {
    position: absolute;
    bottom: -25px;
    right: 0;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.5);
}

.char-count {
    color: #4fc3f7;
    font-weight: 600;
}

/* 科技感提交按钮 */
.tech-submit-btn {
    position: relative;
    background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
    border: none;
    border-radius: 50px;
    padding: 18px 40px;
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s ease;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(79, 195, 247, 0.3);
    min-width: 200px;
}

.tech-submit-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(79, 195, 247, 0.4);
}

.tech-submit-btn:active {
    transform: translateY(-1px);
}

.tech-submit-btn.loading {
    pointer-events: none;
    opacity: 0.8;
}

.btn-text {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.btn-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.2), transparent);
    opacity: 0;
    transition: all 0.3s ease;
}

.tech-submit-btn:hover .btn-glow {
    opacity: 1;
    animation: btnGlow 2s ease-in-out infinite;
}

@keyframes btnGlow {
    0%, 100% { transform: scale(1); opacity: 0.2; }
    50% { transform: scale(1.1); opacity: 0.4; }
}

.btn-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    border-radius: 50px;
}

.btn-particles::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    transition: all 0.6s ease;
}

.tech-submit-btn:hover .btn-particles::before {
    left: 100%;
}

/* 表单网格响应式设计 */
@media (max-width: 768px) {
    .tech-form-section {
        padding: 40px 0 80px 0;
        margin-top: -30px;
    }

    .tech-form-wrapper {
        padding: 30px 25px;
        max-width: 100%;
        margin: 0 15px;
    }

    .tech-form-title-container {
        max-width: 100%;
        margin: 0 15px;
        padding: 15px 30px;
    }

    .tech-breadcrumb-container {
        max-width: 100%;
        margin: 0 15px;
        padding: 10px 25px;
    }

    .tech-form-grid {
        gap: 25px;
    }

    .tech-form-row {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .tech-form-submit {
        margin-top: 15px;
    }

    .tech-submit-btn {
        width: 100%;
        max-width: 300px;
    }
}

@media (max-width: 480px) {
    .tech-form-section {
        padding: 30px 0 60px 0;
        margin-top: -20px;
    }

    .tech-form-wrapper {
        padding: 25px 20px;
        border-radius: 20px;
        max-width: 100%;
        margin: 0 10px;
    }

    .tech-form-title-container {
        max-width: 100%;
        margin: 0 10px;
        padding: 12px 20px;
        border-radius: 40px;
    }

    .tech-breadcrumb-container {
        max-width: 100%;
        margin: 0 10px;
        padding: 8px 20px;
    }

    .tech-form-grid {
        gap: 20px;
    }

    .tech-form-row {
        gap: 15px;
    }

    .tech-form-control, .tech-form-select, .tech-form-textarea {
        padding: 12px 12px 12px 50px;
        font-size: 0.9rem;
    }

    .tech-input-icon {
        left: 12px;
    }

    .tech-input-icon i {
        width: 18px;
        height: 18px;
    }

    .tech-form-label {
        left: 50px;
        font-size: 0.9rem;
    }

    .tech-input-group.focused .tech-form-label,
    .tech-select-group.focused .tech-form-label,
    .tech-textarea-group.focused .tech-form-label,
    .tech-input-group.has-value .tech-form-label,
    .tech-select-group.has-value .tech-form-label,
    .tech-textarea-group.has-value .tech-form-label {
        font-size: 0.75rem;
        left: 12px;
    }
}

/* 最终强制样式 - 确保选项样式正确 */
.tech-form-select option,
.tech-form-select option:not(:checked),
.tech-form-select option:not(:disabled) {
    background: #1a1a2e !important;
    background-color: #1a1a2e !important;
    color: white !important;
}

.tech-form-select option:hover,
.tech-form-select option:focus,
.tech-form-select option:active,
.tech-form-select option:not(:disabled):hover {
    background: #4fc3f7 !important;
    background-color: #4fc3f7 !important;
    color: white !important;
}

/* ===== 消息提示样式 ===== */
#form-message {
    transition: all 0.3s ease;
}

.tech-alert {
    padding: 25px 30px;
    border-radius: 15px;
    margin: 25px 0;
    font-weight: 500;
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
    animation: alertSlideIn 0.5s ease-out;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

@keyframes alertSlideIn {
    0% {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.tech-alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
    transition: all 0.8s ease;
}

.tech-alert:hover::before {
    left: 100%;
}

.tech-alert-success {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.15) 0%, rgba(139, 195, 74, 0.15) 100%);
    border-color: rgba(76, 175, 80, 0.4);
    color: #66bb6a;
    box-shadow: 0 8px 32px rgba(76, 175, 80, 0.25);
    border-left: 4px solid #4caf50;
    padding-left: 50px;
}

.tech-alert-success::after {
    content: '✓';
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background: #4caf50;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
    animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
    }
}

.tech-alert-danger {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.15) 0%, rgba(255, 87, 34, 0.15) 100%);
    border-color: rgba(244, 67, 54, 0.4);
    color: #ef5350;
    box-shadow: 0 8px 32px rgba(244, 67, 54, 0.25);
    border-left: 4px solid #f44336;
    padding-left: 50px;
}

.tech-alert-danger::after {
    content: '⚠';
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background: #f44336;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
    animation: errorShake 0.5s ease-in-out;
}



@keyframes errorShake {
    0%, 100% { transform: translateY(-50%) translateX(0); }
    25% { transform: translateY(-50%) translateX(-5px); }
    75% { transform: translateY(-50%) translateX(5px); }
}

/* 关闭按钮 */
.tech-alert-close {
    position: absolute;
    top: 10px;
    right: 15px;
    background: none;
    border: none;
    color: inherit;
    font-size: 20px;
    cursor: pointer;
    opacity: 0.7;
    transition: all 0.3s ease;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tech-alert-close:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.1);
    transform: rotate(90deg);
}

option {
    background: #1a1a2e !important;
    color: white !important;
}

option:hover,
option:focus {
    background: #4fc3f7 !important;
    color: white !important;
}

select option {
    background: #1a1a2e !important;
    color: white !important;
}

select option:hover {
    background: #4fc3f7 !important;
    color: white !important;
}

{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/space/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Space from './src/space'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElSpace: SFCWithInstall<typeof Space> = withInstall(Space)\nexport default ElSpace\n\nexport * from './src/space'\nexport * from './src/item'\nexport * from './src/use-space'\n"], "names": ["withInstall", "Space"], "mappings": ";;;;;;;;;AAEY,MAAC,OAAO,GAAGA,mBAAW,CAACC,gBAAK;;;;;;;;"}
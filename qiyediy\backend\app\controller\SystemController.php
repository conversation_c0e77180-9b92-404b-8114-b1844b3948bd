<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * QiyeDIY企业建站系统 - 系统管理控制器
 */

declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\service\SystemService;
use app\validate\SystemValidate;
use think\Request;
use think\Response;

/**
 * 系统管理控制器
 */
class SystemController extends BaseController
{
    protected SystemService $systemService;

    public function __construct()
    {
        parent::__construct();
        $this->systemService = new SystemService();
    }

    /**
     * 获取系统设置
     * @return Response
     */
    public function getSettings(): Response
    {
        try {
            $settings = $this->systemService->getSettings();
            
            return $this->success('获取成功', $settings);
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 更新系统设置
     * @param Request $request
     * @return Response
     */
    public function updateSettings(Request $request): Response
    {
        try {
            $data = $request->post();
            
            // 验证数据
            $validate = new SystemValidate();
            if (!$validate->scene('settings')->check($data)) {
                return $this->error($validate->getError());
            }
            
            $this->systemService->updateSettings($data);
            
            return $this->success('设置更新成功');
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取系统信息
     * @return Response
     */
    public function getInfo(): Response
    {
        try {
            $info = $this->systemService->getSystemInfo();
            
            return $this->success('获取成功', $info);
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取系统统计
     * @return Response
     */
    public function getStatistics(): Response
    {
        try {
            $statistics = $this->systemService->getStatistics();
            
            return $this->success('获取成功', $statistics);
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 测试邮件发送
     * @param Request $request
     * @return Response
     */
    public function testEmail(Request $request): Response
    {
        try {
            $data = $request->post();
            
            // 验证数据
            $validate = new SystemValidate();
            if (!$validate->scene('testEmail')->check($data)) {
                return $this->error($validate->getError());
            }
            
            $this->systemService->testEmail($data);
            
            return $this->success('测试邮件发送成功');
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 清空缓存
     * @return Response
     */
    public function clearCache(): Response
    {
        try {
            $result = $this->systemService->clearCache();
            
            return $this->success('缓存清空成功', $result);
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取系统日志
     * @param Request $request
     * @return Response
     */
    public function getLogs(Request $request): Response
    {
        try {
            $params = $request->get();
            $logs = $this->systemService->getLogs($params);
            
            return $this->success('获取成功', $logs);
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 清空系统日志
     * @param Request $request
     * @return Response
     */
    public function clearLogs(Request $request): Response
    {
        try {
            $data = $request->delete();
            $result = $this->systemService->clearLogs($data);
            
            return $this->success('日志清空成功', $result);
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 系统备份
     * @param Request $request
     * @return Response
     */
    public function backup(Request $request): Response
    {
        try {
            $data = $request->post();
            
            // 验证数据
            $validate = new SystemValidate();
            if (!$validate->scene('backup')->check($data)) {
                return $this->error($validate->getError());
            }
            
            $result = $this->systemService->backup($data);
            
            return $this->success('备份创建成功', $result);
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取备份列表
     * @return Response
     */
    public function getBackups(): Response
    {
        try {
            $backups = $this->systemService->getBackups();
            
            return $this->success('获取成功', $backups);
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 恢复备份
     * @param Request $request
     * @param string $backupId
     * @return Response
     */
    public function restore(Request $request, string $backupId): Response
    {
        try {
            $this->systemService->restore($backupId);
            
            return $this->success('备份恢复成功');
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除备份
     * @param Request $request
     * @param string $backupId
     * @return Response
     */
    public function deleteBackup(Request $request, string $backupId): Response
    {
        try {
            $this->systemService->deleteBackup($backupId);
            
            return $this->success('备份删除成功');
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 系统优化
     * @param Request $request
     * @return Response
     */
    public function optimize(Request $request): Response
    {
        try {
            $data = $request->post();
            $result = $this->systemService->optimize($data);
            
            return $this->success('系统优化完成', $result);
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 检查系统更新
     * @return Response
     */
    public function checkUpdate(): Response
    {
        try {
            $result = $this->systemService->checkUpdate();
            
            return $this->success('检查完成', $result);
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 执行系统更新
     * @return Response
     */
    public function update(): Response
    {
        try {
            $result = $this->systemService->update();
            
            return $this->success('更新完成', $result);
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取系统配置
     * @return Response
     */
    public function getConfig(): Response
    {
        try {
            $config = $this->systemService->getConfig();
            
            return $this->success('获取成功', $config);
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 更新系统配置
     * @param Request $request
     * @return Response
     */
    public function updateConfig(Request $request): Response
    {
        try {
            $data = $request->put();
            $this->systemService->updateConfig($data);
            
            return $this->success('配置更新成功');
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 重启系统服务
     * @param Request $request
     * @return Response
     */
    public function restart(Request $request): Response
    {
        try {
            $data = $request->post();
            $this->systemService->restart($data['service'] ?? 'all');
            
            return $this->success('服务重启成功');
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取系统健康状态
     * @return Response
     */
    public function getHealth(): Response
    {
        try {
            $health = $this->systemService->getHealth();
            
            return $this->success('获取成功', $health);
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取系统监控数据
     * @param Request $request
     * @return Response
     */
    public function getMonitoring(Request $request): Response
    {
        try {
            $params = $request->get();
            $data = $this->systemService->getMonitoring($params);
            
            return $this->success('获取成功', $data);
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 导出系统数据
     * @param Request $request
     * @return Response
     */
    public function export(Request $request): Response
    {
        try {
            $data = $request->post();
            
            // 验证数据
            $validate = new SystemValidate();
            if (!$validate->scene('export')->check($data)) {
                return $this->error($validate->getError());
            }
            
            $result = $this->systemService->export($data);
            
            return $this->success('导出成功', $result);
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 导入系统数据
     * @param Request $request
     * @return Response
     */
    public function import(Request $request): Response
    {
        try {
            $file = $request->file('file');
            if (!$file) {
                return $this->error('请选择要导入的文件');
            }
            
            $result = $this->systemService->import($file);
            
            return $this->success('导入完成', $result);
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}

<!-- 后台管理系统公共JavaScript文件 -->

<!-- 核心JavaScript库 -->
<script src="/assets/js/jquery.min.js"></script>
<script src="/assets/js/bootstrap.bundle.min.js"></script>

<!-- 后台管理系统核心JS -->
<script src="/assets/js/admin.js"></script>

<!-- 核心初始化脚本 -->
<script>
/**
 * 后台管理系统核心初始化 - 三只鱼网络科技 | 韩总
 * 简化版本，专注核心功能 - ThinkPHP6企业级应用
 */

// 设置全局AJAX超时和错误处理
$.ajaxSetup({
    timeout: 30000 // 30秒超时
});

// 全局AJAX错误处理
$(document).ajaxError(function(event, xhr, settings, error) {
    let message = '操作失败';
    
    if (xhr.responseJSON && xhr.responseJSON.message) {
        message = xhr.responseJSON.message;
    } else if (xhr.status === 404) {
        message = '请求的资源不存在';
    } else if (xhr.status === 403) {
        message = '没有权限执行此操作';
    } else if (xhr.status === 500) {
        message = '服务器内部错误';
    } else if (xhr.status === 'timeout') {
        message = '请求超时，请稍后重试';
    } else if (xhr.status === 'error') {
        message = '网络错误，请检查网络连接';
    }
    
    if (typeof showMessage === 'function') {
        showMessage(message, 'error');
    }
});

// 页面加载完成后的初始化
$(document).ready(function() {
    // 初始化Bootstrap工具提示
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    // 初始化Bootstrap弹出框
    if (typeof bootstrap !== 'undefined' && bootstrap.Popover) {
        const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    }
});
</script>

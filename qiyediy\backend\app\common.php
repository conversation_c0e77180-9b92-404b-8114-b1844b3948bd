<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 公共函数库
 */

declare(strict_types=1);

use think\facade\Config;
use think\facade\Log;
use think\Response;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

if (!function_exists('success')) {
    /**
     * 成功响应
     * @param mixed $data 数据
     * @param string $message 消息
     * @param int $code 状态码
     * @return Response
     */
    function success($data = [], string $message = 'success', int $code = 200): Response
    {
        return json([
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ]);
    }
}

if (!function_exists('error')) {
    /**
     * 错误响应
     * @param string $message 错误消息
     * @param int $code 错误码
     * @param mixed $data 数据
     * @return Response
     */
    function error(string $message = 'error', int $code = 400, $data = []): Response
    {
        return json([
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ]);
    }
}

if (!function_exists('paginate')) {
    /**
     * 分页响应
     * @param mixed $data 分页数据
     * @param string $message 消息
     * @return Response
     */
    function paginate($data, string $message = 'success'): Response
    {
        return json([
            'code' => 200,
            'message' => $message,
            'data' => [
                'list' => $data->items(),
                'total' => $data->total(),
                'per_page' => $data->listRows(),
                'current_page' => $data->currentPage(),
                'last_page' => $data->lastPage(),
                'has_more' => $data->hasPages()
            ],
            'timestamp' => time()
        ]);
    }
}

if (!function_exists('generate_jwt')) {
    /**
     * 生成JWT Token
     * @param array $payload 载荷数据
     * @param int $ttl 过期时间（秒）
     * @return string
     */
    function generate_jwt(array $payload, int $ttl = null): string
    {
        $ttl = $ttl ?: Config::get('jwt.ttl', 7200);
        $secret = Config::get('jwt.secret');
        
        $payload['iat'] = time();
        $payload['exp'] = time() + $ttl;
        $payload['iss'] = Config::get('app.name', 'QiyeDIY');
        
        return JWT::encode($payload, $secret, 'HS256');
    }
}

if (!function_exists('verify_jwt')) {
    /**
     * 验证JWT Token
     * @param string $token JWT Token
     * @return array|false
     */
    function verify_jwt(string $token)
    {
        try {
            $secret = Config::get('jwt.secret');
            $decoded = JWT::decode($token, new Key($secret, 'HS256'));
            return (array) $decoded;
        } catch (Exception $e) {
            Log::error('JWT验证失败: ' . $e->getMessage());
            return false;
        }
    }
}

if (!function_exists('upload_path')) {
    /**
     * 获取上传路径
     * @param string $type 文件类型
     * @param string $filename 文件名
     * @return string
     */
    function upload_path(string $type = 'image', string $filename = ''): string
    {
        $date = date('Y/m/d');
        $path = "uploads/{$type}/{$date}";
        
        if ($filename) {
            $path .= '/' . $filename;
        }
        
        return $path;
    }
}

if (!function_exists('generate_filename')) {
    /**
     * 生成文件名
     * @param string $extension 文件扩展名
     * @param string $prefix 前缀
     * @return string
     */
    function generate_filename(string $extension, string $prefix = ''): string
    {
        $timestamp = date('YmdHis');
        $random = substr(md5(uniqid()), 0, 8);
        
        return $prefix . $timestamp . '_' . $random . '.' . $extension;
    }
}

if (!function_exists('format_bytes')) {
    /**
     * 格式化字节大小
     * @param int $bytes 字节数
     * @param int $precision 精度
     * @return string
     */
    function format_bytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

if (!function_exists('generate_slug')) {
    /**
     * 生成URL别名
     * @param string $text 文本
     * @param string $separator 分隔符
     * @return string
     */
    function generate_slug(string $text, string $separator = '-'): string
    {
        // 转换为小写
        $text = strtolower($text);
        
        // 替换中文字符为拼音（简单处理）
        $text = preg_replace('/[\x{4e00}-\x{9fa5}]/u', '', $text);
        
        // 替换特殊字符
        $text = preg_replace('/[^a-z0-9\-_]/', $separator, $text);
        
        // 去除多余的分隔符
        $text = preg_replace('/[' . preg_quote($separator) . ']+/', $separator, $text);
        
        // 去除首尾分隔符
        return trim($text, $separator);
    }
}

if (!function_exists('mask_string')) {
    /**
     * 字符串掩码
     * @param string $string 原字符串
     * @param int $start 开始位置
     * @param int $length 掩码长度
     * @param string $mask 掩码字符
     * @return string
     */
    function mask_string(string $string, int $start = 3, int $length = 4, string $mask = '*'): string
    {
        $strlen = mb_strlen($string);
        
        if ($strlen <= $start) {
            return $string;
        }
        
        $masked = mb_substr($string, 0, $start);
        $masked .= str_repeat($mask, min($length, $strlen - $start));
        
        if ($strlen > $start + $length) {
            $masked .= mb_substr($string, $start + $length);
        }
        
        return $masked;
    }
}

if (!function_exists('is_mobile')) {
    /**
     * 检查是否为手机号
     * @param string $mobile 手机号
     * @return bool
     */
    function is_mobile(string $mobile): bool
    {
        return preg_match('/^1[3-9]\d{9}$/', $mobile);
    }
}

if (!function_exists('is_email')) {
    /**
     * 检查是否为邮箱
     * @param string $email 邮箱
     * @return bool
     */
    function is_email(string $email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
}

if (!function_exists('generate_code')) {
    /**
     * 生成验证码
     * @param int $length 长度
     * @param bool $numeric 是否只包含数字
     * @return string
     */
    function generate_code(int $length = 6, bool $numeric = true): string
    {
        $chars = $numeric ? '0123456789' : '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $code = '';
        
        for ($i = 0; $i < $length; $i++) {
            $code .= $chars[mt_rand(0, strlen($chars) - 1)];
        }
        
        return $code;
    }
}

if (!function_exists('encrypt_password')) {
    /**
     * 加密密码
     * @param string $password 明文密码
     * @return string
     */
    function encrypt_password(string $password): string
    {
        return password_hash($password, PASSWORD_DEFAULT);
    }
}

if (!function_exists('verify_password')) {
    /**
     * 验证密码
     * @param string $password 明文密码
     * @param string $hash 密码哈希
     * @return bool
     */
    function verify_password(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }
}

if (!function_exists('get_client_ip')) {
    /**
     * 获取客户端IP
     * @return string
     */
    function get_client_ip(): string
    {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? 
              $_SERVER['HTTP_X_REAL_IP'] ?? 
              $_SERVER['HTTP_CLIENT_IP'] ?? 
              $_SERVER['REMOTE_ADDR'] ?? 
              '0.0.0.0';
              
        // 处理多个IP的情况
        if (strpos($ip, ',') !== false) {
            $ip = trim(explode(',', $ip)[0]);
        }
        
        return $ip;
    }
}

if (!function_exists('get_user_agent')) {
    /**
     * 获取用户代理
     * @return string
     */
    function get_user_agent(): string
    {
        return $_SERVER['HTTP_USER_AGENT'] ?? '';
    }
}

if (!function_exists('array_to_tree')) {
    /**
     * 数组转树形结构
     * @param array $data 数据
     * @param string $id ID字段
     * @param string $pid 父ID字段
     * @param string $children 子节点字段
     * @return array
     */
    function array_to_tree(array $data, string $id = 'id', string $pid = 'parent_id', string $children = 'children'): array
    {
        $tree = [];
        $map = [];
        
        // 建立映射
        foreach ($data as $item) {
            $map[$item[$id]] = $item;
        }
        
        // 构建树形结构
        foreach ($data as $item) {
            if (isset($map[$item[$pid]])) {
                $map[$item[$pid]][$children][] = &$map[$item[$id]];
            } else {
                $tree[] = &$map[$item[$id]];
            }
        }
        
        return $tree;
    }
}

if (!function_exists('tree_to_array')) {
    /**
     * 树形结构转数组
     * @param array $tree 树形数据
     * @param string $children 子节点字段
     * @return array
     */
    function tree_to_array(array $tree, string $children = 'children'): array
    {
        $result = [];
        
        foreach ($tree as $item) {
            $children_data = $item[$children] ?? [];
            unset($item[$children]);
            
            $result[] = $item;
            
            if (!empty($children_data)) {
                $result = array_merge($result, tree_to_array($children_data, $children));
            }
        }
        
        return $result;
    }
}

![](https://www.thinkphp.cn/uploads/images/20230630/300c856765af4d8ae758c503185f8739.png)

ThinkPHP 8
===============

## 特性

* 基于PHP`8.0+`重构
* 升级`PSR`依赖
* 依赖`think-orm`3.0+版本
* 全新的`think-dumper`服务，支持远程调试
* 支持`6.0`/`6.1`无缝升级

> ThinkPHP8的运行环境要求PHP8.0+

现在开始，你可以使用官方提供的[ThinkChat](https://chat.topthink.com/)，让你在学习ThinkPHP的旅途中享受私人AI助理服务！

![](https://www.topthink.com/uploads/assistant/20230630/4d1a3f0ad2958b49bb8189b7ef824cb0.png)

ThinkPHP生态服务由[顶想云](https://www.topthink.com)（TOPThink Cloud）提供，为生态提供专业的开发者服务和价值之选。

## 文档

[完全开发手册](https://doc.thinkphp.cn)


## 赞助

全新的[赞助计划](https://www.thinkphp.cn/sponsor)可以让你通过我们的网站、手册、欢迎页及GIT仓库获得巨大曝光，同时提升企业的品牌声誉，也更好保障ThinkPHP的可持续发展。

[![](https://www.thinkphp.cn/sponsor/special.svg)](https://www.thinkphp.cn/sponsor/special)

[![](https://www.thinkphp.cn/sponsor.svg)](https://www.thinkphp.cn/sponsor)

## 安装

~~~
composer create-project topthink/think tp
~~~

启动服务

~~~
cd tp
php think run
~~~

然后就可以在浏览器中访问

~~~
http://localhost:8000
~~~

如果需要更新框架使用
~~~
composer update topthink/framework
~~~

## 命名规范

`ThinkPHP`遵循PSR-2命名规范和PSR-4自动加载规范。

## 参与开发

直接提交PR或者Issue即可

## 版权信息

ThinkPHP遵循Apache2开源协议发布，并提供免费使用。

本项目包含的第三方源码和二进制文件之版权信息另行标注。

版权所有Copyright © 2006-2024 by ThinkPHP (http://thinkphp.cn) All rights reserved。

ThinkPHP® 商标和著作权所有者为上海顶想信息科技有限公司。

更多细节参阅 [LICENSE.txt](LICENSE.txt)

---

# 企业官网管理系统

基于ThinkPHP 8的企业级网站内容管理系统，集成DIY页面构建器。

## 🚀 项目特性

- **DIY页面构建器** - 可视化拖拽式页面编辑
- **内容管理系统** - 新闻、案例、产品管理
- **图片管理系统** - 批量上传、分组管理
- **响应式设计** - 完美适配各种设备
- **SEO优化** - 友好URL和元数据支持

## 📁 项目结构

```
├── app/                    # 应用目录
│   ├── admin/             # 后台管理
│   │   └── controller/    # 后台控制器
│   ├── api/               # API接口
│   ├── controller/        # 前台控制器
│   ├── model/             # 数据模型
│   ├── service/           # 服务层
│   └── view/              # 视图模板
├── config/                # 配置文件
├── database/              # 数据库文件
├── docs/                  # 项目文档
├── public/                # 公共资源
│   ├── assets/           # 前端资源
│   ├── diy/              # DIY组件
│   └── uploads/          # 上传文件
├── route/                 # 路由配置
└── vendor/               # 第三方依赖
```

## 🎯 核心功能

### DIY页面构建器
- 5个自定义组件（文本块、统计数字、团队介绍、客户评价、联系信息）
- 可视化编辑界面
- 实时预览功能
- 响应式设计
- 模板类型筛选和分类管理

### 内容管理
- 新闻管理（分类、发布、编辑）
- 案例管理（行业分类、项目展示）
- 图片管理（批量上传、选择器）
- 轮播图管理

### 系统功能
- 管理员认证
- 权限控制
- 系统设置
- 联系表单

## 📊 当前状态

**项目状态**：✅ **生产就绪**
**完成度**：100%
**最后更新**：2025年6月8日

详细状态请查看：[项目状态文档](docs/project-status.md)

## 📖 文档

- [开发总结](docs/开发总结.md) - 项目开发历程和功能总结
- [项目状态](docs/项目状态.md) - 当前项目状态和完成度
- [状态关键词](docs/状态关键词.md) - 快速状态查看
- [用户使用指南](docs/用户使用指南.md) - DIY页面构建器使用说明
- [技术集成指南](docs/技术集成指南.md) - 技术架构和集成方案
- [案例模块开发指南](docs/案例模块开发指南.md) - 案例管理功能详细说明
- [快速了解指南](docs/快速了解指南.md) - 新会话快速了解项目
- [工具使用指南](docs/工具使用指南.md) - 专业工具集使用说明
- [模板类型筛选功能说明](docs/模板类型筛选功能说明.md) - 模板分类管理功能详解

## 🛠️ 环境要求

- PHP 8.0+
- MySQL 5.7+
- Composer
- Web服务器（Apache/Nginx）

## ⚡ 快速开始

1. 克隆项目
2. 安装依赖：`composer install`
3. 配置数据库：修改 `config/database.php`
4. 导入数据库：`website.sql`
5. 启动服务：`php think run`

## 📄 许可证

本项目基于ThinkPHP框架开发，遵循Apache2开源协议。

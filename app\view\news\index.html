{assign name="pageTitle" value="$pageData.pageTitlee" /}

{include file="common/header"}
<!-- 新闻专用CSS -->
<link rel="stylesheet" href="{:asset('assets/css/news.css')}?v={:time()}">

<!-- 整个新闻页面背景区域 -->
<section class="news-page-wrapper">
    <div class="news-background">
        <!-- 3D新闻插画装饰 -->
        <div class="hero-illustration">
            <div class="news-illustration">
                <!-- 新闻报纸 -->
                <div class="newspaper">
                    <div class="newspaper-content">
                        <div class="newspaper-header"></div>
                        <div class="newspaper-lines"></div>
                    </div>
                </div>
                <!-- 信息流泡泡 -->
                <div class="info-bubble bubble-1">
                    <i class="fas fa-rss"></i>
                </div>
                <div class="info-bubble bubble-2">
                    <i class="fas fa-newspaper"></i>
                </div>
                <div class="info-bubble bubble-3">
                    <i class="fas fa-bullhorn"></i>
                </div>
                <!-- 文档页面 -->
                <div class="document-stack">
                    <div class="document doc-1"></div>
                    <div class="document doc-2"></div>
                    <div class="document doc-3"></div>
                </div>
                <!-- 新闻图标 -->
                <div class="news-icon">
                    <i class="fas fa-globe"></i>
                </div>
                <!-- 数据流 -->
                <div class="data-stream">
                    <div class="stream-line line-1"></div>
                    <div class="stream-line line-2"></div>
                    <div class="stream-line line-3"></div>
                </div>
            </div>
        </div>

        <!-- 页面标题区域 -->
        <div class="page-header">
            <div class="container">
                <div class="page-title-content">
                    <h1 class="page-title">{$pageTitle|default='新闻资讯'}</h1>
                    <p class="page-subtitle">{$pageDescription|default='关注我们的最新资讯以及行业动态'}</p>
                    <nav class="page-breadcrumb">
                        <a href="/" class="breadcrumb-link">
                            <i class="fas fa-home"></i>
                            首页
                        </a>
                        <span class="breadcrumb-separator">></span>
                        <span class="breadcrumb-current">新闻资讯</span>
                    </nav>
                </div>
            </div>
        </div>

        <!-- 新闻内容区域 -->
        <div class="news-content">
            <div class="container">
                <div class="row">
                    <!-- 左侧主要内容 -->
                    <div class="col-lg-8 col-md-7">
                        <!-- 新闻分类标签 -->
                        <div class="news-categories-section">
                            <div class="category-filters">
                                <div class="category-header">
                                    <h3 class="category-title">
                                        <i class="fas fa-tags"></i>
                                        新闻分类
                                    </h3>
                                    <p class="category-subtitle">选择您感兴趣的内容分类</p>
                                </div>
                                <div class="category-tabs-wrapper">
                                    <div class="category-tabs">
                                        <button class="category-tab {if condition='$currentCategory == ""'}active{/if}" data-category="">
                                            <i class="fas fa-globe"></i>
                                            <span>全部</span>
                                            <div class="tab-accent"></div>
                                        </button>
                                        {volist name="categories" id="category"}
                                        <button class="category-tab {if condition='$currentCategory == $category.slug'}active{/if}" data-category="{$category.slug}">
                                            <i class="fas fa-folder"></i>
                                            <span>{$category.name}</span>
                                            <div class="tab-accent"></div>
                                        </button>
                                        {/volist}
                                    </div>
                                    <div class="tab-slider"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 筛选状态提示 -->
                        {if condition="$currentTag"}
                        <div class="filter-status">
                            <div class="filter-info">
                                <i class="fas fa-filter"></i>
                                <span>当前筛选：标签 "<strong>{$currentTag}</strong>"</span>
                                <a href="#" class="clear-filter" onclick="clearFilter(event)">
                                    <i class="fas fa-times"></i>
                                    清除筛选
                                </a>
                            </div>
                        </div>
                        {/if}

                        <!-- 新闻列表 -->
                        <div class="news-list-section" id="news-container">
                            <div class="news-grid" id="news-grid">
                                {volist name="newsList" id="news"}
                                <article class="news-item" data-aos="fade-up" data-aos-delay="{$key * 100}">
                                    <div class="news-card">
                                        <div class="news-image">
                                            <a href="{$news.detail_url|default='/news/'.$news.slug}">
                                                <img src="{$news.image|default='/assets/images/news/default.jpg'}" alt="{$news.title}" loading="lazy">
                                                <div class="image-overlay">
                                                    <div class="overlay-content">
                                                        <i class="fas fa-eye"></i>
                                                        <span>阅读详情</span>
                                                    </div>
                                                </div>
                                            </a>
                                            {if condition="$news.is_featured"}
                                            <div class="featured-badge">
                                                <i class="fas fa-star"></i>
                                                <span>精选</span>
                                            </div>
                                            {/if}
                                        </div>
                                        <div class="news-content">
                                            <div class="news-meta">
                                                <div class="meta-item category">
                                                    <i class="fas fa-tag"></i>
                                                    <span>{$news.category.name|default='公司动态'}</span>
                                                </div>
                                                <div class="meta-item date">
                                                    <i class="fas fa-clock"></i>
                                                    <time datetime="{$news.published_at|default=$news.created_at}">{$news.published_at|default=$news.created_at|date='Y-m-d'}</time>
                                                </div>
                                                <div class="meta-item views">
                                                    <i class="fas fa-eye"></i>
                                                    <span>{$news.views|default=0}</span>
                                                </div>
                                            </div>
                                            <h3 class="news-title">
                                                <a href="{$news.detail_url|default='/news/'.$news.slug}">{$news.title}</a>
                                            </h3>
                                            <p class="news-excerpt">{$news.summary|default=$news.content|strip_tags|mb_substr=0,120,'UTF-8'}...</p>
                                            <div class="news-actions">
                                                <a href="{$news.detail_url|default='/news/'.$news.slug}" class="read-more-btn1">
                                                    <span>阅读全文</span>
                                                    <i class="fas fa-arrow-right"></i>
                                                </a>
                                                {if condition="$news.author"}
                                                <div class="news-author">
                                                    <i class="fas fa-user"></i>
                                                    <span>{$news.author}</span>
                                                </div>
                                                {/if}
                                            </div>
                                        </div>
                                    </div>
                                </article>
                                {/volist}
                            </div>

                            <!-- 分页导航 -->
                            {if condition="$pagination"}
                            <div class="pagination-wrapper">
                                <div class="pagination-container">
                                    {$pagination|raw}
                                </div>
                            </div>
                            {/if}

                            <!-- 无新闻时的提示 -->
                            {empty name="newsList"}
                            <div class="no-news-found">
                                <div class="no-news-content">
                                    <div class="no-news-icon">
                                        <i class="fas fa-newspaper"></i>
                                    </div>
                                    <h4>暂无相关新闻</h4>
                                    <p>该分类下暂时没有新闻内容，请关注其他分类或稍后再来查看。</p>
                                    <a href="#" class="btn-back-all" onclick="clearFilter(event)">
                                        <i class="fas fa-arrow-left"></i>
                                        返回全部新闻
                                    </a>
                                </div>
                            </div>
                            {/empty}
                        </div>
                    </div>

                    <!-- 右侧边栏 -->
                    <div class="col-lg-4 col-md-5">
                        <div class="news-sidebar">
                            <!-- 标签云 -->
                            <div class="sidebar-widget tags-widget">
                                <div class="widget-header">
                                    <h4 class="widget-title">
                                        <i class="fas fa-tags"></i>
                                        热门标签
                                    </h4>
                                    <div class="widget-accent"></div>
                                </div>
                                <div class="widget-content">
                                    <div class="tags-cloud">
                                        {volist name="popularTags" id="tag"}
                                        <a href="/news?tag={$tag.name}" class="tag-item{if condition='$currentTag == $tag.name'} active{/if}" data-count="{$tag.count}">{$tag.name}</a>
                                        {/volist}
                                        
                                        {empty name="popularTags"}
                                        <div class="empty-state">
                                            <p>暂无热门标签</p>
                                        </div>
                                        {/empty}
                                    </div>
                                </div>
                            </div>                            
                            <!-- 热门新闻 -->
                            <div class="sidebar-widget hot-news-widget">
                                <div class="widget-header">
                                    <h4 class="widget-title">
                                        <i class="fas fa-fire"></i>
                                        热门新闻
                                    </h4>
                                    <div class="widget-accent"></div>
                                </div>
                                <div class="widget-content">
                                    <div class="hot-news-list">
                                        {volist name="hotNews" id="hot" key="index"}
                                        <div class="hot-news-item">
                                            <div class="hot-news-rank">{$index}</div>
                                            <div class="hot-news-content">
                                                <h5><a href="{$hot.detail_url|default='/news/'.$hot.slug}">{$hot.title}</a></h5>
                                                <div class="hot-news-meta">
                                                    <span class="views"><i class="fas fa-eye"></i> {$hot.views|default=0}</span>
                                                    <span class="date">{$hot.published_at|default=$hot.created_at|date='m-d'}</span>
                                                </div>
                                            </div>
                                        </div>
                                        {/volist}
                                        
                                        {empty name="hotNews"}
                                        <div class="empty-state">
                                            <p>暂无热门新闻</p>
                                        </div>
                                        {/empty}
                                    </div>
                                </div>
                            </div>

                            <!-- 最新发布 -->
                            <div class="sidebar-widget latest-news-widget">
                                <div class="widget-header">
                                    <h4 class="widget-title">
                                        <i class="fas fa-clock"></i>
                                        最新发布
                                    </h4>
                                    <div class="widget-accent"></div>
                                </div>
                                <div class="widget-content">
                                    <div class="latest-news-list">
                                        {volist name="latestNews" id="latest"}
                                        <div class="latest-news-item">
                                            <div class="latest-news-image">
                                                <img src="{$latest.image|default='/assets/images/news/default.jpg'}" alt="{$latest.title}" loading="lazy">
                                            </div>
                                            <div class="latest-news-content">
                                                <h6><a href="{$latest.detail_url|default='/news/'.$latest.slug}">{$latest.title}</a></h6>
                                                <div class="latest-news-meta">
                                                    <span class="category">
                                                        <i class="fas fa-tag"></i>
                                                        {$latest.category.name|default='未分类'}
                                                    </span>
                                                    <span class="date">{$latest.published_at|default=$latest.created_at|date='Y-m-d'}</span>
                                                </div>
                                            </div>
                                        </div>
                                        {/volist}
                                        
                                        {empty name="latestNews"}
                                        <div class="empty-state">
                                            <p>暂无最新新闻</p>
                                        </div>
                                        {/empty}
                                    </div>
                                </div>
                            </div>

                            <!-- 新闻分类统计 -->
                            <div class="sidebar-widget categories-widget">
                                <div class="widget-header">
                                    <h4 class="widget-title">
                                        <i class="fas fa-chart-bar"></i>
                                        分类统计
                                    </h4>
                                    <div class="widget-accent"></div>
                                </div>
                                <div class="widget-content">
                                    <div class="categories-stats">
                                        {volist name="categories" id="category"}
                                        <div class="category-stat-item">
                                            <a href="/news?category={$category.slug}" class="category-stat-link">
                                                <div class="category-info">
                                                    <span class="category-name">{$category.name}</span>
                                                    <span class="category-count">{$category.news_count|default=0}</span>
                                                </div>
                                                <div class="category-progress">
                                                    {php}$width = min(($category['news_count'] ?? 0) * 10, 100);{/php}
                                                    <div class="progress-bar" style="width: {$width}%"></div>
                                                </div>
                                            </a>
                                        </div>
                                        {/volist}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- JavaScript -->
<script src="{:asset('assets/js/jquery.min.js')}"></script>
<script src="{:asset('assets/js/bootstrap.bundle.min.js')}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化AOS动画库（如果已引入）
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 600,
            easing: 'ease-out',
            once: true
        });
    }

    // 分类标签切换功能 - AJAX无痕刷新
    const categoryTabs = document.querySelectorAll('.category-tab');
    const newsContainer = document.getElementById('news-container');
    let currentCategory = ''; // 记录当前分类
    
    categoryTabs.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            const category = this.getAttribute('data-category');
            currentCategory = category;
            
            // 防止重复点击同一分类
            if (this.classList.contains('active')) {
                return;
            }
            
            // 更新活动状态
            categoryTabs.forEach(t => {
                t.classList.remove('active');
                t.style.opacity = '1'; // 恢复所有按钮的透明度
                t.style.pointerEvents = 'auto'; // 恢复所有按钮的点击
            });
            this.classList.add('active');
            
            // 显示加载动画
            showLoadingState();
            
            // 清除标签筛选状态
            updateFilterStatus('');
            updateTagsActiveState('');
            
            // 构建AJAX请求URL
            loadNewsData(category, 1); // 加载第一页
        });
    });

    // 加载新闻数据的通用函数
    function loadNewsData(category = '', page = 1, tag = '') {
        const url = '/news/ajax-list';
        const params = new URLSearchParams();
        if (category) {
            params.set('category', category);
        }
        if (tag) {
            params.set('tag', tag);
        }
        params.set('page', page);
            
        // 发送AJAX请求
        fetch(url + '?' + params.toString(), {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新新闻列表内容
                updateNewsContent(data.html);
                
                // 更新筛选状态提示
                updateFilterStatus(tag);
                
                // 更新标签active状态
                updateTagsActiveState(tag);
                
                // 重新绑定分页事件
                bindPaginationEvents();
                
                // 更新URL但不刷新页面
                const newUrl = new URL(window.location);
                if (category) {
                    newUrl.searchParams.set('category', category);
                } else {
                    newUrl.searchParams.delete('category');
                }
                if (tag) {
                    newUrl.searchParams.set('tag', tag);
                } else {
                    newUrl.searchParams.delete('tag');
                }
                if (page > 1) {
                    newUrl.searchParams.set('page', page);
                } else {
                    newUrl.searchParams.delete('page');
                }
                window.history.pushState({category: category, page: page, tag: tag}, '', newUrl.toString());
            } else {
                console.error('加载失败:', data.message);
                showErrorState(data.message || '加载失败，请重试');
            }
        })
        .catch(error => {
            console.error('请求错误:', error);
            showErrorState('网络错误，请检查连接后重试');
        })
        .finally(() => {
            hideLoadingState();
        });
    }
    
    // 显示加载状态
    function showLoadingState() {
        const newsGrid = document.getElementById('news-grid');
        if (newsGrid) {
            newsGrid.style.opacity = '0.5';
            newsGrid.style.pointerEvents = 'none';
            
            // 添加加载指示器
            const loader = document.createElement('div');
            loader.id = 'news-loader';
            loader.innerHTML = '<div class="loader-spinner"></div><p>正在加载新闻...</p>';
            loader.className = 'news-loader';
            newsGrid.parentNode.appendChild(loader);
        }
    }
    
    // 隐藏加载状态
    function hideLoadingState() {
        const loader = document.getElementById('news-loader');
        if (loader) {
            loader.remove();
        }
        
        // 恢复新闻网格状态
        const newsGrid = document.getElementById('news-grid');
        if (newsGrid) {
            newsGrid.style.opacity = '1';
            newsGrid.style.pointerEvents = 'auto';
        }
        
        // 确保所有分类按钮状态正常
        const categoryTabs = document.querySelectorAll('.category-tab');
        categoryTabs.forEach(tab => {
            if (!tab.classList.contains('active')) {
                tab.style.opacity = '1';
                tab.style.pointerEvents = 'auto';
            }
        });
    }
    
    // 显示错误状态
    function showErrorState(message) {
        const newsGrid = document.getElementById('news-grid');
        if (newsGrid) {
            newsGrid.innerHTML = `
                <div class="error-state">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h4>加载出错了</h4>
                    <p>${message}</p>
                    <button onclick="location.reload()" class="btn-retry">
                        <i class="fas fa-redo"></i>
                        重新加载
                    </button>
                </div>
            `;
        }
    }
    
    // 更新新闻内容
    function updateNewsContent(html) {
        const newsContainer = document.getElementById('news-container');
        if (newsContainer && html) {
            newsContainer.innerHTML = html;
            
            // 重新初始化动画
            if (typeof AOS !== 'undefined') {
                AOS.refresh();
            }
            
            // 重新绑定新闻卡片事件
            initNewsCardEvents();
            
            // 重新绑定标签点击事件
            bindTagEvents();
        }
    }
    
    // 更新筛选状态提示
    function updateFilterStatus(tag) {
        // 查找现有的筛选状态提示
        let filterStatus = document.querySelector('.filter-status');
        
        if (tag) {
            // 如果有标签，显示或更新筛选状态
            if (!filterStatus) {
                // 创建筛选状态提示
                filterStatus = document.createElement('div');
                filterStatus.className = 'filter-status';
                
                // 插入到新闻列表前面
                const newsListSection = document.querySelector('.news-list-section');
                if (newsListSection) {
                    newsListSection.parentNode.insertBefore(filterStatus, newsListSection);
                }
            }
            
                         filterStatus.innerHTML = `
                 <div class="filter-info">
                     <i class="fas fa-filter"></i>
                     <span>当前筛选：标签 "<strong>${tag}</strong>"</span>
                     <a href="#" class="clear-filter" onclick="clearFilter(event)">
                         <i class="fas fa-times"></i>
                         清除筛选
                     </a>
                 </div>
             `;
        } else {
            // 如果没有标签，移除筛选状态提示
            if (filterStatus) {
                filterStatus.remove();
            }
        }
    }
    
    // 更新标签active状态
    function updateTagsActiveState(tag) {
        const tagItems = document.querySelectorAll('.tag-item');
        tagItems.forEach(tagItem => {
            const tagName = tagItem.textContent.trim();
            if (tagName === tag) {
                tagItem.classList.add('active');
            } else {
                tagItem.classList.remove('active');
            }
        });
    }
    
    // 初始化新闻卡片事件
    function initNewsCardEvents() {
        const newsCards = document.querySelectorAll('.news-card');
        newsCards.forEach(card => {
            const newsLink = card.querySelector('.news-title a, .read-more-btn');
            if (newsLink) {
                newsLink.addEventListener('click', function() {
                    const newsId = this.getAttribute('data-news-id');
                    if (newsId) {
                        fetch('/news/view/' + newsId, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        }).catch(e => console.log('统计失败:', e));
                    }
                });
            }
        });
    }

    // 搜索表单优化
    const searchForm = document.querySelector('.news-search-form');
    const searchInput = document.querySelector('.search-input');
    
    if (searchForm && searchInput) {
        // 搜索输入框聚焦效果
        searchInput.addEventListener('focus', function() {
            this.parentNode.style.transform = 'scale(1.02)';
        });
        
        searchInput.addEventListener('blur', function() {
            this.parentNode.style.transform = 'scale(1)';
        });
        
        // 搜索建议点击处理
        const suggestionTags = document.querySelectorAll('.suggestion-tag');
        suggestionTags.forEach(tag => {
            tag.addEventListener('click', function(e) {
                e.preventDefault();
                const query = this.textContent.trim();
                searchInput.value = query;
                searchForm.submit();
            });
        });
    }

    // 初始化新闻卡片事件
    initNewsCardEvents();

    // 绑定分页事件
    function bindPaginationEvents() {
        // 绑定所有分页链接（包括上一页、下一页、页码）
        const paginationLinks = document.querySelectorAll('.pagination a, .pagination .page-link');
        paginationLinks.forEach(link => {
            // 移除之前的事件监听器
            link.removeEventListener('click', handlePaginationClick);
            // 添加新的事件监听器
            link.addEventListener('click', handlePaginationClick);
        });
    }
    
    // 分页点击处理函数
    function handlePaginationClick(e) {
        e.preventDefault();
        
        // 获取页码
        const href = this.getAttribute('href');
        if (!href || href === '#' || this.classList.contains('disabled')) {
            return;
        }
        
        let page = 1;
        
        // 从href中提取页码
        if (href.includes('page=')) {
            const url = new URL(href, window.location.origin);
            page = url.searchParams.get('page') || 1;
        } else if (href.includes('/page/')) {
            // 处理 /news/page/2 这种格式
            const matches = href.match(/\/page\/(\d+)/);
            if (matches) {
                page = matches[1];
            }
        } else if (this.textContent.trim().match(/^\d+$/)) {
            // 如果链接文本是纯数字，直接使用
            page = this.textContent.trim();
        }
        
        // 显示加载状态
        showLoadingState();
        
        // 获取当前分类和标签
        const currentCategory = document.querySelector('.category-tab.active')?.getAttribute('data-category') || '';
        const currentTag = new URLSearchParams(window.location.search).get('tag') || '';
        
        // 加载对应页面的数据
        loadNewsData(currentCategory, page, currentTag);
        
        // 滚动到新闻区域顶部
        document.getElementById('news-container').scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }

    // 初始化分页事件
    bindPaginationEvents();

    // 侧边栏组件交互
    const sidebarWidgets = document.querySelectorAll('.sidebar-widget');
    sidebarWidgets.forEach(widget => {
        // 折叠/展开功能（可选）
        const header = widget.querySelector('.widget-header');
        if (header && header.hasAttribute('data-collapsible')) {
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                const content = widget.querySelector('.widget-content');
                const isCollapsed = content.style.display === 'none';
                content.style.display = isCollapsed ? 'block' : 'none';
                
                // 添加折叠图标动画
                const icon = header.querySelector('i');
                if (icon) {
                    icon.style.transform = isCollapsed ? 'rotate(0deg)' : 'rotate(180deg)';
                }
            });
        }
    });

    // 热门新闻项悬停效果
    const hotNewsItems = document.querySelectorAll('.hot-news-item');
    hotNewsItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(5px)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });

    // 最新新闻项悬停效果
    const latestNewsItems = document.querySelectorAll('.latest-news-item');
    latestNewsItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(3px)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });

    // 分类统计进度条动画
    const progressBars = document.querySelectorAll('.progress-bar');
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const progressObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const progressBar = entry.target;
                const width = progressBar.style.width;
                progressBar.style.width = '0%';
                setTimeout(() => {
                    progressBar.style.width = width;
                }, 200);
            }
        });
    }, observerOptions);
    
    progressBars.forEach(bar => {
        progressObserver.observe(bar);
    });

    // 绑定标签点击事件
    function bindTagEvents() {
        const tagItems = document.querySelectorAll('.tag-item');
        tagItems.forEach(tag => {
            // 移除之前的事件监听器（如果有）
            tag.removeEventListener('click', handleTagClick);
            // 添加新的事件监听器
            tag.addEventListener('click', handleTagClick);
        });
    }
    
    // 标签点击处理函数
    function handleTagClick(e) {
        e.preventDefault(); // 阻止默认链接跳转
        
        // 获取标签名称
        const tagName = this.textContent.trim();
        
        // 清除分类选择状态
        const categoryTabs = document.querySelectorAll('.category-tab');
        categoryTabs.forEach(tab => {
            tab.classList.remove('active');
            tab.style.opacity = '1';
            tab.style.pointerEvents = 'auto';
        });
        
        // 设置"全部"分类为活动状态
        const allTab = document.querySelector('.category-tab[data-category=""]');
        if (allTab) {
            allTab.classList.add('active');
        }
        
        // 显示加载状态
        showLoadingState();
        
        // 加载标签相关的新闻
        loadNewsData('', 1, tagName);
        
        // 添加点击波纹效果
        const ripple = document.createElement('span');
        ripple.classList.add('ripple');
        this.appendChild(ripple);
        
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = (e.clientX - rect.left - size / 2) + 'px';
        ripple.style.top = (e.clientY - rect.top - size / 2) + 'px';
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }
    
    // 清除筛选函数
    window.clearFilter = function(e) {
        e.preventDefault();
        
        // 清除所有标签的active状态
        updateTagsActiveState('');
        
        // 移除筛选状态提示
        updateFilterStatus('');
        
        // 设置"全部"分类为活动状态
        const categoryTabs = document.querySelectorAll('.category-tab');
        categoryTabs.forEach(tab => {
            tab.classList.remove('active');
            tab.style.opacity = '1';
            tab.style.pointerEvents = 'auto';
        });
        
        const allTab = document.querySelector('.category-tab[data-category=""]');
        if (allTab) {
            allTab.classList.add('active');
        }
        
        // 显示加载状态
        showLoadingState();
        
        // 加载全部新闻
        loadNewsData('', 1, '');
    };
    
    // 初始化标签云交互效果
    bindTagEvents();

    // 3D插画动画效果增强
    const newsIllustration = document.querySelector('.news-illustration');
    if (newsIllustration) {
        // 鼠标移动视差效果
        document.addEventListener('mousemove', function(e) {
            const mouseX = (e.clientX / window.innerWidth - 0.5) * 2;
            const mouseY = (e.clientY / window.innerHeight - 0.5) * 2;
            
            // 报纸元素
            const newspaper = newsIllustration.querySelector('.newspaper');
            if (newspaper) {
                newspaper.style.transform = `rotate(-15deg) translate(${mouseX * 5}px, ${mouseY * 5}px)`;
            }
            
            // 信息气泡
            const bubbles = newsIllustration.querySelectorAll('.info-bubble');
            bubbles.forEach((bubble, index) => {
                const speed = (index + 1) * 3;
                bubble.style.transform = `translate(${mouseX * speed}px, ${mouseY * speed}px) scale(${1 + mouseX * 0.1})`;
            });
            
            // 文档堆叠
            const docStack = newsIllustration.querySelector('.document-stack');
            if (docStack) {
                docStack.style.transform = `translate(${mouseX * 8}px, ${mouseY * 8}px)`;
            }
        });
        
        // 滚动视差效果
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            newsIllustration.style.transform = `translateY(${rate}px)`;
        });
    }

    // 平滑滚动到顶部功能
    const backToTopBtn = document.createElement('button');
    backToTopBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
    backToTopBtn.className = 'back-to-top-btn';
    backToTopBtn.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border: none;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        cursor: pointer;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1000;
        box-shadow: 0 4px 15px rgba(44, 123, 229, 0.3);
    `;
    
    document.body.appendChild(backToTopBtn);
    
    // 显示/隐藏返回顶部按钮
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopBtn.style.opacity = '1';
            backToTopBtn.style.visibility = 'visible';
        } else {
            backToTopBtn.style.opacity = '0';
            backToTopBtn.style.visibility = 'hidden';
        }
    });
    
    // 返回顶部功能
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // 延迟加载图片优化
    const images = document.querySelectorAll('img[loading="lazy"]');
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src || img.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    }
});
</script>

{include file="common/footer"} 
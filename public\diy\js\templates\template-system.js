/**
 * 完整网站模板系统
 * 支持整站模板、页面管理、组件编辑
 */

// 网站模板数据结构
const websiteTemplates = {
    'sanzhiyu': {
        name: '三只鱼模板',
        description: '专业企业官网模板，适合科技公司',
        preview: '/assets/images/ceo-apply-bg.png',
        
        // 网站全局设置
        globalSettings: {
            siteName: '创新科技企业',
            logo: '/assets/images/logo.png',
            primaryColor: '#667eea',
            secondaryColor: '#764ba2',
            fontFamily: 'Microsoft YaHei, sans-serif'
        },
        
        // 页面配置
        pages: {
            home: {
                name: '首页',
                path: '/',
                icon: 'home',
                components: [
                    {
                        type: 'navbar',
                        id: 'navbar-1',
                        properties: {
                            logoType: 'text',
                            logoText: '网站Logo',
                            logoColor: '#2d3748',
                            logoFontSize: 20,
                            bgColor: '#ffffff',
                            textColor: '#2d3748',
                            hoverColor: '#667eea',
                            shadow: true,
                            sticky: false,
                            btn1Text: '登录',
                            btn1Color: '#667eea',
                            btn1BorderColor: '#667eea',
                            btn1BorderRadius: 6,
                            btn2Text: '注册',
                            btn2Color: '#ffffff',
                            btn2BackgroundColor: '#667eea',
                            btn2BorderRadius: 6,
                            menuItems: [
                                { name: '首页', link: '#', type: 'normal', children: [] },
                                {
                                    name: '产品介绍',
                                    link: '#',
                                    type: 'dropdown',
                                    children: [
                                        { name: '产品A', link: '#' },
                                        { name: '产品B', link: '#' }
                                    ]
                                },
                                {
                                    name: '解决方案',
                                    link: '#',
                                    type: 'dropdown',
                                    children: [
                                        { name: '企业方案', link: '#' },
                                        { name: '个人方案', link: '#' }
                                    ]
                                },
                                { name: '客户案例', link: '#', type: 'normal', children: [] },
                                { name: '新闻资讯', link: '#', type: 'normal', children: [] },
                                { name: '关于我们', link: '#', type: 'normal', children: [] },
                                { name: '联系我们', link: '#', type: 'normal', children: [] }
                            ]
                        }
                    },
                    // {
                    //     type: 'hero',
                    //     id: 'hero-1',
                    //     properties: {
                    //         title: '专业企业建站解决方案',
                    //         subtitle: '助力企业数字化转型，打造专业品牌形象',
                    //         description: '提供一站式企业官网建设服务，从设计到开发，从上线到运维，全程专业支持',
                    //         backgroundType: 'gradient',
                    //         backgroundGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    //         showButtons: true,
                    //         primaryButtonText: '了解详情',
                    //         secondaryButtonText: '免费咨询'
                    //     }
                    // },
                    {
                        type: 'card',
                        id: 'features-1',
                        properties: {
                            styleMode: 'system',
                            columnsCount: 3,
                            layoutMode: 'icon-top',
                            cardSpacing: 30,

                            // 区块设置
                            sectionTitle: '为什么选择我们',
                            sectionSubtitle: '专业、可靠、高效的企业级解决方案',
                            sectionBackground: 'image',
                            sectionBackgroundImage: '/assets/images/ceo-home-vip-bg.png',
                            sectionBackgroundColor: '#667eea',
                            sectionBackgroundGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            // 特殊标识
                            isWhyChooseUs: true,
                            cards: [
                                {
                                    title: '快速部署',
                                    content: '专业团队快速响应，高效部署实施，让您的项目快速上线',
                                    showIcon: true,
                                    iconText: '⚡',
                                    showButton: false,
                                    buttonText: '',
                                    buttonLink: '#'
                                },
                                {
                                    title: '安全可靠',
                                    content: '企业级安全保障，多重防护机制，确保数据安全无忧',
                                    showIcon: true,
                                    iconText: '🛡️',
                                    showButton: false,
                                    buttonText: '',
                                    buttonLink: '#'
                                },
                                {
                                    title: '定制开发',
                                    content: '根据业务需求量身定制，满足个性化要求，提供最佳解决方案',
                                    showIcon: true,
                                    iconText: '⚙️',
                                    showButton: false,
                                    buttonText: '',
                                    buttonLink: '#'
                                },
                                {
                                    title: '7×24服务',
                                    content: '全天候技术支持，专业客服团队，及时响应解决问题',
                                    showIcon: true,
                                    iconText: '🎧',
                                    showButton: false,
                                    buttonText: '',
                                    buttonLink: '#'
                                },
                                {
                                    title: '数据分析',
                                    content: '智能数据分析，深度挖掘业务价值，助力决策优化',
                                    showIcon: true,
                                    iconText: '📊',
                                    showButton: false,
                                    buttonText: '',
                                    buttonLink: '#'
                                },
                                {
                                    title: '团队协作',
                                    content: '高效团队协作工具，提升工作效率，促进团队沟通',
                                    showIcon: true,
                                    iconText: '👥',
                                    showButton: false,
                                    buttonText: '',
                                    buttonLink: '#'
                                }
                            ],
                            bgColor: '#ffffff',
                            titleColor: '#2d3748',
                            textColor: '#4a5568',
                            buttonColor: '#667eea',
                            buttonTextColor: '#ffffff',
                            buttonStyle: 'filled',
                            borderRadius: 16,
                            padding: 40,
                            shadow: true,
                            shadowIntensity: 0.1,
                            titleSize: 20,
                            contentSize: 15,
                            iconSize: 48,
                            iconColor: '#667eea',

                            // 尺寸设置
                            maxWidth: 1200,
                            marginHorizontal: 20,
                            marginVertical: 20,
                            positionVertical: 0,

                            // 字体设置
                            buttonSize: 14,
                            textAlign: 'left',
                            imageHeight: 200
                        }
                    },
                    {
                        type: 'section',
                        id: 'company-intro-1',
                        properties: {
                            title: '企业实力展示',
                            subtitle: '专业团队，卓越服务，值得信赖的合作伙伴',
                            content: '10年+行业经验\n500+成功案例\n专业技术团队\n7×24技术支持',
                            backgroundImage: '/assets/images/ceo-apply-bg.png',
                            layout: 'image-right',
                            showButtons: true,
                            primaryButtonText: '了解更多',
                            secondaryButtonText: '联系我们'
                        },

                    },
                    {
                        type: 'card',
                        id: 'news-1',
                        properties: {
                            styleMode: 'system',
                            columnsCount: 3,
                            layoutMode: 'image-top',
                            cardSpacing: 30,

                            // 数据源设置
                            dataSource: 'news', // 默认动态数据源
                            dataLimit: 6,

                            // 区块设置
                            sectionTitle: '最新动态',
                            sectionSubtitle: '关注我们的最新资讯和行业动态',
                            sectionBackground: 'image',
                            sectionBackgroundImage: '/assets/images/ceo-apply-bg1.png',
                            sectionBackgroundColor: '#f8fafc',
                            sectionBackgroundGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            isLatestNews: true, // 标识为最新动态模板，移除遮罩层
                            cards: [
                                {
                                    title: '小红书小程序，酒店运营版！',
                                    content: '小程序是一种无需下载、用完即走的新应用，它具备丰富的框架能力和优秀的产品体验...',
                                    showImage: true,
                                    imageUrl: '/assets/images/news1.png',
                                    selectedImageIndex: 0,
                                    showButton: true,
                                    buttonText: '阅读更多',
                                    buttonLink: '#',
                                    // 新闻专用字段
                                    category: '产品发布',
                                    date: '2025-06-04',
                                    day: '04',
                                    month: 'Jun',
                                    views: '6',
                                    excerpt: '小程序是一种无需下载、用完即走的新应用，它具备丰富的框架能力和优秀的产品体验，第三方开发者以此为载体提供服务。...'
                                },
                                {
                                    title: '企业建站版本v1',
                                    content: '企业建站版本v1 马上和大家见面了！期待吧...',
                                    showImage: true,
                                    imageUrl: '/assets/images/news2.png',
                                    selectedImageIndex: 1,
                                    showButton: true,
                                    buttonText: '阅读更多',
                                    buttonLink: '#',
                                    // 新闻专用字段
                                    category: '公司动态',
                                    date: '2025-06-03',
                                    day: '03',
                                    month: 'Jun',
                                    views: '2',
                                    excerpt: '企业建站版本v1 马上和大家见面了！期待吧...'
                                },
                                {
                                    title: '微信开通千2费率',
                                    content: '开通微信千2费率...',
                                    showImage: true,
                                    imageUrl: '/assets/images/news3.png',
                                    selectedImageIndex: 2,
                                    showButton: true,
                                    buttonText: '阅读更多',
                                    buttonLink: '#',
                                    // 新闻专用字段
                                    category: '产品发布',
                                    date: '2025-06-03',
                                    day: '03',
                                    month: 'Jun',
                                    views: '2',
                                    excerpt: '开通微信千2费率...'
                                }
                            ],
                            bgColor: '#ffffff',
                            titleColor: '#2d3748',
                            textColor: '#4a5568',
                            buttonColor: '#667eea',
                            buttonTextColor: '#ffffff',
                            buttonStyle: 'filled',
                            borderRadius: 12,
                            padding: 24,
                            shadow: true,
                            shadowIntensity: 0.08,
                            titleSize: 18,
                            contentSize: 14,
                            imageHeight: 200,

                            // 尺寸设置
                            maxWidth: 1200,
                            marginHorizontal: 20,
                            marginVertical: 20,
                            positionVertical: 0,
                            iconSize: 48,
                            iconColor: '#667eea',

                            // 字体设置
                            buttonSize: 14,
                            textAlign: 'left',

                            // 底部按钮设置
                            showMoreButton: true,
                            moreButtonText: '查看更多动态',
                            moreButtonLink: '/news',
                            moreButtonIcon: '📰'
                        }
                    },
                    {
                        type: 'stats',
                        id: 'stats-1',
                        properties: {
                            // 统计项数据数组
                            stats: [
                                { number: '100', label: '成功案例', icon: 'users', iconType: 'lucide', suffix: '+' },
                                { number: '30', label: '合作客户', icon: 'handshake', iconType: 'lucide', suffix: '+' },
                                { number: '10', label: '专业团队', icon: 'link', iconType: 'lucide', suffix: '+' },
                                { number: '22', label: '服务年限', icon: 'clock', iconType: 'lucide', suffix: '+' }
                            ],

                            // 布局设置
                            columnsCount: 4,
                            layout: 'horizontal',
                            alignment: 'center',

                            // 全局样式设置
                            styleTemplate: 'strength',
                            stylePreset: '实力见证',
                            bgColor: 'rgba(255, 255, 255, 0.05)',
                            numberColor: '#ffffff',
                            labelColor: '#ffffff',
                            iconColor: '#10d5c2',
                            borderColor: 'rgba(255, 255, 255, 0.1)',

                            // 实力见证模板专用设置
                            strengthBgType: 'combined',
                            strengthBgColor: '#1a1a2e',
                            strengthBgGradient: 'linear-gradient(135deg, rgba(26, 26, 46, 0.9) 0%, rgba(16, 213, 194, 0.1) 100%)',
                            strengthBgImage: '/assets/images/index_section6_bg.png',
                            strengthBgImageOpacity: 0.3,
                            strengthTitle: '实力见证',
                            strengthSubtitle: '用数据说话，用实力证明',
                            strengthIconSize: 48,
                            showTrustIndicators: true,
                            trustIndicators: [
                                { icon: 'shield', text: '安全可靠' },
                                { icon: 'clock', text: '7×24服务' },
                                { icon: 'award', text: '权威认证' },
                                { icon: 'thumbs-up', text: '客户满意' }
                            ],

                            // 尺寸设置
                            maxWidth: 1200,
                            borderRadius: 12,
                            padding: 32,
                            itemSpacing: 24,
                            marginHorizontal: 20,
                            marginVertical: 20,
                            positionVertical: 0,

                            // 字体设置
                            numberSize: 36,
                            labelSize: 16,
                            iconSize: 48,
                            fontWeight: 'bold',

                            // 边框和阴影设置
                            showBorder: false,
                            borderWidth: 1,
                            shadow: true,
                            shadowIntensity: 0.1,

                            // 动画设置
                            animation: false,
                            animationDuration: 2000,
                            animationDelay: 200,

                            // 响应式设置
                            mobileColumns: 2,
                            tabletColumns: 3
                        }
                    },
                    {
                        type: 'footer',
                        id: 'footer-1',
                        properties: {
                            style: 'style1',
                            siteName: '三只鸟网络',
                            copyright: '© 2024 版权所有. 保留所有权利.',
                            companyInfo: {
                                logo: '/assets/images/logo.png',
                                description: '我们提供专业的企业解决方案，助力企业数字化转型，打造专业品牌形象，提供一站式服务支持。',
                                wechatQR: '/assets/images/weixin.png',
                                address: '北京市朝阳区科技园',
                                phone: '************',
                                email: '<EMAIL>',
                                qq: '*********'
                            },
                            solutions: [
                                { name: '社交分销', link: '/solutions/social-distribution' },
                                { name: '智能多门店', link: '/solutions/multi-store' },
                                { name: '大型多商户', link: '/solutions/multi-merchant' },
                                { name: '大货批发', link: '/solutions/wholesale' },
                                { name: '平台级供货商', link: '/solutions/supplier' },
                                { name: '本地生活服务', link: '/solutions/local-service' }
                            ],
                            products: [
                                { name: '电商系统', link: '/products/ecommerce' },
                                { name: '分销系统', link: '/products/distribution' },
                                { name: '管理系统', link: '/products/management' },
                                { name: '移动应用', link: '/products/mobile-app' },
                                { name: '定制开发', link: '/products/custom' }
                            ],
                            support: [
                                { name: '帮助中心', link: '/help' },
                                { name: '开发文档', link: '/docs' },
                                { name: '常见问题', link: '/faq' },
                                { name: '技术支持', link: '/contact' },
                                { name: '意见反馈', link: '/feedback' }
                            ]
                        }
                    }
                ]
            },
            
            about: {
                name: '关于我们',
                path: '/about',
                icon: 'info',
                components: [
                    {
                        type: 'hero',
                        id: 'about-hero',
                        properties: {
                            title: '关于我们',
                            subtitle: '专业团队，专注创新',
                            backgroundType: 'image',
                            backgroundImage: '/assets/images/about-bg.jpg'
                        }
                    },
                    {
                        type: 'textblock',
                        id: 'company-story',
                        properties: {
                            title: '公司简介',
                            content: '我们是一家专注于企业数字化转型的科技公司...'
                        }
                    },
                    {
                        type: 'team',
                        id: 'team-members',
                        properties: {
                            title: '核心团队',
                            members: [
                                {
                                    name: '张总',
                                    position: 'CEO',
                                    avatar: '/assets/images/team/ceo.jpg',
                                    bio: '10年行业经验，专注产品创新'
                                }
                            ]
                        }
                    }
                ]
            },
            
            cases: {
                name: '客户案例',
                path: '/cases',
                icon: 'folder',
                components: [
                    {
                        type: 'hero',
                        id: 'cases-hero',
                        properties: {
                            title: '客户案例',
                            subtitle: '成功案例展示'
                        }
                    },
                    {
                        type: 'card',
                        id: 'case-list',
                        properties: {
                            title: '精选案例',
                            cards: [
                                {
                                    image: '/assets/images/cases/case1.jpg',
                                    title: '某科技公司官网',
                                    description: '现代化企业官网设计'
                                }
                            ]
                        }
                    }
                ]
            },
            
            news: {
                name: '新闻资讯',
                path: '/news',
                icon: 'newspaper',
                components: [
                    {
                        type: 'hero',
                        id: 'news-hero',
                        properties: {
                            title: '新闻资讯',
                            subtitle: '最新动态和行业资讯'
                        }
                    },
                    {
                        type: 'card',
                        id: 'news-list',
                        properties: {
                            title: '最新资讯',
                            layout: 'news-style'
                        }
                    }
                ]
            },
            
            contact: {
                name: '联系我们',
                path: '/contact',
                icon: 'phone',
                components: [
                    {
                        type: 'hero',
                        id: 'contact-hero',
                        properties: {
                            title: '联系我们',
                            subtitle: '随时为您提供专业服务'
                        }
                    },
                    {
                        type: 'contact',
                        id: 'contact-info',
                        properties: {
                            title: '联系方式',
                            contacts: [
                                {
                                    type: 'phone',
                                    label: '联系电话',
                                    value: '************',
                                    icon: 'phone'
                                },
                                {
                                    type: 'email',
                                    label: '邮箱地址',
                                    value: '<EMAIL>',
                                    icon: 'mail'
                                },
                                {
                                    type: 'address',
                                    label: '公司地址',
                                    value: '北京市朝阳区科技园',
                                    icon: 'map-pin'
                                }
                            ]
                        }
                    }
                ]
            }
        }
    }
};

// 模板管理器
const TemplateManager = {
    currentTemplate: null,
    currentPage: 'home',
    
    // 应用整站模板
    applyWebsiteTemplate(templateId) {
        const template = websiteTemplates[templateId];
        if (!template) {
            console.error('模板不存在:', templateId);
            return;
        }
        
        this.currentTemplate = template;
        
        // 应用全局设置
        this.applyGlobalSettings(template.globalSettings);
        
        // 显示首页
        this.showPage('home');
        
        // 更新页面管理器
        this.updatePageManager(template.pages);
        
        console.log('✅ 已应用模板:', template.name);
    },
    
    // 应用全局设置
    applyGlobalSettings(settings) {
        // 更新网站标题
        document.title = settings.siteName;
        
        // 更新CSS变量
        document.documentElement.style.setProperty('--primary-color', settings.primaryColor);
        document.documentElement.style.setProperty('--secondary-color', settings.secondaryColor);
        document.documentElement.style.setProperty('--font-family', settings.fontFamily);
    },
    
    // 显示指定页面
    showPage(pageId) {
        console.log('📄 显示页面:', pageId);

        if (!this.currentTemplate) {
            console.error('❌ 当前模板不存在');
            return;
        }

        const page = this.currentTemplate.pages[pageId];
        if (!page) {
            console.error('❌ 页面不存在:', pageId);
            return;
        }

        this.currentPage = pageId;

        // 清空画布
        const canvas = document.getElementById('canvas');
        if (!canvas) {
            console.error('❌ 画布容器未找到 #canvas');
            return;
        }

        // 清空画布内容
        canvas.innerHTML = '';

        console.log(`🔄 开始渲染页面 "${page.name}"，组件数量:`, page.components.length);

        // 渲染页面组件
        if (page.components.length === 0) {
            canvas.innerHTML = `
                <div style="text-align: center; padding: 100px 20px; color: #a0aec0;">
                    <h2>${page.name}</h2>
                    <p>此页面暂无内容，从左侧组件库添加组件</p>
                </div>
            `;
        } else {
            page.components.forEach((component, index) => {
                console.log(`🔧 渲染组件 ${index + 1}/${page.components.length}:`, component.type);
                this.renderComponent(component);

                // 如果是卡片组件，渲染完成后自动选中以更新属性面板
                if (component.type === 'card' && index === page.components.length - 1) {
                    setTimeout(() => {
                        if (typeof selectComponent === 'function') {
                            selectComponent(component.id);
                        }
                    }, 200);
                }
            });
        }

        // 更新页面管理器状态
        this.updatePageManagerState(pageId);

        console.log('✅ 已切换到页面:', page.name);
    },
    
    // 渲染组件
    renderComponent(componentData) {
        console.log('🔧 开始渲染组件:', componentData.type, componentData.id);

        const canvas = document.getElementById('canvas');
        if (!canvas) {
            console.error('❌ 画布容器未找到');
            return;
        }

        const componentElement = document.createElement('div');
        componentElement.className = 'component-block';
        componentElement.dataset.componentType = componentData.type;
        componentElement.dataset.type = componentData.type; // 兼容性
        componentElement.id = componentData.id;

        // 添加组件控制按钮
        componentElement.innerHTML = `
            <div class="component-controls">
                <button class="control-btn" onclick="moveUp('${componentData.id}')" title="上移">上移</button>
                <button class="control-btn" onclick="moveDown('${componentData.id}')" title="下移">下移</button>
                <button class="control-btn" onclick="moveToTop('${componentData.id}')" title="置顶">置顶</button>
                <button class="control-btn" onclick="moveToBottom('${componentData.id}')" title="置底">置底</button>
                <button class="control-btn" onclick="deleteComponent('${componentData.id}')" title="删除">删除</button>
            </div>
        `;

        // 应用组件模板
        if (typeof ComponentManager !== 'undefined') {
            const template = ComponentManager.getTemplate(componentData.type);
            if (template) {
                console.log('✅ 找到组件模板:', componentData.type);

                // 添加组件HTML到控制按钮后面
                componentElement.innerHTML += template.html;

                // 设置组件属性
                this.setComponentProperties(componentElement, componentData);

                // 确保属性与模板默认属性合并
                const defaultProperties = ComponentManager.getDefaultProperties(componentData.type);
                const mergedProperties = ComponentManager.mergeProperties(defaultProperties, componentData.properties);

                // 更新显示
                const updater = ComponentManager.getDisplayUpdater(componentData.type);
                if (updater) {
                    console.log('🎨 应用组件样式:', componentData.type);
                    setTimeout(() => {
                        updater(componentElement, mergedProperties);

                        // 特殊处理：导航栏组件需要确保z-index层级
                        if (componentData.type === 'navbar') {
                            console.log('🔧 为导航栏组件设置特殊z-index');
                            const navbarContent = componentElement.querySelector('.navbar-component');
                            if (navbarContent) {
                                navbarContent.style.position = 'relative';
                                navbarContent.style.zIndex = '9998';  // 稍微降低，让组件控制按钮能显示在上面
                                // 确保下拉菜单也有正确的z-index
                                const dropdowns = navbarContent.querySelectorAll('.navbar-dropdown');
                                dropdowns.forEach(dropdown => {
                                    dropdown.style.zIndex = '9999';  // 下拉菜单保持高层级
                                });
                            }
                        }

                        // 特殊处理：如果是卡片组件且有动态数据源，需要获取数据
                        if (componentData.type === 'card' && mergedProperties.dataSource && mergedProperties.dataSource !== 'static') {
                            console.log('🔄 卡片组件使用动态数据源，开始获取数据:', mergedProperties.dataSource);
                            console.log('🔍 组件属性详情:', mergedProperties);
                            if (typeof fetchCardData === 'function') {
                                fetchCardData(componentElement, mergedProperties);
                            }
                        }
                    }, 100); // 增加延迟确保DOM完全渲染
                } else {
                    console.warn('⚠️ 组件更新器未找到:', componentData.type);
                }
            } else {
                console.warn('⚠️ 组件模板未找到:', componentData.type);
                componentElement.innerHTML += `
                    <div style="padding: 40px 20px; text-align: center; color: #e53e3e; border: 2px dashed #e53e3e; border-radius: 8px; margin: 10px;">
                        <h3>组件 ${componentData.type} 模板未找到</h3>
                        <p>请检查组件是否正确注册</p>
                    </div>
                `;
            }
        } else {
            console.error('❌ ComponentManager 未定义');
            componentElement.innerHTML += `
                <div style="padding: 40px 20px; text-align: center; color: #e53e3e; border: 2px dashed #e53e3e; border-radius: 8px; margin: 10px;">
                    <h3>组件管理器未加载</h3>
                    <p>请检查 ComponentManager 是否正确加载</p>
                </div>
            `;
        }

        // 绑定点击事件
        componentElement.addEventListener('click', (e) => {
            e.stopPropagation();
            if (typeof selectComponent === 'function') {
                selectComponent(componentData.id);
            } else {
                console.warn('⚠️ selectComponent 函数未找到');
            }
        });

        canvas.appendChild(componentElement);
        console.log('✅ 组件已添加到画布:', componentData.type, componentData.id);
    },

    // 设置组件属性
    setComponentProperties(componentElement, componentData) {
        const type = componentData.type;

        // 获取默认属性并与传入属性合并
        const defaultProperties = ComponentManager.getDefaultProperties(type);
        const mergedProperties = ComponentManager.mergeProperties(defaultProperties, componentData.properties);

        // 根据组件类型设置特定的属性存储
        switch (type) {
            case 'navbar':
                componentElement._navbarProperties = { ...mergedProperties };
                break;
            case 'hero':
                componentElement._heroProperties = { ...mergedProperties };
                break;
            case 'card':
                componentElement._cardProperties = { ...mergedProperties };
                break;
            case 'stats':
                componentElement._statsProperties = { ...mergedProperties };
                break;
            case 'team':
                componentElement._teamProperties = { ...mergedProperties };
                break;
            case 'testimonials':
                componentElement._testimonialsProperties = { ...mergedProperties };
                break;
            case 'contact':
                componentElement._contactProperties = { ...mergedProperties };
                break;
            case 'textblock':
                componentElement.componentProperties = { ...mergedProperties };
                break;
            case 'section':
                componentElement._sectionProperties = { ...mergedProperties };
                break;
            case 'footer':
                componentElement._footerProperties = { ...mergedProperties };
                break;
            default:
                componentElement._properties = { ...mergedProperties };
        }
    },
    
    // 更新页面管理器
    updatePageManager(pages, retryCount = 0) {
        console.log('🔄 更新页面管理器，页面数量:', Object.keys(pages).length);

        const pageManager = document.getElementById('page-manager');
        if (!pageManager) {
            console.warn('⚠️ 页面管理器容器未找到 #page-manager');

            // 重试机制，最多重试2次（减少重试次数）
            if (retryCount < 2) {
                console.log(`⏳ 第${retryCount + 1}次重试，等待DOM更新...`);
                setTimeout(() => {
                    this.updatePageManager(pages, retryCount + 1);
                }, 300);
                return;
            } else {
                console.warn('⚠️ 重试后仍未找到页面管理器容器，可能已在其他地方处理');
                return;
            }
        }

        console.log('✅ 找到页面管理器容器');

        const pageListHTML = Object.entries(pages).map(([pageId, page]) => `
            <div class="page-item ${pageId === this.currentPage ? 'active' : ''}"
                 data-page-id="${pageId}"
                 onclick="showPage('${pageId}')">
                <span class="page-icon">📄</span>
                <span class="page-name">${page.name}</span>
            </div>
        `).join('');

        pageManager.innerHTML = `
            <div class="page-manager-header">
                <h4>页面管理</h4>
                <p style="font-size: 12px; color: #666; margin: 5px 0 0 0;">点击页面名称切换编辑</p>
            </div>
            <div class="page-list">
                ${pageListHTML}
            </div>
            <div class="page-actions">
                <button class="btn-add-page" onclick="addPage()">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="12" y1="5" x2="12" y2="19"/>
                        <line x1="5" y1="12" x2="19" y2="12"/>
                    </svg>
                    添加页面
                </button>
            </div>
        `;

        console.log('✅ 页面管理器已更新，当前页面:', this.currentPage);
    },
    
    // 更新页面管理器状态
    updatePageManagerState(activePageId) {
        const pageItems = document.querySelectorAll('.page-item');
        pageItems.forEach(item => {
            item.classList.toggle('active', item.dataset.pageId === activePageId);
        });
    },
    
    // 添加新页面
    addPage() {
        const pageName = prompt('请输入页面名称:');
        if (!pageName) return;
        
        const pageId = pageName.toLowerCase().replace(/\s+/g, '-');
        const newPage = {
            name: pageName,
            path: `/${pageId}`,
            icon: 'file',
            components: [
                {
                    type: 'hero',
                    id: `${pageId}-hero`,
                    properties: {
                        title: pageName,
                        subtitle: `${pageName}页面`
                    }
                }
            ]
        };
        
        this.currentTemplate.pages[pageId] = newPage;
        this.updatePageManager(this.currentTemplate.pages);
        this.showPage(pageId);
    },
    
    // 删除页面
    deletePage(pageId) {
        if (pageId === 'home') {
            alert('首页不能删除');
            return;
        }
        
        if (confirm('确定要删除这个页面吗？')) {
            delete this.currentTemplate.pages[pageId];
            this.updatePageManager(this.currentTemplate.pages);
            this.showPage('home');
        }
    }
};

// 导出模块
window.TemplateManager = TemplateManager;
window.websiteTemplates = websiteTemplates;

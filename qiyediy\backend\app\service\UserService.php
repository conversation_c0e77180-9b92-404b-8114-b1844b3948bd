<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 用户服务
 */

declare(strict_types=1);

namespace app\service;

use app\model\User;
use app\model\Role;
use think\db\exception\DataNotFoundException;
use think\facade\Db;

/**
 * 用户服务类
 */
class UserService
{
    /**
     * 获取用户列表
     * @param array $params 查询参数
     * @return object
     */
    public function getList(array $params): object
    {
        $query = User::with(['roles']);

        // 搜索条件
        if (!empty($params['keyword'])) {
            $query->search($params['keyword']);
        }

        // 状态筛选
        if (isset($params['status']) && $params['status'] !== '') {
            $query->status((int)$params['status']);
        }

        // 角色筛选
        if (!empty($params['role_id'])) {
            $query->role((int)$params['role_id']);
        }

        // 时间范围
        if (!empty($params['start_date'])) {
            $query->where('created_at', '>=', $params['start_date'] . ' 00:00:00');
        }
        if (!empty($params['end_date'])) {
            $query->where('created_at', '<=', $params['end_date'] . ' 23:59:59');
        }

        // 排序
        $sortField = $params['sort_field'] ?? 'created_at';
        $sortOrder = $params['sort_order'] ?? 'desc';
        $query->order($sortField, $sortOrder);

        return $query->paginate($params['per_page'] ?? 15);
    }

    /**
     * 根据ID获取用户
     * @param int $id 用户ID
     * @return User|null
     */
    public function getById(int $id): ?User
    {
        return User::with(['roles'])->find($id);
    }

    /**
     * 创建用户
     * @param array $data 用户数据
     * @return User
     * @throws \Exception
     */
    public function create(array $data): User
    {
        Db::startTrans();
        try {
            // 创建用户
            $user = User::createUser($data);

            Db::commit();
            return $user;

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 更新用户
     * @param int $id 用户ID
     * @param array $data 更新数据
     * @return User
     * @throws \Exception
     */
    public function update(int $id, array $data): User
    {
        $user = User::find($id);
        if (!$user) {
            throw new \Exception('用户不存在');
        }

        Db::startTrans();
        try {
            // 更新用户信息
            $user->updateUser($data);

            Db::commit();
            return $user->refresh();

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 删除用户
     * @param int $id 用户ID
     * @return bool
     * @throws \Exception
     */
    public function delete(int $id): bool
    {
        $user = User::find($id);
        if (!$user) {
            throw new \Exception('用户不存在');
        }

        // 检查是否为超级管理员
        if ($user->isSuperAdmin()) {
            throw new \Exception('不能删除超级管理员');
        }

        return $user->delete();
    }

    /**
     * 批量删除用户
     * @param array $ids 用户ID数组
     * @return int 删除数量
     * @throws \Exception
     */
    public function batchDelete(array $ids): int
    {
        // 检查是否包含超级管理员
        $superAdminCount = User::whereIn('id', $ids)
            ->whereExists(function ($query) {
                $query->table('user_roles ur')
                      ->join('roles r', 'ur.role_id', '=', 'r.id')
                      ->where('r.slug', 'super_admin')
                      ->whereRaw('ur.user_id = users.id');
            })->count();

        if ($superAdminCount > 0) {
            throw new \Exception('不能删除超级管理员');
        }

        return User::whereIn('id', $ids)->delete();
    }

    /**
     * 启用用户
     * @param int $id 用户ID
     * @return bool
     * @throws \Exception
     */
    public function enable(int $id): bool
    {
        $user = User::find($id);
        if (!$user) {
            throw new \Exception('用户不存在');
        }

        return $user->enable();
    }

    /**
     * 禁用用户
     * @param int $id 用户ID
     * @return bool
     * @throws \Exception
     */
    public function disable(int $id): bool
    {
        $user = User::find($id);
        if (!$user) {
            throw new \Exception('用户不存在');
        }

        // 检查是否为超级管理员
        if ($user->isSuperAdmin()) {
            throw new \Exception('不能禁用超级管理员');
        }

        return $user->disable();
    }

    /**
     * 重置密码
     * @param int $id 用户ID
     * @param string $password 新密码
     * @return bool
     * @throws \Exception
     */
    public function resetPassword(int $id, string $password): bool
    {
        $user = User::find($id);
        if (!$user) {
            throw new \Exception('用户不存在');
        }

        return $user->resetPassword($password);
    }

    /**
     * 分配角色
     * @param int $id 用户ID
     * @param array $roleIds 角色ID数组
     * @return bool
     * @throws \Exception
     */
    public function assignRoles(int $id, array $roleIds): bool
    {
        $user = User::find($id);
        if (!$user) {
            throw new \Exception('用户不存在');
        }

        // 验证角色是否存在
        if (!empty($roleIds)) {
            $existingRoles = Role::whereIn('id', $roleIds)->count();
            if ($existingRoles !== count($roleIds)) {
                throw new \Exception('部分角色不存在');
            }
        }

        return $user->assignRoles($roleIds);
    }

    /**
     * 获取用户统计
     * @return array
     */
    public function getStatistics(): array
    {
        $total = User::count();
        $enabled = User::where('status', User::STATUS_ENABLED)->count();
        $disabled = User::where('status', User::STATUS_DISABLED)->count();
        
        // 今日新增
        $todayNew = User::whereDate('created_at', date('Y-m-d'))->count();
        
        // 本周新增
        $weekStart = date('Y-m-d', strtotime('this week'));
        $weekNew = User::where('created_at', '>=', $weekStart)->count();
        
        // 本月新增
        $monthStart = date('Y-m-01');
        $monthNew = User::where('created_at', '>=', $monthStart)->count();

        // 最近登录
        $recentLogin = User::where('last_login_at', '>=', date('Y-m-d H:i:s', strtotime('-7 days')))
                          ->count();

        // 角色分布
        $roleStats = Db::table('user_roles ur')
            ->join('roles r', 'ur.role_id', '=', 'r.id')
            ->field('r.name, COUNT(*) as count')
            ->group('r.id, r.name')
            ->select()
            ->toArray();

        return [
            'total' => $total,
            'enabled' => $enabled,
            'disabled' => $disabled,
            'today_new' => $todayNew,
            'week_new' => $weekNew,
            'month_new' => $monthNew,
            'recent_login' => $recentLogin,
            'role_stats' => $roleStats
        ];
    }

    /**
     * 导出用户
     * @param array $params 导出参数
     * @return string 文件路径
     * @throws \Exception
     */
    public function export(array $params): string
    {
        // 获取用户数据
        $query = User::with(['roles']);

        // 应用筛选条件
        if (!empty($params['keyword'])) {
            $query->search($params['keyword']);
        }
        if (isset($params['status']) && $params['status'] !== '') {
            $query->status((int)$params['status']);
        }
        if (!empty($params['role_id'])) {
            $query->role((int)$params['role_id']);
        }

        $users = $query->select();

        // 准备导出数据
        $exportData = [];
        $exportData[] = ['ID', '用户名', '邮箱', '真实姓名', '手机号', '状态', '角色', '创建时间', '最后登录'];

        foreach ($users as $user) {
            $roles = $user->roles->column('name');
            $exportData[] = [
                $user->id,
                $user->username,
                $user->email,
                $user->real_name,
                $user->phone,
                $user->status_text,
                implode(',', $roles),
                $user->created_at,
                $user->last_login_at
            ];
        }

        // 生成Excel文件
        $filename = 'users_' . date('YmdHis') . '.xlsx';
        $filePath = upload_path('export', $filename);

        // 这里需要使用PhpSpreadsheet库来生成Excel文件
        // 简化处理，返回CSV格式
        $csvContent = '';
        foreach ($exportData as $row) {
            $csvContent .= implode(',', array_map(function($field) {
                return '"' . str_replace('"', '""', $field) . '"';
            }, $row)) . "\n";
        }

        // 保存文件
        $fullPath = public_path() . '/' . $filePath;
        if (!is_dir(dirname($fullPath))) {
            mkdir(dirname($fullPath), 0755, true);
        }
        file_put_contents($fullPath, "\xEF\xBB\xBF" . $csvContent); // 添加BOM以支持中文

        return $filePath;
    }

    /**
     * 导入用户
     * @param string $filePath 文件路径
     * @return array 导入结果
     * @throws \Exception
     */
    public function import(string $filePath): array
    {
        if (!file_exists($filePath)) {
            throw new \Exception('导入文件不存在');
        }

        // 读取CSV文件
        $handle = fopen($filePath, 'r');
        if (!$handle) {
            throw new \Exception('无法读取导入文件');
        }

        $successCount = 0;
        $errorCount = 0;
        $errors = [];
        $lineNumber = 0;

        // 跳过标题行
        fgetcsv($handle);
        $lineNumber++;

        Db::startTrans();
        try {
            while (($data = fgetcsv($handle)) !== false) {
                $lineNumber++;
                
                try {
                    // 验证数据
                    if (count($data) < 3) {
                        throw new \Exception('数据格式不正确');
                    }

                    $userData = [
                        'username' => trim($data[0]),
                        'email' => trim($data[1]),
                        'password' => trim($data[2]) ?: '123456', // 默认密码
                        'real_name' => trim($data[3] ?? ''),
                        'phone' => trim($data[4] ?? ''),
                        'status' => User::STATUS_ENABLED
                    ];

                    // 验证必填字段
                    if (empty($userData['username']) || empty($userData['email'])) {
                        throw new \Exception('用户名和邮箱不能为空');
                    }

                    // 检查用户名是否存在
                    if (User::where('username', $userData['username'])->exists()) {
                        throw new \Exception('用户名已存在');
                    }

                    // 检查邮箱是否存在
                    if (User::where('email', $userData['email'])->exists()) {
                        throw new \Exception('邮箱已存在');
                    }

                    // 创建用户
                    User::createUser($userData);
                    $successCount++;

                } catch (\Exception $e) {
                    $errorCount++;
                    $errors[] = "第{$lineNumber}行: " . $e->getMessage();
                }
            }

            fclose($handle);
            Db::commit();

            return [
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'errors' => $errors
            ];

        } catch (\Exception $e) {
            fclose($handle);
            Db::rollback();
            throw $e;
        }
    }
}

---
description: 
globs: 
alwaysApply: false
---
# 前端优化与CSS架构规范

## 🎨 CSS架构重构策略

### 当前问题分析
项目存在严重的CSS冗余问题：
- 多个页面重复相同的按钮、分页、表单样式
- 单个页面CSS文件过大（26-41KB）
- 样式分散，维护困难
- 加载性能差，总CSS体积达186KB

### 重构目标
- **性能提升**：CSS总体积减少71%（186KB → 53KB）
- **维护性**：统一组件样式，修改一处生效全部
- **开发效率**：新页面开发时间减少50%
- **代码质量**：减少样式冲突和覆盖问题

## 📁 CSS文件架构

### 三层架构设计
```
1. 通用组件层 - common.css (8KB)
   ├── 按钮组件 (.btn-action, .btn-edit, .btn-delete)
   ├── 分页组件 (.pagination-btn, .custom-pagination-container)
   ├── 表单组件 (.form-group, .form-control)
   ├── 卡片组件 (.card, .card-header, .card-body)
   └── 工具类 (.text-center, .mb-3, .d-flex)

2. 基础框架层 - admin.css (45KB)
   ├── 布局框架 (sidebar, header, main)
   ├── 响应式网格
   ├── 主题色彩
   └── 基础动画

3. 页面特有层 - 内联<style> (≤5KB)
   ├── 页面特有组件
   ├── 特殊布局需求
   └── 业务逻辑样式
```

### 文件引用规范
```html
<!-- 只引入基础框架，common.css已被包含 -->
<link rel="stylesheet" href="/assets/css/admin.css">

<!-- 页面特有样式内联 -->
<style>
/**
 * 三只鱼网络科技 | 韩总 | 2025-01-12
 * 页面名称 - 响应式设计
 */
/* 页面特有样式，控制在5KB以内 */
</style>
```

## 🧩 通用组件规范

### 按钮组件
参考：[public/assets/css/admin/common.css](mdc:public/assets/css/admin/common.css)

```html
<!-- 标准操作按钮 -->
<div class="action-buttons">
    <a href="#" class="btn-action btn-edit"><i class="fas fa-edit"></i></a>
    <button class="btn-action btn-delete"><i class="fas fa-trash"></i></button>
    <a href="#" class="btn-action btn-view"><i class="fas fa-eye"></i></a>
</div>

<!-- 主要操作按钮 -->
<a href="#" class="btn-add-custom">
    <i class="fas fa-plus"></i>
    添加新项目
</a>
```

### 分页组件
```html
<div class="custom-pagination-container">
    <div class="pagination-info">
        显示第 1-10 条，共 50 条记录
    </div>
    <div class="pagination-buttons">
        <a href="#" class="pagination-btn">上一页</a>
        <a href="#" class="pagination-btn active">1</a>
        <a href="#" class="pagination-btn">2</a>
        <a href="#" class="pagination-btn">下一页</a>
    </div>
</div>
```

### 列表项组件
```html
<div class="list-item">
    <div class="list-item-content">
        <div class="list-item-header">
            <h3 class="list-item-title">项目标题</h3>
            <div class="list-item-badges">
                <span class="badge badge-success">已发布</span>
            </div>
        </div>
        <div class="list-item-meta">
            <span class="meta-item">
                <i class="fas fa-calendar"></i>
                2025-01-12
            </span>
            <span class="meta-item">
                <i class="fas fa-user"></i>
                管理员
            </span>
        </div>
        <div class="list-item-summary">
            项目描述内容...
        </div>
    </div>
    <div class="list-item-actions">
        <div class="action-buttons">
            <a href="#" class="btn-action btn-edit"><i class="fas fa-edit"></i></a>
            <button class="btn-action btn-delete"><i class="fas fa-trash"></i></button>
        </div>
    </div>
</div>
```

## 📱 响应式布局规范

### Bootstrap网格系统
```html
<!-- 标准12列布局 -->
<div class="row">
    <div class="col-md-8 col-lg-9">主内容区域</div>
    <div class="col-md-4 col-lg-3">侧边栏</div>
</div>

<!-- 卡片网格布局 -->
<div class="row">
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card">卡片内容</div>
    </div>
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card">卡片内容</div>
    </div>
    <div class="col-md-12 col-lg-4 mb-4">
        <div class="card">卡片内容</div>
    </div>
</div>
```

### 响应式断点要求
- **必须包含**：`col-md-*` 和 `col-lg-*`
- **可选包含**：`col-sm-*` 和 `col-xl-*`
- **列宽验证**：每行总和必须等于12
- **间距统一**：使用Bootstrap标准间距类（mb-3, p-4等）

### Flexbox对齐规范
```css
/* 水平居中 */
.d-flex.justify-content-center

/* 垂直居中 */
.d-flex.align-items-center

/* 两端对齐 */
.d-flex.justify-content-between

/* 垂直排列 */
.d-flex.flex-column
```

## 🎯 页面特有样式规范

### 命名约定
```css
/* 页面前缀 + 组件名称 + 状态/变体 */
.case-thumbnail { }          /* 案例缩略图 */
.case-thumbnail:hover { }    /* 悬停状态 */
.case-content-wrapper { }    /* 案例内容包装器 */
.news-category-filter { }    /* 新闻分类筛选 */
.product-spec-table { }      /* 产品规格表格 */
```

### 样式组织结构
```css
/**
 * 三只鱼网络科技 | 韩总 | 2025-01-12
 * 页面名称管理 - 响应式设计
 */

/* 1. 页面特有组件 */
.page-specific-component { }

/* 2. 布局相关 */
.page-layout-wrapper { }

/* 3. 交互状态 */
.component:hover { }
.component.active { }

/* 4. 响应式适配 */
@media (max-width: 768px) {
    /* 移动端适配 */
}

@media (min-width: 992px) {
    /* 桌面端优化 */
}
```

## 🔧 开发工具集成

### 布局质量检测
```bash
# 检测页面布局质量
php tools/layout_analyzer.php check app/view/admin/页面名.html

# 目标分数
# 新页面：≥85分
# 上线标准：≥80分
```

### CSS优化工具
```bash
# CSS/JS文件优化
php tools/css_js_optimizer.php full

# 检测重复样式
php tools/css_js_optimizer.php duplicates

# 压缩CSS文件
php tools/css_js_optimizer.php minify
```

### 性能监控
```bash
# 页面性能分析
php tools/performance_analyzer.php full

# CSS加载性能
php tools/performance_analyzer.php css

# 响应式测试
php tools/performance_analyzer.php responsive
```

## 📊 重构检查清单

### 重构前检查
- [ ] 识别页面中的重复样式
- [ ] 确认可复用的组件
- [ ] 评估当前CSS文件大小
- [ ] 记录现有功能和样式

### 重构执行
- [ ] 将重复样式提取到common.css
- [ ] 页面特有样式改为内联
- [ ] 删除或简化原CSS文件
- [ ] 更新HTML使用通用组件类名

### 重构后验证
- [ ] 布局质量检测≥85分
- [ ] CSS总体积减少≥50%
- [ ] 响应式功能完整
- [ ] 所有交互功能正常
- [ ] 浏览器兼容性测试

## 🚀 性能优化目标

### 文件大小对比
| 页面类型 | 重构前 | 重构后 | 减少比例 |
|----------|--------|--------|----------|
| 案例管理 | 26KB | 5KB | 81% |
| 新闻管理 | 41KB | 5KB | 88% |
| 产品管理 | 35KB | 5KB | 86% |
| 轮播图管理 | 31KB | 5KB | 84% |

### 开发效率提升
- **新页面开发**：减少50%时间
- **样式调试**：减少60%时间
- **代码审查**：提升40%效率
- **维护成本**：降低70%

### 用户体验改善
- **首屏加载**：提升35%
- **样式一致性**：100%统一
- **响应式体验**：全面优化
- **交互流畅度**：显著提升

---

**重构原则**：渐进式优化、保持功能完整、可随时回滚


{include file="common/header"}
    <!-- 轮播图区域 - 动态渐变背景 -->
    {assign name="initialBg" value="/assets/images/tu1.png" /}
    {if condition="!empty($banners) && !empty($banners[0]['image'])"}
        {assign name="initialBg" value="$banners[0]['image']" /}
    {/if}

    <section class="hero-section bg-slide-1" id="heroSection" style="background-image: url('{:asset("assets/images/tu1.png")}'); background-size: cover; background-position: center center; background-repeat: no-repeat;">
        <!-- 动态背景层 -->
        <div class="hero-background" style="background-image: url('{:asset("assets/images/tu1.png")}'); background-size: cover; background-position: center center; background-repeat: no-repeat;"></div>

        <!-- 遮罩层 -->
        <div class="hero-overlay"></div>
        
        <!-- 装饰元素 -->
        <div class="hero-decorations">
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
            <div class="floating-icon icon-1"><i data-lucide="code"></i></div>
            <div class="floating-icon icon-2"><i data-lucide="settings"></i></div>
            <div class="floating-icon icon-3"><i data-lucide="trending-up"></i></div>
            <div class="floating-icon icon-4"><i data-lucide="globe"></i></div>
            <div class="floating-icon icon-5"><i data-lucide="smartphone"></i></div>
        </div>
        
        <div class="hero-slider">
            <div class="swiper-wrapper">
                {if condition="$useCustomBanners"}
                    <!-- 从数据库读取的轮播图 -->
                    {volist name="banners" id="banner" key="index"}
                    <div class="swiper-slide" data-bg-index="{$index}" style="background-image: url('{$banner.image}'); background-size: cover; background-position: center;">
                        <div class="container">
                            <div class="row align-items-center min-vh-100">
                                <div class="col-lg-8 col-md-10 col-sm-12 mx-auto">
                                    <div class="hero-content text-center">
                                        <h1 class="hero-title animated fadeInUp">{$banner.title}</h1>
                                        {if condition="!empty($banner.subtitle)"}
                                        <p class="hero-subtitle animated fadeInUp delay-1s">{$banner.subtitle}</p>
                                        {/if}
                                        {if condition="!empty($banner.description)"}
                                        <p class="hero-description animated fadeInUp delay-1s">{$banner.description}</p>
                                        {/if}
                                        <div class="hero-buttons animated fadeInUp delay-2s">
                                            {if condition="!empty($banner.link_url)"}
                                            <a href="{$banner.link_url}" class="btn btn-primary btn-lg" target="{$banner.link_target|default='_self'}">了解详情</a>
                                            {else /}
                                            <a href="/solutions" class="btn btn-primary btn-lg">了解方案</a>
                                            {/if}
                                            <a href="/contact" class="btn btn-outline-light btn-lg">免费咨询</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {/volist}
                {else /}
                    <!-- 默认轮播图1 -->
                    <div class="swiper-slide" data-bg-index="1">
                        <div class="container">
                            <div class="row align-items-center min-vh-100">
                                <div class="col-lg-8 col-md-10 col-sm-12 mx-auto">
                                    <div class="hero-content text-center">
                                        <h1 class="hero-title">专业企业建站解决方案</h1>
                                        <p class="hero-subtitle">助力企业数字化转型，打造专业品牌形象</p>
                                        <p class="hero-description">提供一站式企业官网建设服务，从设计到开发，从上线到运维，全程专业支持</p>
                                        <div class="hero-buttons">
                                            <a href="/solutions" class="btn btn-primary btn-lg">了解方案</a>
                                            <a href="/contact" class="btn btn-outline-light btn-lg">免费咨询</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 默认轮播图2 -->
                    <div class="swiper-slide" data-bg-index="2">
                        <div class="container">
                            <div class="row align-items-center min-vh-100">
                                <div class="col-lg-8 col-md-10 col-sm-12 mx-auto">
                                    <div class="hero-content text-center">
                                        <h1 class="hero-title animated fadeInUp">智能支付系统集成</h1>
                                        <p class="hero-subtitle animated fadeInUp delay-1s">安全便捷的支付解决方案</p>
                                        <p class="hero-description animated fadeInUp delay-1s">支持多种支付方式，提供完整的支付生态系统，让交易更简单安全</p>
                                        <div class="hero-buttons animated fadeInUp delay-2s">
                                            <a href="/solutions" class="btn btn-primary btn-lg">查看详情</a>
                                            <a href="/contact" class="btn btn-outline-light btn-lg">立即体验</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 默认轮播图3 -->
                    <div class="swiper-slide" data-bg-index="3">
                        <div class="container">
                            <div class="row align-items-center min-vh-100">
                                <div class="col-lg-8 col-md-10 col-sm-12 mx-auto">
                                    <div class="hero-content text-center">
                                        <h1 class="hero-title animated fadeInUp">云端SAAS服务平台</h1>
                                        <p class="hero-subtitle animated fadeInUp delay-1s">高效稳定的云端应用服务</p>
                                        <p class="hero-description animated fadeInUp delay-1s">基于云计算技术，提供弹性扩展的SAAS服务，助力企业快速发展</p>
                                        <div class="hero-buttons animated fadeInUp delay-2s">
                                            <a href="/solutions" class="btn btn-primary btn-lg">产品介绍</a>
                                            <a href="/contact" class="btn btn-outline-light btn-lg">申请试用</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 默认轮播图4 -->
                    <div class="swiper-slide" data-bg-index="4">
                        <div class="container">
                            <div class="row align-items-center min-vh-100">
                                <div class="col-lg-8 col-md-10 col-sm-12 mx-auto">
                                    <div class="hero-content text-center">
                                        <h1 class="hero-title animated fadeInUp">专业技术支持服务</h1>
                                        <p class="hero-subtitle animated fadeInUp delay-1s">7×24小时贴心服务保障</p>
                                        <p class="hero-description animated fadeInUp delay-1s">专业技术团队提供全方位支持，确保系统稳定运行，让您无后顾之忧</p>
                                        <div class="hero-buttons animated fadeInUp delay-2s">
                                            <a href="/about" class="btn btn-primary btn-lg">服务详情</a>
                                            <a href="/contact" class="btn btn-outline-light btn-lg">联系客服</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {/if}
            </div>
            
            <!-- 分页器 -->
            <div class="swiper-pagination"></div>
            
            <!-- 导航按钮 -->
            <div class="swiper-button-next"></div>
            <div class="swiper-button-prev"></div>
        </div>
    </section>

    <!-- 特色服务区域 - 使用ceo-home-vip-bg.png作为背景 -->
    <section class="features-section py-5" style="background-image: url('{:asset("assets/images/ceo-home-vip-bg.png")}'); background-size: cover; background-attachment: local; position: relative;">
        <!-- 背景遮罩 -->
        <div class="features-overlay"></div>
        
        <div class="container" style="position: relative; z-index: 2;">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="section-title" style="color: white;">为什么选择我们</h2>
                    <p class="section-subtitle" style="color: rgba(255,255,255,0.9);">专业、可靠、高效的企业级解决方案</p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-item text-center feature-item-overlay">
                        <div class="feature-icon">
                            <i data-lucide="rocket"></i>
                        </div>
                        <h5 style="color: white;">快速部署</h5>
                        <p style="color: rgba(255,255,255,0.8);">专业团队快速响应，高效部署实施，让您的项目快速上线</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-item text-center feature-item-overlay">
                        <div class="feature-icon">
                            <i data-lucide="shield-check"></i>
                        </div>
                        <h5 style="color: white;">安全可靠</h5>
                        <p style="color: rgba(255,255,255,0.8);">企业级安全保障，多重防护机制，确保数据安全无忧</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-item text-center feature-item-overlay">
                        <div class="feature-icon">
                            <i data-lucide="settings"></i>
                        </div>
                        <h5 style="color: white;">定制开发</h5>
                        <p style="color: rgba(255,255,255,0.8);">根据业务需求量身定制，满足个性化要求，提供最佳解决方案</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-item text-center feature-item-overlay">
                        <div class="feature-icon">
                            <i data-lucide="headphones"></i>
                        </div>
                        <h5 style="color: white;">7×24服务</h5>
                        <p style="color: rgba(255,255,255,0.8);">全天候技术支持，专业客服团队，及时响应解决问题</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-item text-center feature-item-overlay">
                        <div class="feature-icon">
                            <i data-lucide="trending-up"></i>
                        </div>
                        <h5 style="color: white;">数据分析</h5>
                        <p style="color: rgba(255,255,255,0.8);">智能数据分析，深度挖掘业务价值，助力决策优化</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-item text-center feature-item-overlay">
                        <div class="feature-icon">
                            <i data-lucide="users"></i>
                        </div>
                        <h5 style="color: white;">团队协作</h5>
                        <p style="color: rgba(255,255,255,0.8);">高效团队协作工具，提升工作效率，促进团队沟通</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 企业展示区域 -->
    <section class="company-showcase" style="background-image: url('{:asset("assets/images/shangjairuzhu.jpg")}'); background-size: cover; background-position: center; background-repeat: no-repeat; background-attachment: fixed; position: relative; image-rendering: -webkit-optimize-contrast; image-rendering: crisp-edges; image-rendering: optimizeQuality;">
        <div class="hero-overlay"></div>
        <div class="container">
            <div class="row align-items-center" style="min-height: 500px;">
                <div class="col-lg-6">
                    <div class="showcase-content" style="position: relative; z-index: 2; color: white;">
                        <h2 class="section-title" style="color: white;">企业实力展示</h2>
                        <p class="section-subtitle" style="color: rgba(255,255,255,0.9);">专业团队，卓越服务，值得信赖的合作伙伴</p>
                        <div class="showcase-features">
                            <div class="showcase-item" style="margin-bottom: 15px;">
                                <i class="fa fa-check-circle" style="color: var(--accent-color); margin-right: 10px;"></i>
                                <span>10年+行业经验</span>
                            </div>
                            <div class="showcase-item" style="margin-bottom: 15px;">
                                <i class="fa fa-check-circle" style="color: var(--accent-color); margin-right: 10px;"></i>
                                <span>500+成功案例</span>
                            </div>
                            <div class="showcase-item" style="margin-bottom: 15px;">
                                <i class="fa fa-check-circle" style="color: var(--accent-color); margin-right: 10px;"></i>
                                <span>专业技术团队</span>
                            </div>
                            <div class="showcase-item" style="margin-bottom: 15px;">
                                <i class="fa fa-check-circle" style="color: var(--accent-color); margin-right: 10px;"></i>
                                <span>7×24技术支持</span>
                            </div>
                        </div>
                        <div style="margin-top: 30px;">
                            <a href="/about" class="btn btn-primary btn-lg">了解更多</a>
                            <a href="/contact" class="btn btn-outline-primary btn-lg" style="border-color: white; color: white; margin-left: 15px;">联系我们</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="showcase-image" style="position: relative; z-index: 2; text-align: center;">
                        <img src="{:asset('assets/images/ceo-apply-bg.png')}" alt="企业展示" class="img-fluid" style="border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.3); max-width: 90%;">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 解决方案区域 -->
    {if condition="!empty($solutions)"}
    <section class="solutions-section py-5 bg-light">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="section-title">解决方案</h2>
                    <p class="section-subtitle">全行业解决方案，满足不同业务需求</p>
                </div>
            </div>

            <div class="solutions-grid">
                {volist name="solutions" id="solution" key="index"}
                    <a href="/solutions/{$solution.slug}" class="solution-item">
                        {if condition="!empty($solution['image'])"}
                            <img src="{$solution.image}" class="solution-image" alt="{$solution.name}">
                        {/if}
                        <div class="solution-content">
                            <div class="solution-icon">
                                {if condition="!empty($solution['icon'])"}
                                    <img src="{$solution.icon}" alt="{$solution.name}">
                                {else /}
                                    <i class="fa fa-cube"></i>
                                {/if}
                            </div>
                            <h5 class="card-title">{$solution.name}</h5>
                            <p class="card-text">{$solution.short_description}</p>
                            <a href="/solutions/{$solution.slug}" class="btn btn-primary">了解详情</a>
                        </div>
                    </a>
                {/volist}
            </div>

            <div class="text-center mt-4">
                <a href="/solutions" class="btn btn-outline-primary btn-lg">查看全部解决方案</a>
            </div>
        </div>
    </section>
    {/if}

    <!-- 新闻动态区域 -->
    <section class="news-section" style="background-image: url('{:asset("assets/images/ceo-apply-bg1.png")}'); position: relative;">
        <!-- 背景覆盖层 -->
        <div class="news-overlay"></div>
        <div class="container" style="position: relative; z-index: 2;">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <div class="section-header">
                        <h2 class="section-title">最新动态</h2>
                        <p class="section-subtitle">关注我们的最新资讯和行业动态</p>
                        <div class="section-divider"></div>
                    </div>
                </div>
            </div>

            <div class="row">
                {if condition="!empty($latestNews)"}
                    {volist name="latestNews" id="news" key="index"}
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="news-card">
                                <div class="news-image">
                                    {if condition="!empty($news.image)"}
                                        <img src="{$news.image}" alt="{$news.title}" class="img-fluid">
                                    {else /}
                                        {php}
                                            $newsImages = ['news1.png', 'news2.png', 'news3.png'];
                                            $imageIndex = ($index - 1) % 3;
                                        {/php}
                                        <img src="{:asset('assets/images/' . $newsImages[$imageIndex])}" alt="{$news.title}" class="img-fluid">
                                    {/if}
                                    <div class="news-overlay">
                                        <div class="news-date">
                                            <span class="day">{$news.published_at|default=$news.created_at|date='d'}</span>
                                            <span class="month">{$news.published_at|default=$news.created_at|date='M'}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="news-content">
                                    <div class="news-meta">
                                        <span class="news-category">
                                            <i class="fa fa-tag"></i>
                                            {$news.category.name|default='公司动态'}
                                        </span>
                                        <span class="news-time">
                                            <i class="fa fa-clock-o"></i>
                                            {$news.published_at|default=$news.created_at|date='Y-m-d'}
                                        </span>
                                    </div>
                                    <h5 class="news-title">
                                        <a href="{$news.detail_url|default='/news/' . $news.slug}">{$news.title}</a>
                                    </h5>
                                    <p class="news-excerpt">{$news.summary|default=$news.content|strip_tags|mb_substr=0,100,'UTF-8'}...</p>
                                    <div class="news-footer">
                                        <a href="{$news.detail_url|default='/news/' . $news.slug}" class="read-more-btn">
                                            阅读更多 <i class="fa fa-arrow-right"></i>
                                        </a>
                                        <div class="news-stats">
                                            <span><i class="fa fa-eye"></i> {$news.views|default=0}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {/volist}
                {else /}
                    <!-- 默认新闻展示 -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="news-card">
                            <div class="news-image">
                                <img src="{:asset('assets/images/news1.png')}" alt="企业数字化转型" class="img-fluid">
                                <div class="news-overlay">
                                    <div class="news-date">
                                        <span class="day">{:date('d')}</span>
                                        <span class="month">{:date('M')}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="news-content">
                                <div class="news-meta">
                                    <span class="news-category"><i class="fa fa-tag"></i> 行业资讯</span>
                                    <span class="news-time"><i class="fa fa-clock-o"></i> {:date('Y-m-d')}</span>
                                </div>
                                <h5 class="news-title">
                                    <a href="/news">企业数字化转型的关键要素</a>
                                </h5>
                                <p class="news-excerpt">随着数字化时代的到来，企业数字化转型已成为提升竞争力的重要途径。本文将深入探讨数字化转型的核心要素...</p>
                                <div class="news-footer">
                                    <a href="/news" class="read-more-btn">
                                        阅读更多 <i class="fa fa-arrow-right"></i>
                                    </a>
                                    <div class="news-stats">
                                        <span><i class="fa fa-eye"></i> 1,234</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {/if}
            </div>

            <div class="text-center mt-5">
                <a href="/news" class="btn btn-primary btn-lg news-more-btn">
                    <i class="fa fa-newspaper-o"></i> 查看更多动态
                </a>
            </div>
        </div>
    </section>

    <!-- 数据统计区域 - 重新设计 -->
    <section class="stats-section">
        <div class="stats-background">
            <img src="{:asset('assets/images/index_section6_bg.png')}" alt="统计背景" class="stats-bg-image">
            <div class="stats-overlay"></div>
        </div>

        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <div class="stats-header">
                        <h2 class="stats-title">实力见证</h2>
                        <p class="stats-subtitle">用数据说话，用实力证明</p>
                        <div class="stats-divider"></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <img src="{:asset('assets/images/footer-top-01.png')}" alt="成功案例" class="stat-icon-img">
                        </div>
                        <div class="stat-content">
                            <h3 class="counter" data-count="500">100</h3>
                            <span class="stat-plus">+</span>
                            <p class="stat-label">成功案例</p>
                            <div class="stat-description">覆盖多个行业领域</div>
                        </div>
                        <div class="stat-decoration"></div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <img src="{:asset('assets/images/footer-top-02.png')}" alt="合作客户" class="stat-icon-img">
                        </div>
                        <div class="stat-content">
                            <h3 class="counter" data-count="1000">30</h3>
                            <span class="stat-plus">+</span>
                            <p class="stat-label">合作客户</p>
                            <div class="stat-description">遍布全国各地</div>
                        </div>
                        <div class="stat-decoration"></div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <img src="{:asset('assets/images/footer-top-03.png')}" alt="专业团队" class="stat-icon-img">
                        </div>
                        <div class="stat-content">
                            <h3 class="counter" data-count="50">10</h3>
                            <span class="stat-plus">+</span>
                            <p class="stat-label">专业团队</p>
                            <div class="stat-description">资深技术专家</div>
                        </div>
                        <div class="stat-decoration"></div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <img src="{:asset('assets/images/footer-top-04.png')}" alt="服务年限" class="stat-icon-img">
                        </div>
                        <div class="stat-content">
                            <h3 class="counter" data-count="10">22</h3>
                            <span class="stat-plus">+</span>
                            <p class="stat-label">服务年限</p>
                            <div class="stat-description">行业经验丰富</div>
                        </div>
                        <div class="stat-decoration"></div>
                    </div>
                </div>
            </div>

            <!-- 添加信任标识 -->
            <div class="row mt-5">
                <div class="col-12">
                    <div class="trust-indicators">
                        <div class="trust-item">
                            <i class="fa fa-shield"></i>
                            <span>安全可靠</span>
                        </div>
                        <div class="trust-item">
                            <i class="fa fa-clock-o"></i>
                            <span>7×24服务</span>
                        </div>
                        <div class="trust-item">
                            <i class="fa fa-certificate"></i>
                            <span>权威认证</span>
                        </div>
                        <div class="trust-item">
                            <i class="fa fa-thumbs-up"></i>
                            <span>客户满意</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



<script>
// 首页专用JavaScript - 只处理Lucide图标初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化Lucide图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});
</script>

{include file="common/footer"}

{assign name="pageTitle" value="$pageData.title" /}
{assign name="pageDescription" value="$pageData.description" /}
{assign name="pageKeywords" value="$pageData.keywords" /}
{include file="common/header-bg"}
<!-- 新闻专用CSS -->
<link rel="stylesheet" href="{:asset('assets/css/news.css')}?v={:time()}">

<!-- 整个新闻页面背景区域 -->
<section class="news-page-wrapper">
    <div class="news-background">
        <!-- 页面标题区域 -->
        <div class="page-header">
            <div class="container">
                <div class="page-title-content">
                    <h1 class="page-title">{$categoryInfo.name}</h1>
                    <p class="page-subtitle">{$categoryInfo.description|default='浏览该分类下的相关资讯'}</p>
                    <nav class="page-breadcrumb">
                        <a href="/" class="breadcrumb-link">
                            <i class="fas fa-home"></i>
                            首页
                        </a>
                        <span class="breadcrumb-separator">></span>
                        <a href="/news" class="breadcrumb-link">新闻资讯</a>
                        <span class="breadcrumb-separator">></span>
                        <span class="breadcrumb-current">{$categoryInfo.name}</span>
                    </nav>
                </div>
            </div>
        </div>

        <!-- 新闻内容区域 -->
        <div class="news-content">
            <div class="container">
                <div class="row">
                    <!-- 左侧主要内容 -->
                    <div class="col-lg-8 col-md-7">
                        <!-- 新闻列表 -->
                        <div class="news-list-section" id="news-container">
                            <div class="news-grid" id="news-grid">
                                {volist name="newsList" id="news"}
                                <article class="news-item" data-aos="fade-up" data-aos-delay="{$key * 100}">
                                    <div class="news-card">
                                        <div class="news-image">
                                            <a href="{$news.detail_url|default='/news/'.$news.slug}">
                                                <img src="{$news.image|default='/assets/images/news/default.jpg'}" alt="{$news.title}" loading="lazy">
                                                <div class="image-overlay">
                                                    <div class="overlay-content">
                                                        <i class="fas fa-eye"></i>
                                                        <span>阅读详情</span>
                                                    </div>
                                                </div>
                                            </a>
                                            {if condition="$news.is_featured"}
                                            <div class="featured-badge">
                                                <i class="fas fa-star"></i>
                                                <span>精选</span>
                                            </div>
                                            {/if}
                                        </div>
                                        <div class="news-content">
                                            <div class="news-meta">
                                                <div class="meta-item category">
                                                    <i class="fas fa-tag"></i>
                                                    <span>{$categoryInfo.name}</span>
                                                </div>
                                                <div class="meta-item date">
                                                    <i class="fas fa-clock"></i>
                                                    <time datetime="{$news.published_at|default=$news.created_at}">{$news.published_at|default=$news.created_at|date='Y-m-d'}</time>
                                                </div>
                                                <div class="meta-item views">
                                                    <i class="fas fa-eye"></i>
                                                    <span>{$news.views|default=0}</span>
                                                </div>
                                            </div>
                                            <h3 class="news-title">
                                                <a href="{$news.detail_url|default='/news/'.$news.slug}">{$news.title}</a>
                                            </h3>
                                            <p class="news-excerpt">{$news.summary|default=$news.content|strip_tags|mb_substr=0,120,'UTF-8'}...</p>
                                            <div class="news-actions">
                                                <a href="{$news.detail_url|default='/news/'.$news.slug}" class="read-more-btn1">
                                                    <span>阅读全文</span>
                                                    <i class="fas fa-arrow-right"></i>
                                                </a>
                                                {if condition="$news.author"}
                                                <div class="news-author">
                                                    <i class="fas fa-user"></i>
                                                    <span>{$news.author}</span>
                                                </div>
                                                {/if}
                                            </div>
                                        </div>
                                    </div>
                                </article>
                                {/volist}
                            </div>

                            <!-- 分页导航 -->
                            {if condition="$pagination"}
                            <div class="pagination-wrapper">
                                <div class="pagination-container">
                                    {$pagination|raw}
                                </div>
                            </div>
                            {/if}

                            <!-- 无新闻时的提示 -->
                            {empty name="newsList"}
                            <div class="no-news-found">
                                <div class="no-news-content">
                                    <div class="no-news-icon">
                                        <i class="fas fa-newspaper"></i>
                                    </div>
                                    <h4>暂无相关新闻</h4>
                                    <p>该分类下暂时没有新闻内容，请关注其他分类或稍后再来查看。</p>
                                    <a href="/news" class="btn-back-all">
                                        <i class="fas fa-arrow-left"></i>
                                        返回全部新闻
                                    </a>
                                </div>
                            </div>
                            {/empty}
                        </div>
                    </div>

                    <!-- 右侧边栏 -->
                    <div class="col-lg-4 col-md-5">
                        <div class="news-sidebar">
                            <!-- 新闻分类统计 -->
                            <div class="sidebar-widget categories-widget">
                                <div class="widget-header">
                                    <h4 class="widget-title">
                                        <i class="fas fa-chart-bar"></i>
                                        新闻分类
                                    </h4>
                                    <div class="widget-accent"></div>
                                </div>
                                <div class="widget-content">
                                    <div class="categories-stats">
                                        <div class="category-stat-item">
                                            <a href="/news" class="category-stat-link">
                                                <div class="category-info">
                                                    <span class="category-name">全部新闻</span>
                                                </div>
                                            </a>
                                        </div>
                                        {volist name="categories" id="category"}
                                        <div class="category-stat-item">
                                            <a href="/news/category/{$category.slug}" class="category-stat-link {if $category.slug == $categoryInfo.slug}active{/if}">
                                                <div class="category-info">
                                                    <span class="category-name">{$category.name}</span>
                                                    <span class="category-count">{$category.news_count|default=0}</span>
                                                </div>
                                                <div class="category-progress">
                                                    {php}$width = min(($category['news_count'] ?? 0) * 10, 100);{/php}
                                                    <div class="progress-bar" style="width: {$width}%"></div>
                                                </div>
                                            </a>
                                        </div>
                                        {/volist}
                                    </div>
                                </div>
                            </div>

                            <!-- 热门新闻 -->
                            <div class="sidebar-widget hot-news-widget">
                                <div class="widget-header">
                                    <h4 class="widget-title">
                                        <i class="fas fa-fire"></i>
                                        热门新闻
                                    </h4>
                                    <div class="widget-accent"></div>
                                </div>
                                <div class="widget-content">
                                    <div class="hot-news-list">
                                        {volist name="hotNews" id="hot" key="index"}
                                        <div class="hot-news-item">
                                            <div class="hot-news-rank">{$index}</div>
                                            <div class="hot-news-content">
                                                <h5><a href="{$hot.detail_url|default='/news/'.$hot.slug}">{$hot.title}</a></h5>
                                                <div class="hot-news-meta">
                                                    <span class="views"><i class="fas fa-eye"></i> {$hot.views|default=0}</span>
                                                    <span class="date">{$hot.published_at|default=$hot.created_at|date='m-d'}</span>
                                                </div>
                                            </div>
                                        </div>
                                        {/volist}

                                        {empty name="hotNews"}
                                        <div class="empty-state">
                                            <p>暂无热门新闻</p>
                                        </div>
                                        {/empty}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
    
<!-- JavaScript -->
<script src="{:asset('assets/js/jquery.min.js')}"></script>
<script src="{:asset('assets/js/bootstrap.bundle.min.js')}"></script>

{include file="common/footer"}

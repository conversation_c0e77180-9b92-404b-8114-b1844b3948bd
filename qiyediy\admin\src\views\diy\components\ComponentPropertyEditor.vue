<!--
  三只鱼网络科技 | 韩总 | 2024-12-20
  QiyeDIY企业建站系统 - 组件属性编辑器
-->

<template>
  <div class="component-property-editor">
    <div class="property-header">
      <h4 class="property-title">{{ getComponentTypeName(component.type) }}属性</h4>
      <el-button size="small" text @click="handleReset">重置</el-button>
    </div>

    <div class="property-content">
      <!-- 文本组件属性 -->
      <div v-if="component.type === 'text'" class="property-group">
        <el-form :model="textProps" label-width="80px" size="small">
          <el-form-item label="文本内容">
            <el-input
              v-model="textProps.text"
              type="textarea"
              :rows="3"
              placeholder="请输入文本内容"
              @input="handleTextChange"
            />
          </el-form-item>
          
          <el-form-item label="字体大小">
            <el-input-number
              v-model="textProps.fontSize"
              :min="12"
              :max="72"
              controls-position="right"
              @change="handleStyleChange"
            />
            <span class="unit">px</span>
          </el-form-item>
          
          <el-form-item label="字体颜色">
            <el-color-picker
              v-model="textProps.color"
              @change="handleStyleChange"
            />
          </el-form-item>
          
          <el-form-item label="对齐方式">
            <el-radio-group v-model="textProps.textAlign" @change="handleStyleChange">
              <el-radio-button label="left">左对齐</el-radio-button>
              <el-radio-button label="center">居中</el-radio-button>
              <el-radio-button label="right">右对齐</el-radio-button>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="字体粗细">
            <el-select v-model="textProps.fontWeight" @change="handleStyleChange">
              <el-option label="正常" value="normal" />
              <el-option label="粗体" value="bold" />
              <el-option label="100" value="100" />
              <el-option label="300" value="300" />
              <el-option label="500" value="500" />
              <el-option label="700" value="700" />
              <el-option label="900" value="900" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="行高">
            <el-input-number
              v-model="textProps.lineHeight"
              :min="1"
              :max="3"
              :step="0.1"
              controls-position="right"
              @change="handleStyleChange"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 图片组件属性 -->
      <div v-else-if="component.type === 'image'" class="property-group">
        <el-form :model="imageProps" label-width="80px" size="small">
          <el-form-item label="图片地址">
            <div class="image-upload">
              <el-upload
                class="image-uploader"
                :action="uploadAction"
                :headers="uploadHeaders"
                :show-file-list="false"
                :before-upload="beforeImageUpload"
                :on-success="handleImageSuccess"
              >
                <img v-if="imageProps.src" :src="imageProps.src" class="uploaded-image" />
                <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
              </el-upload>
              <el-input
                v-model="imageProps.src"
                placeholder="或输入图片URL"
                @input="handleImageChange"
              />
            </div>
          </el-form-item>
          
          <el-form-item label="替代文本">
            <el-input
              v-model="imageProps.alt"
              placeholder="图片描述"
              @input="handleImageChange"
            />
          </el-form-item>
          
          <el-form-item label="宽度">
            <el-input
              v-model="imageProps.width"
              placeholder="auto"
              @input="handleStyleChange"
            />
          </el-form-item>
          
          <el-form-item label="高度">
            <el-input
              v-model="imageProps.height"
              placeholder="auto"
              @input="handleStyleChange"
            />
          </el-form-item>
          
          <el-form-item label="适应方式">
            <el-select v-model="imageProps.objectFit" @change="handleStyleChange">
              <el-option label="填充" value="fill" />
              <el-option label="包含" value="contain" />
              <el-option label="覆盖" value="cover" />
              <el-option label="缩放" value="scale-down" />
              <el-option label="无" value="none" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="圆角">
            <el-input-number
              v-model="imageProps.borderRadius"
              :min="0"
              :max="50"
              controls-position="right"
              @change="handleStyleChange"
            />
            <span class="unit">px</span>
          </el-form-item>
        </el-form>
      </div>

      <!-- 按钮组件属性 -->
      <div v-else-if="component.type === 'button'" class="property-group">
        <el-form :model="buttonProps" label-width="80px" size="small">
          <el-form-item label="按钮文本">
            <el-input
              v-model="buttonProps.text"
              placeholder="按钮文本"
              @input="handleButtonChange"
            />
          </el-form-item>
          
          <el-form-item label="链接地址">
            <el-input
              v-model="buttonProps.link"
              placeholder="http://example.com"
              @input="handleButtonChange"
            />
          </el-form-item>
          
          <el-form-item label="按钮类型">
            <el-select v-model="buttonProps.type" @change="handleStyleChange">
              <el-option label="主要" value="primary" />
              <el-option label="成功" value="success" />
              <el-option label="信息" value="info" />
              <el-option label="警告" value="warning" />
              <el-option label="危险" value="danger" />
              <el-option label="默认" value="default" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="按钮大小">
            <el-select v-model="buttonProps.size" @change="handleStyleChange">
              <el-option label="大" value="large" />
              <el-option label="默认" value="default" />
              <el-option label="小" value="small" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="按钮样式">
            <el-checkbox v-model="buttonProps.plain" @change="handleStyleChange">朴素按钮</el-checkbox>
            <el-checkbox v-model="buttonProps.round" @change="handleStyleChange">圆角按钮</el-checkbox>
            <el-checkbox v-model="buttonProps.circle" @change="handleStyleChange">圆形按钮</el-checkbox>
          </el-form-item>
          
          <el-form-item label="自定义宽度">
            <el-input
              v-model="buttonProps.width"
              placeholder="auto"
              @input="handleStyleChange"
            />
          </el-form-item>
          
          <el-form-item label="背景颜色">
            <el-color-picker
              v-model="buttonProps.backgroundColor"
              @change="handleStyleChange"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 容器组件属性 -->
      <div v-else-if="component.type === 'container'" class="property-group">
        <el-form :model="containerProps" label-width="80px" size="small">
          <el-form-item label="布局方式">
            <el-select v-model="containerProps.display" @change="handleStyleChange">
              <el-option label="块级" value="block" />
              <el-option label="弹性布局" value="flex" />
              <el-option label="网格布局" value="grid" />
              <el-option label="行内块" value="inline-block" />
            </el-select>
          </el-form-item>
          
          <el-form-item v-if="containerProps.display === 'flex'" label="主轴方向">
            <el-select v-model="containerProps.flexDirection" @change="handleStyleChange">
              <el-option label="水平" value="row" />
              <el-option label="垂直" value="column" />
              <el-option label="水平反向" value="row-reverse" />
              <el-option label="垂直反向" value="column-reverse" />
            </el-select>
          </el-form-item>
          
          <el-form-item v-if="containerProps.display === 'flex'" label="主轴对齐">
            <el-select v-model="containerProps.justifyContent" @change="handleStyleChange">
              <el-option label="起始" value="flex-start" />
              <el-option label="居中" value="center" />
              <el-option label="末尾" value="flex-end" />
              <el-option label="两端对齐" value="space-between" />
              <el-option label="环绕对齐" value="space-around" />
              <el-option label="均匀分布" value="space-evenly" />
            </el-select>
          </el-form-item>
          
          <el-form-item v-if="containerProps.display === 'flex'" label="交叉轴对齐">
            <el-select v-model="containerProps.alignItems" @change="handleStyleChange">
              <el-option label="起始" value="flex-start" />
              <el-option label="居中" value="center" />
              <el-option label="末尾" value="flex-end" />
              <el-option label="拉伸" value="stretch" />
              <el-option label="基线" value="baseline" />
            </el-select>
          </el-form-item>
          
          <el-form-item v-if="containerProps.display === 'flex'" label="间距">
            <el-input-number
              v-model="containerProps.gap"
              :min="0"
              :max="100"
              controls-position="right"
              @change="handleStyleChange"
            />
            <span class="unit">px</span>
          </el-form-item>
          
          <el-form-item label="最小高度">
            <el-input-number
              v-model="containerProps.minHeight"
              :min="0"
              :max="1000"
              controls-position="right"
              @change="handleStyleChange"
            />
            <span class="unit">px</span>
          </el-form-item>
          
          <el-form-item label="背景颜色">
            <el-color-picker
              v-model="containerProps.background"
              @change="handleStyleChange"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 通用样式属性 -->
      <el-divider>通用样式</el-divider>
      
      <el-form :model="commonProps" label-width="80px" size="small">
        <el-form-item label="外边距">
          <div class="spacing-input">
            <el-input-number
              v-model="commonProps.marginTop"
              placeholder="上"
              size="small"
              @change="handleCommonStyleChange"
            />
            <el-input-number
              v-model="commonProps.marginRight"
              placeholder="右"
              size="small"
              @change="handleCommonStyleChange"
            />
            <el-input-number
              v-model="commonProps.marginBottom"
              placeholder="下"
              size="small"
              @change="handleCommonStyleChange"
            />
            <el-input-number
              v-model="commonProps.marginLeft"
              placeholder="左"
              size="small"
              @change="handleCommonStyleChange"
            />
          </div>
        </el-form-item>
        
        <el-form-item label="内边距">
          <div class="spacing-input">
            <el-input-number
              v-model="commonProps.paddingTop"
              placeholder="上"
              size="small"
              @change="handleCommonStyleChange"
            />
            <el-input-number
              v-model="commonProps.paddingRight"
              placeholder="右"
              size="small"
              @change="handleCommonStyleChange"
            />
            <el-input-number
              v-model="commonProps.paddingBottom"
              placeholder="下"
              size="small"
              @change="handleCommonStyleChange"
            />
            <el-input-number
              v-model="commonProps.paddingLeft"
              placeholder="左"
              size="small"
              @change="handleCommonStyleChange"
            />
          </div>
        </el-form-item>
        
        <el-form-item label="边框">
          <div class="border-input">
            <el-input-number
              v-model="commonProps.borderWidth"
              placeholder="宽度"
              size="small"
              :min="0"
              @change="handleCommonStyleChange"
            />
            <el-select
              v-model="commonProps.borderStyle"
              placeholder="样式"
              size="small"
              @change="handleCommonStyleChange"
            >
              <el-option label="实线" value="solid" />
              <el-option label="虚线" value="dashed" />
              <el-option label="点线" value="dotted" />
              <el-option label="双线" value="double" />
            </el-select>
            <el-color-picker
              v-model="commonProps.borderColor"
              size="small"
              @change="handleCommonStyleChange"
            />
          </div>
        </el-form-item>
        
        <el-form-item label="圆角">
          <el-input-number
            v-model="commonProps.borderRadius"
            :min="0"
            :max="50"
            controls-position="right"
            @change="handleCommonStyleChange"
          />
          <span class="unit">px</span>
        </el-form-item>
        
        <el-form-item label="阴影">
          <el-input
            v-model="commonProps.boxShadow"
            placeholder="0 2px 4px rgba(0,0,0,0.1)"
            @input="handleCommonStyleChange"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useUserStore } from '@/store/modules/user'
import type { DiyComponent } from '@/api/diy'
import { Plus } from '@element-plus/icons-vue'

interface Props {
  component: DiyComponent
}

interface Emits {
  (e: 'update', component: DiyComponent): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const userStore = useUserStore()

// 组件属性数据
const textProps = reactive({
  text: '',
  fontSize: 16,
  color: '#333333',
  textAlign: 'left',
  fontWeight: 'normal',
  lineHeight: 1.5
})

const imageProps = reactive({
  src: '',
  alt: '',
  width: '100%',
  height: 'auto',
  objectFit: 'cover',
  borderRadius: 0
})

const buttonProps = reactive({
  text: '',
  link: '',
  type: 'primary',
  size: 'default',
  plain: false,
  round: false,
  circle: false,
  width: 'auto',
  backgroundColor: ''
})

const containerProps = reactive({
  display: 'block',
  flexDirection: 'row',
  justifyContent: 'flex-start',
  alignItems: 'stretch',
  gap: 0,
  minHeight: 100,
  background: '#ffffff'
})

const commonProps = reactive({
  marginTop: 0,
  marginRight: 0,
  marginBottom: 0,
  marginLeft: 0,
  paddingTop: 0,
  paddingRight: 0,
  paddingBottom: 0,
  paddingLeft: 0,
  borderWidth: 0,
  borderStyle: 'solid',
  borderColor: '#e5e5e5',
  borderRadius: 0,
  boxShadow: ''
})

// 计算属性
const uploadAction = computed(() => {
  return import.meta.env.VITE_API_BASE_URL + '/upload/image'
})

const uploadHeaders = computed(() => {
  return {
    Authorization: `Bearer ${userStore.token}`
  }
})

/**
 * 获取组件类型名称
 */
const getComponentTypeName = (type: string): string => {
  const typeNames = {
    text: '文本',
    image: '图片',
    button: '按钮',
    container: '容器',
    divider: '分割线',
    video: '视频',
    form: '表单'
  }
  return typeNames[type] || type
}

/**
 * 初始化属性数据
 */
const initProps = () => {
  const { config = {}, content = {} } = props.component
  
  // 初始化文本属性
  if (props.component.type === 'text') {
    Object.assign(textProps, {
      text: content.text || '',
      fontSize: config.fontSize || 16,
      color: config.color || '#333333',
      textAlign: config.textAlign || 'left',
      fontWeight: config.fontWeight || 'normal',
      lineHeight: config.lineHeight || 1.5
    })
  }
  
  // 初始化图片属性
  if (props.component.type === 'image') {
    Object.assign(imageProps, {
      src: content.src || '',
      alt: content.alt || '',
      width: config.width || '100%',
      height: config.height || 'auto',
      objectFit: config.objectFit || 'cover',
      borderRadius: config.borderRadius || 0
    })
  }
  
  // 初始化按钮属性
  if (props.component.type === 'button') {
    Object.assign(buttonProps, {
      text: content.text || '',
      link: content.link || '',
      type: config.type || 'primary',
      size: config.size || 'default',
      plain: config.plain || false,
      round: config.round || false,
      circle: config.circle || false,
      width: config.width || 'auto',
      backgroundColor: config.backgroundColor || ''
    })
  }
  
  // 初始化容器属性
  if (props.component.type === 'container') {
    Object.assign(containerProps, {
      display: config.display || 'block',
      flexDirection: config.flexDirection || 'row',
      justifyContent: config.justifyContent || 'flex-start',
      alignItems: config.alignItems || 'stretch',
      gap: config.gap || 0,
      minHeight: config.minHeight || 100,
      background: config.background || '#ffffff'
    })
  }
  
  // 初始化通用属性
  Object.assign(commonProps, {
    marginTop: config.marginTop || 0,
    marginRight: config.marginRight || 0,
    marginBottom: config.marginBottom || 0,
    marginLeft: config.marginLeft || 0,
    paddingTop: config.paddingTop || 0,
    paddingRight: config.paddingRight || 0,
    paddingBottom: config.paddingBottom || 0,
    paddingLeft: config.paddingLeft || 0,
    borderWidth: config.borderWidth || 0,
    borderStyle: config.borderStyle || 'solid',
    borderColor: config.borderColor || '#e5e5e5',
    borderRadius: config.borderRadius || 0,
    boxShadow: config.boxShadow || ''
  })
}

/**
 * 处理文本内容变化
 */
const handleTextChange = () => {
  const updatedComponent = {
    ...props.component,
    content: {
      ...props.component.content,
      text: textProps.text
    }
  }
  emit('update', updatedComponent)
}

/**
 * 处理图片变化
 */
const handleImageChange = () => {
  const updatedComponent = {
    ...props.component,
    content: {
      ...props.component.content,
      src: imageProps.src,
      alt: imageProps.alt
    }
  }
  emit('update', updatedComponent)
}

/**
 * 处理按钮变化
 */
const handleButtonChange = () => {
  const updatedComponent = {
    ...props.component,
    content: {
      ...props.component.content,
      text: buttonProps.text,
      link: buttonProps.link
    }
  }
  emit('update', updatedComponent)
}

/**
 * 处理样式变化
 */
const handleStyleChange = () => {
  let newConfig = { ...props.component.config }
  
  if (props.component.type === 'text') {
    newConfig = {
      ...newConfig,
      fontSize: textProps.fontSize,
      color: textProps.color,
      textAlign: textProps.textAlign,
      fontWeight: textProps.fontWeight,
      lineHeight: textProps.lineHeight
    }
  } else if (props.component.type === 'image') {
    newConfig = {
      ...newConfig,
      width: imageProps.width,
      height: imageProps.height,
      objectFit: imageProps.objectFit,
      borderRadius: imageProps.borderRadius
    }
  } else if (props.component.type === 'button') {
    newConfig = {
      ...newConfig,
      type: buttonProps.type,
      size: buttonProps.size,
      plain: buttonProps.plain,
      round: buttonProps.round,
      circle: buttonProps.circle,
      width: buttonProps.width,
      backgroundColor: buttonProps.backgroundColor
    }
  } else if (props.component.type === 'container') {
    newConfig = {
      ...newConfig,
      display: containerProps.display,
      flexDirection: containerProps.flexDirection,
      justifyContent: containerProps.justifyContent,
      alignItems: containerProps.alignItems,
      gap: containerProps.gap,
      minHeight: containerProps.minHeight,
      background: containerProps.background
    }
  }
  
  const updatedComponent = {
    ...props.component,
    config: newConfig
  }
  emit('update', updatedComponent)
}

/**
 * 处理通用样式变化
 */
const handleCommonStyleChange = () => {
  const newConfig = {
    ...props.component.config,
    marginTop: commonProps.marginTop,
    marginRight: commonProps.marginRight,
    marginBottom: commonProps.marginBottom,
    marginLeft: commonProps.marginLeft,
    paddingTop: commonProps.paddingTop,
    paddingRight: commonProps.paddingRight,
    paddingBottom: commonProps.paddingBottom,
    paddingLeft: commonProps.paddingLeft,
    borderWidth: commonProps.borderWidth,
    borderStyle: commonProps.borderStyle,
    borderColor: commonProps.borderColor,
    borderRadius: commonProps.borderRadius,
    boxShadow: commonProps.boxShadow
  }
  
  const updatedComponent = {
    ...props.component,
    config: newConfig
  }
  emit('update', updatedComponent)
}

/**
 * 图片上传前检查
 */
const beforeImageUpload = (rawFile: File) => {
  const isImage = rawFile.type.startsWith('image/')
  const isLt5M = rawFile.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

/**
 * 图片上传成功
 */
const handleImageSuccess = (response: any) => {
  if (response.code === 200) {
    imageProps.src = response.data.url
    handleImageChange()
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error(response.message || '图片上传失败')
  }
}

/**
 * 重置属性
 */
const handleReset = () => {
  initProps()
  handleStyleChange()
  handleCommonStyleChange()
}

// 监听组件变化
watch(() => props.component, () => {
  initProps()
}, { immediate: true, deep: true })
</script>

<style lang="scss" scoped>
.component-property-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.property-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color-light);
  
  .property-title {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.property-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.property-group {
  margin-bottom: 24px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.unit {
  margin-left: 8px;
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

.image-upload {
  .image-uploader {
    margin-bottom: 8px;
    
    :deep(.el-upload) {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
      
      &:hover {
        border-color: var(--el-color-primary);
      }
    }
  }
  
  .image-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 80px;
    height: 80px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .uploaded-image {
    width: 80px;
    height: 80px;
    display: block;
    object-fit: cover;
  }
}

.spacing-input {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 4px;
  
  :deep(.el-input-number) {
    width: 100%;
    
    .el-input__inner {
      text-align: center;
    }
  }
}

.border-input {
  display: flex;
  gap: 8px;
  align-items: center;
  
  :deep(.el-input-number) {
    flex: 1;
  }
  
  :deep(.el-select) {
    flex: 1;
  }
  
  :deep(.el-color-picker) {
    flex-shrink: 0;
  }
}

:deep(.el-form-item) {
  margin-bottom: 16px;
  
  .el-form-item__label {
    font-size: 12px;
    color: var(--el-text-color-regular);
  }
}

:deep(.el-divider) {
  margin: 20px 0;
  
  .el-divider__text {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
}

:deep(.el-radio-group) {
  .el-radio-button {
    .el-radio-button__inner {
      padding: 4px 8px;
      font-size: 12px;
    }
  }
}

:deep(.el-checkbox) {
  margin-right: 12px;
  margin-bottom: 8px;
  
  .el-checkbox__label {
    font-size: 12px;
  }
}
</style>

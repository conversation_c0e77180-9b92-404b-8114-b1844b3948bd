<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面预览 - {$template.name}</title>
    
    <!-- 基础CSS -->
    <link rel="stylesheet" href="/assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="/assets/css/all.min.css">

    <!-- 组件样式CSS -->
    <link rel="stylesheet" href="/diy/css/all.css">

    <!-- 数据库中保存的CSS样式 -->
    <style id="template-styles">
        {$template.css_content|raw}
    </style>

    <style>
        /* 预览页面专用样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: #ffffff;
            line-height: 1.6;
        }
        
        /* 预览工具栏 */
        .preview-toolbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: rgba(45, 55, 72, 0.95);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            z-index: 9999;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .preview-toolbar-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .preview-title {
            font-size: 16px;
            font-weight: 600;
            color: white;
        }
        
        .preview-subtitle {
            font-size: 12px;
            color: #a0aec0;
        }
        
        .preview-toolbar-right {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .preview-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            transition: all 0.2s ease;
        }
        
        .preview-btn.secondary {
            background: #4a5568;
            color: white;
        }
        
        .preview-btn.secondary:hover {
            background: #2d3748;
            color: white;
        }
        
        .preview-btn.primary {
            background: #667eea;
            color: white;
        }
        
        .preview-btn.primary:hover {
            background: #5a67d8;
            color: white;
        }
        
        /* 预览内容区域 */
        .preview-content {
            margin-top: 50px;
            min-height: calc(100vh - 50px);
            background: #ffffff;
        }
        
        /* 响应式预览模式 */
        .preview-mode {
            transition: all 0.3s ease;
        }
        
        .preview-mode.mobile {
            max-width: 375px;
            margin: 20px auto;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .preview-mode.tablet {
            max-width: 768px;
            margin: 20px auto;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .preview-mode.desktop {
            width: 100%;
            margin: 0;
        }
        
        /* 设备模式指示器 */
        .device-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(45, 55, 72, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            z-index: 9998;
            backdrop-filter: blur(10px);
        }
        
        /* 加载状态 */
        .preview-loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 9997;
        }
        
        .preview-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 隐藏组件控制按钮 */
        .component-controls {
            display: none !important;
        }
        
        /* 移除组件边框 */
        .component-block {
            border: none !important;
            box-shadow: none !important;
        }
        
        .component-block:hover {
            border: none !important;
            box-shadow: none !important;
        }

        /* 确保组件样式正确加载 */
        .navbar-menu-item:hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
            color: white !important;
        }

        /* 重置可能的样式冲突 */
        * {
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <!-- 预览工具栏 -->
    <div class="preview-toolbar">
        <div class="preview-toolbar-left">
            <div>
                <div class="preview-title">
                    <i class="fas fa-eye"></i> 页面预览
                </div>
                <div class="preview-subtitle">模板: {$template.name}</div>
            </div>
        </div>
        <div class="preview-toolbar-right">
            <button class="preview-btn secondary" onclick="switchDevice('mobile')">
                <i class="fas fa-mobile-alt"></i>
                手机
            </button>
            <button class="preview-btn secondary" onclick="switchDevice('tablet')">
                <i class="fas fa-tablet-alt"></i>
                平板
            </button>
            <button class="preview-btn secondary" onclick="switchDevice('desktop')">
                <i class="fas fa-desktop"></i>
                桌面
            </button>
            <button class="preview-btn primary" onclick="refreshPreview()">
                <i class="fas fa-sync-alt"></i>
                刷新
            </button>
            <button class="preview-btn secondary" onclick="window.close()">
                <i class="fas fa-times"></i>
                关闭
            </button>
        </div>
    </div>

    <!-- 预览内容 -->
    <div class="preview-content">
        <div class="preview-mode desktop" id="preview-container">
            <!-- 加载状态 -->
            <div class="preview-loading" id="preview-loading">
                <div class="preview-spinner"></div>
                <div>正在加载预览...</div>
            </div>
            
            <!-- 预览内容将在这里显示 -->
            <div id="preview-canvas"></div>
        </div>
    </div>

    <!-- 设备模式指示器 -->
    <div class="device-indicator" id="device-indicator">桌面模式</div>

    <!-- 基础JS -->
    <script src="/assets/js/jquery.min.js"></script>
    
    <script>
        // 初始化模板数据
        window.templateData = {
            id: {$template.id|default=0},
            name: {$template.name|default=""|json_encode|raw},
            html: {$template.html_content|default=""|json_encode|raw},
            css: {$template.css_content|default=""|json_encode|raw}
        };

        let currentDevice = 'desktop';

        // 切换设备预览模式
        function switchDevice(device) {
            currentDevice = device;
            const container = document.getElementById('preview-container');
            const indicator = document.getElementById('device-indicator');
            
            // 移除所有设备类
            container.classList.remove('mobile', 'tablet', 'desktop');
            
            // 添加新的设备类
            container.classList.add(device);
            
            // 更新指示器
            const deviceNames = {
                'mobile': '手机模式 (375px)',
                'tablet': '平板模式 (768px)', 
                'desktop': '桌面模式'
            };
            indicator.textContent = deviceNames[device];
            
            // 更新按钮状态
            document.querySelectorAll('.preview-toolbar-right .preview-btn').forEach(btn => {
                btn.classList.remove('primary');
                btn.classList.add('secondary');
            });
            event.target.classList.remove('secondary');
            event.target.classList.add('primary');
        }

        // 刷新预览
        function refreshPreview() {
            const loading = document.getElementById('preview-loading');
            const canvas = document.getElementById('preview-canvas');
            
            loading.style.display = 'block';
            
            // 重新加载内容
            setTimeout(() => {
                loadPreviewContent();
                loading.style.display = 'none';
            }, 500);
        }

        // 加载预览内容
        function loadPreviewContent() {
            const canvas = document.getElementById('preview-canvas');

            if (window.templateData.html && window.templateData.html.trim() !== '') {
                canvas.innerHTML = window.templateData.html;
            } else {
                canvas.innerHTML = `
                    <div style="text-align: center; padding: 100px 20px; color: #a0aec0;">
                        <h2>暂无预览内容</h2>
                        <p>请先在设计器中添加组件</p>
                    </div>
                `;
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟加载预览内容
            setTimeout(() => {
                loadPreviewContent();
                document.getElementById('preview-loading').style.display = 'none';
            }, 800);
        });
    </script>
</body>
</html>

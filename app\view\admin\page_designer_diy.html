<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DIY页面设计器 - {$template.name}</title>
    
    <!-- 基础CSS -->
    <link rel="stylesheet" href="/assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="/assets/css/all.min.css">
    
    <!-- DIY设计器CSS -->
    <link rel="stylesheet" href="/diy/css/all.css">
    <link rel="stylesheet" href="/diy/css/style.css">
    
    <style>
        /* 重置默认样式，确保全屏显示 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: #f5f7fa;
            height: 100vh;
            overflow: hidden;
        }
        
        /* 独立页面的顶部工具栏样式 */
        .diy-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: #2d3748;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            z-index: 1001;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .diy-header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .diy-header-title {
            font-size: 18px;
            font-weight: 600;
            color: white;
        }
        
        .diy-header-subtitle {
            font-size: 14px;
            color: #a0aec0;
        }
        
        .diy-header-right {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .diy-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .diy-btn.primary {
            background: #667eea;
            color: white;
        }
        
        .diy-btn.primary:hover {
            background: #5a67d8;
            color: white;
        }
        
        .diy-btn.success {
            background: #28a745;
            color: white;
        }
        
        .diy-btn.success:hover {
            background: #218838;
            color: white;
        }
        
        .diy-btn.secondary {
            background: #4a5568;
            color: white;
        }
        
        .diy-btn.secondary:hover {
            background: #2d3748;
            color: white;
        }

        .diy-btn.warning {
            background: #fd7e14;
            color: white;
        }

        .diy-btn.warning:hover {
            background: #e8690b;
            color: white;
        }

        /* 隐藏原有工具栏，使用我们的DIY头部 */
        .builder-container .toolbar {
            display: none;
        }
        
        /* 消息提示样式 */
        .message-toast {
            position: fixed;
            top: 80px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 2000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }
        
        .message-toast.show {
            opacity: 1;
            transform: translateX(0);
        }
        
        .message-toast.success {
            background: #28a745;
        }
        
        .message-toast.error {
            background: #dc3545;
        }
        
        .message-toast.info {
            background: #17a2b8;
        }

        /* 加载遮罩层样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .loading-content {
            background: white;
            padding: 40px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            max-width: 300px;
            width: 90%;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 16px;
            color: #333;
            margin-bottom: 10px;
            font-weight: 500;
        }

        .loading-subtext {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }

        /* 页面初始化加载样式 */
        .page-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f5f7fa;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            transition: all 0.5s ease;
        }

        .page-loading.hide {
            opacity: 0;
            visibility: hidden;
        }

        .page-loading-content {
            text-align: center;
        }

        .page-loading-spinner {
            width: 60px;
            height: 60px;
            border: 5px solid #e2e8f0;
            border-top: 5px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        .page-loading-text {
            font-size: 18px;
            color: #2d3748;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .page-loading-subtext {
            font-size: 14px;
            color: #718096;
        }
    </style>
</head>
<body>
    <!-- 页面初始化加载层 -->
    <div class="page-loading" id="page-loading">
        <div class="page-loading-content">
            <div class="page-loading-spinner"></div>
            <div class="page-loading-text">正在加载页面设计器</div>
            <div class="page-loading-subtext">请稍候，正在初始化编辑环境...</div>
        </div>
    </div>

    <!-- 操作加载遮罩层 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text" id="loading-text">正在处理...</div>
            <div class="loading-subtext" id="loading-subtext">请稍候</div>
        </div>
    </div>
    <!-- 独立页面头部 -->
    <div class="diy-header">
        <div class="diy-header-left">
            <div>
                <div class="diy-header-title">
                    <i class="fas fa-magic"></i> DIY页面设计器
                </div>
                <div class="diy-header-subtitle">正在编辑: {$template.name}</div>
            </div>
        </div>
        <div class="diy-header-right">
            <button class="diy-btn secondary" onclick="previewPage()">
                <i class="fas fa-eye"></i>
                预览
            </button>
            <button class="diy-btn secondary" onclick="viewHtmlCode()">
                <i class="fas fa-code"></i>
                查看代码
            </button>
            <button class="diy-btn warning" onclick="clearCanvas()">
                <i class="fas fa-trash"></i>
                清空画布
            </button>
            <button class="diy-btn success" onclick="saveDiyDesign()">
                <i class="fas fa-save"></i>
                保存设计
            </button>
            <button class="diy-btn secondary" onclick="closeDesigner()">
                <i class="fas fa-times"></i>
                关闭
            </button>
        </div>
    </div>

    <div class="builder-container">
        <!-- 顶部工具栏 -->

        <!-- 组件库 -->
        <div class="component-library">
            <!-- 网站模板分类 -->
            <div class="component-category">
                <div class="library-title">网站模板</div>
                <div class="components-grid">
                    <div class="component-item" onclick="showTemplateSelector()">
                        <span class="component-icon"><i data-lucide="layout-template"></i></span>
                        <span class="component-name">选择模板</span>
                    </div>
                </div>
            </div>

            <!-- 基础组件分类 -->
            <div class="component-category">
                <div class="library-title">基础组件</div>
                <div class="components-grid">
                    <div class="component-item" onclick="addComponent('background')">
                        <span class="component-icon"><i data-lucide="image"></i></span>
                        <span class="component-name">背景图</span>
                    </div>
                    <div class="component-item" onclick="addComponent('navbar')">
                        <span class="component-icon"><i data-lucide="navigation"></i></span>
                        <span class="component-name">导航栏</span>
                    </div>
                    <div class="component-item" onclick="addComponent('hero')">
                        <span class="component-icon"><i data-lucide="target"></i></span>
                        <span class="component-name">英雄区</span>
                    </div>
                    <div class="component-item" onclick="addComponent('section')">
                        <span class="component-icon"><i data-lucide="layout"></i></span>
                        <span class="component-name">区块</span>
                    </div>
                    <div class="component-item" onclick="addComponent('card')">
                        <span class="component-icon"><i data-lucide="credit-card"></i></span>
                        <span class="component-name">卡片</span>
                    </div>
                    <div class="component-item" onclick="addComponent('footer')">
                        <span class="component-icon"><i data-lucide="align-horizontal-distribute-end"></i></span>
                        <span class="component-name">页脚</span>
                    </div>
                </div>
            </div>

            <!-- 内容组件分类 -->
            <div class="component-category">
                <div class="library-title">内容组件</div>
                <div class="components-grid">
                    <div class="component-item" onclick="addComponent('textblock')">
                        <span class="component-icon"><i data-lucide="file-text"></i></span>
                        <span class="component-name">文本块</span>
                    </div>
                    <div class="component-item" onclick="addComponent('stats')">
                        <span class="component-icon"><i data-lucide="bar-chart-3"></i></span>
                        <span class="component-name">统计数字</span>
                    </div>
                    <div class="component-item" onclick="addComponent('team')">
                        <span class="component-icon"><i data-lucide="users"></i></span>
                        <span class="component-name">团队介绍</span>
                    </div>
                    <div class="component-item" onclick="addComponent('testimonials')">
                        <span class="component-icon"><i data-lucide="message-circle"></i></span>
                        <span class="component-name">客户评价</span>
                    </div>
                    <div class="component-item" onclick="addComponent('contact')">
                        <span class="component-icon"><i data-lucide="phone"></i></span>
                        <span class="component-name">联系信息</span>
                    </div>
                    <div class="component-item" onclick="addComponent('styletemplates')">
                        <span class="component-icon"><i data-lucide="palette"></i></span>
                        <span class="component-name">样式模板</span>
                    </div>
                </div>
            </div>

            <!-- 功能组件分类 -->
            <div class="component-category">
                <div class="library-title">功能组件</div>
                <div class="components-grid">
                    <div class="component-item" onclick="addComponent('news')">
                        <span class="component-icon"><i data-lucide="newspaper"></i></span>
                        <span class="component-name">新闻</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 编辑区 -->
        <div class="editor-area">
            <div class="canvas" id="canvas">
                <div style="text-align: center; padding: 100px 20px; color: #a0aec0;">
                    <h2>从左侧组件库选择组件开始构建页面</h2>
                    <p>点击组件添加到编辑区，支持拖拽排序</p>
                </div>
            </div>
        </div>

        <!-- 属性区 -->
        <div class="properties-panel" id="properties-panel">
            <div class="panel-title">属性设置</div>
            <div class="properties-content" id="properties-content">
                <p style="color: #a0aec0; text-align: center; margin-top: 50px;">请选择一个组件</p>
            </div>
        </div>
    </div>

    <!-- 消息提示容器 -->
    <div id="message-container"></div>

    <!-- 基础JS -->
    <script src="/assets/js/jquery.min.js"></script>
    
    <!-- 组件文件 -->
    <script src="/diy/js/components/background.js"></script>
    <script src="/diy/js/components/navbar.js"></script>
    <script src="/diy/js/components/hero.js"></script>
    <script src="/diy/js/components/section.js"></script>
    <script src="/diy/js/components/card.js"></script>
    <script src="/diy/js/components/footer.js"></script>
    <script src="/diy/js/components/textblock.js"></script>
    <script src="/diy/js/components/stats.js"></script>
    <script src="/diy/js/components/team.js"></script>
    <script src="/diy/js/components/testimonials.js"></script>
    <script src="/diy/js/components/contact.js"></script>
    <script src="/diy/js/components/manager.js"></script>

    <!-- 模板系统文件 -->
    <script src="/diy/js/templates/template-system.js"></script>
    <script src="/diy/js/templates/template-selector.js"></script>

    <!-- Lucide图标库 -->
    <script src="/assets/js/lucide.js"></script>

    <!-- 主文件 -->
    <script src="/diy/js/all.js"></script>

    <script>
        // 初始化模板数据
        window.templateId = {$template.id|default=0};
        window.templateData = {
            id: {$template.id|default=0},
            name: {$template.name|default=""|json_encode|raw},
            type: {$template.type|default=""|json_encode|raw},
            config: {$template.config|default=""|json_encode|raw},
            html: {$template.html_content|default=""|json_encode|raw},
            css: {$template.css_content|default=""|json_encode|raw}
        };

        // 初始化页面信息，将模板名称作为默认标题
        window.pageInfo = {
            title: {$template.name|default="我的模板"|json_encode|raw}
        };



        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 显示页面加载状态
            LoadingManager.showPageLoading();

            // 快速初始化过程
            setTimeout(function() {
                // 如果有已保存的设计，加载它
                let componentsHtml = '';
                let componentsData = null;

                // 首先尝试从config中获取组件HTML和属性数据
                if (window.templateData.config) {
                    try {
                        const config = typeof window.templateData.config === 'string'
                            ? JSON.parse(window.templateData.config)
                            : window.templateData.config;
                        if (config.components_html) {
                            componentsHtml = config.components_html;
                        }
                        if (config.components_data) {
                            componentsData = config.components_data;
                        }
                    } catch (e) {
                        console.log('解析config失败:', e);
                    }
                }

                // 如果config中没有，尝试从html中提取body内容
                if (!componentsHtml && window.templateData.html && window.templateData.html.trim() !== '') {
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = window.templateData.html;
                    const bodyContent = tempDiv.querySelector('body');
                    if (bodyContent) {
                        componentsHtml = bodyContent.innerHTML;
                    } else {
                        componentsHtml = window.templateData.html;
                    }
                }

                // 加载到画布
                if (componentsHtml) {
                    const canvas = document.getElementById('canvas');
                    if (canvas) {
                        canvas.innerHTML = componentsHtml;

                        // 恢复组件属性数据
                        if (componentsData) {
                            restoreComponentsData(componentsData);
                        }

                        // 重新绑定所有组件的点击事件
                        setTimeout(() => {
                            bindComponentEvents();
                        }, 100);
                    }
                }
                LoadingManager.hidePageLoading();
            }, 300);
        });

        // 加载状态管理
        const LoadingManager = {
            // 显示页面初始化加载
            showPageLoading() {
                const pageLoading = document.getElementById('page-loading');
                if (pageLoading) {
                    pageLoading.style.display = 'flex';
                }
            },

            // 隐藏页面初始化加载
            hidePageLoading() {
                const pageLoading = document.getElementById('page-loading');
                if (pageLoading) {
                    pageLoading.classList.add('hide');
                    setTimeout(() => {
                        pageLoading.style.display = 'none';
                    }, 500);
                }
            },

            // 显示操作加载
            showLoading(text = '正在处理...', subtext = '请稍候') {
                const overlay = document.getElementById('loading-overlay');
                const textEl = document.getElementById('loading-text');
                const subtextEl = document.getElementById('loading-subtext');

                if (overlay && textEl && subtextEl) {
                    textEl.textContent = text;
                    subtextEl.textContent = subtext;
                    overlay.classList.add('show');
                }
            },

            // 隐藏操作加载
            hideLoading() {
                const overlay = document.getElementById('loading-overlay');
                if (overlay) {
                    overlay.classList.remove('show');
                }
            }
        };

        // showMessage函数已移至message.html统一管理

        // 保存DIY设计的函数
        function saveDiyDesign() {
            const canvas = document.getElementById('canvas');
            if (!canvas) {
                showMessage('编辑器未初始化', 'error');
                return;
            }

            // 显示加载状态
            LoadingManager.showLoading('正在保存设计...', '请稍候，正在将您的设计保存到数据库');

            // 保存两种HTML：
            // 1. 完整页面HTML（用于预览和导出）
            const fullPageHtml = getPreviewHTML();
            // 2. 组件片段HTML（用于编辑器加载）
            const componentsHtml = canvas.innerHTML;
            const cssContent = extractCssFromPage();

            // 3. 收集所有组件的属性数据
            const componentsData = collectComponentsData();

            // 获取模板标题 - 从pageInfo对象获取
            const templateTitle = (window.pageInfo && window.pageInfo.title) ? window.pageInfo.title.trim() : '';

            const data = {
                action: 'save_design',
                id: window.templateId,
                html: fullPageHtml,           // 完整页面HTML
                css: cssContent,
                config: JSON.stringify({
                    type: 'diy',
                    timestamp: new Date().getTime(),
                    components_html: componentsHtml,  // 组件片段HTML
                    components_data: componentsData   // 组件属性数据
                })
            };

            // 只在有有效标题时才包含name字段
            if (templateTitle && templateTitle.length > 0) {
                data.name = templateTitle;
            }

            fetch('/admin/page-designer/diy?id=' + window.templateId, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                LoadingManager.hideLoading();
                if (result.success) {
                    showMessage('设计已保存成功！', 'success');
                } else {
                    showMessage('保存失败: ' + result.message, 'error');
                }
            })
            .catch(error => {
                LoadingManager.hideLoading();
                console.error('保存失败:', error);
                showMessage('保存失败: ' + error.message, 'error');
            });
        }

        // 提取页面CSS的函数 - 只提取画布内组件的内联样式
        function extractCssFromPage() {
            let css = '';

            // 方法1：从画布内的组件提取内联样式
            const canvas = document.getElementById('canvas');
            if (canvas) {
                const elementsWithStyle = canvas.querySelectorAll('[style]');
                elementsWithStyle.forEach(element => {
                    if (element.style.cssText) {
                        // 为每个有内联样式的元素生成CSS规则
                        let className = element.className;
                        if (!className || typeof className !== 'string') {
                            className = 'element-' + Math.random().toString(36).substr(2, 9);
                            element.className = className;
                        }

                        // 安全地获取第一个类名
                        const firstClassName = className.toString().split(' ')[0];
                        if (firstClassName) {
                            css += `.${firstClassName} { ${element.style.cssText} }\n`;
                        }
                    }
                });
            }

            return css;
        }

        // 清空画布（同时清空数据库）
        function clearCanvas() {
            // 显示加载状态
            LoadingManager.showLoading('正在清空数据...', '请稍候，正在清除画布和数据库内容');

            // 先清空数据库
            const data = {
                action: 'save_design',
                id: window.templateId,
                html: '',
                css: '',
                config: ''
            };

            fetch('/admin/page-designer/diy?id=' + window.templateId, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                LoadingManager.hideLoading();
                if (result.success) {
                    // 数据库清空成功后，清空画布
                    const canvas = document.getElementById('canvas');
                    if (canvas) {
                        canvas.innerHTML = `
                            <div style="text-align: center; padding: 100px 20px; color: #a0aec0;">
                                <h2>从左侧组件库选择组件开始构建页面</h2>
                                <p>点击组件添加到编辑区，支持拖拽排序</p>
                            </div>
                        `;
                    }
                    showMessage('画布已清空！', 'success');
                } else {
                    showMessage('清空失败: ' + result.message, 'error');
                }
            })
            .catch(error => {
                LoadingManager.hideLoading();
                console.error('清空失败:', error);
                showMessage('清空失败: ' + error.message, 'error');
            });
        }



        // 关闭设计器
        function closeDesigner() {
            if (confirm('确定要关闭设计器吗？未保存的更改将会丢失。')) {
                window.close();
                // 如果无法关闭窗口，尝试返回上一页
                if (!window.closed) {
                    window.history.back();
                }
            }
        }

        // 预览页面函数
        function previewPage() {
            const previewUrl = '/admin/page-designer/preview/' + window.templateId;
            window.open(previewUrl, '_blank');
            showMessage('预览页面已在新标签页打开', 'success');
        }

        // 收集所有组件的属性数据
        function collectComponentsData() {
            const canvas = document.getElementById('canvas');
            if (!canvas) return {};

            const components = canvas.querySelectorAll('.component-block');
            const componentsData = {};

            components.forEach(component => {
                const componentId = component.id;
                const componentType = component.dataset.type;

                if (componentType && componentTemplates[componentType]) {
                    let properties;

                    // 特殊处理统计组件的属性存储
                    if (componentType === 'stats' && component._statsProperties) {
                        properties = component._statsProperties;
                    } else {
                        properties = componentTemplates[componentType].properties;
                    }

                    componentsData[componentId] = {
                        type: componentType,
                        properties: JSON.parse(JSON.stringify(properties))  // 深拷贝属性对象
                    };
                }
            });

            console.log('收集到组件数据:', componentsData);
            return componentsData;
        }

        // 恢复组件属性数据
        function restoreComponentsData(componentsData) {
            if (!componentsData) return;

            Object.keys(componentsData).forEach(componentId => {
                const componentData = componentsData[componentId];
                const componentType = componentData.type;

                if (componentTemplates[componentType]) {
                    // 特殊处理统计组件的属性恢复
                    if (componentType === 'stats') {
                        const component = document.getElementById(componentId);
                        if (component) {
                            component._statsProperties = JSON.parse(JSON.stringify(componentData.properties));
                        }
                        // 同时也更新模板属性，保持兼容性
                        componentTemplates[componentType].properties = JSON.parse(JSON.stringify(componentData.properties));
                    } else {
                        // 其他组件正常恢复
                        componentTemplates[componentType].properties = JSON.parse(JSON.stringify(componentData.properties));
                    }

                    // 重新应用组件的动态样式
                    setTimeout(() => {
                        applyComponentDynamicStyles(componentId, componentType, componentData.properties);
                    }, 200);
                }
            });

            console.log('恢复了组件属性数据');
        }

        // 应用组件动态样式
        function applyComponentDynamicStyles(componentId, componentType, properties) {
            try {
                // 根据组件类型应用对应的动态样式
                switch (componentType) {
                    case 'hero':
                        if (typeof updateHeroNavHoverStyles === 'function') {
                            updateHeroNavHoverStyles(componentId, properties);
                        }
                        if (typeof updateHeroButtonHoverStyles === 'function') {
                            updateHeroButtonHoverStyles(componentId, properties);
                        }
                        break;
                    case 'navbar':
                        if (typeof updateNavbarHoverStyles === 'function') {
                            updateNavbarHoverStyles(componentId, properties);
                        }
                        break;
                    case 'footer':
                        if (typeof updateFooterHoverStyles === 'function') {
                            updateFooterHoverStyles(componentId, properties);
                        }
                        break;
                    case 'stats':
                        if (typeof updateStatsCSS === 'function') {
                            updateStatsCSS(componentId, properties);
                        }
                        break;
                    case 'section':
                        if (typeof updateSectionCSS === 'function') {
                            updateSectionCSS(componentId, properties);
                        }
                        break;
                    case 'textblock':
                        if (typeof applyTextblockCSS === 'function') {
                            applyTextblockCSS(componentId, properties);
                        }
                        break;
                }

                console.log(`应用了 ${componentType} 组件的动态样式:`, componentId);
            } catch (error) {
                console.warn(`应用 ${componentType} 组件动态样式失败:`, error);
            }
        }

        // 重新绑定组件事件的函数
        function bindComponentEvents() {
            const canvas = document.getElementById('canvas');
            if (!canvas) return;

            // 为所有组件绑定点击事件
            const components = canvas.querySelectorAll('.component-block');
            components.forEach(component => {
                // 移除可能存在的旧事件监听器
                const newComponent = component.cloneNode(true);
                component.parentNode.replaceChild(newComponent, component);

                // 绑定新的点击事件
                newComponent.addEventListener('click', (e) => {
                    e.stopPropagation();
                    selectComponent(newComponent.id);
                });
            });

            console.log('重新绑定了', components.length, '个组件的事件');
        }

        // 重写原有的保存函数，使其调用DIY保存
        window.savePage = saveDiyDesign;





        // 跟踪画布变化的函数
        function trackChanges() {
            // 记录画布变化，避免频繁操作
            console.log('画布内容发生变化');
        }

        // 初始化 Lucide 图标
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });
    </script>
</body>
</html> 
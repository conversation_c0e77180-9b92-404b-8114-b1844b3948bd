<?php
declare(strict_types=1);

namespace app\admin\controller;

use think\facade\Db;
use think\facade\Request;
use think\facade\Session;
use think\facade\Filesystem;
use think\exception\ValidateException;
use app\BaseController;
use app\service\FileSecurityService;
use app\validate\SecurityValidate;

class Banners extends BaseController
{
    public function index()
    {
        try {
            $admin_info = [];

        // 获取操作类型
        $action = Request::param('action', 'list');
        $id = (int)Request::param('id', 0);

        // 处理表单提交
        if (Request::isPost()) {
            return $this->handlePost();
        }

        // 处理删除操作
        if ($action === 'delete' && $id > 0) {
            return $this->handleDelete($id);
        }

        // AJAX状态切换已在handlePost中处理
        
        // 处理状态切换
        if ($action === 'toggle_status' && $id > 0) {
            return $this->handleToggleStatus($id);
        }

        // 获取编辑数据
        $editData = null;
        if ($action === 'edit' && $id > 0) {
            try {
                $editData = Db::table('banners')->where('id', $id)->find();
                if (!$editData) {
                        return '<h1>错误</h1><p>轮播图不存在</p><a href="/admin/banners">返回列表</a>';
                }
            } catch (\Exception $e) {
                    return '<h1>数据库错误</h1><p>' . $e->getMessage() . '</p><a href="/admin/banners">返回列表</a>';
            }
        }

        // 获取轮播图列表
        $page = (int)Request::param('page', 1);
        $itemsPerPage = 10;
        $offset = ($page - 1) * $itemsPerPage;

        $totalItems = 0;
        $banners = [];

        try {

            // 获取总数
            $totalItems = Db::table('banners')->count();

            // 获取列表数据
            $banners = Db::table('banners')
                ->order('sort_order', 'asc')
                ->order('id', 'desc')
                ->limit($offset, $itemsPerPage)
                ->select()
                ->toArray();

        } catch (\Exception $e) {
                return '<h1>数据获取错误</h1><p>' . $e->getMessage() . '</p>';
        }

        $totalPages = ceil($totalItems / $itemsPerPage);

            // 获取消息
            $message = Session::pull('message');
            $messageType = Session::pull('messageType');

        return view('admin/banners', [
            'pageTitle' => '轮播图管理',
            'pageIcon' => 'fas fa-images',
            'admin_info' => $admin_info,
            'action' => $action,
            'editData' => $editData,
            'banners' => $banners,
            'totalItems' => $totalItems,
            'totalPages' => $totalPages,
            'page' => $page,
            'message' => $message,
            'messageType' => $messageType,
            'currentController' => 'Banners'
        ]);
            
        } catch (\Exception $e) {
            return '<h1>控制器错误</h1><p>' . $e->getMessage() . '</p><p>文件：' . $e->getFile() . '</p><p>行号：' . $e->getLine() . '</p>';
        }
    }
    
    private function handlePost()
    {
        $action = Request::param('action');
        
        // 检查是否是AJAX状态切换请求
        if ($action === 'toggle_status' && (Request::isAjax() || isset($_SERVER['HTTP_X_REQUESTED_WITH']))) {
            return $this->handleToggleStatusAjax();
        }
        
        switch ($action) {
            case 'add':
            case 'edit':
                return $this->handleSave();
            default:
                Session::flash('message', '无效的操作');
                Session::flash('messageType', 'error');
                return redirect('/admin/banners');
        }
    }
    
    private function handleSave()
    {
        $data = Request::param();
        $action = $data['action'];
        $id = $data['id'] ?? 0;

        // 数据安全验证 - 使用统一安全验证器
        $securityCheck = SecurityValidate::validateDataSecurity($data, [
            'title' => 'checkXss',
            'subtitle' => 'checkXss',
            'description' => 'checkXss',
            'link_url' => 'checkUrlSafe',
        ]);

        if (!$securityCheck['valid']) {
            $errors = [];
            foreach ($securityCheck['errors'] as $field => $fieldErrors) {
                $errors[] = $field . ': ' . implode(', ', $fieldErrors);
            }
            Session::flash('message', '数据安全检查失败: ' . implode('; ', $errors));
            Session::flash('messageType', 'error');
            return redirect()->restore();
        }

        // 验证必填字段
        if (empty($data['title'])) {
            Session::flash('message', '请输入轮播图标题');
            Session::flash('messageType', 'error');
            return redirect()->restore();
        }
            
            // 检查轮播图数量限制（仅在添加时检查）
            if ($action === 'add') {
                $currentCount = Db::table('banners')->count();
                if ($currentCount >= 5) {
                Session::flash('message', '最多只能添加5个轮播图');
                Session::flash('messageType', 'error');
                return redirect('/admin/banners');
                }
            }
            
            // 处理图片选择器传递的图片路径
        $imagePath = '';
        $imageChanged = false;
        
        $selectedImagePath = trim($data['image'] ?? '');
        
        if (!empty($selectedImagePath)) {
            // 图片选择器选择的图片
            $imagePath = $selectedImagePath;
            // 确保路径以 / 开头
            if (strpos($imagePath, '/') !== 0) {
                $imagePath = '/' . $imagePath;
            }

            // 图片路径安全验证
            $pathSecurityCheck = SecurityValidate::validateDataSecurity(['image_path' => $imagePath], [
                'image_path' => 'checkPathSafe',
            ]);

            if (!$pathSecurityCheck['valid']) {
                Session::flash('message', '图片路径安全检查失败：' . implode(', ', $pathSecurityCheck['errors']['image_path'] ?? ['路径不安全']));
                Session::flash('messageType', 'error');
                return redirect()->restore();
            }

            // 检查图片文件是否存在且安全（如果是本地文件）
            if (strpos($imagePath, '/storage/') === 0 || strpos($imagePath, '/uploads/') === 0) {
                $fullPath = public_path() . $imagePath;
                if (file_exists($fullPath)) {
                    // 基础的图片文件安全检查
                    $imageInfo = @getimagesize($fullPath);
                    if (!$imageInfo) {
                        Session::flash('message', '选择的文件不是有效的图片文件');
                        Session::flash('messageType', 'error');
                        return redirect()->restore();
                    }

                    // 检查文件大小（限制为10MB）
                    $fileSize = filesize($fullPath);
                    if ($fileSize > 10 * 1024 * 1024) {
                        Session::flash('message', '图片文件过大，请选择小于10MB的图片');
                        Session::flash('messageType', 'error');
                        return redirect()->restore();
                    }
                }
            }

            $imageChanged = true;
        }
        
        // 检查是否有图片（添加时必须要有图片）
        if ($action === 'add' && empty($imagePath)) {
            Session::flash('message', '请选择轮播图片');
            Session::flash('messageType', 'error');
            return redirect()->restore();
        }

        // 准备数据
        $saveData = [
            'title' => trim($data['title']),
            'subtitle' => trim($data['subtitle'] ?? ''),
            'description' => trim($data['description'] ?? ''),
            'link_url' => trim($data['link_url'] ?? ''),
            'sort_order' => (int)($data['sort_order'] ?? 0),
            'status' => isset($data['status']) ? 1 : 0,
        ];

        // 处理图片字段
        if ($action === 'add') {
            // 添加时必须有图片
            $saveData['image'] = $imagePath;
        } elseif ($imageChanged && $imagePath) {
            // 编辑时只有在图片有变化时才更新图片字段
            $saveData['image'] = $imagePath;
        }
        // 编辑时如果没有选择新图片，保持原有图片不变（不更新image字段）
            
        try {
            if ($action === 'add') {
                $saveData['created_at'] = date('Y-m-d H:i:s');
                Db::table('banners')->insert($saveData);
                Session::flash('message', '轮播图添加成功');
            } else {
                $banner = Db::table('banners')->where('id', $id)->find();
                if (!$banner) {
                    Session::flash('message', '轮播图不存在');
                    Session::flash('messageType', 'error');
                    return redirect('/admin/banners');
                }

                // 如果上传了新图片，删除旧图片（仅当旧图片是本地文件时）
                if ($imageChanged && $imagePath && $banner['image'] && $banner['image'] !== $imagePath && strpos($banner['image'], '/storage/') === 0) {
                    try {
                        $oldImagePath = str_replace('/storage/', '', $banner['image']);
                        Filesystem::disk('public')->delete($oldImagePath);
                    } catch (\Exception $e) {
                        // 忽略删除失败
                    }
                }

                $saveData['updated_at'] = date('Y-m-d H:i:s');
                Db::table('banners')->where('id', $id)->update($saveData);
                Session::flash('message', '轮播图更新成功');
            }
            
            Session::flash('messageType', 'success');
            return redirect('/admin/banners');
            
        } catch (\Exception $e) {
            Session::flash('message', '操作失败：' . $e->getMessage());
            Session::flash('messageType', 'error');
            return redirect()->restore();
        }
    }
    
    private function handleDelete($id)
    {
        try {
            // 获取轮播图信息
            $banner = Db::table('banners')->where('id', $id)->find();

            if (!$banner) {
                Session::flash('message', '轮播图不存在');
                Session::flash('messageType', 'error');
                return redirect('/admin/banners');
            }

            // 删除图片记录和文件
            if (!empty($banner['image'])) {
                $this->deleteImageRecord($banner['image']);
            }

            // 删除数据库记录
            Db::table('banners')->where('id', $id)->delete();

            Session::flash('message', '轮播图删除成功');
            Session::flash('messageType', 'success');
            return redirect('/admin/banners');

        } catch (\Exception $e) {
            Session::flash('message', '删除失败：' . $e->getMessage());
            Session::flash('messageType', 'error');
            return redirect('/admin/banners');
        }
    }

    /**
     * 删除图片记录和文件
     */
    private function deleteImageRecord($imageUrl)
    {
        if (empty($imageUrl)) {
            return;
        }

        try {
            // 查找图片数据库记录
            $image = \app\model\Image::where('file_url', $imageUrl)
                ->whereOr('file_path', $imageUrl)
                ->find();

            if ($image) {
                // 删除数据库记录（模型事件会自动删除物理文件）
                $image->delete();
            } else {
                // 如果数据库中没有记录，尝试删除物理文件（兼容旧数据）
                $this->deletePhysicalFile($imageUrl);
            }
        } catch (\Exception $e) {
            // 记录错误但不影响主流程
            trace('删除图片失败：' . $e->getMessage(), 'error');
            // 尝试删除物理文件作为备用方案
            $this->deletePhysicalFile($imageUrl);
        }
    }

    /**
     * 删除物理文件（备用方案）
     */
    private function deletePhysicalFile($imageUrl)
    {
        try {
            if (strpos($imageUrl, '/storage/') === 0) {
                $imagePath = str_replace('/storage/', '', $imageUrl);
                Filesystem::disk('public')->delete($imagePath);
            } elseif (strpos($imageUrl, '/uploads/') === 0) {
                $filePath = public_path() . $imageUrl;
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            } else {
                // 兼容旧的直接路径格式
                $filePath = public_path() . '/' . $imageUrl;
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }
        } catch (\Exception $e) {
            // 忽略删除失败
        }
    }
    
    private function handleToggleStatusAjax()
    {
        try {
            // 调试日志
            error_log("AJAX请求到达: " . date('Y-m-d H:i:s'));
            
            // 获取JSON数据
            $input = file_get_contents('php://input');
            error_log("原始输入: " . $input);
            
            $data = json_decode($input, true);
            error_log("解析后数据: " . json_encode($data));
            
            if (!$data || !isset($data['id']) || !isset($data['status'])) {
                return json(['success' => false, 'message' => '参数错误，输入数据：' . $input]);
            }
            
            $id = (int)$data['id'];
            $status = (int)$data['status'];
            
            // 验证轮播图是否存在
            $banner = Db::table('banners')->where('id', $id)->find();
            if (!$banner) {
                return json(['success' => false, 'message' => '轮播图不存在']);
            }
            
            // 更新状态
            Db::table('banners')->where('id', $id)->update([
                'status' => $status,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            $statusText = $status ? '启用' : '禁用';
            return json([
                'success' => true,
                'message' => '状态已切换为' . $statusText,
                'data' => [
                    'id' => $id,
                    'status' => $status,
                    'status_text' => $statusText
                ]
            ]);
            
        } catch (\Exception $e) {
            return json(['success' => false, 'message' => '状态更新失败：' . $e->getMessage()]);
        }
    }
    
    private function handleToggleStatus($id)
    {
        try {
            Db::table('banners')->where('id', $id)->update([
                'status' => Db::raw('1 - status'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            Session::flash('message', '状态更新成功');
            Session::flash('messageType', 'success');
            return redirect('/admin/banners');
            
        } catch (\Exception $e) {
            Session::flash('message', '状态更新失败：' . $e->getMessage());
            Session::flash('messageType', 'error');
            return redirect('/admin/banners');
        }
    }

}

{"version": 3, "file": "hi.js", "sources": ["../../../../../packages/locale/lang/hi.ts"], "sourcesContent": ["export default {\n  name: 'hi',\n  el: {\n    breadcrumb: {\n      label: 'ब्रेडक्रंब',\n    },\n    colorpicker: {\n      confirm: 'ठीक है',\n      clear: 'हटाएँ',\n      defaultLabel: 'कलर पिकर',\n      description:\n        'मौजूदा रंग {color} है. कोई नया रंग चुनने के लिए एंटर दबाएँ.',\n      alphaLabel: 'अल्फा मान चुनें',\n    },\n    datepicker: {\n      now: 'अभी',\n      today: 'आज',\n      cancel: 'कैंसिल करें',\n      clear: 'हटाएँ',\n      confirm: 'ठीक है',\n      dateTablePrompt:\n        'महीने का दिन चुनने के लिए एरो कुंजी का इस्तेमाल करें और एंटर करें',\n      monthTablePrompt:\n        'महीने चुनने के लिए एरो कुंजी का इस्तेमाल करें और एंटर करें ',\n      yearTablePrompt:\n        'साल चुनने के लिए एरो कुंजी का इस्तेमाल करें और एंटर करें',\n      selectedDate: 'चुनी गई तारीख',\n      selectDate: 'तारीख चुनें',\n      selectTime: 'समय चुनें',\n      startDate: 'शुरू होने की तारीख',\n      startTime: 'शुरू होने का समय',\n      endDate: 'खत्म होने की तारीख',\n      endTime: 'खत्म होने का समय',\n      prevYear: 'पिछला साल',\n      nextYear: 'अगला साल',\n      prevMonth: 'पिछला महीना',\n      nextMonth: 'अगला महीना',\n      year: '',\n      month1: 'जनवरी',\n      month2: 'फरवरी',\n      month3: 'मार्च',\n      month4: 'अप्रैल',\n      month5: 'मई',\n      month6: 'जून',\n      month7: 'जुलाई',\n      month8: 'अगस्त',\n      month9: 'सितंबर',\n      month10: 'अक्टूबर',\n      month11: 'नवंबर',\n      month12: 'दिसंबर',\n      week: 'सप्ताह',\n      weeks: {\n        sun: 'रवि',\n        mon: 'सोम',\n        tue: 'मंगल',\n        wed: 'बुध',\n        thu: 'गुरु',\n        fri: 'शुक्र',\n        sat: 'शनि',\n      },\n      weeksFull: {\n        sun: 'रविवार',\n        mon: 'सोमवार',\n        tue: 'मंगलवार',\n        wed: 'बुधवार',\n        thu: 'गुरुवार',\n        fri: 'शुक्रवार',\n        sat: 'शनिवार',\n      },\n      months: {\n        jan: 'जन.',\n        feb: 'फर.',\n        mar: 'मार्च',\n        apr: 'अप्रैल',\n        may: 'मई',\n        jun: 'जून',\n        jul: 'जुलाई',\n        aug: 'अग.',\n        sep: 'सितं.',\n        oct: 'अक्तू.',\n        nov: 'नवं.',\n        dec: 'दिसं.',\n      },\n    },\n    inputNumber: {\n      decrease: 'संख्या घटाएँ',\n      increase: 'संख्या बढ़ाएँ',\n    },\n    select: {\n      loading: 'लोड हो रहा है',\n      noMatch: 'कोई मैचिंग डेटा नहीं है',\n      noData: 'कोई डेटा नहीं है',\n      placeholder: 'चुनें',\n    },\n    mention: {\n      loading: 'लोड हो रहा है',\n    },\n    dropdown: {\n      toggleDropdown: 'ड्रॉपडाउन को टॉगल करें',\n    },\n    cascader: {\n      noMatch: 'कोई मैचिंग डेटा नहीं है',\n      loading: 'लोड हो रहा है',\n      placeholder: 'चुनें',\n      noData: 'कोई डेटा नहीं है',\n    },\n    pagination: {\n      goto: 'पर जाएँ',\n      pagesize: '/पेज',\n      total: 'कुल {total}',\n      pageClassifier: '',\n      page: 'पेज',\n      prev: 'पिछले पेज पर जाएँ',\n      next: 'अगले पेज पर जाएँ',\n      currentPage: 'पेज {pager}',\n      prevPages: 'पिछले {pager} पेज',\n      nextPages: 'अगले {pager} पेज',\n      deprecationWarning:\n        'पुरानी पद्धति के उपयोग का पता चला, अधिक जानकारी के लिए एल-पेजिनेशन का डॉक्यूमेंटेशन देखें',\n    },\n    dialog: {\n      close: 'यह डायलॉग बंद करें',\n    },\n    drawer: {\n      close: 'यह डायलॉग बंद करें',\n    },\n    messagebox: {\n      title: 'मैसेज',\n      confirm: 'ठीक है',\n      cancel: 'कैंसिल करें',\n      error: 'अवैध इनपुट',\n      close: 'यह डायलॉग बंद करें',\n    },\n    upload: {\n      deleteTip: 'हटाने के लिए डिलीट दबाएँ',\n      delete: 'हटाएँ',\n      preview: 'प्रीव्यू',\n      continue: 'जारी रखें',\n    },\n    slider: {\n      defaultLabel: '{min} और {max} के बीच स्लाइडर',\n      defaultRangeStartLabel: 'शुरूआती वैल्यू चुनें',\n      defaultRangeEndLabel: 'समाप्ति की वैल्यू चुनें',\n    },\n    table: {\n      emptyText: 'कोई डेटा नहीं है',\n      confirmFilter: 'पुष्टि करें',\n      resetFilter: 'रीसेट करें',\n      clearFilter: 'सभी',\n      sumText: 'जोड़े',\n    },\n    tour: {\n      next: 'अगला',\n      previous: 'पिछला',\n      finish: 'पूरा करें',\n    },\n    tree: {\n      emptyText: 'कोई डेटा नहीं है',\n    },\n    transfer: {\n      noMatch: 'कोई मैचिंग डेटा नहीं है',\n      noData: 'कोई डेटा नहीं है',\n      titles: ['लिस्ट 1', 'लिस्ट 2'],\n      filterPlaceholder: 'कीवर्ड डालें',\n      noCheckedFormat: '{total} आइटम',\n      hasCheckedFormat: '{checked}/{total} चेक किया गया',\n    },\n    image: {\n      error: 'नहीं हो सका',\n    },\n    pageHeader: {\n      title: 'पीछे जाएँ ',\n    },\n    popconfirm: {\n      confirmButtonText: 'हाँ',\n      cancelButtonText: 'नहीं',\n    },\n    carousel: {\n      leftArrow: 'कैरोसेल तीर बाएँ',\n      rightArrow: 'कैरोसेल तीर दाएँ',\n      indicator: 'कैरोसेल इंडेक्स {index} पर स्विच करें',\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,8DAA8D;AAC3E,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,iCAAiC;AAChD,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,YAAY,EAAE,6CAA6C;AACjE,MAAM,WAAW,EAAE,gQAAgQ;AACnR,MAAM,UAAU,EAAE,kFAAkF;AACpG,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,oBAAoB;AAC/B,MAAM,KAAK,EAAE,cAAc;AAC3B,MAAM,MAAM,EAAE,+DAA+D;AAC7E,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,OAAO,EAAE,iCAAiC;AAChD,MAAM,eAAe,EAAE,uUAAuU;AAC9V,MAAM,gBAAgB,EAAE,wSAAwS;AAChU,MAAM,eAAe,EAAE,2RAA2R;AAClT,MAAM,YAAY,EAAE,sEAAsE;AAC1F,MAAM,UAAU,EAAE,+DAA+D;AACjF,MAAM,UAAU,EAAE,mDAAmD;AACrE,MAAM,SAAS,EAAE,+FAA+F;AAChH,MAAM,SAAS,EAAE,mFAAmF;AACpG,MAAM,OAAO,EAAE,+FAA+F;AAC9G,MAAM,OAAO,EAAE,mFAAmF;AAClG,MAAM,QAAQ,EAAE,mDAAmD;AACnE,MAAM,QAAQ,EAAE,6CAA6C;AAC7D,MAAM,SAAS,EAAE,+DAA+D;AAChF,MAAM,SAAS,EAAE,yDAAyD;AAC1E,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,OAAO,EAAE,sCAAsC;AACrD,MAAM,IAAI,EAAE,sCAAsC;AAClD,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,oBAAoB;AACjC,OAAO;AACP,MAAM,SAAS,EAAE;AACjB,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,sCAAsC;AACnD,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,eAAe;AAC5B,QAAQ,GAAG,EAAE,eAAe;AAC5B,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,eAAe;AAC5B,QAAQ,GAAG,EAAE,2BAA2B;AACxC,QAAQ,GAAG,EAAE,iCAAiC;AAC9C,QAAQ,GAAG,EAAE,qBAAqB;AAClC,QAAQ,GAAG,EAAE,2BAA2B;AACxC,OAAO;AACP,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,QAAQ,EAAE,qEAAqE;AACrF,MAAM,QAAQ,EAAE,2EAA2E;AAC3F,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,iEAAiE;AAChF,MAAM,OAAO,EAAE,wHAAwH;AACvI,MAAM,MAAM,EAAE,mFAAmF;AACjG,MAAM,WAAW,EAAE,gCAAgC;AACnD,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,iEAAiE;AAChF,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,cAAc,EAAE,uHAAuH;AAC7I,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,wHAAwH;AACvI,MAAM,OAAO,EAAE,iEAAiE;AAChF,MAAM,WAAW,EAAE,gCAAgC;AACnD,MAAM,MAAM,EAAE,mFAAmF;AACjG,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,uCAAuC;AACnD,MAAM,QAAQ,EAAE,qBAAqB;AACrC,MAAM,KAAK,EAAE,4BAA4B;AACzC,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,oBAAoB;AAChC,MAAM,IAAI,EAAE,yFAAyF;AACrG,MAAM,IAAI,EAAE,mFAAmF;AAC/F,MAAM,WAAW,EAAE,4BAA4B;AAC/C,MAAM,SAAS,EAAE,2DAA2D;AAC5E,MAAM,SAAS,EAAE,qDAAqD;AACtE,MAAM,kBAAkB,EAAE,wcAAwc;AACle,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,+FAA+F;AAC5G,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,+FAA+F;AAC5G,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,OAAO,EAAE,iCAAiC;AAChD,MAAM,MAAM,EAAE,+DAA+D;AAC7E,MAAM,KAAK,EAAE,yDAAyD;AACtE,MAAM,KAAK,EAAE,+FAA+F;AAC5G,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,8HAA8H;AAC/I,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,QAAQ,EAAE,mDAAmD;AACnE,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,YAAY,EAAE,qGAAqG;AACzH,MAAM,sBAAsB,EAAE,gHAAgH;AAC9I,MAAM,oBAAoB,EAAE,6HAA6H;AACzJ,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,mFAAmF;AACpG,MAAM,aAAa,EAAE,+DAA+D;AACpF,MAAM,WAAW,EAAE,yDAAyD;AAC5E,MAAM,WAAW,EAAE,oBAAoB;AACvC,MAAM,OAAO,EAAE,0BAA0B;AACzC,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,0BAA0B;AACtC,MAAM,QAAQ,EAAE,gCAAgC;AAChD,MAAM,MAAM,EAAE,mDAAmD;AACjE,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,mFAAmF;AACpG,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,wHAAwH;AACvI,MAAM,MAAM,EAAE,mFAAmF;AACjG,MAAM,MAAM,EAAE,CAAC,kCAAkC,EAAE,kCAAkC,CAAC;AACtF,MAAM,iBAAiB,EAAE,qEAAqE;AAC9F,MAAM,eAAe,EAAE,kCAAkC;AACzD,MAAM,gBAAgB,EAAE,kFAAkF;AAC1G,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,0DAA0D;AACvE,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,oDAAoD;AACjE,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,oBAAoB;AAC7C,MAAM,gBAAgB,EAAE,0BAA0B;AAClD,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,wFAAwF;AACzG,MAAM,UAAU,EAAE,wFAAwF;AAC1G,MAAM,SAAS,EAAE,oKAAoK;AACrL,KAAK;AACL,GAAG;AACH,CAAC;;;;"}
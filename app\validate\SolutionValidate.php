<?php
/**
 * 三只鱼网络科技 | 韩总 | 2025-01-15
 * 文件描述：解决方案验证器 - ThinkPHP6企业级应用
 * 功能：解决方案数据验证和安全过滤
 */

namespace app\validate;

class SolutionValidate extends BaseValidate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'name' => 'require|length:1,200|checkSql',
        'slug' => 'require|length:1,200|alphaNum|checkSql',
        'short_description' => 'length:0,500|checkSql',
        'description' => 'checkSql',
        'features' => 'checkSql',
        'icon' => 'length:0,200|checkSql',
        'sort_order' => 'integer|egt:0',
        'status' => 'in:0,1',
        'image_url' => 'url|checkSql',
    ];
    
    /**
     * 验证消息
     */
    protected $message = [
        'name.require' => '解决方案名称不能为空',
        'name.length' => '解决方案名称长度不能超过200个字符',
        'name.checkSql' => '解决方案名称包含非法字符',
        'slug.require' => '解决方案别名不能为空',
        'slug.length' => '解决方案别名长度不能超过200个字符',
        'slug.alphaNum' => '解决方案别名只能包含字母和数字',
        'slug.checkSql' => '解决方案别名包含非法字符',
        'short_description.length' => '简短描述长度不能超过500个字符',
        'short_description.checkSql' => '简短描述包含非法字符',
        'description.checkSql' => '解决方案描述包含非法字符',
        'features.checkSql' => '解决方案特性包含非法字符',
        'icon.length' => '图标长度不能超过200个字符',
        'icon.checkSql' => '图标包含非法字符',
        'sort_order.integer' => '排序必须为整数',
        'sort_order.egt' => '排序必须大于等于0',
        'status.in' => '状态值无效',
        'image_url.url' => '图片URL格式无效',
        'image_url.checkSql' => '图片URL包含非法字符',
    ];
    
    /**
     * 验证场景
     */
    protected $scene = [
        'add' => ['name', 'short_description', 'description', 'features', 'icon', 'sort_order', 'status', 'image_url'],
        'edit' => ['name', 'short_description', 'description', 'features', 'icon', 'sort_order', 'status', 'image_url'],
        'search' => ['keyword'],
    ];
    
    /**
     * 自定义验证规则：SQL注入检查
     */
    protected function checkSql($value, $rule, $data = [])
    {
        if (!$this->checkSqlInjection($value)) {
            return '输入内容包含非法字符';
        }
        return true;
    }
    
    /**
     * 验证搜索参数
     */
    public function validateSearch($data)
    {
        $rules = [
            'keyword' => 'require|length:1,100|checkSql',
        ];
        
        $messages = [
            'keyword.require' => '搜索关键词不能为空',
            'keyword.length' => '搜索关键词长度不能超过100个字符',
            'keyword.checkSql' => '搜索关键词包含非法字符',
        ];
        
        $validate = new self();
        $validate->rule($rules)->message($messages);
        
        if (!$validate->check($data)) {
            return $validate->getError();
        }
        
        return true;
    }
    
    /**
     * 验证文件上传
     */
    public function validateFileUpload($file)
    {
        if (!$file) {
            return '请选择要上传的文件';
        }
        
        // 检查文件大小（5MB限制）
        if ($file->getSize() > 5 * 1024 * 1024) {
            return '文件大小不能超过5MB';
        }
        
        // 检查文件扩展名
        if (!$this->checkFileExtension($file->getOriginalName())) {
            return '只允许上传jpg、jpeg、png、gif、webp格式的图片';
        }
        
        // 检查MIME类型
        if (!$this->checkImageMimeType($file->getMime())) {
            return '文件类型不正确，只允许上传图片文件';
        }
        
        // 检查文件名安全性
        if (!$this->checkFilename($file->getOriginalName())) {
            return '文件名包含非法字符';
        }
        
        return true;
    }
}

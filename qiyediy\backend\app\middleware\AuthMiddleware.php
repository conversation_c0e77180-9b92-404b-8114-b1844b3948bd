<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 认证中间件
 */

declare(strict_types=1);

namespace app\middleware;

use think\Request;
use think\Response;
use think\facade\Cache;
use app\model\User;

/**
 * 认证中间件
 */
class AuthMiddleware
{
    /**
     * 处理请求
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle(Request $request, \Closure $next): Response
    {
        // 获取Token
        $token = $this->getToken($request);
        
        if (!$token) {
            return $this->unauthorized('Token不能为空');
        }

        // 验证Token
        $payload = verify_jwt($token);
        if (!$payload) {
            return $this->unauthorized('Token无效');
        }

        // 检查Token是否在黑名单中
        if ($this->isTokenBlacklisted($token)) {
            return $this->unauthorized('Token已失效');
        }

        // 获取用户信息
        $user = User::find($payload['user_id']);
        if (!$user) {
            return $this->unauthorized('用户不存在');
        }

        // 检查用户状态
        if ($user->status === User::STATUS_DISABLED) {
            return $this->unauthorized('用户已被禁用');
        }

        // 将用户信息注入到请求中
        $request->user = [
            'id' => $user->id,
            'username' => $user->username,
            'email' => $user->email,
            'real_name' => $user->real_name,
            'avatar' => $user->avatar,
            'status' => $user->status,
            'is_super_admin' => $user->isSuperAdmin(),
            'permissions' => $user->getAllPermissions()
        ];

        return $next($request);
    }

    /**
     * 从请求中获取Token
     * @param Request $request
     * @return string|null
     */
    private function getToken(Request $request): ?string
    {
        // 从Header中获取
        $authorization = $request->header('Authorization');
        if ($authorization && str_starts_with($authorization, 'Bearer ')) {
            return substr($authorization, 7);
        }

        // 从GET参数中获取
        $token = $request->get('token');
        if ($token) {
            return $token;
        }

        // 从POST参数中获取
        $token = $request->post('token');
        if ($token) {
            return $token;
        }

        return null;
    }

    /**
     * 检查Token是否在黑名单中
     * @param string $token
     * @return bool
     */
    private function isTokenBlacklisted(string $token): bool
    {
        return Cache::get("blacklist_token:{$token}") === true;
    }

    /**
     * 返回未授权响应
     * @param string $message
     * @return Response
     */
    private function unauthorized(string $message): Response
    {
        return json([
            'code' => 401,
            'message' => $message,
            'data' => [],
            'timestamp' => time()
        ], 401);
    }
}

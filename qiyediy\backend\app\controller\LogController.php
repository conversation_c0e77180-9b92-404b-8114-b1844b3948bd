<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * QiyeDIY企业建站系统 - 日志控制器
 */

declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\service\LogService;
use think\Request;
use think\Response;

/**
 * 日志控制器
 */
class LogController extends BaseController
{
    protected LogService $logService;

    public function __construct()
    {
        parent::__construct();
        $this->logService = new LogService();
    }

    /**
     * 获取日志列表
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        try {
            $params = $request->only([
                'page',
                'per_page',
                'level',
                'action',
                'user_id',
                'keyword',
                'ip',
                'start_date',
                'end_date',
                'sort',
                'order'
            ]);

            $result = $this->logService->getLogs($params);

            return $this->success('获取成功', $result);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取日志统计
     * @return Response
     */
    public function stats(): Response
    {
        try {
            $stats = $this->logService->getLogStats();

            return $this->success('获取成功', $stats);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取日志详情
     * @param Request $request
     * @param string $id
     * @return Response
     */
    public function show(Request $request, string $id): Response
    {
        try {
            $log = $this->logService->getLogById($id);

            if (!$log) {
                return $this->error('日志不存在', [], 404);
            }

            return $this->success('获取成功', $log);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 创建日志
     * @param Request $request
     * @return Response
     */
    public function create(Request $request): Response
    {
        try {
            $params = $request->only([
                'level',
                'action',
                'message',
                'context',
                'user_id',
                'ip',
                'user_agent',
                'url',
                'method',
                'status_code'
            ]);

            // 参数验证
            $this->validate($params, [
                'level|日志级别' => 'require|in:info,warning,error,debug',
                'action|操作类型' => 'require|length:1,50',
                'message|日志消息' => 'require|length:1,500'
            ]);

            $log = $this->logService->createLog($params);

            return $this->success('创建成功', $log);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 批量删除日志
     * @param Request $request
     * @return Response
     */
    public function batchDelete(Request $request): Response
    {
        try {
            $ids = $request->param('ids', []);

            if (empty($ids)) {
                return $this->error('请选择要删除的日志');
            }

            $count = $this->logService->batchDeleteLogs($ids);

            return $this->success('删除成功', ['count' => $count]);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 清空日志
     * @param Request $request
     * @return Response
     */
    public function clear(Request $request): Response
    {
        try {
            $params = $request->only([
                'type',
                'level',
                'days'
            ]);

            // 参数验证
            $this->validate($params, [
                'type|清空类型' => 'require|in:all,old,level'
            ]);

            $count = $this->logService->clearLogs($params);

            return $this->success('清空成功', ['count' => $count]);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 导出日志
     * @param Request $request
     * @return Response
     */
    public function export(Request $request): Response
    {
        try {
            $params = $request->only([
                'level',
                'action',
                'user_id',
                'keyword',
                'ip',
                'start_date',
                'end_date',
                'format'
            ]);

            // 参数验证
            $this->validate($params, [
                'format|导出格式' => 'in:excel,csv,json'
            ]);

            $result = $this->logService->exportLogs($params);

            return $this->success('导出成功', $result);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取日志级别选项
     * @return Response
     */
    public function levels(): Response
    {
        try {
            $levels = [
                ['value' => 'info', 'label' => '信息'],
                ['value' => 'warning', 'label' => '警告'],
                ['value' => 'error', 'label' => '错误'],
                ['value' => 'debug', 'label' => '调试']
            ];

            return $this->success('获取成功', $levels);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取操作类型选项
     * @return Response
     */
    public function actions(): Response
    {
        try {
            $actions = [
                ['value' => 'login', 'label' => '登录'],
                ['value' => 'logout', 'label' => '登出'],
                ['value' => 'create', 'label' => '创建'],
                ['value' => 'update', 'label' => '更新'],
                ['value' => 'delete', 'label' => '删除'],
                ['value' => 'view', 'label' => '查看'],
                ['value' => 'upload', 'label' => '上传'],
                ['value' => 'download', 'label' => '下载']
            ];

            return $this->success('获取成功', $actions);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取日志趋势
     * @param Request $request
     * @return Response
     */
    public function trend(Request $request): Response
    {
        try {
            $params = $request->only([
                'period',
                'level',
                'action'
            ]);

            $trend = $this->logService->getLogTrend($params);

            return $this->success('获取成功', $trend);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取用户操作统计
     * @param Request $request
     * @return Response
     */
    public function userStats(Request $request): Response
    {
        try {
            $params = $request->only([
                'start_date',
                'end_date',
                'limit'
            ]);

            $stats = $this->logService->getUserOperationStats($params);

            return $this->success('获取成功', $stats);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取IP访问统计
     * @param Request $request
     * @return Response
     */
    public function ipStats(Request $request): Response
    {
        try {
            $params = $request->only([
                'start_date',
                'end_date',
                'limit'
            ]);

            $stats = $this->logService->getIpAccessStats($params);

            return $this->success('获取成功', $stats);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取错误日志统计
     * @param Request $request
     * @return Response
     */
    public function errorStats(Request $request): Response
    {
        try {
            $params = $request->only([
                'start_date',
                'end_date',
                'group_by'
            ]);

            $stats = $this->logService->getErrorLogStats($params);

            return $this->success('获取成功', $stats);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 日志分析
     * @param Request $request
     * @return Response
     */
    public function analyze(Request $request): Response
    {
        try {
            $params = $request->only([
                'start_date',
                'end_date',
                'type'
            ]);

            $analysis = $this->logService->analyzeLog($params);

            return $this->success('分析完成', $analysis);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取系统事件日志
     * @param Request $request
     * @return Response
     */
    public function systemEvents(Request $request): Response
    {
        try {
            $params = $request->only([
                'page',
                'per_page',
                'event_type',
                'start_date',
                'end_date'
            ]);

            $result = $this->logService->getSystemEvents($params);

            return $this->success('获取成功', $result);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取安全日志
     * @param Request $request
     * @return Response
     */
    public function securityLogs(Request $request): Response
    {
        try {
            $params = $request->only([
                'page',
                'per_page',
                'risk_level',
                'start_date',
                'end_date'
            ]);

            $result = $this->logService->getSecurityLogs($params);

            return $this->success('获取成功', $result);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 日志配置
     * @param Request $request
     * @return Response
     */
    public function config(Request $request): Response
    {
        try {
            if ($request->isPost()) {
                // 更新配置
                $config = $request->param('config', []);
                
                $this->logService->updateLogConfig($config);
                
                return $this->success('配置更新成功');
            } else {
                // 获取配置
                $config = $this->logService->getLogConfig();
                
                return $this->success('获取成功', $config);
            }

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 日志归档
     * @param Request $request
     * @return Response
     */
    public function archive(Request $request): Response
    {
        try {
            $params = $request->only([
                'start_date',
                'end_date',
                'compress'
            ]);

            $result = $this->logService->archiveLogs($params);

            return $this->success('归档成功', $result);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 恢复归档日志
     * @param Request $request
     * @param string $archiveId
     * @return Response
     */
    public function restore(Request $request, string $archiveId): Response
    {
        try {
            $result = $this->logService->restoreArchivedLogs($archiveId);

            return $this->success('恢复成功', $result);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}

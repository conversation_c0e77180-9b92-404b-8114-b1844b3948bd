/**
 * 英雄区组件
 * 全屏展示区域，包含导航栏、标题、副标题和按钮
 */

// 英雄区组件模板
const heroComponent = {
    name: '英雄区',
    html: `<div class="hero-component">
        <div class="hero-navbar">
            <div class="hero-logo">网站Logo</div>
            <div class="hero-nav-menu">
                <a href="#" class="nav-item">首页</a>
                <a href="#" class="nav-item">产品介绍</a>
                <a href="#" class="nav-item">解决方案</a>
                <a href="#" class="nav-item">客户案例</a>
                <a href="#" class="nav-item">新闻资讯</a>
                <a href="#" class="nav-item">关于我们</a>
                <a href="#" class="nav-item">联系我们</a>
            </div>
            <div class="hero-nav-buttons">
                <button class="hero-nav-btn">登录</button>
                <button class="hero-nav-btn primary">注册</button>
            </div>
        </div>
        <div class="hero-content">
            <h1>欢迎来到我们的网站</h1>
            <p>这里是英雄区域的描述文字</p>
            <div class="hero-buttons">
                <button class="hero-btn">了解更多</button>
                <button class="hero-btn secondary">联系我们</button>
            </div>
        </div>
    </div>`,
    properties: {
        // 导航栏设置
        logoType: 'text',
        logoText: '网站Logo',
        logoImage: '',
        logoFontSize: 24,
        logoColor: '#ffffff',
        navTextColor: '#ffffff',
        navHoverColor: '#ffffff',
        navItems: [
            { name: '首页', link: '#' },
            { name: '产品介绍', link: '#' },
            { name: '解决方案', link: '#' },
            { name: '客户案例', link: '#' },
            { name: '新闻资讯', link: '#' },
            { name: '关于我们', link: '#' },
            { name: '联系我们', link: '#' }
        ],
        navBtn1Text: '登录',
        navBtn1Link: '#',
        navBtn1Color: '#ffffff',
        navBtn1BorderColor: '#ffffff',
        navBtn1BorderRadius: 6,
        navBtn2Text: '注册',
        navBtn2Link: '#',
        navBtn2Color: '#ffffff',
        navBtn2BorderColor: '#5a67d8',
        navBtn2BackgroundColor: '#5a67d8',
        navBtn2BorderRadius: 6,

        // 导航栏样式设置
        navbarSticky: false,
        navbarShadow: true,

        // 内容设置
        title: '欢迎来到我们的网站',
        subtitle: '这里是英雄区域的描述文字',
        titleColor: '#ffffff',
        titleFontSize: 56,
        subtitleColor: '#ffffff',
        subtitleFontSize: 20,
        contentOffsetY: 0, // 内容垂直偏移量（px）
        contentOffsetX: 0, // 内容水平偏移量（px）
        
        // 背景设置
        bgType: 'gradient',
        bgValue: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        bgImages: [
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80',
            'https://images.unsplash.com/photo-1519904981063-b0cf448d479e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80',
            'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80',
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80',
            'https://images.unsplash.com/photo-1519904981063-b0cf448d479e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80'
        ],
        selectedBgImage: 0,
        
        // 按钮设置
        btn1Text: '了解更多',
        btn1Link: '#',
        btn1Color: '#ffffff',
        btn1TextColor: '#333333',
        btn1BorderColor: '#ffffff',
        btn1BorderRadius: 8,
        btn2Text: '联系我们',
        btn2Link: '#',
        btn2Color: '#ffffff',
        btn2TextColor: '#333333',
        btn2BorderColor: '#ffffff',
        btn2BorderRadius: 8
    }
};

// 背景样式预设（与背景组件共享）
const heroBackgroundStyles = [
    {
        name: '经典蓝紫',
        value: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        hoverColor: '#ffffff',
        accentColor: '#5a67d8',
        pattern: 'floating-circles'
    },
    {
        name: '海洋蓝',
        value: 'linear-gradient(135deg, #2196F3 0%, #21CBF3 100%)',
        hoverColor: '#ffffff',
        accentColor: '#1976d2',
        pattern: 'bubbles'
    },
    {
        name: '日落橙',
        value: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        hoverColor: '#ffffff',
        accentColor: '#e91e63',
        pattern: 'particles'
    },
    {
        name: '翡翠绿',
        value: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)',
        hoverColor: '#ffffff',
        accentColor: '#00695c',
        pattern: 'leaves'
    },
    {
        name: '暗夜红',
        value: 'linear-gradient(135deg, #000000 0%, #000000 70%, #8B0000 100%)',
        hoverColor: '#ff6b6b',
        accentColor: '#dc2626',
        pattern: 'sparks'
    },
    {
        name: '烈焰红',
        value: 'linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%)',
        hoverColor: '#ffffff',
        accentColor: '#d32f2f',
        pattern: 'flames'
    },
    {
        name: '极光紫',
        value: 'linear-gradient(135deg, #8360c3 0%, #2ebf91 100%)',
        hoverColor: '#ffffff',
        accentColor: '#673ab7',
        pattern: 'none'
    },
    {
        name: '科技蓝',
        value: 'linear-gradient(135deg, #0f4c75 0%, #3282b8 100%)',
        hoverColor: '#ffffff',
        accentColor: '#1565c0',
        pattern: 'grid'
    },
    {
        name: '卡其大地',
        value: 'linear-gradient(135deg, #d2b48c 0%, #8b7355 100%)',
        hoverColor: '#ffffff',
        accentColor: '#8b4513',
        pattern: 'windstorm'
    },
    {
        name: '科技黑',
        value: 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)',
        hoverColor: '#74b9ff',
        accentColor: '#0984e3',
        pattern: 'matrix'
    }
];

// 根据背景渐变获取对应的颜色搭配
function getColorSchemeForBackground(bgValue) {
    const matchedStyle = heroBackgroundStyles.find(style => style.value === bgValue);
    if (matchedStyle) {
        return {
            hoverColor: matchedStyle.hoverColor,
            accentColor: matchedStyle.accentColor,
            pattern: matchedStyle.pattern
        };
    }
    // 默认返回紫色渐变的颜色搭配
    return {
        hoverColor: '#8b9aff',
        accentColor: '#5a67d8',
        pattern: 'floating-circles'
    };
}

// 生成动态图案HTML
function generateDynamicPattern(patternType, componentId) {
    // 如果是none类型，不生成任何图案
    if (patternType === 'none') {
        return '';
    }

    const patterns = {
        'floating-circles': generateFloatingCircles(),
        'bubbles': generateBubbles(),
        'particles': generateParticles(),
        'leaves': generateLeaves(),
        'sparks': generateSparks(),
        'flames': generateFlames(),
        'grid': generateGrid(),
        'windstorm': generateWindstorm(),
        'matrix': generateMatrix()
    };

    return patterns[patternType] || patterns['floating-circles'];
}

// 浮动圆圈
function generateFloatingCircles() {
    let html = '';
    for (let i = 0; i < 8; i++) {
        const size = Math.random() * 60 + 20;
        const left = Math.random() * 90;
        const top = Math.random() * 90;
        const delay = Math.random() * 4;
        html += `<div class="pattern-circle" style="
            width: ${size}px;
            height: ${size}px;
            left: ${left}%;
            top: ${top}%;
            animation-delay: ${delay}s;
        "></div>`;
    }
    return html;
}

// 气泡上升
function generateBubbles() {
    let html = '';
    for (let i = 0; i < 12; i++) {
        const size = Math.random() * 40 + 20;
        const left = Math.random() * 90;
        const delay = Math.random() * 4;
        const duration = Math.random() * 3 + 4;
        html += `<div class="pattern-bubble" style="
            width: ${size}px;
            height: ${size}px;
            left: ${left}%;
            animation-delay: ${delay}s;
            animation-duration: ${duration}s;
        "></div>`;
    }
    return html;
}

// 粒子
function generateParticles() {
    let html = '';
    for (let i = 0; i < 15; i++) {
        const left = Math.random() * 100;
        const top = Math.random() * 100;
        const delay = Math.random() * 3;
        html += `<div class="pattern-particle" style="
            left: ${left}%;
            top: ${top}%;
            animation-delay: ${delay}s;
        "></div>`;
    }
    return html;
}

// 叶子
function generateLeaves() {
    let html = '';
    for (let i = 0; i < 6; i++) {
        const left = Math.random() * 90;
        const delay = Math.random() * 4;
        html += `<div class="pattern-leaf" style="
            left: ${left}%;
            animation-delay: ${delay}s;
        "></div>`;
    }
    return html;
}

// 火花
function generateSparks() {
    let html = '';
    for (let i = 0; i < 12; i++) {
        const left = Math.random() * 100;
        const top = Math.random() * 100;
        const delay = Math.random() * 2;
        html += `<div class="pattern-spark" style="
            left: ${left}%;
            top: ${top}%;
            animation-delay: ${delay}s;
        "></div>`;
    }
    return html;
}

// 火焰
function generateFlames() {
    let html = '';
    for (let i = 0; i < 8; i++) {
        const left = Math.random() * 90;
        const delay = Math.random() * 3;
        html += `<div class="pattern-flame" style="
            left: ${left}%;
            animation-delay: ${delay}s;
        "></div>`;
    }
    return html;
}



// 网格
function generateGrid() {
    let html = '';
    for (let i = 0; i < 10; i++) {
        const left = Math.random() * 90;
        const top = Math.random() * 90;
        const delay = Math.random() * 4;
        html += `<div class="pattern-grid-dot" style="
            left: ${left}%;
            top: ${top}%;
            animation-delay: ${delay}s;
        "></div>`;
    }
    return html;
}

// 风沙飞舞
function generateWindstorm() {
    let html = '';
    for (let i = 0; i < 25; i++) {
        const left = Math.random() * 100;
        const top = Math.random() * 100;
        const delay = Math.random() * 4;
        const size = Math.random() * 3 + 2;
        html += `<div class="pattern-windstorm" style="
            left: ${left}%;
            top: ${top}%;
            width: ${size}px;
            height: ${size}px;
            animation-delay: ${delay}s;
        "></div>`;
    }
    return html;
}

// 矩阵
function generateMatrix() {
    let html = '';
    for (let i = 0; i < 8; i++) {
        const left = Math.random() * 90;
        const delay = Math.random() * 3;
        html += `<div class="pattern-matrix" style="
            left: ${left}%;
            animation-delay: ${delay}s;
        "></div>`;
    }
    return html;
}

// 添加图案动画样式
function addPatternStyles(patternType, componentId) {
    // 移除旧的样式
    const oldStyle = document.getElementById(`pattern-style-${componentId}`);
    if (oldStyle) {
        oldStyle.remove();
    }

    // 创建新的样式
    const style = document.createElement('style');
    style.id = `pattern-style-${componentId}`;

    const animations = {
        'floating-circles': `
            #${componentId} .pattern-circle {
                position: absolute;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 50%;
                animation: float 6s ease-in-out infinite;
            }
            @keyframes float {
                0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
                50% { transform: translateY(-20px) rotate(180deg); opacity: 0.8; }
            }
        `,
        'bubbles': `
            #${componentId} .pattern-bubble {
                position: absolute;
                bottom: -50px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 50%;
                animation: bubble-rise 6s linear infinite;
            }
            @keyframes bubble-rise {
                0% { transform: translateY(0) scale(0.5); opacity: 0.8; }
                50% { transform: translateY(-50vh) scale(1); opacity: 0.6; }
                100% { transform: translateY(-100vh) scale(0.3); opacity: 0; }
            }
        `,
        'particles': `
            #${componentId} .pattern-particle {
                position: absolute;
                width: 4px;
                height: 4px;
                background: rgba(255, 255, 255, 0.6);
                border-radius: 50%;
                animation: particle 3s linear infinite;
            }
            @keyframes particle {
                0% { transform: translateY(0) scale(1); opacity: 1; }
                100% { transform: translateY(-100px) scale(0); opacity: 0; }
            }
        `,
        'leaves': `
            #${componentId} .pattern-leaf {
                position: absolute;
                top: -20px;
                width: 20px;
                height: 20px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 0 100% 0 100%;
                animation: leaf 8s linear infinite;
            }
            @keyframes leaf {
                0% { transform: translateY(-20px) rotate(0deg); opacity: 1; }
                100% { transform: translateY(100vh) rotate(360deg); opacity: 0; }
            }
        `,
        'sparks': `
            #${componentId} .pattern-spark {
                position: absolute;
                width: 3px;
                height: 3px;
                background: #ff6b6b;
                border-radius: 50%;
                animation: spark 2s ease-out infinite;
            }
            @keyframes spark {
                0% { transform: scale(0) rotate(0deg); opacity: 1; }
                50% { transform: scale(1) rotate(180deg); opacity: 0.8; }
                100% { transform: scale(0) rotate(360deg); opacity: 0; }
            }
        `,
        'flames': `
            #${componentId} .pattern-flame {
                position: absolute;
                bottom: 0;
                width: 20px;
                height: 40px;
                background: linear-gradient(to top, #ff4b2b, #ff416c, transparent);
                border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
                animation: flame 2s ease-in-out infinite;
            }
            @keyframes flame {
                0%, 100% { transform: scaleY(1) scaleX(1); }
                50% { transform: scaleY(1.2) scaleX(0.8); }
            }
        `,

        'grid': `
            #${componentId} .pattern-grid-dot {
                position: absolute;
                width: 6px;
                height: 6px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                animation: grid 4s ease-in-out infinite;
            }
            @keyframes grid {
                0%, 100% { opacity: 0.3; transform: scale(1); }
                50% { opacity: 1; transform: scale(1.5); }
            }
        `,
        'windstorm': `
            #${componentId} .pattern-windstorm {
                position: absolute;
                background: rgba(210, 180, 140, 0.6);
                border-radius: 50%;
                animation: windstorm-pattern 5s ease-in-out infinite;
            }
            @keyframes windstorm-pattern {
                0% { transform: translateX(0) translateY(0) rotate(0deg); opacity: 0.3; }
                25% { transform: translateX(30px) translateY(-20px) rotate(90deg); opacity: 0.8; }
                50% { transform: translateX(-20px) translateY(15px) rotate(180deg); opacity: 0.6; }
                75% { transform: translateX(40px) translateY(-10px) rotate(270deg); opacity: 0.9; }
                100% { transform: translateX(0) translateY(0) rotate(360deg); opacity: 0.3; }
            }
        `,
        'matrix': `
            #${componentId} .pattern-matrix {
                position: absolute;
                top: -20px;
                width: 2px;
                height: 20px;
                background: linear-gradient(to bottom, #00ff41, transparent);
                animation: matrix 3s linear infinite;
            }
            @keyframes matrix {
                0% { transform: translateY(-20px); opacity: 1; }
                100% { transform: translateY(100vh); opacity: 0; }
            }
        `
    };

    style.textContent = animations[patternType] || animations['floating-circles'];
    document.head.appendChild(style);
}

// 生成英雄区组件属性面板
function generateHeroProperties(component) {
    const props = heroComponent.properties;

    let html = `
        <!-- 导航栏设置 -->
        <div class="property-section">
            <h4 class="section-title">导航栏设置</h4>

            <div class="property-group">
                <label class="property-label">Logo类型</label>
                <select class="property-input" onchange="updateHeroProperty('${component.id}', 'logoType', this.value)">
                    <option value="text" ${props.logoType === 'text' ? 'selected' : ''}>文字Logo</option>
                    <option value="image" ${props.logoType === 'image' ? 'selected' : ''}>图片Logo</option>
                </select>
            </div>

            <div class="property-group">
                <label class="property-label">${props.logoType === 'text' ? 'Logo文字' : 'Logo图片URL'}</label>
                <input type="text" class="property-input"
                       value="${props.logoType === 'text' ? props.logoText : props.logoImage}"
                       placeholder="${props.logoType === 'text' ? '请输入Logo文字' : '请输入图片URL地址'}"
                       onchange="updateHeroProperty('${component.id}', '${props.logoType === 'text' ? 'logoText' : 'logoImage'}', this.value)">
            </div>

            ${props.logoType === 'text' ? `
            <div class="property-group">
                <div class="color-row">
                    <div class="color-item">
                        <label>Logo颜色</label>
                        <input type="color" class="property-input" value="${props.logoColor}"
                               onchange="updateHeroProperty('${component.id}', 'logoColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>字体大小</label>
                        <input type="number" class="property-input" value="${props.logoFontSize}" min="12" max="48"
                               onchange="updateHeroProperty('${component.id}', 'logoFontSize', this.value)">
                    </div>
                </div>
            </div>
            ` : ''}

            <div class="property-group">
                <label class="property-label">导航菜单项</label>
                <div id="nav-items-${component.id}">
                    ${props.navItems.map((item, index) => `
                        <div class="menu-item-row">
                            <div class="menu-item-inputs">
                                <input type="text" class="property-input" value="${item.name}" placeholder="菜单名称"
                                       onchange="updateNavItem('${component.id}', ${index}, 'name', this.value)">
                                <input type="text" class="property-input" value="${item.link}" placeholder="链接地址"
                                       onchange="updateNavItem('${component.id}', ${index}, 'link', this.value)">
                                <button onclick="removeNavItem('${component.id}', ${index})" class="delete-btn">删除</button>
                            </div>
                        </div>
                    `).join('')}
                </div>
                <button onclick="addNavItem('${component.id}')" class="add-btn">添加菜单项</button>
            </div>

            <div class="property-group">
                <label class="property-label">菜单颜色设置</label>
                <div class="color-row">
                    <div class="color-item">
                        <label>菜单文字颜色</label>
                        <input type="color" class="property-input" value="${props.navTextColor}"
                               onchange="updateHeroProperty('${component.id}', 'navTextColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>悬停颜色</label>
                        <input type="color" class="property-input" value="${props.navHoverColor}"
                               onchange="updateHeroProperty('${component.id}', 'navHoverColor', this.value)">
                    </div>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">导航栏样式</label>
                <div class="checkbox-group">
                    <label class="checkbox-item">
                        <input type="checkbox" ${props.navbarShadow ? 'checked' : ''}
                               onchange="updateHeroProperty('${component.id}', 'navbarShadow', this.checked)">
                        阴影效果
                    </label>
                    <label class="checkbox-item">
                        <input type="checkbox" ${props.navbarSticky ? 'checked' : ''}
                               onchange="updateHeroProperty('${component.id}', 'navbarSticky', this.checked)">
                        固定顶部
                    </label>
                </div>
            </div>
        </div>

        <div class="property-group nav-button-group">
            <label class="property-label">登录按钮</label>
            <div class="input-group">
                <input type="text" class="property-input" value="${props.navBtn1Text}" placeholder="按钮文字"
                       onchange="updateHeroProperty('${component.id}', 'navBtn1Text', this.value)">
            </div>
            <div class="input-group">
                <input type="text" class="property-input" value="${props.navBtn1Link}" placeholder="按钮链接"
                       onchange="updateHeroProperty('${component.id}', 'navBtn1Link', this.value)">
            </div>
            <div class="color-row">
                <div class="color-item">
                    <label>文字颜色</label>
                    <input type="color" class="property-input" value="${props.navBtn1Color}"
                           onchange="updateHeroProperty('${component.id}', 'navBtn1Color', this.value)">
                </div>
                <div class="color-item">
                    <label>边框颜色</label>
                    <input type="color" class="property-input" value="${props.navBtn1BorderColor}"
                           onchange="updateHeroProperty('${component.id}', 'navBtn1BorderColor', this.value)">
                </div>
                <div class="color-item">
                    <label>圆角大小</label>
                    <input type="number" class="property-input" value="${props.navBtn1BorderRadius}" min="0" max="50"
                           onchange="updateHeroProperty('${component.id}', 'navBtn1BorderRadius', this.value)">
                </div>
            </div>
        </div>

        <div class="property-group nav-button-group">
            <label class="property-label">注册按钮</label>
            <div class="input-group">
                <input type="text" class="property-input" value="${props.navBtn2Text}" placeholder="按钮文字"
                       onchange="updateHeroProperty('${component.id}', 'navBtn2Text', this.value)">
            </div>
            <div class="input-group">
                <input type="text" class="property-input" value="${props.navBtn2Link}" placeholder="按钮链接"
                       onchange="updateHeroProperty('${component.id}', 'navBtn2Link', this.value)">
            </div>
            <div class="color-row">
                <div class="color-item">
                    <label>文字颜色</label>
                    <input type="color" class="property-input" value="${props.navBtn2Color}"
                           onchange="updateHeroProperty('${component.id}', 'navBtn2Color', this.value)">
                </div>
                <div class="color-item">
                    <label>边框颜色</label>
                    <input type="color" class="property-input" value="${props.navBtn2BorderColor}"
                           onchange="updateHeroProperty('${component.id}', 'navBtn2BorderColor', this.value)">
                </div>
                <div class="color-item">
                    <label>圆角大小</label>
                    <input type="number" class="property-input" value="${props.navBtn2BorderRadius}" min="0" max="50"
                           onchange="updateHeroProperty('${component.id}', 'navBtn2BorderRadius', this.value)">
                </div>
            </div>
        </div>

        <!-- 内容设置 -->
        <div class="property-section">
            <h4 class="section-title">内容设置</h4>

            <div class="property-group">
                <label class="property-label">主标题</label>
                <input type="text" class="property-input" value="${props.title}"
                       placeholder="请输入主标题内容"
                       onchange="updateHeroProperty('${component.id}', 'title', this.value)">
                <div class="color-row">
                    <div class="color-item">
                        <label>标题颜色</label>
                        <input type="color" class="property-input" value="${props.titleColor}"
                               onchange="updateHeroProperty('${component.id}', 'titleColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>字体大小</label>
                        <input type="number" class="property-input" value="${props.titleFontSize}" min="24" max="80"
                               onchange="updateHeroProperty('${component.id}', 'titleFontSize', this.value)">
                    </div>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">副标题</label>
                <textarea class="property-input" rows="3"
                          placeholder="请输入副标题描述内容"
                          onchange="updateHeroProperty('${component.id}', 'subtitle', this.value)">${props.subtitle}</textarea>
                <div class="color-row">
                    <div class="color-item">
                        <label>副标题颜色</label>
                        <input type="color" class="property-input" value="${props.subtitleColor}"
                               onchange="updateHeroProperty('${component.id}', 'subtitleColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>字体大小</label>
                        <input type="number" class="property-input" value="${props.subtitleFontSize}" min="12" max="32"
                               onchange="updateHeroProperty('${component.id}', 'subtitleFontSize', this.value)">
                    </div>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">上下移动</label>
                <div class="range-wrapper">
                    <div class="range-control">
                        <input type="range" class="range-slider" min="-200" max="200" value="${props.contentOffsetY}"
                               oninput="updateHeroOffsetDisplay('${component.id}', 'Y', this.value)"
                               onchange="updateHeroProperty('${component.id}', 'contentOffsetY', parseInt(this.value))">
                        <span class="range-value" id="offsetY-${component.id}">${props.contentOffsetY}px</span>
                    </div>
                    <div class="range-hint">负值向上，正值向下</div>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">左右移动</label>
                <div class="range-wrapper">
                    <div class="range-control">
                        <input type="range" class="range-slider" min="-300" max="300" value="${props.contentOffsetX}"
                               oninput="updateHeroOffsetDisplay('${component.id}', 'X', this.value)"
                               onchange="updateHeroProperty('${component.id}', 'contentOffsetX', parseInt(this.value))">
                        <span class="range-value" id="offsetX-${component.id}">${props.contentOffsetX}px</span>
                    </div>
                    <div class="range-hint">负值向左，正值向右</div>
                </div>
            </div>
        </div>



        <!-- 背景设置 -->
        <div class="property-group">
            <h4 style="margin: 20px 0 10px 0; color: #667eea;">背景设置</h4>
            <label class="property-label">背景类型</label>
            <select class="property-input" onchange="changeHeroBgType('${component.id}', this.value)">
                <option value="gradient" ${props.bgType === 'gradient' ? 'selected' : ''}>渐变背景</option>
                <option value="image" ${props.bgType === 'image' ? 'selected' : ''}>图片背景</option>
            </select>
        </div>
    `;

    if (props.bgType === 'gradient') {
        html += `
            <div class="property-group">
                <label class="property-label">预设渐变样式</label>
                <div class="bg-options">
                    ${heroBackgroundStyles.map((style, index) => `
                        <div class="bg-option ${props.bgValue === style.value ? 'selected' : ''}"
                             style="background: ${style.value}"
                             onclick="updateHeroBgValue('${component.id}', \`${style.value}\`)"
                             title="${style.name}">
                        </div>
                    `).join('')}
                </div>
                <div class="bg-preview" style="background: ${props.bgValue}"></div>
            </div>
        `;
    } else {
        html += `
            <div class="property-group">
                <label class="property-label">背景图片选择</label>
                <div class="bg-options">
                    ${props.bgImages.map((image, index) => `
                        <div class="bg-option ${props.selectedBgImage === index ? 'selected' : ''}"
                             style="background: url('${image}') center/cover"
                             onclick="updateHeroBgImage('${component.id}', ${index})"
                             title="背景图片 ${index + 1}">
                        </div>
                    `).join('')}
                </div>
                <div class="bg-preview" style="background: url('${props.bgImages[props.selectedBgImage]}') center/cover"></div>
            </div>
        `;
    }

    // 按钮设置
    html += `
        <div class="property-section">
            <h4 class="section-title">按钮设置</h4>

            <div class="property-group button-group">
                <label class="property-label">主要按钮</label>
                <div class="input-group">
                    <input type="text" class="property-input" value="${props.btn1Text}" placeholder="按钮文字"
                           onchange="updateHeroProperty('${component.id}', 'btn1Text', this.value)">
                </div>
                <div class="input-group">
                    <input type="text" class="property-input" value="${props.btn1Link}" placeholder="按钮链接"
                           onchange="updateHeroProperty('${component.id}', 'btn1Link', this.value)">
                </div>
                <div class="color-row">
                    <div class="color-item">
                        <label>文字颜色</label>
                        <input type="color" class="property-input" value="${props.btn1TextColor}"
                               onchange="updateHeroProperty('${component.id}', 'btn1TextColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>背景颜色</label>
                        <input type="color" class="property-input" value="${props.btn1Color}"
                               onchange="updateHeroBtnColorAndBorder('${component.id}', 'btn1', this.value)">
                    </div>
                    <div class="color-item">
                        <label>圆角大小</label>
                        <input type="number" class="property-input" value="${props.btn1BorderRadius}" min="0" max="50"
                               onchange="updateHeroProperty('${component.id}', 'btn1BorderRadius', this.value)">
                    </div>
                </div>
            </div>

            <div class="property-group button-group">
                <label class="property-label">次要按钮</label>
                <div class="input-group">
                    <input type="text" class="property-input" value="${props.btn2Text}" placeholder="按钮文字"
                           onchange="updateHeroProperty('${component.id}', 'btn2Text', this.value)">
                </div>
                <div class="input-group">
                    <input type="text" class="property-input" value="${props.btn2Link}" placeholder="按钮链接"
                           onchange="updateHeroProperty('${component.id}', 'btn2Link', this.value)">
                </div>
                <div class="color-row">
                    <div class="color-item">
                        <label>文字颜色</label>
                        <input type="color" class="property-input" value="${props.btn2TextColor}"
                               onchange="updateHeroProperty('${component.id}', 'btn2TextColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>背景颜色</label>
                        <input type="color" class="property-input" value="${props.btn2Color}"
                               onchange="updateHeroBtnColorAndBorder('${component.id}', 'btn2', this.value)">
                    </div>
                    <div class="color-item">
                        <label>圆角大小</label>
                        <input type="number" class="property-input" value="${props.btn2BorderRadius}" min="0" max="50"
                               onchange="updateHeroProperty('${component.id}', 'btn2BorderRadius', this.value)">
                    </div>
                </div>
            </div>
        </div>
    `;

    return html;
}

// 更新英雄区显示
function updateHeroDisplay(component, props) {
    const contentDiv = component.querySelector('.hero-component');
    if (!contentDiv) return;

    // 更新背景（包括动态图案）
    updateHeroBackground(component, props);

    // 更新导航栏
    const navbar = contentDiv.querySelector('.hero-navbar');
    if (navbar) {
        const logo = navbar.querySelector('.hero-logo');
        if (props.logoType === 'text') {
            logo.innerHTML = props.logoText;
            logo.style.fontSize = props.logoFontSize + 'px';
            logo.style.color = props.logoColor;
            logo.style.fontWeight = 'bold';
        } else {
            logo.innerHTML = `<img src="${props.logoImage}" alt="Logo" style="height: ${props.logoFontSize + 16}px; width: auto;">`;
            logo.style.fontSize = '';
            logo.style.color = '';
            logo.style.fontWeight = '';
        }

        // 更新导航菜单
        const navMenu = navbar.querySelector('.hero-nav-menu');
        navMenu.innerHTML = props.navItems.map(item =>
            `<a href="${item.link}" class="nav-item" style="color: ${props.navTextColor};">${item.name}</a>`
        ).join('');

        // 添加菜单项悬停效果
        updateHeroNavHoverStyles(component.id, props);

        // 更新导航按钮
        const navButtons = navbar.querySelector('.hero-nav-buttons');
        navButtons.innerHTML = `
            <button class="hero-nav-btn" style="
                color: ${props.navBtn1Color};
                border-color: ${props.navBtn1BorderColor};
                border-radius: ${props.navBtn1BorderRadius}px;
                background-color: transparent;
            ">${props.navBtn1Text}</button>
            <button class="hero-nav-btn primary" style="
                color: ${props.navBtn2Color};
                border-color: ${props.navBtn2BorderColor};
                background-color: ${props.navBtn2BackgroundColor};
                border-radius: ${props.navBtn2BorderRadius}px;
            ">${props.navBtn2Text}</button>
        `;

        // 添加导航按钮悬停效果
        updateHeroNavButtonHoverStyles(component.id, props);

        // 更新导航栏样式
        navbar.style.position = props.navbarSticky ? 'sticky' : 'relative';
        navbar.style.top = props.navbarSticky ? '0' : 'auto';
        navbar.style.zIndex = props.navbarSticky ? '1000' : '1';
        navbar.style.boxShadow = props.navbarShadow ? '0 2px 10px rgba(0,0,0,0.1)' : 'none';
    }

    // 更新内容区 - 调用专门的内容更新函数
    updateHeroContent(component, props);
}

// 英雄区背景类型切换
function changeHeroBgType(componentId, type) {
    const component = document.getElementById(componentId);
    const props = heroComponent.properties;

    // 更新背景类型
    props.bgType = type;

    // 根据类型重置背景值
    if (type === 'image') {
        if (props.bgImages && props.bgImages.length > 0) {
            props.bgValue = props.bgImages[props.selectedBgImage || 0];
        } else {
            // 如果没有图片，回退到渐变
            props.bgType = 'gradient';
            props.bgValue = heroBackgroundStyles[0].value;
        }
    } else {
        // 切换到渐变背景时，检查当前bgValue是否是有效的渐变值
        // 如果是图片URL或者无效值，则重置为默认渐变
        if (!props.bgValue ||
            props.bgValue.startsWith('url(') ||
            props.bgValue.startsWith('http') ||
            !props.bgValue.includes('gradient')) {
            props.bgValue = heroBackgroundStyles[0].value;
        }
    }

    // 只更新背景，不影响按钮
    updateHeroBackground(component, props);

    // 重新生成属性面板
    updatePropertiesPanel(component);
}

// 更新英雄区属性
function updateHeroProperty(componentId, property, value) {
    const component = document.getElementById(componentId);
    const props = heroComponent.properties;

    // 更新属性值
    if (property === 'selectedBgImage' || property === 'contentOffsetY' || property === 'contentOffsetX' ||
        property === 'logoFontSize' || property === 'titleFontSize' || property === 'subtitleFontSize') {
        props[property] = parseInt(value);
    } else if (typeof value === 'boolean') {
        props[property] = value;
    } else {
        props[property] = value;
    }

    // 根据属性类型选择更新方式
    if (property === 'bgValue' || property === 'selectedBgImage' || property === 'bgType') {
        // 只更新背景，不影响按钮
        updateHeroBackground(component, props);
    } else if (property === 'navbarSticky' || property === 'navbarShadow') {
        // 导航栏样式属性，只更新导航栏样式
        updateHeroNavbarStyle(component, props);
    } else if (property.includes('navBtn') || property === 'logoType' || property === 'logoText' || property === 'logoImage' ||
               property === 'logoFontSize' || property === 'logoColor' || property === 'navTextColor' || property === 'navHoverColor') {
        // 导航相关属性，重新渲染整个英雄区
        updateHeroDisplay(component, props);
    } else if (property === 'contentOffsetY' || property === 'contentOffsetX') {
        // 只更新内容位置，不影响按钮
        updateHeroContentPosition(component, props);
    } else if (property === 'title' || property === 'subtitle' || property.includes('btn') ||
               property === 'titleColor' || property === 'titleFontSize' || property === 'subtitleColor' || property === 'subtitleFontSize') {
        // 内容相关属性，只更新内容部分
        updateHeroContent(component, props);
    } else {
        // 其他属性，重新渲染整个英雄区
        updateHeroDisplay(component, props);
    }

    // 如果是Logo类型改变或背景值改变，需要重新生成属性面板
    if (property === 'logoType' || property === 'bgValue' || property === 'selectedBgImage') {
        updatePropertiesPanel(component);
    }
}

// 单独更新英雄区背景
function updateHeroBackground(component, props) {
    const contentDiv = component.querySelector('.hero-component');
    if (!contentDiv) return;

    // 移除旧的动态图案
    const oldPattern = contentDiv.querySelector('.dynamic-pattern');
    if (oldPattern) {
        oldPattern.remove();
    }

    // 更新背景 - 确保背景始终存在
    if (props.bgType === 'gradient') {
        // 渐变背景模式 - 清除图片背景属性，设置渐变背景
        contentDiv.style.backgroundImage = '';
        contentDiv.style.backgroundSize = '';
        contentDiv.style.backgroundPosition = '';
        contentDiv.style.backgroundRepeat = '';
        contentDiv.style.background = props.bgValue || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';

        // 添加动态图案
        const colorScheme = getColorSchemeForBackground(props.bgValue);
        addDynamicPattern(contentDiv, colorScheme.pattern, component.id);
    } else {
        // 图片背景模式
        if (props.bgImages && props.selectedBgImage !== undefined && props.bgImages[props.selectedBgImage]) {
            contentDiv.style.background = '';  // 清除渐变背景
            contentDiv.style.backgroundImage = `url('${props.bgImages[props.selectedBgImage]}')`;
            contentDiv.style.backgroundSize = 'cover';
            contentDiv.style.backgroundPosition = 'center';
            contentDiv.style.backgroundRepeat = 'no-repeat';
        } else {
            // 如果没有图片，回退到渐变
            contentDiv.style.backgroundImage = '';
            contentDiv.style.backgroundSize = '';
            contentDiv.style.backgroundPosition = '';
            contentDiv.style.backgroundRepeat = '';
            contentDiv.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';

            // 添加默认动态图案
            addDynamicPattern(contentDiv, 'floating-circles', component.id);
        }
    }
}

// 添加动态图案到背景
function addDynamicPattern(contentDiv, patternType, componentId) {
    // 创建动态图案容器
    const patternContainer = document.createElement('div');
    patternContainer.className = 'dynamic-pattern';
    patternContainer.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        z-index: 1;
        overflow: hidden;
    `;

    // 生成图案HTML
    patternContainer.innerHTML = generateDynamicPattern(patternType, componentId);

    // 添加到内容区
    contentDiv.appendChild(patternContainer);

    // 添加对应的CSS动画样式
    addPatternStyles(patternType, componentId);
}

// 单独更新英雄区内容
function updateHeroContent(component, props) {
    const heroContent = component.querySelector('.hero-content');
    if (!heroContent) return;

    // 更新标题和副标题
    const titleElement = heroContent.querySelector('h1');
    const subtitleElement = heroContent.querySelector('p');

    if (titleElement) {
        titleElement.textContent = props.title;
        titleElement.style.color = props.titleColor;
        titleElement.style.fontSize = props.titleFontSize + 'px';
    }
    if (subtitleElement) {
        subtitleElement.textContent = props.subtitle;
        subtitleElement.style.color = props.subtitleColor;
        subtitleElement.style.fontSize = props.subtitleFontSize + 'px';
    }

    // 应用内容位置偏移
    heroContent.style.transform = `translate(${props.contentOffsetX}px, ${props.contentOffsetY}px)`;

    // 更新按钮
    const heroButtons = heroContent.querySelector('.hero-buttons');
    if (heroButtons) {
        heroButtons.innerHTML = `
            <button class="hero-btn" style="
                background: ${props.btn1Color};
                color: ${props.btn1TextColor};
                border-color: ${props.btn1Color};
                border-radius: ${props.btn1BorderRadius}px;
            ">${props.btn1Text}</button>
            <button class="hero-btn secondary" style="
                background: ${props.btn2Color};
                color: ${props.btn2TextColor};
                border-color: ${props.btn2Color};
                border-radius: ${props.btn2BorderRadius}px;
            ">${props.btn2Text}</button>
        `;

        // 添加按钮悬停效果
        updateHeroButtonHoverStyles(component.id, props);
    }
}

// 专门更新英雄区背景值（用于渐变样式点击）
function updateHeroBgValue(componentId, bgValue) {
    const component = document.getElementById(componentId);
    const props = heroComponent.properties;

    // 更新背景值
    props.bgValue = bgValue;

    // 根据新背景获取对应的颜色搭配
    const colorScheme = getColorSchemeForBackground(bgValue);

    // 自动更新悬停颜色和注册按钮背景色
    props.navHoverColor = colorScheme.hoverColor;
    props.navBtn2BackgroundColor = colorScheme.accentColor;
    props.navBtn2BorderColor = colorScheme.accentColor;

    // 更新整个英雄区显示（包括颜色变化）
    updateHeroDisplay(component, props);

    // 更新属性面板中的选中状态
    updatePropertiesPanel(component);
}

// 专门更新英雄区背景图片（用于图片背景切换）
function updateHeroBgImage(componentId, imageIndex) {
    const component = document.getElementById(componentId);
    const props = heroComponent.properties;

    // 更新选中的背景图片索引
    props.selectedBgImage = imageIndex;

    // 确保背景类型为图片
    props.bgType = 'image';

    // 只更新背景，不影响按钮
    updateHeroBackground(component, props);

    // 更新属性面板中的选中状态
    updatePropertiesPanel(component);
}

// 更新英雄区按钮颜色（带透明度）
function updateHeroBtnColor(componentId, property, color) {
    const component = document.getElementById(componentId);
    const props = heroComponent.properties;

    // 将颜色转换为带透明度的rgba格式
    const rgba = hexToRgba(color, 0.9);
    props[property] = rgba;

    updateHeroDisplay(component, props);
}

// 更新英雄区按钮颜色和边框颜色（保持一致）
function updateHeroBtnColorAndBorder(componentId, btnType, color) {
    const component = document.getElementById(componentId);
    const props = heroComponent.properties;

    // 直接使用颜色值，不添加透明度
    props[btnType + 'Color'] = color;

    updateHeroDisplay(component, props);
}

// 颜色转换函数
function hexToRgba(hex, alpha) {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

// 导航菜单项管理
function updateNavItem(componentId, index, field, value) {
    const props = heroComponent.properties;
    props.navItems[index][field] = value;

    const component = document.getElementById(componentId);
    updateHeroNavigation(component, props);
}

function addNavItem(componentId) {
    const props = heroComponent.properties;
    props.navItems.push({ name: '新菜单', link: '#' });

    const component = document.getElementById(componentId);
    updatePropertiesPanel(component);
    updateHeroNavigation(component, props);
}

function removeNavItem(componentId, index) {
    const props = heroComponent.properties;
    props.navItems.splice(index, 1);

    const component = document.getElementById(componentId);

    // 只更新导航菜单部分，不重新渲染整个英雄区
    updateHeroNavigation(component, props);

    // 重新生成属性面板（导航项列表）
    updatePropertiesPanel(component);
}

// 单独更新英雄区导航部分
function updateHeroNavigation(component, props) {
    const heroNavMenu = component.querySelector('.hero-nav-menu');
    if (!heroNavMenu) return;

    // 只更新导航菜单项，不影响其他部分
    heroNavMenu.innerHTML = props.navItems.map(item =>
        `<a href="${item.link}" class="nav-item" style="color: ${props.navTextColor};">${item.name}</a>`
    ).join('');

    // 添加菜单项悬停效果
    updateHeroNavHoverStyles(component.id, props);
}

// 单独更新英雄区导航栏样式
function updateHeroNavbarStyle(component, props) {
    const navbar = component.querySelector('.hero-navbar');
    if (!navbar) return;

    // 只更新导航栏样式，不影响其他内容
    navbar.style.position = props.navbarSticky ? 'sticky' : 'relative';
    navbar.style.top = props.navbarSticky ? '0' : 'auto';
    navbar.style.zIndex = props.navbarSticky ? '1000' : '1';
    navbar.style.boxShadow = props.navbarShadow ? '0 2px 10px rgba(0,0,0,0.1)' : 'none';
}

// 单独更新英雄区内容位置
function updateHeroContentPosition(component, props) {
    const heroContent = component.querySelector('.hero-content');
    if (!heroContent) return;

    // 只更新内容位置，不影响其他内容
    heroContent.style.transform = `translate(${props.contentOffsetX}px, ${props.contentOffsetY}px)`;
}

// 实时更新偏移量显示
function updateHeroOffsetDisplay(componentId, axis, value) {
    const displayElement = document.getElementById(`offset${axis}-${componentId}`);
    if (displayElement) {
        displayElement.textContent = `${value}px`;
    }
}

// 添加英雄区导航菜单悬停样式
function updateHeroNavHoverStyles(componentId, props) {
    // 移除旧的样式
    const oldStyle = document.getElementById(`hero-nav-hover-style-${componentId}`);
    if (oldStyle) {
        oldStyle.remove();
    }

    // 创建新的样式
    const style = document.createElement('style');
    style.id = `hero-nav-hover-style-${componentId}`;
    style.textContent = `
        #${componentId} .nav-item {
            transition: all 0.2s ease !important;
            border-radius: 6px !important;
            padding: 8px 16px !important;
            margin: 0 4px !important;
            border: 2px solid transparent !important;
            position: relative !important;
            overflow: hidden !important;
        }
        #${componentId} .nav-item::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: -100% !important;
            width: 100% !important;
            height: 100% !important;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent) !important;
            transition: left 0.6s ease !important;
            z-index: 1 !important;
            pointer-events: none !important;
        }
        #${componentId} .nav-item:hover {
            color: ${props.navHoverColor} !important;
            border: 2px solid ${props.navHoverColor} !important;
            background: rgba(${hexToRgb(props.navHoverColor)}, 0.1) !important;
        }
        #${componentId} .nav-item:hover::before {
            left: 100% !important;
        }

        /* 编辑器环境特殊处理 */
        div#canvas #${componentId} .nav-item::before,
        .canvas #${componentId} .nav-item::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: -100% !important;
            width: 100% !important;
            height: 100% !important;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent) !important;
            transition: left 0.6s ease !important;
            z-index: 999 !important;
            pointer-events: none !important;
            display: block !important;
        }

        div#canvas #${componentId} .nav-item:hover::before,
        .canvas #${componentId} .nav-item:hover::before {
            left: 100% !important;
        }
    `;

    document.head.appendChild(style);

    // 调试信息
    console.log(`应用导航悬停样式: ${componentId}`);
}

// 辅助函数：将十六进制颜色转换为RGB
function hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ?
        parseInt(result[1], 16) + ',' + parseInt(result[2], 16) + ',' + parseInt(result[3], 16) :
        '102, 126, 234';
}

// 更新英雄区按钮悬停样式
function updateHeroButtonHoverStyles(componentId, props) {
    // 移除旧的样式
    const oldStyle = document.getElementById(`hero-btn-hover-style-${componentId}`);
    if (oldStyle) {
        oldStyle.remove();
    }

    // 创建新的悬停样式
    const style = document.createElement('style');
    style.id = `hero-btn-hover-style-${componentId}`;
    style.textContent = `
        #${componentId} .hero-btn {
            transition: all 0.2s ease !important;
            border: 2px solid transparent !important;
            position: relative !important;
            overflow: hidden !important;
        }
        #${componentId} .hero-btn::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: -100% !important;
            width: 100% !important;
            height: 100% !important;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent) !important;
            transition: left 0.6s ease !important;
            z-index: 1 !important;
            pointer-events: none !important;
        }
        #${componentId} .hero-btn:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
            filter: brightness(1.05) !important;
        }
        #${componentId} .hero-btn:hover::before {
            left: 100% !important;
        }
        #${componentId} .hero-btn:not(.secondary):hover {
            background-color: ${props.btn1Color} !important;
            border: 2px solid ${props.btn1Color} !important;
        }
        #${componentId} .hero-btn.secondary:hover {
            background-color: ${props.btn2Color} !important;
            border: 2px solid ${props.btn2Color} !important;
        }

        /* 编辑器环境特殊处理 */
        div#canvas #${componentId} .hero-btn::before,
        .canvas #${componentId} .hero-btn::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: -100% !important;
            width: 100% !important;
            height: 100% !important;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent) !important;
            transition: left 0.6s ease !important;
            z-index: 999 !important;
            pointer-events: none !important;
            display: block !important;
        }

        div#canvas #${componentId} .hero-btn:hover::before,
        .canvas #${componentId} .hero-btn:hover::before {
            left: 100% !important;
        }
    `;
    document.head.appendChild(style);

    // 调试信息
    console.log(`应用主按钮悬停样式: ${componentId}`);
}

// 更新英雄区导航按钮悬停样式
function updateHeroNavButtonHoverStyles(componentId, props) {
    // 移除旧的样式
    const oldStyle = document.getElementById(`hero-nav-btn-hover-style-${componentId}`);
    if (oldStyle) {
        oldStyle.remove();
    }

    // 创建新的悬停样式
    const style = document.createElement('style');
    style.id = `hero-nav-btn-hover-style-${componentId}`;
    style.textContent = `
        #${componentId} .hero-nav-btn {
            transition: all 0.2s ease !important;
            position: relative !important;
            overflow: hidden !important;
        }
        #${componentId} .hero-nav-btn::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: -100% !important;
            width: 100% !important;
            height: 100% !important;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent) !important;
            transition: left 0.6s ease !important;
            z-index: 1 !important;
            pointer-events: none !important;
        }
        #${componentId} .hero-nav-btn:hover {
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15) !important;
            filter: brightness(1.05) !important;
        }
        #${componentId} .hero-nav-btn:hover::before {
            left: 100% !important;
        }
        #${componentId} .hero-nav-btn:not(.primary):hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
            border: 2px solid ${props.navBtn1BorderColor} !important;
        }
        #${componentId} .hero-nav-btn.primary:hover {
            background-color: ${props.navBtn2BackgroundColor} !important;
            border: 2px solid ${props.navBtn2BorderColor} !important;
        }

        /* 编辑器环境特殊处理 */
        div#canvas #${componentId} .hero-nav-btn::before,
        .canvas #${componentId} .hero-nav-btn::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: -100% !important;
            width: 100% !important;
            height: 100% !important;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent) !important;
            transition: left 0.6s ease !important;
            z-index: 999 !important;
            pointer-events: none !important;
            display: block !important;
        }

        div#canvas #${componentId} .hero-nav-btn:hover::before,
        .canvas #${componentId} .hero-nav-btn:hover::before {
            left: 100% !important;
        }
    `;
    document.head.appendChild(style);

    // 调试信息
    console.log(`应用导航按钮悬停样式: ${componentId}`);
}

// 注册英雄区组件
if (typeof ComponentManager !== 'undefined') {
    ComponentManager.register('hero', heroComponent, generateHeroProperties, updateHeroDisplay);
}

// 初始化日志
console.log('🦸 英雄区组件已加载');

<?php
declare(strict_types=1);

namespace app\admin\controller;

use think\facade\Request;
use think\facade\Db;
use app\validate\ContactValidate;
use app\validate\SecurityValidate;
use think\exception\ValidateException;

class Contacts extends Base
{
    public function index()
    {
        $admin_info = $this->getAdminUser();

        // 处理操作
        $action = Request::param('action', 'list');
        $id = (int)Request::param('id', 0);

        // 处理POST请求
        if (Request::isPost()) {
            return $this->handlePost();
        }

        // 处理删除操作（GET请求）
        if ($action === 'delete' && $id > 0) {
            return $this->handleDelete($id);
        }

        // 获取联系表单详情（查看/编辑模式）
        $contact = null;
        if (in_array($action, ['view', 'edit']) && $id > 0) {
            try {
                $contact = Db::table('contact_forms')->where('id', $id)->find();
                if (!$contact) {
                    return redirect('/admin/contacts?msg=' . urlencode('联系表单不存在') . '&type=danger');
                }

                // 如果是查看模式且状态为new，自动标记为已读
                if ($action === 'view' && $contact['status'] === 'new') {
                    Db::table('contact_forms')->where('id', $id)->update(['status' => 'read']);
                    $contact['status'] = 'read';
                }
            } catch (\Exception $e) {
                return redirect('/admin/contacts?msg=' . urlencode('获取联系表单详情失败') . '&type=danger');
            }
        }

        // 分页处理
        $page = (int)Request::param('page', 1);
        $itemsPerPage = 5;
        $offset = ($page - 1) * $itemsPerPage;

        // 状态筛选和搜索
        $statusFilter = Request::param('status', '');
        $searchKeyword = Request::param('search', '');

        // 输入验证和安全过滤
        $validate = new ContactValidate();
        $searchData = $validate->filterInput([
            'status' => $statusFilter,
            'search' => $searchKeyword
        ]);

        // 数据安全验证 - 使用统一安全验证器
        $securityCheck = SecurityValidate::validateDataSecurity($searchData, [
            'status' => 'checkUsernameSafe',
            'search' => 'checkXss',
        ]);

        if (!$securityCheck['valid']) {
            // 安全检查失败，使用默认值
            $searchData = ['status' => '', 'search' => ''];
        } else {
            $validationResult = $validate->validateSearch($searchData);
            if ($validationResult !== true) {
                $searchData = ['status' => '', 'search' => ''];
            }
        }

        $statusFilter = $searchData['status'];
        $searchKeyword = $searchData['search'];

        // 构建查询条件
        $whereConditions = [];
        $params = [];

        if (!empty($statusFilter)) {
            $whereConditions[] = "status = ?";
            $params[] = $statusFilter;
        }

        if (!empty($searchKeyword)) {
            $whereConditions[] = "(name LIKE ? OR email LIKE ? OR subject LIKE ? OR message LIKE ?)";
            $searchTerm = "%{$searchKeyword}%";
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
        }

        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

        // 获取联系表单总数和列表
        $totalItems = 0;
        $contacts = [];

        try {
            // 先检查表是否存在
            $tables = Db::query("SHOW TABLES LIKE 'contact_forms'");
            if (empty($tables)) {
                // 表不存在，创建表
                $this->createContactFormsTable();
            }

            // 获取总数
            $sql = "SELECT COUNT(*) FROM contact_forms {$whereClause}";
            $totalItems = Db::query($sql, $params)[0]['COUNT(*)'] ?? 0;

            // 获取列表数据
            $sql = "SELECT * FROM contact_forms {$whereClause} ORDER BY created_at DESC LIMIT {$itemsPerPage} OFFSET {$offset}";
            $contacts = Db::query($sql, $params);

        } catch (\Exception $e) {
            // 如果出错，尝试创建表
            try {
                $this->createContactFormsTable();
                $sql = "SELECT COUNT(*) FROM contact_forms {$whereClause}";
                $totalItems = Db::query($sql, $params)[0]['COUNT(*)'] ?? 0;
                $sql = "SELECT * FROM contact_forms {$whereClause} ORDER BY created_at DESC LIMIT {$itemsPerPage} OFFSET {$offset}";
                $contacts = Db::query($sql, $params);
            } catch (\Exception $e2) {
                $contacts = [];
                $totalItems = 0;
            }
        }

        $totalPages = ceil($totalItems / $itemsPerPage);

        // 获取状态统计
        $statusStats = [];
        try {
            $stats = Db::query("SELECT status, COUNT(*) as count FROM contact_forms GROUP BY status");
            foreach ($stats as $stat) {
                $statusStats[$stat['status']] = $stat['count'];
            }
        } catch (\Exception $e) {
            $statusStats = [];
        }

        // 从URL参数获取消息
        $message = Request::param('msg', '');
        $messageType = Request::param('type', '');

        return view('admin/contacts', [
            'pageTitle' => '联系表单管理',
            'pageIcon' => 'fas fa-envelope',
            'admin_info' => $admin_info,
            'action' => $action,
            'contact' => $contact,
            'contacts' => $contacts,
            'totalItems' => $totalItems,
            'totalPages' => $totalPages,
            'page' => $page,
            'statusFilter' => $statusFilter,
            'searchKeyword' => $searchKeyword,
            'statusStats' => $statusStats,
            'message' => $message,
            'messageType' => $messageType,
            'currentController' => 'Contacts'
        ]);
    }

    private function handlePost()
    {
        try {
            $action = Request::param('action');
            $id = (int)Request::param('id', 0);

            if ($action === 'update_status') {
                $data = Request::param();

                // 输入验证和安全过滤
                $validate = new ContactValidate();
                $data = $validate->filterInput($data);

                // 数据安全验证 - 使用统一安全验证器
                $securityCheck = SecurityValidate::validateDataSecurity($data, [
                    'status' => 'checkUsernameSafe',
                    'admin_reply' => 'checkXss',
                ]);

                if (!$securityCheck['valid']) {
                    $errors = [];
                    foreach ($securityCheck['errors'] as $field => $fieldErrors) {
                        $errors[] = $field . ': ' . implode(', ', $fieldErrors);
                    }
                    return redirect('/admin/contacts?msg=' . urlencode('数据安全检查失败: ' . implode('; ', $errors)) . '&type=danger');
                }

                try {
                    $validate->scene('admin_reply')->check($data);
                } catch (ValidateException $e) {
                    return redirect('/admin/contacts?msg=' . urlencode($e->getError()) . '&type=danger');
                }

                $status = $data['status'];
                $adminReply = $data['admin_reply'] ?? '';

                $updateData = ['status' => $status];

                if (!empty($adminReply)) {
                    $updateData['admin_reply'] = $adminReply;
                    $updateData['replied_at'] = date('Y-m-d H:i:s');
                    $updateData['replied_by'] = 1; // 假设管理员ID为1
                }

                Db::table('contact_forms')->where('id', $id)->update($updateData);

                return redirect('/admin/contacts?msg=' . urlencode('联系表单状态已更新') . '&type=success');
            }

            // 处理删除操作
            if ($action === 'delete' && $id > 0) {
                return $this->handleDelete($id);
            }

            // 如果没有匹配的操作，返回列表页
            return redirect('/admin/contacts?msg=' . urlencode('无效的操作') . '&type=danger');

        } catch (\Exception $e) {
            return redirect('/admin/contacts?msg=' . urlencode('操作失败：' . $e->getMessage()) . '&type=danger');
        }
    }

    private function handleDelete($id)
    {
        try {
            // 验证ID
            if (!$id || $id <= 0) {
                return redirect('/admin/contacts?msg=' . urlencode('无效的ID参数') . '&type=danger');
            }

            // 获取联系表单信息
            $contact = Db::table('contact_forms')->where('id', $id)->find();

            if (!$contact) {
                return redirect('/admin/contacts?msg=' . urlencode('联系表单不存在，ID: ' . $id) . '&type=danger');
            }

            // 删除数据库记录
            $result = Db::table('contact_forms')->where('id', $id)->delete();

            if ($result) {
                return redirect('/admin/contacts?msg=' . urlencode('联系表单删除成功') . '&type=success');
            } else {
                return redirect('/admin/contacts?msg=' . urlencode('删除操作失败，未影响任何记录') . '&type=danger');
            }

        } catch (\Exception $e) {
            // 记录详细错误信息
            $errorMsg = '删除失败：' . $e->getMessage() . ' (文件: ' . $e->getFile() . ', 行: ' . $e->getLine() . ')';
            return redirect('/admin/contacts?msg=' . urlencode($errorMsg) . '&type=danger');
        }
    }

    private function createContactFormsTable()
    {
        $sql = "
        CREATE TABLE IF NOT EXISTS `contact_forms` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `name` varchar(100) NOT NULL COMMENT '姓名',
          `email` varchar(200) NOT NULL COMMENT '邮箱',
          `phone` varchar(20) DEFAULT NULL COMMENT '电话',
          `company` varchar(200) DEFAULT NULL COMMENT '公司',
          `subject` varchar(300) NOT NULL COMMENT '主题',
          `message` text NOT NULL COMMENT '留言内容',
          `status` enum('new','read','replied','closed') DEFAULT 'new' COMMENT '状态',
          `admin_reply` text DEFAULT NULL COMMENT '管理员回复',
          `replied_at` timestamp NULL DEFAULT NULL COMMENT '回复时间',
          `replied_by` int(11) DEFAULT NULL COMMENT '回复人ID',
          `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
          `user_agent` text DEFAULT NULL COMMENT '浏览器信息',
          `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          PRIMARY KEY (`id`),
          KEY `idx_status` (`status`),
          KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='联系表单表'
        ";

        Db::execute($sql);

        // 插入示例数据
        $sampleData = [
            [
                'name' => '张三',
                'email' => '<EMAIL>',
                'phone' => '13800138000',
                'company' => '示例科技有限公司',
                'subject' => '咨询企业网站建设服务',
                'message' => '您好，我们公司想要建设一个企业官网，希望了解相关的服务内容和报价。我们主要从事软件开发业务，希望网站能够展示我们的技术实力和成功案例。',
                'status' => 'new',
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ],
            [
                'name' => '李四',
                'email' => '<EMAIL>',
                'phone' => '13900139000',
                'company' => '创新电商有限公司',
                'subject' => '电商平台开发需求',
                'message' => '我们需要开发一个B2C电商平台，包含商品管理、订单处理、支付集成等功能。希望能够支持多种支付方式，并且有良好的用户体验。',
                'status' => 'read',
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
            ],
            [
                'name' => '王五',
                'email' => '<EMAIL>',
                'phone' => '13700137000',
                'company' => '智能制造股份有限公司',
                'subject' => '工业物联网解决方案咨询',
                'message' => '我们是一家制造企业，希望通过物联网技术提升生产效率。需要了解相关的技术方案和实施周期。',
                'status' => 'replied',
                'admin_reply' => '感谢您的咨询！我们在工业物联网领域有丰富的经验，可以为您提供完整的解决方案。我们的技术团队会在2个工作日内与您联系，详细了解您的需求并提供专业的技术方案。',
                'replied_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'replied_by' => 1,
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]
        ];

        // 检查是否已有数据，避免重复插入
        $existingCount = Db::table('contact_forms')->count();
        if ($existingCount == 0) {
            foreach ($sampleData as $data) {
                Db::table('contact_forms')->insert($data);
            }
        }
    }
}

<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - DIY页面模型
 */

declare(strict_types=1);

namespace app\model;

use think\Model;
use think\model\concern\SoftDelete;
use think\model\relation\BelongsTo;
use think\model\relation\HasMany;

/**
 * DIY页面模型
 * @property int $id 页面ID
 * @property string $title 页面标题
 * @property string $slug 页面别名
 * @property string $description 页面描述
 * @property array $content 页面内容
 * @property int $template_id 模板ID
 * @property string $layout 布局类型
 * @property string $theme 主题样式
 * @property string $custom_css 自定义CSS
 * @property string $custom_js 自定义JS
 * @property string $seo_title SEO标题
 * @property string $seo_description SEO描述
 * @property string $seo_keywords SEO关键词
 * @property string $og_image OG图片
 * @property int $status 状态
 * @property int $is_home 是否首页
 * @property int $sort_order 排序
 * @property int $view_count 浏览次数
 * @property int $created_by 创建者
 * @property int $updated_by 更新者
 * @property string $published_at 发布时间
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 */
class DiyPage extends Model
{
    use SoftDelete;

    // 表名
    protected $name = 'diy_pages';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 软删除字段
    protected $deleteTime = 'deleted_at';

    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'template_id' => 'integer',
        'content' => 'json',
        'status' => 'integer',
        'is_home' => 'integer',
        'sort_order' => 'integer',
        'view_count' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'published_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    // 追加字段
    protected $append = [
        'status_text',
        'url',
        'preview_url'
    ];

    // 允许批量赋值的字段
    protected $field = [
        'title',
        'slug',
        'description',
        'content',
        'template_id',
        'layout',
        'theme',
        'custom_css',
        'custom_js',
        'seo_title',
        'seo_description',
        'seo_keywords',
        'og_image',
        'status',
        'is_home',
        'sort_order',
        'created_by',
        'updated_by'
    ];

    // 状态常量
    const STATUS_DRAFT = 0;     // 草稿
    const STATUS_PUBLISHED = 1; // 发布
    const STATUS_OFFLINE = 2;   // 下线

    // 状态文本映射
    const STATUS_TEXT = [
        self::STATUS_DRAFT => '草稿',
        self::STATUS_PUBLISHED => '已发布',
        self::STATUS_OFFLINE => '已下线'
    ];

    /**
     * 内容设置器
     * @param mixed $value
     * @return string
     */
    public function setContentAttr($value): string
    {
        if (is_array($value)) {
            return json_encode($value, JSON_UNESCAPED_UNICODE);
        }
        return $value;
    }

    /**
     * 内容获取器
     * @param string $value
     * @return array
     */
    public function getContentAttr(string $value): array
    {
        return json_decode($value, true) ?? [];
    }

    /**
     * 状态文本获取器
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getStatusTextAttr($value, array $data): string
    {
        return self::STATUS_TEXT[$data['status']] ?? '未知';
    }

    /**
     * URL获取器
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getUrlAttr($value, array $data): string
    {
        if ($data['is_home']) {
            return '/';
        }
        return '/' . ltrim($data['slug'], '/');
    }

    /**
     * 预览URL获取器
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getPreviewUrlAttr($value, array $data): string
    {
        return '/preview/' . $data['id'];
    }

    /**
     * 关联模板
     * @return BelongsTo
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(DiyTemplate::class, 'template_id');
    }

    /**
     * 关联创建者
     * @return BelongsTo
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 关联更新者
     * @return BelongsTo
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * 关联页面区块
     * @return HasMany
     */
    public function blocks(): HasMany
    {
        return $this->hasMany(DiyPageBlock::class, 'page_id')->order('sort_order');
    }

    /**
     * 搜索范围
     * @param \think\db\Query $query
     * @param string $keyword
     * @return void
     */
    public function scopeSearch($query, string $keyword): void
    {
        if (!empty($keyword)) {
            $query->where(function ($q) use ($keyword) {
                $q->whereLike('title', "%{$keyword}%")
                  ->whereOr('slug', 'like', "%{$keyword}%")
                  ->whereOr('description', 'like', "%{$keyword}%");
            });
        }
    }

    /**
     * 状态范围
     * @param \think\db\Query $query
     * @param int $status
     * @return void
     */
    public function scopeStatus($query, int $status): void
    {
        $query->where('status', $status);
    }

    /**
     * 首页范围
     * @param \think\db\Query $query
     * @param int $isHome
     * @return void
     */
    public function scopeHome($query, int $isHome): void
    {
        $query->where('is_home', $isHome);
    }

    /**
     * 已发布范围
     * @param \think\db\Query $query
     * @return void
     */
    public function scopePublished($query): void
    {
        $query->where('status', self::STATUS_PUBLISHED)
              ->where('published_at', '<=', date('Y-m-d H:i:s'));
    }

    /**
     * 创建页面
     * @param array $data 页面数据
     * @return static
     */
    public static function createPage(array $data): self
    {
        // 检查别名是否存在
        if (self::where('slug', $data['slug'])->exists()) {
            throw new \Exception('页面别名已存在');
        }

        // 如果设置为首页，取消其他页面的首页状态
        if (!empty($data['is_home'])) {
            self::where('is_home', 1)->update(['is_home' => 0]);
        }

        return self::create($data);
    }

    /**
     * 更新页面
     * @param array $data 页面数据
     * @return bool
     */
    public function updatePage(array $data): bool
    {
        // 检查别名是否存在（排除自己）
        if (isset($data['slug']) && 
            self::where('slug', $data['slug'])
                ->where('id', '<>', $this->id)
                ->exists()) {
            throw new \Exception('页面别名已存在');
        }

        // 如果设置为首页，取消其他页面的首页状态
        if (!empty($data['is_home'])) {
            self::where('is_home', 1)
                ->where('id', '<>', $this->id)
                ->update(['is_home' => 0]);
        }

        return $this->save($data);
    }

    /**
     * 发布页面
     * @return bool
     */
    public function publish(): bool
    {
        return $this->save([
            'status' => self::STATUS_PUBLISHED,
            'published_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 下线页面
     * @return bool
     */
    public function offline(): bool
    {
        return $this->save(['status' => self::STATUS_OFFLINE]);
    }

    /**
     * 设为首页
     * @return bool
     */
    public function setAsHome(): bool
    {
        // 取消其他页面的首页状态
        self::where('is_home', 1)->update(['is_home' => 0]);

        // 设置当前页面为首页
        return $this->save(['is_home' => 1]);
    }

    /**
     * 增加浏览量
     * @return bool
     */
    public function incrementViewCount(): bool
    {
        return $this->save(['view_count' => $this->view_count + 1]);
    }

    /**
     * 复制页面
     * @param string $newTitle 新标题
     * @param string $newSlug 新别名
     * @return static
     */
    public function duplicate(string $newTitle, string $newSlug): self
    {
        $data = $this->toArray();
        
        // 移除不需要复制的字段
        unset($data['id'], $data['created_at'], $data['updated_at'], $data['deleted_at']);
        
        // 设置新的标题和别名
        $data['title'] = $newTitle;
        $data['slug'] = $newSlug;
        $data['status'] = self::STATUS_DRAFT;
        $data['is_home'] = 0;
        $data['view_count'] = 0;
        $data['published_at'] = null;

        return self::createPage($data);
    }

    /**
     * 获取首页
     * @return static|null
     */
    public static function getHomePage(): ?self
    {
        return self::where('is_home', 1)
                   ->where('status', self::STATUS_PUBLISHED)
                   ->find();
    }

    /**
     * 根据别名获取页面
     * @param string $slug 别名
     * @return static|null
     */
    public static function getBySlug(string $slug): ?self
    {
        return self::where('slug', $slug)
                   ->where('status', self::STATUS_PUBLISHED)
                   ->find();
    }
}

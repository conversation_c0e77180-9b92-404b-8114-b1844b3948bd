<?php
namespace app\model;

use think\Model;

/**
 * 新闻模型
 */
class News extends Model
{
    // 数据表名
    protected $name = 'news';

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    // 字段类型转换
    protected $type = [
        'category_id' => 'integer',
        'status' => 'integer',
        'views' => 'integer',
        'is_featured' => 'integer',
        'sort_order' => 'integer',
        'published_at' => 'datetime',
    ];

    // 关联新闻分类
    public function category()
    {
        return $this->belongsTo(NewsCategory::class, 'category_id');
    }

    // 获取格式化的发布时间
    public function getPublishedAtTextAttr($value, $data)
    {
        $publishedAt = $data['published_at'] ?? $data['created_at'];
        return date('Y-m-d H:i', strtotime($publishedAt));
    }

    // 获取摘要（如果没有摘要则从内容中截取）
    public function getSummaryTextAttr($value, $data)
    {
        if (!empty($data['summary'])) {
            return $data['summary'];
        }

        // 从内容中提取纯文本并截取
        $content = strip_tags($data['content'] ?? '');
        return mb_substr($content, 0, 150, 'UTF-8') . '...';
    }

    // 获取状态文本
    public function getStatusTextAttr($value, $data)
    {
        return $data['status'] ? '已发布' : '草稿';
    }

    // 生成URL友好的slug
    public static function generateSlug($title)
    {
        // 移除特殊字符，保留中文、英文、数字
        $slug = preg_replace('/[^\p{L}\p{N}\s-]/u', '', $title);
        // 替换空格为连字符
        $slug = preg_replace('/\s+/', '-', trim($slug));
        // 转换为小写（对英文有效）
        $slug = strtolower($slug);
        // 移除多余的连字符
        $slug = preg_replace('/-+/', '-', $slug);
        // 移除首尾连字符
        $slug = trim($slug, '-');

        return $slug ?: 'news-' . time();
    }

    // 检查slug唯一性
    public static function checkSlugUnique($slug, $excludeId = null)
    {
        $query = self::where('slug', $slug);
        if ($excludeId) {
            $query->where('id', '<>', $excludeId);
        }
        return !$query->find();
    }

    // 获取推荐新闻
    public static function getFeaturedNews($limit = 5)
    {
        return self::with('category')
            ->where('status', 1)
            ->where('is_featured', 1)
            ->order('sort_order', 'desc')
            ->order('published_at', 'desc')
            ->limit($limit)
            ->select();
    }

    // 获取最新新闻
    public static function getLatestNews($limit = 10, $categoryId = null)
    {
        $query = self::with('category')
            ->where('status', 1);

        if ($categoryId) {
            $query->where('category_id', $categoryId);
        }

        return $query->order('sort_order', 'desc')
            ->order('published_at', 'desc')
            ->limit($limit)
            ->select();
    }
}

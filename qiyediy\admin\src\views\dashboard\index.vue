<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 仪表盘页面
-->

<template>
  <div class="dashboard-container">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <el-card class="welcome-card">
        <div class="welcome-content">
          <div class="welcome-info">
            <h2 class="welcome-title">
              欢迎回来，{{ userStore.displayName }}！
            </h2>
            <p class="welcome-subtitle">
              今天是 {{ currentDate }}，{{ getGreeting() }}
            </p>
            <div class="welcome-stats">
              <div class="stat-item">
                <span class="stat-label">上次登录：</span>
                <span class="stat-value">{{ formatLastLogin() }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">登录次数：</span>
                <span class="stat-value">{{ userStore.user?.login_count || 0 }} 次</span>
              </div>
            </div>
          </div>
          <div class="welcome-avatar">
            <el-avatar :size="80" :src="userStore.avatar">
              <el-icon size="40"><User /></el-icon>
            </el-avatar>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <StatCard
            title="总用户数"
            :value="stats.users.total"
            icon="User"
            color="#409EFF"
            :trend="stats.users.trend"
          />
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <StatCard
            title="DIY页面"
            :value="stats.pages.total"
            icon="Document"
            color="#67C23A"
            :trend="stats.pages.trend"
          />
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <StatCard
            title="今日访问"
            :value="stats.visits.today"
            icon="View"
            color="#E6A23C"
            :trend="stats.visits.trend"
          />
        </el-col>
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <StatCard
            title="系统消息"
            :value="stats.messages.unread"
            icon="Message"
            color="#F56C6C"
            :trend="stats.messages.trend"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 图表和快捷操作 -->
    <div class="content-section">
      <el-row :gutter="20">
        <!-- 访问趋势图表 -->
        <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span class="card-title">访问趋势</span>
                <el-radio-group v-model="chartPeriod" size="small">
                  <el-radio-button label="7d">7天</el-radio-button>
                  <el-radio-button label="30d">30天</el-radio-button>
                  <el-radio-button label="90d">90天</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div class="chart-container">
              <VisitChart :period="chartPeriod" />
            </div>
          </el-card>
        </el-col>

        <!-- 快捷操作 -->
        <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
          <el-card class="quick-actions-card">
            <template #header>
              <span class="card-title">快捷操作</span>
            </template>
            <div class="quick-actions">
              <div class="action-item" @click="handleQuickAction('create-page')">
                <el-icon class="action-icon" color="#409EFF"><EditPen /></el-icon>
                <div class="action-content">
                  <div class="action-title">创建页面</div>
                  <div class="action-desc">快速创建DIY页面</div>
                </div>
              </div>
              <div class="action-item" @click="handleQuickAction('user-manage')">
                <el-icon class="action-icon" color="#67C23A"><User /></el-icon>
                <div class="action-content">
                  <div class="action-title">用户管理</div>
                  <div class="action-desc">管理系统用户</div>
                </div>
              </div>
              <div class="action-item" @click="handleQuickAction('system-settings')">
                <el-icon class="action-icon" color="#E6A23C"><Setting /></el-icon>
                <div class="action-content">
                  <div class="action-title">系统设置</div>
                  <div class="action-desc">配置系统参数</div>
                </div>
              </div>
              <div class="action-item" @click="handleQuickAction('view-logs')">
                <el-icon class="action-icon" color="#F56C6C"><Document /></el-icon>
                <div class="action-content">
                  <div class="action-title">查看日志</div>
                  <div class="action-desc">系统操作日志</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 最近活动和系统信息 -->
    <div class="bottom-section">
      <el-row :gutter="20">
        <!-- 最近活动 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <el-card class="activity-card">
            <template #header>
              <span class="card-title">最近活动</span>
            </template>
            <div class="activity-list">
              <div
                v-for="activity in recentActivities"
                :key="activity.id"
                class="activity-item"
              >
                <div class="activity-avatar">
                  <el-avatar :size="32" :src="activity.avatar">
                    <el-icon><User /></el-icon>
                  </el-avatar>
                </div>
                <div class="activity-content">
                  <div class="activity-text">
                    <strong>{{ activity.user }}</strong> {{ activity.action }}
                  </div>
                  <div class="activity-time">{{ activity.time }}</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 系统信息 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <el-card class="system-info-card">
            <template #header>
              <span class="card-title">系统信息</span>
            </template>
            <div class="system-info">
              <div class="info-item">
                <span class="info-label">系统版本：</span>
                <span class="info-value">QiyeDIY v1.0.0</span>
              </div>
              <div class="info-item">
                <span class="info-label">PHP版本：</span>
                <span class="info-value">{{ systemInfo.phpVersion }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">数据库：</span>
                <span class="info-value">{{ systemInfo.database }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">服务器：</span>
                <span class="info-value">{{ systemInfo.server }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">运行时间：</span>
                <span class="info-value">{{ systemInfo.uptime }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { User, EditPen, Setting, Document } from '@element-plus/icons-vue'
import StatCard from './components/StatCard.vue'
import VisitChart from './components/VisitChart.vue'

const router = useRouter()
const userStore = useUserStore()

// 状态
const chartPeriod = ref('7d')

// 当前日期
const currentDate = computed(() => {
  return new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
})

// 统计数据
const stats = reactive({
  users: { total: 1234, trend: 12 },
  pages: { total: 56, trend: 8 },
  visits: { today: 2345, trend: -5 },
  messages: { unread: 3, trend: 0 }
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    user: '张三',
    action: '创建了新页面 "产品介绍"',
    time: '2分钟前',
    avatar: ''
  },
  {
    id: 2,
    user: '李四',
    action: '更新了用户权限',
    time: '10分钟前',
    avatar: ''
  },
  {
    id: 3,
    user: '王五',
    action: '发布了文章 "企业文化"',
    time: '1小时前',
    avatar: ''
  }
])

// 系统信息
const systemInfo = reactive({
  phpVersion: 'PHP 8.1.0',
  database: 'MySQL 8.0.28',
  server: 'Nginx 1.20.2',
  uptime: '15天 8小时 32分钟'
})

/**
 * 获取问候语
 */
const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 6) return '夜深了，注意休息'
  if (hour < 9) return '早上好'
  if (hour < 12) return '上午好'
  if (hour < 14) return '中午好'
  if (hour < 17) return '下午好'
  if (hour < 19) return '傍晚好'
  if (hour < 22) return '晚上好'
  return '夜深了，注意休息'
}

/**
 * 格式化最后登录时间
 */
const formatLastLogin = () => {
  const lastLogin = userStore.user?.last_login_at
  if (!lastLogin) return '首次登录'
  
  const date = new Date(lastLogin)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 30) return `${days}天前`
  
  return date.toLocaleDateString('zh-CN')
}

/**
 * 处理快捷操作
 */
const handleQuickAction = (action: string) => {
  switch (action) {
    case 'create-page':
      router.push('/diy/editor')
      break
    case 'user-manage':
      router.push('/user/list')
      break
    case 'system-settings':
      router.push('/system/settings')
      break
    case 'view-logs':
      router.push('/system/logs')
      break
  }
}

/**
 * 加载仪表盘数据
 */
const loadDashboardData = async () => {
  try {
    // 这里可以调用API获取实际数据
    // const data = await dashboardApi.getStats()
    // Object.assign(stats, data)
  } catch (error) {
    console.error('加载仪表盘数据失败:', error)
  }
}

onMounted(() => {
  loadDashboardData()
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background-color: var(--el-bg-color-page);
  min-height: calc(100vh - 140px);
}

.welcome-section {
  margin-bottom: 20px;
}

.welcome-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-info {
  flex: 1;
}

.welcome-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.welcome-subtitle {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin: 0 0 16px 0;
}

.welcome-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  font-size: 13px;
  
  .stat-label {
    color: var(--el-text-color-secondary);
  }
  
  .stat-value {
    color: var(--el-text-color-primary);
    font-weight: 500;
  }
}

.welcome-avatar {
  margin-left: 20px;
}

.stats-section {
  margin-bottom: 20px;
}

.content-section {
  margin-bottom: 20px;
}

.chart-card,
.quick-actions-card,
.activity-card,
.system-info-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.chart-container {
  height: 320px;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  background-color: var(--el-fill-color-lighter);
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: var(--el-fill-color-light);
    transform: translateY(-2px);
  }
}

.action-icon {
  font-size: 24px;
  margin-right: 12px;
}

.action-content {
  flex: 1;
}

.action-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.action-desc {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 320px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 14px;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.system-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
  
  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.info-value {
  font-size: 14px;
  color: var(--el-text-color-primary);
  font-weight: 500;
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }
  
  .welcome-content {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .welcome-avatar {
    margin-left: 0;
  }
  
  .welcome-stats {
    justify-content: center;
  }
  
  .chart-card,
  .quick-actions-card,
  .activity-card,
  .system-info-card {
    height: auto;
    margin-bottom: 16px;
  }
}
</style>

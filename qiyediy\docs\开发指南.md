# 🚀 QiyeDIY企业建站系统 - 开发指南

**三只鱼网络科技 | 韩总 | 2024-12-19**  
**项目代号：QiyeDIY - 新一代企业DIY建站系统**

---

## 📋 开发环境搭建

### 🔧 环境要求

#### 基础环境
- **PHP**: >= 8.1
- **MySQL**: >= 8.0
- **Redis**: >= 7.0
- **Node.js**: >= 18.0
- **Composer**: >= 2.0
- **npm/yarn/pnpm**: 最新版本

#### 推荐开发工具
- **IDE**: PhpStorm / VS Code
- **数据库管理**: Navicat / phpMyAdmin
- **API测试**: Postman / Insomnia
- **版本控制**: Git
- **终端**: Windows Terminal / iTerm2

### 🚀 快速开始

#### 1. 项目克隆
```bash
git clone <repository-url> qiyediy
cd qiyediy
```

#### 2. 后端环境搭建
```bash
# 进入后端目录
cd backend

# 安装依赖
composer install

# 复制环境配置
cp .env.example .env

# 编辑环境配置
# 配置数据库连接、Redis连接等信息

# 创建数据库
mysql -u root -p
CREATE DATABASE qiyediy CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入数据库结构
mysql -u root -p qiyediy < database/qiyediy.sql

# 生成应用密钥
php think key:generate

# 启动开发服务器
php think serve --host 0.0.0.0 --port 8000
```

#### 3. 管理后台环境搭建
```bash
# 进入管理后台目录
cd admin

# 安装依赖
npm install
# 或者使用 yarn install / pnpm install

# 复制环境配置
cp .env.example .env

# 编辑环境配置
# 配置API地址等信息

# 启动开发服务器
npm run dev
```

#### 4. 前端展示环境搭建
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 复制环境配置
cp .env.example .env

# 编辑环境配置
# 配置API地址等信息

# 启动开发服务器
npm run dev
```

### 🔑 默认账号信息
- **用户名**: admin
- **密码**: admin123
- **邮箱**: <EMAIL>

---

## 🏗️ 项目架构详解

### 📁 目录结构说明

```
qiyediy/
├── backend/                 # 后端API服务
│   ├── app/                # 应用核心
│   │   ├── controller/     # 控制器层
│   │   ├── model/          # 模型层
│   │   ├── service/        # 服务层
│   │   ├── middleware/     # 中间件
│   │   └── validate/       # 验证器
│   ├── config/             # 配置文件
│   ├── database/           # 数据库相关
│   └── public/             # 公共入口
├── admin/                   # 管理后台
│   ├── src/                # 源码目录
│   │   ├── components/     # 公共组件
│   │   ├── views/          # 页面组件
│   │   ├── router/         # 路由配置
│   │   ├── store/          # 状态管理
│   │   ├── api/            # API接口
│   │   └── utils/          # 工具函数
│   └── public/             # 静态资源
├── frontend/                # 前端展示
│   ├── components/         # 组件库
│   ├── pages/              # 页面
│   ├── layouts/            # 布局
│   └── assets/             # 资源文件
└── shared/                  # 共享资源
    ├── types/              # 类型定义
    ├── utils/              # 工具函数
    └── constants/          # 常量定义
```

### 🔄 数据流向

```
用户请求 → 前端路由 → API调用 → 后端控制器 → 服务层 → 模型层 → 数据库
                                    ↓
用户界面 ← 前端组件 ← 状态管理 ← API响应 ← JSON数据 ← 业务逻辑 ← 数据处理
```

---

## 🛠️ 开发规范

### 📝 代码规范

#### PHP代码规范 (PSR-12)
```php
<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * 文件描述 - QiyeDIY企业建站系统
 */

declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use think\Response;

class ExampleController extends BaseController
{
    /**
     * 示例方法
     * @return Response
     */
    public function index(): Response
    {
        return success([
            'message' => 'Hello QiyeDIY!'
        ]);
    }
}
```

#### TypeScript代码规范
```typescript
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * 文件描述 - QiyeDIY企业建站系统
 */

interface User {
  id: number
  username: string
  email: string
  status: number
}

export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  
  const login = async (credentials: LoginCredentials): Promise<void> => {
    try {
      const response = await api.post('/auth/login', credentials)
      user.value = response.data.user
    } catch (error) {
      throw new Error('登录失败')
    }
  }
  
  return {
    user: readonly(user),
    login
  }
})
```

### 🗂️ 文件命名规范

#### 后端文件命名
- **控制器**: `UserController.php`
- **模型**: `User.php`
- **服务**: `UserService.php`
- **中间件**: `AuthMiddleware.php`
- **验证器**: `UserValidate.php`

#### 前端文件命名
- **组件**: `UserCard.vue`
- **页面**: `user-list.vue`
- **Store**: `useUserStore.ts`
- **API**: `user.ts`
- **工具**: `format.ts`

### 🔧 Git提交规范

```bash
# 功能开发
git commit -m "feat: 添加用户管理功能"

# 问题修复
git commit -m "fix: 修复登录验证问题"

# 文档更新
git commit -m "docs: 更新开发指南"

# 样式调整
git commit -m "style: 调整按钮样式"

# 代码重构
git commit -m "refactor: 重构用户服务层"

# 性能优化
git commit -m "perf: 优化数据库查询性能"

# 测试相关
git commit -m "test: 添加用户模块单元测试"
```

---

## 🧩 组件开发指南

### 🎨 DIY组件开发

#### 1. 组件结构
```typescript
interface DIYComponent {
  id: string
  name: string
  type: string
  category: string
  icon: string
  preview: string
  config: ComponentConfig
  defaultProps: Record<string, any>
  styleConfig: StyleConfig
}
```

#### 2. 组件注册
```typescript
// 注册新组件
export const registerComponent = (component: DIYComponent) => {
  grapesjs.DomComponents.addType(component.type, {
    model: {
      defaults: {
        ...component.defaultProps,
        traits: component.config.traits
      }
    },
    view: {
      onRender() {
        // 组件渲染逻辑
      }
    }
  })
}
```

#### 3. 组件开发示例
```vue
<!-- TextComponent.vue -->
<template>
  <div class="text-component" :style="computedStyle">
    <component
      :is="tag"
      :class="textClass"
      v-html="content"
    />
  </div>
</template>

<script setup lang="ts">
interface Props {
  content: string
  tag: string
  fontSize: number
  color: string
  textAlign: string
  fontWeight: string
}

const props = withDefaults(defineProps<Props>(), {
  content: '请输入文本内容',
  tag: 'p',
  fontSize: 16,
  color: '#333333',
  textAlign: 'left',
  fontWeight: 'normal'
})

const computedStyle = computed(() => ({
  fontSize: `${props.fontSize}px`,
  color: props.color,
  textAlign: props.textAlign,
  fontWeight: props.fontWeight
}))
</script>
```

### 🔌 API接口开发

#### 1. 控制器开发
```php
<?php

namespace app\controller;

use app\BaseController;
use app\service\UserService;
use app\validate\UserValidate;
use think\Response;

class UserController extends BaseController
{
    protected UserService $userService;
    
    public function __construct()
    {
        $this->userService = new UserService();
    }
    
    /**
     * 用户列表
     */
    public function index(): Response
    {
        $params = $this->request->param();
        $result = $this->userService->getList($params);
        
        return paginate($result);
    }
    
    /**
     * 创建用户
     */
    public function store(): Response
    {
        $data = $this->request->post();
        
        // 数据验证
        $validate = new UserValidate();
        if (!$validate->scene('create')->check($data)) {
            return error($validate->getError());
        }
        
        $result = $this->userService->create($data);
        
        return success($result, '创建成功');
    }
}
```

#### 2. 服务层开发
```php
<?php

namespace app\service;

use app\model\User;
use think\db\exception\DataNotFoundException;

class UserService
{
    /**
     * 获取用户列表
     */
    public function getList(array $params): object
    {
        $query = User::with(['roles']);
        
        // 搜索条件
        if (!empty($params['keyword'])) {
            $query->where('username|email', 'like', '%' . $params['keyword'] . '%');
        }
        
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }
        
        return $query->order('created_at', 'desc')
                    ->paginate($params['per_page'] ?? 15);
    }
    
    /**
     * 创建用户
     */
    public function create(array $data): User
    {
        $data['password'] = encrypt_password($data['password']);
        
        return User::create($data);
    }
}
```

#### 3. 前端API调用
```typescript
// api/user.ts
import { http } from '@/utils/http'

export interface User {
  id: number
  username: string
  email: string
  status: number
}

export interface UserListParams {
  page?: number
  per_page?: number
  keyword?: string
  status?: number
}

export const userApi = {
  // 获取用户列表
  getList: (params: UserListParams) => 
    http.get<PaginatedResponse<User>>('/users', { params }),
  
  // 创建用户
  create: (data: Partial<User>) => 
    http.post<User>('/users', data),
  
  // 更新用户
  update: (id: number, data: Partial<User>) => 
    http.put<User>(`/users/${id}`, data),
  
  // 删除用户
  delete: (id: number) => 
    http.delete(`/users/${id}`)
}
```

---

## 🧪 测试指南

### 🔬 后端测试

#### 1. 单元测试
```php
<?php

namespace tests\unit;

use PHPUnit\Framework\TestCase;
use app\service\UserService;

class UserServiceTest extends TestCase
{
    protected UserService $userService;
    
    protected function setUp(): void
    {
        $this->userService = new UserService();
    }
    
    public function testCreateUser(): void
    {
        $data = [
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => '123456'
        ];
        
        $user = $this->userService->create($data);
        
        $this->assertInstanceOf(User::class, $user);
        $this->assertEquals('testuser', $user->username);
    }
}
```

#### 2. API测试
```php
<?php

namespace tests\feature;

use Tests\TestCase;

class UserControllerTest extends TestCase
{
    public function testUserList(): void
    {
        $response = $this->get('/api/users');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'code',
                    'message',
                    'data' => [
                        'list',
                        'total',
                        'per_page',
                        'current_page'
                    ]
                ]);
    }
}
```

### 🎭 前端测试

#### 1. 组件测试
```typescript
// UserCard.test.ts
import { mount } from '@vue/test-utils'
import UserCard from '@/components/UserCard.vue'

describe('UserCard', () => {
  it('renders user information correctly', () => {
    const user = {
      id: 1,
      username: 'testuser',
      email: '<EMAIL>'
    }
    
    const wrapper = mount(UserCard, {
      props: { user }
    })
    
    expect(wrapper.text()).toContain('testuser')
    expect(wrapper.text()).toContain('<EMAIL>')
  })
})
```

---

## 🚀 部署指南

### 📦 生产环境部署

#### 1. 后端部署
```bash
# 安装依赖
composer install --no-dev --optimize-autoloader

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置生产环境配置

# 优化配置
php think optimize:config
php think optimize:route

# 设置文件权限
chmod -R 755 storage/
chmod -R 755 runtime/
```

#### 2. 前端部署
```bash
# 管理后台构建
cd admin
npm run build

# 前端展示构建
cd frontend
npm run build
```

#### 3. Nginx配置
```nginx
# 后端API
server {
    listen 80;
    server_name api.qiyediy.com;
    root /var/www/qiyediy/backend/public;
    index index.php;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
}

# 管理后台
server {
    listen 80;
    server_name admin.qiyediy.com;
    root /var/www/qiyediy/admin/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
}

# 前端展示
server {
    listen 80;
    server_name www.qiyediy.com;
    root /var/www/qiyediy/frontend/.output/public;
    index index.html;
    
    location / {
        try_files $uri $uri/ @nuxt;
    }
    
    location @nuxt {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

---

## 📚 常见问题

### ❓ 开发环境问题

**Q: 后端启动失败，提示数据库连接错误？**
A: 检查 `.env` 文件中的数据库配置，确保数据库服务已启动，用户名密码正确。

**Q: 前端启动失败，提示端口被占用？**
A: 修改 `vite.config.ts` 或 `nuxt.config.ts` 中的端口配置，或者关闭占用端口的进程。

**Q: GrapesJS编辑器无法正常加载？**
A: 检查网络连接，确保CDN资源可以正常访问，或者使用本地资源。

### 🔧 功能开发问题

**Q: 如何添加新的DIY组件？**
A: 参考组件开发指南，创建组件配置文件，注册到GrapesJS中。

**Q: 如何实现权限控制？**
A: 使用中间件验证用户权限，前端路由守卫配合后端API权限验证。

**Q: 如何优化页面加载速度？**
A: 使用缓存策略、CDN加速、图片压缩、代码分割等技术。

---

## 📞 技术支持

- **官方网站**: https://www.qiyediy.com
- **技术文档**: https://docs.qiyediy.com
- **问题反馈**: https://github.com/qiyediy/issues
- **技术交流群**: QQ群 123456789
- **邮箱支持**: <EMAIL>

---

**© 2024 三只鱼网络科技 | 韩总 - QiyeDIY企业建站系统开发指南**

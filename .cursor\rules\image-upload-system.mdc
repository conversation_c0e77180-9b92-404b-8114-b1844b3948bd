---
description: 
globs: 
alwaysApply: false
---
# 图片上传系统规范

## 🖼️ 图片上传组件架构

### 核心问题解决
项目中图片上传功能存在的问题：
- 提示消息不自动关闭
- 双重提示问题（全局+组件）
- 页面滚动锁死
- 样式不统一
- 删除确认提示异常

### 统一解决方案
所有页面的图片上传功能必须使用各自上传器的独立提示系统，禁止调用全局`showMessage()`函数。

## 📁 相关文件结构

### 核心文件
- [public/assets/js/image-uploader.js](mdc:public/assets/js/image-uploader.js) - 主图片上传器
- [public/assets/js/image-selector-extension.js](mdc:public/assets/js/image-selector-extension.js) - 图片选择扩展
- [public/assets/css/image-uploader.css](mdc:public/assets/css/image-uploader.css) - 上传器样式

### 使用页面
- [app/view/admin/cases.html](mdc:app/view/admin/cases.html) - 案例管理
- [app/view/admin/news.html](mdc:app/view/admin/news.html) - 新闻管理
- [app/view/admin/products.html](mdc:app/view/admin/products.html) - 产品管理
- [app/view/admin/solutions.html](mdc:app/view/admin/solutions.html) - 解决方案
- [app/view/admin/banners.html](mdc:app/view/admin/banners.html) - 轮播图管理

## 🔧 图片上传器配置规范

### 标准初始化
```javascript
/**
 * 三只鱼网络科技 | 韩总 | 2025-01-12
 * 图片上传器初始化 - 现代前端解决方案
 */

// 主图片上传器
const imageUploader = new ImageUploader({
    containerId: 'imageUploader',
    uploadUrl: '/admin/upload/image',
    maxFiles: 10,
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    maxFileSize: 5 * 1024 * 1024, // 5MB
    onUploadSuccess: function(response, file) {
        // 使用上传器自己的提示方法
        this.showMessage('图片上传成功', 'success');
        // 其他业务逻辑...
    },
    onUploadError: function(error, file) {
        this.showMessage('上传失败：' + error, 'error');
    },
    onDeleteSuccess: function(imageId) {
        this.showMessage('图片删除成功', 'success');
    }
});

// 编辑器图片上传器（如果需要）
const editorImageUploader = new ImageUploader({
    containerId: 'editorImageUploader',
    uploadUrl: '/admin/upload/editor-image',
    maxFiles: 1,
    onUploadSuccess: function(response, file) {
        this.showMessage('图片上传成功', 'success');
        // 插入到编辑器...
    }
});
```

### 回调函数规范
```javascript
// ✅ 正确：使用上传器自己的showMessage方法
onUploadSuccess: function(response, file) {
    this.showMessage('图片上传成功', 'success');
    // 业务逻辑...
}

// ❌ 错误：使用全局showMessage函数
onUploadSuccess: function(response, file) {
    showMessage('图片上传成功', 'success'); // 会导致双重提示
}

// ✅ 正确：删除成功回调
onDeleteSuccess: function(imageId) {
    this.showMessage('图片删除成功', 'success');
    // 更新界面...
}

// ✅ 正确：错误处理
onUploadError: function(error, file) {
    this.showMessage('上传失败：' + error, 'error');
}
```

## 🎨 提示消息系统

### 独立提示系统特性
- **自动关闭**：3秒后自动消失
- **手动关闭**：点击×按钮立即关闭
- **防重复**：避免多个提示同时显示
- **动画效果**：滑入滑出动画
- **样式统一**：成功/错误/警告不同颜色

### 提示样式规范
```css
/**
 * 三只鱼网络科技 | 韩总 | 2025-01-12
 * 图片上传提示样式 - 响应式设计
 */

.image-uploader-message {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    min-width: 300px;
    max-width: 500px;
    padding: 12px 35px 12px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-size: 14px;
    line-height: 1.5;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.image-uploader-message.show {
    transform: translateX(0);
}

.image-uploader-message.success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.image-uploader-message.error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.image-uploader-message .close-btn {
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: inherit;
    font-size: 16px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-uploader-message .close-btn:hover {
    opacity: 0.8;
}
```

## 🚫 常见错误和解决方案

### 错误1：双重提示
**问题**：页面回调函数调用全局`showMessage()`，同时上传器也显示提示
```javascript
// ❌ 错误代码
onUploadSuccess: function(response, file) {
    showMessage('上传成功', 'success'); // 全局提示
    // 上传器内部也会显示提示，导致双重提示
}
```

**解决方案**：
```javascript
// ✅ 正确代码
onUploadSuccess: function(response, file) {
    this.showMessage('上传成功', 'success'); // 使用上传器自己的提示
}
```

### 错误2：页面滚动锁死
**问题**：弹窗显示时设置`overflow: hidden`但关闭时没有恢复
```javascript
// ❌ 问题代码
show() {
    document.body.style.overflow = 'hidden';
}
close() {
    // 忘记恢复滚动
}
```

**解决方案**：
```javascript
// ✅ 正确代码
close() {
    document.body.style.overflow = ''; // 恢复滚动
    this.destroy();
}
```

### 错误3：提示不自动关闭
**问题**：没有设置自动关闭定时器
```javascript
// ❌ 问题代码
showMessage(message, type) {
    // 只显示，不自动关闭
}
```

**解决方案**：
```javascript
// ✅ 正确代码
showMessage(message, type) {
    // 显示提示
    this.show();
    
    // 3秒后自动关闭
    setTimeout(() => {
        this.close();
    }, 3000);
}
```

## 📋 页面集成检查清单

### 新页面集成
- [ ] 引入图片上传器JS和CSS文件
- [ ] 正确初始化上传器实例
- [ ] 配置上传URL和参数
- [ ] 设置文件类型和大小限制
- [ ] 实现所有必要的回调函数

### 回调函数检查
- [ ] `onUploadSuccess` 使用 `this.showMessage()`
- [ ] `onUploadError` 使用 `this.showMessage()`
- [ ] `onDeleteSuccess` 使用 `this.showMessage()`
- [ ] 没有调用全局 `showMessage()` 函数
- [ ] 业务逻辑正确处理响应数据

### 样式和交互检查
- [ ] 提示消息正确显示
- [ ] 3秒自动关闭功能正常
- [ ] 手动关闭按钮可用
- [ ] 页面滚动不被锁定
- [ ] 上传进度显示正常
- [ ] 图片预览功能正常

### 兼容性检查
- [ ] 多文件上传功能
- [ ] 拖拽上传功能
- [ ] 图片选择器集成
- [ ] 编辑器图片插入
- [ ] 移动端适配

## 🔄 维护和更新

### 版本控制
- 图片上传器核心功能更新时，所有页面自动受益
- 提示系统样式修改一处，全部页面生效
- 新增功能向后兼容，不影响现有页面

### 性能优化
- 图片压缩和格式转换
- 上传进度显示优化
- 缓存机制改进
- 错误重试机制

### 安全考虑
- 文件类型验证
- 文件大小限制
- 上传路径安全检查
- 图片内容安全扫描

---

**核心原则**：统一接口、独立提示、用户友好、性能优先


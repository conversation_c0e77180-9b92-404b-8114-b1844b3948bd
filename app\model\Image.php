<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * 图片模型 - ThinkPHP6企业级应用
 * 功能：图片管理、安全上传、文件验证 - 已集成统一安全防护系统
 */

namespace app\model;

use think\Model;
use think\model\concern\SoftDelete;
use app\service\FileSecurityService;

/**
 * 图片模型 - 集成统一安全防护系统
 */
class Image extends Model
{
    // 表名
    protected $name = 'images';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'file_size' => 'integer',
        'width' => 'integer',
        'height' => 'integer',
        'group_id' => 'integer',
        'status' => 'boolean',
        'tags' => 'json',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    
    // 允许写入的字段
    protected $field = [
        'filename', 'stored_name', 'file_path', 'file_url', 'file_size',
        'mime_type', 'extension', 'width', 'height', 'group_id',
        'alt_text', 'tags', 'upload_ip', 'user_agent', 'status'
    ];

    // 隐藏字段
    protected $hidden = [
        'upload_ip', 'user_agent', 'deleted_at'
    ];

    // 追加字段
    protected $append = [
        'file_size_text', 'dimensions_text', 'full_url', 'security_status'
    ];
    
    // 关联图片分组
    public function group()
    {
        return $this->belongsTo(ImageGroup::class, 'group_id', 'id');
    }
    
    // 获取文件大小格式化
    public function getFileSizeTextAttr($value, $data)
    {
        return $this->formatFileSize($data['file_size'] ?? 0);
    }
    
    // 获取图片尺寸文本
    public function getDimensionsTextAttr($value, $data)
    {
        if (empty($data['width']) || empty($data['height'])) {
            return '未知';
        }
        return $data['width'] . ' × ' . $data['height'];
    }
    
    // 获取完整URL
    public function getFullUrlAttr($value, $data)
    {
        $url = $data['file_url'] ?? '';
        if (empty($url)) {
            return '';
        }
        
        // 如果已经是完整URL，直接返回
        if (strpos($url, 'http') === 0) {
            return $url;
        }
        
        // 直接从数据库读取站点URL
        $domain = \app\service\ConfigService::getSiteUrl();
        return $domain . $url;
    }
    
    /**
     * 格式化文件大小
     * @deprecated 建议使用 FileSecurityService 中的格式化方法
     * @param int $bytes 字节数
     * @return string 格式化后的大小
     */
    private function formatFileSize($bytes)
    {
        if ($bytes === 0) return '0 B';

        $k = 1024;
        $sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        $i = floor(log($bytes) / log($k));

        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }

    /**
     * 获取图片的安全状态
     * @return string 安全状态
     */
    public function getSecurityStatusAttr()
    {
        if (empty($this->file_path)) {
            return 'unknown';
        }

        $filePath = public_path() . $this->file_path;
        if (!file_exists($filePath)) {
            return 'missing';
        }

        $report = FileSecurityService::getSecurityReport($filePath);
        return $report['risk_level'] ?? 'unknown';
    }

    /**
     * 检查图片是否安全
     * @return bool 是否安全
     */
    public function isSafe()
    {
        $status = $this->getSecurityStatusAttr();
        return in_array($status, ['low', 'safe']);
    }

    /**
     * 获取图片的详细安全信息
     * @return array 安全信息
     */
    public function getSecurityInfo()
    {
        if (empty($this->file_path)) {
            return ['error' => '文件路径为空'];
        }

        $filePath = public_path() . $this->file_path;
        if (!file_exists($filePath)) {
            return ['error' => '文件不存在'];
        }

        return FileSecurityService::getSecurityReport($filePath);
    }
    
    /**
     * 验证图片文件 - 使用统一安全防护系统
     * @deprecated 建议使用 FileSecurityService::validateFile() 或 self::secureValidate()
     * @param \think\file\UploadedFile $file 上传文件
     * @return array 错误信息数组
     */
    public static function validateImageFile($file)
    {
        // 为了向后兼容，保留此方法但内部使用新的安全服务
        $result = FileSecurityService::validateFile($file, [
            'check_extension' => true,
            'check_mime' => true,
            'check_size' => true,
            'check_magic' => true,
            'check_content' => true,
            'check_image' => true,
            'strict_mode' => true,
        ]);

        return $result['valid'] ? [] : $result['errors'];
    }

    /**
     * 安全验证图片文件 - 推荐使用
     * @param \think\file\UploadedFile $file 上传文件
     * @param array $options 验证选项
     * @return array 验证结果
     */
    public static function secureValidate($file, $options = [])
    {
        $defaultOptions = [
            'check_extension' => true,
            'check_mime' => true,
            'check_size' => true,
            'check_magic' => true,
            'check_content' => true,
            'check_image' => true,
            'strict_mode' => true,
        ];

        $options = array_merge($defaultOptions, $options);
        return FileSecurityService::validateFile($file, $options);
    }

    /**
     * 安全上传图片文件
     * @param \think\file\UploadedFile $file 上传文件
     * @param array $options 上传选项
     * @return array 上传结果
     */
    public static function secureUpload($file, $options = [])
    {
        $defaultOptions = [
            'check_extension' => true,
            'check_mime' => true,
            'check_size' => true,
            'check_magic' => true,
            'check_content' => true,
            'check_image' => true,
            'strict_mode' => true,
            'base_dir' => 'uploads',
            'sub_dir' => 'images',
        ];

        $options = array_merge($defaultOptions, $options);

        // 使用统一的安全上传服务
        $uploadResult = FileSecurityService::secureUpload($file, $options);

        if ($uploadResult['success']) {
            // 上传成功，保存到数据库
            $fileInfo = $uploadResult['data'];

            // 获取图片尺寸信息
            $imagePath = public_path() . $fileInfo['file_path'];
            $imageInfo = @getimagesize($imagePath);

            $imageData = [
                'filename' => $fileInfo['original_name'],
                'stored_name' => $fileInfo['safe_name'],
                'file_path' => $fileInfo['file_path'],
                'file_url' => $fileInfo['file_url'],
                'file_size' => $fileInfo['file_size'],
                'mime_type' => $fileInfo['mime_type'],
                'extension' => $fileInfo['extension'],
                'width' => $imageInfo ? $imageInfo[0] : 0,
                'height' => $imageInfo ? $imageInfo[1] : 0,
                'group_id' => $options['group_id'] ?? 1,
                'alt_text' => $options['alt_text'] ?? '',
                'tags' => $options['tags'] ?? [],
                'upload_ip' => request()->ip(),
                'user_agent' => request()->header('User-Agent'),
                'status' => 1,
            ];

            try {
                $image = self::create($imageData);

                return [
                    'success' => true,
                    'message' => '图片上传成功',
                    'data' => [
                        'id' => $image->id,
                        'file_url' => $fileInfo['file_url'],
                        'full_url' => $fileInfo['full_url'],
                        'file_info' => $fileInfo,
                        'image_info' => $imageData,
                    ]
                ];

            } catch (\Exception $e) {
                // 数据库保存失败，删除已上传的文件
                $filePath = public_path() . $fileInfo['file_path'];
                if (file_exists($filePath)) {
                    @unlink($filePath);
                }

                return [
                    'success' => false,
                    'message' => '数据库保存失败: ' . $e->getMessage(),
                    'data' => null
                ];
            }
        }

        return $uploadResult;
    }
    
    /**
     * 生成安全的文件名
     * @deprecated 建议使用 FileSecurityService 中的文件名生成功能
     * @param string $originalName 原始文件名
     * @param string|null $extension 文件扩展名
     * @return string 安全的文件名
     */
    public static function generateSafeName($originalName, $extension = null)
    {
        // 为了向后兼容，保留此方法
        if (!$extension) {
            $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        }

        $timestamp = date('YmdHis');
        $random = substr(md5(uniqid(mt_rand(), true)), 0, 8);

        return $timestamp . '_' . $random . '.' . strtolower($extension);
    }

    /**
     * 获取存储路径
     * @param string $groupSlug 分组标识
     * @return string 存储路径
     */
    public static function getStoragePath($groupSlug = 'default')
    {
        $date = date('Y/m/d');
        return "uploads/images/{$groupSlug}/{$date}";
    }

    /**
     * 获取文件安全报告
     * @param string $filePath 文件路径
     * @return array 安全报告
     */
    public static function getSecurityReport($filePath)
    {
        return FileSecurityService::getSecurityReport($filePath);
    }

    /**
     * 批量验证图片文件
     * @param array $files 文件数组
     * @param array $options 验证选项
     * @return array 验证结果
     */
    public static function batchValidate($files, $options = [])
    {
        $defaultOptions = [
            'check_extension' => true,
            'check_mime' => true,
            'check_size' => true,
            'check_magic' => true,
            'check_content' => true,
            'check_image' => true,
            'strict_mode' => true,
        ];

        $options = array_merge($defaultOptions, $options);
        return FileSecurityService::validateMultipleFiles($files, $options);
    }

    /**
     * 清理恶意图片文件
     * @param string $filePath 文件路径
     * @return array 清理结果
     */
    public static function cleanMaliciousFile($filePath)
    {
        return FileSecurityService::cleanMaliciousFile($filePath);
    }

    /**
     * 获取安全的图片列表
     * @param array $where 查询条件
     * @return \think\Collection 安全的图片列表
     */
    public static function getSafeImages($where = [])
    {
        $images = self::where($where)->select();

        return $images->filter(function($image) {
            return $image->isSafe();
        });
    }

    /**
     * 批量安全检查现有图片
     * @param int $limit 每次检查数量
     * @return array 检查结果
     */
    public static function batchSecurityCheck($limit = 50)
    {
        $images = self::limit($limit)->select();
        $results = [
            'total' => $images->count(),
            'safe' => 0,
            'unsafe' => 0,
            'missing' => 0,
            'details' => []
        ];

        foreach ($images as $image) {
            $status = $image->getSecurityStatusAttr();
            $results['details'][] = [
                'id' => $image->id,
                'filename' => $image->filename,
                'file_path' => $image->file_path,
                'status' => $status
            ];

            switch ($status) {
                case 'low':
                case 'safe':
                    $results['safe']++;
                    break;
                case 'missing':
                    $results['missing']++;
                    break;
                default:
                    $results['unsafe']++;
                    break;
            }
        }

        return $results;
    }

    /**
     * 获取图片统计信息
     * @return array 统计信息
     */
    public static function getStatistics()
    {
        $total = self::count();
        $byStatus = self::field('status, COUNT(*) as count')
            ->group('status')
            ->select()
            ->toArray();

        $byExtension = self::field('extension, COUNT(*) as count')
            ->group('extension')
            ->select()
            ->toArray();

        $totalSize = self::sum('file_size');

        return [
            'total_images' => $total,
            'total_size' => $totalSize,
            'total_size_text' => self::formatBytes($totalSize),
            'by_status' => $byStatus,
            'by_extension' => $byExtension,
            'average_size' => $total > 0 ? round($totalSize / $total, 2) : 0,
        ];
    }

    /**
     * 格式化字节数
     * @param int $bytes 字节数
     * @return string 格式化后的大小
     */
    private static function formatBytes($bytes)
    {
        if ($bytes === 0) return '0 B';

        $k = 1024;
        $sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        $i = floor(log($bytes) / log($k));

        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }

    // 删除图片文件
    public function deleteFile()
    {
        $filePath = public_path() . $this->file_path;
        if (file_exists($filePath)) {
            return unlink($filePath);
        }
        return true;
    }
    
    // 模型事件 - 删除时同步删除文件
    public static function onAfterDelete($image)
    {
        $image->deleteFile();
        
        // 更新分组图片数量
        if ($image->group_id) {
            ImageGroup::where('id', $image->group_id)->dec('image_count');
        }
    }
    
    // 模型事件 - 创建时更新分组数量
    public static function onAfterInsert($image)
    {
        if ($image->group_id) {
            ImageGroup::where('id', $image->group_id)->inc('image_count');
        }
    }
    
    // 模型事件 - 更新分组时调整数量
    public static function onAfterUpdate($image)
    {
        $changedData = $image->getChangedData();
        
        if (isset($changedData['group_id'])) {
            $oldGroupId = $image->getOrigin('group_id');
            $newGroupId = $changedData['group_id'];
            
            // 旧分组减1
            if ($oldGroupId) {
                ImageGroup::where('id', $oldGroupId)->dec('image_count');
            }
            
            // 新分组加1
            if ($newGroupId) {
                ImageGroup::where('id', $newGroupId)->inc('image_count');
            }
        }
    }
} 
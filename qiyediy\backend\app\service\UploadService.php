<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * QiyeDIY企业建站系统 - 文件上传服务
 */

declare(strict_types=1);

namespace app\service;

use think\facade\Filesystem;
use think\facade\Config;
use think\file\UploadedFile;
use Intervention\Image\ImageManagerStatic as Image;

/**
 * 文件上传服务类
 */
class UploadService
{
    // 允许的图片类型
    protected array $allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
    
    // 允许的视频类型
    protected array $allowedVideoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
    
    // 允许的文档类型
    protected array $allowedDocumentTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];

    /**
     * 上传图片
     * @param UploadedFile $file 上传文件
     * @return array
     * @throws \Exception
     */
    public function uploadImage(UploadedFile $file): array
    {
        // 验证文件类型
        $this->validateImageFile($file);

        // 生成文件名和路径
        $filename = $this->generateFilename($file);
        $path = 'images/' . date('Y/m/d');
        
        // 保存文件
        $savePath = Filesystem::disk('public')->putFileAs($path, $file, $filename);
        
        // 获取文件信息
        $fileInfo = $this->getUploadedFileInfo($file, $savePath);
        
        // 生成缩略图
        $this->generateImageThumbnail($savePath);
        
        return $fileInfo;
    }

    /**
     * 上传视频
     * @param UploadedFile $file 上传文件
     * @return array
     * @throws \Exception
     */
    public function uploadVideo(UploadedFile $file): array
    {
        // 验证文件类型
        $this->validateVideoFile($file);

        // 生成文件名和路径
        $filename = $this->generateFilename($file);
        $path = 'videos/' . date('Y/m/d');
        
        // 保存文件
        $savePath = Filesystem::disk('public')->putFileAs($path, $file, $filename);
        
        // 获取文件信息
        $fileInfo = $this->getUploadedFileInfo($file, $savePath);
        
        return $fileInfo;
    }

    /**
     * 上传文档
     * @param UploadedFile $file 上传文件
     * @return array
     * @throws \Exception
     */
    public function uploadDocument(UploadedFile $file): array
    {
        // 验证文件类型
        $this->validateDocumentFile($file);

        // 生成文件名和路径
        $filename = $this->generateFilename($file);
        $path = 'documents/' . date('Y/m/d');
        
        // 保存文件
        $savePath = Filesystem::disk('public')->putFileAs($path, $file, $filename);
        
        // 获取文件信息
        $fileInfo = $this->getUploadedFileInfo($file, $savePath);
        
        return $fileInfo;
    }

    /**
     * 上传头像
     * @param UploadedFile $file 上传文件
     * @return array
     * @throws \Exception
     */
    public function uploadAvatar(UploadedFile $file): array
    {
        // 验证文件类型
        $this->validateImageFile($file);

        // 生成文件名和路径
        $filename = $this->generateFilename($file);
        $path = 'avatars/' . date('Y/m');
        
        // 保存文件
        $savePath = Filesystem::disk('public')->putFileAs($path, $file, $filename);
        
        // 获取文件信息
        $fileInfo = $this->getUploadedFileInfo($file, $savePath);
        
        // 生成头像缩略图
        $this->generateAvatarThumbnail($savePath);
        
        return $fileInfo;
    }

    /**
     * 批量上传
     * @param array $files 文件数组
     * @return array
     */
    public function batchUpload(array $files): array
    {
        $results = [
            'success' => [],
            'failed' => [],
            'total' => count($files)
        ];

        foreach ($files as $index => $file) {
            try {
                if (!$file instanceof UploadedFile) {
                    throw new \Exception('无效的文件');
                }

                // 根据文件类型选择上传方法
                $extension = strtolower($file->getOriginalExtension());
                
                if (in_array($extension, $this->allowedImageTypes)) {
                    $result = $this->uploadImage($file);
                } elseif (in_array($extension, $this->allowedVideoTypes)) {
                    $result = $this->uploadVideo($file);
                } elseif (in_array($extension, $this->allowedDocumentTypes)) {
                    $result = $this->uploadDocument($file);
                } else {
                    throw new \Exception('不支持的文件类型');
                }

                $results['success'][] = $result;

            } catch (\Exception $e) {
                $results['failed'][] = [
                    'index' => $index,
                    'filename' => $file->getOriginalName(),
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * Base64上传
     * @param string $data Base64数据
     * @param string $type 文件类型
     * @return array
     * @throws \Exception
     */
    public function uploadBase64(string $data, string $type = 'image'): array
    {
        // 解析Base64数据
        if (!preg_match('/^data:([^;]+);base64,(.+)$/', $data, $matches)) {
            throw new \Exception('无效的Base64数据');
        }

        $mimeType = $matches[1];
        $base64Data = $matches[2];
        
        // 验证MIME类型
        $extension = $this->getExtensionFromMimeType($mimeType);
        if (!$extension) {
            throw new \Exception('不支持的文件类型');
        }

        // 解码数据
        $fileData = base64_decode($base64Data);
        if ($fileData === false) {
            throw new \Exception('Base64解码失败');
        }

        // 生成文件名和路径
        $filename = uniqid() . '.' . $extension;
        $path = $type . 's/' . date('Y/m/d');
        $fullPath = $path . '/' . $filename;

        // 保存文件
        $saved = Filesystem::disk('public')->put($fullPath, $fileData);
        if (!$saved) {
            throw new \Exception('文件保存失败');
        }

        // 获取文件信息
        $fileInfo = [
            'filename' => $filename,
            'original_name' => $filename,
            'path' => $fullPath,
            'url' => $this->getFileUrl($fullPath),
            'size' => strlen($fileData),
            'mime_type' => $mimeType,
            'extension' => $extension,
            'type' => $type,
            'created_at' => date('Y-m-d H:i:s')
        ];

        // 如果是图片，生成缩略图
        if ($type === 'image') {
            $this->generateImageThumbnail($fullPath);
        }

        return $fileInfo;
    }

    /**
     * 删除文件
     * @param string $path 文件路径
     * @return bool
     */
    public function deleteFile(string $path): bool
    {
        try {
            // 删除主文件
            $deleted = Filesystem::disk('public')->delete($path);
            
            // 删除缩略图
            $this->deleteThumbnails($path);
            
            return $deleted;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取文件信息
     * @param string $path 文件路径
     * @return array
     * @throws \Exception
     */
    public function getFileInfo(string $path): array
    {
        if (!Filesystem::disk('public')->exists($path)) {
            throw new \Exception('文件不存在');
        }

        $fullPath = Filesystem::disk('public')->path($path);
        $size = filesize($fullPath);
        $mimeType = mime_content_type($fullPath);
        
        return [
            'path' => $path,
            'url' => $this->getFileUrl($path),
            'size' => $size,
            'size_formatted' => $this->formatFileSize($size),
            'mime_type' => $mimeType,
            'extension' => pathinfo($path, PATHINFO_EXTENSION),
            'created_at' => date('Y-m-d H:i:s', filemtime($fullPath)),
            'is_image' => $this->isImageFile($mimeType),
            'is_video' => $this->isVideoFile($mimeType),
            'is_document' => $this->isDocumentFile($mimeType)
        ];
    }

    /**
     * 生成缩略图
     * @param string $path 文件路径
     * @param int $width 宽度
     * @param int $height 高度
     * @param int $quality 质量
     * @return array
     * @throws \Exception
     */
    public function generateThumbnail(string $path, int $width = 200, int $height = 200, int $quality = 80): array
    {
        if (!Filesystem::disk('public')->exists($path)) {
            throw new \Exception('文件不存在');
        }

        $fullPath = Filesystem::disk('public')->path($path);
        
        // 生成缩略图路径
        $pathInfo = pathinfo($path);
        $thumbnailPath = $pathInfo['dirname'] . '/thumb_' . $width . 'x' . $height . '_' . $pathInfo['basename'];
        $thumbnailFullPath = Filesystem::disk('public')->path($thumbnailPath);

        // 生成缩略图
        $image = Image::make($fullPath);
        $image->fit($width, $height, function ($constraint) {
            $constraint->upsize();
        });
        $image->save($thumbnailFullPath, $quality);

        return [
            'path' => $thumbnailPath,
            'url' => $this->getFileUrl($thumbnailPath),
            'width' => $width,
            'height' => $height,
            'size' => filesize($thumbnailFullPath)
        ];
    }

    /**
     * 裁剪图片
     * @param string $path 文件路径
     * @param int $x X坐标
     * @param int $y Y坐标
     * @param int $width 宽度
     * @param int $height 高度
     * @param int $quality 质量
     * @return array
     * @throws \Exception
     */
    public function cropImage(string $path, int $x, int $y, int $width, int $height, int $quality = 80): array
    {
        if (!Filesystem::disk('public')->exists($path)) {
            throw new \Exception('文件不存在');
        }

        $fullPath = Filesystem::disk('public')->path($path);
        
        // 生成裁剪后的文件路径
        $pathInfo = pathinfo($path);
        $croppedPath = $pathInfo['dirname'] . '/cropped_' . time() . '_' . $pathInfo['basename'];
        $croppedFullPath = Filesystem::disk('public')->path($croppedPath);

        // 裁剪图片
        $image = Image::make($fullPath);
        $image->crop($width, $height, $x, $y);
        $image->save($croppedFullPath, $quality);

        return [
            'path' => $croppedPath,
            'url' => $this->getFileUrl($croppedPath),
            'width' => $width,
            'height' => $height,
            'size' => filesize($croppedFullPath)
        ];
    }

    /**
     * 压缩图片
     * @param string $path 文件路径
     * @param int $quality 质量
     * @param int|null $maxWidth 最大宽度
     * @param int|null $maxHeight 最大高度
     * @return array
     * @throws \Exception
     */
    public function compressImage(string $path, int $quality = 80, ?int $maxWidth = null, ?int $maxHeight = null): array
    {
        if (!Filesystem::disk('public')->exists($path)) {
            throw new \Exception('文件不存在');
        }

        $fullPath = Filesystem::disk('public')->path($path);
        $originalSize = filesize($fullPath);
        
        // 压缩图片
        $image = Image::make($fullPath);
        
        // 如果指定了最大尺寸，进行缩放
        if ($maxWidth || $maxHeight) {
            $image->resize($maxWidth, $maxHeight, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            });
        }
        
        $image->save($fullPath, $quality);
        $newSize = filesize($fullPath);

        return [
            'path' => $path,
            'url' => $this->getFileUrl($path),
            'original_size' => $originalSize,
            'new_size' => $newSize,
            'compression_ratio' => round((1 - $newSize / $originalSize) * 100, 2),
            'quality' => $quality
        ];
    }

    /**
     * 获取上传配置
     * @return array
     */
    public function getUploadConfig(): array
    {
        return [
            'max_file_size' => Config::get('upload.max_file_size', 10 * 1024 * 1024), // 10MB
            'allowed_image_types' => $this->allowedImageTypes,
            'allowed_video_types' => $this->allowedVideoTypes,
            'allowed_document_types' => $this->allowedDocumentTypes,
            'image_max_width' => Config::get('upload.image_max_width', 2048),
            'image_max_height' => Config::get('upload.image_max_height', 2048),
            'thumbnail_sizes' => Config::get('upload.thumbnail_sizes', [
                'small' => [150, 150],
                'medium' => [300, 300],
                'large' => [600, 600]
            ])
        ];
    }

    /**
     * 获取上传统计
     * @return array
     */
    public function getUploadStatistics(): array
    {
        $publicPath = Filesystem::disk('public')->path('');
        
        // 统计各类型文件数量和大小
        $stats = [
            'images' => $this->getDirectoryStats($publicPath . 'images'),
            'videos' => $this->getDirectoryStats($publicPath . 'videos'),
            'documents' => $this->getDirectoryStats($publicPath . 'documents'),
            'avatars' => $this->getDirectoryStats($publicPath . 'avatars')
        ];

        // 计算总计
        $stats['total'] = [
            'count' => array_sum(array_column($stats, 'count')),
            'size' => array_sum(array_column($stats, 'size'))
        ];

        return $stats;
    }

    /**
     * 验证图片文件
     * @param UploadedFile $file
     * @throws \Exception
     */
    protected function validateImageFile(UploadedFile $file): void
    {
        $extension = strtolower($file->getOriginalExtension());
        if (!in_array($extension, $this->allowedImageTypes)) {
            throw new \Exception('不支持的图片格式');
        }

        $maxSize = Config::get('upload.image_max_size', 5 * 1024 * 1024); // 5MB
        if ($file->getSize() > $maxSize) {
            throw new \Exception('图片文件过大，最大允许' . $this->formatFileSize($maxSize));
        }
    }

    /**
     * 验证视频文件
     * @param UploadedFile $file
     * @throws \Exception
     */
    protected function validateVideoFile(UploadedFile $file): void
    {
        $extension = strtolower($file->getOriginalExtension());
        if (!in_array($extension, $this->allowedVideoTypes)) {
            throw new \Exception('不支持的视频格式');
        }

        $maxSize = Config::get('upload.video_max_size', 100 * 1024 * 1024); // 100MB
        if ($file->getSize() > $maxSize) {
            throw new \Exception('视频文件过大，最大允许' . $this->formatFileSize($maxSize));
        }
    }

    /**
     * 验证文档文件
     * @param UploadedFile $file
     * @throws \Exception
     */
    protected function validateDocumentFile(UploadedFile $file): void
    {
        $extension = strtolower($file->getOriginalExtension());
        if (!in_array($extension, $this->allowedDocumentTypes)) {
            throw new \Exception('不支持的文档格式');
        }

        $maxSize = Config::get('upload.document_max_size', 20 * 1024 * 1024); // 20MB
        if ($file->getSize() > $maxSize) {
            throw new \Exception('文档文件过大，最大允许' . $this->formatFileSize($maxSize));
        }
    }

    /**
     * 生成文件名
     * @param UploadedFile $file
     * @return string
     */
    protected function generateFilename(UploadedFile $file): string
    {
        $extension = $file->getOriginalExtension();
        return date('YmdHis') . '_' . uniqid() . '.' . $extension;
    }

    /**
     * 获取上传文件信息
     * @param UploadedFile $file
     * @param string $savePath
     * @return array
     */
    protected function getUploadedFileInfo(UploadedFile $file, string $savePath): array
    {
        return [
            'filename' => basename($savePath),
            'original_name' => $file->getOriginalName(),
            'path' => $savePath,
            'url' => $this->getFileUrl($savePath),
            'size' => $file->getSize(),
            'size_formatted' => $this->formatFileSize($file->getSize()),
            'mime_type' => $file->getOriginalMime(),
            'extension' => $file->getOriginalExtension(),
            'type' => $this->getFileType($file->getOriginalMime()),
            'created_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * 生成图片缩略图
     * @param string $path
     */
    protected function generateImageThumbnail(string $path): void
    {
        $thumbnailSizes = Config::get('upload.thumbnail_sizes', [
            'small' => [150, 150],
            'medium' => [300, 300]
        ]);

        foreach ($thumbnailSizes as $size => $dimensions) {
            try {
                $this->generateThumbnail($path, $dimensions[0], $dimensions[1]);
            } catch (\Exception $e) {
                // 忽略缩略图生成错误
            }
        }
    }

    /**
     * 生成头像缩略图
     * @param string $path
     */
    protected function generateAvatarThumbnail(string $path): void
    {
        $avatarSizes = [
            [50, 50],   // 小头像
            [100, 100], // 中头像
            [200, 200]  // 大头像
        ];

        foreach ($avatarSizes as $size) {
            try {
                $this->generateThumbnail($path, $size[0], $size[1]);
            } catch (\Exception $e) {
                // 忽略缩略图生成错误
            }
        }
    }

    /**
     * 删除缩略图
     * @param string $path
     */
    protected function deleteThumbnails(string $path): void
    {
        $pathInfo = pathinfo($path);
        $directory = $pathInfo['dirname'];
        $filename = $pathInfo['filename'];
        $extension = $pathInfo['extension'];

        // 删除各种尺寸的缩略图
        $patterns = [
            'thumb_*_' . $filename . '.' . $extension,
            'cropped_*_' . $filename . '.' . $extension
        ];

        foreach ($patterns as $pattern) {
            $files = glob(Filesystem::disk('public')->path($directory) . '/' . $pattern);
            foreach ($files as $file) {
                @unlink($file);
            }
        }
    }

    /**
     * 获取文件URL
     * @param string $path
     * @return string
     */
    protected function getFileUrl(string $path): string
    {
        return Config::get('app.app_host', 'http://localhost:8000') . '/storage/' . $path;
    }

    /**
     * 格式化文件大小
     * @param int $size
     * @return string
     */
    protected function formatFileSize(int $size): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $unit = 0;
        
        while ($size >= 1024 && $unit < count($units) - 1) {
            $size /= 1024;
            $unit++;
        }
        
        return round($size, 2) . ' ' . $units[$unit];
    }

    /**
     * 获取文件类型
     * @param string $mimeType
     * @return string
     */
    protected function getFileType(string $mimeType): string
    {
        if ($this->isImageFile($mimeType)) {
            return 'image';
        } elseif ($this->isVideoFile($mimeType)) {
            return 'video';
        } elseif ($this->isDocumentFile($mimeType)) {
            return 'document';
        }
        
        return 'other';
    }

    /**
     * 检查是否为图片文件
     * @param string $mimeType
     * @return bool
     */
    protected function isImageFile(string $mimeType): bool
    {
        return strpos($mimeType, 'image/') === 0;
    }

    /**
     * 检查是否为视频文件
     * @param string $mimeType
     * @return bool
     */
    protected function isVideoFile(string $mimeType): bool
    {
        return strpos($mimeType, 'video/') === 0;
    }

    /**
     * 检查是否为文档文件
     * @param string $mimeType
     * @return bool
     */
    protected function isDocumentFile(string $mimeType): bool
    {
        $documentMimes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'text/plain'
        ];
        
        return in_array($mimeType, $documentMimes);
    }

    /**
     * 从MIME类型获取扩展名
     * @param string $mimeType
     * @return string|null
     */
    protected function getExtensionFromMimeType(string $mimeType): ?string
    {
        $mimeMap = [
            'image/jpeg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp',
            'image/bmp' => 'bmp',
            'video/mp4' => 'mp4',
            'video/avi' => 'avi',
            'video/quicktime' => 'mov',
            'application/pdf' => 'pdf',
            'text/plain' => 'txt'
        ];
        
        return $mimeMap[$mimeType] ?? null;
    }

    /**
     * 获取目录统计信息
     * @param string $directory
     * @return array
     */
    protected function getDirectoryStats(string $directory): array
    {
        if (!is_dir($directory)) {
            return ['count' => 0, 'size' => 0];
        }

        $count = 0;
        $size = 0;
        
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($directory, \RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $count++;
                $size += $file->getSize();
            }
        }
        
        return [
            'count' => $count,
            'size' => $size,
            'size_formatted' => $this->formatFileSize($size)
        ];
    }
}

<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * 安全验证器 - ThinkPHP6企业级应用
 * 功能：统一安全验证规则、文件上传验证、SQL注入检测
 */

namespace app\validate;

use think\Validate;
use think\facade\Config;
use app\service\FileSecurityService;

class SecurityValidate extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'file' => 'require|checkFileSecure',
        'content' => 'checkSqlInjection|checkXss',
        'url' => 'url|checkUrlSafe',
        'email' => 'email|checkEmailSafe',
        'phone' => 'mobile|checkPhoneSafe',
        'password' => 'require|length:8,32|checkPasswordStrength',
        'username' => 'require|alphaNum|length:3,20|checkUsernameSafe',
    ];

    /**
     * 验证消息
     */
    protected $message = [
        'file.require' => '请选择要上传的文件',
        'file.checkFileSecure' => '文件安全检查失败',
        'content.checkSqlInjection' => '内容包含危险的SQL代码',
        'content.checkXss' => '内容包含危险的脚本代码',
        'url.url' => 'URL格式不正确',
        'url.checkUrlSafe' => 'URL包含危险内容',
        'email.email' => '邮箱格式不正确',
        'email.checkEmailSafe' => '邮箱包含危险字符',
        'phone.mobile' => '手机号格式不正确',
        'phone.checkPhoneSafe' => '手机号包含危险字符',
        'password.require' => '密码不能为空',
        'password.length' => '密码长度必须在8-32位之间',
        'password.checkPasswordStrength' => '密码强度不够',
        'username.require' => '用户名不能为空',
        'username.alphaNum' => '用户名只能包含字母和数字',
        'username.length' => '用户名长度必须在3-20位之间',
        'username.checkUsernameSafe' => '用户名包含危险字符',
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        'upload' => ['file'],
        'content' => ['content'],
        'user' => ['username', 'password', 'email'],
        'contact' => ['content', 'email', 'phone'],
    ];

    /**
     * 文件安全检查
     */
    protected function checkFileSecure($value, $rule, $data = [])
    {
        if (!$value || !is_object($value)) {
            return '无效的文件对象';
        }

        // 使用FileSecurityService进行安全检查
        $result = FileSecurityService::validateFile($value);
        
        if (!$result['valid']) {
            return implode(', ', $result['errors']);
        }

        return true;
    }

    /**
     * SQL注入检测
     */
    protected function checkSqlInjection($value, $rule, $data = [])
    {
        if (!is_string($value)) {
            return true;
        }

        $dangerousPatterns = [
            '/union\s+select/i',
            '/drop\s+table/i',
            '/delete\s+from/i',
            '/insert\s+into/i',
            '/update\s+set/i',
            '/exec\s*\(/i',
            '/execute\s*\(/i',
            '/script\s*:/i',
            '/javascript\s*:/i',
            '/vbscript\s*:/i',
            '/onload\s*=/i',
            '/onerror\s*=/i',
            '/onclick\s*=/i',
        ];

        foreach ($dangerousPatterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return false;
            }
        }

        return true;
    }

    /**
     * XSS检测
     */
    protected function checkXss($value, $rule, $data = [])
    {
        if (!is_string($value)) {
            return true;
        }

        $xssPatterns = [
            '/<script[^>]*>.*?<\/script>/is',
            '/<iframe[^>]*>.*?<\/iframe>/is',
            '/<object[^>]*>.*?<\/object>/is',
            '/<embed[^>]*>.*?<\/embed>/is',
            '/<applet[^>]*>.*?<\/applet>/is',
            '/<meta[^>]*>/is',
            '/<link[^>]*>/is',
            '/javascript\s*:/i',
            '/vbscript\s*:/i',
            '/data\s*:/i',
            '/on\w+\s*=/i',
        ];

        foreach ($xssPatterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return false;
            }
        }

        return true;
    }

    /**
     * URL安全检查
     */
    protected function checkUrlSafe($value, $rule, $data = [])
    {
        if (!is_string($value)) {
            return true;
        }

        // 检查危险协议
        $dangerousProtocols = [
            'javascript:', 'vbscript:', 'data:', 'file:', 'ftp:'
        ];

        $lowerValue = strtolower($value);
        foreach ($dangerousProtocols as $protocol) {
            if (strpos($lowerValue, $protocol) === 0) {
                return false;
            }
        }

        // 检查危险字符
        if (preg_match('/[<>"\']/', $value)) {
            return false;
        }

        return true;
    }

    /**
     * 邮箱安全检查
     */
    protected function checkEmailSafe($value, $rule, $data = [])
    {
        if (!is_string($value)) {
            return true;
        }

        // 检查危险字符
        if (preg_match('/[<>"\';]/', $value)) {
            return false;
        }

        // 检查长度
        if (strlen($value) > 100) {
            return '邮箱长度不能超过100个字符';
        }

        return true;
    }

    /**
     * 手机号安全检查
     */
    protected function checkPhoneSafe($value, $rule, $data = [])
    {
        if (!is_string($value)) {
            return true;
        }

        // 只允许数字、加号、减号、空格、括号
        if (!preg_match('/^[0-9+\-\s()]+$/', $value)) {
            return false;
        }

        // 检查长度
        if (strlen($value) > 20) {
            return '手机号长度不能超过20个字符';
        }

        return true;
    }

    /**
     * 密码强度检查
     */
    protected function checkPasswordStrength($value, $rule, $data = [])
    {
        if (!is_string($value)) {
            return '密码必须是字符串';
        }

        $config = Config::get('security.password', []);
        
        // 检查最小长度
        $minLength = $config['min_length'] ?? 8;
        if (strlen($value) < $minLength) {
            return "密码长度不能少于{$minLength}位";
        }

        // 检查最大长度
        $maxLength = $config['max_length'] ?? 32;
        if (strlen($value) > $maxLength) {
            return "密码长度不能超过{$maxLength}位";
        }

        // 检查是否包含数字
        if (($config['require_number'] ?? true) && !preg_match('/\d/', $value)) {
            return '密码必须包含至少一个数字';
        }

        // 检查是否包含字母
        if (($config['require_letter'] ?? true) && !preg_match('/[a-zA-Z]/', $value)) {
            return '密码必须包含至少一个字母';
        }

        // 检查是否包含特殊字符
        if (($config['require_special'] ?? false) && !preg_match('/[!@#$%^&*(),.?":{}|<>]/', $value)) {
            return '密码必须包含至少一个特殊字符';
        }

        return true;
    }

    /**
     * 用户名安全检查
     */
    protected function checkUsernameSafe($value, $rule, $data = [])
    {
        if (!is_string($value)) {
            return true;
        }

        // 检查危险字符
        if (preg_match('/[<>"\';\\\\\/]/', $value)) {
            return false;
        }

        // 检查是否以数字开头
        if (preg_match('/^\d/', $value)) {
            return '用户名不能以数字开头';
        }

        // 检查保留关键词
        $reservedWords = ['admin', 'root', 'system', 'test', 'guest', 'null', 'undefined'];
        if (in_array(strtolower($value), $reservedWords)) {
            return '用户名不能使用保留关键词';
        }

        return true;
    }

    /**
     * 批量验证数据安全性
     */
    public static function validateDataSecurity($data, $rules = [])
    {
        $validator = new self();
        $errors = [];

        foreach ($data as $key => $value) {
            // 检查键名安全性
            if (!$validator->checkFieldNameSafe($key)) {
                $errors[$key][] = '字段名包含危险字符';
            }

            // 根据规则验证值
            if (isset($rules[$key])) {
                $rule = $rules[$key];
                if (is_string($rule)) {
                    $methods = explode('|', $rule);
                    foreach ($methods as $method) {
                        if (method_exists($validator, $method)) {
                            $result = $validator->$method($value, $method, $data);
                            if ($result !== true) {
                                $errors[$key][] = $result;
                            }
                        }
                    }
                }
            }

            // 默认安全检查
            if (is_string($value)) {
                if (!$validator->checkSqlInjection($value, '', $data)) {
                    $errors[$key][] = '包含危险的SQL代码';
                }
                if (!$validator->checkXss($value, '', $data)) {
                    $errors[$key][] = '包含危险的脚本代码';
                }
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * 检查字段名安全性
     */
    protected function checkFieldNameSafe($fieldName)
    {
        // 只允许字母、数字、下划线
        return preg_match('/^[a-zA-Z_][a-zA-Z0-9_]*$/', $fieldName);
    }

    /**
     * 生成安全的随机字符串
     */
    public static function generateSecureToken($length = 32)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $token = '';
        
        for ($i = 0; $i < $length; $i++) {
            $token .= $characters[random_int(0, strlen($characters) - 1)];
        }
        
        return $token;
    }

    /**
     * 安全的密码哈希
     */
    public static function hashPassword($password)
    {
        $config = Config::get('security.password', []);
        $algo = $config['hash_algo'] ?? 'bcrypt';
        $cost = $config['hash_cost'] ?? 12;

        switch ($algo) {
            case 'bcrypt':
                return password_hash($password, PASSWORD_BCRYPT, ['cost' => $cost]);
            case 'argon2i':
                return password_hash($password, PASSWORD_ARGON2I);
            case 'argon2id':
                return password_hash($password, PASSWORD_ARGON2ID);
            default:
                return password_hash($password, PASSWORD_DEFAULT);
        }
    }

    /**
     * 验证密码
     */
    public static function verifyPassword($password, $hash)
    {
        return password_verify($password, $hash);
    }
}

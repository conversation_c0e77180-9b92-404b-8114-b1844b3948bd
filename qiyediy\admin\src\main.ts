/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 管理后台入口文件
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

import App from './App.vue'
import router from './router'
import { setupDirectives } from './directives'
import { setupGlobalComponents } from './components'

// 样式文件
import '@/styles/index.scss'
import 'uno.css'

// 创建应用实例
const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 安装插件
app.use(createPinia())
app.use(router)
app.use(ElementPlus, {
  size: 'default',
  zIndex: 3000
})

// 设置全局组件
setupGlobalComponents(app)

// 设置全局指令
setupDirectives(app)

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err, info)
  ElMessage.error('系统发生错误，请稍后重试')
}

// 全局警告处理
app.config.warnHandler = (msg, vm, trace) => {
  console.warn('全局警告:', msg, trace)
}

// 挂载应用
app.mount('#app')

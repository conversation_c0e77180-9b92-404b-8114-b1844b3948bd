diy-progress-tracker.md  当前进度

docs\current-status-summary.md  当前状态总

方式一：直接指令式
//--------------//
"查看当前项目状态"
"检查开发进度" 
"项目现状如何"
//-------------//
方式二：具体功能询问
//-----------------//
"DIY功能现在什么状态"
"后台管理有什么问题"
"前端页面工作正常吗"
//---------------//
方式三：问题导向
//-----------------//
"现在还有什么需要完善的"
"用户反馈了什么问题"
"哪些功能需要优化"
//-----------------//
自动读取 docs/development-summary.md 了解整体状态
快速扫描 最近修改的文件和功能
检查 是否有未完成的TODO或问题
总结 当前可用功能和待优化项目

推荐话术
"项目检查" 或 "项目状态"

”分析这个项目的整体结构和技术架构“

//-------------------//
启用CSRF保护
完善文件上传验证
加强SQL注入防护
//-------------------//

###开发新功能话术
//-------------------------//
增加个解决方案 功能  借鉴产品管理的样式  和编辑器 和上传图片的等功能 要严格按照系统风格 和文件写发去制作
//------------------------//
启动服务：`php think run`

# 执行所有未执行的迁移
php think migrate:run

# 执行指定的迁移文件
php think migrate:run --target=20250610

# 回滚最后一次迁移
php think migrate:rollback

# 回滚到指定版本
php think migrate:rollback --target=20250607

# 查看迁移状态
php think migrate:status



1. Pixabay中国镜像
网址：https://pixabay.com/zh/
特点：免费商用，高质量图片，有中文界面
2. Unsplash
网址：https://unsplash.com/
特点：专业摄影师作品，免费商用
3. Pexels
网址：https://www.pexels.com/zh-cn/
特点：免费商用，有中文版
4. StockVault
网址：https://www.stockvault.net/
特点：免费图片和图形资源

安装npx 
npm install -g npx

# 1. 文件系统管理（最实用）
npm install -g @modelcontextprotocol/server-filesystem

# 2. 上下文分析（项目理解）
npm install -g @upstash/context7-mcp

# 3. 内存管理（性能优化）
npm install -g @modelcontextprotocol/server-memory

# 4. 基础MCP服务器
npm install -g mcp-server

# 5. 数据库工具（如果需要PostgreSQL支持）
npm install -g @modelcontextprotocol/server-postgres

1. Context 7 ⭐⭐⭐⭐⭐
功能：包文档分析和上下文理解
用途：ThinkPHP6项目架构分析、代码关系理解
2. Desktop Commander ⭐⭐⭐⭐⭐
功能：终端命令执行和文件管理
用途：项目文件操作、开发工具集成
3. Sequential thinking ⭐⭐⭐⭐⭐
功能：复杂问题逐步分析
用途：业务逻辑设计、架构决策分析
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["@modelcontextprotocol/server-filesystem"]
    },
    "context7": {
      "command": "npx", 
      "args": ["@upstash/context7-mcp"]
    },
    "memory": {
      "command": "npx",
      "args": ["@modelcontextprotocol/server-memory"]
    }
  }
}

Name: Sequential thinking
Command: mcp-server-sequential-thinking
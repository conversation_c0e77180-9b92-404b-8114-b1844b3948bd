<?php /*a:4:{s:58:"D:\EServer\core\www\san.com\app\view\solutions\detail.html";i:1749772337;s:55:"D:\EServer\core\www\san.com\app\view\common\header.html";i:1749772617;s:55:"D:\EServer\core\www\san.com\app\view\common\styles.html";i:1748797665;s:55:"D:\EServer\core\www\san.com\app\view\common\footer.html";i:1749617057;}*/ ?>
<?php $pageTitle = $pageData['pageTitle']; ?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <title><?php echo htmlentities((string) (isset($pageTitle) && ($pageTitle !== '')?$pageTitle:'首页')); ?> - <?php echo htmlentities((string) (isset($siteConfig['site_name']) && ($siteConfig['site_name'] !== '')?$siteConfig['site_name']:'三只鱼网络')); ?></title>
    <meta name="description" content="<?php echo htmlentities((string) ((isset($pageDescription) && ($pageDescription !== '')?$pageDescription:$siteConfig['site_description']) ?: '专注于为企业提供专业、可靠、高效的数字化转型解决方案')); ?>">
    <meta name="keywords" content="<?php echo htmlentities((string) ((isset($pageKeywords) && ($pageKeywords !== '')?$pageKeywords:$siteConfig['site_keywords']) ?: '数字化转型,企业解决方案,技术服务,创新科技,专业团队')); ?>">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?php echo htmlentities((string) ((isset($pageTitle) && ($pageTitle !== '')?$pageTitle:$siteConfig['site_name']) ?: '三只鱼网络')); ?>">
    <meta property="og:description" content="<?php echo htmlentities((string) ((isset($pageDescription) && ($pageDescription !== '')?$pageDescription:$siteConfig['site_description']) ?: '专注于为企业提供专业、可靠、高效的数字化转型解决方案')); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo htmlentities((string) app('request')->domain()); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo asset('assets/images/favicon.ico'); ?>">
    
    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo asset('assets/css/bootstrap.min.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset('assets/css/all.min.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset('assets/css/animate.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset('assets/css/swiper-bundle.min.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset('assets/css/style.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset('assets/css/contact.css'); ?>">

    <!-- Lucide Icons - 本地版本 -->
    <script src="<?php echo asset('assets/js/lucide.js'); ?>"></script>
    
    <!-- 导航菜单层级修复 -->
    <link rel="stylesheet" href="<?php echo asset('assets/css/nav-fix.css'); ?>">

    <!-- 导航栏样式 -->
    <style>
    /* ===== 下拉菜单基础样式 ===== */
    .dropdown-menu {
        background: #ffffff;
        border: none;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        border-radius: 12px;
        padding: 12px 0;
    }

    .dropdown-item {
        padding: 10px 20px;
        font-size: 14px;
        transition: all 0.2s ease;
        border: none;
        background: transparent;
    }

    .dropdown-item:hover {
        background: rgba(0, 123, 255, 0.08);
        color: #007bff;
    }

    /* ===== 大型下拉菜单容器 ===== */
    .mega-dropdown {
        position: relative;
    }

    .mega-dropdown .dropdown-menu {
        display: none;
        position: fixed;
        top: 80px;
        left: 50%;
        transform: translateX(-50%);
        margin: 0;
        border-radius: 16px;
        background: white;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        border: none;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        pointer-events: none;
        width: 1200px;
        max-width: 90vw;
        max-height: 80vh;
        overflow-y: auto;
    }

    /* 下拉菜单显示状态 - 通过JavaScript控制 */
    .mega-dropdown.show .dropdown-menu {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: auto !important;
    }

    /* 确保菜单内容有正确的显示状态 */
    .dropdown-menu {
        display: none;
        opacity: 0;
        visibility: hidden;
        pointer-events: none;
    }

    /* 当菜单被JavaScript激活时的样式 */
    .dropdown-menu.show,
    .dropdown:hover .dropdown-menu {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: auto !important;
    }

    /* 增加菜单和触发器之间的连接区域 */
    .mega-dropdown::before {
        content: '';
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        height: 20px;
        background: transparent;
        z-index: 9999;
        pointer-events: auto;
    }

    /* ===== 简单可靠的三角形指示器 ===== */
    .mega-dropdown .dropdown-menu::before {
        content: '';
        position: absolute;
        top: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-bottom: 8px solid white;
        z-index: 1001;
    }

    /* 三角形阴影 */
    .mega-dropdown .dropdown-menu::after {
        content: '';
        position: absolute;
        top: -9px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 9px solid transparent;
        border-right: 9px solid transparent;
        border-bottom: 9px solid rgba(0, 0, 0, 0.1);
        z-index: 1000;
    }

    /* ===== 下拉箭头指示器 ===== */
    .dropdown-toggle::after {
        display: inline-block;
        margin-left: 6px;
        vertical-align: middle;
        content: "▼";
        font-size: 10px;
        transition: transform 0.3s ease;
        line-height: 1;
    }

    .dropdown-toggle[aria-expanded="true"]::after,
    .mega-dropdown.show .dropdown-toggle::after {
        transform: rotate(180deg);
    }

    /* 解决方案菜单特殊样式 */
    .solutions-menu {
        padding: 0;
    }

    .solutions-menu .mega-menu-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 25px 30px;
        text-align: center;
        color: white;
        border-radius: 16px 16px 0 0;
    }

    .solutions-menu .mega-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 8px;
        color: white;
    }

    .solutions-menu .mega-subtitle {
        font-size: 0.9rem;
        opacity: 0.9;
        margin: 0;
        color: white;
    }

    /* 解决方案网格布局 */
    .solutions-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
        padding: 30px;
        background: white;
    }

    .solution-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 20px 15px;
        border-radius: 12px;
        transition: all 0.3s ease;
        cursor: pointer;
        text-decoration: none;
        color: inherit;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        min-height: 140px;
        justify-content: flex-start;
    }

    .solution-item:hover {
        background: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.12);
        text-decoration: none;
        color: inherit;
        border-color: rgba(102, 126, 234, 0.3);
    }

    .solution-item .solution-icon {
        width: 64px;
        height: 64px;
        background: transparent;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 12px;
        transition: all 0.3s ease;
        flex-shrink: 0;
    }

    .solution-item .solution-icon img {
        width: 48px;
        height: 48px;
        object-fit: contain;
    }

    .solution-item:hover .solution-icon {
        transform: scale(1.1);
    }

    .solution-item .solution-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 100%;
    }

    .solution-item .solution-content h4 {
        font-size: 1rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        line-height: 1.3;
    }

    .solution-item .solution-content p {
        font-size: 0.8rem;
        color: #666;
        line-height: 1.4;
        margin: 0;
    }

    /* 产品网格布局修正 */
    .products-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 25px;
        padding: 30px;
        background: white;
        min-width: 800px;
    }

    .products-column {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .product-item {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        border-radius: 10px;
        transition: all 0.3s ease;
        cursor: pointer;
        background: white;
        border: 1px solid rgba(102, 126, 234, 0.1);
        text-decoration: none;
        color: inherit;
        margin-bottom: 8px;
    }

    .product-item:hover {
        background: rgba(102, 126, 234, 0.12);
        border-color: rgba(102, 126, 234, 0.25);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
        text-decoration: none;
        color: inherit;
    }

    .product-icon {
        width: 35px;
        height: 35px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        flex-shrink: 0;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .product-icon.purple {
        background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
        color: white;
    }

    .product-icon i {
        font-size: 18px;
        color: white;
        line-height: 1;
        width: auto;
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    /* Lucide图标样式 */
    .product-icon svg {
        width: 18px;
        height: 18px;
        color: white;
        stroke: white;
        stroke-width: 2;
    }

    .product-item:hover .product-icon {
        transform: scale(1.1);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.35);
    }

    .product-item:hover .product-icon i {
        transform: translate(-50%, -50%) scale(1);
    }

    .product-name {
        font-size: 0.9rem;
        font-weight: 500;
        color: #333;
        line-height: 1.4;
        transition: color 0.3s ease;
    }

    .product-item:hover .product-name {
        color: #667eea;
    }

    .product-item-placeholder {
        padding: 12px 15px;
        color: #999;
        font-size: 0.9rem;
        text-align: center;
        font-style: italic;
    }

    /* 菜单底部 */
    .mega-menu-footer {
        text-align: center;
        padding-top: 25px;
        border-top: 1px solid rgba(0, 0, 0, 0.08);
        margin-top: 20px;
    }

    .mega-menu-footer .btn {
        padding: 12px 30px;
        font-size: 0.95rem;
        font-weight: 600;
        border-radius: 25px;
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        color: white;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.25);
    }

    .mega-menu-footer .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(0, 123, 255, 0.35);
        background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
        text-decoration: none;
        color: white;
    }

    /* ===== 产品介绍菜单样式 ===== */
    .products-menu {
        padding: 0;
        z-index: 10000 !important; /* 比解决方案菜单更高的z-index */
    }

    .products-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 30px;
        padding: 30px;
        background: white;
        min-width: 800px;
    }

    .products-column {
        display: flex;
        flex-direction: column;
    }

    .mega-menu-category {
        font-size: 1.1rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #667eea;
        text-align: center;
    }

    .product-item {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        border-radius: 8px;
        transition: all 0.3s ease;
        text-decoration: none;
        color: #333;
        margin-bottom: 8px;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
    }

    .product-item:hover {
        background: white;
        transform: translateX(5px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.15);
        text-decoration: none;
        color: #667eea;
        border-color: #667eea;
    }

    .product-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        transition: all 0.3s ease;
        flex-shrink: 0;
        position: relative;
        overflow: hidden;
    }

    .product-icon.purple {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .product-icon i {
        font-size: 18px;
        line-height: 1;
        width: auto;
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .product-item:hover .product-icon {
        transform: scale(1.1);
    }

    .product-item:hover .product-icon i {
        transform: translate(-50%, -50%) scale(1);
    }

    .product-name {
        font-size: 0.95rem;
        font-weight: 600;
        color: inherit;
    }

    .product-item-placeholder {
        padding: 12px 15px;
        color: #999;
        font-size: 0.9rem;
        text-align: center;
        font-style: italic;
    }

    /* ===== 导航栏基础样式增强 ===== */
    .header {
        position: relative;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    }

    .navbar {
        position: relative;
        z-index: 1001;
    }

    /* 确保所有下拉菜单都有足够高的z-index */
    .mega-dropdown .dropdown-menu {
        z-index: 10001 !important;
    }

    .solutions-menu {
        z-index: 10002 !important;
    }

    .products-menu {
        z-index: 10003 !important;
    }

    /* 确保三角形指示器也有正确的z-index */
    .mega-dropdown .dropdown-menu::before {
        z-index: 10004;
    }

    .mega-dropdown .dropdown-menu::after {
        z-index: 10003;
    }

    /* ===== 三级菜单样式 ===== */
    .dropdown-submenu {
        position: relative;
    }

    .dropdown-submenu .dropdown-menu {
        position: absolute;
        top: 0;
        left: 100%;
        margin-top: -1px;
        display: none;
        min-width: 200px;
    }

    .dropdown-submenu:hover .dropdown-menu {
        display: block;
    }

    .dropdown-submenu .dropdown-toggle::after {
        content: "▶";
        float: right;
        margin-top: 2px;
        border: none;
    }

    /* 响应式处理 */
    @media (max-width: 991px) {
        .dropdown-submenu .dropdown-menu {
            position: static;
            float: none;
            width: auto;
            margin-top: 0;
            background-color: #f8f9fa;
            border: none;
            box-shadow: none;
            padding-left: 20px;
        }
    }
    </style>
</head>
<body class="<?php echo htmlentities((string) (isset($bodyClass) && ($bodyClass !== '')?$bodyClass:'home-page')); ?>">
    <!-- 导航栏 -->
    <header class="header">
        <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="/">
                <img src="<?php echo asset('assets/images/logo.png'); ?>" alt="<?php echo htmlentities((string) (isset($siteConfig['site_name']) && ($siteConfig['site_name'] !== '')?$siteConfig['site_name']:'三只鱼网络')); ?>" height="40">
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto" id="main-nav">
                    <!-- 动态菜单将在这里加载 -->
                </ul>


                <!-- 右侧按钮组 -->
                <div class="navbar-actions ms-3">
                    <a href="#" class="search-btn me-2"><i data-lucide="search"></i></a>
                    <a href="/login" class="btn btn-outline-light btn-sm me-2">登录</a>
                    <a href="/register" class="btn btn-primary btn-sm">注册</a>
                </div>
            </div>
        </div>
    </nav>
</header>

    <!-- 主要内容 -->
    <main>

<script>
// 菜单加载 - 添加调试信息

// URL格式化函数
function formatUrl(url) {
    if (!url || url === '#') {
        return '#';
    }

    // 转换为字符串并清理
    url = String(url).trim();

    // 如果是完整的URL（包含协议），直接返回
    if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
    }

    // 如果已经是正确的相对路径（以/开头），直接返回
    if (url.startsWith('/')) {
        return url;
    }

    // 如果是相对路径，添加/前缀
    return '/' + url;
}

document.addEventListener('DOMContentLoaded', function() {
    loadMenus();
});

function loadMenus() {
    fetch('/api/sys-menus')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {

            if (data && data.success && data.data) {
                renderMenus(data.data);
            } else {
            }
        })
        .catch(error => {
            console.error('菜单加载失败:', error);
            console.error('错误详情:', error.message);
        });
}

function renderMenus(menus) {
    const nav = document.getElementById('main-nav');
    if (!nav) {
        console.error('找不到导航容器 #main-nav');
        return;
    }

    // 清空现有菜单（保留首页）
    const existing = nav.querySelectorAll('li:not(:first-child)');
    existing.forEach(item => item.remove());

    // 添加新菜单
    let hasMorePages = false;
    let hasContact = false;
    
    menus.forEach((menu, index) => {
        const li = createMenuItem(menu);
        nav.appendChild(li);
        
        // 检查是否已经有"更多页面"和"联系我们"菜单
        if (menu.name === '更多页面') {
            hasMorePages = true;
            // 为动态的"更多页面"菜单添加id，以便DIY页面加载
            const dropdownMenu = li.querySelector('.dropdown-menu');
            if (dropdownMenu && !dropdownMenu.id) {
                dropdownMenu.id = 'diy-pages-menu';
            }
            // 检查是否有DIY页面内容，没有的话隐藏这个菜单项
            checkDiyPagesAndToggleMenu(li);
        }
        if (menu.name === '联系我们') {
            hasContact = true;
        }
    });

    // 只有当动态菜单中没有"更多页面"时才检查是否需要添加固定的"更多页面"菜单
    if (!hasMorePages) {
        // 先检查是否有DIY页面，有的话才添加"更多页面"菜单
        checkAndAddMorePagesMenu(nav);
    }

    // 只有当动态菜单中没有"联系我们"时才添加固定的"联系我们"菜单
    if (!hasContact) {
        const contact = document.createElement('li');
        contact.className = 'nav-item';
        contact.innerHTML = '<a class="nav-link" href="/contact">联系我们</a>';
        nav.appendChild(contact);
    }


    // 初始化下拉菜单事件
    initDropdownEvents();

    // 确保Lucide图标正确渲染
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
}

function createMenuItem(menu) {
    const li = document.createElement('li');
    const url = formatUrl(menu.url || menu.link_value || '#');

    if (menu.children && menu.children.length > 0) {
        // 检查是否是特殊的mega-dropdown菜单
        if (menu.name === '解决方案') {
            return createSolutionsMenuItem(menu, url);
        } else if (menu.name === '产品介绍') {
            return createProductsMenuItem(menu, url);
        } else {
            // 普通下拉菜单
            li.className = 'nav-item dropdown';

            // 创建主链接
            const mainLink = document.createElement('a');
            mainLink.className = 'nav-link dropdown-toggle';
            mainLink.href = url;
            mainLink.setAttribute('role', 'button');
            mainLink.setAttribute('aria-expanded', 'false');
            mainLink.textContent = menu.name;

            // 创建下拉菜单
            const dropdownMenu = document.createElement('ul');
            dropdownMenu.className = 'dropdown-menu';
            dropdownMenu.innerHTML = renderSubMenus(menu.children);

            li.appendChild(mainLink);
            li.appendChild(dropdownMenu);

            // 添加悬停事件
            li.addEventListener('mouseenter', function() {
                const dropdown = li.querySelector('.dropdown-menu');
                if (dropdown) {
                    dropdown.style.display = 'block';
                    li.classList.add('show');
                }
            });

            li.addEventListener('mouseleave', function() {
                const dropdown = li.querySelector('.dropdown-menu');
                if (dropdown) {
                    dropdown.style.display = 'none';
                    li.classList.remove('show');
                }
            });

        }
    } else {
        // 普通菜单项
        li.className = 'nav-item';

        const link = document.createElement('a');
        link.className = 'nav-link';
        link.href = url;
        link.textContent = menu.name;
        li.appendChild(link);
    }

    return li;
}

// 创建解决方案菜单项
function createSolutionsMenuItem(menu, url) {
    const li = document.createElement('li');
    li.className = 'nav-item mega-dropdown';

    const mainLink = document.createElement('a');
    mainLink.className = 'nav-link dropdown-toggle';
    mainLink.href = url;
    mainLink.id = 'solutionsDropdown';
    mainLink.setAttribute('role', 'button');
    mainLink.setAttribute('aria-expanded', 'false');
    mainLink.textContent = menu.name;

    const dropdownMenu = document.createElement('div');
    dropdownMenu.className = 'dropdown-menu solutions-menu';
    
    // 动态生成解决方案网格
    let solutionsHtml = '<div class="solutions-grid">';
    
    if (menu.children && menu.children.length > 0) {
        // 使用真实的子菜单数据
        menu.children.forEach(solution => {
            const solutionUrl = formatUrl(solution.url || solution.link_value || '#');
            const iconPath = solution.icon || '/assets/images/nav/default.png';
            const description = solution.description || solution.remark || '专业的解决方案';
            
            solutionsHtml += `
                <a href="${solutionUrl}" class="solution-item">
                    <div class="solution-icon">
                        <img src="${iconPath}" alt="${solution.name}">
                    </div>
                    <div class="solution-content">
                        <h4>${solution.name}</h4>
                        <p>${description}</p>
                    </div>
                </a>
            `;
        });
    }
    
    solutionsHtml += '</div>';
    dropdownMenu.innerHTML = solutionsHtml;

    li.appendChild(mainLink);
    li.appendChild(dropdownMenu);

    // 添加mega-dropdown悬停事件
    addMegaDropdownEvents(li);

    return li;
}

// 创建产品介绍菜单项
function createProductsMenuItem(menu, url) {
    const li = document.createElement('li');
    li.className = 'nav-item mega-dropdown';

    const mainLink = document.createElement('a');
    mainLink.className = 'nav-link dropdown-toggle';
    mainLink.href = url;
    mainLink.id = 'productsDropdown';
    mainLink.setAttribute('role', 'button');
    mainLink.setAttribute('aria-expanded', 'false');
    mainLink.textContent = menu.name;

    const dropdownMenu = document.createElement('div');
    dropdownMenu.className = 'dropdown-menu products-menu';
    
    // 动态生成产品网格
    let productsHtml = '<div class="products-grid">';
    
    if (menu.children && menu.children.length > 0) {
        // 使用真实的子菜单数据，每个子菜单作为一个分类
        menu.children.forEach(category => {
            productsHtml += '<div class="products-column">';
            productsHtml += `<h6 class="mega-menu-category">${category.name}</h6>`;
            
            // 如果分类有子项目（产品），显示产品列表
            if (category.children && category.children.length > 0) {
                category.children.forEach(product => {
                    const productUrl = formatUrl(product.url || product.link_value || '#');
                    const productIcon = product.icon || 'fas fa-box';
                    
                    productsHtml += `
                        <a href="${productUrl}" class="product-item">
                            <div class="product-icon purple">
                                <i class="${productIcon}"></i>
                            </div>
                            <span class="product-name">${product.name}</span>
                        </a>
                    `;
                });
            } else {
                // 如果分类没有子产品，将分类本身作为产品显示
                const categoryUrl = formatUrl(category.url || category.link_value || '#');
                const categoryIcon = category.icon || 'fas fa-box';
                
                productsHtml += `
                    <a href="${categoryUrl}" class="product-item">
                        <div class="product-icon purple">
                            <i class="${categoryIcon}"></i>
                        </div>
                        <span class="product-name">${category.name}</span>
                    </a>
                `;
            }
            
            productsHtml += '</div>';
        });
    }
    
    productsHtml += '</div>';
    dropdownMenu.innerHTML = productsHtml;

    li.appendChild(mainLink);
    li.appendChild(dropdownMenu);

    // 添加mega-dropdown悬停事件
    addMegaDropdownEvents(li);

    return li;
}

// 添加mega-dropdown悬停事件
function addMegaDropdownEvents(dropdown) {
    const menu = dropdown.querySelector('.dropdown-menu');
    const toggle = dropdown.querySelector('.dropdown-toggle');
    let hoverTimer = null;
    let isMenuHovered = false;
    let isToggleHovered = false;
    
    // 显示菜单的函数
    function showMenu() {
        if (menu) {
            clearTimeout(hoverTimer);
            menu.style.display = 'block';
            menu.style.opacity = '1';
            menu.style.visibility = 'visible';
            menu.style.pointerEvents = 'auto';
            dropdown.classList.add('show');
            toggle.setAttribute('aria-expanded', 'true');
        }
    }
    
    // 隐藏菜单的函数
    function hideMenu() {
        if (menu) {
            hoverTimer = setTimeout(function() {
                if (!isMenuHovered && !isToggleHovered) {
                    menu.style.display = 'none';
                    menu.style.opacity = '0';
                    menu.style.visibility = 'hidden';
                    menu.style.pointerEvents = 'none';
                    dropdown.classList.remove('show');
                    toggle.setAttribute('aria-expanded', 'false');
                }
            }, 150);
        }
    }
    
    // 触发器悬停事件
    toggle.addEventListener('mouseenter', function() {
        isToggleHovered = true;
        showMenu();
    });
    
    toggle.addEventListener('mouseleave', function() {
        isToggleHovered = false;
        hideMenu();
    });
    
    // 菜单悬停事件
    if (menu) {
        menu.addEventListener('mouseenter', function() {
            isMenuHovered = true;
            clearTimeout(hoverTimer);
        });
        
        menu.addEventListener('mouseleave', function() {
            isMenuHovered = false;
            hideMenu();
        });
    }
    
    // 点击触发器时阻止默认行为
    toggle.addEventListener('click', function(e) {
        e.preventDefault();
        if (menu.style.display === 'block') {
            hideMenu();
            isToggleHovered = false;
            isMenuHovered = false;
        } else {
            showMenu();
        }
    });
}

function renderSubMenus(children) {
    const items = children.map(child => {
        const url = formatUrl(child.url || child.link_value || '#');

        const li = document.createElement('li');

        if (child.children && child.children.length > 0) {
            // 有三级菜单的二级菜单项
            li.className = 'dropdown-submenu';

            const link = document.createElement('a');
            link.className = 'dropdown-item';
            link.href = url;
            link.textContent = child.name + ' ▶';

            const submenu = document.createElement('ul');
            submenu.className = 'dropdown-menu submenu';

            child.children.forEach(grandChild => {
                const grandUrl = formatUrl(grandChild.url || grandChild.link_value || '#');
                const grandLi = document.createElement('li');
                const grandLink = document.createElement('a');
                grandLink.className = 'dropdown-item';
                grandLink.href = grandUrl;
                grandLink.textContent = grandChild.name;
                grandLi.appendChild(grandLink);
                submenu.appendChild(grandLi);

            });

            li.appendChild(link);
            li.appendChild(submenu);

        } else {
            // 普通二级菜单项
            const link = document.createElement('a');
            link.className = 'dropdown-item';
            link.href = url;
            link.textContent = child.name;
            li.appendChild(link);

        }

        return li.outerHTML;
    });

    return items.join('');
}

function initDropdownEvents() {
    // Bootstrap下拉菜单自动处理，但需要处理三级菜单
    document.querySelectorAll('.dropdown-submenu').forEach(submenu => {
        const toggle = submenu.querySelector('.dropdown-item');
        const menu = submenu.querySelector('.dropdown-menu');

        if (toggle && menu) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // 切换显示状态
                menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
            });

            // 鼠标悬停显示三级菜单
            submenu.addEventListener('mouseenter', function() {
                menu.style.display = 'block';
            });

            submenu.addEventListener('mouseleave', function() {
                menu.style.display = 'none';
            });
        }
    });

    // 点击页面其他地方时关闭所有mega-dropdown菜单
    document.addEventListener('click', function(e) {
        // 检查点击的目标是否在下拉菜单内
        const isDropdownClick = e.target.closest('.mega-dropdown');
        
        if (!isDropdownClick) {
            const megaDropdowns = document.querySelectorAll('.mega-dropdown');
            megaDropdowns.forEach(function(dropdown) {
                const menu = dropdown.querySelector('.dropdown-menu');
                const toggle = dropdown.querySelector('.dropdown-toggle');
                
                if (menu) {
                    menu.style.display = 'none';
                    menu.style.opacity = '0';
                    menu.style.visibility = 'hidden';
                    menu.style.pointerEvents = 'none';
                    dropdown.classList.remove('show');
                    toggle.setAttribute('aria-expanded', 'false');
                }
            });
        }
    });
}

// 检查DIY页面并切换菜单显示
function checkDiyPagesAndToggleMenu(menuItem) {
    fetch('/api/diy-pages')
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            // 如果没有DIY页面，隐藏菜单项
            if (!data.success || !data.pages || data.pages.length === 0) {
                menuItem.style.display = 'none';
            } else {
                // 有DIY页面，加载内容
                loadDiyPagesToMenu();
            }
        })
        .catch(error => {
            console.error('检查DIY页面失败:', error);
            // 检查失败时隐藏菜单项
            menuItem.style.display = 'none';
        });
}

// 检查并添加"更多页面"菜单
function checkAndAddMorePagesMenu(nav) {
    // 先检查是否有DIY页面
    fetch('/api/diy-pages')
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            // 只有当有DIY页面时才添加"更多页面"菜单
            if (data.success && data.pages && data.pages.length > 0) {
                const morePages = document.createElement('li');
                morePages.className = 'nav-item dropdown';
                morePages.innerHTML = `
                    <a class="nav-link dropdown-toggle" href="#" id="diyPagesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        更多页面
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="diyPagesDropdown" id="diy-pages-menu">
                    </ul>
                `;
                nav.appendChild(morePages);

                // 为"更多页面"添加悬停事件
                morePages.addEventListener('mouseenter', function() {
                    const dropdown = morePages.querySelector('.dropdown-menu');
                    if (dropdown) {
                        dropdown.style.display = 'block';
                        morePages.classList.add('show');
                    }
                });

                morePages.addEventListener('mouseleave', function() {
                    const dropdown = morePages.querySelector('.dropdown-menu');
                    if (dropdown) {
                        dropdown.style.display = 'none';
                        morePages.classList.remove('show');
                    }
                });

                // 加载DIY页面内容
                loadDiyPagesToMenu();
            }
        })
        .catch(error => {
            console.error('检查DIY页面失败:', error);
            // 如果检查失败，不添加"更多页面"菜单
        });
}

// 加载DIY页面到导航菜单
function loadDiyPagesToMenu() {
    // 获取菜单容器
    const menuContainer = document.getElementById('diy-pages-menu');
    if (!menuContainer) {
        console.error('未找到DIY页面菜单容器');
        return;
    }

    // 发起API请求
    fetch('/api/diy-pages')
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.pages && data.pages.length > 0) {
                // 清空现有的DIY页面项
                const existingDiyItems = menuContainer.querySelectorAll('.diy-page-item');
                existingDiyItems.forEach(item => item.remove());

                // 添加DIY页面链接
                data.pages.forEach(page => {
                    const li = document.createElement('li');
                    li.className = 'diy-page-item';
                    li.innerHTML = `
                        <a class="dropdown-item" href="/page/${page.slug}" title="${page.description || page.name}">
                            <i class="fas fa-file-alt me-2"></i>
                            ${page.name}
                        </a>
                    `;
                    menuContainer.appendChild(li);
                });
            }
        })
        .catch(error => {
            console.error('加载DIY页面失败:', error);
        });
}
</script>
<!-- 公共样式文件 -->
<style>
/* ===== Header和Navbar样式 ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar {
    padding: 12px 0 !important;
    background: transparent !important;
    border: none !important;
    transition: background 0.3s ease, backdrop-filter 0.3s ease !important;
    min-height: 70px !important;
    display: flex !important;
    align-items: center !important;
}

/* 确保navbar容器正确布局 */
.navbar .container {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    width: 100% !important;
}

/* 滚动时header背景 */
header.scrolled {
    background: rgba(47, 76, 153, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    box-shadow: 0 2px 20px rgba(47, 76, 153, 0.3) !important;
}

/* 确保navbar也有背景 */
header.scrolled .navbar,
header.scrolled .navbar-expand-lg,
header.scrolled .navbar-light {
    background: #2f4c99f5 !important;
    background-color: #2f4c99f5 !important;
}

.navbar-brand {
    font-size: 1.6rem !important;
    font-weight: 600 !important;
    color: white !important;
    text-decoration: none !important;
    display: flex !important;
    align-items: center !important;
    background: none !important;
    -webkit-background-clip: unset !important;
    -webkit-text-fill-color: white !important;
    background-clip: unset !important;
}

.navbar-brand .logo {
    height: 45px !important;
    width: auto !important;
    margin-right: 10px !important;
    filter: brightness(0) invert(1) !important;
    transition: all 0.3s ease !important;
    vertical-align: middle !important;
}

/* 滚动时Logo颜色变化 */
header.scrolled .navbar-brand .logo {
    filter: none !important;
}

/* 导航菜单容器对齐 */
.navbar-nav {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    margin: 0 !important;
    list-style: none !important;
    padding: 0 !important;
}

/* 导航链接样式 */
.navbar-nav .nav-item {
    margin: 0 5px !important;
}

.navbar-nav .nav-link {
    color: white !important;
    font-weight: 400 !important;
    padding: 12px 20px !important;
    position: relative !important;
    transition: color 0.3s ease !important;
    font-size: 15px !important;
    background: none !important;
    border: none !important;
    display: flex !important;
    align-items: center !important;
}

/* 导航链接悬停效果 */
.navbar-nav .nav-link:hover {
    color: #ff6b35 !important;
}

/* 导航链接下划线效果 - 排除下拉菜单项 */
.navbar-nav .nav-link:not(.dropdown-toggle)::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:not(.dropdown-toggle):hover::after {
    width: 80%;
}

/* 激活状态的导航链接样式 */
.navbar-nav .nav-link.active {
    color: #ff6b35 !important;
}

.navbar-nav .nav-link.active:not(.dropdown-toggle)::after {
    width: 80%;
    background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
}

/* 右侧按钮组对齐 */
.navbar-actions {
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
    margin-left: 20px !important;
    height: 45px !important;
    flex-shrink: 0 !important;
}

.search-btn {
    color: white !important;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    text-decoration: none;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white !important;
    text-decoration: none;
}

.search-btn svg {
    width: 16px;
    height: 16px;
    stroke: currentColor;
    stroke-width: 2;
}

/* 登录注册按钮样式 */
.navbar-actions .btn {
    padding: 8px 18px !important;
    font-size: 14px !important;
    border-radius: 4px !important;
    font-weight: 400 !important;
    transition: color 0.3s ease, background 0.3s ease, border-color 0.3s ease !important;
    text-decoration: none !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.navbar-actions .btn-outline-light {
    color: white !important;
    background: transparent !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
}

.navbar-actions .btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: white !important;
}

.navbar-actions .btn-primary {
    background: #ff6b35 !important;
    border-color: #ff6b35 !important;
    color: white !important;
}

.navbar-actions .btn-primary:hover {
    background: #e55a2b !important;
    border-color: #e55a2b !important;
}

/* 滚动时按钮样式调整 */
header.scrolled .navbar-actions .btn-outline-light {
    color: white !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
}

header.scrolled .navbar-actions .btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: white !important;
}

header.scrolled .navbar-actions .btn-primary {
    background: #ff6b35 !important;
    border-color: #ff6b35 !important;
    color: white !important;
}

header.scrolled .navbar-actions .btn-primary:hover {
    background: #e55a2b !important;
    border-color: #e55a2b !important;
    color: white !important;
}

/* 强制确保滚动时按钮样式不被覆盖 */
header.scrolled .btn-primary,
header.scrolled .navbar-actions .btn-primary,
header.scrolled .navbar .btn-primary {
    background: #ff6b35 !important;
    background-color: #ff6b35 !important;
    border-color: #ff6b35 !important;
    color: white !important;
}

header.scrolled .btn-primary:hover,
header.scrolled .navbar-actions .btn-primary:hover,
header.scrolled .navbar .btn-primary:hover {
    background: #e55a2b !important;
    background-color: #e55a2b !important;
    border-color: #e55a2b !important;
    color: white !important;
}

/* 移动端菜单按钮样式 */
.navbar-toggler {
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    padding: 4px 8px !important;
    transition: all 0.3s ease !important;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
    transition: all 0.3s ease !important;
}

/* 确保主要内容不被固定header遮挡 */
main {
    padding-top: 70px;
}

/* 首页轮播图需要全屏显示，移除顶部间距 */
.home-page main {
    padding-top: 0;
}

/* ===== 产品菜单特殊样式 ===== */
.products-menu {
    padding: 0;
}

.products-header {
    background: #f8f9fa;
    padding: 25px 30px;
    border-bottom: 1px solid #e9ecef;
    border-radius: 16px 16px 0 0;
}

/* 产品网格布局 */
.products-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
    padding: 30px;
}

.products-column {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.product-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-radius: 10px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
    border: 1px solid rgba(102, 126, 234, 0.1);
    text-decoration: none;
    color: inherit;
}

.product-item:hover {
    background: rgba(102, 126, 234, 0.12);
    border-color: rgba(102, 126, 234, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
    text-decoration: none;
    color: inherit;
}

.product-icon {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.product-icon.purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
}

.product-icon i {
    font-size: 18px;
    color: white;
}

/* Lucide图标样式 */
.product-icon svg {
    width: 18px;
    height: 18px;
    color: white;
    stroke: white;
    stroke-width: 2;
}

.product-item:hover .product-icon {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.35);
}

.product-name {
    font-size: 0.9rem;
    font-weight: 500;
    color: #333;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.product-item:hover .product-name {
    color: #667eea;
}

/* ===== 菜单分类标题 ===== */
.mega-menu-category {
    font-size: 1.1rem;
    font-weight: 600;
    color: #007bff;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid rgba(0, 123, 255, 0.2);
    position: relative;
}

.mega-menu-category::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 30px;
    height: 2px;
    background: #007bff;
    border-radius: 1px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .solutions-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 900px) {
    .solutions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 600px) {
    .solutions-grid {
        grid-template-columns: 1fr;
    }
    .products-grid {
        grid-template-columns: 1fr;
    }
}
</style>


<!-- 🚀 量子科技风格页面头部 -->
<section class="quantum-detail-hero">
    <!-- 多维背景系统 -->
    <div class="quantum-bg-system">
        <div class="bg-primary-layer"></div>
        <div class="holographic-grid">
            <div class="grid-matrix"></div>
            <div class="grid-scanlines"></div>
        </div>
        <div class="quantum-particles"></div>
        <div class="energy-pulse-waves">
            <div class="pulse-wave wave-alpha"></div>
            <div class="pulse-wave wave-beta"></div>
        </div>
        <div class="depth-gradient-mask"></div>
    </div>

    <div class="container" style="max-width: 1400px;">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="quantum-detail-content">
                    <!-- 面包屑导航 -->
                    <nav class="quantum-breadcrumb" data-aos="fade-up" data-aos-delay="100">
                        <div class="breadcrumb-quantum">
                            <a href="/" class="breadcrumb-link">
                                <i class="fas fa-home"></i>
                                <span>首页</span>
                            </a>
                            <div class="breadcrumb-separator">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                            <a href="/solutions" class="breadcrumb-link">
                                <i class="fas fa-lightbulb"></i>
                                <span>解决方案</span>
                            </a>
                            <div class="breadcrumb-separator">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                            <span class="breadcrumb-current"><?php echo htmlentities((string) $solution['name']); ?></span>
                        </div>
                    </nav>

                    <!-- 解决方案标题区域 -->
                    <div class="solution-hero-title" data-aos="fade-up" data-aos-delay="200">
                        <div class="solution-icon-quantum">
                            <?php switch($solution['name']): case "社交分销解决方案": ?>
                                    <i class="fas fa-share-alt"></i>
                                <?php break; case "智能多门店管理": ?>
                                    <i class="fas fa-store"></i>
                                <?php break; case "大型多商户平台": ?>
                                    <i class="fas fa-building"></i>
                                <?php break; case "大货批发系统": ?>
                                    <i class="fas fa-boxes"></i>
                                <?php break; case "平台级供货商系统": ?>
                                    <i class="fas fa-truck"></i>
                                <?php break; case "本地生活服务平台": ?>
                                    <i class="fas fa-map-marker-alt"></i>
                                <?php break; default: ?>
                                    <i class="fas fa-lightbulb"></i>
                            <?php endswitch; ?>
                            <div class="icon-energy-ring"></div>
                        </div>
                        <h1 class="solution-mega-title">
                            <span class="title-glow-effect"><?php echo htmlentities((string) $solution['name']); ?></span>
                        </h1>
                        <p class="solution-hero-subtitle">
                            <?php echo htmlentities((string) (isset($solution['short_description']) && ($solution['short_description'] !== '')?$solution['short_description']:'专业的解决方案，助力企业数字化转型')); ?>
                        </p>
                        <div class="title-energy-line"></div>
                    </div>

                    <!-- 快速导航 -->
                    <div class="quantum-quick-nav" data-aos="fade-up" data-aos-delay="300">
                        <a href="#solution-overview" class="nav-quantum-btn">
                            <span>方案概述</span>
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="#solution-features" class="nav-quantum-btn">
                            <span>核心特性</span>
                            <i class="fas fa-star"></i>
                        </a>
                        <a href="#solution-process" class="nav-quantum-btn">
                            <span>实施流程</span>
                            <i class="fas fa-cogs"></i>
                        </a>
                        <a href="#solution-contact" class="nav-quantum-btn">
                            <span>立即咨询</span>
                            <i class="fas fa-phone"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 滚动指示器 -->
    <div class="scroll-indicator" data-aos="fade-up" data-aos-delay="600">
        <div class="scroll-icon">
            <i class="fas fa-chevron-down"></i>
        </div>
        <div class="scroll-text">探索详情</div>
    </div>
</section>

<!-- 🌟 解决方案详情主体 -->
<section class="quantum-solution-detail">
    <div class="container" style="max-width: 1400px;">
        <div class="row justify-content-center">
            <!-- 主要内容 -->
            <div class="col-lg-8 col-md-12">

                <!-- 方案概述 -->
                <div id="solution-overview" class="quantum-content-block" data-aos="fade-up" data-aos-delay="100">
                    <div class="block-header">
                        <div class="block-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <h2 class="block-title">方案概述</h2>
                        <div class="block-energy-line"></div>
                    </div>
                    <div class="block-content">
                        <div class="overview-grid">
                            <div class="overview-visual">
                                <div class="solution-image-quantum">
                                    <img src="<?php echo htmlentities((string) $solution['image']); ?>" alt="<?php echo htmlentities((string) $solution['name']); ?>" onerror="this.src='/static/images/solutions/enterprise-digital.jpg'; this.onerror=null;">
                                    <div class="image-overlay">
                                        <div class="overlay-pattern"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="overview-content">
                                <div class="solution-description-rich">
                                    <?php echo $solution['description']; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 核心特性 -->
                <div id="solution-features" class="quantum-content-block" data-aos="fade-up" data-aos-delay="200">
                    <div class="block-header">
                        <div class="block-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <h2 class="block-title">核心特性</h2>
                        <div class="block-energy-line"></div>
                    </div>
                    <div class="block-content">
                        <div class="features-quantum-grid">
                            <?php if($solution['features']): 
                                    // 检查features是否已经是数组
                                    if (is_array($solution['features'])) {
                                        $features = $solution['features'];
                                    } else {
                                        $features = json_decode($solution['features'], true);
                                    }
                                    if (!$features) $features = ['专业定制', '安全可靠', '高效实施', '持续支持', '技术领先'];
                                 if(is_array($features) || $features instanceof \think\Collection || $features instanceof \think\Paginator): $index = 0; $__LIST__ = $features;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$feature): $mod = ($index % 2 );++$index;?>
                                <div class="feature-quantum-card" data-aos="zoom-in" data-aos-delay="<?php echo htmlentities((string) $index * 100); ?>">
                                    <div class="feature-energy-border"></div>
                                    <div class="feature-icon-quantum">
                                        <?php switch($feature): case "零成本获客": ?>
                                                <i class="fas fa-user-plus"></i>
                                            <?php break; case "裂变式增长": ?>
                                                <i class="fas fa-expand-arrows-alt"></i>
                                            <?php break; case "精准营销": ?>
                                                <i class="fas fa-bullseye"></i>
                                            <?php break; case "统一库存": ?>
                                                <i class="fas fa-warehouse"></i>
                                            <?php break; case "订单一体化": ?>
                                                <i class="fas fa-clipboard-list"></i>
                                            <?php break; case "会员通用": ?>
                                                <i class="fas fa-users"></i>
                                            <?php break; case "高并发处理": ?>
                                                <i class="fas fa-tachometer-alt"></i>
                                            <?php break; case "商户自主管理": ?>
                                                <i class="fas fa-user-cog"></i>
                                            <?php break; case "安全保障": ?>
                                                <i class="fas fa-shield-alt"></i>
                                            <?php break; case "大宗交易": ?>
                                                <i class="fas fa-handshake"></i>
                                            <?php break; case "供应链管理": ?>
                                                <i class="fas fa-link"></i>
                                            <?php break; case "AI驱动": ?>
                                                <i class="fas fa-brain"></i>
                                            <?php break; case "LBS技术": ?>
                                                <i class="fas fa-map-marked-alt"></i>
                                            <?php break; default: ?>
                                                <i class="fas fa-star"></i>
                                        <?php endswitch; ?>
                                        <div class="icon-pulse-effect"></div>
                                    </div>
                                    <div class="feature-content-quantum">
                                        <h4 class="feature-title"><?php echo htmlentities((string) $feature); ?></h4>
                                        <p class="feature-description">
                                            <?php switch($feature): case "零成本获客": ?>通过社交分享实现自然传播，降低获客成本<?php break; case "裂变式增长": ?>每个用户都是潜在分销商，实现指数级增长<?php break; case "精准营销": ?>基于社交关系的信任营销，提升转化率<?php break; case "统一库存": ?>实时同步各门店库存信息，避免超卖<?php break; case "订单一体化": ?>线上线下订单统一处理，提升效率<?php break; case "会员通用": ?>会员信息全门店共享，提升用户体验<?php break; case "高并发处理": ?>支持百万级用户同时在线访问<?php break; case "商户自主管理": ?>完善的商户后台管理系统<?php break; case "安全保障": ?>多层安全防护，确保交易安全<?php break; case "大宗交易": ?>支持大批量商品交易处理<?php break; case "供应链管理": ?>完整的供应链跟踪体系<?php break; case "AI驱动": ?>智能算法优化业务流程<?php break; case "LBS技术": ?>精准的位置服务和智能推荐<?php break; default: ?>先进的技术解决方案，助力企业发展
                                            <?php endswitch; ?>
                                        </p>
                                    </div>
                                    <div class="feature-hover-glow"></div>
                                </div>
                                <?php endforeach; endif; else: echo "" ;endif; else: ?>
                                <!-- 默认特性 -->
                                <div class="feature-quantum-card">
                                    <div class="feature-energy-border"></div>
                                    <div class="feature-icon-quantum">
                                        <i class="fas fa-cogs"></i>
                                        <div class="icon-pulse-effect"></div>
                                    </div>
                                    <div class="feature-content-quantum">
                                        <h4 class="feature-title">专业定制</h4>
                                        <p class="feature-description">根据企业实际需求，提供个性化的解决方案</p>
                                    </div>
                                    <div class="feature-hover-glow"></div>
                                </div>
                                <div class="feature-quantum-card">
                                    <div class="feature-energy-border"></div>
                                    <div class="feature-icon-quantum">
                                        <i class="fas fa-shield-alt"></i>
                                        <div class="icon-pulse-effect"></div>
                                    </div>
                                    <div class="feature-content-quantum">
                                        <h4 class="feature-title">安全可靠</h4>
                                        <p class="feature-description">采用先进的安全技术，确保系统稳定运行</p>
                                    </div>
                                    <div class="feature-hover-glow"></div>
                                </div>
                                <div class="feature-quantum-card">
                                    <div class="feature-energy-border"></div>
                                    <div class="feature-icon-quantum">
                                        <i class="fas fa-rocket"></i>
                                        <div class="icon-pulse-effect"></div>
                                    </div>
                                    <div class="feature-content-quantum">
                                        <h4 class="feature-title">高效实施</h4>
                                        <p class="feature-description">专业团队快速部署，缩短项目周期</p>
                                    </div>
                                    <div class="feature-hover-glow"></div>
                                </div>
                                <div class="feature-quantum-card">
                                    <div class="feature-energy-border"></div>
                                    <div class="feature-icon-quantum">
                                        <i class="fas fa-headset"></i>
                                        <div class="icon-pulse-effect"></div>
                                    </div>
                                    <div class="feature-content-quantum">
                                        <h4 class="feature-title">持续支持</h4>
                                        <p class="feature-description">提供7×24小时技术支持和维护服务</p>
                                    </div>
                                    <div class="feature-hover-glow"></div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- 实施流程 -->
                <div id="solution-process" class="quantum-content-block" data-aos="fade-up" data-aos-delay="300">
                    <div class="block-header">
                        <div class="block-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h2 class="block-title">实施流程</h2>
                        <div class="block-energy-line"></div>
                    </div>
                    <div class="block-content">
                        <div class="process-quantum-timeline">
                            <div class="timeline-quantum-line"></div>
                            <div class="timeline-quantum-item" data-aos="fade-right" data-aos-delay="100">
                                <div class="timeline-quantum-marker">
                                    <div class="marker-core">1</div>
                                    <div class="marker-pulse"></div>
                                </div>
                                <div class="timeline-quantum-content">
                                    <h4>需求分析</h4>
                                    <p>深入了解企业需求，制定详细的解决方案</p>
                                    <div class="timeline-tech-tags">
                                        <span class="tech-tag">业务调研</span>
                                        <span class="tech-tag">需求梳理</span>
                                    </div>
                                </div>
                            </div>
                            <div class="timeline-quantum-item" data-aos="fade-left" data-aos-delay="200">
                                <div class="timeline-quantum-marker">
                                    <div class="marker-core">2</div>
                                    <div class="marker-pulse"></div>
                                </div>
                                <div class="timeline-quantum-content">
                                    <h4>方案设计</h4>
                                    <p>基于需求分析，设计最优的技术架构</p>
                                    <div class="timeline-tech-tags">
                                        <span class="tech-tag">架构设计</span>
                                        <span class="tech-tag">原型制作</span>
                                    </div>
                                </div>
                            </div>
                            <div class="timeline-quantum-item" data-aos="fade-right" data-aos-delay="300">
                                <div class="timeline-quantum-marker">
                                    <div class="marker-core">3</div>
                                    <div class="marker-pulse"></div>
                                </div>
                                <div class="timeline-quantum-content">
                                    <h4>系统开发</h4>
                                    <p>专业团队进行系统开发和功能实现</p>
                                    <div class="timeline-tech-tags">
                                        <span class="tech-tag">敏捷开发</span>
                                        <span class="tech-tag">代码审查</span>
                                    </div>
                                </div>
                            </div>
                            <div class="timeline-quantum-item" data-aos="fade-left" data-aos-delay="400">
                                <div class="timeline-quantum-marker">
                                    <div class="marker-core">4</div>
                                    <div class="marker-pulse"></div>
                                </div>
                                <div class="timeline-quantum-content">
                                    <h4>测试部署</h4>
                                    <p>全面测试系统功能，确保稳定上线</p>
                                    <div class="timeline-tech-tags">
                                        <span class="tech-tag">自动化测试</span>
                                        <span class="tech-tag">性能优化</span>
                                    </div>
                                </div>
                            </div>
                            <div class="timeline-quantum-item" data-aos="fade-right" data-aos-delay="500">
                                <div class="timeline-quantum-marker">
                                    <div class="marker-core">5</div>
                                    <div class="marker-pulse"></div>
                                </div>
                                <div class="timeline-quantum-content">
                                    <h4>培训维护</h4>
                                    <p>提供用户培训和持续的技术支持</p>
                                    <div class="timeline-tech-tags">
                                        <span class="tech-tag">用户培训</span>
                                        <span class="tech-tag">7×24支持</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 联系咨询 -->
                <div id="solution-contact" class="quantum-content-block" data-aos="fade-up" data-aos-delay="400">
                    <div class="block-header">
                        <div class="block-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h2 class="block-title">立即咨询</h2>
                        <div class="block-energy-line"></div>
                    </div>
                    <div class="block-content">
                        <div class="contact-quantum-card">
                            <div class="contact-bg-effects">
                                <div class="contact-particle-1"></div>
                                <div class="contact-particle-2"></div>
                                <div class="contact-particle-3"></div>
                            </div>
                            <div class="contact-content-grid">
                                <div class="contact-info-section">
                                    <h3 class="contact-title">
                                        <span class="title-glow">需要专业咨询？</span>
                                    </h3>
                                    <p class="contact-subtitle">
                                        我们的专业顾问将为您提供详细的解决方案咨询，助力企业数字化转型
                                    </p>
                                    <div class="contact-features">
                                        <div class="contact-feature">
                                            <i class="fas fa-user-tie"></i>
                                            <span>专业顾问团队</span>
                                        </div>
                                        <div class="contact-feature">
                                            <i class="fas fa-clock"></i>
                                            <span>快速响应服务</span>
                                        </div>
                                        <div class="contact-feature">
                                            <i class="fas fa-shield-alt"></i>
                                            <span>方案保密承诺</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="contact-actions-section">
                                    <div class="contact-buttons-quantum">
                                        <a href="/contact" class="btn-contact-primary">
                                            <div class="btn-core">
                                                <i class="fas fa-envelope"></i>
                                                <span>在线咨询</span>
                                            </div>
                                            <div class="btn-energy-wave"></div>
                                        </a>
                                        <a href="tel:<?php echo htmlentities((string) (isset($siteConfig['company_phone']) && ($siteConfig['company_phone'] !== '')?$siteConfig['company_phone']:'************')); ?>" class="btn-contact-secondary">
                                            <div class="btn-core">
                                                <i class="fas fa-phone"></i>
                                                <span>电话咨询</span>
                                            </div>
                                            <div class="btn-neural-effect"></div>
                                        </a>
                                        <a href="javascript:void(0)" onclick="showQRCode()" class="btn-contact-tertiary">
                                            <div class="btn-core">
                                                <i class="fab fa-weixin"></i>
                                                <span>微信咨询</span>
                                            </div>
                                            <div class="btn-scan-effect"></div>
                                        </a>
                                    </div>
                                    <div class="contact-info-quick">
                                        <div class="info-item">
                                            <span class="info-label">咨询热线</span>
                                            <span class="info-value"><?php echo htmlentities((string) (isset($siteConfig['company_phone']) && ($siteConfig['company_phone'] !== '')?$siteConfig['company_phone']:'************')); ?></span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">邮箱地址</span>
                                            <span class="info-value"><?php echo htmlentities((string) (isset($siteConfig['company_email']) && ($siteConfig['company_email'] !== '')?$siteConfig['company_email']:'<EMAIL>')); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 量子侧边栏 -->
            <div class="col-lg-4 col-md-12">
                <div class="quantum-sidebar" data-aos="fade-left" data-aos-delay="200">

                    <!-- 相关解决方案 -->
                    <?php if($relatedSolutions && count($relatedSolutions) > 0): ?>
                    <div class="sidebar-quantum-widget">
                        <div class="widget-header">
                            <div class="widget-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <h4 class="widget-title">相关解决方案</h4>
                            <div class="widget-energy-line"></div>
                        </div>
                        <div class="widget-content">
                            <div class="related-solutions-quantum">
                                <?php if(is_array($relatedSolutions) || $relatedSolutions instanceof \think\Collection || $relatedSolutions instanceof \think\Paginator): $index = 0; $__LIST__ = $relatedSolutions;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$related): $mod = ($index % 2 );++$index;?>
                                <div class="related-quantum-item" data-aos="fade-up" data-aos-delay="<?php echo htmlentities((string) $index * 100); ?>">
                                    <div class="related-energy-border"></div>
                                    <div class="related-icon-quantum">
                                        <?php switch($related['name']): case "社交分销": ?>
                                                <i class="fas fa-share-alt"></i>
                                            <?php break; case "智能多门店": ?>
                                                <i class="fas fa-store"></i>
                                            <?php break; case "大型多商户": ?>
                                                <i class="fas fa-building"></i>
                                            <?php break; case "大货批发": ?>
                                                <i class="fas fa-boxes"></i>
                                            <?php break; case "平台级供货商": ?>
                                                <i class="fas fa-truck"></i>
                                            <?php break; case "本地生活服务": ?>
                                                <i class="fas fa-map-marker-alt"></i>
                                            <?php break; case "企业数字化": ?>
                                                <i class="fas fa-digital-tachograph"></i>
                                            <?php break; case "定制化方案": ?>
                                                <i class="fas fa-cogs"></i>
                                            <?php break; default: ?>
                                                <i class="fas fa-lightbulb"></i>
                                        <?php endswitch; ?>
                                        <div class="icon-glow-effect"></div>
                                    </div>
                                    <div class="related-content-quantum">
                                        <h5 class="related-title">
                                            <a href="/solutions/<?php echo htmlentities((string) (isset($related['link_value']) && ($related['link_value'] !== '')?$related['link_value']:$related['slug'])); ?>"><?php echo htmlentities((string) $related['name']); ?></a>
                                        </h5>
                                        <p class="related-desc"><?php echo htmlentities((string) (isset($related['remark']) && ($related['remark'] !== '')?$related['remark']:'专业的解决方案')); ?></p>
                                    </div>
                                    <div class="related-hover-effect"></div>
                                </div>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- 技术优势 -->
                    <div class="sidebar-quantum-widget">
                        <div class="widget-header">
                            <div class="widget-icon">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <h4 class="widget-title">技术优势</h4>
                            <div class="widget-energy-line"></div>
                        </div>
                        <div class="widget-content">
                            <div class="tech-advantages-quantum">
                                <div class="advantage-quantum-item">
                                    <div class="advantage-icon">
                                        <i class="fas fa-code"></i>
                                    </div>
                                    <div class="advantage-content">
                                        <span class="advantage-title">最新技术框架</span>
                                        <span class="advantage-desc">采用前沿技术栈</span>
                                    </div>
                                </div>
                                <div class="advantage-quantum-item">
                                    <div class="advantage-icon">
                                        <i class="fas fa-cloud"></i>
                                    </div>
                                    <div class="advantage-content">
                                        <span class="advantage-title">云端部署</span>
                                        <span class="advantage-desc">支持多种部署方式</span>
                                    </div>
                                </div>
                                <div class="advantage-quantum-item">
                                    <div class="advantage-icon">
                                        <i class="fas fa-expand-arrows-alt"></i>
                                    </div>
                                    <div class="advantage-content">
                                        <span class="advantage-title">良好扩展性</span>
                                        <span class="advantage-desc">模块化架构设计</span>
                                    </div>
                                </div>
                                <div class="advantage-quantum-item">
                                    <div class="advantage-icon">
                                        <i class="fas fa-book"></i>
                                    </div>
                                    <div class="advantage-content">
                                        <span class="advantage-title">完整文档</span>
                                        <span class="advantage-desc">API接口文档齐全</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 服务保障 -->
                    <div class="sidebar-quantum-widget">
                        <div class="widget-header">
                            <div class="widget-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h4 class="widget-title">服务保障</h4>
                            <div class="widget-energy-line"></div>
                        </div>
                        <div class="widget-content">
                            <div class="service-guarantees">
                                <div class="guarantee-item">
                                    <div class="guarantee-icon">
                                        <i class="fas fa-award"></i>
                                    </div>
                                    <span>专业团队</span>
                                </div>
                                <div class="guarantee-item">
                                    <div class="guarantee-icon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <span>快速响应</span>
                                </div>
                                <div class="guarantee-item">
                                    <div class="guarantee-icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <span>质量保证</span>
                                </div>
                                <div class="guarantee-item">
                                    <div class="guarantee-icon">
                                        <i class="fas fa-headset"></i>
                                    </div>
                                    <span>持续支持</span>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</section>

<!-- 🎨 量子科技风格样式 -->
<style>
/* ===== 量子详情页Hero区域 ===== */
.quantum-detail-hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: radial-gradient(ellipse at center, #0a0a0a 0%, #000000 100%);
}

/* 多维背景系统 */
.quantum-bg-system {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.bg-primary-layer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.1) 0%, rgba(255, 0, 255, 0.08) 100%);
}

.holographic-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.3;
}

.grid-matrix {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 20s linear infinite;
}

.grid-scanlines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        0deg,
        transparent,
        transparent 2px,
        rgba(0, 255, 255, 0.03) 2px,
        rgba(0, 255, 255, 0.03) 4px
    );
    animation: scanlineMove 3s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

@keyframes scanlineMove {
    0% { transform: translateY(0); }
    100% { transform: translateY(4px); }
}

.quantum-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 30%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(255, 0, 255, 0.08) 0%, transparent 50%);
    animation: quantumFloat 25s ease-in-out infinite;
}

@keyframes quantumFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.energy-pulse-waves {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.pulse-wave {
    position: absolute;
    width: 200px;
    height: 200px;
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 50%;
    animation: pulseExpand 4s ease-out infinite;
}

.wave-alpha {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.wave-beta {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
    border-color: rgba(255, 0, 255, 0.3);
}

@keyframes pulseExpand {
    0% { transform: scale(0.5); opacity: 1; }
    100% { transform: scale(2); opacity: 0; }
}

.depth-gradient-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.3) 100%);
}

/* 量子详情内容 */
.quantum-detail-content {
    position: relative;
    z-index: 10;
    text-align: center;
}

/* 面包屑导航 */
.quantum-breadcrumb {
    margin-bottom: 3rem;
}

.breadcrumb-quantum {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.breadcrumb-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.breadcrumb-link:hover {
    color: #00ffff;
    background: rgba(0, 255, 255, 0.2);
    border-color: rgba(0, 255, 255, 0.4);
    transform: translateY(-2px);
}

.breadcrumb-separator {
    color: rgba(255, 255, 255, 0.5);
}

.breadcrumb-current {
    color: #00ffff;
    padding: 0.5rem 1rem;
    background: rgba(0, 255, 255, 0.2);
    border-radius: 20px;
    border: 1px solid rgba(0, 255, 255, 0.4);
}

/* 解决方案标题区域 */
.solution-hero-title {
    margin-bottom: 3rem;
}

.solution-icon-quantum {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto 2rem;
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
}

.icon-energy-ring {
    position: absolute;
    top: -15px;
    left: -15px;
    right: -15px;
    bottom: -15px;
    border: 2px solid rgba(0, 255, 255, 0.4);
    border-radius: 50%;
    animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); opacity: 0.6; }
    50% { transform: scale(1.1); opacity: 1; }
}

.solution-mega-title {
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    font-family: 'Orbitron', monospace;
}

.title-glow-effect {
    background: linear-gradient(135deg, #ffffff 0%, #00ffff 50%, #ff00ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
    animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% { filter: brightness(1) drop-shadow(0 0 20px rgba(0, 255, 255, 0.3)); }
    100% { filter: brightness(1.3) drop-shadow(0 0 40px rgba(0, 255, 255, 0.6)); }
}

.solution-hero-subtitle {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.title-energy-line {
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, transparent, #00ffff, transparent);
    margin: 0 auto;
    animation: energyLinePulse 2s ease-in-out infinite;
}

@keyframes energyLinePulse {
    0%, 100% { opacity: 0.5; transform: scaleX(1); }
    50% { opacity: 1; transform: scaleX(1.5); }
}

/* 快速导航 */
.quantum-quick-nav {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.nav-quantum-btn {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1rem 2rem;
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 30px;
    color: #00ffff;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-quantum-btn:hover {
    background: rgba(0, 255, 255, 0.2);
    border-color: rgba(0, 255, 255, 0.5);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 255, 255, 0.3);
}

.nav-quantum-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.nav-quantum-btn:hover::before {
    left: 100%;
}

/* 滚动指示器 */
.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
}

.scroll-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    animation: scrollBounce 2s ease-in-out infinite;
}

@keyframes scrollBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(10px); }
}

.scroll-text {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* ===== 解决方案详情主体 ===== */
.quantum-solution-detail {
    padding: 60px 0;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    position: relative;
    overflow: hidden;
}

/* 量子内容块 */
.quantum-content-block {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 20px;
    padding: 3rem 2.5rem;
    margin-bottom: 3rem;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.quantum-content-block::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff7f, #ffd700);
    border-radius: 20px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.quantum-content-block:hover::before {
    opacity: 0.3;
}

.block-header {
    display: flex;
    align-items: center;
    margin-bottom: 2.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2);
}

.block-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    font-size: 1.5rem;
    color: white;
}

.block-title {
    font-size: 2rem;
    font-weight: 700;
    color: #ffffff;
    margin: 0;
    font-family: 'Orbitron', monospace;
}

.block-energy-line {
    flex: 1;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00ffff, transparent);
    margin-left: 2rem;
    animation: energyFlow 2s ease-in-out infinite;
}

@keyframes energyFlow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* 概述网格 */
.overview-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.solution-image-quantum {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
}

.solution-image-quantum img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 15px;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.2) 0%, rgba(255, 0, 255, 0.1) 100%);
}

.overlay-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 48%, rgba(255, 255, 255, 0.1) 49%, rgba(255, 255, 255, 0.1) 51%, transparent 52%);
    background-size: 20px 20px;
    animation: patternMove 8s linear infinite;
}

@keyframes patternMove {
    0% { background-position: 0 0; }
    100% { background-position: 20px 20px; }
}

.solution-description-rich {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.8;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.solution-stats {
    display: flex;
    gap: 2rem;
    justify-content: space-around;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #00ffff;
    font-family: 'Orbitron', monospace;
    text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

/* 特性量子网格 */
.features-quantum-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-quantum-card {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 15px;
    padding: 2rem;
    transition: all 0.4s ease;
    overflow: hidden;
}

.feature-quantum-card:hover {
    transform: translateY(-10px);
    border-color: rgba(0, 255, 255, 0.5);
    box-shadow: 0 20px 40px rgba(0, 255, 255, 0.2);
}

.feature-energy-border {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff7f);
    border-radius: 15px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.feature-quantum-card:hover .feature-energy-border {
    opacity: 0.3;
}

.feature-icon-quantum {
    position: relative;
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
    color: white;
}

.icon-pulse-effect {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border: 2px solid rgba(0, 255, 255, 0.4);
    border-radius: 50%;
    animation: iconPulseEffect 2s ease-in-out infinite;
}

@keyframes iconPulseEffect {
    0%, 100% { transform: scale(1); opacity: 0.6; }
    50% { transform: scale(1.1); opacity: 1; }
}

.feature-content-quantum {
    position: relative;
    z-index: 2;
}

.feature-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
    font-family: 'Orbitron', monospace;
}

.feature-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin: 0;
}

.feature-hover-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.feature-quantum-card:hover .feature-hover-glow {
    opacity: 1;
}

/* 量子时间线 */
.process-quantum-timeline {
    position: relative;
    padding: 2rem 0;
}

.timeline-quantum-line {
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(180deg, #00ffff 0%, #ff00ff 50%, #00ff7f 100%);
    transform: translateX(-50%);
    animation: timelineGlow 3s ease-in-out infinite;
}

@keyframes timelineGlow {
    0%, 100% { box-shadow: 0 0 10px rgba(0, 255, 255, 0.5); }
    50% { box-shadow: 0 0 20px rgba(0, 255, 255, 0.8); }
}

.timeline-quantum-item {
    position: relative;
    margin-bottom: 4rem;
    display: flex;
    align-items: center;
}

.timeline-quantum-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-quantum-marker {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.marker-core {
    font-size: 1.5rem;
    font-weight: 800;
    color: white;
    font-family: 'Orbitron', monospace;
}

.marker-pulse {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border: 2px solid rgba(0, 255, 255, 0.4);
    border-radius: 50%;
    animation: markerPulse 2s ease-in-out infinite;
}

@keyframes markerPulse {
    0%, 100% { transform: scale(1); opacity: 0.6; }
    50% { transform: scale(1.2); opacity: 1; }
}

.timeline-quantum-content {
    width: 45%;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 15px;
    padding: 2rem;
    backdrop-filter: blur(10px);
}

.timeline-quantum-item:nth-child(even) .timeline-quantum-content {
    margin-right: auto;
}

.timeline-quantum-item:nth-child(odd) .timeline-quantum-content {
    margin-left: auto;
}

.timeline-quantum-content h4 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
    font-family: 'Orbitron', monospace;
}

.timeline-quantum-content p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.timeline-tech-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tech-tag {
    padding: 0.3rem 0.8rem;
    background: rgba(0, 255, 255, 0.2);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 15px;
    color: #00ffff;
    font-size: 0.8rem;
    font-weight: 500;
}

/* 联系量子卡片 */
.contact-quantum-card {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 20px;
    padding: 3rem;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.contact-bg-effects {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.contact-particle-1,
.contact-particle-2,
.contact-particle-3 {
    position: absolute;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    opacity: 0.1;
    animation: particleFloat 8s ease-in-out infinite;
}

.contact-particle-1 {
    top: 10%;
    left: 10%;
    background: radial-gradient(circle, #00ffff 0%, transparent 70%);
    animation-delay: 0s;
}

.contact-particle-2 {
    top: 60%;
    right: 20%;
    background: radial-gradient(circle, #ff00ff 0%, transparent 70%);
    animation-delay: 2s;
}

.contact-particle-3 {
    bottom: 20%;
    left: 30%;
    background: radial-gradient(circle, #00ff7f 0%, transparent 70%);
    animation-delay: 4s;
}

@keyframes particleFloat {
    0%, 100% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-20px) scale(1.1); }
}

.contact-content-grid {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.contact-title {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    font-family: 'Orbitron', monospace;
}

.title-glow {
    background: linear-gradient(135deg, #ffffff 0%, #00ffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.contact-subtitle {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 2rem;
}

.contact-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-feature {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: rgba(255, 255, 255, 0.9);
}

.contact-feature i {
    color: #00ffff;
    font-size: 1.2rem;
}

.contact-buttons-quantum {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.btn-contact-primary,
.btn-contact-secondary,
.btn-contact-tertiary {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 1.2rem 2rem;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.4s ease;
    overflow: hidden;
}

.btn-contact-primary {
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    color: white;
}

.btn-contact-secondary {
    background: transparent;
    border: 2px solid rgba(0, 255, 255, 0.5);
    color: #00ffff;
}

.btn-contact-tertiary {
    background: transparent;
    border: 2px solid rgba(0, 255, 127, 0.5);
    color: #00ff7f;
}

.btn-contact-primary:hover,
.btn-contact-secondary:hover,
.btn-contact-tertiary:hover {
    transform: translateY(-3px);
    color: white;
}

.btn-contact-primary:hover {
    box-shadow: 0 15px 30px rgba(0, 255, 255, 0.4);
}

.btn-contact-secondary:hover {
    background: rgba(0, 255, 255, 0.1);
    border-color: rgba(0, 255, 255, 0.8);
}

.btn-contact-tertiary:hover {
    background: rgba(0, 255, 127, 0.1);
    border-color: rgba(0, 255, 127, 0.8);
}

.contact-info-quick {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem 1rem;
    background: rgba(0, 255, 255, 0.1);
    border-radius: 10px;
    border: 1px solid rgba(0, 255, 255, 0.2);
}

.info-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.info-value {
    color: #00ffff;
    font-weight: 600;
}

/* ===== 量子侧边栏 ===== */
.quantum-sidebar {
    position: sticky;
}

.sidebar-quantum-widget {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.sidebar-quantum-widget:hover {
    border-color: rgba(0, 255, 255, 0.4);
    box-shadow: 0 10px 25px rgba(0, 255, 255, 0.1);
}

.widget-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2);
}

.widget-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: white;
}

.widget-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: #ffffff;
    margin: 0;
    font-family: 'Orbitron', monospace;
}

.widget-energy-line {
    flex: 1;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00ffff, transparent);
    margin-left: 1rem;
}

/* 相关解决方案 */
.related-solutions-quantum {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.related-quantum-item {
    position: relative;
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(0, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.1);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.related-quantum-item:hover {
    background: rgba(0, 255, 255, 0.1);
    border-color: rgba(0, 255, 255, 0.3);
    transform: translateX(5px);
}

.related-energy-border {
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(45deg, #00ffff, #ff00ff);
    border-radius: 10px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.related-quantum-item:hover .related-energy-border {
    opacity: 0.3;
}

.related-icon-quantum {
    position: relative;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.icon-glow-effect {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border: 1px solid rgba(0, 255, 255, 0.4);
    border-radius: 50%;
    animation: iconGlow 2s ease-in-out infinite;
}

@keyframes iconGlow {
    0%, 100% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
}

.related-content-quantum {
    flex: 1;
}

.related-title {
    margin: 0 0 0.5rem 0;
}

.related-title a {
    color: #ffffff;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    transition: color 0.3s ease;
}

.related-title a:hover {
    color: #00ffff;
}

.related-desc {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.85rem;
    margin: 0;
}

/* 技术优势 */
.tech-advantages-quantum {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.advantage-quantum-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(0, 255, 255, 0.05);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.advantage-quantum-item:hover {
    background: rgba(0, 255, 255, 0.1);
    transform: translateX(3px);
}

.advantage-icon {
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
}

.advantage-content {
    display: flex;
    flex-direction: column;
}

.advantage-title {
    color: #ffffff;
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 0.2rem;
}

.advantage-desc {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8rem;
}

/* 服务保障 */
.service-guarantees {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.guarantee-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1rem;
    background: rgba(0, 255, 255, 0.05);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.guarantee-item:hover {
    background: rgba(0, 255, 255, 0.1);
    transform: translateY(-2px);
}

.guarantee-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-bottom: 0.5rem;
}

.guarantee-item span {
    color: #ffffff;
    font-weight: 600;
    font-size: 0.9rem;
}

/* ===== 响应式设计 ===== */
@media (max-width: 1400px) {
    .overview-grid,
    .contact-content-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .timeline-quantum-content {
        width: 80%;
    }

    .timeline-quantum-marker {
        left: 10%;
        transform: translateX(0);
    }

    .timeline-quantum-item {
        flex-direction: row !important;
        padding-left: 15%;
    }

    .timeline-quantum-content {
        margin-left: 0 !important;
        margin-right: 0 !important;
    }
}

@media (max-width: 768px) {
    .quantum-detail-hero {
        min-height: 80vh;
        padding: 2rem 0;
    }

    .solution-mega-title {
        font-size: 2.5rem;
    }

    .solution-icon-quantum {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .quantum-quick-nav {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .nav-quantum-btn {
        width: 100%;
        max-width: 280px;
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    .quantum-content-block {
        padding: 1.5rem 1rem;
        margin-bottom: 2rem;
    }

    .features-quantum-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .solution-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .contact-buttons-quantum {
        flex-direction: column;
        gap: 1rem;
    }

    .btn-contact-primary,
    .btn-contact-secondary,
    .btn-contact-tertiary {
        width: 100%;
        padding: 1rem 1.5rem;
        justify-content: center;
    }

    .service-guarantees {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    /* 移动端恢复正常滚动 */
    .quantum-solution-detail {
        height: auto;
        overflow: visible;
        padding: 0;
    }

    .main-content-scroll {
        height: auto;
        overflow: visible;
        padding: 20px 15px;
    }

    .sidebar-fixed {
        height: auto;
        padding: 20px 15px;
        margin-top: 2rem;
    }

    .quantum-sidebar {
        height: auto;
        overflow: visible;
        padding-right: 0;
    }

    /* 移动端侧边栏样式调整 */
    .sidebar-quantum-widget {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .widget-header {
        margin-bottom: 1rem;
        padding-bottom: 0.8rem;
    }

    .widget-title {
        font-size: 1.1rem;
    }

    .related-quantum-item,
    .advantage-quantum-item {
        padding: 0.8rem;
    }

    .guarantee-item {
        padding: 0.8rem;
    }

    /* 移动端优化触摸体验 */
    .related-quantum-item:hover,
    .advantage-quantum-item:hover,
    .guarantee-item:hover {
        transform: none;
    }

    /* 移动端字体大小调整 */
    .block-title {
        font-size: 1.8rem;
    }

    .feature-title {
        font-size: 1.2rem;
    }

    .feature-description {
        font-size: 0.9rem;
    }

    /* 移动端禁用复杂动画 */
    .quantum-particles,
    .energy-pulse-waves,
    .contact-bg-effects {
        display: none;
    }
}

@media (max-width: 480px) {
    .solution-mega-title {
        font-size: 2rem;
    }

    .block-title {
        font-size: 1.5rem;
    }

    .quantum-content-block {
        padding: 1.2rem 0.8rem;
        margin-bottom: 1.5rem;
    }

    .timeline-quantum-content {
        width: 90%;
        padding: 1.2rem;
    }

    .contact-quantum-card {
        padding: 1.5rem 1rem;
    }

    /* 超小屏幕侧边栏优化 */
    .sidebar-quantum-widget {
        padding: 1.2rem;
        margin-bottom: 1.2rem;
    }

    .widget-title {
        font-size: 1rem;
    }

    .related-quantum-item,
    .advantage-quantum-item {
        padding: 0.6rem;
        gap: 0.8rem;
    }

    .guarantee-item {
        padding: 0.6rem;
    }

    .nav-quantum-btn {
        max-width: 100%;
        padding: 0.7rem 1.2rem;
        font-size: 0.85rem;
    }

    /* 超小屏幕字体调整 */
    .feature-title {
        font-size: 1.1rem;
    }

    .feature-description {
        font-size: 0.85rem;
    }

    .related-title a {
        font-size: 0.9rem;
    }

    .related-desc {
        font-size: 0.8rem;
    }

    .advantage-title {
        font-size: 0.9rem;
    }

    .advantage-desc {
        font-size: 0.75rem;
    }
}

</style>

<!-- 🚀 JavaScript功能 -->
<script>
// AOS动画初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化AOS
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 800,
            easing: 'ease-out-cubic',
            once: true,
            offset: 100
        });
    }

    // 平滑滚动 - 桌面端在主内容区域内滚动，移动端使用页面滚动
    document.querySelectorAll('.nav-quantum-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            const mainContent = document.querySelector('.main-content-scroll');
            const isMobile = window.innerWidth <= 768;

            if (targetElement) {
                if (isMobile) {
                    // 移动端使用页面滚动
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                } else if (mainContent) {
                    // 桌面端在主内容区域内滚动
                    const targetOffset = targetElement.offsetTop - mainContent.offsetTop;
                    mainContent.scrollTo({
                        top: targetOffset - 20,
                        behavior: 'smooth'
                    });
                }
            }
        });
    });

    // 滚动指示器点击
    document.querySelector('.scroll-indicator')?.addEventListener('click', function() {
        const mainContent = document.querySelector('.main-content-scroll');
        const isMobile = window.innerWidth <= 768;

        if (isMobile) {
            // 移动端滚动到详情区域
            document.querySelector('.quantum-solution-detail').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        } else if (mainContent) {
            // 桌面端在主内容区域内滚动
            mainContent.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
    });

    // 动态粒子效果
    createQuantumParticles();

    // 视差滚动效果
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const parallax = document.querySelector('.quantum-bg-system');
        if (parallax) {
            parallax.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });
});

// 创建量子粒子效果
function createQuantumParticles() {
    const particleContainer = document.querySelector('.quantum-particles');
    if (!particleContainer) return;

    for (let i = 0; i < 20; i++) {
        const particle = document.createElement('div');
        particle.className = 'quantum-particle';
        particle.style.cssText = `
            position: absolute;
            width: ${Math.random() * 4 + 2}px;
            height: ${Math.random() * 4 + 2}px;
            background: ${Math.random() > 0.5 ? '#00ffff' : '#ff00ff'};
            border-radius: 50%;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation: particleMove ${Math.random() * 10 + 10}s linear infinite;
            opacity: ${Math.random() * 0.5 + 0.3};
        `;
        particleContainer.appendChild(particle);
    }
}

// 显示微信二维码
function showQRCode() {
    // 创建模态框
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        backdrop-filter: blur(10px);
    `;

    const content = document.createElement('div');
    content.style.cssText = `
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
        padding: 2rem;
        border-radius: 20px;
        text-align: center;
        border: 1px solid rgba(0, 255, 255, 0.3);
        box-shadow: 0 20px 40px rgba(0, 255, 255, 0.2);
    `;

    content.innerHTML = `
        <h3 style="color: #00ffff; margin-bottom: 1rem; font-family: 'Orbitron', monospace;">微信咨询</h3>
        <div style="width: 200px; height: 200px; background: white; margin: 0 auto 1rem; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: #333;">
            二维码占位
        </div>
        <p style="color: rgba(255, 255, 255, 0.8); margin-bottom: 1.5rem;">扫描二维码添加微信咨询</p>
        <button onclick="this.closest('.modal').remove()" style="
            background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
            color: white;
            border: none;
            padding: 0.8rem 2rem;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
        ">关闭</button>
    `;

    modal.className = 'modal';
    modal.appendChild(content);
    document.body.appendChild(modal);

    // 点击背景关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// 添加粒子移动动画
const style = document.createElement('style');
style.textContent = `
    @keyframes particleMove {
        0% { transform: translateY(0) rotate(0deg); }
        100% { transform: translateY(-100vh) rotate(360deg); }
    }
`;
document.head.appendChild(style);
</script>

</main>

    <!-- 页脚 -->
    <footer class="footer">
        <!-- 主要页脚内容 -->
        <div class="footer-main">
            <div class="container">
                <div class="row">
                    <!-- 公司信息 -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="footer-widget">
                            <div class="footer-logo">
                                <img src="<?php echo asset('assets/images/logo.png'); ?>" alt="<?php echo htmlentities((string) (isset($siteConfig['site_name']) && ($siteConfig['site_name'] !== '')?$siteConfig['site_name']:'三只鱼网络')); ?>">
                            </div>
                            <p class="footer-desc">
                                <?php echo htmlentities((string) (isset($siteConfig['site_description']) && ($siteConfig['site_description'] !== '')?$siteConfig['site_description']:'我们提供专业的企业解决方案，助力企业数字化转型，打造专业品牌形象，提供一站式服务支持。')); ?>
                            </p>
                            
                            <!-- 微信二维码 -->
                            <div class="footer-qrcode">
                                <img src="<?php echo asset('assets/images/weixin.png'); ?>" alt="微信二维码" class="qrcode-img">
                                <p class="qrcode-text">扫码关注微信</p>
                            </div>
                            
                            <div class="footer-social">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 解决方案 -->
                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">解决方案</h5>
                            <ul class="footer-links">
                                <?php if(is_array($footerSolutions) || $footerSolutions instanceof \think\Collection || $footerSolutions instanceof \think\Paginator): $i = 0; $__LIST__ = $footerSolutions;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$solution): $mod = ($i % 2 );++$i;?>
                                <li><a href="/solutions/<?php echo htmlentities((string) $solution['link_value']); ?>"><?php echo htmlentities((string) $solution['name']); ?></a></li>
                                <?php endforeach; endif; else: echo "" ;endif; if(empty($footerSolutions) || (($footerSolutions instanceof \think\Collection || $footerSolutions instanceof \think\Paginator ) && $footerSolutions->isEmpty())): ?>
                                <li><a href="/solutions">暂无解决方案</a></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 产品分类 -->
                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">产品分类</h5>
                            <ul class="footer-links">
                                <?php if(is_array($footerProductCategories) || $footerProductCategories instanceof \think\Collection || $footerProductCategories instanceof \think\Paginator): $i = 0; $__LIST__ = $footerProductCategories;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$category): $mod = ($i % 2 );++$i;?>
                                <li><a href="/products?category=<?php echo htmlentities((string) $category['slug']); ?>"><?php echo htmlentities((string) $category['name']); ?></a></li>
                                <?php endforeach; endif; else: echo "" ;endif; if(empty($footerProductCategories) || (($footerProductCategories instanceof \think\Collection || $footerProductCategories instanceof \think\Paginator ) && $footerProductCategories->isEmpty())): ?>
                                <li><a href="/products">暂无产品分类</a></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 普通页面 -->
                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">帮助中心</h5>
                            <ul class="footer-links">
                                <?php if(is_array($footerDiyPages) || $footerDiyPages instanceof \think\Collection || $footerDiyPages instanceof \think\Paginator): $i = 0; $__LIST__ = $footerDiyPages;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$page): $mod = ($i % 2 );++$i;?>
                                <li><a href="/page/<?php echo htmlentities((string) $page['slug']); ?>"><?php echo htmlentities((string) $page['name']); ?></a></li>
                                <?php endforeach; endif; else: echo "" ;endif; if(empty($footerDiyPages) || (($footerDiyPages instanceof \think\Collection || $footerDiyPages instanceof \think\Paginator ) && $footerDiyPages->isEmpty())): ?>
                                <li><a href="/about">关于我们</a></li>
                                <li><a href="/contact">联系我们</a></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 联系信息 -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">联系我们</h5>
                            <div class="footer-contact">
                                <div class="contact-item">
                                    <i data-lucide="map-pin"></i>
                                    <span><?php echo htmlentities((string) (isset($siteConfig['company_address']) && ($siteConfig['company_address'] !== '')?$siteConfig['company_address']:'北京市朝阳区科技园区')); ?></span>
                                </div>
                                <div class="contact-item">
                                    <i data-lucide="phone"></i>
                                    <span><?php echo htmlentities((string) (isset($siteConfig['company_phone']) && ($siteConfig['company_phone'] !== '')?$siteConfig['company_phone']:'1800001111')); ?></span>
                                </div>
                                <div class="contact-item">
                                    <i data-lucide="mail"></i>
                                    <span><?php echo htmlentities((string) (isset($siteConfig['company_email']) && ($siteConfig['company_email'] !== '')?$siteConfig['company_email']:'<EMAIL>')); ?></span>
                                </div>
                                <div class="contact-item">
                                    <i data-lucide="message-square"></i>
                                    <span><?php echo htmlentities((string) (isset($siteConfig['company_qq']) && ($siteConfig['company_qq'] !== '')?$siteConfig['company_qq']:'*********')); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 版权信息 -->
        <div class="footer-bottom">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="copyright">
                            <p>&copy; <?php echo date('Y'); ?> <?php echo htmlentities((string) (isset($siteConfig['site_name']) && ($siteConfig['site_name'] !== '')?$siteConfig['site_name']:'三只鱼科技有限公司')); ?>. 版权所有</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="footer-links-bottom">
                            <a href="/privacy">隐私政策</a>
                            <a href="/terms">服务条款</a>
                            <a href="/sitemap">网站地图</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- 返回顶部按钮 -->
    <div class="back-to-top" id="backToTop">
        <i class="fa fa-angle-up"></i>
    </div>

    <!-- 在线客服 -->
    <div class="online-service">
        <div class="service-btn" id="serviceBtn">
            <i data-lucide="message-circle"></i>
            <span>在线客服</span>
        </div>
        <div class="service-panel" id="servicePanel">
            <div class="service-header">
                <h6>在线客服</h6>
                <button class="close-btn" id="closeService">&times;</button>
            </div>
            <div class="service-content">
                <div class="service-item">
                    <div class="service-icon qq-icon">
                        <i data-lucide="message-circle"></i>
                    </div>
                    <div class="service-info">
                        <h6>QQ咨询</h6>
                        <p><?php echo htmlentities((string) (isset($siteConfig['company_qq']) && ($siteConfig['company_qq'] !== '')?$siteConfig['company_qq']:'*********')); ?></p>
                    </div>
                    <a href="http://wpa.qq.com/msgrd?v=3&uin=<?php echo htmlentities((string) (isset($siteConfig['company_qq']) && ($siteConfig['company_qq'] !== '')?$siteConfig['company_qq']:'*********')); ?>&site=qq&menu=yes" target="_blank" class="btn btn-sm btn-primary">咨询</a>
                </div>
                <div class="service-item">
                    <div class="service-icon wechat-icon">
                        <i data-lucide="smartphone"></i>
                    </div>
                    <div class="service-info">
                        <h6>微信咨询</h6>
                        <p><?php echo htmlentities((string) (isset($siteConfig['company_wechat']) && ($siteConfig['company_wechat'] !== '')?$siteConfig['company_wechat']:'hsdj37')); ?></p>
                    </div>
                    <button class="btn btn-sm btn-success" onclick="showWechatQR()">扫码</button>
                </div>
                <div class="service-item">
                    <div class="service-icon phone-icon">
                        <i data-lucide="phone"></i>
                    </div>
                    <div class="service-info">
                        <h6>电话咨询</h6>
                        <p><?php echo htmlentities((string) (isset($siteConfig['company_phone']) && ($siteConfig['company_phone'] !== '')?$siteConfig['company_phone']:'************')); ?></p>
                    </div>
                    <a href="tel:<?php echo htmlentities((string) (isset($siteConfig['company_phone']) && ($siteConfig['company_phone'] !== '')?$siteConfig['company_phone']:'************')); ?>" class="btn btn-sm btn-warning">拨打</a>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="<?php echo asset('assets/js/jquery.min.js'); ?>"></script>
    <script src="<?php echo asset('assets/js/bootstrap.bundle.min.js'); ?>"></script> 
    <script src="<?php echo asset('assets/js/swiper-bundle.min.js'); ?>"></script>
    <script src="<?php echo asset('assets/js/main.js'); ?>"></script>
    <script src="<?php echo asset('assets/js/hero-slider.js'); ?>"></script>
    <script src="<?php echo asset('assets/js/scroll-handler.js'); ?>"></script>

    <!-- 初始化Lucide图标 -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    });
    </script>

</body>
</html>

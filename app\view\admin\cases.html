<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户案例管理 - 后台管理系统</title>

    <!-- CSS -->
    {include file="admin/common/css"}
    <link rel="stylesheet" href="/assets/css/admin/cases.css">
    <link rel="stylesheet" href="/assets/css/image-uploader.css">
    <!-- CKEditor 5 CSS -->
    <link rel="stylesheet" href="/assets/css/ckeditor.css">
</head>
<body>
    <!-- 顶部导航 -->
    {include file="admin/common/header"}
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            {include file="admin/common/sidebar"}

            <!-- 主要内容 -->
            <main class="main-content">
                <!-- 内容头部 -->
                <div class="content-header">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-briefcase"></i> 客户案例管理
                    </h1>
                </div>

                <!-- 引用统一消息组件 -->
                {include file="admin/common/message"}

                <!-- 页面内容区域 -->
                <div class="content-body">
                    <div class="cases-container">

                    {if condition="$action == 'list'"}
                        <!-- 案例管理列表视图 -->

                        <div class="list-header">
                            <div class="list-header-content">
                                <div class="list-title-section">
                                    <div class="list-icon">
                                        <i class="fas fa-briefcase"></i>
                                    </div>
                                    <div>
                                        <h1 class="list-title">客户案例</h1>
                                        <p class="list-subtitle">管理客户案例展示内容</p>
                                    </div>
                                </div>
                                <div class="header-actions">
                                    <a href="/admin/cases?action=add" class="btn-add-custom">
                                        <i class="fas fa-plus"></i>
                                        <span>添加案例</span>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="list-body">
                            {if condition="$casesList && count($casesList) > 0"}
                                <div class="cases-list">
                                    {volist name="casesList" id="case"}
                                    <div class="case-item">
                                        <div class="case-content-wrapper">
                                            <div class="case-thumbnail">
                                                {if condition="$case.image"}
                                                    <img src="{$case.image}" alt="{$case.title}" class="case-thumb-img"
                                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                                    <div class="case-thumb-placeholder" style="display: none;">
                                                        <i class="fas fa-briefcase"></i>
                                                    </div>
                                                {else /}
                                                    <div class="case-thumb-placeholder">
                                                        <i class="fas fa-briefcase"></i>
                                                    </div>
                                                {/if}
                                            </div>

                                            <div class="case-info">
                                                <div class="case-header">
                                                    <h3 class="case-title">{$case.title}</h3>
                                                    <div class="case-badges">
                                                        {if condition="$case.is_featured"}
                                                            <span class="badge badge-featured">
                                                                <i class="fas fa-star"></i>
                                                                推荐
                                                            </span>
                                                        {/if}
                                                        {if condition="$case.industry"}
                                                            <span class="badge badge-industry">
                                                                <i class="fas fa-industry"></i>
                                                                {$industries[$case.industry]|default=$case.industry}
                                                            </span>
                                                        {/if}
                                                    </div>
                                                </div>

                                                <div class="case-meta">
                                                    <div class="meta-item">
                                                        <i class="fas fa-user"></i>
                                                        <span>{$case.client_name}</span>
                                                    </div>
                                                    <div class="meta-item">
                                                        <i class="fas fa-calendar"></i>
                                                        <span>{$case.completion_date|date='Y-m-d'|default='未设置'}</span>
                                                    </div>
                                                    <div class="meta-item">
                                                        <i class="fas fa-eye"></i>
                                                        <span>{$case.views ?? 0} 次浏览</span>
                                                    </div>
                                                    {if condition="$case.project_url"}
                                                        <div class="meta-item">
                                                            <i class="fas fa-link"></i>
                                                            <a href="{$case.project_url}" target="_blank">项目链接</a>
                                                        </div>
                                                    {/if}
                                                </div>

                                                {if condition="$case.summary"}
                                                    <div class="case-summary">
                                                        {$case.summary|mb_substr=0,150,'UTF-8'}
                                                        {if condition="mb_strlen($case.summary, 'UTF-8') > 150"}...{/if}
                                                    </div>
                                                {/if}
                                            </div>

                                            <div class="case-actions">
                                                <div class="status-toggle">
                                                    <label class="switch">
                                                        <input type="checkbox"
                                                               {$case.status ? 'checked' : ''}
                                                               onchange="toggleStatus('{$case.id}', this.checked ? 1 : 0)">
                                                        <span class="slider"></span>
                                                    </label>
                                                    <span class="status-label">{$case.status ? '已发布' : '草稿'}</span>
                                                </div>

                                                <div class="action-buttons">
                                                    <a href="/admin/cases?action=edit&id={$case.id}" class="btn-action btn-edit" title="编辑">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button onclick="deleteItem('{$case.id}', '{$case.title|htmlentities}', '/admin/cases?action=delete&id={$case.id}')"
                                                            class="btn-action btn-delete" title="删除">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {/volist}
                                </div>

                                <!-- 自定义分页 -->
                                {if condition="$casesList->hasPages()"}
                                    <div class="custom-pagination-container">
                                        <nav class="custom-pagination-nav">
                                            <div class="pagination-info">
                                                <span class="pagination-text">
                                                    显示第 {$casesList->currentPage()} 页，共 {$casesList->lastPage()} 页，总计 {$casesList->total()} 条记录
                                                </span>
                                            </div>
                                            <div class="pagination-buttons">
                                                {if condition="$casesList->currentPage() > 1"}
                                                    <a href="/admin/cases?action=list&page=1" class="pagination-btn pagination-first">
                                                        <i class="fas fa-angle-double-left"></i>
                                                        首页
                                                    </a>
                                                    <a href="/admin/cases?action=list&page={$casesList->currentPage() - 1}" class="pagination-btn pagination-prev">
                                                        <i class="fas fa-angle-left"></i>
                                                        上一页
                                                    </a>
                                                {else /}
                                                    <span class="pagination-btn pagination-first disabled">
                                                        <i class="fas fa-angle-double-left"></i>
                                                        首页
                                                    </span>
                                                    <span class="pagination-btn pagination-prev disabled">
                                                        <i class="fas fa-angle-left"></i>
                                                        上一页
                                                    </span>
                                                {/if}

                                                <!-- 页码按钮 -->
                                                {php}
                                                    $currentPage = $casesList->currentPage();
                                                    $lastPage = $casesList->lastPage();

                                                    if ($lastPage <= 7) {
                                                        $startPage = 1;
                                                        $endPage = $lastPage;
                                                    } else {
                                                        $startPage = max(1, $currentPage - 2);
                                                        $endPage = min($lastPage, $currentPage + 2);
                                                    }
                                                {/php}

                                                {if condition="$startPage > 1"}
                                                    <a href="/admin/cases?action=list&page=1" class="pagination-btn pagination-number">1</a>
                                                    {if condition="$startPage > 2"}
                                                        <span class="pagination-ellipsis">...</span>
                                                    {/if}
                                                {/if}

                                                {php}
                                                    $pageNumbers = range($startPage, $endPage);
                                                {/php}

                                                {volist name="pageNumbers" id="pageNum"}
                                                    {if condition="$pageNum == $currentPage"}
                                                        <span class="pagination-btn pagination-number active">{$pageNum}</span>
                                                    {else /}
                                                        <a href="/admin/cases?action=list&page={$pageNum}" class="pagination-btn pagination-number">{$pageNum}</a>
                                                    {/if}
                                                {/volist}

                                                {if condition="$endPage < $lastPage"}
                                                    {if condition="$endPage < $lastPage - 1"}
                                                        <span class="pagination-ellipsis">...</span>
                                                    {/if}
                                                    <a href="/admin/cases?action=list&page={$lastPage}" class="pagination-btn pagination-number">{$lastPage}</a>
                                                {/if}

                                                {if condition="$casesList->currentPage() < $casesList->lastPage()"}
                                                    <a href="/admin/cases?action=list&page={$casesList->currentPage() + 1}" class="pagination-btn pagination-next">
                                                        下一页
                                                        <i class="fas fa-angle-right"></i>
                                                    </a>
                                                    <a href="/admin/cases?action=list&page={$casesList->lastPage()}" class="pagination-btn pagination-last">
                                                        末页
                                                        <i class="fas fa-angle-double-right"></i>
                                                    </a>
                                                {else /}
                                                    <span class="pagination-btn pagination-next disabled">
                                                        下一页
                                                        <i class="fas fa-angle-right"></i>
                                                    </span>
                                                    <span class="pagination-btn pagination-last disabled">
                                                        末页
                                                        <i class="fas fa-angle-double-right"></i>
                                                    </span>
                                                {/if}
                                            </div>
                                        </nav>
                                    </div>
                                {/if}
                            {else /}
                                <div class="empty-state">
                                    <div class="empty-icon">
                                        <i class="fas fa-briefcase"></i>
                                    </div>
                                    <h3 class="empty-title">暂无案例</h3>
                                    <p class="empty-description">还没有添加任何客户案例，点击上方按钮开始添加第一个案例吧！</p>
                                </div>
                            {/if}
                        </div>

                    {elseif condition="$action == 'add' OR $action == 'edit'"}
                        <!-- 添加/编辑案例表单 -->
                        <div class="form-header">
                            <div class="form-header-content">
                                <div class="form-title-section">
                                    <div class="form-icon">
                                        <i class="fas fa-{$action == 'add' ? 'plus' : 'edit'}"></i>
                                    </div>
                                    <div>
                                        <h1 class="form-title">{$action == 'add' ? '添加案例' : '编辑案例'}</h1>
                                        <p class="form-subtitle">{$action == 'add' ? '创建新的客户案例' : '修改案例信息'}</p>
                                    </div>
                                </div>
                                <a href="/admin/cases" class="btn-back">
                                    <i class="fas fa-arrow-left"></i>
                                    <span>返回列表</span>
                                </a>
                            </div>
                        </div>

                        <div class="form-body">
                            <form method="POST" action="/admin/cases" enctype="multipart/form-data" class="case-form">
                                <input type="hidden" name="action" value="{$action}">
                                {if condition="$editData"}
                                    <input type="hidden" name="id" value="{$editData.id}">
                                {/if}

                                <div class="form-grid">
                                    <!-- 基本信息 -->
                                    <div class="form-section">
                                        <div class="section-header">
                                            <h3 class="section-title">
                                                <i class="fas fa-info-circle"></i>
                                                基本信息
                                            </h3>
                                        </div>

                                        <div class="form-row" style="margin-top: 20px;">
                                            <div class="form-group">
                                                <label for="title" class="form-label required">
                                                    <i class="fas fa-heading"></i>
                                                    案例标题
                                                </label>
                                                <input type="text" class="form-input" id="title" name="title"
                                                       value="{$editData.title|default=''}"
                                                       placeholder="请输入案例标题" required>
                                            </div>

                                            <div class="form-group">
                                                <label for="slug" class="form-label">
                                                    <i class="fas fa-link"></i>
                                                    URL别名
                                                </label>
                                                <input type="text" class="form-input" id="slug" name="slug"
                                                       value="{$editData.slug|default=''}"
                                                       placeholder="自动生成或手动输入">
                                            </div>
                                        </div>

                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="client_name" class="form-label required">
                                                    <i class="fas fa-user"></i>
                                                    客户名称
                                                </label>
                                                <input type="text" class="form-input" id="client_name" name="client_name"
                                                       value="{$editData.client_name|default=''}"
                                                       placeholder="请输入客户名称" required>
                                            </div>

                                            <div class="form-group">
                                                <label for="industry" class="form-label required">
                                                    <i class="fas fa-industry"></i>
                                                    所属行业
                                                </label>
                                                <select class="form-select" id="industry" name="industry" required>
                                                    <option value="">请选择行业</option>
                                                    {volist name="industries" id="industryName" key="industryKey"}
                                                        <option value="{$industryName}"
                                                                {if condition="$editData && $editData.industry == $industryName"}selected{/if}>
                                                            {$industryName}
                                                        </option>
                                                    {/volist}
                                                </select>
                                            </div>
                                        </div>

                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="project_url" class="form-label">
                                                    <i class="fas fa-link"></i>
                                                    项目链接
                                                </label>
                                                <input type="url" class="form-input" id="project_url" name="project_url"
                                                       value="{$editData.project_url|default=''}"
                                                       placeholder="请输入项目链接">
                                            </div>

                                            <div class="form-group">
                                                <label for="completion_date" class="form-label">
                                                    <i class="fas fa-calendar"></i>
                                                    完成日期
                                                </label>
                                                <input type="date" class="form-input" id="completion_date" name="completion_date"
                                                       value="{$editData.completion_date|default=date('Y-m-d')}">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="summary" class="form-label">
                                                <i class="fas fa-align-left"></i>
                                                案例摘要
                                            </label>
                                            <textarea class="form-textarea" id="summary" name="summary" rows="3"
                                                      placeholder="请输入案例摘要，用于列表页显示">{$editData.summary|default=''}</textarea>
                                            <div class="form-help">建议长度在100-200字之间，用于列表页和搜索结果显示</div>
                                        </div>

                                        <div class="form-group">
                                            <label for="description" class="form-label required">
                                                <i class="fas fa-file-text"></i>
                                                案例描述
                                            </label>
                                            <!-- CKEditor 5编辑器 -->
                                            <div class="ck-editor-container">
                                                <textarea id="editor" name="description">{$editData.description|default=''}</textarea>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 图片上传 -->
                                    <div class="form-section">
                                        <div class="section-header">
                                            <h3 class="section-title">
                                                <i class="fas fa-image"></i>
                                                案例图片
                                            </h3>
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label" style="margin-top: 20px;">
                                                <i class="fas fa-upload"></i>
                                                案例图片
                                            </label>

                                            <!-- 图片上传弹窗按钮 -->
                                            <div class="image-upload-section">
                                                <button type="button" class="btn-upload-image" id="btnSelectCaseImage">
                                                    <i class="fas fa-image"></i>
                                                    <span>选择图片</span>
                                                </button>

                                                <!-- 当前图片预览 -->
                                                <div class="current-image-preview" id="currentImagePreview"
                                                     {if condition="!$editData || !$editData.image"}style="display: none;"{/if}>
                                                    <div class="preview-wrapper">
                                                        <img id="currentImageDisplay"
                                                             src="{$editData.image|default=''}"
                                                             alt="当前图片">
                                                        <div class="preview-overlay">
                                                            <button type="button" class="btn-change-image" id="btnChangeCaseImage">
                                                                <i class="fas fa-edit"></i>
                                                                更换图片
                                                            </button>
                                                            <button type="button" class="btn-remove-image" id="btnRemoveCaseImage">
                                                                <i class="fas fa-trash"></i>
                                                                删除图片
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 隐藏字段存储选中的图片URL -->
                                                <input type="hidden" id="selectedImageUrl" name="image_url"
                                                       value="{$editData.image|default=''}">
                                            </div>

                                            <div class="form-help">建议尺寸：800x600像素，支持JPG、PNG格式</div>
                                        </div>
                                    </div>

                                    <!-- 设置选项 -->
                                    <div class="form-section">
                                        <div class="section-header">
                                            <h3 class="section-title">
                                                <i class="fas fa-cog"></i>
                                                发布设置
                                            </h3>
                                        </div>

                                        <div class="form-row" style="margin-top: 20px;">
                                            <div class="form-group">
                                                <label for="sort_order" class="form-label">
                                                    <i class="fas fa-sort"></i>
                                                    排序权重
                                                </label>
                                                <input type="number" class="form-input" id="sort_order" name="sort_order"
                                                       value="{$editData.sort_order|default=0}"
                                                       placeholder="数字越大排序越靠前">
                                                <div class="form-help">数字越大排序越靠前，默认为0</div>
                                            </div>
                                        </div>

                                        <div class="form-row">
                                            <div class="form-group">
                                                <div class="checkbox-group">
                                                    <label class="checkbox-label">
                                                        <input type="checkbox" name="status" value="1"
                                                               {if condition="!$editData || $editData.status"}checked{/if}>
                                                        <span class="checkbox-custom"></span>
                                                        <span class="checkbox-text">
                                                            <i class="fas fa-eye"></i>
                                                            立即发布
                                                        </span>
                                                    </label>
                                                    <div class="checkbox-help">取消勾选将保存为草稿状态</div>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <div class="checkbox-group">
                                                    <label class="checkbox-label">
                                                        <input type="checkbox" name="is_featured" value="1"
                                                               {if condition="$editData && $editData.is_featured"}checked{/if}>
                                                        <span class="checkbox-custom"></span>
                                                        <span class="checkbox-text">
                                                            <i class="fas fa-star"></i>
                                                            推荐案例
                                                        </span>
                                                    </label>
                                                    <div class="checkbox-help">推荐案例将在首页等位置优先显示</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 表单操作按钮 -->
                                <div class="form-actions">
                                    <button type="submit" class="btn-submit">
                                        <i class="fas fa-save"></i>
                                        <span>{$action == 'add' ? '添加案例' : '保存修改'}</span>
                                    </button>
                                    <a href="/admin/cases" class="btn-cancel">
                                        <i class="fas fa-times"></i>
                                        <span>取消</span>
                                    </a>
                                </div>
                            </form>
                        </div>
                    {/if}

                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- JavaScript -->
    {include file="admin/common/js"}
    <!-- CKEditor 5 Classic版本 -->
    <script src="/assets/js/ckeditor.js"></script>
    <!-- CKEditor 5 中文语言包 -->
    <script src="/assets/js/zh-cn.js"></script>
    <!-- 图片上传弹窗组件 -->
    <script src="/assets/js/image-uploader.js"></script>
    <!-- 图片选择器扩展 -->
    <script src="/assets/js/image-selector-extension.js"></script>

    <script>
        let editor;
        let editorImageUploader; // 编辑器专用图片上传组件实例
        let caseImageUploader; // 案例图片选择器实例

        // 生成URL别名的函数
        function generateSlug(text, type = 'case') {
            if (!text || text.trim() === '') {
                return '';
            }

            let result = '';

            // 遍历每个字符
            for (let i = 0; i < text.length; i++) {
                const char = text[i];
                const code = char.charCodeAt(0);

                // 英文字母和数字直接保留
                if ((code >= 65 && code <= 90) || (code >= 97 && code <= 122) || (code >= 48 && code <= 57)) {
                    result += char.toLowerCase();
                }
                // 中文字符转换为拼音（简化版）
                else if (code >= 0x4e00 && code <= 0x9fff) {
                    // 这里可以添加更复杂的中文转拼音逻辑
                    // 暂时跳过中文字符
                    continue;
                }
                // 空格和连字符转换为连字符
                else if (char === ' ' || char === '-' || char === '_') {
                    if (result && result[result.length - 1] !== '-') {
                        result += '-';
                    }
                }
                // 其他字符跳过
            }

            // 清理结果
            result = result.replace(/^-+|-+$/g, ''); // 移除首尾连字符
            result = result.replace(/-+/g, '-'); // 多个连字符合并为一个

            // 如果结果为空或太短，使用默认值
            if (!result || result.length < 2) {
                if (type === 'case') {
                    // 案例不添加前缀，保持简洁
                    result = 'case-' + Date.now();
                } else {
                    // 其他类型保持原样
                    result = type + '-' + Date.now();
                }
            }

            return result;
        }

        $(document).ready(function() {
            // 初始化编辑器专用图片上传组件（不限制数量）
            editorImageUploader = createImageUploader({
                uploadUrl: '/admin/image/upload?context=cases',
                uploadField: 'upload',
                maxFiles: 999, // 编辑器不限制图片数量
                maxSize: 5 * 1024 * 1024, // 5MB限制
                allowedTypes: ['image/jpeg', 'image/png'], // 只允许JPG和PNG
                enableImageSelector: true,
                selectorUrl: '/admin/image/selector',
                isEditor: true, // 标记为编辑器环境
                context: 'editor', // 明确标识上下文
                instanceId: 'editor-uploader', // 唯一实例ID
                onSelect: function(files) {
                    console.log('编辑器选择了文件:', files);

                    // 验证文件类型和大小
                    const validFiles = [];
                    const errors = [];

                    files.forEach(file => {
                        // 检查文件类型
                        if (!['image/jpeg', 'image/png'].includes(file.type)) {
                            errors.push(`${file.name}: 不支持的文件类型`);
                            return;
                        }

                        // 检查文件大小
                        if (file.size > 5 * 1024 * 1024) {
                            errors.push(`${file.name}: 文件大小超过5MB`);
                            return;
                        }

                        validFiles.push(file);
                    });

                    if (errors.length > 0) {
                        editorImageUploader.showMessage('部分文件无效：\n' + errors.join('\n'), 'error');
                    }

                    return validFiles;
                },
                onConfirm: function(orderedData, mode) {
                    console.log('编辑器确认选择的数据（按顺序）:', orderedData, '模式:', mode);

                    if (mode === 'select') {
                        // 图片选择模式 - 直接插入已有图片到编辑器
                        insertSelectedImagesToEditor(orderedData);
                    } else {
                        // 文件上传模式 - 插入文件预览到编辑器
                        insertOrderedFilesToEditor(orderedData);
                    }
                },
                onUpload: function(uploadedFiles) {
                    console.log('编辑器上传成功:', uploadedFiles);
                    // 将上传的图片按顺序插入到编辑器中
                    uploadedFiles.forEach((fileData, index) => {
                        setTimeout(() => {
                            insertImageToEditor(fileData.url);
                        }, index * 100); // 延时确保按顺序插入
                    });
                    editorImageUploader.close();
                },
                onError: function(error) {
                    console.error('编辑器上传错误:', error);
                    editorImageUploader.showMessage('图片上传失败：' + error.message, 'error');
                }
            });

            // 初始化CKEditor 5编辑器 - Classic版本
            if ($('#editor').length) {
                ClassicEditor
                    .create(document.querySelector('#editor'), {
                        language: 'zh-cn',
                        placeholder: '请输入案例描述...',
                        toolbar: [
                            'heading',
                            'bold',
                            'italic',
                            'underline',
                            'numberedList',
                            'bulletedList',
                            'outdent',
                            'indent',
                            'link',
                            'insertTable',
                            'blockQuote',
                            'undo',
                            'redo'
                        ],
                        heading: {
                            options: [
                                { model: 'paragraph', title: '段落', class: 'ck-heading_paragraph' },
                                { model: 'heading1', view: 'h1', title: '标题 1', class: 'ck-heading_heading1' },
                                { model: 'heading2', view: 'h2', title: '标题 2', class: 'ck-heading_heading2' },
                                { model: 'heading3', view: 'h3', title: '标题 3', class: 'ck-heading_heading3' }
                            ]
                        },
                        table: {
                            contentToolbar: [
                                'tableColumn',
                                'tableRow',
                                'mergeTableCells'
                            ]
                        },
                        link: {
                            decorators: {
                                openInNewTab: {
                                    mode: 'manual',
                                    label: '在新标签页中打开',
                                    attributes: {
                                        target: '_blank',
                                        rel: 'noopener noreferrer'
                                    }
                                }
                            }
                        }
                    })
                    .then(newEditor => {
                        editor = newEditor;
                        window.editor = newEditor;

                        // 设置编辑器高度
                        const editingView = editor.editing.view;
                        editingView.change(writer => {
                            writer.setStyle('min-height', '300px', editingView.document.getRoot());
                            writer.setStyle('max-height', '500px', editingView.document.getRoot());
                        });

                        // 设置焦点样式
                        editor.ui.focusTracker.on('change:isFocused', (evt, name, isFocused) => {
                            if (isFocused) {
                                $('.ck-editor-container').addClass('focused');
                            } else {
                                $('.ck-editor-container').removeClass('focused');
                            }
                        });

                        // 添加图片上传按钮 - 使用新的弹窗组件
                        setTimeout(() => {
                            addImageUploadButton();
                        }, 1000);

                        console.log('✅ CKEditor 5 Classic 初始化成功');
                    })
                    .catch(error => {
                        console.error('❌ CKEditor 5 初始化失败:', error);
                        // 降级到普通textarea
                        $('#editor').addClass('form-textarea').attr('rows', 15).show();

                        // 显示错误提示
                        showMessage('编辑器加载失败，已切换到基础模式', 'warning');

                        // 确保window.editor为null，表单验证会使用textarea
                        window.editor = null;
                    });
            }

            // 添加图片上传按钮 - 使用新的弹窗组件
            function addImageUploadButton() {
                // 多种方式查找工具栏
                let toolbarItems = document.querySelector('.ck-toolbar .ck-toolbar__items');
                if (!toolbarItems) {
                    toolbarItems = document.querySelector('.ck-toolbar__items');
                }
                if (!toolbarItems) {
                    toolbarItems = document.querySelector('.ck-toolbar');
                }

                if (!toolbarItems) {
                    return false;
                }

                // 检查按钮是否已存在
                if (document.querySelector('[data-upload-button="true"]')) {
                    return true;
                }

                // 创建图片上传按钮
                const imageButton = document.createElement('button');
                imageButton.className = 'ck-button ck-button_with-text';
                imageButton.type = 'button';
                imageButton.setAttribute('data-upload-button', 'true');
                imageButton.setAttribute('title', '上传图片');
                imageButton.setAttribute('aria-label', '上传图片');

                // 创建图标容器
                const iconContainer = document.createElement('span');
                iconContainer.className = 'ck-button__icon';

                // 使用FontAwesome图标
                const icon = document.createElement('i');
                icon.className = 'fas fa-images';
                icon.style.cssText = `
                    font-size: 12px !important;
                    color: rgba(255, 255, 255, 0.8) !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                    display: inline-block !important;
                `;
                iconContainer.appendChild(icon);
                imageButton.appendChild(iconContainer);

                // 添加文本标签
                const textLabel = document.createElement('span');
                textLabel.className = 'ck-button__label';
                textLabel.textContent = '图片';
                imageButton.appendChild(textLabel);

                // 绑定点击事件 - 打开编辑器专用弹窗
                imageButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🖼️ 打开编辑器图片上传弹窗');

                    if (editorImageUploader) {
                        editorImageUploader.show();
                    } else {
                        console.error('❌ 编辑器图片上传组件未初始化');
                    }
                });

                // 插入到工具栏末尾
                toolbarItems.appendChild(imageButton);
                console.log('✅ 图片按钮已添加到工具栏');
                return true;
            }

            // 初始化图片选择器
            initCaseImageUploader();

            // 删除图片 - 直接执行，无确认提示
            $('#btnRemoveCaseImage').click(function(e) {
                e.preventDefault();
                e.stopPropagation();

                console.log('删除图片按钮被点击，直接执行删除');

                // 直接执行删除操作
                $('#selectedImageUrl').val('');
                $('#currentImageDisplay').attr('src', '');
                $('#currentImagePreview').hide();
                if (caseImageUploader) {
                    caseImageUploader.showMessage('图片已删除', 'success');
                } else {
                showMessage('图片已删除', 'info');
                }
            });

            // 标题输入时自动生成slug
            $('#title').on('input', function() {
                const title = $(this).val();
                const slug = generateSlug(title, 'case');
                $('#slug').val(slug);
            });

            // 表单提交验证 - 使用与News页面相同的逻辑
            $('form').on('submit', function(e) {
                console.log('表单提交验证开始');

                // 检查是否在编辑器页面（有CKEditor）
                if ($('#editor').length > 0) {
                    console.log('检测到编辑器，执行案例表单验证');
                    return validateCaseForm(e);
                }

                // 其他表单不做特殊验证
                console.log('其他表单，跳过特殊验证');
                return true;
            });

            // 案例表单验证函数
            function validateCaseForm(e) {
                // 如果CKEditor存在，同步内容
                if (window.editor) {
                    const data = window.editor.getData();
                    $('#editor').val(data);

                    // 验证编辑器内容
                    if (!data || data.trim() === '') {
                        e.preventDefault();
                        showMessage('请输入案例描述', 'warning');

                        // 聚焦到编辑器
                        setTimeout(() => {
                            if (window.editor) {
                                window.editor.editing.view.focus();
                            }
                        }, 100);
                        return false;
                    }
                } else {
                    // 如果CKEditor未初始化，检查原始textarea
                    const content = $('#editor').val();
                    if (!content || content.trim() === '') {
                        e.preventDefault();
                        showMessage('请输入案例描述', 'warning');
                        $('#editor').focus();
                        return false;
                    }
                }

                // 验证标题
                const title = $('#title').val();
                if (!title || title.trim() === '') {
                    e.preventDefault();
                    showMessage('请输入案例标题', 'warning');
                    $('#title').focus();
                    return false;
                }

                // 验证客户名称
                const clientName = $('#client_name').val();
                if (!clientName || clientName.trim() === '') {
                    e.preventDefault();
                    showMessage('请输入客户名称', 'warning');
                    $('#client_name').focus();
                    return false;
                }

                // 验证所属行业
                const industry = $('#industry').val();
                if (!industry || industry.trim() === '') {
                    e.preventDefault();
                    showMessage('请选择所属行业', 'warning');
                    $('#industry').focus();
                    return false;
                }

                return true;
            }
        });

        // 插入选择的图片到编辑器（已有图片）
        function insertSelectedImagesToEditor(selectedImages) {
            if (!window.editor) {
                console.error('❌ 编辑器实例不存在');
                return;
            }

            console.log(`📋 开始按顺序插入 ${selectedImages.length} 张已选择的图片到编辑器`);

            selectedImages.forEach((image, index) => {
                setTimeout(() => {
                    try {
                        // 插入最终图片
                        const imageHtml = `
                            <img src="${image.file_url}" alt="${image.alt_text || image.filename}"
                                 style="max-width: 100%; height: auto; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                        `;

                        // 获取当前选择位置
                        const selection = window.editor.model.document.selection;
                        const position = selection.getLastPosition();

                        // 将HTML转换为模型并插入
                        const viewFragment = window.editor.data.processor.toView(imageHtml);
                        const modelFragment = window.editor.data.toModel(viewFragment);

                        window.editor.model.change(writer => {
                            window.editor.model.insertContent(modelFragment, position);
                        });

                        console.log(`✅ 第 ${index + 1} 张图片已插入: ${image.filename}`);

                        if (index === selectedImages.length - 1) {
                                editorImageUploader.showMessage(`已按顺序插入 ${selectedImages.length} 张图片`, 'success');
                        }
                    } catch (error) {
                        console.error(`❌ 第 ${index + 1} 张图片插入失败:`, error);
                    }
                }, index * 200); // 延时确保按顺序插入
            });
        }

        // 按顺序插入文件到编辑器（仅插入预览，不上传）
        function insertOrderedFilesToEditor(orderedFiles) {
            if (!window.editor) {
                console.error('❌ 编辑器实例不存在');
                return;
            }

            console.log(`📋 开始按顺序插入 ${orderedFiles.length} 个文件到编辑器`);

            orderedFiles.forEach((file, index) => {
                // 创建本地预览URL
                const localUrl = URL.createObjectURL(file);

                setTimeout(() => {
                    try {
                        // 插入预览图片（带上传提示）
                        const placeholderHtml = `
                            <div style="border: 2px dashed #ccc; padding: 20px; margin: 10px 0; text-align: center; border-radius: 8px; background: #f9f9f9;">
                                <img src="${localUrl}" alt="${file.name}" style="max-width: 200px; height: auto; border-radius: 4px; margin-bottom: 10px;">
                                <p style="margin: 0; color: #666; font-size: 14px;">
                                    📁 ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)<br>
                                    <small>⏳ 等待上传...</small>
                                </p>
                            </div>
                        `;

                        // 获取当前选择位置
                        const selection = window.editor.model.document.selection;
                        const position = selection.getLastPosition();

                        // 将HTML转换为模型并插入
                        const viewFragment = window.editor.data.processor.toView(placeholderHtml);
                        const modelFragment = window.editor.data.toModel(viewFragment);

                        window.editor.model.change(writer => {
                            window.editor.model.insertContent(modelFragment, position);
                        });

                        console.log(`✅ 第 ${index + 1} 张图片预览已插入: ${file.name}`);

                        if (index === orderedFiles.length - 1) {
                            editorImageUploader.showMessage(`已按顺序插入 ${orderedFiles.length} 张图片预览`, 'success');
                        }
                    } catch (error) {
                        console.error(`❌ 第 ${index + 1} 张图片插入失败:`, error);
                    }
                }, index * 200); // 延时确保按顺序插入
            });
        }

        // 将图片插入到编辑器中（上传后的最终图片）
        function insertImageToEditor(imageUrl) {
            if (!window.editor) {
                console.error('❌ 编辑器实例不存在');
                return;
            }

            try {
                // 使用HTML方式插入图片
                const imageHtml = `<img src="${imageUrl}" alt="上传的图片" style="max-width: 100%; height: auto; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">`;

                // 获取当前选择位置
                const selection = window.editor.model.document.selection;
                const position = selection.getLastPosition();

                // 将HTML转换为模型并插入
                const viewFragment = window.editor.data.processor.toView(imageHtml);
                const modelFragment = window.editor.data.toModel(viewFragment);

                window.editor.model.change(writer => {
                    window.editor.model.insertContent(modelFragment, position);
                });

                console.log('✅ 图片已插入编辑器:', imageUrl);
            } catch (error) {
                console.error('❌ 图片插入失败:', error);
                editorImageUploader.showMessage('图片插入失败', 'error');
            }
        }

        // 初始化案例图片上传器
        function initCaseImageUploader() {
            // 创建案例图片选择器实例
            caseImageUploader = createImageUploader({
                uploadUrl: '/admin/image/upload?context=cases',
                uploadField: 'upload',
                maxFiles: 1, // 案例只需要一张主图
                maxSize: 5 * 1024 * 1024, // 5MB限制
                allowedTypes: ['image/jpeg', 'image/png'], // 只允许JPG和PNG
                enableImageSelector: true,
                selectorUrl: '/admin/image/selector',
                isEditor: false, // 不是编辑器环境
                context: 'case-image', // 明确标识上下文
                instanceId: 'case-image-uploader', // 唯一实例ID
                onSelect: function(files) {
                    console.log('案例图片选择了文件:', files);

                    // 验证文件类型和大小
                    const validFiles = [];
                    const errors = [];

                    files.forEach(file => {
                        // 检查文件类型
                        if (!['image/jpeg', 'image/png'].includes(file.type)) {
                            errors.push(`${file.name}: 不支持的文件类型`);
                            return;
                        }

                        // 检查文件大小
                        if (file.size > 5 * 1024 * 1024) {
                            errors.push(`${file.name}: 文件大小超过5MB`);
                            return;
                        }

                        validFiles.push(file);
                    });

                    if (errors.length > 0) {
                        // 使用图片上传器自己的提示
                        caseImageUploader.showMessage('部分文件无效：\n' + errors.join('\n'), 'error');
                    }

                    return validFiles;
                },
                onConfirm: function(orderedData, mode) {
                    console.log('案例图片确认选择的数据:', orderedData, '模式:', mode);

                    if (orderedData && orderedData.length > 0) {
                        const selectedItem = orderedData[0]; // 只取第一个

                        if (mode === 'select') {
                            // 图片选择模式 - 使用已有图片
                            $('#selectedImageUrl').val(selectedItem.file_url);
                            $('#currentImageDisplay').attr('src', selectedItem.file_url);
                            $('#currentImagePreview').show();
                            // 使用图片上传器自己的提示
                            caseImageUploader.showMessage('图片选择成功', 'success');
                        } else {
                            // 文件上传模式 - 显示预览，等待上传
                            const localUrl = URL.createObjectURL(selectedItem);
                            $('#currentImageDisplay').attr('src', localUrl);
                            $('#currentImagePreview').show();
                            // 使用图片上传器自己的提示
                            caseImageUploader.showMessage('图片预览已设置，提交表单时将自动上传', 'info');
                        }
                    }

                    caseImageUploader.close();
                },
                onUpload: function(uploadedFiles) {
                    console.log('案例图片上传成功:', uploadedFiles);
                    if (uploadedFiles && uploadedFiles.length > 0) {
                        const uploadedFile = uploadedFiles[0];
                        $('#selectedImageUrl').val(uploadedFile.url);
                        $('#currentImageDisplay').attr('src', uploadedFile.url);
                        $('#currentImagePreview').show();
                        // 使用图片上传器自己的提示
                        caseImageUploader.showMessage('图片上传成功', 'success');
                    }
                    caseImageUploader.close();
                },
                onError: function(error) {
                    console.error('案例图片上传错误:', error);
                    // 使用图片上传器自己的提示
                    caseImageUploader.showMessage('图片处理失败：' + error.message, 'error');
                }
            });

            // 绑定选择图片按钮
            $('#btnSelectCaseImage, #btnChangeCaseImage').click(function() {
                console.log('打开案例图片选择器');
                if (caseImageUploader) {
                    caseImageUploader.show();
                } else {
                    console.error('❌ 案例图片上传组件未初始化');
                }
            });
        }

        // 切换状态
        function toggleStatus(id, status) {
            $.post('/admin/cases', {
                action: 'toggle_status',
                id: id,
                status: status
            }, function(response) {
                if (response.success) {
                    showMessage(response.message || '状态更新成功', 'success');
                } else {
                    showMessage(response.message || '状态更新失败', 'error');
                    // 恢复开关状态
                    $('input[onchange*="' + id + '"]').prop('checked', !status);
                }
            }, 'json').fail(function() {
                showMessage('网络错误，请重试', 'error');
                // 恢复开关状态
                $('input[onchange*="' + id + '"]').prop('checked', !status);
            });
        }

        // 删除案例功能已移至admin.js统一管理

    </script>
</body>
</html>
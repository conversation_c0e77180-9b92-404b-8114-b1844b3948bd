<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-10
 * 系统菜单管理控制器 - ThinkPHP6企业级应用
 * 功能：系统菜单的增删改查、链接选择器、层级管理
 */

namespace app\admin\controller;

use think\facade\Db;
use think\facade\View;
use think\facade\Session;
use think\Request;
use think\response\Json;

class SysMenu extends Base
{
    /**
     * 系统菜单管理首页
     */
    public function index(Request $request)
    {
        if ($request->isPost()) {
            $action = $request->post('action');
            
            switch ($action) {
                case 'getMenuList':
                    return $this->getMenuList();
                case 'save':
                    return $this->save($request);
                case 'delete':
                    return $this->delete($request);
                case 'updateStatus':
                    return $this->updateStatus($request);
                case 'sort':
                    return $this->sort($request);
                case 'getLinkData':
                    return $this->getLinkData($request);
                default:
                    return json(['success' => false, 'message' => '无效的操作']);
            }
        }

        // GET请求处理删除
        $deleteId = $request->get('action') === 'delete' ? $request->get('id', 0) : 0;
        if ($deleteId > 0) {
            return $this->handleDelete($deleteId);
        }

        // GET请求显示页面
        $editId = $request->get('edit', 0);
        $parentId = $request->get('parent_id', 0);
        
        $data = [
            'pageTitle' => '系统菜单管理',
            'editMode' => $editId > 0,
            'editId' => $editId,
            'parentId' => $parentId,
            'menu' => null,
            'parentMenus' => [],
            'linkOptions' => [],
            'message' => Session::pull('message'),
            'messageType' => Session::pull('messageType')
        ];

        // 如果是编辑模式，获取菜单数据
        if ($editId > 0) {
            $data['menu'] = Db::table('sys_menu')->where('id', $editId)->find();
            if (!$data['menu']) {
                $this->error('菜单不存在');
            }
            $data['pageTitle'] = '编辑菜单';
        } elseif ($parentId > 0) {
            $data['pageTitle'] = '添加子菜单';
        } else {
            $data['pageTitle'] = '添加菜单';
        }

        // 获取父级菜单列表
        $data['parentMenus'] = Db::table('sys_menu')
            ->where('parent_id', 0)
            ->where('status', 1)
            ->order('sort_order', 'asc')
            ->select()
            ->toArray();

        // 获取链接选择器数据
        $data['linkOptions'] = $this->getLinkOptions();

        return View::fetch('admin/sys_menu', $data);
    }

    /**
     * 获取菜单列表
     */
    private function getMenuList(): Json
    {
        try {
            $menus = $this->getMenuTree();
            $stats = $this->getMenuStats();
            
            return json([
                'success' => true,
                'data' => $menus,
                'stats' => $stats,
                'message' => '获取成功'
            ]);
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 保存菜单
     */
    private function save(Request $request): Json
    {
        try {
            $data = $request->post();
            
            // 验证数据
            if (empty($data['name'])) {
                return json(['success' => false, 'message' => '菜单名称不能为空']);
            }
            
            if (empty($data['link_type'])) {
                return json(['success' => false, 'message' => '链接类型不能为空']);
            }
            
            if (empty($data['link_value'])) {
                return json(['success' => false, 'message' => '链接值不能为空']);
            }

            $id = $data['id'] ?? 0;
            $saveData = [
                'parent_id' => $data['parent_id'] ?? 0,
                'name' => $data['name'],
                'link_type' => $data['link_type'],
                'link_value' => $data['link_value'],
                'icon' => $data['icon'] ?? '',
                'sort_order' => $data['sort_order'] ?? 0,
                'status' => $data['status'] ?? 1,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            if ($id > 0) {
                // 更新
                Db::table('sys_menu')->where('id', $id)->update($saveData);
                $message = '更新成功';
            } else {
                // 新增
                $saveData['created_at'] = date('Y-m-d H:i:s');
                Db::table('sys_menu')->insert($saveData);
                $message = '添加成功';
            }

            return json(['success' => true, 'message' => $message]);

        } catch (\Exception $e) {
            return json(['success' => false, 'message' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 处理GET请求删除
     */
    private function handleDelete(int $id)
    {
        try {
            // 获取菜单信息
            $menu = Db::table('sys_menu')->where('id', $id)->find();
            if (!$menu) {
                Session::flash('message', '菜单不存在');
                Session::flash('messageType', 'error');
                return redirect('/admin/sys-menu');
            }

            // 检查是否有子菜单
            $children = Db::table('sys_menu')->where('parent_id', $id)->select()->toArray();
            if (count($children) > 0) {
                $childNames = array_column($children, 'name');
                Session::flash('message', '请先删除子菜单：' . implode('、', $childNames));
                Session::flash('messageType', 'error');
                return redirect('/admin/sys-menu');
            }

            Db::table('sys_menu')->where('id', $id)->delete();

            Session::flash('message', '菜单"' . $menu['name'] . '"删除成功');
            Session::flash('messageType', 'success');

        } catch (\Exception $e) {
            Session::flash('message', '删除失败：' . $e->getMessage());
            Session::flash('messageType', 'error');
        }

        return redirect('/admin/sys-menu');
    }

    /**
     * 删除菜单
     */
    private function delete(Request $request): Json
    {
        try {
            $id = $request->post('id');
            
            // 检查是否有子菜单
            $hasChildren = Db::table('sys_menu')->where('parent_id', $id)->count();
            if ($hasChildren > 0) {
                return json(['success' => false, 'message' => '请先删除子菜单']);
            }

            Db::table('sys_menu')->where('id', $id)->delete();

            return json(['success' => true, 'message' => '删除成功']);

        } catch (\Exception $e) {
            return json(['success' => false, 'message' => '删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 更新菜单状态
     */
    private function updateStatus(Request $request): Json
    {
        try {
            $id = $request->post('id');
            $status = $request->post('status');
            
            Db::table('sys_menu')->where('id', $id)->update([
                'status' => $status,
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            return json(['success' => true, 'message' => '状态更新成功']);

        } catch (\Exception $e) {
            return json(['success' => false, 'message' => '更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 排序菜单
     */
    private function sort(Request $request): Json
    {
        try {
            $data = $request->post('data');
            
            foreach ($data as $item) {
                Db::table('sys_menu')->where('id', $item['id'])->update([
                    'sort_order' => $item['sort_order'],
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }

            return json(['success' => true, 'message' => '排序成功']);

        } catch (\Exception $e) {
            return json(['success' => false, 'message' => '排序失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取链接数据
     */
    private function getLinkData(Request $request): Json
    {
        try {
            $type = $request->post('type');
            $linkOptions = $this->getLinkOptions();
            
            if (!isset($linkOptions[$type])) {
                return json(['success' => false, 'message' => '无效的链接类型']);
            }

            return json([
                'success' => true,
                'data' => $linkOptions[$type],
                'message' => '获取成功'
            ]);

        } catch (\Exception $e) {
            return json(['success' => false, 'message' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 前端API：获取菜单数据
     */
    public function apiMenus()
    {
        try {
            $menus = $this->getFrontendMenuTree();

            return json([
                'success' => true,
                'data' => $menus,
                'message' => '获取成功'
            ]);
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取前端菜单树形结构
     */
    private function getFrontendMenuTree($parentId = 0): array
    {
        $menus = Db::table('sys_menu')
            ->where('parent_id', $parentId)
            ->where('status', 1) // 只获取启用的菜单
            ->order('sort_order', 'asc')
            ->select()
            ->toArray();

        foreach ($menus as &$menu) {
            $menu['children'] = $this->getFrontendMenuTree($menu['id']);
            $menu['url'] = $this->buildMenuUrl($menu);
        }

        return $menus;
    }

    /**
     * 获取菜单树形结构
     */
    private function getMenuTree($parentId = 0): array
    {
        $menus = Db::table('sys_menu')
            ->where('parent_id', $parentId)
            ->order('sort_order', 'asc')
            ->select()
            ->toArray();

        foreach ($menus as &$menu) {
            $menu['children'] = $this->getMenuTree($menu['id']);
            $menu['url'] = $this->buildMenuUrl($menu);
        }

        return $menus;
    }

    /**
     * 构建菜单URL
     */
    private function buildMenuUrl(array $menu): string
    {
        switch ($menu['link_type']) {
            case 'url':
                return $menu['link_value'];
            case 'page':
                // 页面类型：从选项中查找对应的slug
                $pageOptions = [
                    'home' => '/',
                    'contact' => '/contact',
                    'products' => '/products',
                    'solutions' => '/solutions',
                    'cases' => '/cases',
                    'news' => '/news'
                ];
                return $pageOptions[$menu['link_value']] ?? $menu['link_value'];
            case 'product_category':
                return '/products/' . $menu['link_value'];
            case 'product':
                return '/products/' . $menu['link_value'];
            case 'solution':
                return '/solutions/' . $menu['link_value'];
            case 'case':
                return '/cases/' . $menu['link_value'];
            case 'news_category':
                return '/news/category/' . $menu['link_value'];
            case 'news':
                return '/news/' . $menu['link_value'];
            case 'template':
                return '/page/' . $menu['link_value'];
            default:
                return '#';
        }
    }

    /**
     * 获取菜单统计信息
     */
    private function getMenuStats(): array
    {
        return [
            'total' => Db::table('sys_menu')->count(),
            'enabled' => Db::table('sys_menu')->where('status', 1)->count(),
            'disabled' => Db::table('sys_menu')->where('status', 0)->count(),
            'top_level' => Db::table('sys_menu')->where('parent_id', 0)->count(),
            'sub_level' => Db::table('sys_menu')->where('parent_id', '>', 0)->count()
        ];
    }

    /**
     * 获取链接选择器数据
     */
    private function getLinkOptions(): array
    {
        return [
            'product_category' => Db::table('product_categories')->where('status', 1)
                ->field('id,name,slug')->order('sort_order', 'asc')->select()->toArray(),
            'product' => Db::table('products')->where('status', 1)
                ->field('id,name,slug')->order('sort_order', 'asc')->select()->toArray(),
            'solution' => Db::table('solutions')->where('status', 1)
                ->field('id,name,slug')->order('sort_order', 'asc')->select()->toArray(),
            'case' => Db::table('cases')->where('status', 1)
                ->field('id,title as name,slug')->order('sort_order', 'asc')->select()->toArray(),
            'news_category' => Db::table('news_categories')->where('status', 1)
                ->field('id,name,slug')->order('sort_order', 'asc')->select()->toArray(),
            'news' => Db::table('news')->where('status', 1)
                ->field('id,title as name,slug')->order('created_at', 'desc')->limit(50)->select()->toArray(),
            'template' => Db::table('page_templates')->where('status', 1)
                ->field('id,name,slug')->order('sort_order', 'asc')->select()->toArray(),
            'page' => [
                ['id' => 'home', 'name' => '首页', 'slug' => '/'],
                ['id' => 'products', 'name' => '产品中心', 'slug' => '/products'],
                ['id' => 'solutions', 'name' => '解决方案', 'slug' => '/solutions'],
                ['id' => 'cases', 'name' => '成功案例', 'slug' => '/cases'],
                ['id' => 'news', 'name' => '新闻资讯', 'slug' => '/news'],
                ['id' => 'contact', 'name' => '联系我们', 'slug' => '/contact']
            ]
        ];
    }


}

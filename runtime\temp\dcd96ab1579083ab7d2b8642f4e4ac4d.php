<?php /*a:7:{s:57:"D:\EServer\core\www\san.com\app\view\admin\solutions.html";i:1749686313;s:58:"D:\EServer\core\www\san.com\app\view\admin\common\css.html";i:1749480866;s:61:"D:\EServer\core\www\san.com\app\view\admin\common\header.html";i:1748947478;s:62:"D:\EServer\core\www\san.com\app\view\admin\common\sidebar.html";i:1749580694;s:62:"D:\EServer\core\www\san.com\app\view\admin\common\message.html";i:1749486569;s:69:"D:\EServer\core\www\san.com\app\view\admin\common\message-styles.html";i:1749487940;s:57:"D:\EServer\core\www\san.com\app\view\admin\common\js.html";i:1749679067;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>解决方案管理 - 后台管理系统</title>

    <!-- CSS -->
    <link rel="stylesheet" href="/assets/css/bootstrap.min.css">
<link rel="stylesheet" href="/assets/css/all.min.css">
<link rel="stylesheet" href="/assets/css/admin.css">
<link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="/assets/css/admin/news.css">
    <link rel="stylesheet" href="/assets/css/image-uploader.css">
    <!-- CKEditor 5 CSS -->
    <link rel="stylesheet" href="/assets/css/ckeditor.css">

    <!-- 图标选择器样式 -->
    <style>
        /* 图标选择器容器 */
        .icon-selector-container {
            margin-top: 10px;
        }

        .icon-input-group {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 15px;
        }

        .icon-input-group .form-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(120, 119, 198, 0.3);
            color: #fff;
        }

        .btn-icon-selector {
            background: linear-gradient(135deg,
                rgba(120, 119, 198, 0.8) 0%,
                rgba(255, 119, 198, 0.6) 50%,
                rgba(120, 219, 255, 0.7) 100%);
            border: 1px solid rgba(120, 119, 198, 0.6);
            color: #ffffff;
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
        }

        .btn-icon-selector:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(120, 119, 198, 0.4);
        }

        .icon-preview {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(120, 119, 198, 0.3);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin-bottom: 10px;
        }

        .preview-icon {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            color: #fff;
        }

        .preview-icon i {
            font-size: 32px;
            color: rgba(120, 219, 255, 0.9);
        }

        .preview-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            color: rgba(255, 255, 255, 0.5);
        }

        .preview-placeholder i {
            font-size: 32px;
            color: rgba(255, 255, 255, 0.3);
        }

        /* 图标选择器弹窗 */
        .icon-selector-modal {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background: rgba(0, 0, 0, 0.85) !important;
            z-index: 99999 !important;
            display: none !important;
            align-items: center !important;
            justify-content: center !important;
            backdrop-filter: blur(5px) !important;
        }

        .icon-selector-modal.show {
            display: flex !important;
        }

        .icon-selector-content {
            background: linear-gradient(135deg, rgba(15, 15, 15, 0.98), rgba(25, 25, 35, 0.98)) !important;
            border: 1px solid rgba(120, 119, 198, 0.5) !important;
            border-radius: 12px !important;
            width: 90% !important;
            max-width: 900px !important;
            max-height: 85vh !important;
            overflow: hidden !important;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8) !important;
            position: relative !important;
            margin: auto !important;
        }

        .icon-selector-header {
            padding: 20px;
            border-bottom: 1px solid rgba(120, 119, 198, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .icon-selector-title {
            color: #fff;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .icon-selector-close {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .icon-selector-close:hover {
            color: #fff;
            background: rgba(255, 255, 255, 0.1);
        }

        .icon-selector-body {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .icon-search {
            width: 100%;
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(120, 119, 198, 0.3);
            border-radius: 8px;
            color: #fff;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .icon-search::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 10px;
        }

        .icon-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(120, 119, 198, 0.3);
            border-radius: 8px;
            padding: 15px 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: rgba(255, 255, 255, 0.8);
        }

        .icon-item:hover {
            background: rgba(120, 119, 198, 0.2);
            border-color: rgba(120, 119, 198, 0.6);
            transform: translateY(-2px);
        }

        .icon-item.selected {
            background: rgba(120, 119, 198, 0.3);
            border-color: rgba(120, 219, 255, 0.8);
            color: #fff;
        }

        .icon-item i {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }

        .icon-item span {
            font-size: 10px;
            display: block;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <!-- 顶部导航 -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <div class="navbar-left">
            <button class="btn btn-link d-md-none" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
            <a class="navbar-brand" href="/admin">
                <i class="fas fa-cogs"></i>
                <span>后台管理</span>
            </a>
        </div>

        <div class="navbar-nav">
            <a class="nav-link" href="/" target="_blank" style="margin-right: 15px;">
                <i class="fas fa-external-link-alt"></i>
                <span>查看网站</span>
            </a>
            <span class="nav-link" style="margin-right: 15px; color: rgba(255,255,255,0.8);">
                <i class="fas fa-user"></i>
                <span>
                    <?php if(isset($admin_info['real_name']) && $admin_info['real_name']): ?>
                        <?php echo htmlentities((string) $admin_info['real_name']); elseif(isset($admin_info['username']) && $admin_info['username']): ?>
                        <?php echo htmlentities((string) $admin_info['username']); else: ?>
                        系统管理员
                    <?php endif; ?>
                </span>
            </span>
            <a class="nav-link" href="javascript:void(0);" onclick="confirmLogout()" style="color: #ff6b6b;">
                <i class="fas fa-sign-out-alt"></i>
                <span>退出登录</span>
            </a>
        </div>
    </div>
</nav>

<script>
// 退出登录确认对话框
function confirmLogout() {
    // 创建自定义确认对话框
    const modal = document.createElement('div');
    modal.className = 'logout-modal-overlay';
    modal.innerHTML = `
        <div class="logout-modal">
            <div class="logout-modal-header">
                <i class="fas fa-sign-out-alt"></i>
                <h4>确认退出登录</h4>
            </div>
            <div class="logout-modal-body">
                <p>您确定要退出登录吗？</p>
            </div>
            <div class="logout-modal-footer">
                <button class="btn-cancel" onclick="closeLogoutModal()">
                    <i class="fas fa-times"></i> 取消
                </button>
                <button class="btn-confirm" onclick="doLogout()">
                    <i class="fas fa-check"></i> 确认退出
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 添加动画效果
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
}

function closeLogoutModal() {
    const modal = document.querySelector('.logout-modal-overlay');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

function doLogout() {
    window.location.href = '/admin/logout';
}

// 点击遮罩层关闭对话框
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('logout-modal-overlay')) {
        closeLogoutModal();
    }
});


</script>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <!-- 侧边栏 -->
<nav class="col-md-3 col-lg-2 sidebar">
    <div class="sidebar-sticky">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="/admin" data-controller="Index">
                    <i class="fas fa-tachometer-alt"></i> 仪表盘
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/banners" data-controller="Banners">
                    <i class="fas fa-images"></i> 轮播图管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/products" data-controller="Products">
                    <i class="fas fa-cube"></i> 产品管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/solutions" data-controller="Solutions">
                    <i class="fas fa-lightbulb"></i> 解决方案
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/news" data-controller="News">
                    <i class="fas fa-newspaper"></i> 新闻管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/cases" data-controller="Cases">
                    <i class="fas fa-briefcase"></i> 客户案例
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/contacts" data-controller="Contacts">
                    <i class="fas fa-envelope"></i> 联系表单
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/page-builder" data-controller="PageBuilder">
                    <i class="fas fa-paint-brush"></i> 页面装修
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/sys-menu" data-controller="SysMenu">
                    <i class="fas fa-sitemap"></i> 系统菜单
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/settings" data-controller="Settings">
                    <i class="fas fa-cog"></i> 系统设置
                </a>
            </li>
        </ul>
    </div>
</nav>

<!-- 移动端遮罩层 -->
<div class="sidebar-overlay d-md-none"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取当前URL路径
    const currentPath = window.location.pathname;
    console.log('当前路径:', currentPath);

    // 移除所有active状态
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });

    // 根据URL路径设置active状态
    if (currentPath === '/admin' || currentPath === '/admin/' || currentPath.endsWith('/admin/index')) {
        const indexLink = document.querySelector('[data-controller="Index"]');
        if (indexLink) {
            indexLink.classList.add('active');
            console.log('设置仪表盘为active');
        }
    } else if (currentPath.includes('/admin/banners')) {
        const bannersLink = document.querySelector('[data-controller="Banners"]');
        if (bannersLink) {
            bannersLink.classList.add('active');
            console.log('设置轮播图为active');
        }
    } else if (currentPath.includes('/admin/contacts')) {
        const contactsLink = document.querySelector('[data-controller="Contacts"]');
        if (contactsLink) {
            contactsLink.classList.add('active');
            console.log('设置联系表单为active');
        }
    } else if (currentPath.includes('/admin/products')) {
        const productsLink = document.querySelector('[data-controller="Products"]');
        if (productsLink) {
            productsLink.classList.add('active');
            console.log('设置产品管理为active');
        }
    } else if (currentPath.includes('/admin/solutions')) {
        const solutionsLink = document.querySelector('[data-controller="Solutions"]');
        if (solutionsLink) {
            solutionsLink.classList.add('active');
            console.log('设置解决方案为active');
        }
    } else if (currentPath.includes('/admin/news')) {
        const newsLink = document.querySelector('[data-controller="News"]');
        if (newsLink) {
            newsLink.classList.add('active');
            console.log('设置新闻管理为active');
        }
    } else if (currentPath.includes('/admin/cases')) {
        const casesLink = document.querySelector('[data-controller="Cases"]');
        if (casesLink) {
            casesLink.classList.add('active');
            console.log('设置客户案例为active');
        }
    } else if (currentPath.includes('/admin/page-builder')) {
        const pageBuilderLink = document.querySelector('[data-controller="PageBuilder"]');
        if (pageBuilderLink) {
            pageBuilderLink.classList.add('active');
            console.log('设置页面装修为active');
        }
    } else if (currentPath.includes('/admin/sys-menu')) {
        const sysMenuLink = document.querySelector('[data-controller="SysMenu"]');
        if (sysMenuLink) {
            sysMenuLink.classList.add('active');
            console.log('设置系统菜单为active');
        }
    } else if (currentPath.includes('/admin/settings')) {
        const settingsLink = document.querySelector('[data-controller="Settings"]');
        if (settingsLink) {
            settingsLink.classList.add('active');
            console.log('设置系统设置为active');
        }
    }
});
</script>


            <!-- 主要内容 -->
            <main class="main-content">
                <!-- 内容头部 -->
                <div class="content-header">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-lightbulb"></i> 解决方案管理
                    </h1>
                </div>

                <!-- 引用统一消息组件 -->
                <!-- 消息提示组件 -->
<style>
/**
 * 消息提示样式 - 三只鱼网络科技 | 韩总 | 2024-12-19
 * 简洁统一的消息提示系统 - ThinkPHP6企业级应用
 */

/* 消息提示基础样式 - 后台深色科技风完美居中 */
.alert {
    position: fixed !important;
    top: 80px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    padding: 16px 24px !important;
    margin: 0 !important;
    border-radius: 12px !important;
    display: flex !important;
    align-items: center !important;
    gap: 14px !important;
    backdrop-filter: blur(25px) !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.6),
        0 2px 8px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    animation: slideInDown 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    z-index: 99999 !important;
    min-width: 340px !important;
    max-width: 520px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    font-family: 'Rajdhani', 'Microsoft YaHei', sans-serif !important;
    letter-spacing: 0.3px !important;
    /* 确保在所有容器中都能完美居中 */
    margin-left: auto !important;
    margin-right: auto !important;
}

/* 消息图标 - 科技发光效果 */
.alert-icon {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 28px !important;
    height: 28px !important;
    border-radius: 50% !important;
    flex-shrink: 0 !important;
    font-size: 15px !important;
    position: relative !important;
    transition: all 0.3s ease !important;
}

.alert-icon::before {
    content: '' !important;
    position: absolute !important;
    top: -2px !important;
    left: -2px !important;
    right: -2px !important;
    bottom: -2px !important;
    border-radius: 50% !important;
    background: inherit !important;
    opacity: 0.3 !important;
    animation: pulse 2s infinite !important;
}

/* 消息内容 */
.alert-content {
    flex: 1 !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* 成功提示 - 按钮式紫色渐变风格 */
.alert-success {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8) 0%,
        rgba(255, 119, 198, 0.6) 50%,
        rgba(120, 219, 255, 0.7) 100%) !important;
    border: 1px solid rgba(120, 119, 198, 0.6) !important;
    color: #ffffff !important;
    box-shadow: 0 4px 15px rgba(120, 119, 198, 0.3) !important;
}

.alert-success .alert-icon {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.9),
        rgba(240, 240, 255, 1)) !important;
    color: rgba(120, 119, 198, 0.9) !important;
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.4) !important;
}

/* 错误提示 - 后台深色科技风 */
.alert-danger {
    background: linear-gradient(135deg,
        rgba(15, 15, 15, 0.95) 0%,
        rgba(45, 25, 25, 0.95) 100%) !important;
    border: 1px solid rgba(255, 119, 119, 0.4) !important;
    color: #ffb3b3 !important;
}

.alert-danger .alert-icon {
    background: linear-gradient(135deg,
        rgba(255, 119, 119, 0.9),
        rgba(220, 80, 80, 1)) !important;
    color: #ffffff !important;
    box-shadow: 0 0 15px rgba(255, 119, 119, 0.5) !important;
}

/* 警告提示 - 后台深色科技风 */
.alert-warning {
    background: linear-gradient(135deg,
        rgba(15, 15, 15, 0.95) 0%,
        rgba(45, 35, 15, 0.95) 100%) !important;
    border: 1px solid rgba(255, 193, 7, 0.4) !important;
    color: #ffd54f !important;
}

.alert-warning .alert-icon {
    background: linear-gradient(135deg,
        rgba(255, 193, 7, 0.9),
        rgba(255, 152, 0, 1)) !important;
    color: #ffffff !important;
    box-shadow: 0 0 15px rgba(255, 193, 7, 0.5) !important;
}

/* 信息提示 - 后台深色科技风 */
.alert-info {
    background: linear-gradient(135deg,
        rgba(15, 15, 15, 0.95) 0%,
        rgba(25, 25, 45, 0.95) 100%) !important;
    border: 1px solid rgba(120, 119, 198, 0.4) !important;
    color: #b3b3ff !important;
}

.alert-info .alert-icon {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.9),
        rgba(120, 219, 255, 0.9)) !important;
    color: #ffffff !important;
    box-shadow: 0 0 15px rgba(120, 119, 198, 0.5) !important;
}

/* 动画效果 - 科技感流畅动画 */
@keyframes slideInDown {
    0% {
        transform: translateX(-50%) translateY(-100%);
        opacity: 0;
        filter: blur(4px);
    }
    50% {
        transform: translateX(-50%) translateY(-10px);
        opacity: 0.8;
        filter: blur(1px);
    }
    100% {
        transform: translateX(-50%) translateY(0);
        opacity: 1;
        filter: blur(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.15);
        opacity: 0.6;
    }
}

/* 悬停效果 - 科技感交互 */
.alert:hover {
    transform: translateX(-50%) translateY(-3px) !important;
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.7),
        0 4px 12px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

/* 响应式设计 - 移动端优化 */
@media (max-width: 768px) {
    .alert {
        min-width: 300px !important;
        max-width: 92vw !important;
        padding: 14px 18px !important;
        gap: 10px !important;
        font-size: 13px !important;
        border-radius: 10px !important;
        top: 60px !important;
        /* 移动端确保居中 */
        left: 50% !important;
        transform: translateX(-50%) !important;
    }

    .alert-icon {
        width: 24px !important;
        height: 24px !important;
        font-size: 13px !important;
    }

    .alert-icon::before {
        top: -1px !important;
        left: -1px !important;
        right: -1px !important;
        bottom: -1px !important;
    }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
    .alert {
        min-width: 280px !important;
        max-width: 95vw !important;
        padding: 12px 16px !important;
        gap: 8px !important;
        font-size: 12px !important;
        top: 50px !important;
        /* 超小屏幕确保居中 */
        left: 50% !important;
        transform: translateX(-50%) !important;
    }

    .alert-icon {
        width: 22px !important;
        height: 22px !important;
        font-size: 12px !important;
    }
}
</style> 
<?php if(isset($message) && $message): ?>
    <div class="alert alert-<?php echo $messageType=='error' ? 'danger'  :  ($messageType ?: 'info'); ?> alert-dismissible fade show" role="alert" id="alertMessage">
        <div class="alert-icon">
            <i class="fas fa-<?php echo $messageType=='success' ? 'check-circle'  :  ($messageType == 'error' || $messageType == 'danger' ? 'exclamation-circle' : ($messageType == 'warning' ? 'exclamation-triangle' : 'info-circle')); ?>"></i>
        </div>
        <div class="alert-content">
            <?php echo htmlentities((string) $message); ?>
        </div>
    </div>
<?php endif; ?>

<!-- 统一删除确认模态框 -->
<div class="delete-modal" id="deleteModal">
    <div class="modal-content">
        <div class="modal-header">
            <i class="fas fa-exclamation-triangle"></i>
            <h4>确认删除</h4>
        </div>
        <div class="modal-body">
            <p>您确定要删除以下项目吗？</p>
            <div class="delete-item-title" id="deleteItemTitle">
                <!-- 这里将显示要删除的项目标题 -->
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn-cancel" onclick="closeDeleteModal()">
                <i class="fas fa-times"></i>
                取消
            </button>
            <button type="button" class="btn-confirm-delete" id="confirmDeleteBtn">
                <i class="fas fa-trash"></i>
                确认删除
            </button>
        </div>
    </div>
</div>

<script>
// 简化的消息处理 - 主要功能已移至admin.js
function showMessage(message, type = 'info') {
    const existingAlert = document.querySelector('.alert');
    if (existingAlert) existingAlert.remove();

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.id = 'alertMessage';
    alertDiv.innerHTML = `
        <div class="alert-icon">
            <i class="fas fa-${type === 'success' ? 'check-circle' : (type === 'danger' ? 'exclamation-circle' : (type === 'warning' ? 'exclamation-triangle' : 'info-circle'))}"></i>
        </div>
        <div class="alert-content">${message}</div>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv && alertDiv.parentElement) {
            alertDiv.style.transition = 'all 0.5s ease-out';
            alertDiv.style.opacity = '0';
            setTimeout(() => alertDiv.remove(), 500);
        }
    }, 1500);
}

function closeDeleteModal() {
    const modal = document.getElementById('deleteModal');
    if (modal) modal.classList.remove('show');
}

// 页面加载时自动消失消息
document.addEventListener('DOMContentLoaded', function() {
    const alertMessage = document.getElementById('alertMessage');
    if (alertMessage) setTimeout(() => alertMessage.remove(), 1500);
});
</script>

                <!-- 页面内容区域 -->
                <div class="content-body">
                    <div class="news-container">

                    <?php if($action == 'list'): ?>
                        <!-- 解决方案列表视图 -->

                        <div class="list-header">
                            <div class="list-header-content">
                                <div class="list-title-section">
                                    <div class="list-icon">
                                        <i class="fas fa-lightbulb"></i>
                                    </div>
                                    <div>
                                        <h1 class="list-title">解决方案中心</h1>
                                        <p class="list-subtitle">管理企业解决方案信息</p>
                                    </div>
                                </div>
                                <div class="header-actions">
                                    <a href="/admin/solutions?action=add" class="btn-add-custom">
                                        <i class="fas fa-plus"></i>
                                        <span>添加解决方案</span>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="list-body">
                            <?php if($solutionsList && count($solutionsList) > 0): ?>
                                <div class="news-list">
                                    <?php if(is_array($solutionsList) || $solutionsList instanceof \think\Collection || $solutionsList instanceof \think\Paginator): $i = 0; $__LIST__ = $solutionsList;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$solution): $mod = ($i % 2 );++$i;?>
                                    <div class="news-item">
                                        <div class="news-content-wrapper">
                                            <div class="news-thumbnail">
                                                <?php if($solution['image']): ?>
                                                    <img src="<?php echo htmlentities((string) $solution['image']); ?>" alt="<?php echo htmlentities((string) $solution['name']); ?>" class="news-thumb-img"
                                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                                    <div class="news-thumb-placeholder" style="display: none;">
                                                        <i class="fas fa-lightbulb"></i>
                                                    </div>
                                                <?php else: ?>
                                                    <div class="news-thumb-placeholder">
                                                        <i class="fas fa-lightbulb"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <div class="news-info">
                                                <div class="news-header">
                                                    <h3 class="news-title"><?php echo htmlentities((string) $solution['name']); ?></h3>
                                                    <div class="news-badges">
                                                        <?php if($solution['icon']): ?>
                                                            <span class="badge badge-category">
                                                                <i class="<?php echo htmlentities((string) $solution['icon']); ?>"></i>
                                                                图标
                                                            </span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>

                                                <div class="news-meta">
                                                    <div class="meta-item">
                                                        <i class="fas fa-calendar"></i>
                                                        <span><?php echo htmlentities((string) date('Y-m-d H:i',!is_numeric($solution['created_at'])? strtotime($solution['created_at']) : $solution['created_at'])); ?></span>
                                                    </div>
                                                    <div class="meta-item">
                                                        <i class="fas fa-eye"></i>
                                                        <span><?php echo isset($solution['views']) ? htmlentities((string) $solution['views']) : 0; ?> 次浏览</span>
                                                    </div>
                                                    <div class="meta-item">
                                                        <i class="fas fa-sort-numeric-up"></i>
                                                        <span>排序: <?php echo htmlentities((string) $solution['sort_order']); ?></span>
                                                    </div>
                                                </div>

                                                <?php if($solution['short_description']): ?>
                                                    <div class="news-summary">
                                                        <?php echo htmlentities((string) mb_substr($solution['short_description'],0,150,'UTF-8')); if(mb_strlen($solution['short_description'], 'UTF-8') > 150): ?>...<?php endif; ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <div class="news-actions">
                                                <div class="status-toggle">
                                                    <label class="switch">
                                                        <input type="checkbox"
                                                               <?php echo !empty($solution['status']) ? 'checked'  :  ''; ?>
                                                               onchange="toggleStatus('<?php echo htmlentities((string) $solution['id']); ?>', this.checked ? 1 : 0)">
                                                        <span class="slider"></span>
                                                    </label>
                                                    <span class="status-label"><?php echo !empty($solution['status']) ? '已发布'  :  '草稿'; ?></span>
                                                </div>

                                                <div class="action-buttons">
                                                    <a href="/admin/solutions?action=edit&id=<?php echo htmlentities((string) $solution['id']); ?>" class="btn-action btn-edit" title="编辑">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button onclick="deleteItem('<?php echo htmlentities((string) $solution['id']); ?>', '<?php echo htmlentities((string) htmlentities((string) $solution['name'])); ?>', '/admin/solutions?action=delete&id=<?php echo htmlentities((string) $solution['id']); ?>')"
                                                            class="btn-action btn-delete" title="删除">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </div>

                                <!-- 分页组件（复用新闻管理的分页样式） -->
                                <?php if($solutionsList->hasPages()): ?>
                                    <div class="custom-pagination-container">
                                        <nav class="custom-pagination-nav">
                                            <div class="pagination-info">
                                                <span class="pagination-text">
                                                    显示第 <?php echo htmlentities((string) $solutionsList->currentPage()); ?> 页，共 <?php echo htmlentities((string) $solutionsList->lastPage()); ?> 页，总计 <?php echo htmlentities((string) $solutionsList->total()); ?> 条记录
                                                </span>
                                            </div>
                                            <div class="pagination-buttons">
                                                <?php if($solutionsList->currentPage() > 1): ?>
                                                    <a href="/admin/solutions?action=list&page=1" class="pagination-btn pagination-first">
                                                        <i class="fas fa-angle-double-left"></i>
                                                        首页
                                                    </a>
                                                    <a href="/admin/solutions?action=list&page=<?php echo htmlentities((string) $solutionsList->currentPage() - 1); ?>" class="pagination-btn pagination-prev">
                                                        <i class="fas fa-angle-left"></i>
                                                        上一页
                                                    </a>
                                                <?php else: ?>
                                                    <span class="pagination-btn pagination-first disabled">
                                                        <i class="fas fa-angle-double-left"></i>
                                                        首页
                                                    </span>
                                                    <span class="pagination-btn pagination-prev disabled">
                                                        <i class="fas fa-angle-left"></i>
                                                        上一页
                                                    </span>
                                                <?php endif; 
                                                    $currentPage = $solutionsList->currentPage();
                                                    $lastPage = $solutionsList->lastPage();

                                                    if ($lastPage <= 7) {
                                                        $startPage = 1;
                                                        $endPage = $lastPage;
                                                    } else {
                                                        $startPage = max(1, $currentPage - 2);
                                                        $endPage = min($lastPage, $currentPage + 2);
                                                    }
                                                 if($startPage > 1): ?>
                                                    <a href="/admin/solutions?action=list&page=1" class="pagination-btn pagination-number">1</a>
                                                    <?php if($startPage > 2): ?>
                                                        <span class="pagination-ellipsis">...</span>
                                                    <?php endif; ?>
                                                <?php endif; 
                                                    $pageNumbers = range($startPage, $endPage);
                                                 if(is_array($pageNumbers) || $pageNumbers instanceof \think\Collection || $pageNumbers instanceof \think\Paginator): $i = 0; $__LIST__ = $pageNumbers;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$pageNum): $mod = ($i % 2 );++$i;if($pageNum == $currentPage): ?>
                                                        <span class="pagination-btn pagination-number active"><?php echo htmlentities((string) $pageNum); ?></span>
                                                    <?php else: ?>
                                                        <a href="/admin/solutions?action=list&page=<?php echo htmlentities((string) $pageNum); ?>" class="pagination-btn pagination-number"><?php echo htmlentities((string) $pageNum); ?></a>
                                                    <?php endif; ?>
                                                <?php endforeach; endif; else: echo "" ;endif; if($endPage < $lastPage): if($endPage < $lastPage - 1): ?>
                                                        <span class="pagination-ellipsis">...</span>
                                                    <?php endif; ?>
                                                    <a href="/admin/solutions?action=list&page=<?php echo htmlentities((string) $lastPage); ?>" class="pagination-btn pagination-number"><?php echo htmlentities((string) $lastPage); ?></a>
                                                <?php endif; if($solutionsList->currentPage() < $solutionsList->lastPage()): ?>
                                                    <a href="/admin/solutions?action=list&page=<?php echo htmlentities((string) $solutionsList->currentPage() + 1); ?>" class="pagination-btn pagination-next">
                                                        下一页
                                                        <i class="fas fa-angle-right"></i>
                                                    </a>
                                                    <a href="/admin/solutions?action=list&page=<?php echo htmlentities((string) $solutionsList->lastPage()); ?>" class="pagination-btn pagination-last">
                                                        末页
                                                        <i class="fas fa-angle-double-right"></i>
                                                    </a>
                                                <?php else: ?>
                                                    <span class="pagination-btn pagination-next disabled">
                                                        下一页
                                                        <i class="fas fa-angle-right"></i>
                                                    </span>
                                                    <span class="pagination-btn pagination-last disabled">
                                                        末页
                                                        <i class="fas fa-angle-double-right"></i>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </nav>
                                    </div>
                                <?php endif; else: ?>
                                <div class="empty-state">
                                    <div class="empty-icon">
                                        <i class="fas fa-lightbulb"></i>
                                    </div>
                                    <h3 class="empty-title">暂无解决方案</h3>
                                    <p class="empty-description">还没有添加任何解决方案，点击上方按钮开始添加第一个解决方案吧！</p>
                                </div>
                            <?php endif; ?>
                        </div>

                    <?php elseif($action == 'add' || $action == 'edit'): ?>
                        <!-- 添加/编辑解决方案表单 -->
                        <div class="form-header">
                            <div class="form-header-content">
                                <div class="form-title-section">
                                    <div class="form-icon">
                                        <i class="fas fa-<?php echo $action=='add' ? 'plus'  :  'edit'; ?>"></i>
                                    </div>
                                    <div>
                                        <h1 class="form-title"><?php echo $action=='add' ? '添加解决方案'  :  '编辑解决方案'; ?></h1>
                                        <p class="form-subtitle">填写解决方案的详细信息</p>
                                    </div>
                                </div>
                                <a href="/admin/solutions" class="btn-back">
                                    <i class="fas fa-arrow-left"></i>
                                    <span>返回列表</span>
                                </a>
                            </div>
                        </div>

                        <div class="form-body">

                            <form method="POST" action="/admin/solutions" enctype="multipart/form-data" class="news-form">
                                <input type="hidden" name="action" value="<?php echo htmlentities((string) $action); ?>">
                                <?php if($editData): ?>
                                    <input type="hidden" name="id" value="<?php echo htmlentities((string) $editData['id']); ?>">
                                <?php endif; ?>

                                <div class="form-grid">
                                    <!-- 基本信息 -->
                                    <div class="form-section">
                                        <div class="section-header">
                                            <h3 class="section-title">
                                                <i class="fas fa-info-circle"></i>
                                                基本信息
                                            </h3>
                                        </div>

                                        <div class="form-row" style="margin-top: 20px;">
                                            <div class="form-group">
                                                <label for="name" class="form-label required">
                                                    <i class="fas fa-lightbulb"></i>
                                                    解决方案名称
                                                </label>
                                                <input type="text" class="form-input" id="name" name="name"
                                                       value="<?php echo htmlentities((string) (isset($editData['name']) && ($editData['name'] !== '')?$editData['name']:'')); ?>"
                                                       placeholder="请输入解决方案名称" required>
                                            </div>

                                            <div class="form-group">
                                                <label for="slug" class="form-label">
                                                    <i class="fas fa-link"></i>
                                                    URL别名
                                                </label>
                                                <input type="text" class="form-input" id="slug" name="slug"
                                                       value="<?php echo htmlentities((string) (isset($editData['slug']) && ($editData['slug'] !== '')?$editData['slug']:'')); ?>"
                                                       placeholder="自动生成或手动输入">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="short_description" class="form-label">
                                                <i class="fas fa-align-left"></i>
                                                简短描述
                                            </label>
                                            <textarea class="form-textarea" id="short_description" name="short_description" rows="3"
                                                      placeholder="请输入解决方案的简短描述"><?php echo htmlentities((string) (isset($editData['short_description']) && ($editData['short_description'] !== '')?$editData['short_description']:'')); ?></textarea>
                                        </div>

                                        <div class="form-group">
                                            <label for="features" class="form-label">
                                                <i class="fas fa-star"></i>
                                                解决方案特性
                                            </label>
                                            <textarea class="form-textarea" id="features" name="features" rows="4"
                                                      placeholder="每行一个特性，例如：&#10;高效智能分析&#10;实时数据处理&#10;可视化报表展示"><?php if($editData && $editData['features_array'] && is_array($editData['features_array'])): ?><?php echo htmlentities((string) implode($editData['features_array'],"n")); ?><?php endif; ?></textarea>
                                        </div>

                                        <div class="form-group">
                                            <label for="description" class="form-label required">
                                                <i class="fas fa-file-text"></i>
                                                详细描述
                                            </label>
                                            <!-- CKEditor 5编辑器 -->
                                            <div class="ck-editor-container">
                                                <textarea id="editor" name="description"><?php echo htmlentities((string) (isset($editData['description']) && ($editData['description'] !== '')?$editData['description']:'')); ?></textarea>
                                            </div>
                                        </div>

                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="sort_order" class="form-label">
                                                    <i class="fas fa-sort"></i>
                                                    排序权重
                                                </label>
                                                <input type="number" class="form-input" id="sort_order" name="sort_order"
                                                       value="<?php echo htmlentities((string) (isset($editData['sort_order']) && ($editData['sort_order'] !== '')?$editData['sort_order']:0)); ?>"
                                                       placeholder="0" min="0">
                                            </div>

                                            <div class="checkbox-group" style="margin-top: 40px;">
                                                <label class="checkbox-label">
                                                    <input type="checkbox" name="status" value="1"
                                                           <?php if(!$editData || $editData['status']): ?>checked<?php endif; ?>>
                                                    <span class="checkbox-custom"></span>
                                                    <span class="checkbox-text">
                                                        <i class="fas fa-eye"></i>
                                                        立即发布
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 解决方案图片和图标 -->
                                    <div class="form-section">
                                        <div class="section-header">
                                            <h3 class="section-title">
                                                <i class="fas fa-image"></i>
                                                解决方案图片和图标
                                            </h3>
                                        </div>

                                        <div class="form-row" style="margin-top: 20px;">
                                            <!-- 解决方案图片 -->
                                            <div class="form-group">
                                                <label class="form-label">
                                                    <i class="fas fa-upload"></i>
                                                    解决方案图片
                                                </label>

                                                <!-- 图片上传弹窗按钮 -->
                                                <div class="image-upload-section">
                                                    <button type="button" class="btn-upload-image" id="btnSelectSolutionImage">
                                                        <i class="fas fa-images"></i>
                                                        <span>选择图片</span>
                                                    </button>
                                                    <div class="upload-help">
                                                        <small>支持上传新图片或从图库中选择，推荐尺寸：800x600px</small>
                                                    </div>
                                                </div>

                                                <!-- 隐藏的表单字段存储选择的图片URL -->
                                                <input type="hidden" id="selectedImageUrl" name="image_url" value="<?php echo htmlentities((string) (isset($editData['image']) && ($editData['image'] !== '')?$editData['image']:'')); ?>">

                                                <!-- 当前选择的图片预览 -->
                                                <div class="selected-image-preview" id="selectedImagePreview" <?php if(!$editData || !$editData['image']): ?>style="display: none;"<?php endif; ?>>
                                                    <label class="form-label">已选择的图片：</label>
                                                    <div class="image-preview-container">
                                                        <img src="<?php echo htmlentities((string) (isset($editData['image']) && ($editData['image'] !== '')?$editData['image']:'')); ?>" alt="解决方案图片" class="preview-img" id="previewImg">
                                                        <div class="image-actions">
                                                            <button type="button" class="btn-change-image" onclick="changeSolutionImage()">
                                                                <i class="fas fa-edit"></i>
                                                                更换图片
                                                            </button>
                                                            <button type="button" class="btn-remove-image" onclick="removeSolutionImage()">
                                                                <i class="fas fa-trash"></i>
                                                                移除图片
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 解决方案图标 -->
                                            <div class="form-group">
                                                <label for="icon" class="form-label">
                                                    <i class="fas fa-star"></i>
                                                    图标选择
                                                </label>

                                                <!-- 图标选择器 -->
                                                <div class="icon-selector-container">
                                                    <div class="icon-input-group">
                                                        <input type="text" class="form-input" id="icon" name="icon"
                                                               value="<?php echo htmlentities((string) (isset($editData['icon']) && ($editData['icon'] !== '')?$editData['icon']:'')); ?>"
                                                               placeholder="例如：fas fa-lightbulb"
                                                               readonly>
                                                        <button type="button" class="btn-icon-selector" id="btnSelectIcon">
                                                            <i class="fas fa-icons"></i>
                                                            选择图标
                                                        </button>
                                                    </div>

                                                    <!-- 图标预览 -->
                                                    <div class="icon-preview" id="iconPreview">
                                                        <?php if($editData && $editData['icon']): ?>
                                                            <div class="preview-icon">
                                                                <i class="<?php echo htmlentities((string) $editData['icon']); ?>"></i>
                                                                <span>当前图标</span>
                                                            </div>
                                                        <?php else: ?>
                                                            <div class="preview-placeholder">
                                                                <i class="fas fa-question-circle"></i>
                                                                <span>未选择图标</span>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>

                                                    <div class="form-help">
                                                        选择FontAwesome图标代表此解决方案
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>

                                <!-- 表单操作按钮 -->
                                <div class="form-actions">
                                    <button type="submit" class="btn-submit">
                                        <i class="fas fa-save"></i>
                                        <span>保存解决方案</span>
                                    </button>
                                    <a href="/admin/solutions" class="btn-cancel">
                                        <i class="fas fa-times"></i>
                                        <span>取消</span>
                                    </a>
                                </div>
                            </form>
                        </div>
                    <?php endif; ?>

                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 图标选择器弹窗 -->
    <div class="icon-selector-modal" id="iconSelectorModal">
        <div class="icon-selector-content">
            <div class="icon-selector-header">
                <h3 class="icon-selector-title">
                    <i class="fas fa-icons"></i>
                    选择解决方案图标
                </h3>
                <button type="button" class="icon-selector-close" onclick="closeIconSelector()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="icon-selector-body">
                <input type="text" class="icon-search" placeholder="搜索图标..." onkeyup="filterIcons(this.value)">
                <div class="icon-grid" id="iconGrid">
                    <!-- 图标列表将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 图片选择器组件 -->


    <!-- JavaScript -->
    <!-- 后台管理系统公共JavaScript文件 -->

<!-- 核心JavaScript库 -->
<script src="/assets/js/jquery.min.js"></script>
<script src="/assets/js/bootstrap.bundle.min.js"></script>

<!-- 后台管理系统核心JS -->
<script src="/assets/js/admin.js"></script>

<!-- 核心初始化脚本 -->
<script>
/**
 * 后台管理系统核心初始化 - 三只鱼网络科技 | 韩总
 * 简化版本，专注核心功能 - ThinkPHP6企业级应用
 */

// 设置全局AJAX超时和错误处理
$.ajaxSetup({
    timeout: 30000 // 30秒超时
});

// 全局AJAX错误处理
$(document).ajaxError(function(event, xhr, settings, error) {
    let message = '操作失败';
    
    if (xhr.responseJSON && xhr.responseJSON.message) {
        message = xhr.responseJSON.message;
    } else if (xhr.status === 404) {
        message = '请求的资源不存在';
    } else if (xhr.status === 403) {
        message = '没有权限执行此操作';
    } else if (xhr.status === 500) {
        message = '服务器内部错误';
    } else if (xhr.status === 'timeout') {
        message = '请求超时，请稍后重试';
    } else if (xhr.status === 'error') {
        message = '网络错误，请检查网络连接';
    }
    
    if (typeof showMessage === 'function') {
        showMessage(message, 'error');
    }
});

// 页面加载完成后的初始化
$(document).ready(function() {
    // 初始化Bootstrap工具提示
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    // 初始化Bootstrap弹出框
    if (typeof bootstrap !== 'undefined' && bootstrap.Popover) {
        const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    }
});
</script>


    <!-- CKEditor 5 Classic版本 -->
    <script src="/assets/js/ckeditor.js"></script>
    <!-- CKEditor 5 中文语言包 -->
    <script src="/assets/js/zh-cn.js"></script>
    <!-- 图片上传弹窗组件 -->
    <script src="/assets/js/image-uploader.js"></script>
    <!-- 图片选择器扩展 -->
    <script src="/assets/js/image-selector-extension.js"></script>

    <script>
        let editor;
        let editorImageUploader; // 编辑器专用图片上传组件实例
        let solutionImageUploader; // 解决方案图片选择器实例

        $(document).ready(function() {
            // 初始化编辑器专用图片上传组件
            editorImageUploader = createImageUploader({
                uploadUrl: '/admin/image/upload?context=solutions',
                uploadField: 'upload',
                maxFiles: 999,
                maxSize: 5 * 1024 * 1024,
                allowedTypes: ['image/jpeg', 'image/png'],
                enableImageSelector: true,
                selectorUrl: '/admin/image/selector',
                isEditor: true,
                context: 'editor',
                instanceId: 'editor-uploader',
                onSelect: function(files) {
                    const validFiles = [];
                    const errors = [];

                    files.forEach(file => {
                        if (file.file_size && file.file_size > 5 * 1024 * 1024) {
                            errors.push(`${file.filename}: 文件大小超过5MB`);
                        } else if (file.file_type && !['image/jpeg', 'image/png'].includes(file.file_type)) {
                            errors.push(`${file.filename}: 不支持的文件类型`);
                        } else {
                            validFiles.push(file);
                        }
                    });

                    if (errors.length > 0) {
                        editorImageUploader.showMessage('部分文件无效：\n' + errors.join('\n'), 'warning');
                    }

                    return validFiles;
                },
                onConfirm: function(orderedData, mode) {
                    if (mode === 'select') {
                        insertSelectedImagesToEditor(orderedData);
                    } else {
                        insertOrderedFilesToEditor(orderedData);
                    }
                },
                onUpload: function(uploadedFiles) {
                    uploadedFiles.forEach((fileData, index) => {
                        setTimeout(() => {
                            insertImageToEditor(fileData.url);
                        }, index * 100);
                    });
                    editorImageUploader.close();
                },
                onError: function(error) {
                    editorImageUploader.showMessage('图片上传失败：' + error.message, 'error');
                }
            });

            // 初始化CKEditor 5编辑器
            if ($('#editor').length) {
                ClassicEditor
                    .create(document.querySelector('#editor'), {
                        language: 'zh-cn',
                        placeholder: '请输入解决方案详情...',
                        toolbar: [
                            'heading',
                            'bold',
                            'italic',
                            'underline',
                            'numberedList',
                            'bulletedList',
                            'outdent',
                            'indent',
                            'link',
                            'insertTable',
                            'blockQuote',
                            'undo',
                            'redo'
                        ],
                        heading: {
                            options: [
                                { model: 'paragraph', title: '正文', class: 'ck-heading_paragraph' },
                                { model: 'heading1', view: 'h1', title: '标题 1', class: 'ck-heading_heading1' },
                                { model: 'heading2', view: 'h2', title: '标题 2', class: 'ck-heading_heading2' },
                                { model: 'heading3', view: 'h3', title: '标题 3', class: 'ck-heading_heading3' },
                                { model: 'heading4', view: 'h4', title: '标题 4', class: 'ck-heading_heading4' }
                            ]
                        },
                        table: {
                            contentToolbar: [
                                'tableColumn',
                                'tableRow',
                                'mergeTableCells',
                                'tableCellProperties',
                                'tableProperties'
                            ]
                        },
                        link: {
                            decorators: {
                                openInNewTab: {
                                    mode: 'manual',
                                    label: '在新标签页中打开',
                                    attributes: {
                                        target: '_blank',
                                        rel: 'noopener noreferrer'
                                    }
                                }
                            }
                        }
                    })
                    .then(newEditor => {
                        editor = newEditor;
                        window.editor = newEditor;

                        // 设置编辑器高度
                        const editingView = editor.editing.view;
                        editingView.change(writer => {
                            writer.setStyle('min-height', '300px', editingView.document.getRoot());
                            writer.setStyle('max-height', '500px', editingView.document.getRoot());
                        });

                        // 设置焦点样式
                        editor.ui.focusTracker.on('change:isFocused', (evt, name, isFocused) => {
                            if (isFocused) {
                                $('.ck-editor-container').addClass('focused');
                            } else {
                                $('.ck-editor-container').removeClass('focused');
                            }
                        });

                        // 添加图片上传按钮
                        setTimeout(() => {
                            addImageUploadButton();
                        }, 1000);

                        console.log('✅ CKEditor 5 Classic 初始化成功');
                    })
                    .catch(error => {
                        console.error('❌ CKEditor 5 初始化失败:', error);
                        $('#editor').addClass('form-textarea').attr('rows', 15).show();
                        showMessage('编辑器加载失败，已切换到基础模式', 'warning');
                        window.editor = null;
                    });
            }

            // 添加图片上传按钮到编辑器工具栏 - 与新闻管理完全一致
            function addImageUploadButton() {
                // 多种方式查找工具栏
                let toolbarItems = document.querySelector('.ck-toolbar .ck-toolbar__items');
                if (!toolbarItems) {
                    toolbarItems = document.querySelector('.ck-toolbar__items');
                }
                if (!toolbarItems) {
                    toolbarItems = document.querySelector('.ck-toolbar');
                }

                if (!toolbarItems) {
                    return false;
                }

                // 检查按钮是否已存在
                if (document.querySelector('[data-upload-button="true"]')) {
                    return true;
                }

                // 创建图片上传按钮
                const imageButton = document.createElement('button');
                imageButton.className = 'ck-button ck-button_with-text';
                imageButton.type = 'button';
                imageButton.setAttribute('data-upload-button', 'true');
                imageButton.setAttribute('title', '上传图片');
                imageButton.setAttribute('aria-label', '上传图片');

                // 创建图标容器
                const iconContainer = document.createElement('span');
                iconContainer.className = 'ck-button__icon';

                // 使用FontAwesome图标
                const icon = document.createElement('i');
                icon.className = 'fas fa-images';
                icon.style.cssText = `
                    font-size: 12px !important;
                    color: rgba(255, 255, 255, 0.8) !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                    display: inline-block !important;
                `;
                iconContainer.appendChild(icon);
                imageButton.appendChild(iconContainer);

                // 添加文本标签
                const textLabel = document.createElement('span');
                textLabel.className = 'ck-button__label';
                textLabel.textContent = '图片';
                imageButton.appendChild(textLabel);

                // 绑定点击事件 - 打开编辑器专用弹窗
                imageButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🖼️ 打开编辑器图片上传弹窗');

                    if (editorImageUploader) {
                        editorImageUploader.show();
                    } else {
                        console.error('❌ 编辑器图片上传组件未初始化');
                    }
                });

                // 插入到工具栏末尾
                toolbarItems.appendChild(imageButton);
                console.log('✅ 图片按钮已添加到工具栏');
                return true;
            }

            // 插入选择的图片到编辑器
            function insertSelectedImagesToEditor(selectedImages) {
                if (!window.editor) {
                    return;
                }

                selectedImages.forEach((image, index) => {
                    setTimeout(() => {
                        try {
                            const imageHtml = `
                                <img src="${image.file_url}" alt="${image.alt_text || image.filename}"
                                     style="max-width: 100%; height: auto; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            `;

                            const selection = window.editor.model.document.selection;
                            const position = selection.getLastPosition();

                            const viewFragment = window.editor.data.processor.toView(imageHtml);
                            const modelFragment = window.editor.data.toModel(viewFragment);

                            window.editor.model.change(writer => {
                                window.editor.model.insertContent(modelFragment, position);
                            });

                            if (index === selectedImages.length - 1) {
                                editorImageUploader.showMessage(`已按顺序插入 ${selectedImages.length} 张图片`, 'success');
                            }
                        } catch (error) {
                            console.error('插入图片失败:', error);
                        }
                    }, index * 200);
                });
            }

            // 插入文件预览到编辑器
            function insertOrderedFilesToEditor(orderedFiles) {
                if (!window.editor) {
                    return;
                }

                orderedFiles.forEach((file, index) => {
                    const localUrl = URL.createObjectURL(file);
                    setTimeout(() => {
                        try {
                            const placeholderHtml = `
                                <div style="border: 2px dashed #ddd; padding: 20px; margin: 10px 0; text-align: center; border-radius: 8px; background: #f9f9f9;">
                                    <img src="${localUrl}" alt="${file.name}" style="max-width: 200px; max-height: 150px; border-radius: 4px; margin-bottom: 10px;">
                                    <p style="margin: 0; color: #666; font-size: 14px;">
                                        <strong>${file.name}</strong><br>
                                        <em>预览图片 - 需要上传后才能保存</em>
                                    </p>
                                </div>
                            `;

                            const selection = window.editor.model.document.selection;
                            const position = selection.getLastPosition();

                            const viewFragment = window.editor.data.processor.toView(placeholderHtml);
                            const modelFragment = window.editor.data.toModel(viewFragment);

                            window.editor.model.change(writer => {
                                window.editor.model.insertContent(modelFragment, position);
                            });

                            if (index === orderedFiles.length - 1) {
                                editorImageUploader.showMessage(`已按顺序插入 ${orderedFiles.length} 张图片预览`, 'success');
                            }
                        } catch (error) {
                            console.error('插入图片预览失败:', error);
                        }
                    }, index * 200);
                });
            }

            // 插入图片到编辑器
            function insertImageToEditor(imageUrl) {
                if (!window.editor) {
                    return;
                }

                try {
                    const imageHtml = `<img src="${imageUrl}" alt="上传的图片" style="max-width: 100%; height: auto; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">`;

                    const selection = window.editor.model.document.selection;
                    const position = selection.getLastPosition();

                    const viewFragment = window.editor.data.processor.toView(imageHtml);
                    const modelFragment = window.editor.data.toModel(viewFragment);

                    window.editor.model.change(writer => {
                        window.editor.model.insertContent(modelFragment, position);
                    });
                } catch (error) {
                    editorImageUploader.showMessage('图片插入失败', 'error');
                }
            }

            // 初始化图标选择器
            initIconSelector();
        });

        // 状态切换函数
        function toggleStatus(id, status) {
            fetch('/admin/solutions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `action=toggle_status&id=${id}&status=${status}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('状态更新成功', 'success');
                } else {
                    showMessage(data.message || '操作失败', 'error');
                    // 恢复开关状态
                    const checkbox = document.querySelector(`input[onchange*="${id}"]`);
                    if (checkbox) {
                        checkbox.checked = !checkbox.checked;
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage('网络错误', 'error');
                // 恢复开关状态
                const checkbox = document.querySelector(`input[onchange*="${id}"]`);
                if (checkbox) {
                    checkbox.checked = !checkbox.checked;
                }
            });
        }

        // 删除确认函数
        function deleteItem(id, name, url) {
            if (confirm(`确定要删除解决方案"${name}"吗？此操作不可恢复。`)) {
                window.location.href = url;
            }
        }

        // 初始化解决方案图片上传器
        $(document).ready(function() {
            // 初始化解决方案图片选择器
            solutionImageUploader = createImageUploader({
                uploadUrl: '/admin/image/upload?context=solutions',
                uploadField: 'upload',
                maxFiles: 1,
                maxSize: 5 * 1024 * 1024,
                allowedTypes: ['image/jpeg', 'image/png'],
                enableImageSelector: true,
                selectorUrl: '/admin/image/selector',
                isEditor: false,
                context: 'solution',
                instanceId: 'solution-image-uploader',
                onSelect: function(files) {
                    if (files.length > 1) {
                        // 使用图片上传器自己的提示
                        solutionImageUploader.showMessage('解决方案图片只能选择一张', 'warning');
                        return [files[0]];
                    }
                    return files;
                },
                onConfirm: function(selectedFiles, mode) {
                    if (selectedFiles.length > 0) {
                        const file = selectedFiles[0];
                        if (mode === 'select') {
                            updateSolutionImagePreview(file.file_url, file.filename);
                            solutionImageUploader.showMessage('图片选择成功', 'success');
                        } else {
                            // 文件上传模式 - 显示预览
                            const reader = new FileReader();
                            reader.onload = function(e) {
                                updateSolutionImagePreview(e.target.result, file.name);
                            };
                            reader.readAsDataURL(file);
                            solutionImageUploader.showMessage('图片预览已设置，提交表单时将自动上传', 'info');
                        }
                    }
                    solutionImageUploader.close();
                },
                onUpload: function(uploadedFiles) {
                    if (uploadedFiles.length > 0) {
                        const fileData = uploadedFiles[0];
                        updateSolutionImagePreview(fileData.url, fileData.filename);
                        // 使用图片上传器自己的提示
                        solutionImageUploader.showMessage('图片上传成功', 'success');
                    }
                    solutionImageUploader.close();
                },
                onError: function(error) {
                    // 使用图片上传器自己的提示
                    solutionImageUploader.showMessage('图片上传失败：' + error.message, 'error');
                }
            });

            // 绑定解决方案图片选择按钮
            $('#btnSelectSolutionImage').on('click', function() {
                if (solutionImageUploader) {
                    solutionImageUploader.show();
                }
            });
        });

        // 更新解决方案图片预览
        function updateSolutionImagePreview(imageUrl, filename) {
            const selectedImageUrl = document.getElementById('selectedImageUrl');
            const selectedImagePreview = document.getElementById('selectedImagePreview');
            const previewImg = document.getElementById('previewImg');

            if (selectedImageUrl && selectedImagePreview && previewImg) {
                selectedImageUrl.value = imageUrl;
                previewImg.src = imageUrl;
                previewImg.alt = filename || '解决方案图片';
                selectedImagePreview.style.display = 'block';
            }
        }

        // 更换解决方案图片
        function changeSolutionImage() {
            if (solutionImageUploader) {
                solutionImageUploader.show();
            }
        }

        // 移除解决方案图片 - 使用图片上传器自己的提示
        function removeSolutionImage() {
            const selectedImageUrl = document.getElementById('selectedImageUrl');
            const selectedImagePreview = document.getElementById('selectedImagePreview');

            if (selectedImageUrl && selectedImagePreview) {
                selectedImageUrl.value = '';
                selectedImagePreview.style.display = 'none';
                if (solutionImageUploader) {
                    solutionImageUploader.showMessage('图片已移除', 'success');
                } else {
                showMessage('图片已移除', 'success');
                }
            }
        }
    </script>

    <!-- 图标选择器JavaScript -->
    <script>
        // 初始化图标选择器
        function initIconSelector() {
            const btnSelectIcon = document.getElementById('btnSelectIcon');
            if (btnSelectIcon) {
                btnSelectIcon.addEventListener('click', function() {
                    openIconSelector();
                });
            }

            // 页面加载时更新图标预览
            const iconInput = document.getElementById('icon');
            if (iconInput && iconInput.value) {
                updateIconPreview(iconInput.value);
            }
        }

        // 常用图标列表
        const iconList = [
            // 解决方案相关图标
            { class: 'fas fa-lightbulb', name: '灯泡' },
            { class: 'fas fa-rocket', name: '火箭' },
            { class: 'fas fa-cogs', name: '齿轮' },
            { class: 'fas fa-chart-line', name: '图表' },
            { class: 'fas fa-shield-alt', name: '盾牌' },
            { class: 'fas fa-cloud', name: '云' },
            { class: 'fas fa-mobile-alt', name: '手机' },
            { class: 'fas fa-laptop', name: '笔记本' },
            { class: 'fas fa-database', name: '数据库' },
            { class: 'fas fa-network-wired', name: '网络' },
            { class: 'fas fa-brain', name: '大脑' },
            { class: 'fas fa-robot', name: '机器人' },
            { class: 'fas fa-microchip', name: '芯片' },
            { class: 'fas fa-wifi', name: 'WiFi' },
            { class: 'fas fa-globe', name: '地球' },
            { class: 'fas fa-server', name: '服务器' },
            { class: 'fas fa-code', name: '代码' },
            { class: 'fas fa-bug', name: '调试' },
            { class: 'fas fa-wrench', name: '扳手' },
            { class: 'fas fa-hammer', name: '锤子' },
            { class: 'fas fa-tools', name: '工具' },
            { class: 'fas fa-magic', name: '魔法' },
            { class: 'fas fa-star', name: '星星' },
            { class: 'fas fa-fire', name: '火焰' },
            { class: 'fas fa-bolt', name: '闪电' },
            { class: 'fas fa-gem', name: '宝石' },
            { class: 'fas fa-crown', name: '皇冠' },
            { class: 'fas fa-trophy', name: '奖杯' },
            { class: 'fas fa-medal', name: '奖牌' },
            { class: 'fas fa-award', name: '奖项' },
            // 业务相关图标
            { class: 'fas fa-handshake', name: '握手' },
            { class: 'fas fa-users', name: '用户' },
            { class: 'fas fa-user-tie', name: '商务' },
            { class: 'fas fa-briefcase', name: '公文包' },
            { class: 'fas fa-building', name: '建筑' },
            { class: 'fas fa-industry', name: '工业' },
            { class: 'fas fa-store', name: '商店' },
            { class: 'fas fa-shopping-cart', name: '购物车' },
            { class: 'fas fa-credit-card', name: '信用卡' },
            { class: 'fas fa-money-bill', name: '钞票' },
            { class: 'fas fa-chart-pie', name: '饼图' },
            { class: 'fas fa-chart-bar', name: '柱状图' },
            { class: 'fas fa-analytics', name: '分析' },
            { class: 'fas fa-search', name: '搜索' },
            { class: 'fas fa-eye', name: '眼睛' },
            { class: 'fas fa-heart', name: '心' },
            { class: 'fas fa-thumbs-up', name: '点赞' },
            { class: 'fas fa-check', name: '勾选' },
            { class: 'fas fa-times', name: '关闭' },
            { class: 'fas fa-plus', name: '加号' },
            { class: 'fas fa-minus', name: '减号' }
        ];

        let allIcons = [...iconList];
        let selectedIcon = '';

        // 打开图标选择器
        function openIconSelector() {
            const modal = document.getElementById('iconSelectorModal');
            modal.classList.add('show');
            generateIconGrid(allIcons);

            // 设置当前选中的图标
            const currentIcon = document.getElementById('icon').value;
            if (currentIcon) {
                selectedIcon = currentIcon;
                highlightSelectedIcon(currentIcon);
            }
        }

        // 关闭图标选择器
        function closeIconSelector() {
            const modal = document.getElementById('iconSelectorModal');
            modal.classList.remove('show');
        }

        // 生成图标网格
        function generateIconGrid(icons) {
            const grid = document.getElementById('iconGrid');
            grid.innerHTML = '';

            icons.forEach(icon => {
                const iconItem = document.createElement('div');
                iconItem.className = 'icon-item';
                iconItem.setAttribute('data-icon', icon.class);
                iconItem.innerHTML = `
                    <i class="${icon.class}"></i>
                    <span>${icon.name}</span>
                `;

                iconItem.addEventListener('click', function() {
                    selectIcon(icon.class);
                });

                grid.appendChild(iconItem);
            });
        }

        // 选择图标
        function selectIcon(iconClass) {
            selectedIcon = iconClass;

            // 更新输入框
            document.getElementById('icon').value = iconClass;

            // 更新预览
            updateIconPreview(iconClass);

            // 高亮选中的图标
            highlightSelectedIcon(iconClass);

            // 关闭选择器
            setTimeout(() => {
                closeIconSelector();
            }, 200);
        }

        // 更新图标预览
        function updateIconPreview(iconClass) {
            const preview = document.getElementById('iconPreview');
            if (iconClass) {
                preview.innerHTML = `
                    <div class="preview-icon">
                        <i class="${iconClass}"></i>
                        <span>当前图标</span>
                    </div>
                `;
            } else {
                preview.innerHTML = `
                    <div class="preview-placeholder">
                        <i class="fas fa-image"></i>
                        <span>点击下方按钮选择图标</span>
                    </div>
                `;
            }
        }

        // 高亮选中的图标
        function highlightSelectedIcon(iconClass) {
            // 移除所有选中状态
            document.querySelectorAll('.icon-item').forEach(item => {
                item.classList.remove('selected');
            });

            // 添加选中状态
            const selectedItem = document.querySelector(`[data-icon="${iconClass}"]`);
            if (selectedItem) {
                selectedItem.classList.add('selected');
            }
        }

        // 过滤图标
        function filterIcons(searchTerm) {
            const filteredIcons = allIcons.filter(icon =>
                icon.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                icon.class.toLowerCase().includes(searchTerm.toLowerCase())
            );
            generateIconGrid(filteredIcons);

            // 如果有选中的图标，重新高亮
            if (selectedIcon) {
                highlightSelectedIcon(selectedIcon);
            }
        }

        // 点击模态框外部关闭
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('iconSelectorModal');
            if (e.target === modal) {
                closeIconSelector();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeIconSelector();
            }
        });

        // showMessage函数已移至message.html统一管理
    </script>
</body>
</html>

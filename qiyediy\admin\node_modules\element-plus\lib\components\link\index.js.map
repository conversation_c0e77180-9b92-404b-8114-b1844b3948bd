{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/link/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Link from './src/link.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElLink: SFCWithInstall<typeof Link> = withInstall(Link)\nexport default ElLink\n\nexport * from './src/link'\n"], "names": ["withInstall", "Link"], "mappings": ";;;;;;;;AAEY,MAAC,MAAM,GAAGA,mBAAW,CAACC,iBAAI;;;;;;;"}
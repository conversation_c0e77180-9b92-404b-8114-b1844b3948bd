/**
 * 组件管理器
 * 统一管理所有组件的注册、属性生成和显示更新
 */

// 组件注册表
const componentRegistry = {
    background: backgroundComponent,
    navbar: navbarComponent,
    hero: heroComponent,
    section: sectionComponent,
    card: cardComponent,
    footer: footerComponent,
    textblock: textblockComponent,
    stats: statsComponent,
    team: teamComponent,
    testimonials: testimonialsComponent,
    contact: contactComponent
};

// 属性生成器注册表
const propertyGenerators = {
    background: generateBackgroundProperties,
    navbar: generateNavbarProperties,
    hero: generateHeroProperties,
    section: generateSectionProperties,
    card: generateCardProperties,
    footer: generateFooterProperties,
    textblock: generateTextblockProperties,
    stats: generateStatsProperties,
    team: generateTeamProperties,
    testimonials: generateTestimonialsProperties,
    contact: generateContactProperties
};

// 显示更新器注册表 - 动态获取函数
const displayUpdaters = {};

/**
 * 获取组件模板
 * @param {string} type - 组件类型
 * @returns {object} 组件模板对象
 */
function getComponentTemplate(type) {
    return componentRegistry[type] || null;
}

/**
 * 生成组件属性面板HTML
 * @param {string} type - 组件类型
 * @param {HTMLElement} component - 组件DOM元素
 * @returns {string} 属性面板HTML
 */
function generateComponentProperties(type, component) {
    const generator = propertyGenerators[type];
    if (!generator) {
        console.warn(`未找到组件类型 ${type} 的属性生成器`);
        return '<p style="color: #e53e3e;">未知组件类型</p>';
    }
    
    try {
        return generator(component);
    } catch (error) {
        console.error(`生成组件 ${type} 属性时出错:`, error);
        return '<p style="color: #e53e3e;">属性生成失败</p>';
    }
}

/**
 * 获取组件显示更新器
 * @param {string} type - 组件类型
 * @returns {function|null} 显示更新器函数
 */
function getComponentDisplayUpdater(type) {
    // 首先检查注册表中是否有
    let updater = displayUpdaters[type];

    // 如果注册表中没有，尝试从全局作用域获取
    if (!updater) {
        const functionName = `update${type.charAt(0).toUpperCase() + type.slice(1)}Display`;
        updater = window[functionName];

        if (updater && typeof updater === 'function') {
            // 缓存到注册表中
            displayUpdaters[type] = updater;
        }
    }

    if (!updater) {
        console.warn(`⚠️ 显示更新器未找到: ${type}`);
        console.log('📋 已注册的显示更新器:', Object.keys(displayUpdaters));
        console.log('🔍 尝试查找的函数名:', `update${type.charAt(0).toUpperCase() + type.slice(1)}Display`);
    }

    return updater || null;
}

/**
 * 更新组件显示
 * @param {string} type - 组件类型
 * @param {HTMLElement} component - 组件DOM元素
 * @param {object} properties - 组件属性
 */
function updateComponentDisplay(type, component, properties) {
    const updater = displayUpdaters[type];
    if (!updater) {
        console.warn(`未找到组件类型 ${type} 的显示更新器`);
        return;
    }

    try {
        updater(component, properties);
    } catch (error) {
        console.error(`更新组件 ${type} 显示时出错:`, error);
    }
}

/**
 * 注册新组件
 * @param {string} type - 组件类型
 * @param {object} template - 组件模板
 * @param {function} propertyGenerator - 属性生成器函数
 * @param {function} displayUpdater - 显示更新器函数
 */
function registerComponent(type, template, propertyGenerator, displayUpdater) {
    componentRegistry[type] = template;
    propertyGenerators[type] = propertyGenerator;
    displayUpdaters[type] = displayUpdater;
    
    console.log(`组件 ${type} 注册成功`);
}

/**
 * 获取所有已注册的组件类型
 * @returns {string[]} 组件类型数组
 */
function getRegisteredComponentTypes() {
    return Object.keys(componentRegistry);
}

/**
 * 检查组件类型是否已注册
 * @param {string} type - 组件类型
 * @returns {boolean} 是否已注册
 */
function isComponentRegistered(type) {
    return type in componentRegistry;
}

/**
 * 获取组件的默认属性
 * @param {string} type - 组件类型
 * @returns {object} 默认属性对象
 */
function getComponentDefaultProperties(type) {
    const template = getComponentTemplate(type);
    return template ? { ...template.properties } : {};
}

/**
 * 验证组件属性
 * @param {string} type - 组件类型
 * @param {object} properties - 要验证的属性
 * @returns {boolean} 属性是否有效
 */
function validateComponentProperties(type, properties) {
    const template = getComponentTemplate(type);
    if (!template) return false;
    
    const defaultProps = template.properties;
    
    // 检查必要属性是否存在
    for (const key in defaultProps) {
        if (!(key in properties)) {
            console.warn(`组件 ${type} 缺少必要属性: ${key}`);
            return false;
        }
    }
    
    return true;
}

/**
 * 克隆组件属性（深拷贝）
 * @param {object} properties - 要克隆的属性
 * @returns {object} 克隆后的属性
 */
function cloneComponentProperties(properties) {
    return JSON.parse(JSON.stringify(properties));
}

/**
 * 合并组件属性
 * @param {object} defaultProps - 默认属性
 * @param {object} customProps - 自定义属性
 * @returns {object} 合并后的属性
 */
function mergeComponentProperties(defaultProps, customProps) {
    return { ...defaultProps, ...customProps };
}

// 导出组件管理器API
window.ComponentManager = {
    getTemplate: getComponentTemplate,
    generateProperties: generateComponentProperties,
    updateDisplay: updateComponentDisplay,
    getDisplayUpdater: getComponentDisplayUpdater,
    register: registerComponent,
    getRegisteredTypes: getRegisteredComponentTypes,
    isRegistered: isComponentRegistered,
    getDefaultProperties: getComponentDefaultProperties,
    validateProperties: validateComponentProperties,
    cloneProperties: cloneComponentProperties,
    mergeProperties: mergeComponentProperties
};

// 初始化日志
console.log('🎨 组件管理器已初始化');
console.log('📦 已注册组件:', getRegisteredComponentTypes());
console.log('🔧 getDisplayUpdater 函数已导出:', typeof window.ComponentManager.getDisplayUpdater);

/**
 * 图片选择器扩展功能
 * 扩展ImageUploader类，添加从服务器选择图片的功能
 * 作者: AI龙头韩哥
 * 版本: 2.0.0 - 增强版
 */

// 扩展ImageUploader类
if (typeof ImageUploader !== 'undefined') {
    
    // 添加图片选择器相关方法
    Object.assign(ImageUploader.prototype, {
        
        // 初始化图片选择器
        initImageSelector() {
            if (!this.options.enableImageSelector) {
                return;
            }
            
            // 检查模态框是否存在
            if (!this.modal) {
                console.error('❌ 模态框不存在，无法初始化图片选择器');
                return;
            }
            
            try {
                // 绑定模式切换事件
                this.bindModeSwitchEvents();
            } catch (error) {
                console.error('❌ 绑定模式切换事件失败:', error);
            }
            
            try {
                // 加载分组列表
                this.loadGroups();
            } catch (error) {
                console.error('❌ 加载分组列表失败:', error);
            }
            
            try {
                // 绑定选择器事件
                this.bindSelectorEvents();
            } catch (error) {
                console.error('❌ 绑定选择器事件失败:', error);
            }
        },
        
        // 绑定模式切换事件
        bindModeSwitchEvents() {
            if (!this.modal) {
                console.error('❌ 模态框不存在，无法绑定模式切换事件');
                return;
            }
            
            const modeTabs = this.modal.querySelectorAll('.mode-tab');
            
            if (modeTabs.length === 0) {
                return;
            }
            
            modeTabs.forEach(tab => {
                tab.addEventListener('click', (e) => {
                    const mode = e.currentTarget.getAttribute('data-mode');
                    this.switchMode(mode);
                });
            });
        },
        
        // 切换模式
        switchMode(mode) {
            this.mode = mode;
            
            // 更新选项卡状态
            const modeTabs = this.modal.querySelectorAll('.mode-tab');
            modeTabs.forEach(tab => {
                tab.classList.toggle('active', tab.getAttribute('data-mode') === mode);
            });
            
            // 显示/隐藏对应区域（在当前实例的模态框内查找）
            const uploadZone = this.modal.querySelector(`#uploadZone_${this.modalId}`);
            const selectorArea = this.modal.querySelector(`#imageSelectorArea_${this.modalId}`);
            const previewArea = this.modal.querySelector(`#previewArea_${this.modalId}`);
            
            if (mode === 'upload') {
                if (uploadZone) uploadZone.style.display = this.selectedFiles.length > 0 ? 'none' : 'flex';
                if (selectorArea) selectorArea.style.display = 'none';
                if (previewArea) previewArea.style.display = this.selectedFiles.length > 0 ? 'block' : 'none';
                
                // 清空图片选择状态
                this.selectedImages = [];
                this.updateSelectorUI();
            } else if (mode === 'select') {
                if (uploadZone) uploadZone.style.display = 'none';
                if (selectorArea) selectorArea.style.display = 'block';
                if (previewArea) previewArea.style.display = 'none';
                
                // 清空文件选择状态
                this.selectedFiles = [];
                
                // 加载图片列表（会自动更新警告提示）
                this.loadImages();
            }
            
            this.updateUI();
            this.updateFooterButtons();
        },
        
        // 更新选择限制警告提示
        updateSelectionLimitWarning() {
            const selectorArea = this.modal.querySelector('.image-selector-area');
            if (!selectorArea) return;
            
            // 移除现有的警告提示
            const existingWarning = selectorArea.querySelector('.selection-limit-warning');
            if (existingWarning) {
                existingWarning.remove();
            }
            
            // 获取当前实例的配置信息
            const instanceId = this.options.instanceId || 'unknown';
            const context = this.options.context || 'unknown';
            const maxFiles = this.options.maxFiles || 10;
            const isEditor = this.options.isEditor || false;
            
            // 检查是否在编辑器环境中
            const isInEditor = context === 'editor' ||
                              instanceId === 'editor-uploader' ||
                              isEditor ||
                              maxFiles >= 999;
            
            // 检查是否在新闻图片环境中
            const isNewsImage = context === 'news' ||
                               instanceId === 'news-uploader';
            
            // 只有在非编辑器环境且设置了明确的数量限制时才显示警告
            if (!isInEditor && maxFiles && maxFiles <= 10) {
                const contextName = isNewsImage ? '新闻' : '';
                const warningDiv = document.createElement('div');
                warningDiv.className = 'selection-limit-warning';
                warningDiv.innerHTML = `
                    <div class="warning-content">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>最多只能选择 ${maxFiles} 张${contextName}图片</span>
                    </div>
                `;
                
                // 插入到图片网格的下面，分页的上面
                const selectorGrid = selectorArea.querySelector('.selector-grid');
                const selectorPagination = selectorArea.querySelector('.selector-pagination');
                
                if (selectorGrid && selectorPagination) {
                    selectorArea.insertBefore(warningDiv, selectorPagination);
                } else if (selectorGrid) {
                    selectorGrid.parentNode.insertBefore(warningDiv, selectorGrid.nextSibling);
                }
            }
        },

        // 高亮显示选择限制警告
        highlightSelectionWarning() {
            const warningElement = this.modal.querySelector('.selection-limit-warning');
            if (warningElement) {
                // 添加高亮效果
                warningElement.style.animation = 'warningPulse 0.6s ease-in-out';
                
                // 动画结束后移除
                setTimeout(() => {
                    if (warningElement) {
                        warningElement.style.animation = '';
                    }
                }, 600);
            }
        },

        // 绑定选择器事件
        bindSelectorEvents() {
            const searchBtn = this.modal.querySelector(`#btnSearch_${this.modalId}`);
            const searchInput = this.modal.querySelector(`#selectorSearch_${this.modalId}`);
            const groupFilter = this.modal.querySelector(`#selectorGroupFilter_${this.modalId}`);
            const btnBatchDelete = this.modal.querySelector(`#btnBatchDelete_${this.modalId}`);
            const btnMoveToGroup = this.modal.querySelector(`#btnMoveToGroup_${this.modalId}`);
            const btnSelectAll = this.modal.querySelector(`#btnSelectAll_${this.modalId}`);
            const btnClearSelection = this.modal.querySelector(`#btnClearSelection_${this.modalId}`);
            
            // 搜索按钮
            if (searchBtn) {
                searchBtn.addEventListener('click', () => this.searchImages());
            }
            
            // 搜索输入框回车
            if (searchInput) {
                searchInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.searchImages();
                    }
                });
            }
            
            // 分组筛选
            if (groupFilter) {
                groupFilter.addEventListener('change', () => {
                    // 清空当前选择，避免跨分组选择问题
                    this.selectedImages = [];
                    this.updateImageOrderDisplay();
                    this.updateSelectorUI();
                    this.updateFooterButtons();
                    // 重新加载图片
                    this.searchImages();
                });
            }
            
            // 批量操作按钮
            if (btnBatchDelete) {
                btnBatchDelete.addEventListener('click', () => this.batchDeleteImages());
            }
            
            if (btnMoveToGroup) {
                btnMoveToGroup.addEventListener('click', () => this.showMoveToGroupDialog());
            }
            
            if (btnSelectAll) {
                btnSelectAll.addEventListener('click', () => this.selectAllImages());
            }
            
            if (btnClearSelection) {
                btnClearSelection.addEventListener('click', () => this.clearImageSelection());
            }
        },
        
        // 加载分组列表
        async loadGroups() {
            try {
                const response = await fetch('/admin/image/groups');
                const result = await response.json();
                
                if (result.success) {
                    this.renderGroupOptions(result.data);
                    this.renderMoveGroupOptions(result.data);
                }
            } catch (error) {
                console.error('❌ 加载分组失败:', error);
            }
        },
        
        // 渲染分组选项
        renderGroupOptions(groups) {
            const groupFilter = this.modal.querySelector(`#selectorGroupFilter_${this.modalId}`);
            
            // 清空现有选项（保留"所有分组"）
            while (groupFilter.children.length > 1) {
                groupFilter.removeChild(groupFilter.lastChild);
            }
            
            // 添加分组选项
            groups.forEach(group => {
                const option = document.createElement('option');
                option.value = group.id;
                option.textContent = `${group.name} (${group.image_count || 0})`;
                groupFilter.appendChild(option);
            });
        },
        
        // 渲染移动分组选项
        renderMoveGroupOptions(groups) {
            const moveGroupSelect = document.getElementById('moveToGroupSelect');
            if (!moveGroupSelect) return;
            
            // 清空现有选项
            moveGroupSelect.innerHTML = '';
            
            // 添加分组选项
            groups.forEach(group => {
                const option = document.createElement('option');
                option.value = group.id;
                option.textContent = group.name;
                moveGroupSelect.appendChild(option);
            });
        },
        
        // 加载图片列表
        async loadImages(page = 1) {
            try {
                // 确保页码是数字类型
                page = parseInt(page) || 1;
                
                // 保存当前页码
                this.currentPage = page;
                
                const groupId = this.modal.querySelector(`#selectorGroupFilter_${this.modalId}`).value;
                const keyword = this.modal.querySelector(`#selectorSearch_${this.modalId}`).value;
                
                const params = new URLSearchParams({
                    page: page,
                    limit: 10,
                    group_id: groupId,
                    keyword: keyword
                });
                
                const response = await fetch(`/admin/image/selector?${params}`);
                const result = await response.json();
                
                if (result.success) {
                    this.renderImageGrid(result.data.list);
                    this.renderPagination(result.data);
                    // 在图片加载完成后更新警告提示
                    this.updateSelectionLimitWarning();
                } else {
                    this.showMessage(result.message || '加载图片失败', 'error');
                }
            } catch (error) {
                console.error('❌ 加载图片失败:', error);
                this.showMessage('加载图片失败', 'error');
            }
        },
        
        // 渲染图片网格
        renderImageGrid(images) {
            const grid = this.modal.querySelector(`#selectorGrid_${this.modalId}`);
            grid.innerHTML = '';
            
            if (images.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state" style="grid-column: 1 / -1; text-align: center; padding: 40px;">
                        <div class="empty-icon">
                            <i class="fas fa-images" style="font-size: 48px; color: rgba(255, 255, 255, 0.3);"></i>
                        </div>
                        <h3 style="color: rgba(255, 255, 255, 0.7); margin: 20px 0 10px;">暂无图片</h3>
                        <p style="color: rgba(255, 255, 255, 0.5);">请尝试其他搜索条件或上传新图片</p>
                    </div>
                `;
                return;
            }
            
            images.forEach(image => {
                const item = this.createSelectorItem(image);
                grid.appendChild(item);
            });
        },
        
        // 创建选择器图片项
        createSelectorItem(image) {
            const item = document.createElement('div');
            item.className = 'selector-item';
            item.setAttribute('data-image-id', image.id);
            
            // 检查是否已选择
            const selectedIndex = this.selectedImages.findIndex(img => img.id === image.id);
            if (selectedIndex !== -1) {
                item.classList.add('selected');
            }
            
            item.innerHTML = `
                <div class="selector-item-image">
                    <img src="${image.file_url}" alt="${image.filename}" loading="lazy">
                    <div class="selector-item-overlay">
                        <div class="selector-item-check">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="selector-item-actions">
                            <button class="item-action-btn delete-btn" title="删除图片" data-action="delete">
                                <i class="fas fa-trash"></i>
                            </button>
                            <button class="item-action-btn move-btn" title="移动到分组" data-action="move">
                                <i class="fas fa-folder"></i>
                            </button>
                        </div>
                    </div>
                    <div class="selector-item-order">${selectedIndex !== -1 ? selectedIndex + 1 : ''}</div>
                </div>
                <div class="selector-item-info">
                    <div class="selector-item-name" title="${image.filename}">${image.filename}</div>
                    <div class="selector-item-meta">
                        <span>${image.file_size_text}</span>
                        <span>${image.dimensions_text}</span>
                        <span class="group-name">${image.group_name}</span>
                    </div>
                </div>
            `;
            
            // 绑定点击事件
            item.addEventListener('click', (e) => {
                // 如果点击的是操作按钮，不触发选择
                if (e.target.closest('.item-action-btn')) {
                    return;
                }
                this.toggleImageSelection(image, item);
            });
            
            // 绑定操作按钮事件
            const deleteBtn = item.querySelector('[data-action="delete"]');
            const moveBtn = item.querySelector('[data-action="move"]');
            
            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.deleteImage(image.id, item);
            });
            
            moveBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.showMoveImageDialog(image);
            });
            
            return item;
        },
        
        // 切换图片选择状态
        toggleImageSelection(image, item) {
            const selectedIndex = this.selectedImages.findIndex(img => img.id === image.id);
            
            // 获取当前实例的上下文信息
            const instanceId = this.options.instanceId || 'unknown';
            const context = this.options.context || 'unknown';
            const maxFiles = this.options.maxFiles || 10;
            
            if (selectedIndex !== -1) {
                // 取消选择
                this.selectedImages.splice(selectedIndex, 1);
                item.classList.remove('selected');
            } else {
                // 检查是否在编辑器环境中
                const isInEditor = context === 'editor' ||
                                  instanceId === 'editor-uploader' ||
                                  this.options.isEditor ||
                                  maxFiles >= 999; // 大于等于999视为不限制
                
                // 检查是否在新闻图片环境中
                const isNewsImage = context === 'news' ||
                                   instanceId === 'news-uploader';
                
                // 只有在非编辑器环境中才检查数量限制
                if (!isInEditor && this.selectedImages.length >= maxFiles) {
                    const contextName = isNewsImage ? '新闻图片' : '图片';
                    const message = `${contextName}最多只能选择 ${maxFiles} 张`;
                    // 移除showMessage调用，避免双重提示，改为console输出
                    console.log(`⚠️ ${message}`);
                    // 高亮显示警告提示
                    this.highlightSelectionWarning();
                    return;
                }
                
                // 添加选择
                this.selectedImages.push(image);
                item.classList.add('selected');
            }
            
            // 更新所有图片项的顺序显示
            this.updateImageOrderDisplay();
            this.updateSelectorUI();
            this.updateFooterButtons();
        },
        
        // 更新图片顺序显示
        updateImageOrderDisplay() {
            const items = document.querySelectorAll('.selector-item');
            
            items.forEach(item => {
                const imageId = parseInt(item.getAttribute('data-image-id'));
                const selectedIndex = this.selectedImages.findIndex(img => img.id === imageId);
                const orderElement = item.querySelector('.selector-item-order');
                
                if (orderElement) {
                    if (selectedIndex !== -1) {
                        orderElement.textContent = selectedIndex + 1;
                        orderElement.style.opacity = '1';
                        orderElement.style.transform = 'scale(1)';
                    } else {
                        orderElement.textContent = '';
                        orderElement.style.opacity = '0';
                        orderElement.style.transform = 'scale(0)';
                    }
                }
            });
        },
        
        // 更新选择器UI
        updateSelectorUI() {
            const selectedCount = this.modal.querySelector(`#selectorSelectedCount_${this.modalId}`);
            if (selectedCount) {
                selectedCount.textContent = this.selectedImages.length;
            }
        },
        
        // 更新底部按钮状态
        updateFooterButtons() {
            const btnConfirm = this.modal.querySelector(`#btnConfirm_${this.modalId}`);
            const btnUpload = this.modal.querySelector(`#btnUpload_${this.modalId}`);
            const selectedCount = this.modal.querySelector(`#selectedCount_${this.modalId}`);
            
            if (this.mode === 'select') {
                // 图片选择模式
                const hasSelection = this.selectedImages.length > 0;
                btnConfirm.disabled = !hasSelection;
                btnConfirm.className = hasSelection ? 'btn-confirm active' : 'btn-confirm';
                btnConfirm.style.display = 'inline-flex'; // 显示确认选择按钮
                btnUpload.style.display = 'none'; // 隐藏上传按钮
                selectedCount.textContent = this.selectedImages.length;
            } else {
                // 文件上传模式
                const hasFiles = this.selectedFiles.length > 0;
                btnConfirm.style.display = 'none'; // 隐藏确认选择按钮
                btnUpload.disabled = !hasFiles;
                btnUpload.style.display = hasFiles ? 'inline-flex' : 'none';
                selectedCount.textContent = this.selectedFiles.length;
            }
        },
        
        // 显示自定义确认对话框
        showConfirmDialog(title, message, onConfirm, isDanger = false) {
            const dialog = document.createElement('div');
            dialog.className = 'confirm-dialog-overlay';
            dialog.innerHTML = `
                <div class="confirm-dialog">
                    <div class="confirm-dialog-header">
                        <h3>${title}</h3>
                        <button class="confirm-dialog-close">&times;</button>
                    </div>
                    <div class="confirm-dialog-content">
                        <p>${message}</p>
                    </div>
                    <div class="confirm-dialog-footer">
                        <button class="btn-cancel">取消</button>
                        <button class="btn-confirm ${isDanger ? 'danger' : ''}">确认</button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(dialog);
            
            // 绑定事件
            const closeDialog = () => {
                document.body.removeChild(dialog);
            };
            
            dialog.querySelector('.confirm-dialog-close').addEventListener('click', closeDialog);
            dialog.querySelector('.btn-cancel').addEventListener('click', closeDialog);
            
            dialog.querySelector('.btn-confirm').addEventListener('click', () => {
                onConfirm();
                closeDialog();
            });
            
            // 点击遮罩关闭
            dialog.addEventListener('click', (e) => {
                if (e.target === dialog) {
                    closeDialog();
                }
            });
        },
        
        // 删除图片
        async deleteImage(imageId, itemElement) {
            this.showConfirmDialog(
                '删除图片',
                '确定要删除这张图片吗？删除后无法恢复！',
                async () => {
                    try {
                        const response = await fetch(`/admin/image/delete`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ id: imageId })
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            // 从选择列表中移除
                            const selectedIndex = this.selectedImages.findIndex(img => img.id === imageId);
                            if (selectedIndex !== -1) {
                                this.selectedImages.splice(selectedIndex, 1);
                            }
                            
                            // 移除DOM元素
                            itemElement.remove();
                            
                            // 更新UI
                            this.updateImageOrderDisplay();
                            this.updateSelectorUI();
                            this.updateFooterButtons();
                            
                            this.showMessage('图片删除成功', 'success');
                        } else {
                            this.showMessage(result.message || '删除失败', 'error');
                        }
                    } catch (error) {
                        console.error('❌ 删除图片失败:', error);
                        this.showMessage('删除失败', 'error');
                    }
                },
                true
            );
        },
        
        // 显示移动图片对话框
        async showMoveImageDialog(image) {
            const dialog = document.createElement('div');
            dialog.className = 'move-dialog-overlay';
            dialog.innerHTML = `
                <div class="move-dialog">
                    <div class="move-dialog-header">
                        <h3>移动图片到分组</h3>
                        <button class="move-dialog-close">&times;</button>
                    </div>
                    <div class="move-dialog-content">
                        <p>将图片 "<strong>${image.filename}</strong>" 移动到：</p>
                        <select class="move-group-select" id="moveGroupSelect">
                            <option value="">请选择分组...</option>
                        </select>
                    </div>
                    <div class="move-dialog-footer">
                        <button class="btn-cancel">取消</button>
                        <button class="btn-confirm">确认移动</button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(dialog);
            
            // 重新加载分组列表并渲染选项
            try {
                const response = await fetch('/admin/image/groups');
                const result = await response.json();
                
                if (result.success) {
                    const moveGroupSelect = dialog.querySelector('#moveGroupSelect');
                    
                    // 清空现有选项（保留默认选项）
                    while (moveGroupSelect.children.length > 1) {
                        moveGroupSelect.removeChild(moveGroupSelect.lastChild);
                    }
                    
                    // 添加分组选项
                    result.data.forEach(group => {
                        const option = document.createElement('option');
                        option.value = group.id;
                        option.textContent = group.name;
                        moveGroupSelect.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('❌ 加载分组失败:', error);
                this.showMessage('加载分组失败', 'error');
            }
            
            // 绑定事件
            dialog.querySelector('.move-dialog-close').addEventListener('click', () => {
                document.body.removeChild(dialog);
            });
            
            dialog.querySelector('.btn-cancel').addEventListener('click', () => {
                document.body.removeChild(dialog);
            });
            
            dialog.querySelector('.btn-confirm').addEventListener('click', async () => {
                const groupId = dialog.querySelector('#moveGroupSelect').value;
                if (groupId) {
                    await this.moveImageToGroup(image.id, groupId);
                    document.body.removeChild(dialog);
                    this.loadImages(); // 重新加载图片列表
                } else {
                    this.showMessage('请选择目标分组', 'warning');
                }
            });
        },
        
        // 移动图片到分组
        async moveImageToGroup(imageId, groupId) {
            try {
                const response = await fetch(`/admin/image/move-to-group`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        image_id: imageId, 
                        group_id: groupId 
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    this.showMessage('图片移动成功', 'success');
                } else {
                    this.showMessage(result.message || '移动失败', 'error');
                }
            } catch (error) {
                console.error('❌ 移动图片失败:', error);
                this.showMessage('移动失败', 'error');
            }
        },
        
        // 批量删除图片
        async batchDeleteImages() {
            if (this.selectedImages.length === 0) {
                this.showMessage('请先选择要删除的图片', 'warning');
                return;
            }
            
            this.showConfirmDialog(
                '批量删除图片',
                `确定要删除选中的 ${this.selectedImages.length} 张图片吗？删除后无法恢复！`,
                async () => {
                    try {
                        const imageIds = this.selectedImages.map(img => img.id);
                        const response = await fetch(`/admin/image/delete-multiple`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ ids: imageIds })
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            this.selectedImages = [];
                            this.loadImages(); // 重新加载图片列表
                            this.showMessage('批量删除成功', 'success');
                        } else {
                            this.showMessage(result.message || '批量删除失败', 'error');
                        }
                    } catch (error) {
                        console.error('❌ 批量删除失败:', error);
                        this.showMessage('批量删除失败', 'error');
                    }
                },
                true
            );
        },
        
        // 全选图片
        selectAllImages() {
            const items = document.querySelectorAll('.selector-item');
            const maxSelect = Math.min(items.length, this.options.maxFiles);
            
            this.selectedImages = [];
            
            for (let i = 0; i < maxSelect; i++) {
                const item = items[i];
                const imageId = parseInt(item.getAttribute('data-image-id'));
                
                // 从DOM中获取图片信息
                const img = item.querySelector('img');
                const filename = item.querySelector('.selector-item-name').textContent;
                const fileUrl = img.src;
                
                this.selectedImages.push({
                    id: imageId,
                    filename: filename,
                    file_url: fileUrl
                });
                
                item.classList.add('selected');
            }
            
            this.updateImageOrderDisplay();
            this.updateSelectorUI();
            this.updateFooterButtons();
            
            // 移除showMessage调用，避免双重提示
            console.log(`✅ 已选择 ${this.selectedImages.length} 张图片`);
        },
        
        // 清空选择
        clearImageSelection() {
            this.selectedImages = [];
            
            const items = document.querySelectorAll('.selector-item');
            items.forEach(item => {
                item.classList.remove('selected');
            });
            
            this.updateImageOrderDisplay();
            this.updateSelectorUI();
            this.updateFooterButtons();
            
            // 移除showMessage调用，避免双重提示
            console.log('ℹ️ 已清空选择');
        },
        
        // 渲染分页
        renderPagination(data) {
            const pagination = this.modal.querySelector(`#selectorPagination_${this.modalId}`);
            
            if (data.pages <= 1) {
                pagination.innerHTML = '';
                return;
            }
            
            // 清空现有内容
            pagination.innerHTML = '';
            
            // 确保页码是数字类型
            const currentPage = parseInt(data.page) || 1;
            const totalPages = parseInt(data.pages) || 1;
            
            // 上一页
            if (currentPage > 1) {
                const prevBtn = document.createElement('button');
                prevBtn.className = 'pagination-btn';
                prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
                prevBtn.title = '上一页';
                prevBtn.addEventListener('click', () => {
                    const prevPage = currentPage - 1;
                    this.loadImages(prevPage);
                });
                pagination.appendChild(prevBtn);
            }
            
            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);
            
            if (startPage > 1) {
                const firstBtn = document.createElement('button');
                firstBtn.className = 'pagination-btn';
                firstBtn.textContent = '1';
                firstBtn.addEventListener('click', () => this.loadImages(1));
                pagination.appendChild(firstBtn);
                
                if (startPage > 2) {
                    const ellipsis = document.createElement('span');
                    ellipsis.className = 'pagination-info';
                    ellipsis.textContent = '...';
                    pagination.appendChild(ellipsis);
                }
            }
            
            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `pagination-btn ${i === currentPage ? 'active' : ''}`;
                pageBtn.textContent = i;
                pageBtn.addEventListener('click', () => this.loadImages(i));
                pagination.appendChild(pageBtn);
            }
            
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    const ellipsis = document.createElement('span');
                    ellipsis.className = 'pagination-info';
                    ellipsis.textContent = '...';
                    pagination.appendChild(ellipsis);
                }
                
                const lastBtn = document.createElement('button');
                lastBtn.className = 'pagination-btn';
                lastBtn.textContent = totalPages;
                lastBtn.addEventListener('click', () => this.loadImages(totalPages));
                pagination.appendChild(lastBtn);
            }
            
            // 下一页
            if (currentPage < totalPages) {
                const nextBtn = document.createElement('button');
                nextBtn.className = 'pagination-btn';
                nextBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
                nextBtn.title = '下一页';
                nextBtn.addEventListener('click', () => {
                    const nextPage = currentPage + 1;
                    this.loadImages(nextPage);
                });
                pagination.appendChild(nextBtn);
            }
            
            // 页面信息
            const pageInfo = document.createElement('span');
            pageInfo.className = 'pagination-info';
            pageInfo.textContent = `共 ${data.total} 张图片`;
            pagination.appendChild(pageInfo);
        },
        
        // 搜索图片
        searchImages() {
            this.loadImages(1);
        },
        
        // 确认选择（重写原方法以支持图片选择器）
        confirmSelection() {
            const instanceId = this.options.instanceId || 'unknown';
            const context = this.options.context || 'unknown';
            
            if (this.mode === 'select') {
                // 图片选择模式
                if (this.selectedImages.length === 0) {
                    // 移除showMessage调用，避免双重提示
                    console.log('⚠️ 请先选择图片');
                    return;
                }
                
                // 触发确认回调，传递有序的图片列表
                if (this.options.onConfirm) {
                    this.options.onConfirm(this.selectedImages, 'select');
                } else {
                    console.warn('⚠️ 未设置onConfirm回调函数');
                }
                
                this.close();
            } else {
                // 文件上传模式，调用原方法
                this.confirm();
            }
        }
    });
    
    // 重写原始的createModal方法，添加增强功能
    const originalCreateModal = ImageUploader.prototype.createModal;
    ImageUploader.prototype.createModal = function() {
        // 生成唯一的Modal ID
        this.modalId = this.options.instanceId ? 
            `imageUploaderModal_${this.options.instanceId}` : 
            `imageUploaderModal_${Date.now()}`;
        
        const modalHtml = `
            <div class="image-uploader-overlay" id="${this.modalId}">
                <div class="image-uploader-modal">
                    <!-- 弹窗头部 -->
                    <div class="image-uploader-header">
                        <div class="header-title">
                            <i class="fas fa-images"></i>
                            <span>图片管理</span>
                        </div>
                        <div class="header-actions">
                            <button class="btn-minimize" title="最小化">
                                <i class="fas fa-minus"></i>
                            </button>
                            <button class="btn-close" title="关闭">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 弹窗内容 -->
                    <div class="image-uploader-content">
                        <!-- 模式选择选项卡 -->
                        <div class="mode-tabs" id="modeTabs_${this.modalId}" style="display: ${this.options.enableImageSelector ? 'flex' : 'none'};">
                            <button class="mode-tab active" data-mode="upload">
                                <i class="fas fa-upload"></i>
                                <span>上传图片</span>
                            </button>
                            <button class="mode-tab" data-mode="select">
                                <i class="fas fa-images"></i>
                                <span>选择图片</span>
                            </button>
                        </div>
                        
                        <!-- 上传区域 -->
                        <div class="upload-zone" id="uploadZone_${this.modalId}">
                            <div class="upload-zone-content">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <div class="upload-text">
                                    <h3>拖拽图片到此处或点击选择</h3>
                                    <p>支持 JPG、PNG 格式，单个文件最大 5MB</p>
                                    <p>最多可选择 ${this.options.maxFiles} 张图片</p>
                                </div>
                                <button class="btn-select-files">
                                    <i class="fas fa-folder-open"></i>
                                    选择图片
                                </button>
                            </div>
                            <input type="file" id="fileInput_${this.modalId}" multiple accept="image/*" style="display: none;">
                        </div>
                        
                        <!-- 图片选择区域 -->
                        <div class="image-selector-area" id="imageSelectorArea_${this.modalId}" style="display: none;">
                            <div class="selector-header">
                                <div class="selector-filters-info">
                                    <div class="selector-filters">
                                        <select class="selector-group-filter" id="selectorGroupFilter_${this.modalId}">
                                            <option value="">所有分组</option>
                                        </select>
                                        <input type="text" class="selector-search" id="selectorSearch_${this.modalId}" placeholder="搜索图片...">
                                        <button class="btn-search" id="btnSearch_${this.modalId}">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                    <div class="selector-info">
                                        <span class="selected-count">已选择 <strong id="selectorSelectedCount_${this.modalId}">0</strong> 张图片</span>
                                    </div>
                                </div>
                                <div class="selector-actions">
                                    <button class="btn-action" id="btnSelectAll_${this.modalId}" title="全选">
                                        <i class="fas fa-check-square"></i>
                                        全选
                                    </button>
                                    <button class="btn-action" id="btnClearSelection_${this.modalId}" title="清空选择">
                                        <i class="fas fa-square"></i>
                                        清空
                                    </button>
                                    <button class="btn-action danger" id="btnBatchDelete_${this.modalId}" title="批量删除">
                                        <i class="fas fa-trash"></i>
                                        删除
                                    </button>
                                </div>
                            </div>
                            <div class="selector-grid" id="selectorGrid_${this.modalId}">
                                <!-- 图片网格将在这里动态生成 -->
                            </div>
                            <div class="selector-pagination" id="selectorPagination_${this.modalId}">
                                <!-- 分页将在这里动态生成 -->
                            </div>
                        </div>
                        
                        <!-- 图片预览区域 -->
                        <div class="preview-area" id="previewArea_${this.modalId}" style="display: none;">
                            <div class="preview-header">
                                <div class="preview-title">
                                    <i class="fas fa-images"></i>
                                    <span>已选择图片 (<span id="fileCount_${this.modalId}">0</span>/${this.options.maxFiles})</span>
                                </div>
                                <div class="preview-actions">
                                    <button class="btn-add-more">
                                        <i class="fas fa-plus"></i>
                                        继续添加
                                    </button>
                                    <button class="btn-clear-all">
                                        <i class="fas fa-trash"></i>
                                        清空所有
                                    </button>
                                </div>
                            </div>
                            <div class="preview-grid" id="previewGrid_${this.modalId}">
                                <!-- 图片预览项将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- 弹窗底部 -->
                    <div class="image-uploader-footer">
                        <div class="footer-info">
                            <span class="selected-count">已选择 <strong id="selectedCount_${this.modalId}">0</strong> 张图片</span>
                            <span class="upload-progress" id="uploadProgress_${this.modalId}" style="display: none;">
                                上传进度: <strong id="progressText_${this.modalId}">0%</strong>
                            </span>
                        </div>
                        <div class="footer-actions">
                            <button class="btn-cancel">
                                <i class="fas fa-times"></i>
                                取消
                            </button>
                            <button class="btn-confirm" id="btnConfirm_${this.modalId}" disabled>
                                <i class="fas fa-check"></i>
                                确认选择
                            </button>
                            <button class="btn-upload" id="btnUpload_${this.modalId}" style="display: none;">
                                <i class="fas fa-upload"></i>
                                开始上传
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        this.modal = document.getElementById(this.modalId);
        
    };
    
    // 重写原始的绑定事件方法，添加图片选择器初始化
    const originalBindEvents = ImageUploader.prototype.bindEvents;
    ImageUploader.prototype.bindEvents = function() {
        try {
            // 调用原始方法
            originalBindEvents.call(this);
        } catch (error) {
            console.error('❌ 原始bindEvents方法出错:', error);
            // 即使原始方法失败，也继续执行扩展功能
        }
        
        try {
            // 初始化图片选择器
            this.initImageSelector();
        } catch (error) {
            console.error('❌ 初始化图片选择器失败:', error);
        }
        
        try {
            // 重新绑定确认按钮事件
            const btnConfirm = this.modal.querySelector(`#btnConfirm_${this.modalId}`);
            if (btnConfirm) {
                // 移除原有事件监听器
                btnConfirm.removeEventListener('click', this.confirm);
                
                // 添加新的事件监听器
                btnConfirm.addEventListener('click', () => this.confirmSelection());
            }
        } catch (error) {
            console.error('❌ 重新绑定确认按钮事件失败:', error);
        }
    };
    
    // 重写updateUI方法
    const originalUpdateUI = ImageUploader.prototype.updateUI;
    ImageUploader.prototype.updateUI = function() {
        originalUpdateUI.call(this);
        this.updateFooterButtons();
    };
    
    // 重写show方法，确保每次打开时正确刷新状态
    const originalShow = ImageUploader.prototype.show;
    ImageUploader.prototype.show = function() {
        // 重置状态
        this.resetModalState();
        
        // 调用原始show方法
        originalShow.call(this);
        
        // 刷新配置和UI
        this.refreshModalConfig();
        
        // 如果启用了图片选择器，默认切换到上传模式
        if (this.options.enableImageSelector) {
            // 延迟执行，确保DOM已渲染
            setTimeout(() => {
                this.switchMode('upload');
                
                // 如果是编辑器环境，加载图片选择器数据
                if (this.options.enableImageSelector) {
                    this.loadGroups();
                }
            }, 100);
        }
    };
    
    // 添加重置模态框状态的方法
    Object.assign(ImageUploader.prototype, {
        // 重置模态框状态
        resetModalState() {
            // 重置选择的文件和图片
            this.selectedFiles = [];
            this.selectedImages = [];
            this.uploadedFiles = [];
            this.mode = 'upload';
            this.currentPage = 1;
            
            // 重置UI状态（在当前实例的模态框内查找）
            const uploadZone = this.modal.querySelector(`#uploadZone_${this.modalId}`);
            const previewArea = this.modal.querySelector(`#previewArea_${this.modalId}`);
            const selectorArea = this.modal.querySelector(`#imageSelectorArea_${this.modalId}`);
            
            if (uploadZone) uploadZone.style.display = 'flex';
            if (previewArea) previewArea.style.display = 'none';
            if (selectorArea) selectorArea.style.display = 'none';
            
            // 清空预览网格
            const previewGrid = this.modal.querySelector(`#previewGrid_${this.modalId}`);
            if (previewGrid) previewGrid.innerHTML = '';
            
            // 清空选择器网格
            const selectorGrid = this.modal.querySelector(`#selectorGrid_${this.modalId}`);
            if (selectorGrid) selectorGrid.innerHTML = '';
            
            // 重置计数器
            this.updateSelectorUI();
            this.updateFooterButtons();
        },
        
        // 刷新模态框配置
        refreshModalConfig() {
            // 更新标题
            const headerTitle = this.modal.querySelector('.header-title span');
            if (headerTitle) {
                const isEditor = this.options.context === 'editor' || this.options.isEditor;
                const isNews = this.options.context === 'news';
                
                if (isEditor) {
                    headerTitle.textContent = '编辑器图片管理';
                } else if (isNews) {
                    headerTitle.textContent = '新闻图片选择';
                } else {
                    headerTitle.textContent = '图片管理';
                }
            }
            
            // 更新上传区域的文字提示
            const uploadText = this.modal.querySelector('.upload-text');
            if (uploadText) {
                const isEditor = this.options.context === 'editor' || this.options.isEditor;
                const maxFiles = this.options.maxFiles;
                
                let limitText = '';
                if (isEditor || maxFiles >= 999) {
                    limitText = '支持批量选择，无数量限制';
                } else {
                    limitText = `最多可选择 ${maxFiles} 张图片`;
                }
                
                uploadText.innerHTML = `
                    <h3>拖拽图片到此处或点击选择</h3>
                    <p>支持 JPG、PNG 格式，单个文件最大 5MB</p>
                    <p>${limitText}</p>
                `;
            }
            
            // 更新模式选项卡显示
            const modeTabs = this.modal.querySelector(`#modeTabs_${this.modalId}`);
            if (modeTabs) {
                modeTabs.style.display = this.options.enableImageSelector ? 'flex' : 'none';
            }
        }
    });
    

} 
<?php

use think\facade\Route;

// 后台登录相关路由（不需要认证）
Route::group('admin', function () {
    Route::get('login', 'app\admin\controller\Login@index');
    Route::post('login/doLogin', 'app\admin\controller\Login@doLogin');
    Route::get('logout', 'app\admin\controller\Login@logout');
    

});

// 后台管理路由组（需要认证）
Route::group('admin', function () {
    
    // 首页
    Route::get('/', 'app\admin\controller\Index@index');
    Route::get('/index', 'app\admin\controller\Index@index');
    
    // 新闻管理 - 使用更明确的路由配置
    Route::any('news/add-category', 'app\admin\controller\News@addCategory');
    Route::any('news', 'app\admin\controller\News@index');
    
    // 图片管理路由组
    Route::group('image', function () {
        // 上传图片
        Route::post('/upload', 'app\admin\controller\ImageController@upload');
        Route::post('/upload-multiple', 'app\admin\controller\ImageController@uploadMultiple');
        
        // 图片列表和管理
        Route::get('/', 'app\admin\controller\ImageController@index');
        Route::get('/list', 'app\admin\controller\ImageController@index');
        Route::get('/selector', 'app\admin\controller\ImageController@selector');
        Route::get('/detail/:id', 'app\admin\controller\ImageController@detail');
        
        // 图片操作
        Route::post('/update', 'app\admin\controller\ImageController@update');
        Route::post('/delete', 'app\admin\controller\ImageController@delete');
        Route::post('/delete-multiple', 'app\admin\controller\ImageController@deleteMultiple');
        Route::post('/move-to-group', 'app\admin\controller\ImageController@moveToGroup');
        
        // 分组管理
        Route::get('/groups', 'app\admin\controller\ImageController@groups');
        
        // 统计和维护
        Route::get('/statistics', 'app\admin\controller\ImageController@statistics');
        Route::post('/clean', 'app\admin\controller\ImageController@clean');
    });
    
    // 其他管理功能
    Route::get('/settings', 'app\admin\controller\Settings@index');
    Route::post('/settings', 'app\admin\controller\Settings@index');
    
    Route::get('/banners', 'app\admin\controller\Banners@index');
    Route::post('/banners', 'app\admin\controller\Banners@index');
    
    // 产品管理 - 使用更明确的路由配置
    Route::any('products/add-category', 'app\admin\controller\Products@addCategory');
    Route::any('products', 'app\admin\controller\Products@index');
    
    Route::get('/solutions', 'app\admin\controller\Solutions@index');
    Route::post('/solutions', 'app\admin\controller\Solutions@index');
    
    Route::get('/cases', 'app\admin\controller\Cases@index');
    Route::post('/cases', 'app\admin\controller\Cases@index');
    
    Route::get('/contacts', 'app\admin\controller\Contacts@index');
    Route::post('/contacts', 'app\admin\controller\Contacts@index');

    // 页面装修管理
    Route::any('/page-builder', 'app\admin\controller\PageBuilder@index');
    Route::get('/page-builder/preview/:id', 'app\admin\controller\PageBuilder@preview');

    // 页面设计器
    Route::any('/page-designer/diy', 'app\admin\controller\PageDesigner@diy');
    Route::get('/page-designer/preview/:id', 'app\admin\controller\PageDesigner@preview');

    // 系统菜单管理
    Route::any('sys-menu', 'app\admin\controller\SysMenu@index');

})->middleware(['AdminAuth']);

// 注释掉ImageGroup路由，因为控制器不存在
// Route::group('admin/image-group', function () {
//     Route::get('/', 'admin.ImageGroup/index');
//     Route::get('/list', 'admin.ImageGroup/index');
//     Route::post('/add', 'admin.ImageGroup/add');
//     Route::post('/edit', 'admin.ImageGroup/edit');
//     Route::post('/delete', 'admin.ImageGroup/delete');
//     Route::post('/update-sort', 'admin.ImageGroup/updateSort');
// })->middleware(['AdminAuth']); 
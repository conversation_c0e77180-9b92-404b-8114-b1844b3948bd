/**
 * 三只鱼网络科技 | 韩总 | 2025-01-08
 * 产品展示页面样式 - v7.0 - 响应式设计
 * 设计理念：简约现代、响应式、与系统完美融合
 * 优化：产品详情页面全新设计，美观现代
 */

/* ===== CSS变量定义 ===== */
:root {
    /* 主色调 - 与系统保持一致 */
    --primary-color: #667eea;
    --primary-dark: #5a67d8;
    --primary-light: #7c3aed;
    --secondary-color: #764ba2;
    
    /* 中性色 */
    --text-color: #2d3748;
    --text-light: #718096;
    --text-muted: #a0aec0;
    --bg-color: #ffffff;
    --bg-light: #f7fafc;
    --bg-gray: #edf2f7;
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    
    /* 状态色 */
    --success-color: #48bb78;
    --warning-color: #ed8936;
    --error-color: #f56565;
    --info-color: #4299e1;
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 1.5rem;
    --spacing-3xl: 4rem;
    --spacing-4xl: 5rem;
    
    /* 字体 */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* 阴影 */
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.07), 0 2px 4px rgba(0,0,0,0.06);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1), 0 4px 6px rgba(0,0,0,0.05);
    --shadow-xl: 0 20px 25px rgba(0,0,0,0.1), 0 10px 10px rgba(0,0,0,0.04);
    
    /* 圆角 */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* 动画 */
    --transition-fast: 0.15s ease-out;
    --transition-base: 0.2s ease-out;
    --transition-slow: 0.3s ease-out;
}

/* ===== 页面顶部间距适配 ===== */
.page-top-spacing {
    padding-top: 70px; /* 适配固定头部导航 */
}

@media (max-width: 768px) {
    .page-top-spacing {
        padding-top: 60px; /* 移动端头部高度 */
    }
}

/* ===== 面包屑导航样式 ===== */
.breadcrumb-section {
    /* background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-gray) 100%); */
    padding: var(--spacing-xl) 0;
    border-bottom: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.breadcrumb-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="breadcrumb-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="0.5" fill="rgba(102,126,234,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23breadcrumb-pattern)"/></svg>');
    opacity: 0.6;
}

.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
    font-size: var(--font-size-sm);
    position: relative;
    z-index: 2;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: var(--spacing-sm);
}

.breadcrumb-item {
    color: var(--text-light);
    font-weight: 500;
    display: flex;
    align-items: center;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-base);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    
}

.breadcrumb-item a:hover {
    color: var(--primary-dark);
    text-decoration: none;
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.breadcrumb-item a i {
    font-size: var(--font-size-xs);
    width: 16px;
    height: 16px;
    
}

.breadcrumb-item.active {
    color: var(--text-color);
    font-weight: 600;
    
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: var(--text-muted);
    margin: 0 var(--spacing-sm);
    font-size: var(--font-size-lg);
    font-weight: 600;
    
}

/* ===== 产品详情主要内容 ===== */
.product-detail-main {
    padding: var(--spacing-2xl) 0;
    background: var(--bg-color);
}

/* ===== 产品图片展示区域 ===== */
.product-gallery {
    position: sticky;
    top: 90px;
    height: fit-content;
}

.main-image-container {
    position: relative;
    aspect-ratio: 1;
    border-radius: var(--radius-2xl);
    overflow: hidden;
    background: var(--bg-light);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-light);
    margin-bottom: var(--spacing-lg);
}

.main-product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
    cursor: zoom-in;
}

.main-product-image:hover {
    transform: scale(1.02);
}

.no-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-gray) 100%);
}

.no-image-placeholder i {
    font-size: 4rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.no-image-placeholder span {
    font-size: var(--font-size-lg);
    font-weight: 500;
    opacity: 0.7;
}

.image-zoom-overlay {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    opacity: 0;
    transition: opacity var(--transition-base);
    pointer-events: none;
}

.main-image-container:hover .image-zoom-overlay {
    opacity: 1;
}

/* 缩略图网格 */
.thumbnail-gallery {
    margin-top: var(--spacing-lg);
}

.thumbnail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: var(--spacing-md);
}

.thumbnail-item {
    aspect-ratio: 1;
    border-radius: var(--radius-lg);
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all var(--transition-base);
    box-shadow: var(--shadow-sm);
}

.thumbnail-item.active {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.thumbnail-item:hover {
    border-color: var(--primary-light);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.thumbnail-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* ===== 产品信息面板 ===== */
.product-info-panel {
    background: var(--bg-color);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-light);
}

/* 产品标识徽章 */
.product-badges-section {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.product-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-xs);
    font-weight: 600;
    color: white;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: var(--shadow-sm);
    animation: fadeInUp 0.6s ease-out;
}

.product-badge.badge-featured {
    background: linear-gradient(135deg, var(--warning-color) 0%, #f6ad55 100%);
}

.product-badge.badge-hot {
    background: linear-gradient(135deg, var(--error-color) 0%, #fc8181 100%);
}

.product-badge.badge-new {
    background: linear-gradient(135deg, var(--success-color) 0%, #68d391 100%);
}

.product-badge i {
    font-size: var(--font-size-sm);
}

/* 产品标题 */
.product-main-title {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    color: var(--text-color);
    line-height: 1.2;
    margin-bottom: var(--spacing-lg);
    letter-spacing: -0.02em;
}

/* 产品分类标签 */
.product-category-tag {
    margin-bottom: var(--spacing-lg);
}

.category-tag-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    font-size: var(--font-size-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    background: rgba(102, 126, 234, 0.1);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(102, 126, 234, 0.2);
    transition: all var(--transition-base);
}

.category-tag-link:hover {
    background: rgba(102, 126, 234, 0.15);
    border-color: var(--primary-color);
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.category-tag-link i {
    font-size: var(--font-size-xs);
}

/* 产品简介卡片 */
.product-summary-card {
    display: flex;
    gap: var(--spacing-lg);
    background: var(--bg-light);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    margin-bottom: var(--spacing-xl);
}

.summary-icon {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--info-color) 0%, #63b3ed 100%);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.summary-icon i {
    font-size: var(--font-size-lg);
}

.summary-content {
    flex: 1;
}

.summary-content p {
    margin: 0;
    color: var(--text-light);
    font-size: var(--font-size-base);
    line-height: 1.6;
}

/* 产品价格展示 */
.product-pricing-card {
    background: linear-gradient(135deg, var(--success-color) 0%, #68d391 100%);
    color: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
}

.price-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    font-weight: 600;
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

.price-header i {
    font-size: var(--font-size-base);
}

.price-content {
    display: flex;
    align-items: baseline;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.current-price {
    font-size: var(--font-size-3xl);
    font-weight: 800;
    color: white;
}

.original-price {
    font-size: var(--font-size-lg);
    text-decoration: line-through;
    opacity: 0.7;
}

.discount-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

/* 产品元信息网格 */
.product-meta-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.meta-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    background: var(--bg-light);
    padding: var(--spacing-lg);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    transition: all var(--transition-base);
}

.meta-card:hover {
    background: white;
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.meta-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.meta-icon i {
    font-size: var(--font-size-base);
}

.meta-content {
    flex: 1;
}

.meta-label {
    display: block;
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--spacing-xs);
}

.meta-value {
    font-size: var(--font-size-base);
    color: var(--text-color);
    font-weight: 600;
}

.meta-card.meta-tags .meta-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.tag-item {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 500;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

/* 库存状态样式 */
.stock-in_stock {
    color: var(--success-color);
}

.stock-out_of_stock {
    color: var(--error-color);
}

.stock-pre_order {
    color: var(--warning-color);
}

/* 操作按钮组 */
.product-actions-section {
    border-top: 1px solid var(--border-light);
    padding-top: var(--spacing-xl);
}

.action-buttons-group {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.btn-action-primary {
    flex: 1;
    min-width: 200px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    border: none;
    padding: var(--spacing-lg) var(--spacing-xl);
    border-radius: var(--radius-xl);
    font-weight: 600;
    font-size: var(--font-size-base);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-base);
    box-shadow: var(--shadow-md);
}

.btn-action-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
}

.btn-action-secondary {
    background: white;
    color: var(--text-color);
    border: 2px solid var(--border-color);
    padding: var(--spacing-lg) var(--spacing-xl);
    border-radius: var(--radius-xl);
    font-weight: 600;
    font-size: var(--font-size-base);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-base);
}

.btn-action-secondary:hover {
    background: var(--bg-light);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.btn-action-icon {
    width: 56px;
    height: 56px;
    background: white;
    color: var(--text-light);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-base);
    flex-shrink: 0;
}

.btn-action-icon:hover {
    background: var(--error-color);
    color: white;
    border-color: var(--error-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.btn-action-icon.favorited {
    background: var(--error-color);
    color: white;
    border-color: var(--error-color);
}

.btn-action-icon.animate-heart {
    animation: heartBeat 0.6s ease-out;
}

@keyframes heartBeat {
    0% { transform: scale(1); }
    25% { transform: scale(1.2); }
    50% { transform: scale(1); }
    75% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* ===== 产品详细信息选项卡 ===== */
.product-details-section {
    padding: var(--spacing-2xl) 0;
    background: var(--bg-light);
}

.details-tabs-container {
    background: white;
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-light);
}

.details-nav {
    background: var(--bg-light);
    border-bottom: 1px solid var(--border-light);
    margin: 0;
}

.details-nav .nav-item {
    margin: 0;
}

.details-nav .nav-link {
    background: transparent;
    border: none;
    color: var(--text-light);
    font-weight: 600;
    font-size: var(--font-size-base);
    padding: var(--spacing-xl) var(--spacing-lg);
    border-radius: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-base);
    position: relative;
}

.details-nav .nav-link:hover {
    color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
}

.details-nav .nav-link.active {
    color: var(--primary-color);
    background: white;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.details-nav .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
}

.details-nav .nav-link i {
    font-size: var(--font-size-base);
}

.details-content {
    padding: var(--spacing-2xl);
}

.tab-pane {
    opacity: 0;
    transform: translateY(20px);
    transition: all var(--transition-slow);
}

.tab-pane.active {
    opacity: 1;
    transform: translateY(0);
}

/* 产品详情内容 */
.detail-content-wrapper {
    max-width: none;
}

.rich-content {
    color: var(--text-color);
    line-height: 1.8;
}

.rich-content h1,
.rich-content h2,
.rich-content h3,
.rich-content h4,
.rich-content h5,
.rich-content h6 {
    color: var(--text-color);
    font-weight: 700;
    margin-top: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
}

.rich-content p {
    margin-bottom: var(--spacing-lg);
    color: var(--text-light);
}

.rich-content img {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    margin: var(--spacing-lg) 0;
}

.empty-content {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.empty-content i {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.empty-content p {
    font-size: var(--font-size-lg);
    margin: 0;
}

/* 产品特性网格 */
.features-grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.feature-card {
    background: var(--bg-light);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    transition: all var(--transition-base);
}

.feature-card:hover {
    background: white;
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.feature-icon-wrapper {
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, var(--success-color) 0%, #68d391 100%);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-bottom: var(--spacing-lg);
}

.feature-icon-wrapper i {
    font-size: var(--font-size-xl);
}

.feature-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: var(--spacing-md);
}

.feature-description {
    color: var(--text-light);
    line-height: 1.6;
    margin: 0;
}

/* 技术规格表格 */
.specifications-wrapper {
    background: var(--bg-light);
    border-radius: var(--radius-xl);
    overflow: hidden;
}

.spec-table-container {
    overflow-x: auto;
}

.spec-table {
    width: 100%;
    margin: 0;
    background: white;
}

.spec-table tr:nth-child(even) {
    background: var(--bg-light);
}

.spec-table td {
    padding: var(--spacing-lg);
    border: none;
    border-bottom: 1px solid var(--border-light);
}

.spec-label {
    font-weight: 600;
    color: var(--text-color);
    width: 30%;
    background: var(--bg-light);
}

.spec-value {
    color: var(--text-light);
}

/* ===== 相关产品推荐 ===== */
.related-products-section {
    padding: var(--spacing-2xl) 0;
    background: white;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.section-title-wrapper {
    max-width: 600px;
    margin: 0 auto;
}

.section-title {
    font-size: var(--font-size-3xl);
    font-weight: 800;
    color: var(--text-color);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
}

.section-title i {
    color: var(--primary-color);
}

.section-subtitle {
    color: var(--text-light);
    font-size: var(--font-size-lg);
    margin: 0;
}

.related-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
}

.related-product-card {
    background: white;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
    transition: all var(--transition-base);
}

.related-product-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.related-product-image {
    position: relative;
    aspect-ratio: 4/3;
    overflow: hidden;
    background: var(--bg-light);
}

.related-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.related-product-card:hover .related-product-image img {
    transform: scale(1.05);
}

.related-product-image .no-image {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    background: var(--bg-light);
}

.related-product-image .no-image i {
    font-size: 2rem;
    margin-bottom: var(--spacing-sm);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-base);
}

.related-product-card:hover .product-overlay {
    opacity: 1;
}

.overlay-btn {
    background: white;
    color: var(--text-color);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-base);
}

.overlay-btn:hover {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.related-product-info {
    padding: var(--spacing-xl);
}

.related-product-name {
    font-size: var(--font-size-lg);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
}

.related-product-name a {
    color: var(--text-color);
    text-decoration: none;
    transition: color var(--transition-base);
}

.related-product-name a:hover {
    color: var(--primary-color);
    text-decoration: none;
}

.related-product-description {
    color: var(--text-light);
    font-size: var(--font-size-sm);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.related-product-price {
    display: flex;
    align-items: baseline;
    gap: var(--spacing-sm);
}

.related-product-price .current-price {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--success-color);
}

.related-product-price .original-price {
    font-size: var(--font-size-base);
    color: var(--text-muted);
    text-decoration: line-through;
}

/* 产品徽章样式 */
.products-page-badges {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    z-index: 2;
}

.badge-featured {
    background: linear-gradient(135deg, var(--warning-color) 0%, #f6ad55 100%);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-hot {
    background: linear-gradient(135deg, var(--error-color) 0%, #fc8181 100%);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-new {
    background: linear-gradient(135deg, var(--success-color) 0%, #68d391 100%);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== 图片模态框 ===== */
.image-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    cursor: pointer;
}

.modal-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    z-index: 10001;
}

.modal-content img {
    max-width: 100%;
    max-height: 100%;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
}

.modal-close {
    position: absolute;
    top: -40px;
    right: 0;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-base);
}

.modal-close:hover {
    background: white;
    transform: scale(1.1);
}

.modal-close i {
    font-size: var(--font-size-lg);
    color: var(--text-color);
}

/* ===== 提示消息 ===== */
.toast-message {
    position: fixed;
    top: 100px;
    right: var(--spacing-xl);
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-light);
    z-index: 10000;
    transform: translateX(100%);
    transition: transform var(--transition-slow);
}

.toast-message.show {
    transform: translateX(0);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg) var(--spacing-xl);
}

.toast-success {
    border-left: 4px solid var(--success-color);
}

.toast-error {
    border-left: 4px solid var(--error-color);
}

.toast-info {
    border-left: 4px solid var(--info-color);
}

.toast-content i {
    font-size: var(--font-size-lg);
}

.toast-success i {
    color: var(--success-color);
}

.toast-error i {
    color: var(--error-color);
}

.toast-info i {
    color: var(--info-color);
}

.toast-content span {
    font-weight: 500;
    color: var(--text-color);
}

/* ===== 动画效果 ===== */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== 响应式设计 ===== */
@media (max-width: 992px) {
    .product-main-title {
        font-size: var(--font-size-3xl);
    }
    
    .product-meta-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons-group {
        flex-direction: column;
    }
    
    .btn-action-primary {
        min-width: auto;
    }
    
    .features-grid-container {
        grid-template-columns: 1fr;
    }
    
    .related-products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .product-detail-main {
        padding: var(--spacing-xl) 0;
    }
    
    .product-info-panel {
        padding: var(--spacing-xl);
    }
    
    .product-main-title {
        font-size: var(--font-size-2xl);
    }
    
    .product-summary-card {
        flex-direction: column;
        text-align: center;
    }
    
    .details-content {
        padding: var(--spacing-xl);
    }
    
    .details-nav .nav-link {
        padding: var(--spacing-lg) var(--spacing-md);
        font-size: var(--font-size-sm);
    }
    
    .section-title {
        font-size: var(--font-size-2xl);
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .toast-message {
        right: var(--spacing-md);
        left: var(--spacing-md);
    }
}

@media (max-width: 576px) {
    .breadcrumb {
        font-size: var(--font-size-xs);
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        margin: 0 var(--spacing-xs);
    }
    
    .product-gallery {
        position: static;
    }
    
    .thumbnail-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .product-main-title {
        font-size: var(--font-size-xl);
    }
    
    .related-products-grid {
        grid-template-columns: 1fr;
    }
}

/* ===== 产品列表页面优化样式 ===== */

/* 产品页面头部横幅 */
.products-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.products-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hero-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.15)"/></pattern></defs><rect width="100" height="100" fill="url(%23hero-pattern)"/></svg>');
    opacity: 0.4;
}

.products-hero::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(180deg, transparent 0%, rgba(255,255,255,0.1) 100%);
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    animation: fadeInUp 0.8s ease-out;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: rgba(255, 255, 255, 0.25);
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-2xl);
    font-size: var(--font-size-sm);
    font-weight: 600;
    margin-bottom: var(--spacing-xl);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.4);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.hero-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.hero-badge i {
    width: 18px;
    height: 18px;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    margin-bottom: var(--spacing-xl);
    line-height: 1.1;
    letter-spacing: -0.03em;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.hero-subtitle {
    font-size: var(--font-size-lg);
    opacity: 0.95;
    margin-bottom: var(--spacing-2xl);
    line-height: 1.7;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-2xl);
    flex-wrap: wrap;
    margin-top: var(--spacing-2xl);
}

.hero-stat {
    text-align: center;
    background: rgba(255, 255, 255, 0.15);
    padding: var(--spacing-lg) var(--spacing-xl);
    border-radius: var(--radius-xl);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 120px;
    transition: all 0.3s ease;
}

.hero-stat:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hero-stat-number {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: 800;
    margin-bottom: var(--spacing-xs);
    color: #ffffff;
}

.hero-stat-label {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    font-weight: 500;
}

/* 产品主要内容区域 */
.products-main {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);
    min-height: 80vh;
}

.products-container {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: var(--spacing-2xl);
    align-items: start;
}

/* 侧边栏样式 */
.products-sidebar {
    position: sticky;
    top: 120px;
}

.filter-card {
    background: white;
    border-radius: var(--radius-2xl);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-light);
    overflow: hidden;
}

.filter-section {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border-light);
}

.filter-section:last-child {
    border-bottom: none;
}

.filter-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: var(--spacing-lg);
}

.filter-title i {
    width: 20px;
    height: 20px;
    color: var(--primary-color);
}

.filter-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.filter-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    color: var(--text-light);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
}

.filter-option:hover {
    background: rgba(102, 126, 234, 0.08);
    color: var(--primary-color);
    text-decoration: none;
    transform: translateX(4px);
}

.filter-option.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.filter-option.loading {
    opacity: 0.7;
    pointer-events: none;
}

.filter-option.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

.filter-option i {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

/* 推荐产品区域 */
.featured-section {
    background: var(--bg-light);
    margin: 0 -var(--spacing-xl);
    padding: var(--spacing-xl);
}

.featured-products {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.featured-item {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: white;
    border-radius: var(--radius-lg);
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    border: 1px solid var(--border-light);
}

.featured-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    color: inherit;
}

.featured-image {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-md);
    overflow: hidden;
    flex-shrink: 0;
    background: var(--bg-light);
}

.featured-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.featured-image .no-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    background: var(--bg-gray);
}

.featured-image .no-image i {
    width: 24px;
    height: 24px;
}

.featured-info {
    flex: 1;
    min-width: 0;
}

.featured-name {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: var(--spacing-xs);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.featured-price {
    font-size: var(--font-size-sm);
    color: var(--success-color);
    font-weight: 600;
}

/* 产品内容区域 */
.products-content {
    background: white;
    border-radius: var(--radius-2xl);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-light);
    overflow: hidden;
}

/* 工具栏样式 */
.products-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl);
    background: var(--bg-light);
    border-bottom: 1px solid var(--border-light);
    flex-wrap: wrap;
    gap: var(--spacing-lg);
    min-height: 80px;
}

.toolbar-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
    flex: 1;
    min-width: 0;
    height: 44px;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    height: 44px;
}

.results-info {
    font-size: var(--font-size-base);
    color: var(--text-color);
    font-weight: 600;
    background: white;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    white-space: nowrap;
    height: 44px;
    display: flex;
    align-items: center;
}

.results-count {
    color: var(--primary-color);
    font-weight: 700;
}

/* 产品列表页面搜索表单样式 */
.products-toolbar .search-form {
    position: relative;
    display: flex;
    align-items: center;
    min-width: 320px;
    height: 44px;
}

.products-toolbar .search-form .search-icon {
    position: absolute;
    left: 14px;
    top: 50%;
    transform: translateY(-50%);
    width: 18px;
    height: 18px;
    color: var(--text-muted);
    z-index: 2;
}

.products-toolbar .search-form .search-input {
    width: 100%;
    height: 44px;
    padding: 0 90px 0 45px;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    color: var(--text-color);
    background: white;
    outline: none;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.products-toolbar .search-form .search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.products-toolbar .search-form .search-input::placeholder {
    color: var(--text-muted);
}

.products-toolbar .search-form .search-btn {
    position: absolute;
    right: 6px;
    top: 4px;
    bottom: 3px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: 0 18px;
    font-size: var(--font-size-sm);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
}

.products-toolbar .search-form .search-btn:hover {
    background: var(--primary-dark);
}

/* 排序选择器样式 */
.sort-select-wrapper {
    position: relative;
    display: inline-block;
    min-width: 220px;
}

.sort-select {
    width: 100%;
    background: white;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md) 40px var(--spacing-md) 45px;
    font-size: var(--font-size-sm);
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.3s ease;
    height: 54px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    font-weight: 500;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: none;
}

.sort-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.sort-select:hover {
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.sort-select-wrapper::after {
    content: '';
    position: absolute;
    right: 14px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid var(--text-muted);
    pointer-events: none;
    transition: all 0.3s ease;
}

.sort-select-wrapper:hover::after {
    border-top-color: var(--primary-color);
}

.sort-select-wrapper.loading {
    opacity: 0.7;
    pointer-events: none;
}

.sort-select-wrapper.loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.2), transparent);
    animation: shimmer 1.5s infinite;
    z-index: 1;
}

.sort-select-wrapper .sort-icon {
    position: absolute;
    left: 14px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    color: var(--primary-color);
    pointer-events: none;
}

/* 排序选项样式 */
.sort-select option {
    padding: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-color);
    background: white;
}

/* 产品网格样式 */
.products-page-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--spacing-xl);
    padding: var(--spacing-xl);
}

/* 产品卡片样式 */
.products-page-card {
    background: white;
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-light);
    transition: all 0.4s ease;
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.products-page-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: rgba(102, 126, 234, 0.3);
}

/* 产品图片区域 */
.products-page-image {
    position: relative;
    aspect-ratio: 4/3;
    overflow: hidden;
    background: var(--bg-light);
}

.products-page-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.products-page-card:hover .products-page-image img {
    transform: scale(1.05);
}

.products-page-image .no-image {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-gray) 100%);
}

.products-page-image .no-image i {
    width: 48px;
    height: 48px;
    margin-bottom: var(--spacing-sm);
    opacity: 0.6;
}

.products-page-image .no-image span {
    font-size: var(--font-size-sm);
    font-weight: 500;
    opacity: 0.8;
}

/* 产品标识徽章 */
.products-page-badges {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    z-index: 2;
}

.products-page-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: white;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.products-page-badge i {
    width: 12px;
    height: 12px;
}

.badge-featured {
    background: linear-gradient(135deg, var(--warning-color) 0%, #f6ad55 100%);
}

.badge-hot {
    background: linear-gradient(135deg, var(--error-color) 0%, #fc8181 100%);
}

.badge-new {
    background: linear-gradient(135deg, var(--success-color) 0%, #68d391 100%);
}

/* 产品信息区域 */
.products-page-info {
    padding: var(--spacing-xl);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.products-page-category {
    display: inline-block;
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-decoration: none;
    margin-bottom: var(--spacing-md);
    align-self: flex-start;
    transition: all 0.3s ease;
}

.products-page-category:hover {
    background: rgba(102, 126, 234, 0.15);
    text-decoration: none;
    color: var(--primary-dark);
}

.products-page-name {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: var(--spacing-md);
    line-height: 1.4;
}

.products-page-name a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
}

.products-page-name a:hover {
    color: var(--primary-color);
    text-decoration: none;
}

.products-page-description {
    color: var(--text-light);
    font-size: var(--font-size-sm);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
    flex: 1;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 产品底部信息 */
.products-page-footer {
    padding: 0 var(--spacing-xl) var(--spacing-xl);
    border-top: 1px solid var(--border-light);
    margin-top: auto;
}

.products-page-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-top: var(--spacing-lg);
}

.products-page-price {
    display: flex;
    align-items: baseline;
    gap: var(--spacing-sm);
}

.products-page-price .current-price {
    font-size: var(--font-size-xl);
    font-weight: 800;
    color: var(--success-color);
}

.products-page-price .original-price {
    font-size: var(--font-size-base);
    color: var(--text-muted);
    text-decoration: line-through;
}

.products-page-stats {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    pointer-events: none;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--text-muted);
    font-size: var(--font-size-xs);
    pointer-events: none;
    cursor: default;
}

.stat-item i {
    width: 14px;
    height: 14px;
}

/* 产品操作按钮 */
.products-page-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.btn-primary-outline {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    border: none;
    border-radius: var(--radius-xl);
    text-decoration: none;
    font-weight: 600;
    font-size: var(--font-size-sm);
    line-height: 1;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
    height: 44px;
    min-height: 44px;
}

.btn-primary-outline::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.btn-primary-outline:hover::before {
    left: 100%;
}

.btn-primary-outline:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-primary-outline i {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-secondary-outline {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 44px;
    min-height: 44px;
    background: white;
    color: var(--text-light);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-xl);
    text-decoration: none;
    transition: all 0.3s ease;
    flex-shrink: 0;

    line-height: 1;
}

.btn-secondary-outline:hover {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(72, 187, 120, 0.3);
}

.btn-secondary-outline i {
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: var(--spacing-3xl);
    color: var(--text-muted);
}

.empty-icon {
    margin-bottom: var(--spacing-xl);
}

.empty-icon i {
    width: 80px;
    height: 80px;
    color: var(--text-muted);
    opacity: 0.5;
}

.empty-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: var(--spacing-lg);
}

.empty-description {
    font-size: var(--font-size-base);
    line-height: 1.6;
    margin-bottom: var(--spacing-xl);
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.empty-action {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-lg) var(--spacing-xl);
    border-radius: var(--radius-xl);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.empty-action:hover {
    background: var(--primary-dark);
    text-decoration: none;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.empty-action i {
    width: 18px;
    height: 18px;
}

/* 分页样式 */
.pagination-wrapper {
    padding: var(--spacing-xl);
    border-top: 1px solid var(--border-light);
    background: var(--bg-light);
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
}

.page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    height: 44px;
    padding: var(--spacing-sm) var(--spacing-md);
    background: white;
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.page-link:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.page-link.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.page-link.disabled {
    background: var(--bg-light);
    color: var(--text-muted);
    border-color: var(--border-light);
    cursor: not-allowed;
}

.page-link i {
    width: 16px;
    height: 16px;
}

/* 搜索页面专用搜索框样式 */
.filter-section .search-form {
    position: relative;
    width: 100%;
}

.filter-section .search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    height: 44px;
}

.filter-section .search-input-wrapper .search-icon {
    position: absolute;
    left: 14px;
    top: 50%;
    transform: translateY(-50%);
    width: 18px;
    height: 18px;
    color: var(--text-muted);
    z-index: 2;
}

.filter-section .search-input-wrapper .search-input {
    width: 100%;
    height: 44px;
    padding: 0 110px 0 45px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    color: var(--text-color);
    background: white;
    outline: none;
    transition: all 0.3s ease;
}

.filter-section .search-input-wrapper .search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-section .search-input-wrapper .search-input::placeholder {
    color: var(--text-muted);
}

.filter-section .search-input-wrapper .search-btn {
    position: absolute;
    right: 6px;
    top: 4px;
    bottom: 3px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: 0 18px;
    font-size: var(--font-size-sm);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    min-width: 40px;
}

.filter-section .search-input-wrapper .search-btn:hover {
    background: var(--primary-dark);
}

.filter-section .search-input-wrapper .search-btn i {
    width: 16px;
    height: 16px;
}

.search-tips {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.tip-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-light);
    font-size: var(--font-size-sm);
}

.tip-item i {
    width: 16px;
    height: 16px;
    color: var(--success-color);
    flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .products-container {
        grid-template-columns: 260px 1fr;
        gap: var(--spacing-3xl);
    }

    .products-page-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }
}

@media (max-width: 992px) {
    .products-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .products-sidebar {
        position: static;
        order: 2;
    }

    .products-content {
        order: 1;
    }

    .products-toolbar {
        flex-direction: column;
        align-items: stretch;
    }

    .toolbar-left {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-lg);
    }

    .products-toolbar .search-form {
        min-width: auto;
        width: 100%;
    }

    .sort-select-wrapper {
        min-width: 180px;
    }

    .sort-select-wrapper .sort-icon {
        display: none;
    }

    .sort-select {
        padding-left: var(--spacing-lg);
    }

    .filter-section .search-input-wrapper {
        width: 100%;
    }

    .filter-section .search-input-wrapper .search-input {
        padding: 0 90px 0 40px;
    }

    .filter-section .search-input-wrapper .search-btn {
        min-width: 80px;
        padding: 0 12px;
    }

    .hero-stats {
        gap: var(--spacing-lg);
    }

    .hero-stat {
        min-width: 100px;
        padding: var(--spacing-md) var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .products-hero {
        padding: 3rem 0;
    }

    .hero-title {
        font-size: clamp(2rem, 8vw, 3rem);
    }

    .products-page-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        padding: var(--spacing-lg);
    }

    .filter-card {
        border-radius: var(--radius-xl);
    }

    .products-page-card {
        border-radius: var(--radius-xl);
    }

    .products-page-actions {
        flex-direction: row;
        gap: var(--spacing-sm);
    }

    .btn-secondary-outline {
        width: 48px;
        height: 44px;
    }

    .btn-primary-outline {
        font-size: var(--font-size-xs);
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .pagination {
        flex-wrap: wrap;
        gap: var(--spacing-xs);
    }

    .page-link {
        min-width: 40px;
        height: 40px;
        font-size: var(--font-size-sm);
    }
}

@media (max-width: 480px) {
    .products-main {
        padding: var(--spacing-2xl) 0;
    }

    .hero-stats {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: center;
    }

    .hero-stat {
        width: 100%;
        max-width: 200px;
    }

    .products-page-info {
        padding: var(--spacing-lg);
    }

    .products-page-footer {
        padding: 0 var(--spacing-lg) var(--spacing-lg);
    }

    .products-page-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .sort-select-wrapper {
        min-width: 160px;
    }

    .sort-select {
        font-size: var(--font-size-xs);
    }
}

/* 加载指示器样式 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(2px);
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    color: var(--primary-color);
    font-weight: 600;
}

.loading-spinner i {
    width: 32px;
    height: 32px;
    animation: spin 1s linear infinite;
}

.loading-spinner span {
    font-size: var(--font-size-sm);
    color: var(--text-color);
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 确保产品内容区域有相对定位以支持加载覆盖层 */
.products-content {
    position: relative;
}
{"version": 3, "file": "nb-no.min.js", "sources": ["../../../../packages/locale/lang/nb-no.ts"], "sourcesContent": ["export default {\n  name: 'nb-no',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: 'Tøm',\n    },\n    datepicker: {\n      now: 'N<PERSON>',\n      today: 'I dag',\n      cancel: 'Av<PERSON><PERSON><PERSON>',\n      clear: 'Tøm',\n      confirm: 'OK',\n      selectDate: 'Velg dato',\n      selectTime: 'Velg tidspunkt',\n      startDate: 'Startdato',\n      startTime: 'Starttidspunkt',\n      endDate: 'Sluttdato',\n      endTime: 'Sluttidspunkt',\n      prevYear: 'I fjor',\n      nextYear: 'Neste år',\n      prevMonth: '<PERSON><PERSON>e <PERSON>å<PERSON>',\n      nextMonth: 'Neste Måned',\n      year: '',\n      month1: 'Januar',\n      month2: 'Februar',\n      month3: 'Mars',\n      month4: 'April',\n      month5: 'Mai',\n      month6: 'Juni',\n      month7: 'Juli',\n      month8: 'August',\n      month9: 'September',\n      month10: 'Oktober',\n      month11: 'November',\n      month12: 'Des<PERSON>ber',\n      week: 'uke',\n      weeks: {\n        sun: 'Søn',\n        mon: 'Man',\n        tue: 'Tir',\n        wed: 'Ons',\n        thu: 'Tor',\n        fri: 'Fre',\n        sat: 'Lør',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Mai',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Des',\n      },\n    },\n    select: {\n      loading: 'Laster',\n      noMatch: 'Ingen samsvarende resulater',\n      noData: 'Ingen resulater',\n      placeholder: 'Velg',\n    },\n    mention: {\n      loading: 'Laster',\n    },\n    cascader: {\n      noMatch: 'Ingen samsvarende resultater',\n      loading: 'Laster',\n      placeholder: 'Velg',\n      noData: 'Ingen resultater',\n    },\n    pagination: {\n      goto: 'Gå til',\n      pagesize: '/side',\n      total: 'Total {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      confirm: 'OK',\n      cancel: 'Avbryt',\n      error: 'Ugyldig input',\n    },\n    upload: {\n      deleteTip: 'trykk på x for å slette',\n      delete: 'Slett',\n      preview: 'Forhåndsvisning',\n      continue: 'Fortsett',\n    },\n    table: {\n      emptyText: 'Ingen Data',\n      confirmFilter: 'Bekreft',\n      resetFilter: 'Tilbakestill',\n      clearFilter: 'Alle',\n      sumText: 'Sum',\n    },\n    tree: {\n      emptyText: 'Ingen Data',\n    },\n    transfer: {\n      noMatch: 'Ingen samsvarende data',\n      noData: 'Ingen data',\n      titles: ['Liste 1', 'Liste 2'],\n      filterPlaceholder: 'Skriv inn nøkkelord',\n      noCheckedFormat: '{total} gjenstander',\n      hasCheckedFormat: '{checked}/{total} valgt',\n    },\n    image: {\n      error: 'FEILET',\n    },\n    pageHeader: {\n      title: 'Tilbake',\n    },\n    popconfirm: {\n      confirmButtonText: 'Ja',\n      cancelButtonText: 'Nei',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,aAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,6BAA6B,CAAC,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,8BAA8B,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,aAAa,CAAC,SAAS,CAAC,WAAW,CAAC,cAAc,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,wBAAwB,CAAC,eAAe,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}
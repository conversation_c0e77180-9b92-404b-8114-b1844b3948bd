{include file="common/header-bg"}

<!-- 产品搜索页面样式 -->
<link rel="stylesheet" href="{:asset('assets/css/products.css')}?v=7.1">

<!-- 搜索结果页面头部横幅 -->
<section class="products-hero page-top-spacing">
    <div class="container">
        <div class="hero-content">
            <div class="hero-badge">
                <i data-lucide="search"></i>
                搜索结果
            </div>
            <h1 class="hero-title">搜索：{$keyword}</h1>
            <p class="hero-subtitle">为您找到相关产品，精准匹配您的需求</p>
            <div class="hero-stats">
                <div class="hero-stat">
                    <div class="hero-stat-number">{$products->total()}</div>
                    <div class="hero-stat-label">搜索结果</div>
                </div>
                <div class="hero-stat">
                    <div class="hero-stat-number">{$products->lastPage()}</div>
                    <div class="hero-stat-label">总页数</div>
                </div>
                <div class="hero-stat">
                    <div class="hero-stat-number">{$products->currentPage()}</div>
                    <div class="hero-stat-label">当前页</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 产品主要内容 -->
<section class="products-main">
    <div class="container">
        <div class="products-container">
            <!-- 侧边栏筛选 -->
            <aside class="products-sidebar">
                <div class="filter-card">
                    <!-- 重新搜索 -->
                    <div class="filter-section">
                        <h3 class="filter-title">
                            <i data-lucide="search"></i>
                            重新搜索
                        </h3>
                        <form action="/products/search" method="GET" class="search-form">
                            <div class="search-input-wrapper">
                                <i data-lucide="search" class="search-icon"></i>
                                <input type="text" class="search-input" name="keyword" 
                                       placeholder="搜索产品名称、描述..." value="{$keyword}">
                                <button type="submit" class="search-btn">
                                    <i data-lucide="search"></i>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- 分类筛选 -->
                    <div class="filter-section">
                        <h3 class="filter-title">
                            <i data-lucide="tag"></i>
                            产品分类
                        </h3>
                        <div class="filter-options">
                            <a href="/products/search?keyword={$keyword}" 
                               class="filter-option {if condition='!$categoryId'}active{/if}">
                                <i data-lucide="grid-3x3"></i>
                                全部分类
                            </a>
                            {volist name="categories" id="category"}
                            <a href="/products/search?keyword={$keyword}&category_id={$category.id}" 
                               class="filter-option {if condition='$categoryId == $category.id'}active{/if}">
                                <i data-lucide="tag"></i>
                                {$category.name}
                            </a>
                            {/volist}
                        </div>
                    </div>

                    <!-- 搜索提示 -->
                    <div class="filter-section">
                        <h3 class="filter-title">
                            <i data-lucide="lightbulb"></i>
                            搜索提示
                        </h3>
                        <div class="search-tips">
                            <div class="tip-item">
                                <i data-lucide="check-circle"></i>
                                <span>使用关键词搜索产品名称</span>
                            </div>
                            <div class="tip-item">
                                <i data-lucide="check-circle"></i>
                                <span>尝试不同的关键词组合</span>
                            </div>
                            <div class="tip-item">
                                <i data-lucide="check-circle"></i>
                                <span>选择分类进行精确筛选</span>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 产品内容区域 -->
            <main class="products-content">
                <!-- 搜索结果工具栏 -->
                <div class="products-toolbar">
                    <div class="toolbar-left">
                        <div class="results-info">
                            搜索关键词：<span class="results-count">"{$keyword}"</span>
                        </div>
                    </div>
                    <div class="toolbar-right">
                        <div class="results-info">
                            显示第 <span class="results-count">{$products->currentPage()}</span> 页，
                            共 <span class="results-count">{$products->lastPage()}</span> 页，
                            总计 <span class="results-count">{$products->total()}</span> 个产品
                        </div>
                    </div>
                </div>

                <!-- 产品网格 -->
                {if condition="$products && count($products) > 0"}
                    <div class="products-page-grid">
                        {volist name="products" id="product"}
                        <article class="products-page-card">
                            <div class="products-page-image">
                                <a href="/products/{$product.slug ?: $product.id}">
                                    {if condition="$product.image"}
                                        <img src="{$product.image}" alt="{$product.name}" loading="lazy">
                                    {else /}
                                        <div class="no-image">
                                            <i data-lucide="package"></i>
                                            <span>暂无图片</span>
                                        </div>
                                    {/if}
                                </a>
                                
                                <!-- 产品标识 -->
                                <div class="products-page-badges">
                                    {if condition="$product.is_featured"}
                                        <span class="products-page-badge badge-featured">
                                            <i data-lucide="star"></i>
                                            推荐
                                        </span>
                                    {/if}
                                    {if condition="$product.is_hot"}
                                        <span class="products-page-badge badge-hot">
                                            <i data-lucide="flame"></i>
                                            热门
                                        </span>
                                    {/if}
                                    {if condition="$product.is_new"}
                                        <span class="products-page-badge badge-new">
                                            <i data-lucide="sparkles"></i>
                                            新品
                                        </span>
                                    {/if}
                                </div>
                            </div>

                            <div class="products-page-info">
                                <div class="products-page-category">
                                    <a href="/products?category={$product.category.slug ?: $product.category.id}">
                                        {$product.category.name ?? '未分类'}
                                    </a>
                                </div>

                                <h3 class="products-page-name">
                                    <a href="/products/{$product.slug ?: $product.id}">
                                        {$product.name}
                                    </a>
                                </h3>

                                {if condition="$product.short_description"}
                                    <p class="products-page-description">
                                        {$product.short_description|mb_substr=0,100,'UTF-8'}
                                        {if condition="mb_strlen($product.short_description, 'UTF-8') > 100"}...{/if}
                                    </p>
                                {/if}
                            </div>

                            <div class="products-page-footer">
                                <div class="products-page-meta">
                                    {if condition="$product.price > 0"}
                                        <div class="products-page-price">
                                            <span class="current-price">¥{$product.price}</span>
                                            {if condition="$product.original_price > $product.price"}
                                                <span class="original-price">¥{$product.original_price}</span>
                                            {/if}
                                        </div>
                                    {else /}
                                        <div class="products-page-price">
                                            <span class="current-price">价格面议</span>
                                        </div>
                                    {/if}

                                    <div class="products-page-stats">
                                        <span class="stat-item">
                                            <i data-lucide="eye"></i>
                                            <span>{$product.views ?? 0}</span>
                                        </span>
                                        <span class="stat-item">
                                            <i data-lucide="package-check"></i>
                                            <span>{$product.stock_status_text ?? '现货'}</span>
                                        </span>
                                    </div>
                                </div>

                                <div class="products-page-actions">
                                    <a href="/products/{$product.slug ?: $product.id}" 
                                       class="btn-primary-outline">
                                        <i data-lucide="eye"></i>
                                        查看详情
                                    </a>
                                    <a href="/contact?product={$product.slug ?: $product.id}" 
                                       class="btn-secondary-outline">
                                        <i data-lucide="message-circle"></i>
                                    </a>
                                </div>
                            </div>
                        </article>
                        {/volist}
                    </div>

                    <!-- 分页 -->
                    {if condition="$products->hasPages()"}
                        <div class="pagination-wrapper">
                            <nav aria-label="产品分页" class="pagination">
                                {if condition="$products->currentPage() > 1"}
                                    <a class="page-link" href="{$products->previousPageUrl()}" aria-label="上一页">
                                        <i data-lucide="chevron-left"></i>
                                    </a>
                                {else /}
                                    <span class="page-link disabled" aria-label="上一页">
                                        <i data-lucide="chevron-left"></i>
                                    </span>
                                {/if}
                                
                                {php}
                                    $currentPage = $products->currentPage();
                                    $lastPage = $products->lastPage();
                                    $startPage = max(1, $currentPage - 2);
                                    $endPage = min($lastPage, $currentPage + 2);
                                {/php}
                                
                                {for start="$startPage" end="$endPage"}
                                    {if condition="$i == $currentPage"}
                                        <span class="page-link active" aria-current="page">{$i}</span>
                                    {else /}
                                        <a class="page-link" href="{$products->url($i)}">{$i}</a>
                                    {/if}
                                {/for}
                                
                                {if condition="$products->currentPage() < $products->lastPage()"}
                                    <a class="page-link" href="{$products->nextPageUrl()}" aria-label="下一页">
                                        <i data-lucide="chevron-right"></i>
                                    </a>
                                {else /}
                                    <span class="page-link disabled" aria-label="下一页">
                                        <i data-lucide="chevron-right"></i>
                                    </span>
                                {/if}
                            </nav>
                        </div>
                    {/if}

                {else /}
                    <!-- 空状态 -->
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i data-lucide="search-x"></i>
                        </div>
                        <h3 class="empty-title">未找到相关产品</h3>
                        <p class="empty-description">
                            没有找到与 "<strong>{$keyword}</strong>" 相关的产品<br>
                            请尝试使用其他关键词或浏览全部产品
                        </p>
                        <a href="/products" class="empty-action">
                            <i data-lucide="grid-3x3"></i>
                            浏览全部产品
                        </a>
                    </div>
                {/if}
            </main>
        </div>
    </div>
</section>

<!-- JavaScript -->
<script>
// 确保页面加载时头部状态正确
document.addEventListener('DOMContentLoaded', function() {
    // 检查页面滚动位置并设置头部状态
    const header = document.querySelector('header');
    if (header && window.scrollY > 50) {
        header.classList.add('scrolled');
    }
    
    // 初始化Lucide图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});

// 监听滚动事件，确保头部样式正确
window.addEventListener('scroll', function() {
    const header = document.querySelector('header');
    if (header) {
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    }
});
</script>

{include file="common/footer"}

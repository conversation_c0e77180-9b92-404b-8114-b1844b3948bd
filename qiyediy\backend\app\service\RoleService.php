<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 角色服务
 */

declare(strict_types=1);

namespace app\service;

use app\model\Role;
use app\model\UserRole;
use think\db\exception\DataNotFoundException;
use think\facade\Db;

/**
 * 角色服务类
 */
class RoleService
{
    /**
     * 获取角色列表
     * @param array $params 查询参数
     * @return object
     */
    public function getList(array $params): object
    {
        $query = Role::withCount(['users']);

        // 搜索条件
        if (!empty($params['keyword'])) {
            $query->search($params['keyword']);
        }

        // 默认角色筛选
        if (isset($params['is_default']) && $params['is_default'] !== '') {
            $query->default((int)$params['is_default']);
        }

        // 排序
        $sortField = $params['sort_field'] ?? 'sort_order';
        $sortOrder = $params['sort_order'] ?? 'asc';
        $query->order($sortField, $sortOrder);

        return $query->paginate($params['per_page'] ?? 15);
    }

    /**
     * 根据ID获取角色
     * @param int $id 角色ID
     * @return Role|null
     */
    public function getById(int $id): ?Role
    {
        return Role::withCount(['users'])->find($id);
    }

    /**
     * 创建角色
     * @param array $data 角色数据
     * @return Role
     * @throws \Exception
     */
    public function create(array $data): Role
    {
        return Role::createRole($data);
    }

    /**
     * 更新角色
     * @param int $id 角色ID
     * @param array $data 更新数据
     * @return Role
     * @throws \Exception
     */
    public function update(int $id, array $data): Role
    {
        $role = Role::find($id);
        if (!$role) {
            throw new \Exception('角色不存在');
        }

        $role->updateRole($data);
        return $role->refresh();
    }

    /**
     * 删除角色
     * @param int $id 角色ID
     * @return bool
     * @throws \Exception
     */
    public function delete(int $id): bool
    {
        $role = Role::find($id);
        if (!$role) {
            throw new \Exception('角色不存在');
        }

        // 执行删除前检查
        $role->beforeDelete();

        $result = $role->delete();

        // 执行删除后清理
        if ($result) {
            $role->afterDelete();
        }

        return $result;
    }

    /**
     * 批量删除角色
     * @param array $ids 角色ID数组
     * @return int 删除数量
     * @throws \Exception
     */
    public function batchDelete(array $ids): int
    {
        // 检查是否有用户使用这些角色
        $userCount = UserRole::whereIn('role_id', $ids)->count();
        if ($userCount > 0) {
            throw new \Exception('部分角色下还有用户，无法删除');
        }

        // 检查是否包含默认角色
        $defaultCount = Role::whereIn('id', $ids)->where('is_default', 1)->count();
        if ($defaultCount > 0) {
            throw new \Exception('不能删除默认角色');
        }

        return Role::whereIn('id', $ids)->delete();
    }

    /**
     * 设置默认角色
     * @param int $id 角色ID
     * @return bool
     * @throws \Exception
     */
    public function setDefault(int $id): bool
    {
        $role = Role::find($id);
        if (!$role) {
            throw new \Exception('角色不存在');
        }

        return $role->setAsDefault();
    }

    /**
     * 同步权限
     * @param int $id 角色ID
     * @param array $permissions 权限列表
     * @return bool
     * @throws \Exception
     */
    public function syncPermissions(int $id, array $permissions): bool
    {
        $role = Role::find($id);
        if (!$role) {
            throw new \Exception('角色不存在');
        }

        return $role->syncPermissions($permissions);
    }

    /**
     * 获取所有角色
     * @return array
     */
    public function getAll(): array
    {
        return Role::order('sort_order', 'asc')
                  ->order('created_at', 'desc')
                  ->select()
                  ->toArray();
    }

    /**
     * 获取权限列表
     * @return array
     */
    public function getPermissions(): array
    {
        // 系统权限定义
        return [
            [
                'group' => '用户管理',
                'permissions' => [
                    ['key' => 'user.view', 'name' => '查看用户'],
                    ['key' => 'user.create', 'name' => '创建用户'],
                    ['key' => 'user.update', 'name' => '更新用户'],
                    ['key' => 'user.delete', 'name' => '删除用户'],
                    ['key' => 'user.reset_password', 'name' => '重置密码'],
                    ['key' => 'user.assign_roles', 'name' => '分配角色'],
                    ['key' => 'user.export', 'name' => '导出用户'],
                    ['key' => 'user.import', 'name' => '导入用户']
                ]
            ],
            [
                'group' => '角色管理',
                'permissions' => [
                    ['key' => 'role.view', 'name' => '查看角色'],
                    ['key' => 'role.create', 'name' => '创建角色'],
                    ['key' => 'role.update', 'name' => '更新角色'],
                    ['key' => 'role.delete', 'name' => '删除角色'],
                    ['key' => 'role.assign_permissions', 'name' => '分配权限']
                ]
            ],
            [
                'group' => 'DIY管理',
                'permissions' => [
                    ['key' => 'diy.view', 'name' => '查看页面'],
                    ['key' => 'diy.create', 'name' => '创建页面'],
                    ['key' => 'diy.update', 'name' => '编辑页面'],
                    ['key' => 'diy.delete', 'name' => '删除页面'],
                    ['key' => 'diy.publish', 'name' => '发布页面'],
                    ['key' => 'diy.component', 'name' => '组件管理'],
                    ['key' => 'diy.template', 'name' => '模板管理']
                ]
            ],
            [
                'group' => '内容管理',
                'permissions' => [
                    ['key' => 'content.view', 'name' => '查看内容'],
                    ['key' => 'content.create', 'name' => '创建内容'],
                    ['key' => 'content.update', 'name' => '更新内容'],
                    ['key' => 'content.delete', 'name' => '删除内容'],
                    ['key' => 'content.publish', 'name' => '发布内容'],
                    ['key' => 'content.category', 'name' => '分类管理'],
                    ['key' => 'content.tag', 'name' => '标签管理']
                ]
            ],
            [
                'group' => '媒体管理',
                'permissions' => [
                    ['key' => 'media.view', 'name' => '查看媒体'],
                    ['key' => 'media.upload', 'name' => '上传文件'],
                    ['key' => 'media.delete', 'name' => '删除文件'],
                    ['key' => 'media.manage', 'name' => '媒体管理']
                ]
            ],
            [
                'group' => '系统管理',
                'permissions' => [
                    ['key' => 'system.view', 'name' => '查看设置'],
                    ['key' => 'system.update', 'name' => '更新设置'],
                    ['key' => 'system.menu', 'name' => '菜单管理'],
                    ['key' => 'system.log', 'name' => '日志管理'],
                    ['key' => 'system.backup', 'name' => '备份管理'],
                    ['key' => 'system.cache', 'name' => '缓存管理']
                ]
            ]
        ];
    }

    /**
     * 获取角色统计
     * @return array
     */
    public function getStatistics(): array
    {
        $total = Role::count();
        $defaultRole = Role::where('is_default', 1)->find();
        
        // 角色用户分布
        $roleUserStats = Db::table('roles r')
            ->leftJoin('user_roles ur', 'r.id', '=', 'ur.role_id')
            ->field('r.name, COUNT(ur.user_id) as user_count')
            ->group('r.id, r.name')
            ->order('user_count', 'desc')
            ->select()
            ->toArray();

        // 权限分布统计
        $permissionStats = [];
        $roles = Role::select();
        foreach ($roles as $role) {
            $permissions = $role->permissions;
            $permissionStats[] = [
                'role_name' => $role->name,
                'permission_count' => count($permissions)
            ];
        }

        return [
            'total' => $total,
            'default_role' => $defaultRole ? $defaultRole->name : '无',
            'role_user_stats' => $roleUserStats,
            'permission_stats' => $permissionStats
        ];
    }
}

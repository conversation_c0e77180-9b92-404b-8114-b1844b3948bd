/* CKEditor 5 Classic深色主题样式 */
.ck-editor-container {
    border-radius: 10px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}

.ck-editor {
    border: 1px solid rgba(120, 119, 198, 0.3) !important;
    border-radius: 10px !important;
    box-shadow: none !important;
}

.ck-editor__editable {
    background: rgba(25, 25, 35, 0.9) !important;
    color: #ffffff !important;
    min-height: 300px !important;
    max-height: 500px !important;
    overflow-y: auto !important;
    padding: 20px !important;
    font-size: 14px !important;
    line-height: 1.6 !important;
    border: none !important;
}

/* 编辑器滚动条样式 */
.ck-editor__editable::-webkit-scrollbar {
    width: 8px !important;
}

.ck-editor__editable::-webkit-scrollbar-track {
    background: rgba(30, 30, 40, 0.5) !important;
    border-radius: 4px !important;
}

.ck-editor__editable::-webkit-scrollbar-thumb {
    background: rgba(120, 119, 198, 0.6) !important;
    border-radius: 4px !important;
    transition: background 0.3s ease !important;
}

.ck-editor__editable::-webkit-scrollbar-thumb:hover {
    background: rgba(120, 119, 198, 0.8) !important;
}

.ck-toolbar {
    background: linear-gradient(135deg, 
        rgba(120, 119, 198, 0.15) 0%, 
        rgba(255, 119, 198, 0.1) 50%, 
        rgba(120, 219, 255, 0.15) 100%) !important;
    border-bottom: 1px solid rgba(120, 119, 198, 0.3) !important;
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    padding: 12px 16px !important;
    flex-shrink: 0 !important;
    min-height: auto !important;
}

/* 分隔符隐藏样式已禁用，避免影响图片按钮 */

.ck-button {
    background: rgba(60, 60, 80, 0.6) !important;
    border: 1px solid rgba(120, 119, 198, 0.3) !important;
    border-radius: 6px !important;
    color: rgba(255, 255, 255, 0.8) !important;
    margin: 2px !important;
    transition: all 0.3s ease !important;
    outline: none !important;
    box-shadow: none !important;
}

.ck-button:hover {
    background: rgba(120, 119, 198, 0.4) !important;
    border-color: rgba(120, 119, 198, 0.6) !important;
    color: #ffffff !important;
    outline: none !important;
    box-shadow: none !important;
}

.ck-button:focus {
    background: rgba(120, 119, 198, 0.4) !important;
    border-color: rgba(120, 119, 198, 0.6) !important;
    color: #ffffff !important;
    outline: none !important;
    box-shadow: none !important;
}

.ck-button.ck-on {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.8), rgba(255, 119, 198, 0.6)) !important;
    border-color: rgba(120, 119, 198, 0.8) !important;
    color: #ffffff !important;
}

.ck-dropdown__panel {
    background: rgba(20, 20, 30, 0.95) !important;
    border: 1px solid rgba(120, 119, 198, 0.4) !important;
    border-radius: 8px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5) !important;
}

.ck-list__item {
    color: #ffffff !important;
}

.ck-list__item:hover {
    background: rgba(120, 119, 198, 0.3) !important;
}

/* 标题下拉菜单样式 */
.ck-dropdown__panel .ck-list {
    background: rgba(20, 20, 30, 0.95) !important;
}

.ck-dropdown__panel .ck-list__item {
    background: transparent !important;
    color: #ffffff !important;
    border: none !important;
    padding: 8px 12px !important;
}

.ck-dropdown__panel .ck-list__item:hover {
    background: rgba(120, 119, 198, 0.3) !important;
    color: #ffffff !important;
}

/* 颜色选择器样式 */
.ck-color-grid {
    background: rgba(20, 20, 30, 0.95) !important;
    padding: 8px !important;
}

.ck-color-grid__tile {
    border: 2px solid rgba(120, 119, 198, 0.3) !important;
    border-radius: 6px !important;
    width: 24px !important;
    height: 24px !important;
    margin: 2px !important;
    transition: all 0.2s ease !important;
}

.ck-color-grid__tile:hover {
    border-color: rgba(120, 119, 198, 0.8) !important;
    transform: scale(1.15) !important;
    box-shadow: 0 0 8px rgba(120, 119, 198, 0.4) !important;
}

.ck-color-grid__tile.ck-color-grid__tile_bordered {
    border: 2px solid rgba(255, 255, 255, 0.5) !important;
}

.ck-color-grid__tile.ck-color-grid__tile_bordered:hover {
    border-color: rgba(120, 119, 198, 0.8) !important;
}

/* 颜色选择器下拉面板 */
.ck-color-ui-dropdown .ck-dropdown__panel {
    background: rgba(20, 20, 30, 0.98) !important;
    border: 1px solid rgba(120, 119, 198, 0.4) !important;
    border-radius: 8px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.6) !important;
}

/* 移除颜色按钮 */
.ck-color-ui-dropdown .ck-button {
    background: rgba(60, 60, 80, 0.6) !important;
    border: 1px solid rgba(120, 119, 198, 0.3) !important;
    color: rgba(255, 255, 255, 0.9) !important;
}

.ck-color-ui-dropdown .ck-button:hover {
    background: rgba(120, 119, 198, 0.4) !important;
    border-color: rgba(120, 119, 198, 0.6) !important;
}

/* 强制显示颜色块 */
.ck-color-grid__tile[style*="background"] {
    opacity: 1 !important;
    visibility: visible !important;
}

/* 图片按钮样式强化 - 与其他按钮保持一致 */
.ck-button[data-upload-button="true"] {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: auto !important;
    height: auto !important;
    min-width: 50px !important;
    min-height: 28px !important;
    padding: 4px 6px !important;
    margin: 2px !important;
    background: rgba(60, 60, 80, 0.6) !important;
    border: 1px solid rgba(120, 119, 198, 0.3) !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    opacity: 1 !important;
    visibility: visible !important;
    font-size: 11px !important;
    color: rgba(255, 255, 255, 0.8) !important;
    transition: all 0.3s ease !important;
    outline: none !important;
    box-shadow: none !important;
    vertical-align: middle !important;
}

.ck-button[data-upload-button="true"]:hover {
    background: rgba(120, 119, 198, 0.4) !important;
    border-color: rgba(120, 119, 198, 0.6) !important;
    color: #ffffff !important;
    outline: none !important;
    box-shadow: none !important;
}

.ck-button[data-upload-button="true"]:focus {
    background: rgba(120, 119, 198, 0.4) !important;
    border-color: rgba(120, 119, 198, 0.6) !important;
    color: #ffffff !important;
    outline: none !important;
    box-shadow: none !important;
}

.ck-button[data-upload-button="true"]:active {
    background: rgba(120, 119, 198, 0.5) !important;
    border-color: rgba(120, 119, 198, 0.7) !important;
    color: #ffffff !important;
}

.ck-button[data-upload-button="true"] .ck-button__icon {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 14px !important;
    height: 14px !important;
    margin-right: 0 !important;
    opacity: 1 !important;
    visibility: visible !important;
    vertical-align: middle !important;
}

.ck-button[data-upload-button="true"] .ck-icon,
.ck-button[data-upload-button="true"] .fas,
.ck-button[data-upload-button="true"] i,
.ck-button[data-upload-button="true"] span {
    width: auto !important;
    height: auto !important;
    font-size: 12px !important;
    color: rgba(255, 255, 255, 0.8) !important;
    opacity: 1 !important;
    visibility: visible !important;
    vertical-align: middle !important;
    display: inline-block !important;
    line-height: 1 !important;
}

/* 悬停时所有图标变亮 */
.ck-button[data-upload-button="true"]:hover .ck-icon,
.ck-button[data-upload-button="true"]:hover .fas,
.ck-button[data-upload-button="true"]:hover i,
.ck-button[data-upload-button="true"]:hover span {
    color: #ffffff !important;
}

/* 强制显示FontAwesome图标 - 默认白色 */
.ck-button[data-upload-button="true"] .fas {
    color: rgba(255, 255, 255, 0.8) !important;
    opacity: 1 !important;
    visibility: visible !important;
    display: inline-block !important;
}

.ck-button[data-upload-button="true"] .fas::before {
    font-family: "Font Awesome 5 Free" !important;
    font-weight: 900 !important;
    content: "\f03e" !important; /* fa-image的Unicode */
    color: rgba(255, 255, 255, 0.8) !important;
    opacity: 1 !important;
    visibility: visible !important;
    display: inline-block !important;
}

/* 悬停时图标变亮 */
.ck-button[data-upload-button="true"]:hover .fas,
.ck-button[data-upload-button="true"]:hover .fas::before {
    color: #ffffff !important;
}

/* 备用图标方案 - 如果FontAwesome不可用 */
.ck-button[data-upload-button="true"] .ck-button__icon::before {
    content: "🖼️" !important;
    font-size: 12px !important;
    display: inline-block !important;
    opacity: 1 !important;
    visibility: visible !important;
    color: rgba(255, 255, 255, 0.8) !important;
}

/* 如果有子元素则隐藏伪元素 */
.ck-button[data-upload-button="true"] .ck-button__icon:not(:empty)::before {
    display: none !important;
}

.ck-button[data-upload-button="true"] .ck-button__label {
    display: inline-flex !important;
    align-items: center !important;
    font-size: 11px !important;
    font-weight: 400 !important;
    color: inherit !important;
    opacity: 1 !important;
    visibility: visible !important;
    line-height: 1 !important;
    vertical-align: middle !important;
}

/* 确保按钮在工具栏中正确显示 */
.ck-toolbar .ck-button[data-upload-button="true"] {
    flex-shrink: 0 !important;
    align-self: center !important;
}

/* 确保颜色块有背景色 */
.ck-color-grid__tile:not([style*="background"]) {
    background: #ccc !important;
}

/* 自定义图片上传按钮样式 - 与原生按钮保持一致 */
.ck-button[data-upload-button="true"] {
    /* 继承原生按钮的所有样式，不强制覆盖 */
}



/* 确保图片按钮的图标正确显示 */
.ck-button[data-upload-button="true"] .ck-button__icon {
    /* 继承原生图标容器样式 */
}

.ck-button[data-upload-button="true"] .ck-icon,
.ck-button[data-upload-button="true"] svg {
    /* 继承原生图标样式 */
    fill: currentColor !important;
}

/* 确保SVG路径元素正确显示 */
.ck-button[data-upload-button="true"] svg path {
    fill: inherit !important;
}

/* 确保图片按钮不被隐藏 */
.ck-toolbar .ck-button[data-upload-button="true"] {
    visibility: visible !important;
    opacity: 1 !important;
}

/* 强制保护图片按钮内的所有元素 */
.ck-button[data-upload-button="true"] * {
    visibility: visible !important;
    opacity: 1 !important;
    border-left: none !important;
    border-right: none !important;
}

/* 确保图标和文字完美对齐 */
.ck-button[data-upload-button="true"] svg path {
    fill: inherit !important;
    opacity: 1 !important;
}

/* 图片按钮内容居中对齐 */
.ck-button[data-upload-button="true"] {
    gap: 3px !important;
}

.ck-button[data-upload-button="true"] .ck-button__icon,
.ck-button[data-upload-button="true"] .ck-button__label {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.ck-editor__editable h1, .ck-editor__editable h2, .ck-editor__editable h3,
.ck-editor__editable h4, .ck-editor__editable h5, .ck-editor__editable h6 {
    color: #ffffff !important;
}

.ck-editor__editable p {
    color: #ffffff !important;
}

.ck-editor__editable a {
    color: rgba(120, 119, 198, 0.9) !important;
}

.ck-editor__editable blockquote {
    border-left: 4px solid rgba(120, 119, 198, 0.6) !important;
    background: rgba(120, 119, 198, 0.1) !important;
    color: rgba(255, 255, 255, 0.9) !important;
}

.ck-editor__editable code {
    background: rgba(120, 119, 198, 0.2) !important;
    color: rgba(120, 219, 255, 0.9) !important;
}

         .ck-editor__editable pre {
     background: rgba(20, 20, 30, 0.8) !important;
     color: rgba(120, 219, 255, 0.9) !important;
     border: 1px solid rgba(120, 119, 198, 0.3) !important;
 }
 
 /* 编辑器焦点样式 */
 .ck-editor-container.focused {
     border-color: rgba(120, 119, 198, 0.8) !important;
     box-shadow: 0 0 20px rgba(120, 119, 198, 0.3) !important;
 }
 
 /* 响应式设计 */
 @media (max-width: 768px) {
     .ck-editor-container {
         max-height: 400px !important;
     }
     
     .ck-toolbar {
         padding: 8px 12px !important;
         flex-wrap: wrap !important;
     }
     
     .ck-button {
         margin: 1px !important;
         width: 28px !important;
         height: 28px !important;
     }
     
     .ck-editor__editable {
         padding: 15px !important;
         min-height: 200px !important;
         max-height: 300px !important;
         font-size: 13px !important;
     }
 }
 
 @media (max-width: 480px) {
     .ck-editor-container {
         max-height: 350px !important;
     }
     
     .ck-editor__editable {
         min-height: 150px !important;
         max-height: 250px !important;
         padding: 12px !important;
     }
 }

 /* 新闻图片选择器样式 */
 .image-upload-section {
     margin-bottom: 15px;
 }

 .btn-upload-image {
     background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
     color: white;
     border: none;
     padding: 12px 20px;
     border-radius: 8px;
     cursor: pointer;
     font-size: 14px;
     font-weight: 500;
     display: inline-flex;
     align-items: center;
     gap: 8px;
     transition: all 0.3s ease;
     box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
 }

 .btn-upload-image:hover {
     transform: translateY(-2px);
     box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
 }

 .upload-help {
     margin-top: 8px;
     color: rgba(255, 255, 255, 0.6);
 }

 .selected-image-preview {
     width: 320px;
     margin-top: 20px;
     padding: 15px;
     background: rgba(255, 255, 255, 0.05);
     border-radius: 8px;
     border: 1px solid rgba(255, 255, 255, 0.1);
 }

 .image-preview-container {
     position: relative;
     display: inline-block;
 }

 .image-preview-container .preview-img {
     max-width: 288px;
     max-height: 200px;
     border-radius: 8px;
     box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
 }

 .image-actions {
     margin-top: 10px;
     display: flex;
     gap: 8px;
 }

 .btn-change-image,
 .btn-remove-image {
     padding: 6px 12px;
     border: none;
     border-radius: 6px;
     cursor: pointer;
     font-size: 12px;
     font-weight: 500;
     display: inline-flex;
     align-items: center;
     gap: 4px;
     transition: all 0.3s ease;
 }

 .btn-change-image {
     background: rgba(33, 150, 243, 0.8);
     color: white;
 }

 .btn-change-image:hover {
     background: rgba(33, 150, 243, 1);
     transform: translateY(-1px);
 }

 .btn-remove-image {
     background: rgba(244, 67, 54, 0.8);
     color: white;
 }

 .btn-remove-image:hover {
     background: rgba(244, 67, 54, 1);
     transform: translateY(-1px);
 }

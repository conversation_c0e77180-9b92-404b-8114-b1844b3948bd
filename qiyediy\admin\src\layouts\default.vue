<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 默认布局组件
-->

<template>
  <div class="layout-container" :class="layoutClasses">
    <!-- 侧边栏 -->
    <aside class="layout-sidebar" :class="sidebarClasses">
      <div class="sidebar-content">
        <!-- Logo区域 -->
        <div class="sidebar-logo" v-if="appStore.showLogo">
          <router-link to="/" class="logo-link">
            <img src="/logo.png" alt="QiyeDIY" class="logo-img">
            <span v-if="!appStore.sidebarCollapsed" class="logo-text">QiyeDIY</span>
          </router-link>
        </div>

        <!-- 导航菜单 -->
        <div class="sidebar-menu">
          <SidebarMenu />
        </div>
      </div>
    </aside>

    <!-- 主内容区域 -->
    <div class="layout-main">
      <!-- 顶部导航栏 -->
      <header class="layout-header" :class="headerClasses">
        <div class="header-left">
          <!-- 菜单折叠按钮 -->
          <el-button
            class="collapse-btn"
            :icon="appStore.sidebarCollapsed ? 'Expand' : 'Fold'"
            @click="appStore.toggleSidebar"
          />

          <!-- 面包屑导航 -->
          <Breadcrumb v-if="appStore.breadcrumbEnabled" />
        </div>

        <div class="header-right">
          <!-- 全屏按钮 -->
          <el-tooltip content="全屏" placement="bottom">
            <el-button class="header-btn" :icon="isFullscreen ? 'Aim' : 'FullScreen'" @click="toggleFullscreen" />
          </el-tooltip>

          <!-- 主题切换 -->
          <el-tooltip content="主题切换" placement="bottom">
            <el-button class="header-btn" :icon="appStore.theme === 'dark' ? 'Sunny' : 'Moon'" @click="appStore.toggleTheme" />
          </el-tooltip>

          <!-- 设置按钮 -->
          <el-tooltip content="系统设置" placement="bottom" v-if="appStore.showSettings">
            <el-button class="header-btn" icon="Setting" @click="showSettings = true" />
          </el-tooltip>

          <!-- 用户菜单 -->
          <UserDropdown />
        </div>
      </header>

      <!-- 标签页导航 -->
      <div class="layout-tabs" v-if="appStore.tagsViewEnabled">
        <TagsView />
      </div>

      <!-- 页面内容 -->
      <main class="layout-content">
        <div class="content-wrapper">
          <router-view v-slot="{ Component, route }">
            <transition name="fade-transform" mode="out-in">
              <keep-alive :include="cachedViews">
                <component :is="Component" :key="route.path" />
              </keep-alive>
            </transition>
          </router-view>
        </div>
      </main>

      <!-- 页脚 -->
      <footer class="layout-footer">
        <div class="footer-content">
          <span>© 2024 三只鱼网络科技 | QiyeDIY企业建站系统</span>
          <span>版本 v1.0.0</span>
        </div>
      </footer>
    </div>

    <!-- 设置面板 -->
    <SettingsPanel v-model="showSettings" />

    <!-- 全局加载 -->
    <div v-if="appStore.pageLoading" class="page-loading">
      <el-loading-spinner size="large" />
      <span class="loading-text">加载中...</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { useTagsViewStore } from '@/store/modules/tagsView'
import SidebarMenu from './components/SidebarMenu.vue'
import Breadcrumb from './components/Breadcrumb.vue'
import UserDropdown from './components/UserDropdown.vue'
import TagsView from './components/TagsView.vue'
import SettingsPanel from './components/SettingsPanel.vue'

const appStore = useAppStore()
const tagsViewStore = useTagsViewStore()

// 状态
const showSettings = ref(false)
const isFullscreen = ref(false)

// 计算属性
const layoutClasses = computed(() => ({
  'layout-collapsed': appStore.sidebarCollapsed,
  'layout-mobile': appStore.isMobile,
  'layout-fixed-header': appStore.fixedHeader,
  'layout-tags-view': appStore.tagsViewEnabled
}))

const sidebarClasses = computed(() => ({
  'sidebar-collapsed': appStore.sidebarCollapsed,
  'sidebar-mobile': appStore.isMobile
}))

const headerClasses = computed(() => ({
  'header-fixed': appStore.fixedHeader
}))

const cachedViews = computed(() => tagsViewStore.cachedViews)

/**
 * 切换全屏
 */
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

/**
 * 监听全屏变化
 */
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

/**
 * 监听窗口大小变化
 */
const handleResize = () => {
  appStore.updateDevice()
}

onMounted(() => {
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.layout-container {
  display: flex;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background-color: var(--el-bg-color);
}

// 侧边栏样式
.layout-sidebar {
  width: 260px;
  background-color: var(--el-bg-color-page);
  border-right: 1px solid var(--el-border-color-light);
  transition: all 0.3s ease;
  z-index: 1001;

  &.sidebar-collapsed {
    width: 64px;
  }

  &.sidebar-mobile {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1002;
    transform: translateX(-100%);

    &:not(.sidebar-collapsed) {
      transform: translateX(0);
    }
  }
}

.sidebar-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sidebar-logo {
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);

  .logo-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--el-text-color-primary);
    font-weight: 600;
    font-size: 18px;

    .logo-img {
      width: 32px;
      height: 32px;
      border-radius: 6px;
      margin-right: 12px;
    }

    .logo-text {
      transition: opacity 0.3s ease;
    }
  }
}

.sidebar-menu {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

// 主内容区域样式
.layout-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin-left: 0;
  transition: margin-left 0.3s ease;
}

.layout-header {
  height: 60px;
  background-color: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 1000;

  &.header-fixed {
    position: sticky;
    top: 0;
  }
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;

  .collapse-btn {
    border: none;
    background: transparent;
    color: var(--el-text-color-regular);

    &:hover {
      background-color: var(--el-fill-color-light);
    }
  }
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;

  .header-btn {
    border: none;
    background: transparent;
    color: var(--el-text-color-regular);

    &:hover {
      background-color: var(--el-fill-color-light);
    }
  }
}

.layout-tabs {
  height: 40px;
  background-color: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
}

.layout-content {
  flex: 1;
  overflow: hidden;
  background-color: var(--el-bg-color-page);
}

.content-wrapper {
  height: 100%;
  overflow: auto;
  padding: 20px;
}

.layout-footer {
  height: 50px;
  background-color: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color-light);
  display: flex;
  align-items: center;
  padding: 0 20px;

  .footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
}

// 页面加载样式
.page-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-text {
    margin-top: 16px;
    font-size: 14px;
    color: var(--el-text-color-regular);
  }
}

// 过渡动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s ease;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

// 响应式布局
@media (max-width: 768px) {
  .layout-container.layout-mobile {
    .layout-sidebar {
      &:not(.sidebar-collapsed) {
        box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
      }
    }

    .layout-main {
      margin-left: 0;
    }

    .header-left {
      gap: 8px;
    }

    .content-wrapper {
      padding: 16px;
    }
  }
}

// 暗色主题适配
:deep(.dark) {
  .layout-sidebar {
    background-color: var(--el-bg-color-page);
    border-right-color: var(--el-border-color);
  }

  .layout-header {
    background-color: var(--el-bg-color);
    border-bottom-color: var(--el-border-color);
  }

  .layout-footer {
    background-color: var(--el-bg-color);
    border-top-color: var(--el-border-color);
  }
}
</style>

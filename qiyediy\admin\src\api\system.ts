/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * QiyeDIY企业建站系统 - 系统管理API接口
 */

import { http } from '@/utils/http'
import type { 
  ApiResponse,
  PaginatedResponse
} from '@/types/auth'

/**
 * 系统设置接口
 */
export interface SystemSettings {
  basic?: {
    site_name: string
    site_title: string
    site_description: string
    site_keywords: string
    site_logo: string
    site_domain: string
    icp_number: string
    contact_email: string
    contact_phone: string
    site_status: number
    maintenance_message: string
  }
  upload?: {
    image_max_size: number
    video_max_size: number
    document_max_size: number
    allowed_image_types: string[]
    allowed_video_types: string[]
    image_quality: number
    auto_thumbnail: boolean
    thumbnail_small_width: number
    thumbnail_small_height: number
    thumbnail_medium_width: number
    thumbnail_medium_height: number
    thumbnail_large_width: number
    thumbnail_large_height: number
  }
  email?: {
    driver: string
    smtp_host: string
    smtp_port: number
    smtp_encryption: string
    smtp_username: string
    smtp_password: string
    from_email: string
    from_name: string
  }
  cache?: {
    driver: string
    redis_host: string
    redis_port: number
    redis_password: string
    prefix: string
    default_ttl: number
  }
  security?: {
    login_max_attempts: number
    login_lockout_time: number
    password_min_length: number
    password_rules: string[]
    session_timeout: number
    ip_whitelist: string
    enable_captcha: boolean
    enable_2fa: boolean
  }
}

/**
 * 系统信息接口
 */
export interface SystemInfo {
  server: {
    os: string
    php_version: string
    web_server: string
    database: string
    redis: string
  }
  application: {
    name: string
    version: string
    environment: string
    debug: boolean
    timezone: string
  }
  storage: {
    disk_total: number
    disk_used: number
    disk_free: number
    upload_total: number
    upload_count: number
  }
  performance: {
    memory_usage: number
    memory_limit: number
    cpu_usage: number
    load_average: number[]
  }
}

/**
 * 系统日志接口
 */
export interface SystemLog {
  id: number
  level: string
  message: string
  context: Record<string, any>
  user_id?: number
  ip_address: string
  user_agent: string
  created_at: string
}

/**
 * 系统管理相关API
 */
export const systemApi = {
  /**
   * 获取系统设置
   */
  getSettings: (): Promise<ApiResponse<SystemSettings>> => {
    return http.get('/system/settings')
  },

  /**
   * 更新系统设置
   */
  updateSettings: (data: SystemSettings): Promise<ApiResponse<void>> => {
    return http.put('/system/settings', data)
  },

  /**
   * 获取系统信息
   */
  getInfo: (): Promise<ApiResponse<SystemInfo>> => {
    return http.get('/system/info')
  },

  /**
   * 获取系统统计
   */
  getStatistics: (): Promise<ApiResponse<{
    users: {
      total: number
      today: number
      online: number
    }
    pages: {
      total: number
      published: number
      draft: number
    }
    uploads: {
      total_size: number
      total_count: number
      today_count: number
    }
    visits: {
      today: number
      yesterday: number
      this_month: number
    }
  }>> => {
    return http.get('/system/statistics')
  },

  /**
   * 测试邮件发送
   */
  testEmail: (data: {
    to: string
    subject: string
    content: string
  }): Promise<ApiResponse<void>> => {
    return http.post('/system/test-email', data)
  },

  /**
   * 清空缓存
   */
  clearCache: (): Promise<ApiResponse<void>> => {
    return http.post('/system/clear-cache')
  },

  /**
   * 获取系统日志
   */
  getLogs: (params: {
    page?: number
    per_page?: number
    level?: string
    keyword?: string
    start_date?: string
    end_date?: string
    user_id?: number
  }): Promise<PaginatedResponse<SystemLog>> => {
    return http.get('/system/logs', { params })
  },

  /**
   * 清空系统日志
   */
  clearLogs: (data: {
    level?: string
    before_date?: string
  }): Promise<ApiResponse<{ count: number }>> => {
    return http.delete('/system/logs', { data })
  },

  /**
   * 系统备份
   */
  backup: (data: {
    type: 'database' | 'files' | 'full'
    description?: string
  }): Promise<ApiResponse<{
    backup_id: string
    file_path: string
    size: number
  }>> => {
    return http.post('/system/backup', data)
  },

  /**
   * 获取备份列表
   */
  getBackups: (): Promise<ApiResponse<Array<{
    id: string
    type: string
    description: string
    file_path: string
    size: number
    created_at: string
  }>>> => {
    return http.get('/system/backups')
  },

  /**
   * 恢复备份
   */
  restore: (backupId: string): Promise<ApiResponse<void>> => {
    return http.post(`/system/restore/${backupId}`)
  },

  /**
   * 删除备份
   */
  deleteBackup: (backupId: string): Promise<ApiResponse<void>> => {
    return http.delete(`/system/backup/${backupId}`)
  },

  /**
   * 系统优化
   */
  optimize: (data: {
    clear_cache: boolean
    optimize_database: boolean
    clean_logs: boolean
    compress_images: boolean
  }): Promise<ApiResponse<{
    cache_cleared: boolean
    database_optimized: boolean
    logs_cleaned: number
    images_compressed: number
  }>> => {
    return http.post('/system/optimize', data)
  },

  /**
   * 检查系统更新
   */
  checkUpdate: (): Promise<ApiResponse<{
    has_update: boolean
    current_version: string
    latest_version: string
    update_info: {
      title: string
      description: string
      release_date: string
      download_url: string
    }
  }>> => {
    return http.get('/system/check-update')
  },

  /**
   * 执行系统更新
   */
  update: (): Promise<ApiResponse<{
    success: boolean
    message: string
    new_version: string
  }>> => {
    return http.post('/system/update')
  },

  /**
   * 获取系统配置
   */
  getConfig: (): Promise<ApiResponse<{
    app: Record<string, any>
    database: Record<string, any>
    cache: Record<string, any>
    upload: Record<string, any>
    email: Record<string, any>
  }>> => {
    return http.get('/system/config')
  },

  /**
   * 更新系统配置
   */
  updateConfig: (data: {
    section: string
    config: Record<string, any>
  }): Promise<ApiResponse<void>> => {
    return http.put('/system/config', data)
  },

  /**
   * 重启系统服务
   */
  restart: (service: 'web' | 'cache' | 'queue' | 'all'): Promise<ApiResponse<void>> => {
    return http.post('/system/restart', { service })
  },

  /**
   * 获取系统健康状态
   */
  getHealth: (): Promise<ApiResponse<{
    status: 'healthy' | 'warning' | 'error'
    checks: Array<{
      name: string
      status: 'pass' | 'fail' | 'warn'
      message: string
      details?: Record<string, any>
    }>
    uptime: number
    last_check: string
  }>> => {
    return http.get('/system/health')
  },

  /**
   * 获取系统监控数据
   */
  getMonitoring: (params: {
    metric: 'cpu' | 'memory' | 'disk' | 'network'
    period: '1h' | '6h' | '24h' | '7d' | '30d'
  }): Promise<ApiResponse<Array<{
    timestamp: string
    value: number
    unit: string
  }>>> => {
    return http.get('/system/monitoring', { params })
  },

  /**
   * 导出系统数据
   */
  export: (data: {
    type: 'users' | 'pages' | 'logs' | 'settings'
    format: 'json' | 'csv' | 'excel'
    filters?: Record<string, any>
  }): Promise<ApiResponse<{
    file_path: string
    download_url: string
  }>> => {
    return http.post('/system/export', data)
  },

  /**
   * 导入系统数据
   */
  import: (data: FormData): Promise<ApiResponse<{
    success_count: number
    error_count: number
    errors: string[]
  }>> => {
    return http.post('/system/import', data, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

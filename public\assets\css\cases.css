/* 案例页面样式 */

/* 页面基础样式 */
.cases-page {
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    min-height: 100vh;
    color: #ffffff;
}

/* 页面头部横幅 */
.page-hero {
    position: relative;
    padding: 20px 0;
    overflow: hidden;
    min-height: 40vh;
    display: flex;
    align-items: center;
}

/* 3D背景动画层 */
.hero-3d-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    perspective: 1000px;
}

.floating-elements {
    position: relative;
    width: 100%;
    height: 100%;
}

/* 3D立体案例卡片 */
.case-card-3d {
    position: absolute;
    width: 120px;
    height: 80px;
    transform-style: preserve-3d;
    animation: float3d 6s ease-in-out infinite;
}

.case-card-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.case-card-2 {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.case-card-3 {
    top: 30%;
    right: 25%;
    animation-delay: 4s;
}

.card-face {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    backface-visibility: hidden;
    transition: all 0.6s ease;
}

.card-front {
    background: linear-gradient(135deg, rgba(255, 107, 157, 0.9), rgba(120, 119, 198, 0.9));
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.card-back {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.9), rgba(255, 107, 157, 0.9));
    border: 1px solid rgba(255, 255, 255, 0.3);
    transform: rotateY(180deg);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.case-card-3d:hover {
    transform: rotateY(180deg) scale(1.1);
}

.card-icon {
    font-size: 24px;
    color: #ffffff;
    margin-bottom: 8px;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.card-title {
    font-size: 12px;
    color: #ffffff;
    font-weight: 600;
    text-align: center;
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}

.card-content {
    font-size: 14px;
    color: #ffffff;
    font-weight: 600;
    text-align: center;
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}

/* 3D几何图形 */
.geometric-shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 107, 157, 0.6), rgba(120, 119, 198, 0.6));
    animation: geometricFloat 8s ease-in-out infinite;
}

.shape-1 {
    top: 15%;
    left: 5%;
    width: 60px;
    height: 60px;
    animation-delay: 0s;
}

.shape-2 {
    top: 70%;
    left: 20%;
    width: 40px;
    height: 40px;
    animation-delay: 1s;
}

.shape-3 {
    top: 25%;
    right: 10%;
    width: 80px;
    height: 80px;
    animation-delay: 2s;
}

.shape-4 {
    bottom: 20%;
    right: 30%;
    width: 50px;
    height: 50px;
    animation-delay: 3s;
}

/* 粒子效果 */
.particle-system {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: particleFloat 12s ease-in-out infinite;
}

.particle-1 {
    top: 20%;
    left: 15%;
    animation-delay: 0s;
}

.particle-2 {
    top: 40%;
    left: 80%;
    animation-delay: 2s;
}

.particle-3 {
    top: 60%;
    left: 30%;
    animation-delay: 4s;
}

.particle-4 {
    top: 80%;
    left: 70%;
    animation-delay: 6s;
}

.particle-5 {
    top: 30%;
    left: 50%;
    animation-delay: 8s;
}

.particle-6 {
    top: 70%;
    left: 10%;
    animation-delay: 10s;
}

/* 动态网格背景 */
.grid-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.1;
}

.grid-line {
    position: absolute;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.grid-horizontal {
    top: 50%;
    left: 0;
    width: 100%;
    height: 1px;
    animation: gridPulse 4s ease-in-out infinite;
}

.grid-vertical {
    top: 0;
    left: 50%;
    width: 1px;
    height: 100%;
    animation: gridPulse 4s ease-in-out infinite 2s;
}

/* 光效层 */
.light-effects {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.light-beam {
    position: absolute;
    background: linear-gradient(45deg, transparent, rgba(255, 107, 157, 0.3), transparent);
    border-radius: 50px;
    animation: lightBeam 8s ease-in-out infinite;
}

.light-1 {
    top: 10%;
    left: 20%;
    width: 200px;
    height: 4px;
    animation-delay: 0s;
}

.light-2 {
    top: 60%;
    right: 20%;
    width: 150px;
    height: 4px;
    animation-delay: 2s;
}

.light-3 {
    bottom: 20%;
    left: 30%;
    width: 180px;
    height: 4px;
    animation-delay: 4s;
}

/* 动画关键帧 */
@keyframes float3d {
    0%, 100% {
        transform: translateY(0px) rotateX(0deg) rotateY(0deg);
    }
    50% {
        transform: translateY(-15px) rotateX(5deg) rotateY(5deg);
    }
}

@keyframes geometricFloat {
    0%, 100% {
        transform: translateY(0px) scale(1) rotate(0deg);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-20px) scale(1.1) rotate(180deg);
        opacity: 0.8;
    }
}

@keyframes particleFloat {
    0%, 100% {
        transform: translateY(0px) translateX(0px);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-30px) translateX(10px);
        opacity: 1;
    }
}

@keyframes gridPulse {
    0%, 100% {
        opacity: 0.1;
        transform: scaleX(1);
    }
    50% {
        opacity: 0.3;
        transform: scaleX(1.1);
    }
}

@keyframes lightBeam {
    0%, 100% {
        opacity: 0;
        transform: translateX(-50px);
    }
    50% {
        opacity: 0.6;
        transform: translateX(50px);
    }
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    padding: 1rem;
}

/* 专业徽章 */
.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    padding: 0.4rem 1.2rem;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.hero-badge:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.badge-icon {
    color: #ff6b9d;
    font-size: 1rem;
}

.badge-text {
    color: #ffffff;
    font-size: 0.9rem;
    font-weight: 500;
}

/* 主标题 */
.hero-title {
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.3rem;
}

.title-main {
    font-size: 3rem;
    font-weight: 800;
    background: linear-gradient(135deg, #ffffff, #e0e0e0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.02em;
}

.title-sub {
    font-size: 1.2rem;
    font-weight: 300;
    color: rgba(255, 255, 255, 0.7);
    letter-spacing: 0.2em;
    text-transform: uppercase;
}

/* 副标题 */
.hero-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    line-height: 1.6;
    text-shadow: 0 2px 15px rgba(0, 0, 0, 0.4);
    font-weight: 400;
}

.subtitle-highlight {
    color: rgba(255, 107, 157, 0.9);
    font-weight: 500;
}



/* 主要内容区域 */
.main-content {
    position: relative;
    z-index: 2;
}

/* 筛选区域 */
.filter-section {
    margin-bottom: 4rem;
}

.filter-container {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(120, 119, 198, 0.2);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(15px);
}

.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.filter-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #ffffff;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-title i {
    color: #7877c6;
}

.filter-count {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1rem;
}

.count-number {
    color: #7877c6;
    font-weight: 600;
}

.filter-tabs {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-tab {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(120, 119, 198, 0.2);
    border-radius: 12px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
}

.filter-tab:hover {
    background: rgba(120, 119, 198, 0.1);
    border-color: rgba(120, 119, 198, 0.4);
    color: #ffffff;
    transform: translateY(-2px);
}

.filter-tab.active {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.3), rgba(255, 119, 198, 0.2));
    border-color: rgba(120, 119, 198, 0.5);
    color: #ffffff;
    box-shadow: 0 5px 15px rgba(120, 119, 198, 0.3);
}

.filter-tab i {
    font-size: 0.8rem;
}

/* 案例网格 */
.cases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.case-item {
    transition: all 0.3s ease;
}

.case-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(120, 119, 198, 0.2);
    border-radius: 20px;
    overflow: hidden;
    backdrop-filter: blur(15px);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.case-card:hover {
    transform: translateY(-8px);
    border-color: rgba(120, 119, 198, 0.4);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(120, 119, 198, 0.2);
}

.case-image {
    position: relative;
    height: 220px;
    overflow: hidden;
}

.case-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.case-card:hover .case-image img {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, 
        rgba(120, 119, 198, 0.8), 
        rgba(255, 119, 198, 0.6));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.case-card:hover .image-overlay {
    opacity: 1;
}

.overlay-content {
    text-align: center;
    color: #ffffff;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.case-card:hover .overlay-content {
    transform: translateY(0);
}

.overlay-content i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

.overlay-content span {
    font-size: 1rem;
    font-weight: 500;
}

.featured-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #1a1a2e;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.3rem;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.case-content {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.case-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
}

.meta-item i {
    color: #7877c6;
    font-size: 0.7rem;
}

.case-title {
    margin-bottom: 1rem;
    flex: 1;
}

.case-title a {
    color: #ffffff;
    text-decoration: none;
    font-size: 1.3rem;
    font-weight: 600;
    line-height: 1.4;
    transition: all 0.3s ease;
}

.case-title a:hover {
    color: #7877c6;
}

.case-excerpt {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

.case-actions {
    margin-top: auto;
}

.view-more-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.3), rgba(255, 119, 198, 0.2));
    border: 1px solid rgba(120, 119, 198, 0.4);
    border-radius: 12px;
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.view-more-btn:hover {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.5), rgba(255, 119, 198, 0.3));
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(120, 119, 198, 0.3);
}

.view-more-btn i {
    transition: transform 0.3s ease;
}

.view-more-btn:hover i {
    transform: translateX(3px);
}

/* 空状态 */
.no-cases-found {
    text-align: center;
    padding: 4rem 2rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(120, 119, 198, 0.2);
    border-radius: 20px;
    backdrop-filter: blur(15px);
}

.no-cases-content {
    max-width: 400px;
    margin: 0 auto;
}

.no-cases-icon {
    font-size: 4rem;
    color: rgba(120, 119, 198, 0.5);
    margin-bottom: 1.5rem;
}

.no-cases-found h4 {
    font-size: 1.5rem;
    color: #ffffff;
    margin-bottom: 1rem;
}

.no-cases-found p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.btn-back-all {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.3), rgba(255, 119, 198, 0.2));
    border: 1px solid rgba(120, 119, 198, 0.4);
    border-radius: 12px;
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-back-all:hover {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.5), rgba(255, 119, 198, 0.3));
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(120, 119, 198, 0.3);
}

/* 分页样式 */
.pagination-wrapper {
    margin-top: 3rem;
    text-align: center;
}

.pagination-container {
    display: inline-block;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(120, 119, 198, 0.2);
    border-radius: 16px;
    padding: 1rem;
    backdrop-filter: blur(15px);
}

/* 精选案例区域 */
.featured-cases-section {
    margin-top: 5rem;
    padding: 3rem 0;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 30px;
    border: 1px solid rgba(120, 119, 198, 0.1);
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.section-title .title-icon {
    color: #ffd700;
    font-size: 2rem;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.section-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.7);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.featured-cases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.featured-case-item {
    transition: all 0.3s ease;
}

.featured-case-card {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 16px;
    overflow: hidden;
    backdrop-filter: blur(15px);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.featured-case-card:hover {
    transform: translateY(-5px);
    border-color: rgba(255, 215, 0, 0.4);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3), 0 0 25px rgba(255, 215, 0, 0.2);
}

.featured-case-image {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.featured-case-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.featured-case-card:hover .featured-case-image img {
    transform: scale(1.05);
}

.featured-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(255, 215, 0, 0.8),
        rgba(255, 237, 78, 0.6));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.featured-case-card:hover .featured-overlay {
    opacity: 1;
}

.featured-overlay-content {
    text-align: center;
    color: #1a1a2e;
    font-weight: 600;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.featured-case-card:hover .featured-overlay-content {
    transform: translateY(0);
}

.featured-overlay-content i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    display: block;
}

.featured-case-content {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.featured-case-title {
    margin-bottom: 1rem;
    flex: 1;
}

.featured-case-title a {
    color: #ffffff;
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 600;
    line-height: 1.4;
    transition: all 0.3s ease;
}

.featured-case-title a:hover {
    color: #ffd700;
}

.featured-case-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
}

.featured-case-meta .industry {
    background: rgba(255, 215, 0, 0.2);
    color: #ffd700;
    padding: 0.3rem 0.8rem;
    border-radius: 12px;
    font-weight: 500;
}

.featured-case-meta .client {
    color: rgba(255, 255, 255, 0.8);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-hero {
        min-height: 70vh;
        padding: 80px 0 40px;
    }

    .hero-content {
        padding: 0.5rem;
    }

    .title-main {
        font-size: 2.2rem;
    }

    .title-sub {
        font-size: 1rem;
    }

    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .hero-badge {
        padding: 0.3rem 1rem;
        margin-bottom: 1rem;
    }

    .badge-text {
        font-size: 0.8rem;
    }


    
    .cases-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .filter-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .filter-tabs {
        gap: 0.5rem;
    }
    
    .filter-tab {
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
    }

    .section-title {
        font-size: 2rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .section-title .title-icon {
        font-size: 1.5rem;
    }

    .featured-cases-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .featured-case-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

/* 3D统计数据展示 */
.hero-stats-3d {
    margin-bottom: 2rem;
}

.stats-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
    perspective: 1000px;
}

.stat-item-3d {
    position: relative;
    width: 140px;
    height: 100px;
    transform-style: preserve-3d;
    transition: all 0.6s ease;
}

.stat-item-3d:hover {
    transform: rotateX(-10deg) rotateY(10deg) scale(1.05);
}

.stat-cube {
    position: relative;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    animation: cubeFloat 8s ease-in-out infinite;
}

.stat-item-3d:nth-child(1) .stat-cube { animation-delay: 0s; }
.stat-item-3d:nth-child(2) .stat-cube { animation-delay: 1s; }
.stat-item-3d:nth-child(3) .stat-cube { animation-delay: 2s; }
.stat-item-3d:nth-child(4) .stat-cube { animation-delay: 3s; }

.cube-face {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    backface-visibility: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(15px);
}

.cube-front {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.15),
        rgba(255, 255, 255, 0.05));
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.cube-top {
    background: linear-gradient(135deg,
        rgba(255, 107, 157, 0.8),
        rgba(120, 119, 198, 0.8));
    transform: rotateX(90deg) translateZ(50px);
    box-shadow: 0 8px 32px rgba(255, 107, 157, 0.3);
}

/* 3D统计数据文字样式 */
.stat-number {
    font-size: 1.8rem;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 0.3rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    line-height: 1;
}

.stat-label {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.9);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
}

.stat-icon {
    font-size: 1.8rem;
    color: #ffffff;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

/* 行动按钮 */
.hero-actions {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.btn-primary-3d,
.btn-secondary-3d {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    transform-style: preserve-3d;
    position: relative;
    overflow: hidden;
}

.btn-primary-3d {
    background: linear-gradient(135deg, #ff6b9d, #7777c6);
    color: #ffffff;
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 25px rgba(255, 107, 157, 0.4);
}

.btn-primary-3d:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 35px rgba(255, 107, 157, 0.6);
    color: #ffffff;
    text-decoration: none;
}

.btn-secondary-3d {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn-secondary-3d:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 35px rgba(255, 255, 255, 0.3);
    color: #ffffff;
    text-decoration: none;
}

.btn-text {
    position: relative;
    z-index: 2;
}

.btn-icon {
    position: relative;
    z-index: 2;
    transition: transform 0.3s ease;
}

.btn-primary-3d:hover .btn-icon,
.btn-secondary-3d:hover .btn-icon {
    transform: translateX(3px);
}

/* 3D立方体浮动动画 */
@keyframes cubeFloat {
    0%, 100% {
        transform: translateY(0px) rotateX(0deg) rotateY(0deg);
    }
    50% {
        transform: translateY(-10px) rotateX(5deg) rotateY(5deg);
    }
}

/* 响应式设计 - 3D元素 */
@media (max-width: 768px) {
    .case-card-3d {
        width: 80px;
        height: 60px;
    }

    .card-icon {
        font-size: 16px;
    }

    .card-title {
        font-size: 10px;
    }

    .geometric-shape {
        width: 30px !important;
        height: 30px !important;
    }

    .stats-container {
        gap: 1rem;
    }

    .stat-item-3d {
        width: 120px;
        height: 90px;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .stat-label {
        font-size: 0.7rem;
    }

    .stat-icon {
        font-size: 1.5rem;
    }

    .hero-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .btn-primary-3d,
    .btn-secondary-3d {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }

    .title-main {
        font-size: 2.5rem;
    }

    .title-sub {
        font-size: 1rem;
    }
}

<svg width="1200" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="chartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:0.9" />
    </linearGradient>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="url(#bgGradient)"/>

  <!-- 装饰性圆形 -->
  <circle cx="150" cy="120" r="40" fill="rgba(255,255,255,0.1)" opacity="0.6"/>
  <circle cx="1050" cy="150" r="60" fill="rgba(255,255,255,0.08)" opacity="0.5"/>
  <circle cx="950" cy="450" r="35" fill="rgba(255,255,255,0.12)" opacity="0.4"/>

  <!-- 3D图表元素 -->
  <g transform="translate(700, 200)">
    <!-- 图表背景板 -->
    <path d="M0 0 L200 0 L220 -20 L20 -20 Z" fill="url(#chartGradient)" opacity="0.9"/>
    <path d="M0 0 L0 150 L20 130 L20 -20 Z" fill="rgba(251,191,36,0.7)"/>
    <path d="M0 150 L200 150 L220 130 L20 130 Z" fill="rgba(251,191,36,0.6)"/>

    <!-- 图表内容 -->
    <rect x="10" y="10" width="180" height="130" fill="rgba(255,255,255,0.1)" rx="8"/>

    <!-- 折线图 -->
    <polyline points="30,120 60,100 90,80 120,60 150,40 180,30"
              stroke="rgba(255,255,255,0.8)" stroke-width="3" fill="none"/>

    <!-- 数据点 -->
    <circle cx="30" cy="120" r="4" fill="rgba(255,255,255,0.9)"/>
    <circle cx="60" cy="100" r="4" fill="rgba(255,255,255,0.9)"/>
    <circle cx="90" cy="80" r="4" fill="rgba(255,255,255,0.9)"/>
    <circle cx="120" cy="60" r="4" fill="rgba(255,255,255,0.9)"/>
    <circle cx="150" cy="40" r="4" fill="rgba(255,255,255,0.9)"/>
    <circle cx="180" cy="30" r="4" fill="rgba(255,255,255,0.9)"/>
  </g>

  <!-- 人物剪影 -->
  <g transform="translate(850, 400)">
    <!-- 人物身体 -->
    <ellipse cx="0" cy="40" rx="15" ry="25" fill="rgba(255,255,255,0.3)"/>
    <!-- 人物头部 -->
    <circle cx="0" cy="10" r="8" fill="rgba(255,255,255,0.3)"/>
    <!-- 笔记本电脑 -->
    <rect x="-20" y="35" width="25" height="15" fill="rgba(251,191,36,0.6)" rx="2"/>
    <rect x="-18" y="37" width="21" height="11" fill="rgba(255,255,255,0.2)" rx="1"/>
  </g>

  <!-- 立体盒子 -->
  <g transform="translate(950, 480)">
    <!-- 盒子顶面 -->
    <path d="M0 0 L30 0 L40 -10 L10 -10 Z" fill="rgba(251,191,36,0.8)"/>
    <!-- 盒子左面 -->
    <path d="M0 0 L0 30 L10 20 L10 -10 Z" fill="rgba(251,191,36,0.6)"/>
    <!-- 盒子右面 -->
    <path d="M30 0 L30 30 L40 20 L40 -10 Z" fill="rgba(251,191,36,0.7)"/>
    <!-- 盒子装饰 -->
    <circle cx="20" cy="15" r="8" fill="rgba(255,255,255,0.3)"/>
    <circle cx="20" cy="15" r="4" fill="rgba(255,255,255,0.5)"/>
  </g>

  <!-- 文档/卡片元素 -->
  <g transform="translate(750, 100)">
    <!-- 卡片1 -->
    <rect x="0" y="0" width="80" height="60" fill="rgba(255,255,255,0.15)" rx="8" transform="rotate(-5)"/>
    <rect x="5" y="5" width="70" height="50" fill="rgba(255,255,255,0.1)" rx="4" transform="rotate(-5)"/>

    <!-- 卡片2 -->
    <rect x="60" y="20" width="80" height="60" fill="rgba(255,255,255,0.12)" rx="8" transform="rotate(10)"/>
    <rect x="65" y="25" width="70" height="50" fill="rgba(255,255,255,0.08)" rx="4" transform="rotate(10)"/>
  </g>

  <!-- 标题区域 -->
  <g transform="translate(300, 250)">
    <!-- 图标背景 -->
    <rect x="0" y="0" width="60" height="40" fill="rgba(255,107,157,0.8)" rx="8"/>
    <rect x="5" y="5" width="50" height="30" fill="rgba(255,107,157,0.6)" rx="4"/>

    <!-- 简化的公文包图标 -->
    <rect x="15" y="12" width="30" height="16" fill="rgba(255,255,255,0.9)" rx="2"/>
    <rect x="25" y="8" width="10" height="8" fill="rgba(255,255,255,0.9)" rx="1"/>
  </g>

  <!-- 副标题装饰线 -->
  <line x1="200" y1="320" x2="400" y2="320" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
  <line x1="600" y1="320" x2="800" y2="320" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
</svg>

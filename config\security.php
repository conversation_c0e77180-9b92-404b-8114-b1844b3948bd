<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * 安全配置文件 - ThinkPHP6企业级应用
 * 功能：CSRF保护、XSS防护、安全头设置
 */

return [
    // CSRF保护配置
    'csrf' => [
        // 是否启用CSRF保护
        'enable' => true,
        // CSRF令牌名称
        'token_name' => '__token__',
        // CSRF令牌生成器
        'token_generator' => function() {
            return md5(uniqid(mt_rand(), true));
        },
        // 需要CSRF保护的请求方法
        'methods' => ['POST', 'PUT', 'DELETE', 'PATCH'],
        // 排除的路由（不需要CSRF保护）
        'except' => [
            'api/*',  // API接口
        ],
    ],
    
    // XSS防护配置
    'xss' => [
        // 是否启用XSS过滤
        'enable' => true,
        // 允许的HTML标签
        'allowed_tags' => '<p><br><strong><em><u><h1><h2><h3><h4><h5><h6><ul><ol><li><a><img>',
        // 允许的属性
        'allowed_attributes' => [
            'a' => ['href', 'title', 'target'],
            'img' => ['src', 'alt', 'title', 'width', 'height'],
        ],
    ],
    
    // 安全头配置
    'headers' => [
        // 是否启用安全头
        'enable' => true,
        // 安全头列表
        'list' => [
            'X-Content-Type-Options' => 'nosniff',
            'X-Frame-Options' => 'SAMEORIGIN',
            'X-XSS-Protection' => '1; mode=block',
            'Referrer-Policy' => 'strict-origin-when-cross-origin',
            'Content-Security-Policy' => "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;",
        ],
    ],
    
    // 文件上传安全配置
    'upload' => [
        // 是否启用文件上传安全检查
        'enable_security_check' => true,
        // 允许的文件扩展名
        'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'doc', 'docx', 'xls', 'xlsx'],
        // 允许的MIME类型
        'allowed_mimes' => [
            'image/jpeg', 'image/png', 'image/gif', 'image/webp',
            'application/pdf',
            'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ],
        // 最大文件大小（字节）
        'max_size' => 5 * 1024 * 1024, // 5MB
        // 禁止的文件扩展名
        'forbidden_extensions' => ['php', 'php3', 'php4', 'php5', 'phtml', 'pht', 'phps', 'asp', 'aspx', 'jsp', 'js', 'exe', 'bat', 'cmd', 'sh'],
        // 是否启用文件魔数检查
        'check_magic_number' => true,
        // 是否启用文件内容安全检查
        'check_file_content' => true,
        // 是否启用图片特殊检查
        'check_image_integrity' => true,
        // 严格模式（更严格的安全检查）
        'strict_mode' => true,
        // 安全存储配置
        'secure_storage' => [
            'base_dir' => 'uploads',
            'auto_classify' => true, // 自动按类型分类存储
            'date_structure' => true, // 按日期创建目录结构
            'safe_filename' => true, // 生成安全的文件名
        ],
    ],
    
    // 密码安全配置
    'password' => [
        // 最小长度
        'min_length' => 8,
        // 最大长度
        'max_length' => 32,
        // 是否需要包含数字
        'require_number' => true,
        // 是否需要包含字母
        'require_letter' => true,
        // 是否需要包含特殊字符
        'require_special' => false,
        // 密码加密算法
        'hash_algo' => 'bcrypt',
        // 密码加密成本
        'hash_cost' => 12,
    ],
    
    // 会话安全配置
    'session' => [
        // 会话超时时间（秒）
        'timeout' => 7200, // 2小时
        // 是否启用HttpOnly
        'httponly' => true,
        // 是否启用Secure（HTTPS环境）
        'secure' => false,
        // SameSite设置
        'samesite' => 'Lax',
        // 会话ID重新生成间隔（秒）
        'regenerate_interval' => 300, // 5分钟
    ],
    
    // IP访问控制
    'ip_control' => [
        // 是否启用IP控制
        'enable' => false,
        // 白名单IP
        'whitelist' => [],
        // 黑名单IP
        'blacklist' => [],
        // 管理后台IP白名单
        'admin_whitelist' => [],
    ],
    
    // 请求频率限制
    'rate_limit' => [
        // 是否启用频率限制
        'enable' => true,
        // 默认限制规则（每分钟请求数）
        'default' => 60,
        // 特定路由的限制规则
        'routes' => [
            'login' => 5,      // 登录接口每分钟5次
            'register' => 3,   // 注册接口每分钟3次
            'contact' => 10,   // 联系表单每分钟10次
        ],
    ],

    // SQL注入防护配置
    'sql_injection' => [
        // 是否启用SQL注入防护
        'enable' => true,
        // 严格模式（发现关键词就拦截）
        'strict_mode' => false,
        // 检查请求头
        'check_headers' => true,
        // 检查Cookie
        'check_cookies' => true,
        // 检查URL路径
        'check_path' => true,
        // 排除的路由（不进行SQL注入检查）
        'except' => [
            'api/debug/*',  // 调试接口
        ],
        // 检测到SQL注入时的处理方式：block(阻止), redirect(重定向), clean(清理)
        'action' => 'block',
        // 重定向URL（当action为redirect时使用）
        'redirect_url' => '/',
        // 自定义危险关键词
        'custom_keywords' => [],
        // 白名单参数（这些参数不进行SQL注入检查）
        'whitelist_params' => [
            'search', 'keyword', 'q', // 搜索参数可能包含特殊字符
        ],
    ],

    // 数据库安全配置
    'database' => [
        // 是否启用查询日志
        'enable_query_log' => false,
        // 是否记录慢查询
        'log_slow_queries' => true,
        // 慢查询阈值（毫秒）
        'slow_query_threshold' => 1000,
        // 是否启用预处理语句
        'use_prepared_statements' => true,
        // 是否转义特殊字符
        'escape_special_chars' => true,
    ],

    // 输入验证配置
    'input_validation' => [
        // 是否启用全局输入验证
        'enable' => true,
        // 最大输入长度
        'max_input_length' => 10000,
        // 是否过滤HTML标签
        'filter_html_tags' => true,
        // 是否过滤特殊字符
        'filter_special_chars' => false,
        // 允许的特殊字符
        'allowed_special_chars' => ['-', '_', '.', '@', '#'],
    ],
];

<?php
namespace app\model;

use think\Model;

/**
 * 新闻分类模型
 */
class NewsCategory extends Model
{
    // 数据表名
    protected $name = 'news_categories';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    // 字段类型转换
    protected $type = [
        'status' => 'integer',
        'sort_order' => 'integer',
    ];
    
    // 关联新闻
    public function news()
    {
        return $this->hasMany(News::class, 'category_id');
    }
    
    // 获取启用的分类
    public static function getActiveCategories()
    {
        return self::where('status', 1)
            ->order('sort_order', 'asc')
            ->order('id', 'asc')
            ->select();
    }
    
    // 检查slug唯一性
    public static function checkSlugUnique($slug, $excludeId = null)
    {
        $query = self::where('slug', $slug);
        if ($excludeId) {
            $query->where('id', '<>', $excludeId);
        }
        return !$query->find();
    }
    
    // 获取分类下的新闻数量
    public function getNewsCountAttr()
    {
        return $this->news()->count();
    }
}

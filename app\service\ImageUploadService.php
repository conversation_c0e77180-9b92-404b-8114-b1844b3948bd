<?php

namespace app\service;

use app\model\Image;
use app\model\ImageGroup;
use think\facade\Filesystem;
use think\exception\ValidateException;

/**
 * 图片上传服务
 */
class ImageUploadService
{
    /**
     * 上传单个图片
     * 
     * @param \think\file\UploadedFile $file 上传的文件
     * @param array $options 上传选项
     * @return array
     * @throws \Exception
     */
    public static function uploadSingle($file, $options = [])
    {
        try {
            // 优化：使用空合并运算符避免array_merge
            $options = [
                'group_id' => $options['group_id'] ?? 1,
                'group_slug' => $options['group_slug'] ?? 'default',
                'alt_text' => $options['alt_text'] ?? '',
                'tags' => $options['tags'] ?? [],
                'validate' => $options['validate'] ?? true,
                'save_to_db' => $options['save_to_db'] ?? true,
            ];
            
            // 验证文件
            if ($options['validate']) {
                $errors = Image::validateImageFile($file);
                if (!empty($errors)) {
                    throw new ValidateException(implode(', ', $errors));
                }
            }
            
            // 获取分组信息
            $group = null;
            if ($options['group_id']) {
                $group = ImageGroup::find($options['group_id']);
                if ($group) {
                    $options['group_slug'] = $group->slug;
                }
            }
            
            // 生成文件信息
            $originalName = $file->getOriginalName();
            $extension = $file->extension();
            $mimeType = $file->getMime();
            $fileSize = $file->getSize();
            
            // 生成安全的文件名
            $storedName = Image::generateSafeName($originalName, $extension);
            
            // 获取存储路径
            $storagePath = Image::getStoragePath($options['group_slug']);
            
            // 确保目录存在
            $fullPath = public_path() . '/' . $storagePath;
            if (!is_dir($fullPath)) {
                mkdir($fullPath, 0755, true);
            }
            
            // 移动文件
            $filePath = $storagePath . '/' . $storedName;
            $fullFilePath = public_path() . '/' . $filePath;
            
            if (!$file->move($fullPath, $storedName)) {
                throw new \Exception('文件保存失败');
            }
            
            // 获取图片尺寸
            $imageInfo = getimagesize($fullFilePath);
            $width = $imageInfo[0] ?? null;
            $height = $imageInfo[1] ?? null;
            
            // 生成访问URL
            $fileUrl = '/' . $filePath;
            
            // 准备数据库数据
            $imageData = [
                'filename' => $originalName,
                'stored_name' => $storedName,
                'file_path' => '/' . $filePath,
                'file_url' => $fileUrl,
                'file_size' => $fileSize,
                'mime_type' => $mimeType,
                'extension' => strtolower($extension),
                'width' => $width,
                'height' => $height,
                'group_id' => $options['group_id'],
                'alt_text' => $options['alt_text'],
                'tags' => $options['tags'],
                'upload_ip' => request()->ip(),
                'user_agent' => request()->header('User-Agent'),
                'status' => true,
            ];
            
            // 保存到数据库
            $image = null;
            if ($options['save_to_db']) {
                $image = Image::create($imageData);
                if (!$image) {
                    // 如果数据库保存失败，删除已上传的文件
                    unlink($fullFilePath);
                    throw new \Exception('数据库保存失败');
                }
            }
            
            return [
                'success' => true,
                'message' => '上传成功',
                'data' => [
                    'id' => $image ? $image->id : null,
                    'filename' => $originalName,
                    'stored_name' => $storedName,
                    'file_path' => $filePath,
                    'file_url' => $fileUrl,
                    'full_url' => request()->domain() . $fileUrl,
                    'file_size' => $fileSize,
                    'file_size_text' => self::formatFileSize($fileSize),
                    'mime_type' => $mimeType,
                    'extension' => $extension,
                    'width' => $width,
                    'height' => $height,
                    'dimensions_text' => $width && $height ? "{$width} × {$height}" : '未知',
                    'group_id' => $options['group_id'],
                    'group_name' => $group ? $group->name : '默认分组',
                    'alt_text' => $options['alt_text'],
                    'tags' => $options['tags'],
                    'created_at' => date('Y-m-d H:i:s'),
                ]
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 批量上传图片
     * 
     * @param array $files 文件数组
     * @param array $options 上传选项
     * @return array
     */
    public static function uploadMultiple($files, $options = [])
    {
        $results = [];
        $successCount = 0;
        $failCount = 0;
        
        foreach ($files as $index => $file) {
            $result = self::uploadSingle($file, $options);
            $results[] = $result;
            
            if ($result['success']) {
                $successCount++;
            } else {
                $failCount++;
            }
        }
        
        return [
            'success' => $failCount === 0,
            'message' => "上传完成：成功 {$successCount} 个，失败 {$failCount} 个",
            'data' => [
                'total' => count($files),
                'success_count' => $successCount,
                'fail_count' => $failCount,
                'results' => $results
            ]
        ];
    }
    
    /**
     * 删除图片
     * 
     * @param int $imageId 图片ID
     * @return array
     */
    public static function deleteImage($imageId)
    {
        try {
            $image = Image::find($imageId);
            if (!$image) {
                throw new \Exception('图片不存在');
            }
            
            // 删除文件
            $deleted = $image->deleteFile();
            
            // 删除数据库记录
            $image->delete();
            
            return [
                'success' => true,
                'message' => '删除成功',
                'data' => [
                    'id' => $imageId,
                    'file_deleted' => $deleted
                ]
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 批量删除图片
     * 
     * @param array $imageIds 图片ID数组
     * @return array
     */
    public static function deleteMultiple($imageIds)
    {
        $results = [];
        $successCount = 0;
        $failCount = 0;
        
        foreach ($imageIds as $imageId) {
            $result = self::deleteImage($imageId);
            $results[] = $result;
            
            if ($result['success']) {
                $successCount++;
            } else {
                $failCount++;
            }
        }
        
        return [
            'success' => $failCount === 0,
            'message' => "删除完成：成功 {$successCount} 个，失败 {$failCount} 个",
            'data' => [
                'total' => count($imageIds),
                'success_count' => $successCount,
                'fail_count' => $failCount,
                'results' => $results
            ]
        ];
    }
    
    /**
     * 移动图片到其他分组
     * 
     * @param int $imageId 图片ID
     * @param int $groupId 目标分组ID
     * @return array
     */
    public static function moveToGroup($imageId, $groupId)
    {
        try {
            $image = Image::find($imageId);
            if (!$image) {
                throw new \Exception('图片不存在');
            }
            
            $group = ImageGroup::find($groupId);
            if (!$group) {
                throw new \Exception('目标分组不存在');
            }
            
            $oldGroupId = $image->group_id;
            $image->group_id = $groupId;
            $image->save();
            
            return [
                'success' => true,
                'message' => '移动成功',
                'data' => [
                    'image_id' => $imageId,
                    'old_group_id' => $oldGroupId,
                    'new_group_id' => $groupId,
                    'group_name' => $group->name
                ]
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 格式化文件大小
     * 
     * @param int $bytes 字节数
     * @return string
     */
    private static function formatFileSize($bytes)
    {
        if ($bytes === 0) return '0 B';
        
        $k = 1024;
        $sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        $i = floor(log($bytes) / log($k));
        
        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }
    
    /**
     * 清理无效的图片记录
     * 
     * @return array
     */
    public static function cleanInvalidRecords()
    {
        $images = Image::select();
        $cleanedCount = 0;
        
        foreach ($images as $image) {
            $filePath = public_path() . $image->file_path;
            if (!file_exists($filePath)) {
                $image->delete();
                $cleanedCount++;
            }
        }
        
        return [
            'success' => true,
            'message' => "清理完成，删除了 {$cleanedCount} 条无效记录",
            'data' => [
                'cleaned_count' => $cleanedCount
            ]
        ];
    }
    
    /**
     * 获取图片统计信息
     * 
     * @return array
     */
    public static function getStatistics()
    {
        $totalImages = Image::count();
        $totalSize = Image::sum('file_size');
        $groupStats = Image::field('group_id, count(*) as count, sum(file_size) as size')
            ->group('group_id')
            ->select();
            
        $groups = ImageGroup::getEnabledGroups();
        $groupData = [];
        
        foreach ($groups as $group) {
            $stats = $groupStats->where('group_id', $group->id)->first();
            $groupData[] = [
                'id' => $group->id,
                'name' => $group->name,
                'slug' => $group->slug,
                'count' => $stats ? $stats['count'] : 0,
                'size' => $stats ? $stats['size'] : 0,
                'size_text' => $stats ? self::formatFileSize($stats['size']) : '0 B'
            ];
        }
        
        return [
            'total_images' => $totalImages,
            'total_size' => $totalSize,
            'total_size_text' => self::formatFileSize($totalSize),
            'groups' => $groupData
        ];
    }
} 
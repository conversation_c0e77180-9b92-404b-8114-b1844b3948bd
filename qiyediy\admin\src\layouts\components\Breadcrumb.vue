<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 面包屑导航组件
-->

<template>
  <div class="breadcrumb-container">
    <el-breadcrumb separator="/">
      <transition-group name="breadcrumb" tag="div" class="breadcrumb-list">
        <el-breadcrumb-item
          v-for="(item, index) in breadcrumbList"
          :key="item.path"
          :to="index === breadcrumbList.length - 1 ? undefined : { path: item.path }"
          class="breadcrumb-item"
        >
          <el-icon v-if="item.icon && index === 0" class="breadcrumb-icon">
            <component :is="item.icon" />
          </el-icon>
          <span class="breadcrumb-title">{{ item.title }}</span>
        </el-breadcrumb-item>
      </transition-group>
    </el-breadcrumb>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useMenuStore } from '@/store/modules/menu'

const route = useRoute()
const menuStore = useMenuStore()

// 计算面包屑列表
const breadcrumbList = computed(() => {
  // 获取当前路由的面包屑路径
  let breadcrumbs = menuStore.getBreadcrumbPath(route.path)
  
  // 如果没有找到，尝试使用路由的 meta 信息
  if (breadcrumbs.length === 0 && route.meta?.breadcrumb) {
    breadcrumbs = route.meta.breadcrumb as any[]
  }
  
  // 如果还是没有，使用路由路径生成
  if (breadcrumbs.length === 0) {
    const pathSegments = route.path.split('/').filter(Boolean)
    breadcrumbs = pathSegments.map((segment, index) => {
      const path = '/' + pathSegments.slice(0, index + 1).join('/')
      const menu = menuStore.findMenuByPath(path)
      
      return {
        path,
        title: menu?.title || segment,
        icon: index === 0 ? menu?.icon : undefined
      }
    })
  }
  
  // 确保首页在第一位
  if (breadcrumbs.length > 0 && breadcrumbs[0].path !== '/dashboard') {
    breadcrumbs.unshift({
      path: '/dashboard',
      title: '首页',
      icon: 'HomeFilled'
    })
  }
  
  return breadcrumbs
})

// 监听路由变化，可以在这里添加一些逻辑
watch(
  () => route.path,
  () => {
    // 可以在这里添加路由变化的处理逻辑
    // 比如记录访问历史、更新页面标题等
    if (route.meta?.title) {
      document.title = `${route.meta.title} - QiyeDIY企业建站系统`
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.breadcrumb-container {
  display: flex;
  align-items: center;
  height: 100%;
}

.breadcrumb-list {
  display: flex;
  align-items: center;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  
  :deep(.el-breadcrumb__inner) {
    display: flex;
    align-items: center;
    color: var(--el-text-color-regular);
    font-weight: 400;
    transition: color 0.3s ease;
    
    &:hover {
      color: var(--el-color-primary);
    }
    
    &.is-link {
      cursor: pointer;
    }
  }
  
  &:last-child {
    :deep(.el-breadcrumb__inner) {
      color: var(--el-text-color-primary);
      font-weight: 500;
      cursor: default;
      
      &:hover {
        color: var(--el-text-color-primary);
      }
    }
  }
}

.breadcrumb-icon {
  margin-right: 4px;
  font-size: 14px;
}

.breadcrumb-title {
  font-size: 14px;
}

// 面包屑动画
.breadcrumb-enter-active {
  transition: all 0.3s ease;
}

.breadcrumb-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-leave-active {
  transition: all 0.3s ease;
}

.breadcrumb-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

// Element Plus 面包屑样式覆盖
:deep(.el-breadcrumb) {
  font-size: 14px;
  line-height: 1;
  
  .el-breadcrumb__separator {
    margin: 0 8px;
    color: var(--el-text-color-placeholder);
    font-weight: 400;
  }
  
  .el-breadcrumb__item {
    &:last-child {
      .el-breadcrumb__separator {
        display: none;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .breadcrumb-container {
    display: none;
  }
}

// 暗色主题适配
:deep(.dark) {
  .el-breadcrumb__inner {
    &:hover {
      color: var(--el-color-primary-light-3);
    }
  }
  
  .breadcrumb-item:last-child {
    .el-breadcrumb__inner {
      &:hover {
        color: var(--el-text-color-primary);
      }
    }
  }
}
</style>

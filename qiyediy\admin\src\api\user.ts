/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 用户管理API接口
 */

import { http } from '@/utils/http'
import type { 
  User, 
  UserStatistics,
  ApiResponse,
  PaginatedResponse
} from '@/types/auth'

/**
 * 用户管理相关API
 */
export const userApi = {
  /**
   * 获取用户列表
   */
  getList: (params: {
    page?: number
    per_page?: number
    keyword?: string
    status?: number | string
    role_id?: number | string
    start_date?: string
    end_date?: string
    sort_field?: string
    sort_order?: string
  }): Promise<PaginatedResponse<User>> => {
    return http.get('/user', { params })
  },

  /**
   * 获取用户详情
   */
  getById: (id: number): Promise<ApiResponse<User>> => {
    return http.get(`/user/${id}`)
  },

  /**
   * 创建用户
   */
  create: (data: {
    username: string
    email: string
    password: string
    confirm_password: string
    real_name?: string
    phone?: string
    role_ids?: number[]
    status?: number
    avatar?: string
  }): Promise<ApiResponse<User>> => {
    return http.post('/user', data)
  },

  /**
   * 更新用户
   */
  update: (id: number, data: {
    username?: string
    email?: string
    real_name?: string
    phone?: string
    role_ids?: number[]
    status?: number
    avatar?: string
  }): Promise<ApiResponse<User>> => {
    return http.put(`/user/${id}`, data)
  },

  /**
   * 删除用户
   */
  delete: (id: number): Promise<ApiResponse<void>> => {
    return http.delete(`/user/${id}`)
  },

  /**
   * 批量删除用户
   */
  batchDelete: (ids: number[]): Promise<ApiResponse<{ count: number }>> => {
    return http.delete('/user/batch', { data: { ids } })
  },

  /**
   * 启用用户
   */
  enable: (id: number): Promise<ApiResponse<void>> => {
    return http.post(`/user/${id}/enable`)
  },

  /**
   * 禁用用户
   */
  disable: (id: number): Promise<ApiResponse<void>> => {
    return http.post(`/user/${id}/disable`)
  },

  /**
   * 重置密码
   */
  resetPassword: (id: number, data: {
    password: string
  }): Promise<ApiResponse<void>> => {
    return http.post(`/user/${id}/reset-password`, data)
  },

  /**
   * 分配角色
   */
  assignRoles: (id: number, data: {
    role_ids: number[]
  }): Promise<ApiResponse<void>> => {
    return http.post(`/user/${id}/assign-roles`, data)
  },

  /**
   * 获取用户统计
   */
  getStatistics: (): Promise<ApiResponse<UserStatistics>> => {
    return http.get('/user/statistics')
  },

  /**
   * 导出用户
   */
  export: (params: {
    keyword?: string
    status?: number | string
    role_id?: number | string
    start_date?: string
    end_date?: string
  }): Promise<ApiResponse<{ file_path: string }>> => {
    return http.get('/user/export', { params })
  },

  /**
   * 导入用户
   */
  import: (data: FormData): Promise<ApiResponse<{
    success_count: number
    error_count: number
    errors: string[]
  }>> => {
    return http.post('/user/import', data, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 检查用户名是否可用
   */
  checkUsername: (username: string, excludeId?: number): Promise<ApiResponse<{
    available: boolean
    message: string
  }>> => {
    return http.get('/user/check-username', { 
      params: { username, exclude_id: excludeId } 
    })
  },

  /**
   * 检查邮箱是否可用
   */
  checkEmail: (email: string, excludeId?: number): Promise<ApiResponse<{
    available: boolean
    message: string
  }>> => {
    return http.get('/user/check-email', { 
      params: { email, exclude_id: excludeId } 
    })
  },

  /**
   * 检查手机号是否可用
   */
  checkPhone: (phone: string, excludeId?: number): Promise<ApiResponse<{
    available: boolean
    message: string
  }>> => {
    return http.get('/user/check-phone', { 
      params: { phone, exclude_id: excludeId } 
    })
  },

  /**
   * 获取用户操作日志
   */
  getLogs: (id: number, params?: {
    page?: number
    per_page?: number
    action?: string
    start_date?: string
    end_date?: string
  }): Promise<PaginatedResponse<any>> => {
    return http.get(`/user/${id}/logs`, { params })
  },

  /**
   * 获取用户登录历史
   */
  getLoginHistory: (id: number, params?: {
    page?: number
    per_page?: number
    start_date?: string
    end_date?: string
  }): Promise<PaginatedResponse<any>> => {
    return http.get(`/user/${id}/login-history`, { params })
  }
}

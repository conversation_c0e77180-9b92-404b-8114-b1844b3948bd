/**
 * 客户评价组件 (Testimonials Component)
 * 专业的客户评价展示组件，支持评价内容、客户信息、星级评分等展示
 *
 * @features
 * - 支持1-4列自适应布局
 * - 6种预设样式主题
 * - 客户评价卡片管理
 * - 星级评分系统
 * - 响应式设计
 */

// 预设客户头像库 - 使用稳定的头像源
const testimonialsAvatarLibrary = [
    'https://api.dicebear.com/7.x/avataaars/svg?seed=Client1&backgroundColor=b6e3f4&size=200',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=Client2&backgroundColor=c084fc&size=200',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=Client3&backgroundColor=34d399&size=200',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=Client4&backgroundColor=fbbf24&size=200',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=Client5&backgroundColor=f87171&size=200',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=Client6&backgroundColor=a78bfa&size=200'
];

// 客户评价样式预设 - 借鉴卡片组件的预设结构
const testimonialsStylePresets = [
    { 
        name: '商务专业', 
        bgColor: '#ffffff', 
        nameColor: '#2d3748', 
        companyColor: '#667eea', 
        contentColor: '#4a5568',
        cardBg: '#ffffff',
        cardShadow: true,
        ratingColor: '#ffd700'
    },
    { 
        name: '科技蓝调', 
        bgColor: '#1a202c', 
        nameColor: '#ffffff', 
        companyColor: '#4299e1', 
        contentColor: '#a0aec0',
        cardBg: '#2d3748',
        cardShadow: true,
        ratingColor: '#ffd700'
    },
    { 
        name: '温暖橙色', 
        bgColor: '#fff5f5', 
        nameColor: '#c53030', 
        companyColor: '#ed8936', 
        contentColor: '#2d3748',
        cardBg: '#ffffff',
        cardShadow: false,
        ratingColor: '#ed8936'
    },
    { 
        name: '自然绿色', 
        bgColor: '#f0fff4', 
        nameColor: '#22543d', 
        companyColor: '#38a169', 
        contentColor: '#2d3748',
        cardBg: '#ffffff',
        cardShadow: false,
        ratingColor: '#38a169'
    },
    { 
        name: '优雅紫色', 
        bgColor: '#faf5ff', 
        nameColor: '#553c9a', 
        companyColor: '#805ad5', 
        contentColor: '#2d3748',
        cardBg: '#ffffff',
        cardShadow: false,
        ratingColor: '#805ad5'
    },
    { 
        name: '现代灰色', 
        bgColor: '#f7fafc', 
        nameColor: '#1a202c', 
        companyColor: '#718096', 
        contentColor: '#4a5568',
        cardBg: '#ffffff',
        cardShadow: true,
        ratingColor: '#ffd700'
    }
];

// 客户评价组件模板
const testimonialsComponent = {
    name: '客户评价',
    html: `<div class="testimonials-container">
        <div class="testimonial-item">
            <div class="testimonial-content">
                <div class="testimonial-rating">
                    <span class="star">★</span>
                    <span class="star">★</span>
                    <span class="star">★</span>
                    <span class="star">★</span>
                    <span class="star">★</span>
                </div>
                <p class="testimonial-text">"服务非常专业，团队响应迅速，项目按时交付，质量超出预期。强烈推荐！"</p>
                <div class="testimonial-author">
                    <div class="author-avatar-container">
                        <img class="author-avatar" src="${testimonialsAvatarLibrary[0]}" alt="客户头像">
                    </div>
                    <div class="author-info">
                        <h4 class="author-name">李总</h4>
                        <p class="author-company">ABC科技有限公司</p>
                        <p class="author-position">CEO</p>
                    </div>
                </div>
            </div>
        </div>
    </div>`,
    render: function(component, properties) {
        updateTestimonialsDisplay(component, properties);
    },
    properties: {
        // 评价数据
        testimonials: [
            {
                name: '李总',
                company: 'ABC科技有限公司',
                position: 'CEO',
                avatar: testimonialsAvatarLibrary[0],
                selectedAvatarIndex: 0,
                content: '服务非常专业，团队响应迅速，项目按时交付，质量超出预期。强烈推荐！',
                rating: 5,
                date: '2024-01-15'
            },
            {
                name: '王经理',
                company: 'XYZ贸易公司',
                position: '总经理',
                avatar: testimonialsAvatarLibrary[1],
                selectedAvatarIndex: 1,
                content: '合作愉快，专业度很高，解决了我们的核心问题，期待下次合作。',
                rating: 5,
                date: '2024-01-20'
            }
        ],

        // 布局设置
        columnsCount: 2,
        layout: 'grid',
        
        // 样式预设
        stylePreset: 'business-professional',
        bgColor: '#ffffff',
        bgRadius: 12,
        
        // 样式设置
        nameSize: 18,
        companySize: 14,
        positionSize: 12,
        contentSize: 16,
        nameColor: '#2d3748',
        companyColor: '#667eea',
        positionColor: '#718096',
        contentColor: '#4a5568',
        
        // 卡片设置
        cardBg: '#ffffff',
        cardRadius: 12,
        cardShadow: true,
        cardPadding: 24,
        
        // 头像设置
        avatarSize: 60,
        avatarShape: 'circle',
        
        // 评分设置
        showRating: true,
        ratingColor: '#ffd700',
        ratingSize: 18,
        
        // 尺寸设置
        maxWidth: 1200,
        marginHorizontal: 20,
        marginVertical: 20,
        positionVertical: 0,
        
        // 间距设置
        itemSpacing: 24,
        
        // 文字对齐
        textAlign: 'left'
    }
};

// 生成客户评价组件属性面板 - 借鉴卡片组件的成熟设计模式
function generateTestimonialsProperties(component) {
    // 获取或初始化组件属性
    let props;
    if (component._testimonialsProperties) {
        props = component._testimonialsProperties;
    } else {
        props = JSON.parse(JSON.stringify(testimonialsComponent.properties));
        component._testimonialsProperties = props;
    }

    let html = `
        <!-- 布局设置 -->
        <div class="property-section">
            <h4 class="section-title">布局设置</h4>

            <div class="property-group">
                <label class="property-label">列数布局</label>
                <div class="layout-buttons-clean">
                    <button type="button" class="layout-btn ${props.columnsCount === 1 ? 'active' : ''}"
                            onclick="updateTestimonialsColumns('${component.id}', 1)">1列</button>
                    <button type="button" class="layout-btn ${props.columnsCount === 2 ? 'active' : ''}"
                            onclick="updateTestimonialsColumns('${component.id}', 2)">2列</button>
                    <button type="button" class="layout-btn ${props.columnsCount === 3 ? 'active' : ''}"
                            onclick="updateTestimonialsColumns('${component.id}', 3)">3列</button>
                    <button type="button" class="layout-btn ${props.columnsCount === 4 ? 'active' : ''}"
                            onclick="updateTestimonialsColumns('${component.id}', 4)">4列</button>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">项目间距</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.itemSpacing}" min="12" max="48"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTestimonialsPropertySmooth('${component.id}', 'itemSpacing', this.value)"
                           onchange="updateTestimonialsProperty('${component.id}', 'itemSpacing', this.value)">
                    <span class="range-value">${props.itemSpacing}px</span>
                </div>
            </div>
        </div>

        <!-- 样式预设 -->
        <div class="property-section">
            <h4 class="section-title">样式预设</h4>

            <div class="property-group">
                <label class="property-label">快速样式</label>
                <div class="style-presets">
                    ${testimonialsStylePresets.map((preset, index) => {
                        const presetKey = preset.name.toLowerCase().replace(/\s+/g, '-');
                        const isSelected = props.stylePreset === presetKey;
                        return `
                        <div class="style-preset ${isSelected ? 'selected' : ''}"
                             style="background: ${preset.cardBg}; color: ${preset.nameColor}; border: 2px solid ${preset.companyColor};"
                             onclick="applyTestimonialsStylePreset('${component.id}', ${index})"
                             title="${preset.name}">
                            <span style="font-size: 12px; font-weight: bold;">${preset.name}</span>
                        </div>
                        `;
                    }).join('')}
                </div>
            </div>
        </div>

        <!-- 客户评价管理 -->
        <div class="property-section">
            <h4 class="section-title">客户评价 (${props.testimonials.length}个)</h4>

            <div class="testimonials-manager">
                ${props.testimonials.map((testimonial, index) => `
                    <div class="testimonial-editor" data-index="${index}">
                        <div class="testimonial-editor-header">
                            <span class="testimonial-editor-title">评价 ${index + 1}</span>
                            <div class="testimonial-editor-actions">
                                <button type="button" class="testimonial-action-btn" onclick="toggleTestimonialEditor(this)">
                                    <span class="toggle-icon">▼</span>
                                </button>
                                ${props.testimonials.length > 1 ? `
                                    <button type="button" class="testimonial-action-btn delete" onclick="deleteTestimonial('${component.id}', ${index})">
                                        ✕
                                    </button>
                                ` : ''}
                            </div>
                        </div>

                        <div class="testimonial-editor-content">
                            <div class="property-group">
                                <label class="property-label">客户姓名</label>
                                <input type="text" class="property-input" value="${testimonial.name}"
                                       onchange="updateTestimonialContent('${component.id}', ${index}, 'name', this.value)">
                            </div>

                            <div class="property-group">
                                <label class="property-label">公司名称</label>
                                <input type="text" class="property-input" value="${testimonial.company}"
                                       onchange="updateTestimonialContent('${component.id}', ${index}, 'company', this.value)">
                            </div>

                            <div class="property-group">
                                <label class="property-label">职位</label>
                                <input type="text" class="property-input" value="${testimonial.position}"
                                       onchange="updateTestimonialContent('${component.id}', ${index}, 'position', this.value)">
                            </div>

                            <div class="property-group">
                                <label class="property-label">评价内容</label>
                                <textarea class="property-input" rows="3"
                                          onchange="updateTestimonialContent('${component.id}', ${index}, 'content', this.value)">${testimonial.content}</textarea>
                            </div>

                            <div class="property-group">
                                <label class="property-label">评分 (${testimonial.rating}星)</label>
                                <div class="rating-selector">
                                    ${[1,2,3,4,5].map(star => `
                                        <span class="rating-star ${star <= testimonial.rating ? 'active' : ''}"
                                              onclick="updateTestimonialContent('${component.id}', ${index}, 'rating', ${star})">★</span>
                                    `).join('')}
                                </div>
                            </div>

                            <div class="property-group">
                                <label class="property-label">头像选择</label>
                                <div class="avatar-library-mini">
                                    ${testimonialsAvatarLibrary.slice(0, 6).map((avatar, avatarIndex) => `
                                        <div class="avatar-option-mini ${testimonial.selectedAvatarIndex === avatarIndex ? 'selected' : ''}"
                                             style="background-image: url('${avatar}')"
                                             onclick="updateTestimonialContent('${component.id}', ${index}, 'selectedAvatarIndex', ${avatarIndex})"
                                             title="头像 ${avatarIndex + 1}">
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>

            ${props.testimonials.length < props.columnsCount ? `
                <button type="button" class="add-btn" onclick="addTestimonial('${component.id}')">
                    + 添加评价
                </button>
            ` : ''}
        </div>
    `;

    // 添加颜色设置、卡片设置等其他属性面板
    html += `
        <!-- 颜色设置 -->
        <div class="property-section">
            <h4 class="section-title">颜色设置</h4>

            <div class="property-group">
                <div class="color-row">
                    <div class="color-item">
                        <label>背景色</label>
                        <input type="color" class="property-input" value="${props.bgColor}"
                               onchange="updateTestimonialsProperty('${component.id}', 'bgColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>姓名色</label>
                        <input type="color" class="property-input" value="${props.nameColor}"
                               onchange="updateTestimonialsProperty('${component.id}', 'nameColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>公司色</label>
                        <input type="color" class="property-input" value="${props.companyColor}"
                               onchange="updateTestimonialsProperty('${component.id}', 'companyColor', this.value)">
                    </div>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">背景圆角</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.bgRadius || 0}" min="0" max="50"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTestimonialsPropertySmooth('${component.id}', 'bgRadius', this.value)"
                           onchange="updateTestimonialsProperty('${component.id}', 'bgRadius', this.value)">
                    <span class="range-value">${props.bgRadius || 0}px</span>
                </div>
            </div>
        </div>

        <!-- 卡片设置 -->
        <div class="property-section">
            <h4 class="section-title">卡片设置</h4>

            <div class="property-group">
                <label class="property-label">卡片背景色</label>
                <div class="color-row">
                    <div class="color-item">
                        <input type="color" class="property-input" value="${props.cardBg}"
                               onchange="updateTestimonialsProperty('${component.id}', 'cardBg', this.value)">
                    </div>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">圆角大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.cardRadius}" min="0" max="30"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTestimonialsPropertySmooth('${component.id}', 'cardRadius', this.value)"
                           onchange="updateTestimonialsProperty('${component.id}', 'cardRadius', this.value)">
                    <span class="range-value">${props.cardRadius}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">内边距</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.cardPadding}" min="12" max="48"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTestimonialsPropertySmooth('${component.id}', 'cardPadding', this.value)"
                           onchange="updateTestimonialsProperty('${component.id}', 'cardPadding', this.value)">
                    <span class="range-value">${props.cardPadding}px</span>
                </div>
            </div>

            <div class="property-group">
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" ${props.cardShadow ? 'checked' : ''}
                               onchange="updateTestimonialsProperty('${component.id}', 'cardShadow', this.checked)">
                        <span>卡片阴影</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 评分设置 -->
        <div class="property-section">
            <h4 class="section-title">评分设置</h4>

            <div class="property-group">
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" ${props.showRating ? 'checked' : ''}
                               onchange="updateTestimonialsProperty('${component.id}', 'showRating', this.checked)">
                        <span>显示评分</span>
                    </div>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">评分颜色</label>
                <input type="color" class="property-input" value="${props.ratingColor}"
                       onchange="updateTestimonialsProperty('${component.id}', 'ratingColor', this.value)">
            </div>

            <div class="property-group">
                <label class="property-label">评分大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.ratingSize}" min="14" max="24"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTestimonialsPropertySmooth('${component.id}', 'ratingSize', this.value)"
                           onchange="updateTestimonialsProperty('${component.id}', 'ratingSize', this.value)">
                    <span class="range-value">${props.ratingSize}px</span>
                </div>
            </div>
        </div>

        <!-- 头像设置 -->
        <div class="property-section">
            <h4 class="section-title">头像设置</h4>

            <div class="property-group">
                <label class="property-label">头像大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.avatarSize}" min="40" max="100"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTestimonialsPropertySmooth('${component.id}', 'avatarSize', this.value)"
                           onchange="updateTestimonialsProperty('${component.id}', 'avatarSize', this.value)">
                    <span class="range-value">${props.avatarSize}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">头像形状</label>
                <select class="property-input" onchange="updateTestimonialsProperty('${component.id}', 'avatarShape', this.value)">
                    <option value="circle" ${props.avatarShape === 'circle' ? 'selected' : ''}>圆形</option>
                    <option value="square" ${props.avatarShape === 'square' ? 'selected' : ''}>方形</option>
                    <option value="rounded" ${props.avatarShape === 'rounded' ? 'selected' : ''}>圆角方形</option>
                </select>
            </div>
        </div>

        <!-- 字体设置 -->
        <div class="property-section">
            <h4 class="section-title">字体设置</h4>

            <div class="property-group">
                <label class="property-label">姓名大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.nameSize}" min="14" max="24"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTestimonialsPropertySmooth('${component.id}', 'nameSize', this.value)"
                           onchange="updateTestimonialsProperty('${component.id}', 'nameSize', this.value)">
                    <span class="range-value">${props.nameSize}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">公司大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.companySize}" min="12" max="18"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTestimonialsPropertySmooth('${component.id}', 'companySize', this.value)"
                           onchange="updateTestimonialsProperty('${component.id}', 'companySize', this.value)">
                    <span class="range-value">${props.companySize}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">内容大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.contentSize}" min="12" max="20"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTestimonialsPropertySmooth('${component.id}', 'contentSize', this.value)"
                           onchange="updateTestimonialsProperty('${component.id}', 'contentSize', this.value)">
                    <span class="range-value">${props.contentSize}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">文字对齐</label>
                <select class="property-input" onchange="updateTestimonialsProperty('${component.id}', 'textAlign', this.value)">
                    <option value="left" ${props.textAlign === 'left' ? 'selected' : ''}>左对齐</option>
                    <option value="center" ${props.textAlign === 'center' ? 'selected' : ''}>居中对齐</option>
                    <option value="right" ${props.textAlign === 'right' ? 'selected' : ''}>右对齐</option>
                </select>
            </div>
        </div>

        <!-- 尺寸设置 -->
        <div class="property-section">
            <h4 class="section-title">尺寸设置</h4>

            <div class="property-group">
                <label class="property-label">最大宽度</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.maxWidth}" min="600" max="1800"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTestimonialsPropertySmooth('${component.id}', 'maxWidth', this.value)"
                           onchange="updateTestimonialsProperty('${component.id}', 'maxWidth', this.value)">
                    <span class="range-value">${props.maxWidth}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">左右外边距</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.marginHorizontal}" min="0" max="100"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTestimonialsPropertySmooth('${component.id}', 'marginHorizontal', this.value)"
                           onchange="updateTestimonialsProperty('${component.id}', 'marginHorizontal', this.value)">
                    <span class="range-value">${props.marginHorizontal}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">上下外边距</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.marginVertical}" min="0" max="100"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTestimonialsPropertySmooth('${component.id}', 'marginVertical', this.value)"
                           onchange="updateTestimonialsProperty('${component.id}', 'marginVertical', this.value)">
                    <span class="range-value">${props.marginVertical}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">上下位置调节</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.positionVertical}" min="-600" max="600"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTestimonialsPropertySmooth('${component.id}', 'positionVertical', this.value)"
                           onchange="updateTestimonialsProperty('${component.id}', 'positionVertical', this.value)">
                    <span class="range-value">${props.positionVertical}px</span>
                </div>
                <div class="range-tips">
                    <small>负值向上移动，正值向下移动。层级控制请使用组件上方的"上层"/"下层"按钮</small>
                </div>
            </div>
        </div>
    `;

    return html;
}

// 更新客户评价列数 - 参考团队组件的实现模式
function updateTestimonialsColumns(componentId, columns) {
    const component = document.getElementById(componentId);
    if (!component) return;

    // 获取或初始化组件属性
    let props;
    if (component._testimonialsProperties) {
        props = component._testimonialsProperties;
    } else {
        props = JSON.parse(JSON.stringify(testimonialsComponent.properties));
        component._testimonialsProperties = props;
    }

    props.columnsCount = columns;

    // 调整评价数量以匹配列数
    while (props.testimonials.length < columns) {
        props.testimonials.push({
            name: `客户${props.testimonials.length + 1}`,
            company: '公司名称',
            position: '职位',
            avatar: testimonialsAvatarLibrary[props.testimonials.length % testimonialsAvatarLibrary.length],
            selectedAvatarIndex: props.testimonials.length % testimonialsAvatarLibrary.length,
            content: '这里是客户的评价内容，可以描述服务质量、合作体验等。',
            rating: 5,
            date: '2024-01-15'
        });
    }

    // 如果评价数量超过列数，保留前N个
    if (props.testimonials.length > columns) {
        props.testimonials = props.testimonials.slice(0, columns);
    }

    updateTestimonialsDisplay(component, props);
    updatePropertiesPanel(component);
}

// 添加客户评价
function addTestimonial(componentId) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props;
    if (component._testimonialsProperties) {
        props = component._testimonialsProperties;
    } else {
        props = JSON.parse(JSON.stringify(testimonialsComponent.properties));
        component._testimonialsProperties = props;
    }

    if (props.testimonials.length < props.columnsCount) {
        props.testimonials.push({
            name: `客户${props.testimonials.length + 1}`,
            company: '公司名称',
            position: '职位',
            avatar: testimonialsAvatarLibrary[props.testimonials.length % testimonialsAvatarLibrary.length],
            selectedAvatarIndex: props.testimonials.length % testimonialsAvatarLibrary.length,
            content: '这里是客户的评价内容，可以描述服务质量、合作体验等。',
            rating: 5,
            date: '2024-01-15'
        });

        updateTestimonialsDisplay(component, props);
        updatePropertiesPanel(component);
    }
}

// 删除客户评价
function deleteTestimonial(componentId, testimonialIndex) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props;
    if (component._testimonialsProperties) {
        props = component._testimonialsProperties;
    } else {
        props = JSON.parse(JSON.stringify(testimonialsComponent.properties));
        component._testimonialsProperties = props;
    }

    if (props.testimonials.length > 1) {
        props.testimonials.splice(testimonialIndex, 1);
        updateTestimonialsDisplay(component, props);
        updatePropertiesPanel(component);
    }
}

// 更新单个评价内容
function updateTestimonialContent(componentId, testimonialIndex, property, value) {
    const component = document.getElementById(componentId);
    if (!component) return;

    // 获取组件的属性数据
    let props;
    if (component._testimonialsProperties) {
        props = component._testimonialsProperties;
    } else {
        // 初始化组件属性
        props = JSON.parse(JSON.stringify(testimonialsComponent.properties));
        component._testimonialsProperties = props;
    }

    if (props.testimonials && props.testimonials[testimonialIndex]) {
        if (property === 'selectedAvatarIndex') {
            props.testimonials[testimonialIndex][property] = parseInt(value);
            props.testimonials[testimonialIndex].avatar = testimonialsAvatarLibrary[value];
        } else if (property === 'rating') {
            props.testimonials[testimonialIndex][property] = parseInt(value);

            // 对于评分更新，只更新显示，不重新渲染属性面板，避免评分选择器状态混乱
            updateTestimonialsDisplay(component, props);

            // 只更新当前评价的评分标签显示
            updateRatingLabel(componentId, testimonialIndex, parseInt(value));
            return;
        } else {
            props.testimonials[testimonialIndex][property] = value;
        }

        updateTestimonialsDisplay(component, props);
        updatePropertiesPanel(component);
    }
}

// 更新评分标签显示 - 避免重新渲染整个属性面板
function updateRatingLabel(componentId, testimonialIndex, rating) {
    const component = document.getElementById(componentId);
    if (!component) return;

    // 查找对应的评分标签并更新
    const testimonialEditor = document.querySelector(`[data-index="${testimonialIndex}"]`);
    if (testimonialEditor) {
        // 更精确地查找评分相关的标签
        const propertyGroups = testimonialEditor.querySelectorAll('.property-group');
        let ratingPropertyGroup = null;

        // 找到包含评分的属性组
        propertyGroups.forEach(group => {
            const label = group.querySelector('.property-label');
            if (label && label.textContent.includes('评分')) {
                ratingPropertyGroup = group;
            }
        });

        if (ratingPropertyGroup) {
            // 更新评分标签文字
            const ratingLabel = ratingPropertyGroup.querySelector('.property-label');
            if (ratingLabel) {
                ratingLabel.textContent = `评分 (${rating}星)`;
            }

            // 更新评分选择器的active状态
            const ratingStars = ratingPropertyGroup.querySelectorAll('.rating-star');
            ratingStars.forEach((star, index) => {
                if (index < rating) {
                    star.classList.add('active');
                } else {
                    star.classList.remove('active');
                }
            });
        }
    }
}

// 切换评价编辑器展开/收起
function toggleTestimonialEditor(button) {
    const header = button.closest('.testimonial-editor-header');
    const content = header.nextElementSibling;
    const icon = button.querySelector('.toggle-icon');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        icon.textContent = '▼';
    } else {
        content.style.display = 'none';
        icon.textContent = '▶';
    }
}

// 应用样式预设 - 参考卡片组件的实现
function applyTestimonialsStylePreset(componentId, presetIndex) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props;
    if (component._testimonialsProperties) {
        props = component._testimonialsProperties;
    } else {
        props = JSON.parse(JSON.stringify(testimonialsComponent.properties));
        component._testimonialsProperties = props;
    }

    const preset = testimonialsStylePresets[presetIndex];

    // 应用预设样式
    props.stylePreset = preset.name.toLowerCase().replace(/\s+/g, '-');
    props.bgColor = preset.bgColor;
    props.nameColor = preset.nameColor;
    props.companyColor = preset.companyColor;
    props.contentColor = preset.contentColor;
    props.cardBg = preset.cardBg;
    props.cardShadow = preset.cardShadow;
    props.ratingColor = preset.ratingColor;

    updateTestimonialsDisplay(component, props);
    updatePropertiesPanel(component);
}

// 更新客户评价属性 - 参考团队组件的属性更新逻辑
function updateTestimonialsProperty(componentId, property, value, skipPanelUpdate = false) {
    const component = document.getElementById(componentId);
    if (!component) return;

    // 获取或初始化组件属性
    let props;
    if (component._testimonialsProperties) {
        props = component._testimonialsProperties;
    } else {
        props = JSON.parse(JSON.stringify(testimonialsComponent.properties));
        component._testimonialsProperties = props;
    }

    // 类型转换处理 - 参考团队组件的处理方式
    if (['maxWidth', 'cardPadding', 'cardRadius', 'avatarSize', 'nameSize', 'companySize', 'positionSize', 'contentSize', 'ratingSize', 'itemSpacing', 'marginHorizontal', 'marginVertical', 'positionVertical', 'bgRadius'].includes(property)) {
        props[property] = parseInt(value);
    } else if (property === 'cardShadow' || property === 'showRating') {
        props[property] = value;
    } else {
        props[property] = value;
    }

    updateTestimonialsDisplay(component, props);

    // 只有在不跳过面板更新时才更新面板
    if (!skipPanelUpdate) {
        updatePropertiesPanel(component);
    }
}

// 优化的拖动条更新函数 - 只更新显示，不重新渲染面板
function updateTestimonialsPropertySmooth(componentId, property, value) {
    updateTestimonialsProperty(componentId, property, value, true);
}

// 更新客户评价显示 - 核心显示更新函数
function updateTestimonialsDisplay(component, props) {
    const container = component.querySelector('.testimonials-container');
    if (!container) return;

    // 设置容器样式
    container.style.display = 'grid';
    container.style.gridTemplateColumns = `repeat(${props.columnsCount}, 1fr)`;
    container.style.gap = `${props.itemSpacing}px`;
    container.style.margin = `${props.marginVertical}px ${props.marginHorizontal}px`;
    container.style.maxWidth = `${props.maxWidth}px`;
    container.style.marginLeft = 'auto';
    container.style.marginRight = 'auto';
    container.style.backgroundColor = props.bgColor;
    container.style.borderRadius = `${props.bgRadius || 0}px`;
    container.style.padding = props.bgRadius > 0 ? '20px' : '20px';
    container.style.overflow = 'hidden';

    // 确保圆角效果可见
    if (props.bgRadius > 0) {
        container.style.border = '1px solid rgba(0,0,0,0.05)';
        container.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
    } else {
        container.style.border = 'none';
        container.style.boxShadow = 'none';
    }

    // 将transform应用到整个component-block，这样控制按钮也会跟着移动
    component.style.transform = `translateY(${props.positionVertical}px)`;
    component.style.position = 'relative';

    // 生成客户评价HTML
    container.innerHTML = props.testimonials.map((testimonial, index) => {
        // 确保rating是数字类型
        const rating = parseInt(testimonial.rating) || 5;

        // 生成星级评分
        const ratingStars = props.showRating ?
            Array.from({length: 5}, (_, i) =>
                `<span class="star ${i < rating ? 'active' : ''}" style="color: ${i < rating ? props.ratingColor : '#e2e8f0'};">★</span>`
            ).join('') : '';

        return `
            <div class="testimonial-item">
                <div class="testimonial-content">
                    <p class="testimonial-text">"${testimonial.content}"</p>
                    <div class="testimonial-author">
                        <div class="author-avatar-container">
                            <img class="author-avatar"
                                 src="${testimonial.avatar}"
                                 alt="${testimonial.name}"
                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjZjdmYWZjIi8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjgwIiByPSIzMCIgZmlsbD0iIzRhNTU2OCIvPgo8cGF0aCBkPSJNNjAgMTYwYzAtMjIuMDkgMTcuOTEtNDAgNDAtNDBzNDAgMTcuOTEgNDAgNDB2MjBINjB2LTIweiIgZmlsbD0iIzRhNTU2OCIvPgo8L3N2Zz4K'">
                        </div>
                        <div class="author-info">
                            <h4 class="author-name">${testimonial.name}</h4>
                            <p class="author-company">${testimonial.company}</p>
                            <p class="author-position">${testimonial.position}</p>
                            ${props.showRating ? `
                                <div class="testimonial-rating">
                                    ${ratingStars}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    // 应用样式到所有评价卡片
    const testimonialItems = container.querySelectorAll('.testimonial-item');
    testimonialItems.forEach((testimonialItem, index) => {
        const testimonial = props.testimonials[index];

        // 基础卡片样式
        testimonialItem.style.backgroundColor = props.cardBg;
        testimonialItem.style.borderRadius = `${props.cardRadius}px`;
        testimonialItem.style.padding = `${props.cardPadding}px`;
        testimonialItem.style.textAlign = props.textAlign;
        testimonialItem.style.boxShadow = props.cardShadow ?
            '0 4px 20px rgba(0,0,0,0.1)' : 'none';
        testimonialItem.style.transition = 'all 0.3s ease';
        testimonialItem.style.overflow = 'hidden';

        // 评分样式
        const rating = testimonialItem.querySelector('.testimonial-rating');
        if (rating) {
            rating.style.marginBottom = '16px';
            rating.style.fontSize = `${props.ratingSize}px`;
        }

        // 评价内容样式
        const text = testimonialItem.querySelector('.testimonial-text');
        if (text) {
            text.style.color = props.contentColor;
            text.style.fontSize = `${props.contentSize}px`;
            text.style.lineHeight = '1.6';
            text.style.marginBottom = '20px';
            text.style.fontStyle = 'italic';
        }

        // 头像样式
        const avatar = testimonialItem.querySelector('.author-avatar');
        if (avatar) {
            avatar.style.width = `${props.avatarSize}px`;
            avatar.style.height = `${props.avatarSize}px`;
            avatar.style.objectFit = 'cover';
            avatar.style.marginRight = '12px';

            // 头像形状
            if (props.avatarShape === 'circle') {
                avatar.style.borderRadius = '50%';
            } else if (props.avatarShape === 'square') {
                avatar.style.borderRadius = '0';
            } else if (props.avatarShape === 'rounded') {
                avatar.style.borderRadius = '8px';
            }
        }

        // 作者信息样式
        const authorInfo = testimonialItem.querySelector('.author-info');
        if (authorInfo) {
            authorInfo.style.flex = '1';
        }

        // 姓名样式
        const name = testimonialItem.querySelector('.author-name');
        if (name) {
            name.style.color = props.nameColor;
            name.style.fontSize = `${props.nameSize}px`;
            name.style.marginBottom = '4px';
            name.style.fontWeight = '600';
            name.style.lineHeight = '1.3';
        }

        // 公司样式
        const company = testimonialItem.querySelector('.author-company');
        if (company) {
            company.style.color = props.companyColor;
            company.style.fontSize = `${props.companySize}px`;
            company.style.marginBottom = '2px';
            company.style.fontWeight = '500';
        }

        // 职位样式
        const position = testimonialItem.querySelector('.author-position');
        if (position) {
            position.style.color = props.positionColor;
            position.style.fontSize = `${props.positionSize}px`;
            position.style.opacity = '0.8';
        }

        // 作者区域布局
        const author = testimonialItem.querySelector('.testimonial-author');
        if (author) {
            author.style.display = 'flex';
            author.style.alignItems = 'center';
            author.style.marginTop = '16px';
        }
    });
}

// 注册客户评价组件
if (typeof ComponentManager !== 'undefined') {
    ComponentManager.register('testimonials', testimonialsComponent, generateTestimonialsProperties, updateTestimonialsDisplay);
}

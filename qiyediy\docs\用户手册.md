# QiyeDIY企业建站系统 - 用户手册

> **三只鱼网络科技 | 韩总 | 2024-12-20**  
> **版本**: 1.0.0  
> **现代化企业级DIY建站解决方案**

---

## 📖 目录

1. [系统介绍](#系统介绍)
2. [快速开始](#快速开始)
3. [DIY编辑器使用指南](#diy编辑器使用指南)
4. [模板系统](#模板系统)
5. [系统管理](#系统管理)
6. [常见问题](#常见问题)
7. [技术支持](#技术支持)

---

## 🌟 系统介绍

### 什么是QiyeDIY？

QiyeDIY是一款专为企业打造的可视化建站系统，让您无需编程知识即可创建专业的企业网站。

### 核心特性

- **🎨 可视化编辑** - 拖拽式页面编辑，所见即所得
- **📱 响应式设计** - 自动适配桌面、平板、手机
- **🚀 高性能** - 优化的加载速度和SEO友好
- **🎯 模板丰富** - 多行业精美模板，一键应用
- **⚙️ 易于管理** - 完善的后台管理系统
- **🔧 高度定制** - 灵活的组件和样式配置

### 适用场景

- 企业官网建设
- 产品展示网站
- 个人作品集
- 博客和新闻站点
- 电商展示页面

---

## 🚀 快速开始

### 1. 系统登录

1. 打开浏览器，访问管理后台地址
2. 输入用户名和密码
3. 点击"登录"按钮进入系统

### 2. 创建第一个页面

1. 在左侧菜单点击"DIY编辑器"
2. 点击"新建页面"按钮
3. 输入页面标题和描述
4. 选择页面模板（可选）
5. 点击"创建"开始编辑

### 3. 发布页面

1. 完成页面编辑后，点击右上角"保存"
2. 点击"预览"查看效果
3. 确认无误后点击"发布"
4. 页面即可在前台访问

---

## 🎨 DIY编辑器使用指南

### 编辑器界面

DIY编辑器采用三栏式布局：

- **左侧面板**: 组件库、页面结构、模板
- **中间画布**: 可视化编辑区域
- **右侧面板**: 属性设置、样式配置

### 组件库

#### 基础组件

**文本组件**
- 用途：添加标题、段落、文字内容
- 配置：字体、大小、颜色、对齐方式
- 技巧：支持富文本编辑，可插入链接

**图片组件**
- 用途：展示图片、Logo、产品图
- 配置：尺寸、圆角、边框、链接
- 技巧：支持懒加载，自动压缩优化

**按钮组件**
- 用途：行动号召、链接跳转
- 配置：文字、颜色、大小、样式
- 技巧：可设置悬停效果和动画

**容器组件**
- 用途：布局容器，组织其他组件
- 配置：背景、间距、对齐、边框
- 技巧：支持弹性布局和网格布局

#### 高级组件

**轮播图组件**
- 用途：图片轮播展示
- 配置：自动播放、切换效果、导航
- 技巧：支持多种切换动画

**表单组件**
- 用途：用户信息收集
- 配置：字段类型、验证规则、样式
- 技巧：支持多种输入类型和验证

**列表组件**
- 用途：内容列表展示
- 配置：布局、样式、分页
- 技巧：支持网格和列表两种模式

### 属性编辑

#### 内容属性
- **文本内容**: 直接编辑文字内容
- **图片地址**: 上传或输入图片URL
- **链接设置**: 内部链接或外部链接

#### 样式属性
- **尺寸设置**: 宽度、高度、最大最小值
- **间距设置**: 内边距、外边距
- **边框设置**: 边框样式、圆角、阴影
- **背景设置**: 颜色、图片、渐变

#### 布局属性
- **定位方式**: 静态、相对、绝对、固定
- **显示方式**: 块级、行内、弹性、网格
- **对齐方式**: 左对齐、居中、右对齐

### 样式编辑器

#### 可视化编辑
- **颜色选择器**: 支持透明度调节
- **字体设置**: 字体族、大小、粗细
- **间距调节**: 拖拽式间距设置
- **边框配置**: 样式、宽度、颜色

#### 高级样式
- **CSS动画**: 过渡效果、悬停动画
- **响应式样式**: 不同设备的样式适配
- **自定义CSS**: 直接编写CSS代码

### 预览功能

#### 多设备预览
- **桌面端预览**: 1920x1080分辨率
- **平板预览**: 768x1024分辨率
- **手机预览**: 375x812分辨率

#### 预览操作
- **实时预览**: 编辑时即时查看效果
- **全屏预览**: 全屏模式查看页面
- **新窗口预览**: 在新窗口中打开预览

---

## 📋 模板系统

### 模板中心

#### 模板分类
- **企业官网**: 公司介绍、产品展示类模板
- **电商商城**: 产品销售、购物类模板
- **个人博客**: 个人展示、博客类模板
- **作品展示**: 设计师、摄影师作品集
- **教育培训**: 学校、培训机构类模板

#### 模板筛选
- **按分类筛选**: 选择特定行业模板
- **按标签筛选**: 根据特性标签筛选
- **搜索功能**: 关键词搜索模板
- **排序功能**: 按热度、时间、评分排序

### 使用模板

#### 应用模板
1. 在模板中心选择心仪的模板
2. 点击"预览"查看模板效果
3. 点击"使用模板"创建新页面
4. 根据需要修改内容和样式

#### 保存模板
1. 在编辑器中完成页面设计
2. 点击"保存为模板"
3. 填写模板信息和描述
4. 上传预览图片
5. 设置模板分类和标签

### 模板管理

#### 我的模板
- 查看已创建的模板
- 编辑模板信息
- 删除不需要的模板
- 分享模板给其他用户

---

## ⚙️ 系统管理

### 基本设置

#### 网站信息
- **网站名称**: 设置网站标题
- **网站描述**: SEO描述信息
- **网站Logo**: 上传网站Logo
- **联系信息**: 电话、邮箱、地址

#### 上传设置
- **文件大小限制**: 设置上传文件大小
- **允许格式**: 配置允许的文件格式
- **存储路径**: 设置文件存储位置
- **图片压缩**: 自动压缩图片设置

### 用户管理

#### 用户列表
- 查看所有注册用户
- 编辑用户信息
- 设置用户权限
- 禁用或删除用户

#### 角色权限
- **管理员**: 所有权限
- **编辑者**: 内容编辑权限
- **查看者**: 只读权限

### 系统监控

#### 性能监控
- **页面加载速度**: 监控页面性能
- **服务器状态**: CPU、内存使用情况
- **数据库性能**: 查询速度和连接数
- **缓存状态**: 缓存命中率和大小

#### 访问统计
- **页面访问量**: 各页面访问统计
- **用户行为**: 用户操作行为分析
- **设备统计**: 访问设备类型分布
- **地域分布**: 访问用户地域分析

### 系统维护

#### 缓存管理
- **清空缓存**: 清理系统缓存
- **缓存配置**: 设置缓存策略
- **缓存统计**: 查看缓存使用情况

#### 备份恢复
- **数据备份**: 定期备份数据库
- **文件备份**: 备份上传的文件
- **一键恢复**: 从备份恢复数据
- **备份下载**: 下载备份文件

---

## ❓ 常见问题

### 编辑器相关

**Q: 为什么组件拖拽不生效？**
A: 请检查浏览器兼容性，建议使用Chrome、Firefox等现代浏览器。

**Q: 如何删除已添加的组件？**
A: 选中组件后，点击右上角的删除按钮，或按Delete键。

**Q: 页面保存后为什么前台看不到？**
A: 请确认页面已发布，草稿状态的页面不会在前台显示。

### 模板相关

**Q: 应用模板后能否修改？**
A: 可以，应用模板后可以自由修改所有内容和样式。

**Q: 如何分享自己制作的模板？**
A: 在模板管理中将模板设置为公开，其他用户即可使用。

### 系统相关

**Q: 忘记管理员密码怎么办？**
A: 请联系技术支持重置密码，或通过数据库直接修改。

**Q: 网站访问速度慢怎么优化？**
A: 可以开启缓存、压缩图片、使用CDN等方式优化。

---

## 🛠️ 技术支持

### 联系方式

- **技术支持邮箱**: <EMAIL>
- **客服电话**: 400-123-4567
- **在线客服**: 工作日 9:00-18:00
- **技术文档**: https://docs.qiyediy.com

### 更新日志

#### v1.0.0 (2024-12-20)
- ✅ 发布正式版本
- ✅ 完整的DIY编辑器
- ✅ 丰富的组件库
- ✅ 模板系统
- ✅ 系统管理功能

### 反馈建议

我们非常重视用户的反馈和建议，如果您在使用过程中遇到问题或有改进建议，请通过以下方式联系我们：

1. 发送邮件到 <EMAIL>
2. 在用户群中反馈问题
3. 提交在线工单

---

## 📄 版权信息

**QiyeDIY企业建站系统**  
Copyright © 2024 三只鱼网络科技  
All rights reserved.

本软件受版权法保护，未经授权不得复制、分发或修改。

---

*感谢您选择QiyeDIY企业建站系统！*

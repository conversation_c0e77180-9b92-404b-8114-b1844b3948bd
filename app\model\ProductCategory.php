<?php
/**
 * 三只鱼网络科技 | 开发者：韩总 | 创建时间：2024-12-19
 * 文件描述：产品分类模型 - ThinkPHP6企业级应用
 * 技术栈：PHP 8.0+ + ThinkPHP6 + MySQL + Redis
 * 版权所有：三只鱼网络科技有限公司
 */

namespace app\model;

use think\Model;

/**
 * 产品分类模型
 */
class ProductCategory extends Model
{
    // 数据表名
    protected $name = 'product_categories';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    // 字段类型转换
    protected $type = [
        'parent_id' => 'integer',
        'status' => 'integer',
        'sort_order' => 'integer',
    ];
    
    // 关联产品
    public function products()
    {
        return $this->hasMany(Product::class, 'category_id');
    }
    
    // 关联父分类
    public function parent()
    {
        return $this->belongsTo(self::class, 'parent_id');
    }
    
    // 关联子分类
    public function children()
    {
        return $this->hasMany(self::class, 'parent_id');
    }
    
    // 获取启用的分类
    public static function getActiveCategories()
    {
        return self::where('status', 1)
            ->order('sort_order', 'asc')
            ->order('id', 'asc')
            ->select();
    }
    
    // 获取顶级分类
    public static function getTopCategories()
    {
        return self::where('status', 1)
            ->where('parent_id', 0)
            ->order('sort_order', 'asc')
            ->order('id', 'asc')
            ->select();
    }
    
    // 获取分类树
    public static function getCategoryTree()
    {
        $categories = self::getActiveCategories();
        return self::buildTree($categories);
    }
    
    // 构建分类树
    private static function buildTree($categories, $parentId = 0)
    {
        $tree = [];
        foreach ($categories as $category) {
            if ($category['parent_id'] == $parentId) {
                $category['children'] = self::buildTree($categories, $category['id']);
                $tree[] = $category;
            }
        }
        return $tree;
    }
    
    // 检查slug唯一性
    public static function checkSlugUnique($slug, $excludeId = null)
    {
        $query = self::where('slug', $slug);
        if ($excludeId) {
            $query->where('id', '<>', $excludeId);
        }
        return !$query->find();
    }
    
    // 生成URL别名
    public static function generateSlug($name)
    {
        if (empty($name)) {
            return '';
        }
        
        $result = '';
        
        // 遍历每个字符
        for ($i = 0; $i < strlen($name); $i++) {
            $char = $name[$i];
            
            // 如果是英文字母或数字，直接保留
            if (preg_match('/[a-zA-Z0-9]/', $char)) {
                $result .= strtolower($char);
            }
            // 如果是空格或其他分隔符，转换为连字符
            elseif (preg_match('/[\s\-_]/', $char)) {
                $result .= '-';
            }
        }
        
        // 清理结果
        $result = preg_replace('/[-]+/', '-', $result);  // 多个连字符合并为一个
        $result = trim($result, '-'); // 去除首尾连字符
        $result = substr($result, 0, 50); // 限制长度
        
        // 如果结果为空或太短，使用时间戳
        if (empty($result) || strlen($result) < 2) {
            $result = 'category-' . date('YmdHi');
        }
        
        return $result;
    }
    
    // 获取分类产品数量
    public function getProductCountAttr($value, $data)
    {
        if (!isset($data['id'])) {
            return 0;
        }
        return Product::where('category_id', $data['id'])->count();
    }
    
    // 检查是否可以删除（没有产品）
    public function canDelete()
    {
        $productCount = Product::where('category_id', $this->id)->count();
        $childCount = self::where('parent_id', $this->id)->count();
        
        return $productCount === 0 && $childCount === 0;
    }
    
    // 获取面包屑导航
    public function getBreadcrumb()
    {
        $breadcrumb = [];
        $current = $this;
        
        while ($current) {
            array_unshift($breadcrumb, $current);
            $current = $current->parent;
        }
        
        return $breadcrumb;
    }
}

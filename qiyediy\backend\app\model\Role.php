<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 角色模型
 */

declare(strict_types=1);

namespace app\model;

use think\Model;
use think\model\relation\BelongsToMany;

/**
 * 角色模型
 * @property int $id 角色ID
 * @property string $name 角色名称
 * @property string $slug 角色标识
 * @property string $description 角色描述
 * @property array $permissions 权限列表
 * @property int $is_default 是否默认角色
 * @property int $sort_order 排序
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 */
class Role extends Model
{
    // 表名
    protected $name = 'roles';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'permissions' => 'json',
        'is_default' => 'integer',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // 追加字段
    protected $append = [
        'user_count'
    ];

    // 允许批量赋值的字段
    protected $field = [
        'name',
        'slug',
        'description',
        'permissions',
        'is_default',
        'sort_order'
    ];

    /**
     * 权限设置器
     * @param mixed $value
     * @return string
     */
    public function setPermissionsAttr($value): string
    {
        if (is_array($value)) {
            return json_encode($value, JSON_UNESCAPED_UNICODE);
        }
        return $value;
    }

    /**
     * 权限获取器
     * @param string $value
     * @return array
     */
    public function getPermissionsAttr(string $value): array
    {
        return json_decode($value, true) ?? [];
    }

    /**
     * 用户数量获取器
     * @return int
     */
    public function getUserCountAttr(): int
    {
        return UserRole::where('role_id', $this->id)->count();
    }

    /**
     * 关联用户
     * @return BelongsToMany
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, UserRole::class, 'user_id', 'role_id');
    }

    /**
     * 检查是否有权限
     * @param string $permission 权限标识
     * @return bool
     */
    public function hasPermission(string $permission): bool
    {
        return in_array($permission, $this->permissions);
    }

    /**
     * 添加权限
     * @param string|array $permissions 权限标识
     * @return bool
     */
    public function addPermissions($permissions): bool
    {
        if (!is_array($permissions)) {
            $permissions = [$permissions];
        }

        $currentPermissions = $this->permissions;
        $newPermissions = array_unique(array_merge($currentPermissions, $permissions));

        return $this->save(['permissions' => $newPermissions]);
    }

    /**
     * 移除权限
     * @param string|array $permissions 权限标识
     * @return bool
     */
    public function removePermissions($permissions): bool
    {
        if (!is_array($permissions)) {
            $permissions = [$permissions];
        }

        $currentPermissions = $this->permissions;
        $newPermissions = array_diff($currentPermissions, $permissions);

        return $this->save(['permissions' => array_values($newPermissions)]);
    }

    /**
     * 同步权限
     * @param array $permissions 权限列表
     * @return bool
     */
    public function syncPermissions(array $permissions): bool
    {
        return $this->save(['permissions' => $permissions]);
    }

    /**
     * 搜索范围
     * @param \think\db\Query $query
     * @param string $keyword
     * @return void
     */
    public function scopeSearch($query, string $keyword): void
    {
        if (!empty($keyword)) {
            $query->where(function ($q) use ($keyword) {
                $q->whereLike('name', "%{$keyword}%")
                  ->whereOr('slug', 'like', "%{$keyword}%")
                  ->whereOr('description', 'like', "%{$keyword}%");
            });
        }
    }

    /**
     * 默认角色范围
     * @param \think\db\Query $query
     * @param int $isDefault
     * @return void
     */
    public function scopeDefault($query, int $isDefault): void
    {
        $query->where('is_default', $isDefault);
    }

    /**
     * 创建角色
     * @param array $data 角色数据
     * @return static
     */
    public static function createRole(array $data): self
    {
        // 检查角色标识是否存在
        if (self::where('slug', $data['slug'])->exists()) {
            throw new \Exception('角色标识已存在');
        }

        // 检查角色名称是否存在
        if (self::where('name', $data['name'])->exists()) {
            throw new \Exception('角色名称已存在');
        }

        return self::create($data);
    }

    /**
     * 更新角色
     * @param array $data 角色数据
     * @return bool
     */
    public function updateRole(array $data): bool
    {
        // 检查角色标识是否存在（排除自己）
        if (isset($data['slug']) && 
            self::where('slug', $data['slug'])
                ->where('id', '<>', $this->id)
                ->exists()) {
            throw new \Exception('角色标识已存在');
        }

        // 检查角色名称是否存在（排除自己）
        if (isset($data['name']) && 
            self::where('name', $data['name'])
                ->where('id', '<>', $this->id)
                ->exists()) {
            throw new \Exception('角色名称已存在');
        }

        return $this->save($data);
    }

    /**
     * 获取默认角色
     * @return static|null
     */
    public static function getDefaultRole(): ?self
    {
        return self::where('is_default', 1)->find();
    }

    /**
     * 设置为默认角色
     * @return bool
     */
    public function setAsDefault(): bool
    {
        // 先取消其他角色的默认状态
        self::where('is_default', 1)->update(['is_default' => 0]);

        // 设置当前角色为默认
        return $this->save(['is_default' => 1]);
    }

    /**
     * 取消默认角色
     * @return bool
     */
    public function unsetDefault(): bool
    {
        return $this->save(['is_default' => 0]);
    }

    /**
     * 删除角色前的检查
     * @return bool
     */
    public function beforeDelete(): bool
    {
        // 检查是否有用户使用此角色
        if ($this->user_count > 0) {
            throw new \Exception('该角色下还有用户，无法删除');
        }

        // 检查是否为默认角色
        if ($this->is_default) {
            throw new \Exception('默认角色无法删除');
        }

        return true;
    }

    /**
     * 删除角色后的清理
     * @return void
     */
    public function afterDelete(): void
    {
        // 删除用户角色关联
        UserRole::where('role_id', $this->id)->delete();
    }
}

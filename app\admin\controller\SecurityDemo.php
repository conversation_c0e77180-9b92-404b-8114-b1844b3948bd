<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * 安全防护系统演示控制器 - ThinkPHP6企业级应用
 * 功能：展示安全防护系统的正确使用方法
 */

namespace app\admin\controller;

use app\BaseController;
use app\service\FileSecurityService;
use app\validate\SecurityValidate;
use app\middleware\SqlInjectionMiddleware;
use think\facade\View;
use think\facade\Session;

class SecurityDemo extends BaseController
{
    /**
     * 安全文件上传示例
     */
    public function secureUpload()
    {
        if (request()->isPost()) {
            $file = request()->file('upload_file');
            
            if (!$file) {
                return json(['code' => 400, 'msg' => '请选择要上传的文件']);
            }
            
            // 使用统一的文件安全服务
            $result = FileSecurityService::secureUpload($file, [
                'check_extension' => true,
                'check_mime' => true,
                'check_size' => true,
                'check_magic' => true,
                'check_content' => true,
                'check_image' => true,
                'strict_mode' => true,
                'base_dir' => 'uploads/secure',
            ]);
            
            if ($result['success']) {
                return json([
                    'code' => 200,
                    'msg' => '文件上传成功',
                    'data' => $result['data']
                ]);
            } else {
                return json([
                    'code' => 400,
                    'msg' => $result['message']
                ]);
            }
        }
        
        return View::fetch();
    }
    
    /**
     * 批量文件上传示例
     */
    public function batchUpload()
    {
        if (request()->isPost()) {
            $files = request()->file('batch_files');
            
            if (!$files || !is_array($files)) {
                return json(['code' => 400, 'msg' => '请选择要上传的文件']);
            }
            
            // 批量验证文件安全性
            $batchValidation = FileSecurityService::validateMultipleFiles($files);
            
            if (!$batchValidation['all_valid']) {
                $errors = [];
                foreach ($batchValidation['results'] as $index => $result) {
                    if (!$result['valid']) {
                        $errors[] = "文件 " . ($index + 1) . ": " . implode(', ', $result['errors']);
                    }
                }
                
                return json([
                    'code' => 400,
                    'msg' => '部分文件验证失败',
                    'errors' => $errors,
                    'stats' => [
                        'total' => $batchValidation['total_files'],
                        'valid' => $batchValidation['valid_files'],
                        'invalid' => $batchValidation['invalid_files']
                    ]
                ]);
            }
            
            // 所有文件验证通过，开始上传
            $uploadResults = [];
            foreach ($files as $index => $file) {
                $result = FileSecurityService::secureUpload($file);
                $uploadResults[] = $result;
            }
            
            return json([
                'code' => 200,
                'msg' => '批量上传完成',
                'data' => $uploadResults
            ]);
        }
        
        return View::fetch();
    }
    
    /**
     * 安全内容验证示例
     */
    public function secureContent()
    {
        if (request()->isPost()) {
            $data = request()->param();
            
            // 使用安全验证器
            $validate = new SecurityValidate();
            
            // 验证内容安全性
            if (!$validate->scene('content')->check($data)) {
                return json(['code' => 400, 'msg' => $validate->getError()]);
            }
            
            // 批量验证数据安全性
            $securityCheck = SecurityValidate::validateDataSecurity($data, [
                'title' => 'checkXss',
                'content' => 'checkSqlInjection|checkXss',
                'author' => 'checkUsernameSafe',
                'email' => 'checkEmailSafe',
                'website' => 'checkUrlSafe',
            ]);
            
            if (!$securityCheck['valid']) {
                return json([
                    'code' => 400,
                    'msg' => '数据安全检查失败',
                    'errors' => $securityCheck['errors']
                ]);
            }
            
            // 数据安全，可以保存
            return json([
                'code' => 200,
                'msg' => '内容保存成功',
                'data' => [
                    'title' => $data['title'],
                    'content_length' => strlen($data['content']),
                    'security_check' => 'passed'
                ]
            ]);
        }
        
        return View::fetch();
    }
    
    /**
     * 用户注册安全示例
     */
    public function secureRegister()
    {
        if (request()->isPost()) {
            $data = request()->param();
            
            // 验证用户数据
            $validate = new SecurityValidate();
            if (!$validate->scene('user')->check($data)) {
                return json(['code' => 400, 'msg' => $validate->getError()]);
            }
            
            // 检查密码强度
            $passwordCheck = $validate->checkPasswordStrength($data['password'], '', []);
            if ($passwordCheck !== true) {
                return json(['code' => 400, 'msg' => $passwordCheck]);
            }
            
            // 安全哈希密码
            $hashedPassword = SecurityValidate::hashPassword($data['password']);
            
            // 生成安全令牌
            $token = SecurityValidate::generateSecureToken(32);
            
            return json([
                'code' => 200,
                'msg' => '注册成功',
                'data' => [
                    'username' => $data['username'],
                    'email' => $data['email'],
                    'password_hash' => substr($hashedPassword, 0, 20) . '...',
                    'token' => $token,
                    'security_level' => 'high'
                ]
            ]);
        }
        
        return View::fetch();
    }
    
    /**
     * 文件安全报告示例
     */
    public function fileSecurityReport()
    {
        $filePath = request()->param('file_path');
        
        if (!$filePath) {
            return json(['code' => 400, 'msg' => '请提供文件路径']);
        }
        
        $fullPath = public_path() . $filePath;
        
        if (!file_exists($fullPath)) {
            return json(['code' => 404, 'msg' => '文件不存在']);
        }
        
        // 获取文件安全报告
        $report = FileSecurityService::getSecurityReport($fullPath);
        
        return json([
            'code' => 200,
            'msg' => '安全报告生成成功',
            'data' => $report
        ]);
    }
    
    /**
     * SQL注入统计示例
     */
    public function sqlInjectionStats()
    {
        $days = request()->param('days', 7);
        
        // 获取SQL注入统计
        $stats = SqlInjectionMiddleware::getInjectionStats($days);
        
        return json([
            'code' => 200,
            'msg' => '统计数据获取成功',
            'data' => $stats
        ]);
    }
    
    /**
     * 安全配置检查
     */
    public function securityConfigCheck()
    {
        $config = config('security');
        
        $checks = [
            'csrf_enabled' => $config['csrf']['enable'] ?? false,
            'xss_enabled' => $config['xss']['enable'] ?? false,
            'upload_security' => $config['upload']['enable_security_check'] ?? false,
            'sql_injection_protection' => $config['sql_injection']['enable'] ?? false,
            'rate_limit_enabled' => $config['rate_limit']['enable'] ?? false,
            'headers_security' => $config['headers']['enable'] ?? false,
        ];
        
        $securityScore = (count(array_filter($checks)) / count($checks)) * 100;
        
        return json([
            'code' => 200,
            'msg' => '安全配置检查完成',
            'data' => [
                'checks' => $checks,
                'security_score' => round($securityScore, 2),
                'recommendations' => $this->getSecurityRecommendations($checks)
            ]
        ]);
    }
    
    /**
     * 获取安全建议
     */
    private function getSecurityRecommendations($checks)
    {
        $recommendations = [];
        
        if (!$checks['csrf_enabled']) {
            $recommendations[] = '建议启用CSRF保护';
        }
        
        if (!$checks['xss_enabled']) {
            $recommendations[] = '建议启用XSS防护';
        }
        
        if (!$checks['upload_security']) {
            $recommendations[] = '建议启用文件上传安全检查';
        }
        
        if (!$checks['sql_injection_protection']) {
            $recommendations[] = '建议启用SQL注入防护';
        }
        
        if (!$checks['rate_limit_enabled']) {
            $recommendations[] = '建议启用请求频率限制';
        }
        
        if (!$checks['headers_security']) {
            $recommendations[] = '建议启用安全头设置';
        }
        
        if (empty($recommendations)) {
            $recommendations[] = '安全配置良好，建议定期检查和更新';
        }
        
        return $recommendations;
    }
    
    /**
     * 清理恶意文件示例
     */
    public function cleanMaliciousFile()
    {
        $filePath = request()->param('file_path');
        
        if (!$filePath) {
            return json(['code' => 400, 'msg' => '请提供文件路径']);
        }
        
        $fullPath = public_path() . $filePath;
        
        // 清理恶意文件
        $result = FileSecurityService::cleanMaliciousFile($fullPath);
        
        return json([
            'code' => $result['success'] ? 200 : 400,
            'msg' => $result['message']
        ]);
    }
    
    /**
     * 生成CSRF令牌示例
     */
    public function generateCsrfToken()
    {
        $token = \app\middleware\SecurityMiddleware::generateCsrfToken();
        
        return json([
            'code' => 200,
            'msg' => 'CSRF令牌生成成功',
            'data' => [
                'token' => $token,
                'token_name' => '__token__',
                'expires_in' => '2小时'
            ]
        ]);
    }
}

{assign name="pageTitle" value="$pageData.pageTitle" /}
{include file="common/header" /}
<link rel="stylesheet" href="/assets/css/contact.css">
<!-- 科技感页面标题区域 -->
<section class="tech-hero-section">
    <div class="tech-hero-background">
        <div class="tech-grid-overlay"></div>
        <div class="tech-particles"></div>
        <!-- 装饰元素 -->
        <div class="hero-decorations">
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
            <div class="floating-icon icon-1"><i class="fas fa-phone"></i></div>
            <div class="floating-icon icon-2"><i class="fas fa-envelope"></i></div>
            <div class="floating-icon icon-3"><i class="fas fa-map-marker-alt"></i></div>
            <div class="floating-icon icon-4"><i class="fas fa-comments"></i></div>
            <div class="floating-icon icon-5"><i class="fas fa-headphones"></i></div>
        </div>
    </div>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="tech-hero-content text-center">
                    <div class="tech-icon-wrapper">
                        <i class="fas fa-satellite-dish tech-main-icon"></i>
                    </div>
                    <h1 class="tech-hero-title">
                        <span class="tech-text-gradient">联系我们</span>
                    </h1>
                    <p class="tech-hero-subtitle">
                        连接未来，共创数字化解决方案
                    </p>
                    <nav aria-label="breadcrumb" class="tech-breadcrumb">
                        <div class="tech-breadcrumb-container">
                            <div class="breadcrumb-item">
                                <a href="/" class="breadcrumb-link">
                                    <i class="fas fa-home breadcrumb-icon"></i>
                                    <span>首页</span>
                                </a>
                            </div>
                            <div class="breadcrumb-separator">
                                <i class="fas fa-chevron-right separator-icon"></i>
                            </div>
                            <div class="breadcrumb-item active">
                                <span class="breadcrumb-current">
                                    <i class="fas fa-comments breadcrumb-icon"></i>
                                    <span>联系我们</span>
                                </span>
                            </div>
                        </div>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 科技感联系表单区域 -->
<section class="tech-form-section">
    <div class="tech-form-background">
        <div class="tech-grid-overlay"></div>
        <div class="tech-particles"></div>
        <!-- 装饰元素 -->
        <div class="hero-decorations">
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
            <div class="floating-icon icon-1"><i class="fas fa-microchip"></i></div>
            <div class="floating-icon icon-2"><i class="fas fa-database"></i></div>
            <div class="floating-icon icon-3"><i class="fas fa-server"></i></div>
            <div class="floating-icon icon-4"><i class="fas fa-wifi"></i></div>
            <div class="floating-icon icon-5"><i class="fas fa-bolt"></i></div>
        </div>
    </div>
    <div class="container-fluid" style="padding-bottom: 60px;">
        <div class="tech-form-wrapper">
            <div class="tech-form-header text-center mb-5">

                <div class="tech-form-title-container">
                    <h2 class="tech-form-title">
                        <span class="tech-text-gradient">智能咨询系统</span>
                    </h2>
                    <p class="tech-form-subtitle">
                        AI驱动的智能响应系统，为您提供专业的技术咨询服务
                    </p>
                    <div class="tech-status-indicator">
                        <span class="status-dot"></span>
                        <span class="status-text">系统在线 · 实时响应</span>
                    </div>
                </div>
            </div>

            <form class="tech-contact-form" action="/contact/submit" method="POST">
                <!-- 表单网格布局 -->
                <div class="tech-form-grid">
                    <!-- 第一行：姓名和邮箱 -->
                    <div class="tech-form-row">
                        <div class="tech-input-group">
                            <div class="tech-input-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            <input type="text" class="tech-form-control" id="name" name="name" required>
                            <label for="name" class="tech-form-label">姓名 *</label>
                            <div class="tech-input-border"></div>
                        </div>

                        <div class="tech-input-group">
                            <div class="tech-input-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <input type="email" class="tech-form-control" id="email" name="email" required>
                            <label for="email" class="tech-form-label">邮箱地址 *</label>
                            <div class="tech-input-border"></div>
                        </div>
                    </div>

                    <!-- 第二行：电话和公司 -->
                    <div class="tech-form-row">
                        <div class="tech-input-group">
                            <div class="tech-input-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <input type="tel" class="tech-form-control" id="phone" name="phone">
                            <label for="phone" class="tech-form-label">联系电话</label>
                            <div class="tech-input-border"></div>
                        </div>

                        <div class="tech-input-group">
                            <div class="tech-input-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <input type="text" class="tech-form-control" id="company" name="company">
                            <label for="company" class="tech-form-label">公司名称</label>
                            <div class="tech-input-border"></div>
                        </div>
                    </div>

                    <!-- 第三行：咨询主题（全宽） -->
                    <div class="tech-form-row tech-form-row-full">
                        <div class="tech-select-group">
                            <div class="tech-input-icon">
                                <i class="fas fa-tag"></i>
                            </div>
                            <select class="tech-form-select" id="subject" name="subject" required>
                                <option value="" disabled selected>请选择咨询主题</option>
                                <option value="网站开发">网站开发</option>
                                <option value="移动应用">移动应用</option>
                                <option value="系统集成">系统集成</option>
                                <option value="云服务">云服务</option>
                                <option value="技术支持">技术支持</option>
                                <option value="商务合作">商务合作</option>
                                <option value="其他咨询">其他咨询</option>
                            </select>
                            <div class="tech-input-border"></div>
                        </div>
                    </div>

                    <!-- 第四行：详细描述（全宽） -->
                    <div class="tech-form-row tech-form-row-full">
                        <div class="tech-textarea-group">
                            <div class="tech-input-icon">
                                <i class="fas fa-comment-alt"></i>
                            </div>
                            <textarea class="tech-form-textarea" id="message" name="message" rows="6" required></textarea>
                            <label for="message" class="tech-form-label">详细需求描述 *</label>
                            <div class="tech-input-border"></div>
                            <div class="tech-char-counter">
                                <span class="char-count">0</span> / 500
                            </div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="tech-form-row tech-form-row-full tech-form-submit">
                        <button type="submit" class="tech-submit-btn">
                            <span class="btn-text">
                                <i class="fas fa-paper-plane"></i>
                                发送咨询
                            </span>
                            <div class="btn-glow"></div>
                            <div class="btn-particles"></div>
                        </button>
                    </div>
                </div>
            </form>

            <!-- 消息提示区域 -->
            <div id="form-message" class="mt-4" style="display: none;"></div>
        </div>
    </div>

    <!-- 底部融合区域 -->
    <div class="tech-form-bottom-fusion"></div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 本地化图标初始化 - 使用Font Awesome替代Lucide
    // 如果需要Lucide图标，请确保已加载lucide.js文件

    const contactForm = document.querySelector('.tech-contact-form');
    const messageDiv = document.getElementById('form-message');
    const messageTextarea = document.getElementById('message');
    const charCount = document.querySelector('.char-count');

    // 字符计数功能
    if (messageTextarea && charCount) {
        messageTextarea.addEventListener('input', function() {
            const count = this.value.length;
            charCount.textContent = count;

            // 字符数颜色变化
            if (count > 400) {
                charCount.style.color = '#ef4444';
            } else if (count > 300) {
                charCount.style.color = '#f59e0b';
            } else {
                charCount.style.color = '#4fc3f7';
            }
        });
    }

    // 表单输入动画效果
    document.querySelectorAll('.tech-form-control, .tech-form-select, .tech-form-textarea').forEach(input => {
        function updateLabelState() {
            const hasValue = input.value && input.value.trim() !== '';
            if (hasValue) {
                input.parentElement.classList.add('has-value');
            } else {
                input.parentElement.classList.remove('has-value');
            }
        }

        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
            updateLabelState();
        });

        input.addEventListener('input', function() {
            updateLabelState();
        });

        // 检查初始值
        updateLabelState();
    });

    // 表单提交处理
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const submitBtn = this.querySelector('.tech-submit-btn');
            const btnText = submitBtn.querySelector('.btn-text');
            const originalText = btnText.innerHTML;

            // 显示加载状态
            btnText.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发送中...';
            submitBtn.disabled = true;
            submitBtn.classList.add('loading');

            // 创建FormData对象
            const formData = new FormData(this);

            // 发送AJAX请求
            fetch('/contact/submit', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                    contactForm.reset();
                    // 重置字符计数
                    if (charCount) charCount.textContent = '0';
                    // 移除focused和has-value类
                    document.querySelectorAll('.tech-input-group, .tech-select-group, .tech-textarea-group').forEach(group => {
                        group.classList.remove('focused', 'has-value');
                    });
                } else {
                    showMessage(data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage('网络连接异常，请检查网络后重试', 'danger');
            })
            .finally(() => {
                // 恢复按钮状态
                btnText.innerHTML = originalText;
                submitBtn.disabled = false;
                submitBtn.classList.remove('loading');
            });
        });
    }

    function showMessage(message, type) {
        const alertClass = type === 'success' ? 'tech-alert-success' : 'tech-alert-danger';

        messageDiv.innerHTML = `
            <div class="tech-alert ${alertClass}">
                <div class="alert-content">
                    <span>${message}</span>
                </div>
                <button class="tech-alert-close" onclick="hideMessage()">&times;</button>
            </div>
        `;

        messageDiv.style.display = 'block';

        // 滚动到消息区域
        setTimeout(() => {
            messageDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 100);

        // 成功消息3秒后自动隐藏，错误消息不自动隐藏
        if (type === 'success') {
            setTimeout(() => {
                hideMessage();
            }, 3000);
        }
    }

    // 隐藏消息函数
    window.hideMessage = function() {
        if (messageDiv) {
            messageDiv.style.opacity = '0';
            messageDiv.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                messageDiv.style.display = 'none';
                messageDiv.style.opacity = '1';
                messageDiv.style.transform = 'translateY(0)';
            }, 300);
        }
    };
});
</script>

{include file="common/footer" /}

<!-- 消息提示组件 -->
{include file="admin/common/message-styles"}
{if condition="isset($message) && $message"}
    <div class="alert alert-{$messageType == 'error' ? 'danger' : ($messageType ?: 'info')} alert-dismissible fade show" role="alert" id="alertMessage">
        <div class="alert-icon">
            <i class="fas fa-{$messageType == 'success' ? 'check-circle' : ($messageType == 'error' || $messageType == 'danger' ? 'exclamation-circle' : ($messageType == 'warning' ? 'exclamation-triangle' : 'info-circle'))}"></i>
        </div>
        <div class="alert-content">
            {$message}
        </div>
    </div>
{/if}

<!-- 统一删除确认模态框 -->
<div class="delete-modal" id="deleteModal">
    <div class="modal-content">
        <div class="modal-header">
            <i class="fas fa-exclamation-triangle"></i>
            <h4>确认删除</h4>
        </div>
        <div class="modal-body">
            <p>您确定要删除以下项目吗？</p>
            <div class="delete-item-title" id="deleteItemTitle">
                <!-- 这里将显示要删除的项目标题 -->
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn-cancel" onclick="closeDeleteModal()">
                <i class="fas fa-times"></i>
                取消
            </button>
            <button type="button" class="btn-confirm-delete" id="confirmDeleteBtn">
                <i class="fas fa-trash"></i>
                确认删除
            </button>
        </div>
    </div>
</div>

<script>
// 简化的消息处理 - 主要功能已移至admin.js
function showMessage(message, type = 'info') {
    const existingAlert = document.querySelector('.alert');
    if (existingAlert) existingAlert.remove();

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.id = 'alertMessage';
    alertDiv.innerHTML = `
        <div class="alert-icon">
            <i class="fas fa-${type === 'success' ? 'check-circle' : (type === 'danger' ? 'exclamation-circle' : (type === 'warning' ? 'exclamation-triangle' : 'info-circle'))}"></i>
        </div>
        <div class="alert-content">${message}</div>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv && alertDiv.parentElement) {
            alertDiv.style.transition = 'all 0.5s ease-out';
            alertDiv.style.opacity = '0';
            setTimeout(() => alertDiv.remove(), 500);
        }
    }, 1500);
}

function closeDeleteModal() {
    const modal = document.getElementById('deleteModal');
    if (modal) modal.classList.remove('show');
}

// 页面加载时自动消失消息
document.addEventListener('DOMContentLoaded', function() {
    const alertMessage = document.getElementById('alertMessage');
    if (alertMessage) setTimeout(() => alertMessage.remove(), 1500);
});
</script>
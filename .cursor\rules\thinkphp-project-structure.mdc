---
description:
globs:
alwaysApply: false
---
# ThinkPHP 8 Project Structure Guide

This is a ThinkPHP 8 enterprise website management system with DIY page builder functionality.

## Core Framework Files

- [composer.json](mdc:composer.json) - Project dependencies and autoload configuration
- [think](mdc:think) - Command line tool entry point
- [app/BaseController.php](mdc:app/BaseController.php) - Base controller with common functionality
- [app/common.php](mdc:app/common.php) - Global helper functions
- [app/ExceptionHandle.php](mdc:app/ExceptionHandle.php) - Exception handling logic

## Application Structure

### Controllers
- `app/controller/` - Frontend controllers
- `app/admin/controller/` - Backend admin controllers  
- `app/api/controller/` - API endpoints

### Models & Services
- `app/model/` - Database models using think-orm
- `app/service/` - Business logic services
- `app/validate/` - Input validation rules

### Views & Assets
- `app/view/` - Template files
- `public/assets/` - Frontend resources (CSS, JS, images)
- `public/diy/` - DIY page builder components
- `public/uploads/` - User uploaded files

## Configuration
- `config/` - All configuration files
- `.example.env` - Environment variables template
- `route/` - Route definitions

## Database
- [website.sql](mdc:website.sql) - Database schema and initial data
- `database/` - Database related files

## Key Features Architecture

### DIY Page Builder
- Components located in `public/diy/`
- 5 custom components: text blocks, statistics, team intro, testimonials, contact info
- Visual editor with real-time preview
- Template type filtering and categorization

### Content Management
- News management with categories
- Case studies with industry classification  
- Image management with batch upload
- Carousel/banner management

### Admin System
- Authentication and permissions
- System settings
- Contact form handling

## Development Guidelines

- Follow PSR-2 naming conventions and PSR-4 autoloading
- Use think-orm for database operations
- Implement responsive design for all components
- Include SEO optimization features
- Test all functionality before deployment

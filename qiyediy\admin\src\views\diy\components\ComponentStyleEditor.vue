<!--
  三只鱼网络科技 | 韩总 | 2024-12-20
  QiyeDIY企业建站系统 - 组件样式编辑器
-->

<template>
  <div class="component-style-editor">
    <div class="style-header">
      <h4 class="style-title">样式设置</h4>
      <el-button size="small" text @click="handleReset">重置样式</el-button>
    </div>

    <div class="style-content">
      <!-- 布局样式 -->
      <div class="style-group">
        <h5 class="group-title">布局</h5>
        
        <el-form :model="layoutStyles" label-width="80px" size="small">
          <el-form-item label="显示方式">
            <el-select v-model="layoutStyles.display" @change="handleStyleChange">
              <el-option label="块级" value="block" />
              <el-option label="行内块" value="inline-block" />
              <el-option label="弹性布局" value="flex" />
              <el-option label="网格布局" value="grid" />
              <el-option label="隐藏" value="none" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="位置">
            <el-select v-model="layoutStyles.position" @change="handleStyleChange">
              <el-option label="静态" value="static" />
              <el-option label="相对" value="relative" />
              <el-option label="绝对" value="absolute" />
              <el-option label="固定" value="fixed" />
              <el-option label="粘性" value="sticky" />
            </el-select>
          </el-form-item>
          
          <el-form-item v-if="layoutStyles.position !== 'static'" label="层级">
            <el-input-number
              v-model="layoutStyles.zIndex"
              :min="-999"
              :max="999"
              controls-position="right"
              @change="handleStyleChange"
            />
          </el-form-item>
          
          <el-form-item label="浮动">
            <el-select v-model="layoutStyles.float" @change="handleStyleChange">
              <el-option label="无" value="none" />
              <el-option label="左浮动" value="left" />
              <el-option label="右浮动" value="right" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="清除浮动">
            <el-select v-model="layoutStyles.clear" @change="handleStyleChange">
              <el-option label="无" value="none" />
              <el-option label="左侧" value="left" />
              <el-option label="右侧" value="right" />
              <el-option label="两侧" value="both" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 尺寸样式 -->
      <div class="style-group">
        <h5 class="group-title">尺寸</h5>
        
        <el-form :model="sizeStyles" label-width="80px" size="small">
          <el-form-item label="宽度">
            <div class="size-input">
              <el-input
                v-model="sizeStyles.width"
                placeholder="auto"
                @input="handleStyleChange"
              />
              <el-select v-model="sizeStyles.widthUnit" @change="handleStyleChange">
                <el-option label="px" value="px" />
                <el-option label="%" value="%" />
                <el-option label="em" value="em" />
                <el-option label="rem" value="rem" />
                <el-option label="vw" value="vw" />
              </el-select>
            </div>
          </el-form-item>
          
          <el-form-item label="高度">
            <div class="size-input">
              <el-input
                v-model="sizeStyles.height"
                placeholder="auto"
                @input="handleStyleChange"
              />
              <el-select v-model="sizeStyles.heightUnit" @change="handleStyleChange">
                <el-option label="px" value="px" />
                <el-option label="%" value="%" />
                <el-option label="em" value="em" />
                <el-option label="rem" value="rem" />
                <el-option label="vh" value="vh" />
              </el-select>
            </div>
          </el-form-item>
          
          <el-form-item label="最小宽度">
            <el-input-number
              v-model="sizeStyles.minWidth"
              :min="0"
              controls-position="right"
              @change="handleStyleChange"
            />
            <span class="unit">px</span>
          </el-form-item>
          
          <el-form-item label="最大宽度">
            <el-input-number
              v-model="sizeStyles.maxWidth"
              :min="0"
              controls-position="right"
              @change="handleStyleChange"
            />
            <span class="unit">px</span>
          </el-form-item>
          
          <el-form-item label="最小高度">
            <el-input-number
              v-model="sizeStyles.minHeight"
              :min="0"
              controls-position="right"
              @change="handleStyleChange"
            />
            <span class="unit">px</span>
          </el-form-item>
          
          <el-form-item label="最大高度">
            <el-input-number
              v-model="sizeStyles.maxHeight"
              :min="0"
              controls-position="right"
              @change="handleStyleChange"
            />
            <span class="unit">px</span>
          </el-form-item>
        </el-form>
      </div>

      <!-- 背景样式 -->
      <div class="style-group">
        <h5 class="group-title">背景</h5>
        
        <el-form :model="backgroundStyles" label-width="80px" size="small">
          <el-form-item label="背景颜色">
            <el-color-picker
              v-model="backgroundStyles.backgroundColor"
              show-alpha
              @change="handleStyleChange"
            />
          </el-form-item>
          
          <el-form-item label="背景图片">
            <div class="background-image">
              <el-upload
                class="image-uploader"
                :action="uploadAction"
                :headers="uploadHeaders"
                :show-file-list="false"
                :before-upload="beforeImageUpload"
                :on-success="handleImageSuccess"
              >
                <img v-if="backgroundStyles.backgroundImage" :src="backgroundStyles.backgroundImage" class="uploaded-image" />
                <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
              </el-upload>
              <el-input
                v-model="backgroundStyles.backgroundImage"
                placeholder="或输入图片URL"
                @input="handleStyleChange"
              />
            </div>
          </el-form-item>
          
          <el-form-item v-if="backgroundStyles.backgroundImage" label="背景尺寸">
            <el-select v-model="backgroundStyles.backgroundSize" @change="handleStyleChange">
              <el-option label="自动" value="auto" />
              <el-option label="覆盖" value="cover" />
              <el-option label="包含" value="contain" />
              <el-option label="拉伸" value="100% 100%" />
            </el-select>
          </el-form-item>
          
          <el-form-item v-if="backgroundStyles.backgroundImage" label="背景位置">
            <el-select v-model="backgroundStyles.backgroundPosition" @change="handleStyleChange">
              <el-option label="居中" value="center" />
              <el-option label="左上" value="left top" />
              <el-option label="右上" value="right top" />
              <el-option label="左下" value="left bottom" />
              <el-option label="右下" value="right bottom" />
              <el-option label="左中" value="left center" />
              <el-option label="右中" value="right center" />
              <el-option label="上中" value="center top" />
              <el-option label="下中" value="center bottom" />
            </el-select>
          </el-form-item>
          
          <el-form-item v-if="backgroundStyles.backgroundImage" label="背景重复">
            <el-select v-model="backgroundStyles.backgroundRepeat" @change="handleStyleChange">
              <el-option label="不重复" value="no-repeat" />
              <el-option label="重复" value="repeat" />
              <el-option label="水平重复" value="repeat-x" />
              <el-option label="垂直重复" value="repeat-y" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 文字样式 -->
      <div class="style-group">
        <h5 class="group-title">文字</h5>
        
        <el-form :model="textStyles" label-width="80px" size="small">
          <el-form-item label="字体族">
            <el-select v-model="textStyles.fontFamily" @change="handleStyleChange">
              <el-option label="系统默认" value="" />
              <el-option label="微软雅黑" value="'Microsoft YaHei', sans-serif" />
              <el-option label="宋体" value="SimSun, serif" />
              <el-option label="黑体" value="SimHei, sans-serif" />
              <el-option label="Arial" value="Arial, sans-serif" />
              <el-option label="Times" value="'Times New Roman', serif" />
              <el-option label="Courier" value="'Courier New', monospace" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="字体大小">
            <el-input-number
              v-model="textStyles.fontSize"
              :min="8"
              :max="72"
              controls-position="right"
              @change="handleStyleChange"
            />
            <span class="unit">px</span>
          </el-form-item>
          
          <el-form-item label="字体粗细">
            <el-select v-model="textStyles.fontWeight" @change="handleStyleChange">
              <el-option label="正常" value="normal" />
              <el-option label="粗体" value="bold" />
              <el-option label="100" value="100" />
              <el-option label="200" value="200" />
              <el-option label="300" value="300" />
              <el-option label="400" value="400" />
              <el-option label="500" value="500" />
              <el-option label="600" value="600" />
              <el-option label="700" value="700" />
              <el-option label="800" value="800" />
              <el-option label="900" value="900" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="字体样式">
            <el-select v-model="textStyles.fontStyle" @change="handleStyleChange">
              <el-option label="正常" value="normal" />
              <el-option label="斜体" value="italic" />
              <el-option label="倾斜" value="oblique" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="文字颜色">
            <el-color-picker
              v-model="textStyles.color"
              @change="handleStyleChange"
            />
          </el-form-item>
          
          <el-form-item label="行高">
            <el-input-number
              v-model="textStyles.lineHeight"
              :min="0.5"
              :max="5"
              :step="0.1"
              controls-position="right"
              @change="handleStyleChange"
            />
          </el-form-item>
          
          <el-form-item label="字间距">
            <el-input-number
              v-model="textStyles.letterSpacing"
              :min="-10"
              :max="10"
              :step="0.1"
              controls-position="right"
              @change="handleStyleChange"
            />
            <span class="unit">px</span>
          </el-form-item>
          
          <el-form-item label="对齐方式">
            <el-radio-group v-model="textStyles.textAlign" @change="handleStyleChange">
              <el-radio-button label="left">左对齐</el-radio-button>
              <el-radio-button label="center">居中</el-radio-button>
              <el-radio-button label="right">右对齐</el-radio-button>
              <el-radio-button label="justify">两端对齐</el-radio-button>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="文字装饰">
            <el-checkbox-group v-model="textDecorations" @change="handleTextDecorationChange">
              <el-checkbox label="underline">下划线</el-checkbox>
              <el-checkbox label="overline">上划线</el-checkbox>
              <el-checkbox label="line-through">删除线</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          
          <el-form-item label="文字转换">
            <el-select v-model="textStyles.textTransform" @change="handleStyleChange">
              <el-option label="无" value="none" />
              <el-option label="大写" value="uppercase" />
              <el-option label="小写" value="lowercase" />
              <el-option label="首字母大写" value="capitalize" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 动画效果 -->
      <div class="style-group">
        <h5 class="group-title">动画</h5>
        
        <el-form :model="animationStyles" label-width="80px" size="small">
          <el-form-item label="过渡效果">
            <el-select v-model="animationStyles.transition" @change="handleStyleChange">
              <el-option label="无" value="" />
              <el-option label="全部属性" value="all 0.3s ease" />
              <el-option label="透明度" value="opacity 0.3s ease" />
              <el-option label="变换" value="transform 0.3s ease" />
              <el-option label="颜色" value="color 0.3s ease" />
              <el-option label="背景" value="background 0.3s ease" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="悬停效果">
            <el-select v-model="animationStyles.hoverEffect" @change="handleStyleChange">
              <el-option label="无" value="" />
              <el-option label="放大" value="scale" />
              <el-option label="上移" value="translateY" />
              <el-option label="阴影" value="shadow" />
              <el-option label="透明度" value="opacity" />
              <el-option label="旋转" value="rotate" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="透明度">
            <el-slider
              v-model="animationStyles.opacity"
              :min="0"
              :max="1"
              :step="0.1"
              show-input
              @change="handleStyleChange"
            />
          </el-form-item>
          
          <el-form-item label="变换">
            <el-input
              v-model="animationStyles.transform"
              placeholder="如: rotate(45deg) scale(1.1)"
              @input="handleStyleChange"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 自定义CSS -->
      <div class="style-group">
        <h5 class="group-title">自定义CSS</h5>
        
        <el-form size="small">
          <el-form-item>
            <el-input
              v-model="customCSS"
              type="textarea"
              :rows="6"
              placeholder="输入自定义CSS样式..."
              @input="handleCustomCSSChange"
            />
          </el-form-item>
          
          <el-form-item>
            <div class="css-tips">
              <el-icon><InfoFilled /></el-icon>
              <span>提示：请输入有效的CSS属性，如 color: red; font-size: 16px;</span>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useUserStore } from '@/store/modules/user'
import type { DiyComponent } from '@/api/diy'
import { Plus, InfoFilled } from '@element-plus/icons-vue'

interface Props {
  component: DiyComponent
}

interface Emits {
  (e: 'update', component: DiyComponent): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const userStore = useUserStore()

// 样式数据
const layoutStyles = reactive({
  display: 'block',
  position: 'static',
  zIndex: 0,
  float: 'none',
  clear: 'none'
})

const sizeStyles = reactive({
  width: '',
  widthUnit: 'px',
  height: '',
  heightUnit: 'px',
  minWidth: 0,
  maxWidth: 0,
  minHeight: 0,
  maxHeight: 0
})

const backgroundStyles = reactive({
  backgroundColor: '',
  backgroundImage: '',
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  backgroundRepeat: 'no-repeat'
})

const textStyles = reactive({
  fontFamily: '',
  fontSize: 16,
  fontWeight: 'normal',
  fontStyle: 'normal',
  color: '#333333',
  lineHeight: 1.5,
  letterSpacing: 0,
  textAlign: 'left',
  textDecoration: 'none',
  textTransform: 'none'
})

const animationStyles = reactive({
  transition: '',
  hoverEffect: '',
  opacity: 1,
  transform: ''
})

const textDecorations = ref<string[]>([])
const customCSS = ref('')

// 计算属性
const uploadAction = computed(() => {
  return import.meta.env.VITE_API_BASE_URL + '/upload/image'
})

const uploadHeaders = computed(() => {
  return {
    Authorization: `Bearer ${userStore.token}`
  }
})

/**
 * 初始化样式数据
 */
const initStyles = () => {
  const config = props.component.config || {}
  
  // 布局样式
  Object.assign(layoutStyles, {
    display: config.display || 'block',
    position: config.position || 'static',
    zIndex: config.zIndex || 0,
    float: config.float || 'none',
    clear: config.clear || 'none'
  })
  
  // 尺寸样式
  Object.assign(sizeStyles, {
    width: config.width || '',
    widthUnit: config.widthUnit || 'px',
    height: config.height || '',
    heightUnit: config.heightUnit || 'px',
    minWidth: config.minWidth || 0,
    maxWidth: config.maxWidth || 0,
    minHeight: config.minHeight || 0,
    maxHeight: config.maxHeight || 0
  })
  
  // 背景样式
  Object.assign(backgroundStyles, {
    backgroundColor: config.backgroundColor || '',
    backgroundImage: config.backgroundImage || '',
    backgroundSize: config.backgroundSize || 'cover',
    backgroundPosition: config.backgroundPosition || 'center',
    backgroundRepeat: config.backgroundRepeat || 'no-repeat'
  })
  
  // 文字样式
  Object.assign(textStyles, {
    fontFamily: config.fontFamily || '',
    fontSize: config.fontSize || 16,
    fontWeight: config.fontWeight || 'normal',
    fontStyle: config.fontStyle || 'normal',
    color: config.color || '#333333',
    lineHeight: config.lineHeight || 1.5,
    letterSpacing: config.letterSpacing || 0,
    textAlign: config.textAlign || 'left',
    textDecoration: config.textDecoration || 'none',
    textTransform: config.textTransform || 'none'
  })
  
  // 动画样式
  Object.assign(animationStyles, {
    transition: config.transition || '',
    hoverEffect: config.hoverEffect || '',
    opacity: config.opacity || 1,
    transform: config.transform || ''
  })
  
  // 文字装饰
  if (config.textDecoration) {
    textDecorations.value = config.textDecoration.split(' ').filter(Boolean)
  }
  
  // 自定义CSS
  customCSS.value = config.customCSS || ''
}

/**
 * 处理样式变化
 */
const handleStyleChange = () => {
  const newConfig = {
    ...props.component.config,
    ...layoutStyles,
    ...sizeStyles,
    ...backgroundStyles,
    ...textStyles,
    ...animationStyles
  }
  
  // 处理尺寸单位
  if (sizeStyles.width) {
    newConfig.width = sizeStyles.width + sizeStyles.widthUnit
  }
  if (sizeStyles.height) {
    newConfig.height = sizeStyles.height + sizeStyles.heightUnit
  }
  
  const updatedComponent = {
    ...props.component,
    config: newConfig
  }
  
  emit('update', updatedComponent)
}

/**
 * 处理文字装饰变化
 */
const handleTextDecorationChange = () => {
  textStyles.textDecoration = textDecorations.value.join(' ') || 'none'
  handleStyleChange()
}

/**
 * 处理自定义CSS变化
 */
const handleCustomCSSChange = () => {
  const newConfig = {
    ...props.component.config,
    customCSS: customCSS.value
  }
  
  const updatedComponent = {
    ...props.component,
    config: newConfig
  }
  
  emit('update', updatedComponent)
}

/**
 * 图片上传前检查
 */
const beforeImageUpload = (rawFile: File) => {
  const isImage = rawFile.type.startsWith('image/')
  const isLt5M = rawFile.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

/**
 * 图片上传成功
 */
const handleImageSuccess = (response: any) => {
  if (response.code === 200) {
    backgroundStyles.backgroundImage = response.data.url
    handleStyleChange()
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error(response.message || '图片上传失败')
  }
}

/**
 * 重置样式
 */
const handleReset = () => {
  initStyles()
  handleStyleChange()
}

// 监听组件变化
watch(() => props.component, () => {
  initStyles()
}, { immediate: true, deep: true })
</script>

<style lang="scss" scoped>
.component-style-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.style-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color-light);
  
  .style-title {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.style-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.style-group {
  margin-bottom: 24px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .group-title {
    margin: 0 0 16px 0;
    font-size: 13px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    padding-bottom: 8px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
}

.unit {
  margin-left: 8px;
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

.size-input {
  display: flex;
  gap: 8px;
  
  :deep(.el-input) {
    flex: 1;
  }
  
  :deep(.el-select) {
    width: 60px;
  }
}

.background-image {
  .image-uploader {
    margin-bottom: 8px;
    
    :deep(.el-upload) {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
      
      &:hover {
        border-color: var(--el-color-primary);
      }
    }
  }
  
  .image-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 80px;
    height: 80px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .uploaded-image {
    width: 80px;
    height: 80px;
    display: block;
    object-fit: cover;
  }
}

.css-tips {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  background: var(--el-fill-color-light);
  padding: 8px 12px;
  border-radius: 4px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
  
  .el-form-item__label {
    font-size: 12px;
    color: var(--el-text-color-regular);
  }
}

:deep(.el-radio-group) {
  .el-radio-button {
    .el-radio-button__inner {
      padding: 4px 8px;
      font-size: 12px;
    }
  }
}

:deep(.el-checkbox-group) {
  .el-checkbox {
    margin-right: 12px;
    margin-bottom: 8px;
    
    .el-checkbox__label {
      font-size: 12px;
    }
  }
}

:deep(.el-slider) {
  .el-slider__input {
    width: 80px;
  }
}
</style>

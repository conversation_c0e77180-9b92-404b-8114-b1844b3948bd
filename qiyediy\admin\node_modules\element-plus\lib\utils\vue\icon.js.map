{"version": 3, "file": "icon.js", "sources": ["../../../../../packages/utils/vue/icon.ts"], "sourcesContent": ["import {\n  <PERSON><PERSON>heck,\n  CircleClose,\n  <PERSON>CloseFilled,\n  Close,\n  InfoFilled,\n  Loading,\n  SuccessFilled,\n  WarningFilled,\n} from '@element-plus/icons-vue'\nimport { definePropType } from './props'\n\nimport type { Component } from 'vue'\n\nexport const iconPropType = definePropType<string | Component>([\n  String,\n  Object,\n  Function,\n])\n\nexport const CloseComponents = {\n  Close,\n}\n\nexport const TypeComponents = {\n  Close,\n  SuccessFilled,\n  InfoFilled,\n  WarningFilled,\n  CircleCloseFilled,\n}\n\nexport const TypeComponentsMap = {\n  primary: InfoFilled,\n  success: SuccessFilled,\n  warning: WarningFilled,\n  error: CircleCloseFilled,\n  info: InfoFilled,\n}\n\nexport const ValidateComponentsMap = {\n  validating: Loading,\n  success: CircleCheck,\n  error: CircleClose,\n}\n"], "names": ["definePropType", "Close", "SuccessFilled", "InfoFilled", "WarningFilled", "CircleCloseFilled", "Loading", "CircleCheck", "CircleClose"], "mappings": ";;;;;;;AAWY,MAAC,YAAY,GAAGA,sBAAc,CAAC;AAC3C,EAAE,MAAM;AACR,EAAE,MAAM;AACR,EAAE,QAAQ;AACV,CAAC,EAAE;AACS,MAAC,eAAe,GAAG;AAC/B,SAAEC,cAAK;AACP,EAAE;AACU,MAAC,cAAc,GAAG;AAC9B,SAAEA,cAAK;AACP,iBAAEC,sBAAa;AACf,cAAEC,mBAAU;AACZ,iBAAEC,sBAAa;AACf,qBAAEC,0BAAiB;AACnB,EAAE;AACU,MAAC,iBAAiB,GAAG;AACjC,EAAE,OAAO,EAAEF,mBAAU;AACrB,EAAE,OAAO,EAAED,sBAAa;AACxB,EAAE,OAAO,EAAEE,sBAAa;AACxB,EAAE,KAAK,EAAEC,0BAAiB;AAC1B,EAAE,IAAI,EAAEF,mBAAU;AAClB,EAAE;AACU,MAAC,qBAAqB,GAAG;AACrC,EAAE,UAAU,EAAEG,gBAAO;AACrB,EAAE,OAAO,EAAEC,oBAAW;AACtB,EAAE,KAAK,EAAEC,oBAAW;AACpB;;;;;;;;"}
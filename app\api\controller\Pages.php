<?php

namespace app\api\controller;

use app\BaseController;
use app\model\PageTemplate;
use think\facade\Request;
use think\facade\Cache;

/**
 * DIY页面API控制器
 */
class Pages extends BaseController
{
    /**
     * 获取已发布的DIY页面列表
     */
    public function diyPages()
    {
        try {
            // 尝试从缓存获取
            $cacheKey = 'diy_pages_list';
            $pages = Cache::get($cacheKey);

            if (!$pages) {
                // 从数据库获取
                $pages = PageTemplate::where('status', 1)
                    ->where('slug', '<>', '')
                    ->field('id,name,slug,description,type,updated_at')
                    ->order('sort_order', 'desc')
                    ->order('updated_at', 'desc')
                    ->select()
                    ->toArray();

                // 缓存5分钟（减少缓存时间，确保及时更新）
                Cache::set($cacheKey, $pages, 300);
            }

            return json([
                'success' => true,
                'pages' => $pages,
                'total' => count($pages),
                'timestamp' => time()
            ]);

        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '获取页面列表失败：' . $e->getMessage(),
                'error_code' => 'FETCH_PAGES_ERROR'
            ]);
        }
    }
    
    /**
     * 根据类型获取页面
     */
    public function pagesByType()
    {
        $type = Request::param('type', '');
        
        try {
            // 构建缓存键
            $cacheKey = 'diy_pages_by_type_' . ($type ?: 'all');
            $pages = Cache::get($cacheKey);
            
            if (!$pages) {
                $query = PageTemplate::where('status', 1)
                    ->where('slug', '<>', '');
                    
                if ($type) {
                    $query->where('type', $type);
                }
                
                $pages = $query->field('id,name,slug,description,type,updated_at')
                    ->order('sort_order', 'desc')
                    ->order('updated_at', 'desc')
                    ->select()
                    ->toArray();
                
                // 缓存15分钟
                Cache::set($cacheKey, $pages, 900);
            }
            
            return json([
                'success' => true,
                'pages' => $pages,
                'type' => $type,
                'total' => count($pages),
                'timestamp' => time()
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '获取页面失败：' . $e->getMessage(),
                'error_code' => 'FETCH_PAGES_BY_TYPE_ERROR'
            ]);
        }
    }
    
    /**
     * 获取页面详情
     */
    public function pageDetail()
    {
        $slug = Request::param('slug', '');
        
        if (empty($slug)) {
            return json([
                'success' => false,
                'message' => '页面标识不能为空',
                'error_code' => 'INVALID_SLUG'
            ]);
        }
        
        try {
            // 构建缓存键
            $cacheKey = 'diy_page_detail_' . $slug;
            $page = Cache::get($cacheKey);
            
            if (!$page) {
                $page = PageTemplate::where('slug', $slug)
                    ->where('status', 1)
                    ->field('id,name,slug,description,type,html_content,css_content,updated_at')
                    ->find();
                
                if (!$page) {
                    return json([
                        'success' => false,
                        'message' => '页面不存在',
                        'error_code' => 'PAGE_NOT_FOUND'
                    ]);
                }
                
                $page = $page->toArray();
                
                // 缓存30分钟
                Cache::set($cacheKey, $page, 1800);
            }
            
            return json([
                'success' => true,
                'page' => $page,
                'timestamp' => time()
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '获取页面详情失败：' . $e->getMessage(),
                'error_code' => 'FETCH_PAGE_DETAIL_ERROR'
            ]);
        }
    }
    
    /**
     * 获取页面统计信息
     */
    public function pageStats()
    {
        try {
            // 构建缓存键
            $cacheKey = 'diy_page_stats';
            $stats = Cache::get($cacheKey);
            
            if (!$stats) {
                $totalPages = PageTemplate::count();
                $publishedPages = PageTemplate::where('status', 1)->count();
                $draftPages = PageTemplate::where('status', 0)->count();
                
                // 按类型统计
                $typeStats = PageTemplate::field('type, COUNT(*) as count')
                    ->group('type')
                    ->select()
                    ->toArray();
                
                $stats = [
                    'total' => $totalPages,
                    'published' => $publishedPages,
                    'draft' => $draftPages,
                    'by_type' => $typeStats
                ];
                
                // 缓存1小时
                Cache::set($cacheKey, $stats, 3600);
            }
            
            return json([
                'success' => true,
                'stats' => $stats,
                'timestamp' => time()
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '获取统计信息失败：' . $e->getMessage(),
                'error_code' => 'FETCH_STATS_ERROR'
            ]);
        }
    }
    
    /**
     * 清除页面缓存
     */
    public function clearCache()
    {
        try {
            // 清除所有相关缓存
            $cacheKeys = [
                'diy_pages_list',
                'diy_page_stats'
            ];
            
            foreach ($cacheKeys as $key) {
                Cache::delete($key);
            }
            
            // 清除按类型缓存
            $types = ['page', 'home', 'product', 'news', 'case', 'contact', 'about'];
            foreach ($types as $type) {
                Cache::delete('diy_pages_by_type_' . $type);
            }
            Cache::delete('diy_pages_by_type_all');
            
            // 清除页面详情缓存（这里只能清除已知的，实际使用中可能需要更复杂的缓存管理）
            $slugs = PageTemplate::where('status', 1)->column('slug');
            foreach ($slugs as $slug) {
                Cache::delete('diy_page_detail_' . $slug);
            }
            
            return json([
                'success' => true,
                'message' => '缓存清除成功',
                'timestamp' => time()
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '清除缓存失败：' . $e->getMessage(),
                'error_code' => 'CLEAR_CACHE_ERROR'
            ]);
        }
    }

    /**
     * 获取DIY组件数据
     */
    public function getDiyData()
    {
        $type = Request::param('type', 'news');
        $limit = Request::param('limit', 6, 'intval');
        $page = Request::param('page', 1, 'intval');

        try {
            $data = [];

            switch ($type) {
                case 'news':
                    // 获取真实新闻数据
                    $newsData = \think\facade\Db::name('news')
                        ->where('status', 1)
                        ->order('created_at desc')
                        ->limit($limit)
                        ->page($page)
                        ->field('id,title,content,image,created_at')
                        ->select()
                        ->toArray();

                    // 处理摘要：截取内容前100个字符作为摘要
                    $data = array_map(function($item) {
                        // 如果有摘要字段就用摘要，否则截取内容
                        if (isset($item['summary']) && !empty($item['summary'])) {
                            $item['excerpt'] = $item['summary'];
                        } else {
                            // 去除HTML标签并截取前100个字符
                            $content = strip_tags($item['content']);
                            $item['excerpt'] = mb_substr($content, 0, 100, 'utf-8') . (mb_strlen($content, 'utf-8') > 100 ? '...' : '');
                        }
                        return $item;
                    }, $newsData);
                    break;

                case 'products':
                    // 获取真实产品数据
                    $data = \think\facade\Db::name('products')
                        ->where('status', 1)
                        ->order('sort desc,id desc')
                        ->limit($limit)
                        ->page($page)
                        ->field('id,name as title,description as content,image,price')
                        ->select()
                        ->toArray();
                    break;

                case 'cases':
                    // 获取真实案例数据
                    $data = \think\facade\Db::name('cases')
                        ->where('status', 1)
                        ->order('created_at desc')
                        ->limit($limit)
                        ->page($page)
                        ->field('id,title,description as content,image,client_name')
                        ->select()
                        ->toArray();
                    break;

                default:
                    return json(['success' => false, 'message' => '不支持的数据类型']);
            }

            return json([
                'success' => true,
                'data' => $data,
                'total' => count($data),
                'type' => $type
            ]);

        } catch (\Exception $e) {
            return json(['success' => false, 'message' => '获取数据失败：' . $e->getMessage()]);
        }
    }


}

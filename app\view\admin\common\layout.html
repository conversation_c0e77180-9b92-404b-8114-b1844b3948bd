<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$pageTitle|default='管理后台'} - 三只鱼网络</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="/assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="/assets/css/all.min.css">
    <link rel="stylesheet" href="/assets/css/admin/admin.css">
    <link rel="stylesheet" href="/assets/css/admin/common.css">
    
    <!-- 额外CSS -->
    {if isset($additionalCSS)}
        {foreach $additionalCSS as $css}
            <link rel="stylesheet" href="{$css}">
        {/foreach}
    {/if}
</head>
<body>
    <!-- 顶部导航 -->
    {include file="admin/common/header"}
    
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            {include file="admin/common/sidebar"}

            <!-- 主要内容 -->
            <main class="main-content">
                <!-- 内容头部 -->
                <div class="content-header">
                    <h1 class="h3 mb-0">
                        <i class="{$pageIcon|default='fas fa-tachometer-alt'}"></i> {$pageTitle|default='管理后台'}
                    </h1>
                </div>

                <!-- 页面内容区域 -->
                <div class="content-body">
                    {__CONTENT__}
                </div>
            </main>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/assets/js/jquery.min.js"></script>
    <script src="/assets/js/bootstrap.bundle.min.js"></script>

    <!-- 检查Bootstrap是否正确加载 -->
    <script>
        $(document).ready(function() {
            // 检查Bootstrap组件是否可用
            if (typeof $.fn.tooltip === 'undefined') {
                console.warn('Bootstrap tooltip 组件未加载');
            }
            if (typeof $.fn.popover === 'undefined') {
                console.warn('Bootstrap popover 组件未加载');
            }
        });
    </script>


    
    <!-- 额外JS -->
    {if isset($additionalJS)}
        {foreach $additionalJS as $js}
            <script src="{$js}"></script>
        {/foreach}
    {/if}

    <script>
        $(document).ready(function() {
            // 更新当前时间
            function updateTime() {
                const now = new Date();
                const timeString = now.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
                $('#currentTime').text(timeString);
            }
            
            // 初始化时间并每秒更新
            updateTime();
            setInterval(updateTime, 1000);

            // 侧边栏切换（移动端）
            $('#sidebarToggle').on('click', function() {
                $('.sidebar').toggleClass('show');
                $('.sidebar-overlay').toggleClass('show');
            });

            // 点击遮罩层隐藏侧边栏
            $('.sidebar-overlay').on('click', function() {
                $('.sidebar').removeClass('show');
                $('.sidebar-overlay').removeClass('show');
            });

            // 菜单高亮逻辑已移至sidebar.html中处理
        });
    </script>
</body>
</html>

{"name": "inflight", "version": "1.0.6", "description": "Add callbacks to requests in flight to avoid async duplication", "main": "inflight.js", "files": ["inflight.js"], "dependencies": {"once": "^1.3.0", "wrappy": "1"}, "devDependencies": {"tap": "^7.1.2"}, "scripts": {"test": "tap test.js --100"}, "repository": {"type": "git", "url": "https://github.com/npm/inflight.git"}, "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "bugs": {"url": "https://github.com/isaacs/inflight/issues"}, "homepage": "https://github.com/isaacs/inflight", "license": "ISC"}
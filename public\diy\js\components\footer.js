/**
 * 页脚组件
 * 页面底部信息展示区域
 */

// 页脚组件模板
const footerComponent = {
    name: '页脚',
    html: `<div class="footer-component">
        <div class="footer-content">
            <h4>网站名称</h4>
            <p>© 2024 版权所有. 保留所有权利.</p>
            <div class="footer-links">
                <a href="#">关于我们</a>
                <a href="#">联系我们</a>
                <a href="#">隐私政策</a>
                <a href="#">服务条款</a>
            </div>
        </div>
    </div>`,
    properties: {
        style: 'default', // 新增风格选择：default, style1
        siteName: '网站名称',
        copyright: '© 2024 版权所有. 保留所有权利.',
        links: [
            { name: '关于我们', link: '#' },
            { name: '联系我们', link: '#' },
            { name: '隐私政策', link: '#' },
            { name: '服务条款', link: '#' }
        ],
        // 风格1专用属性
        companyInfo: {
            logo: '/assets/images/logo.png',
            description: '我们提供专业的企业解决方案，助力企业数字化转型，打造专业品牌形象，提供一站式服务支持。',
            wechatQR: '/assets/images/weixin.png',
            address: '北京市朝阳区科技园',
            phone: '************',
            email: '<EMAIL>',
            qq: '*********'
        },
        solutions: [
            { name: '社交分销', link: '/solutions/social-distribution' },
            { name: '智能多门店', link: '/solutions/multi-store' },
            { name: '大型多商户', link: '/solutions/multi-merchant' },
            { name: '大货批发', link: '/solutions/wholesale' },
            { name: '平台级供货商', link: '/solutions/supplier' },
            { name: '本地生活服务', link: '/solutions/local-service' }
        ],
        products: [
            { name: '电商系统', link: '/products/ecommerce' },
            { name: '分销系统', link: '/products/distribution' },
            { name: '管理系统', link: '/products/management' },
            { name: '移动应用', link: '/products/mobile-app' },
            { name: '定制开发', link: '/products/custom' }
        ],
        support: [
            { name: '帮助中心', link: '/help' },
            { name: '开发文档', link: '/docs' },
            { name: '常见问题', link: '/faq' },
            { name: '技术支持', link: '/contact' },
            { name: '意见反馈', link: '/feedback' }
        ],
        bgColor: '#2d3748',
        textColor: '#ffffff',
        linkColor: '#a0aec0',
        linkHoverColor: '#ffffff',
        padding: 40,
        textAlign: 'center',
        titleSize: 20,
        textSize: 14,
        linkSize: 14,
        showLinks: true,
        linkSpacing: 20
    }
};

// 生成页脚组件属性面板
function generateFooterProperties(component) {
    // 获取或创建组件的独立属性对象
    if (!component.footerProps) {
        component.footerProps = JSON.parse(JSON.stringify(footerComponent.properties));
    }
    const props = component.footerProps;

    let html = `
        <!-- 风格选择 -->
        <div class="property-section">
            <h4 class="section-title">风格选择</h4>
            <div class="property-group footer-setting-group">
                <label class="property-label">页脚风格</label>
                <div class="input-group">
                    <select class="property-input" onchange="updateFooterProperty('${component.id}', 'style', this.value)">
                        <option value="default" ${props.style === 'default' ? 'selected' : ''}>默认风格</option>
                        <option value="style1" ${props.style === 'style1' ? 'selected' : ''}>风格1 - 企业版</option>
                    </select>
                </div>
            </div>
        </div>
    `;

    // 根据风格显示不同的设置项
    if (props.style === 'style1') {
        html += generateStyle1Properties(component, props);
    } else {
        html += generateDefaultProperties(component, props);
    }

    return html;
}

// 生成默认风格的属性设置
function generateDefaultProperties(component, props) {
    return `
        <!-- 内容设置 -->
        <div class="property-section">
            <h4 class="section-title">内容设置</h4>

            <div class="property-group footer-setting-group">
                <label class="property-label">网站名称</label>
                <div class="input-group">
                    <input type="text" class="property-input" value="${props.siteName}"
                           onchange="updateFooterProperty('${component.id}', 'siteName', this.value)">
                </div>
            </div>

            <div class="property-group footer-setting-group">
                <label class="property-label">版权信息</label>
                <div class="input-group">
                    <textarea class="property-input" rows="2"
                              onchange="updateFooterProperty('${component.id}', 'copyright', this.value)">${props.copyright}</textarea>
                </div>
            </div>
        </div>

        <!-- 链接设置 -->
        <div class="property-section">
            <h4 class="section-title">链接设置</h4>

            <div class="property-group footer-setting-group">
                <label class="property-label">链接管理</label>
                <div class="checkbox-group">
                    <label class="checkbox-item">
                        <input type="checkbox" ${props.showLinks ? 'checked' : ''}
                               onchange="updateFooterProperty('${component.id}', 'showLinks', this.checked)">
                        显示链接
                    </label>
                </div>
                ${props.showLinks ? `
                    <div id="footer-links-${component.id}" class="menu-items-container">
                        ${props.links.map((link, index) => `
                            <div class="menu-item-row">
                                <div class="menu-item-inputs">
                                    <input type="text" class="property-input" value="${link.name}" placeholder="链接名称"
                                           onchange="updateFooterLink('${component.id}', ${index}, 'name', this.value)">
                                    <input type="text" class="property-input" value="${link.link}" placeholder="链接地址"
                                           onchange="updateFooterLink('${component.id}', ${index}, 'link', this.value)">
                                    <button onclick="removeFooterLink('${component.id}', ${index})" class="delete-btn">删除</button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                    <button onclick="addFooterLink('${component.id}')" class="add-btn">添加链接</button>
                ` : ''}
            </div>
        </div>

        <!-- 颜色设置 -->
        <div class="property-section">
            <h4 class="section-title">颜色设置</h4>

            <div class="property-group footer-setting-group">
                <label class="property-label">背景和文字</label>
                <div class="color-row">
                    <div class="color-item">
                        <label>背景颜色</label>
                        <input type="color" class="property-input" value="${props.bgColor}"
                               onchange="updateFooterProperty('${component.id}', 'bgColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>文字颜色</label>
                        <input type="color" class="property-input" value="${props.textColor}"
                               onchange="updateFooterProperty('${component.id}', 'textColor', this.value)">
                    </div>
                </div>
            </div>

            <div class="property-group footer-setting-group">
                <label class="property-label">链接颜色</label>
                <div class="color-row">
                    <div class="color-item">
                        <label>链接颜色</label>
                        <input type="color" class="property-input" value="${props.linkColor}"
                               onchange="updateFooterProperty('${component.id}', 'linkColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>悬停颜色</label>
                        <input type="color" class="property-input" value="${props.linkHoverColor}"
                               onchange="updateFooterProperty('${component.id}', 'linkHoverColor', this.value)">
                    </div>
                </div>
            </div>
        </div>

        <!-- 尺寸设置 -->
        <div class="property-section">
            <h4 class="section-title">尺寸设置</h4>

            <div class="property-group footer-setting-group">
                <label class="property-label">布局尺寸</label>
                <div class="color-row">
                    <div class="color-item">
                        <label>内边距</label>
                        <input type="number" class="property-input" value="${props.padding}" min="20" max="100"
                               onchange="updateFooterProperty('${component.id}', 'padding', this.value)">
                    </div>
                    <div class="color-item">
                        <label>链接间距</label>
                        <input type="number" class="property-input" value="${props.linkSpacing}" min="10" max="50"
                               onchange="updateFooterProperty('${component.id}', 'linkSpacing', this.value)">
                    </div>
                </div>
            </div>

            <div class="property-group footer-setting-group">
                <label class="property-label">字体大小</label>
                <div class="color-row">
                    <div class="color-item">
                        <label>标题大小</label>
                        <input type="number" class="property-input" value="${props.titleSize}" min="16" max="32"
                               onchange="updateFooterProperty('${component.id}', 'titleSize', this.value)">
                    </div>
                    <div class="color-item">
                        <label>文字大小</label>
                        <input type="number" class="property-input" value="${props.textSize}" min="10" max="20"
                               onchange="updateFooterProperty('${component.id}', 'textSize', this.value)">
                    </div>
                    <div class="color-item">
                        <label>链接大小</label>
                        <input type="number" class="property-input" value="${props.linkSize}" min="10" max="20"
                               onchange="updateFooterProperty('${component.id}', 'linkSize', this.value)">
                    </div>
                </div>
            </div>
        </div>

        <!-- 布局设置 -->
        <div class="property-section">
            <h4 class="section-title">布局设置</h4>

            <div class="property-group footer-setting-group">
                <label class="property-label">对齐方式</label>
                <div class="input-group">
                    <select class="property-input" onchange="updateFooterProperty('${component.id}', 'textAlign', this.value)">
                        <option value="left" ${props.textAlign === 'left' ? 'selected' : ''}>左对齐</option>
                        <option value="center" ${props.textAlign === 'center' ? 'selected' : ''}>居中对齐</option>
                        <option value="right" ${props.textAlign === 'right' ? 'selected' : ''}>右对齐</option>
                    </select>
                </div>
            </div>
        </div>
    `;
}

// 生成风格1的属性设置
function generateStyle1Properties(component, props) {
    return `
        <!-- 公司信息设置 -->
        <div class="property-section">
            <h4 class="section-title">公司信息</h4>

            <div class="property-group footer-setting-group">
                <label class="property-label">公司名称</label>
                <div class="input-group">
                    <input type="text" class="property-input" value="${props.siteName}"
                           onchange="updateFooterProperty('${component.id}', 'siteName', this.value)">
                </div>
            </div>

            <div class="property-group footer-setting-group">
                <label class="property-label">Logo图片路径</label>
                <div class="input-group">
                    <input type="text" class="property-input" value="${props.companyInfo.logo}"
                           onchange="updateFooterCompanyInfo('${component.id}', 'logo', this.value)">
                </div>
            </div>

            <div class="property-group footer-setting-group">
                <label class="property-label">公司描述</label>
                <div class="input-group">
                    <textarea class="property-input" rows="3"
                              onchange="updateFooterCompanyInfo('${component.id}', 'description', this.value)">${props.companyInfo.description}</textarea>
                </div>
            </div>

            <div class="property-group footer-setting-group">
                <label class="property-label">微信二维码路径</label>
                <div class="input-group">
                    <input type="text" class="property-input" value="${props.companyInfo.wechatQR}"
                           onchange="updateFooterCompanyInfo('${component.id}', 'wechatQR', this.value)">
                </div>
            </div>
        </div>

        <!-- 联系信息设置 -->
        <div class="property-section">
            <h4 class="section-title">联系信息</h4>

            <div class="property-group footer-setting-group">
                <label class="property-label">地址</label>
                <div class="input-group">
                    <input type="text" class="property-input" value="${props.companyInfo.address}"
                           onchange="updateFooterCompanyInfo('${component.id}', 'address', this.value)">
                </div>
            </div>

            <div class="property-group footer-setting-group">
                <label class="property-label">电话</label>
                <div class="input-group">
                    <input type="text" class="property-input" value="${props.companyInfo.phone}"
                           onchange="updateFooterCompanyInfo('${component.id}', 'phone', this.value)">
                </div>
            </div>

            <div class="property-group footer-setting-group">
                <label class="property-label">邮箱</label>
                <div class="input-group">
                    <input type="text" class="property-input" value="${props.companyInfo.email}"
                           onchange="updateFooterCompanyInfo('${component.id}', 'email', this.value)">
                </div>
            </div>

            <div class="property-group footer-setting-group">
                <label class="property-label">QQ</label>
                <div class="input-group">
                    <input type="text" class="property-input" value="${props.companyInfo.qq}"
                           onchange="updateFooterCompanyInfo('${component.id}', 'qq', this.value)">
                </div>
            </div>
        </div>

        <!-- 解决方案设置 -->
        <div class="property-section">
            <h4 class="section-title">解决方案</h4>
            <div class="property-group footer-setting-group">
                <div id="footer-solutions-${component.id}" class="menu-items-container">
                    ${props.solutions.map((item, index) => `
                        <div class="menu-item-row">
                            <div class="menu-item-inputs">
                                <input type="text" class="property-input" value="${item.name}" placeholder="方案名称"
                                       onchange="updateFooterSolutions('${component.id}', ${index}, 'name', this.value)">
                                <input type="text" class="property-input" value="${item.link}" placeholder="链接地址"
                                       onchange="updateFooterSolutions('${component.id}', ${index}, 'link', this.value)">
                                <button onclick="removeFooterSolutions('${component.id}', ${index})" class="delete-btn">删除</button>
                            </div>
                        </div>
                    `).join('')}
                </div>
                <button onclick="addFooterSolutions('${component.id}')" class="add-btn">添加解决方案</button>
            </div>
        </div>

        <!-- 产品服务设置 -->
        <div class="property-section">
            <h4 class="section-title">产品服务</h4>
            <div class="property-group footer-setting-group">
                <div id="footer-products-${component.id}" class="menu-items-container">
                    ${props.products.map((item, index) => `
                        <div class="menu-item-row">
                            <div class="menu-item-inputs">
                                <input type="text" class="property-input" value="${item.name}" placeholder="产品名称"
                                       onchange="updateFooterProducts('${component.id}', ${index}, 'name', this.value)">
                                <input type="text" class="property-input" value="${item.link}" placeholder="链接地址"
                                       onchange="updateFooterProducts('${component.id}', ${index}, 'link', this.value)">
                                <button onclick="removeFooterProducts('${component.id}', ${index})" class="delete-btn">删除</button>
                            </div>
                        </div>
                    `).join('')}
                </div>
                <button onclick="addFooterProducts('${component.id}')" class="add-btn">添加产品服务</button>
            </div>
        </div>

        <!-- 帮助支持设置 -->
        <div class="property-section">
            <h4 class="section-title">帮助支持</h4>
            <div class="property-group footer-setting-group">
                <div id="footer-support-${component.id}" class="menu-items-container">
                    ${props.support.map((item, index) => `
                        <div class="menu-item-row">
                            <div class="menu-item-inputs">
                                <input type="text" class="property-input" value="${item.name}" placeholder="支持项目"
                                       onchange="updateFooterSupport('${component.id}', ${index}, 'name', this.value)">
                                <input type="text" class="property-input" value="${item.link}" placeholder="链接地址"
                                       onchange="updateFooterSupport('${component.id}', ${index}, 'link', this.value)">
                                <button onclick="removeFooterSupport('${component.id}', ${index})" class="delete-btn">删除</button>
                            </div>
                        </div>
                    `).join('')}
                </div>
                <button onclick="addFooterSupport('${component.id}')" class="add-btn">添加帮助支持</button>
            </div>
        </div>

        <!-- 版权信息设置 -->
        <div class="property-section">
            <h4 class="section-title">版权信息</h4>
            <div class="property-group footer-setting-group">
                <label class="property-label">版权文字</label>
                <div class="input-group">
                    <textarea class="property-input" rows="2"
                              onchange="updateFooterProperty('${component.id}', 'copyright', this.value)">${props.copyright}</textarea>
                </div>
            </div>
        </div>
    `;
}

// 更新页脚属性
function updateFooterProperty(componentId, property, value) {
    const component = document.getElementById(componentId);
    
    // 获取或创建组件的独立属性对象
    if (!component.footerProps) {
        component.footerProps = JSON.parse(JSON.stringify(footerComponent.properties));
    }
    const props = component.footerProps;

    if (typeof value === 'boolean') {
        props[property] = value;
    } else if (['padding', 'titleSize', 'textSize', 'linkSize', 'linkSpacing'].includes(property)) {
        props[property] = parseInt(value);
    } else {
        props[property] = value;
    }

    // 如果是风格切换，需要强制重新构建组件
    if (property === 'style') {
        // 清空组件内容，强制重新构建
        const contentDiv = component.querySelector('.footer-component');
        if (contentDiv) {
            contentDiv.innerHTML = '';
            contentDiv.className = 'footer-component'; // 重置类名
        }
    }

    updateFooterDisplay(component, props);

    // 如果是风格或显示链接设置改变，需要重新生成属性面板
    if (property === 'showLinks' || property === 'style') {
        updatePropertiesPanel(component);
    }
}

// 更新公司信息
function updateFooterCompanyInfo(componentId, field, value) {
    const component = document.getElementById(componentId);
    if (!component.footerProps) {
        component.footerProps = JSON.parse(JSON.stringify(footerComponent.properties));
    }
    const props = component.footerProps;
    props.companyInfo[field] = value;
    updateFooterDisplay(component, props);
}

// 更新解决方案
function updateFooterSolutions(componentId, index, field, value) {
    const component = document.getElementById(componentId);
    if (!component.footerProps) {
        component.footerProps = JSON.parse(JSON.stringify(footerComponent.properties));
    }
    const props = component.footerProps;
    props.solutions[index][field] = value;
    updateFooterDisplay(component, props);
}

function addFooterSolutions(componentId) {
    const component = document.getElementById(componentId);
    if (!component.footerProps) {
        component.footerProps = JSON.parse(JSON.stringify(footerComponent.properties));
    }
    const props = component.footerProps;
    props.solutions.push({ name: '新解决方案', link: '#' });
    updatePropertiesPanel(component);
    updateFooterDisplay(component, props);
}

function removeFooterSolutions(componentId, index) {
    const component = document.getElementById(componentId);
    if (!component.footerProps) {
        component.footerProps = JSON.parse(JSON.stringify(footerComponent.properties));
    }
    const props = component.footerProps;
    props.solutions.splice(index, 1);
    updatePropertiesPanel(component);
    updateFooterDisplay(component, props);
}

// 更新产品服务
function updateFooterProducts(componentId, index, field, value) {
    const component = document.getElementById(componentId);
    if (!component.footerProps) {
        component.footerProps = JSON.parse(JSON.stringify(footerComponent.properties));
    }
    const props = component.footerProps;
    props.products[index][field] = value;
    updateFooterDisplay(component, props);
}

function addFooterProducts(componentId) {
    const component = document.getElementById(componentId);
    if (!component.footerProps) {
        component.footerProps = JSON.parse(JSON.stringify(footerComponent.properties));
    }
    const props = component.footerProps;
    props.products.push({ name: '新产品', link: '#' });
    updatePropertiesPanel(component);
    updateFooterDisplay(component, props);
}

function removeFooterProducts(componentId, index) {
    const component = document.getElementById(componentId);
    if (!component.footerProps) {
        component.footerProps = JSON.parse(JSON.stringify(footerComponent.properties));
    }
    const props = component.footerProps;
    props.products.splice(index, 1);
    updatePropertiesPanel(component);
    updateFooterDisplay(component, props);
}

// 更新帮助支持
function updateFooterSupport(componentId, index, field, value) {
    const component = document.getElementById(componentId);
    if (!component.footerProps) {
        component.footerProps = JSON.parse(JSON.stringify(footerComponent.properties));
    }
    const props = component.footerProps;
    props.support[index][field] = value;
    updateFooterDisplay(component, props);
}

function addFooterSupport(componentId) {
    const component = document.getElementById(componentId);
    if (!component.footerProps) {
        component.footerProps = JSON.parse(JSON.stringify(footerComponent.properties));
    }
    const props = component.footerProps;
    props.support.push({ name: '新支持项目', link: '#' });
    updatePropertiesPanel(component);
    updateFooterDisplay(component, props);
}

function removeFooterSupport(componentId, index) {
    const component = document.getElementById(componentId);
    if (!component.footerProps) {
        component.footerProps = JSON.parse(JSON.stringify(footerComponent.properties));
    }
    const props = component.footerProps;
    props.support.splice(index, 1);
    updatePropertiesPanel(component);
    updateFooterDisplay(component, props);
}

// 更新页脚显示
function updateFooterDisplay(component, props = null) {
    const contentDiv = component.querySelector('.footer-component');
    if (!contentDiv) return;

    // 如果没有传入props，则获取组件的独立属性
    if (!props) {
        if (!component.footerProps) {
            component.footerProps = JSON.parse(JSON.stringify(footerComponent.properties));
        }
        props = component.footerProps;
    }

    // 根据风格选择不同的显示方式
    if (props.style === 'style1') {
        updateFooterStyle1(contentDiv, props);
    } else {
        updateFooterDefault(contentDiv, props);
    }
}

// 默认风格显示
function updateFooterDefault(contentDiv, props) {
    // 清空所有内容
    contentDiv.innerHTML = '';
    
    // 移除风格1的CSS类名
    contentDiv.classList.remove('style1');
    
    // 重置内联样式
    contentDiv.style.cssText = '';

    // 重新构建页脚内容
    let footerContent = document.createElement('div');
    footerContent.className = 'footer-content';
    contentDiv.appendChild(footerContent);

    // 构建HTML内容
    let html = `<h4>${props.siteName}</h4><p>${props.copyright}</p>`;

    if (props.showLinks && props.links.length > 0) {
        html += '<div class="footer-links">';
        props.links.forEach(link => {
            html += `<a href="${link.link}">${link.name}</a>`;
        });
        html += '</div>';
    }

    footerContent.innerHTML = html;

    // 更新样式
    contentDiv.style.backgroundColor = props.bgColor;
    contentDiv.style.color = props.textColor;
    contentDiv.style.padding = `${props.padding}px 20px`;
    contentDiv.style.textAlign = props.textAlign;

    // 更新标题样式
    const title = footerContent.querySelector('h4');
    if (title) {
        title.style.fontSize = `${props.titleSize}px`;
        title.style.marginBottom = '10px';
        title.style.fontWeight = '600';
    }

    // 更新文字样式
    const text = footerContent.querySelector('p');
    if (text) {
        text.style.fontSize = `${props.textSize}px`;
        text.style.marginBottom = props.showLinks ? '15px' : '0';
        text.style.opacity = '0.9';
    }

    // 更新链接样式
    const linksContainer = footerContent.querySelector('.footer-links');
    if (linksContainer) {
        linksContainer.style.display = 'flex';
        linksContainer.style.gap = `${props.linkSpacing}px`;
        linksContainer.style.justifyContent = props.textAlign === 'center' ? 'center' :
                                             props.textAlign === 'right' ? 'flex-end' : 'flex-start';
        linksContainer.style.flexWrap = 'wrap';

        const links = linksContainer.querySelectorAll('a');
        links.forEach(link => {
            link.style.color = props.linkColor;
            link.style.textDecoration = 'none';
            link.style.fontSize = `${props.linkSize}px`;
            link.style.transition = 'color 0.3s ease';

            link.addEventListener('mouseenter', () => {
                link.style.color = props.linkHoverColor;
            });

            link.addEventListener('mouseleave', () => {
                link.style.color = props.linkColor;
            });
        });
    }
}

// 风格1显示 - 企业版风格
function updateFooterStyle1(contentDiv, props) {
    // 清空现有内容
    contentDiv.innerHTML = '';

    // 添加风格1的CSS类名
    contentDiv.classList.add('style1');

    // 构建风格1的HTML结构
    const footerHTML = `
        <div class="footer-main-style1">
            <div class="container-style1">
                <div class="row-style1">
                    <!-- 公司信息 -->
                    <div class="col-style1">
                        <div class="footer-widget-style1">
                            <div class="footer-logo-style1">
                                <img src="${props.companyInfo.logo}" alt="${props.siteName}">
                            </div>
                            <p class="footer-desc-style1">
                                ${props.companyInfo.description}
                            </p>
                            <div class="footer-qrcode-style1">
                                <img src="${props.companyInfo.wechatQR}" alt="微信二维码" class="qrcode-img-style1">
                                <p class="qrcode-text-style1">扫码关注微信</p>
                            </div>
                        </div>
                    </div>

                    <!-- 解决方案 -->
                    <div class="col-style1">
                        <div class="footer-widget-style1">
                            <h5 class="footer-title-style1">解决方案</h5>
                            <ul class="footer-links-style1">
                                ${props.solutions.map(item => `<li><a href="${item.link}">${item.name}</a></li>`).join('')}
                            </ul>
                        </div>
                    </div>

                    <!-- 产品服务 -->
                    <div class="col-style1">
                        <div class="footer-widget-style1">
                            <h5 class="footer-title-style1">产品服务</h5>
                            <ul class="footer-links-style1">
                                ${props.products.map(item => `<li><a href="${item.link}">${item.name}</a></li>`).join('')}
                            </ul>
                        </div>
                    </div>

                    <!-- 帮助支持 -->
                    <div class="col-style1">
                        <div class="footer-widget-style1">
                            <h5 class="footer-title-style1">帮助支持</h5>
                            <ul class="footer-links-style1">
                                ${props.support.map(item => `<li><a href="${item.link}">${item.name}</a></li>`).join('')}
                            </ul>
                        </div>
                    </div>

                    <!-- 联系信息 -->
                    <div class="col-style1">
                        <div class="footer-widget-style1">
                            <h5 class="footer-title-style1">联系我们</h5>
                            <div class="footer-contact-style1">
                                <div class="contact-item-style1">
                                    <i data-lucide="map-pin" class="icon-style1"></i>
                                    <span>${props.companyInfo.address}</span>
                                </div>
                                <div class="contact-item-style1">
                                    <i data-lucide="phone" class="icon-style1"></i>
                                    <span>${props.companyInfo.phone}</span>
                                </div>
                                <div class="contact-item-style1">
                                    <i data-lucide="mail" class="icon-style1"></i>
                                    <span>${props.companyInfo.email}</span>
                                </div>
                                <div class="contact-item-style1">
                                    <i data-lucide="message-square" class="icon-style1"></i>
                                    <span>${props.companyInfo.qq}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 版权信息 -->
        <div class="footer-bottom-style1">
            <div class="container-style1">
                <div class="row-style1 align-items-center">
                    <div class="col-md-6-style1">
                        <div class="copyright-style1">
                            <p>${props.copyright}</p>
                        </div>
                    </div>
                    <div class="col-md-6-style1">
                        <div class="footer-links-bottom-style1">
                            <a href="/privacy">隐私政策</a>
                            <a href="/terms">服务条款</a>
                            <a href="/sitemap">网站地图</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    contentDiv.innerHTML = footerHTML;

    // 初始化Lucide图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}

// 页脚链接管理函数
function updateFooterLink(componentId, index, field, value) {
    const component = document.getElementById(componentId);
    if (!component.footerProps) {
        component.footerProps = JSON.parse(JSON.stringify(footerComponent.properties));
    }
    const props = component.footerProps;
    props.links[index][field] = value;
    
    updateFooterDisplay(component, props);
}

function addFooterLink(componentId) {
    const component = document.getElementById(componentId);
    if (!component.footerProps) {
        component.footerProps = JSON.parse(JSON.stringify(footerComponent.properties));
    }
    const props = component.footerProps;
    props.links.push({ name: '新链接', link: '#' });
    
    updatePropertiesPanel(component);
    updateFooterDisplay(component, props);
}

function removeFooterLink(componentId, index) {
    const component = document.getElementById(componentId);
    if (!component.footerProps) {
        component.footerProps = JSON.parse(JSON.stringify(footerComponent.properties));
    }
    const props = component.footerProps;
    props.links.splice(index, 1);

    updatePropertiesPanel(component);
    updateFooterDisplay(component, props);
}

// 获取组件属性的辅助函数
function getFooterProps(component) {
    if (!component.footerProps) {
        component.footerProps = JSON.parse(JSON.stringify(footerComponent.properties));
    }
    return component.footerProps;
}

// 注册页脚组件
if (typeof ComponentManager !== 'undefined') {
    ComponentManager.register('footer', footerComponent, generateFooterProperties, updateFooterDisplay);
}

// 初始化日志
console.log('🦶 页脚组件已加载');

{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/splitter/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON><PERSON>Install } from '@element-plus/utils'\nimport Splitter from './src/splitter.vue'\nimport SplitPanel from './src/split-panel.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElSplitter: SFCWithInstall<typeof Splitter> & {\n  SplitPanel: typeof SplitPanel\n} = withInstall(Splitter, {\n  SplitPanel,\n})\nexport default ElSplitter\n\nexport const ElSplitterPanel: SFCWithInstall<typeof SplitPanel> =\n  withNoopInstall(SplitPanel)\n\nexport * from './src/splitter'\nexport * from './src/split-panel'\n"], "names": ["withInstall", "Splitter", "SplitPanel", "withNoopInstall"], "mappings": ";;;;;;;;;;AAGY,MAAC,UAAU,GAAGA,mBAAW,CAACC,qBAAQ,EAAE;AAChD,cAAEC,uBAAU;AACZ,CAAC,EAAE;AAES,MAAC,eAAe,GAAGC,uBAAe,CAACD,uBAAU;;;;;;;;"}
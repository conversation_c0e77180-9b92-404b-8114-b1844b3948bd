/* 页面装修管理专用样式 - 参考轮播图管理样式 */
.page-builder-container {
    max-width: 100%;
    margin: 0;
    padding: 0;
    background: rgba(15, 15, 15, 0.95);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 20px;
    backdrop-filter: blur(25px);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.4),
        0 0 40px rgba(120, 119, 198, 0.1);
    overflow: hidden;
    position: relative;
    margin-bottom: 30px;
}

.page-builder-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
        rgba(120, 119, 198, 0.8),
        rgba(255, 119, 198, 0.8),
        rgba(120, 219, 255, 0.8));
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* 列表头部样式 */
.list-header {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.15) 0%,
        rgba(255, 119, 198, 0.1) 50%,
        rgba(120, 219, 255, 0.15) 100%);
    padding: 30px;
    border-bottom: 1px solid rgba(120, 119, 198, 0.2);
}

.list-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.list-title-section {
    display: flex;
    align-items: center;
    gap: 20px;
}

.list-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.3),
        rgba(255, 119, 198, 0.3));
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #fff;
    text-shadow: 0 0 20px rgba(120, 119, 198, 0.8);
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.3);
}

.list-title {
    font-family: 'Orbitron', monospace;
    font-size: 28px;
    font-weight: 700;
    color: #fff;
    margin: 0;
    text-shadow: 0 0 20px rgba(120, 119, 198, 0.6);
}

.list-subtitle {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.7);
    margin: 5px 0 0 0;
    font-weight: 400;
    line-height: 1.5;
}

/* 添加按钮样式 */
.btn-add-custom {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8) 0%,
        rgba(255, 119, 198, 0.8) 100%);
    border: 1px solid rgba(120, 119, 198, 0.5);
    color: #fff;
    padding: 12px 24px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.3);
}

.btn-add-custom:hover {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 1) 0%,
        rgba(255, 119, 198, 1) 100%);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(120, 119, 198, 0.4);
    color: #fff;
    text-decoration: none;
}

/* 列表主体 */
.list-body {
    padding: 30px;
}

/* 模板列表 */
.templates-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 模板项目样式 */
.template-item {
    background: rgba(20, 20, 30, 0.8);
    border: 1px solid rgba(120, 119, 198, 0.2);
    border-radius: 16px;
    padding: 25px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.template-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(120, 119, 198, 0.1),
        transparent);
    transition: left 0.8s ease;
}

.template-item:hover::before {
    left: 100%;
}

.template-item:hover {
    border-color: rgba(120, 119, 198, 0.5);
    box-shadow: 0 15px 40px rgba(120, 119, 198, 0.2);
    transform: translateY(-3px);
}

/* 模板内容布局 */
.template-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 25px;
    position: relative;
    z-index: 2;
}

/* 模板缩略图 */
.template-thumbnail {
    width: 120px;
    height: 80px;
    border-radius: 12px;
    overflow: hidden;
    flex-shrink: 0;
    background: rgba(30, 30, 40, 0.8);
    border: 1px solid rgba(120, 119, 198, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

.template-thumb-placeholder {
    color: rgba(120, 119, 198, 0.5);
    font-size: 24px;
    text-shadow: 0 0 10px rgba(120, 119, 198, 0.3);
}

/* 模板信息 */
.template-info {
    flex: 1;
    min-width: 0;
}

.template-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.template-name {
    font-size: 20px;
    font-weight: 700;
    color: #fff;
    margin: 0;
    text-shadow: 0 0 10px rgba(120, 119, 198, 0.5);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.template-badges {
    display: flex;
    gap: 8px;
}

.badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-type {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8),
        rgba(255, 119, 198, 0.8));
    color: white;
    text-shadow: 0 0 10px rgba(120, 119, 198, 0.8);  
}

.template-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 8px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
}

.meta-item i {
    color: rgba(120, 119, 198, 0.8);
}

.template-description {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.5);
    margin: 8px 0 0 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 模板操作区域 */
.template-actions {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-shrink: 0;
}

/* 状态开关样式 */
.status-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    user-select: none;
    transition: all 0.3s ease;
    padding: 6px 10px;
    border-radius: 12px;
    background: rgba(20, 20, 30, 0.6);
    border: 1px solid rgba(120, 119, 198, 0.2);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.status-toggle:hover {
    background: rgba(120, 119, 198, 0.1);
    border-color: rgba(120, 119, 198, 0.4);
    transform: scale(1.02);
}

.switch {
    position: relative;
    display: inline-block;
    width: 36px;
    height: 18px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    border-radius: 18px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.slider:before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 14px;
    height: 14px;
    background: #fff;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
}

input:checked + .slider:before {
    transform: translateX(18px);
}

.status-label {
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: rgba(255, 255, 255, 0.8);
    transition: color 0.3s ease;
    min-width: 32px;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 12px;
    flex-shrink: 0;
}

.btn-action {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    border: none;
    border-radius: 10px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    min-width: 85px;
    justify-content: center;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent);
    transition: left 0.5s ease;
}

.btn-action:hover::before {
    left: 100%;
}

.btn-action i {
    transition: transform 0.3s ease;
    filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.3));
}

.btn-action:hover i {
    transform: scale(1.1);
}

/* 设计按钮 */
.btn-design {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8) 0%,
        rgba(255, 119, 198, 0.8) 50%,
        rgba(120, 219, 255, 0.8) 100%) !important;
    border-color: rgba(120, 119, 198, 0.6) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.btn-design:hover {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 1) 0%,
        rgba(255, 119, 198, 1) 50%,
        rgba(120, 219, 255, 1) 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.4);
    color: #ffffff !important;
    text-decoration: none;
}

/* 编辑按钮 */
.btn-edit {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.8) 0%,
        rgba(37, 99, 235, 0.8) 50%,
        rgba(29, 78, 216, 0.8) 100%) !important;
    border-color: rgba(59, 130, 246, 0.6) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.btn-edit:hover {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 1) 0%,
        rgba(37, 99, 235, 1) 50%,
        rgba(29, 78, 216, 1) 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    color: #ffffff !important;
    text-decoration: none;
}

/* 删除按钮 */
.btn-delete {
    background: linear-gradient(135deg,
        rgba(239, 68, 68, 0.8) 0%,
        rgba(220, 38, 38, 0.8) 50%,
        rgba(185, 28, 28, 0.8) 100%) !important;
    border-color: rgba(239, 68, 68, 0.6) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.btn-delete:hover {
    background: linear-gradient(135deg,
        rgba(239, 68, 68, 1) 0%,
        rgba(220, 38, 38, 1) 50%,
        rgba(185, 28, 28, 1) 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
    color: #ffffff !important;
    text-decoration: none;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 30px;
    color: rgba(255, 255, 255, 0.6);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    color: rgba(120, 119, 198, 0.5);
    text-shadow: 0 0 20px rgba(120, 119, 198, 0.3);
}

.empty-title {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 10px;
    color: #fff;
    text-shadow: 0 0 10px rgba(120, 119, 198, 0.5);
}

.empty-subtitle {
    margin-bottom: 30px;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.5;
}



/* 表单样式 */
.form-header {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.15) 0%,
        rgba(255, 119, 198, 0.1) 50%,
        rgba(120, 219, 255, 0.15) 100%);
    padding: 30px;
    border-bottom: 1px solid rgba(120, 119, 198, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.form-title-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.form-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.3),
        rgba(255, 119, 198, 0.3));
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #fff;
    text-shadow: 0 0 20px rgba(120, 119, 198, 0.8);
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.3);
}

.form-title {
    font-family: 'Orbitron', monospace;
    font-size: 28px;
    font-weight: 700;
    color: #fff;
    margin: 0;
    text-shadow: 0 0 20px rgba(120, 119, 198, 0.6);
}

.form-subtitle {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.7);
    margin: 5px 0 0 0;
    font-weight: 400;
    line-height: 1.5;
}

.form-container {
    max-width: 800px;
    margin: 0 auto;
    background: rgba(20, 20, 30, 0.8);
    padding: 40px 50px;
    border-radius: 16px;
    border: 1px solid rgba(120, 119, 198, 0.2);
    box-shadow: 0 15px 40px rgba(120, 119, 198, 0.2);
}

/* 表单元素样式 */
.form-group {
    margin-bottom: 35px;
}

.form-label {
    display: block;
    margin-bottom: 12px;
    font-weight: 600;
    color: #fff;
    font-size: 16px;
    text-shadow: 0 0 10px rgba(120, 119, 198, 0.5);
}

.form-label.required::after {
    content: ' *';
    color: #ff6b6b;
    font-weight: bold;
}

.form-control {
    width: 100%;
    padding: 16px 20px;
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 12px;
    background: rgba(30, 30, 40, 0.8);
    color: #fff;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    min-height: 50px;
}

.form-control:focus {
    outline: none;
    border-color: rgba(120, 119, 198, 0.8);
    box-shadow: 0 0 20px rgba(120, 119, 198, 0.3);
    background: rgba(30, 30, 40, 0.9);
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

/* 文本域特殊样式 */
textarea.form-control {
    min-height: 120px;
    resize: vertical;
    line-height: 1.5;
}

/* 选择框样式 - 旧版本 */
select.form-control {
    cursor: pointer;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgba(255,255,255,0.7)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 16px center;
    background-size: 16px;
    padding-right: 50px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

/* 复选框样式 - 与系统其他模块保持一致 */
.form-check {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 12px;
    /* background: rgba(30, 30, 40, 0.6); */
    border: 1px solid rgba(120, 119, 198, 0.2);
    transition: all 0.3s ease;
    margin-top: 10px;
    cursor: pointer;
    user-select: none;
}

.form-check:hover {
    background: rgba(35, 35, 45, 0.8);
    border-color: rgba(120, 119, 198, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(120, 119, 198, 0.15);
}

.form-check-input {
    display: none;
}

.checkbox-custom {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(120, 119, 198, 0.4);
    border-radius: 6px;
    position: absolute;
    background: rgba(30, 30, 40, 0.8);
    transition: all 0.3s ease;
}

.form-check-input:checked + .checkbox-custom {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.8), rgba(255, 119, 198, 0.6));
    border-color: rgba(120, 119, 198, 0.8);
    box-shadow: 0 0 15px rgba(120, 119, 198, 0.3);
    transform: scale(1.05);
}

.form-check-input:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-size: 12px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.form-check-label {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: color 0.3s ease;
    margin-left: 30px;
}

.form-check:hover .form-check-label {
    color: rgba(255, 255, 255, 1);
}

.form-check-label i {
    color: rgba(120, 119, 198, 0.7);
    font-size: 14px;
    transition: color 0.3s ease;
}

.form-check:hover .form-check-label i {
    color: rgba(120, 119, 198, 0.9);
}

/* 复选框帮助文本样式 */
.checkbox-help {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 4px;
    margin-left: 32px;
    line-height: 1.4;
}

/* 表单操作按钮 */
.form-actions {
    display: flex;
    gap: 20px;
    margin-top: 50px;
    justify-content: center;
    padding: 30px;
    border-top: 1px solid rgba(120, 119, 198, 0.2);
    background: linear-gradient(135deg, rgba(15, 15, 15, 0.3), rgba(25, 25, 35, 0.2));
    border-radius: 0 0 16px 16px;
    margin-left: -30px;
    margin-right: -30px;
    margin-bottom: -30px;
}

.btn-primary {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8) 0%,
        rgba(255, 119, 198, 0.8) 100%);
    border: 1px solid rgba(120, 119, 198, 0.5);
    color: #fff;
    padding: 16px 32px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.3);
    cursor: pointer;
    font-size: 16px;
    min-width: 140px;
    justify-content: center;
}

.btn-primary:hover {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 1) 0%,
        rgba(255, 119, 198, 1) 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(120, 119, 198, 0.4);
    color: #fff !important;
    text-decoration: none;
    border: 1px solid rgba(120, 119, 198, 0.8) !important;
}

.btn-success {
    background: linear-gradient(135deg,
        rgba(34, 197, 94, 0.8) 0%,
        rgba(22, 163, 74, 0.8) 100%);
    border: 1px solid rgba(34, 197, 94, 0.5);
    color: #fff;
    padding: 16px 32px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
    font-size: 16px;
    min-width: 140px;
    justify-content: center;
}

.btn-success:hover {
    background: linear-gradient(135deg,
        rgba(34, 197, 94, 1) 0%,
        rgba(22, 163, 74, 1) 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(34, 197, 94, 0.4);
    color: #fff !important;
    text-decoration: none;
    border: 1px solid rgba(34, 197, 94, 0.8) !important;
}

.btn-secondary {
    background: linear-gradient(135deg,
        rgba(107, 114, 128, 0.8) 0%,
        rgba(75, 85, 99, 0.8) 100%);
    border: 1px solid rgba(107, 114, 128, 0.5);
    color: #fff;
    padding: 16px 32px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
    font-size: 16px;
    min-width: 140px;
    justify-content: center;
}

.btn-secondary:hover {
    background: linear-gradient(135deg,
        rgba(107, 114, 128, 1) 0%,
        rgba(75, 85, 99, 1) 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(107, 114, 128, 0.4);
    color: #fff !important;
    text-decoration: none;
    border: 1px solid rgba(107, 114, 128, 0.8) !important;
}



/* 响应式设计 */
@media (max-width: 768px) {
    .list-header-content,
    .form-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .list-title-section {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .template-content {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .template-thumbnail {
        width: 100%;
        height: 120px;
    }

    .template-actions {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .action-buttons {
        justify-content: center;
    }

    .list-body {
        padding: 20px;
    }

    .list-header {
        padding: 20px;
    }

    .form-container {
        max-width: 100%;
        margin: 0 20px;
        padding: 30px 25px;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-label {
        font-size: 14px;
        margin-bottom: 8px;
    }

    .form-control {
        padding: 14px 16px;
        font-size: 14px;
        min-height: 45px;
    }

    .form-actions {
        flex-direction: column;
        gap: 15px;
        margin-top: 30px;
        padding-top: 20px;
    }

    .btn-primary,
    .btn-success {
        padding: 14px 24px;
        font-size: 14px;
        min-width: 120px;
    }
}

/* 新闻模块样式的表单布局 */
.form-body {
    padding: 30px;
}

/* 表单区域样式 */
.form-section {
    background: rgba(20, 20, 30, 0.6);
    border: 1px solid rgba(120, 119, 198, 0.2);
    border-radius: 16px;
    margin-bottom: 25px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.form-section:hover {
    border-color: rgba(120, 119, 198, 0.3);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    transform: translateY(-2px);
}

.section-header {
    background: linear-gradient(135deg, rgba(15, 15, 15, 0.5), rgba(25, 25, 35, 0.4));
    padding: 22px 25px;
    border-bottom: 1px solid rgba(120, 119, 198, 0.2);
}

.section-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-title i {
    color: rgba(120, 119, 198, 0.8);
    font-size: 16px;
}

.form-section .form-row,
.form-section .form-group {
    padding: 0 25px;
}

.form-section .form-row:first-of-type,
.form-section .form-group:first-of-type {
    padding-top: 35px;
}

.form-section .form-row:last-of-type,
.form-section .form-group:last-of-type {
    padding-bottom: 25px;
}

/* 确保form-row内的form-group不会有额外的padding */
.form-section .form-row .form-group {
    padding: 0;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
    align-items: start;
}

.form-row:last-child {
    margin-bottom: 0;
}

.form-row .form-group {
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.form-row .form-group .form-label {
    margin-bottom: 8px;
    flex-shrink: 0;
}

.form-row .form-group .form-input,
.form-row .form-group .form-textarea,
.form-row .form-group .form-select {
    flex: 1;
    min-height: 44px;
}

.form-row .form-group .form-help {
    margin-top: 6px;
    flex-shrink: 0;
}

.form-group {
    margin-bottom: 20px;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    transition: color 0.3s ease;
}

.form-label.required::after {
    content: '*';
    color: #ff6b6b;
    margin-left: 4px;
    font-weight: bold;
    text-shadow: 0 0 4px rgba(255, 107, 107, 0.3);
}

.form-label i {
    color: rgba(120, 119, 198, 0.8);
    width: 14px;
    flex-shrink: 0;
    transition: color 0.3s ease;
}

.form-group:hover .form-label {
    color: rgba(255, 255, 255, 1);
}

.form-group:hover .form-label i {
    color: rgba(120, 119, 198, 1);
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    background: rgba(15, 15, 15, 0.6);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 10px;
    padding: 12px 16px;
    color: #fff;
    font-size: 14px;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-input:hover,
.form-textarea:hover,
.form-select:hover {
    border-color: rgba(120, 119, 198, 0.4);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: rgba(120, 119, 198, 0.6);
    box-shadow: 0 0 0 3px rgba(120, 119, 198, 0.1), 0 6px 20px rgba(120, 119, 198, 0.2);
    background: rgba(15, 15, 15, 0.8);
    transform: translateY(-1px);
}

.form-input::placeholder,
.form-textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
    font-family: inherit;
}

.form-help {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
    margin-top: 6px;
    line-height: 1.4;
}

/* 选择框样式 */
.form-select {
    cursor: pointer;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgba(255,255,255,0.7)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 16px center;
    background-size: 16px;
    padding-right: 50px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    position: relative;
    z-index: 1;
    isolation: isolate;
}

/* 确保选择框不影响其他元素 */
.form-select:focus {
    z-index: 2;
    position: relative;
    isolation: isolate;
}

/* 选择框下拉选项样式 */
.form-select option {
    background: rgba(20, 20, 30, 0.95);
    color: #fff;
    padding: 8px 12px;
    border: none;
}

.form-select option:hover,
.form-select option:checked {
    background: rgba(120, 119, 198, 0.3);
}

/* 防止选择框影响后续元素的样式 */
.form-select + *,
.form-select ~ * {
    position: relative;
    z-index: auto;
}

/* 强制重置按钮区域的样式 */
.form-actions {
    contain: layout style paint;
}

.form-actions * {
    position: relative;
    z-index: auto;
}

/* 修复按钮样式确保不被覆盖 */
.btn-primary,
.btn-secondary,
.btn-success {
    position: relative;
    z-index: 10;
    border: 1px solid rgba(120, 119, 198, 0.5) !important;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8) 0%,
        rgba(255, 119, 198, 0.8) 100%) !important;
}

.btn-secondary {
    background: linear-gradient(135deg,
        rgba(107, 114, 128, 0.8) 0%,
        rgba(75, 85, 99, 0.8) 100%) !important;
    border: 1px solid rgba(107, 114, 128, 0.5) !important;
}

.btn-success {
    background: linear-gradient(135deg,
        rgba(34, 197, 94, 0.8) 0%,
        rgba(22, 163, 74, 0.8) 100%) !important;
    border: 1px solid rgba(34, 197, 94, 0.5) !important;
}

/* 表单元素层级管理 */
.form-section {
    position: relative;
    z-index: 1;
}

.form-actions {
    position: relative;
    z-index: 100 !important;
    isolation: isolate;
}

.form-actions .btn-primary,
.form-actions .btn-secondary,
.form-actions .btn-success {
    position: relative;
    z-index: 101 !important;
    isolation: isolate;
}

/* 确保按钮样式不被覆盖 - 最高优先级 */
.page-builder-container button.btn-primary,
.page-builder-container a.btn-primary,
.form-actions button.btn-primary,
.form-actions a.btn-primary,
button.btn-primary,
a.btn-primary,
[class*="btn-primary"] {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8) 0%,
        rgba(255, 119, 198, 0.8) 100%) !important;
    border: 1px solid rgba(120, 119, 198, 0.5) !important;
    color: #fff !important;
    text-decoration: none !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 10px !important;
    padding: 16px 32px !important;
    border-radius: 12px !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    min-width: 140px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.3) !important;
    cursor: pointer !important;
    position: relative !important;
    z-index: 999 !important;
    isolation: isolate !important;
}

.page-builder-container button.btn-primary:hover,
.page-builder-container a.btn-primary:hover,
.form-actions button.btn-primary:hover,
.form-actions a.btn-primary:hover,
button.btn-primary:hover,
a.btn-primary:hover,
[class*="btn-primary"]:hover {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 1) 0%,
        rgba(255, 119, 198, 1) 100%) !important;
    color: #fff !important;
    text-decoration: none !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 12px 35px rgba(120, 119, 198, 0.4) !important;
    border: 1px solid rgba(120, 119, 198, 0.8) !important;
    position: relative !important;
    z-index: 999 !important;
    isolation: isolate !important;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .btn-primary, .btn-secondary, .btn-submit, .btn-cancel {
        justify-content: center;
        width: 100%;
    }
}

# 📋 ThinkPHP6企业级应用开发规范和注意事项

**三只鱼网络科技 | 韩总 | 2024-12-10**  
**核心技术栈：PHP 8.0+ + ThinkPHP6 + Redis + MySQL + UniApp + Vue.js**

## 🎯 开发前必读

### ⚠️ 重要原则
1. **必须先读取现有文件内容再进行分析，不做假设和臆想**
2. **基于实际代码结构给出建议，遵循TP6编码规范和PSR标准**
3. **优先使用框架内置功能，避免重复造轮子**
4. **直接给出技术方案，减少模糊表达，不使用举例和假设**

### 📚 开发前准备
- **必须先阅读项目docs/目录下的相关文档**
- **不要在没有先读取现有文件的情况下尝试编辑它**
- **检查现有代码结构和命名规范，按照程序原本逻辑走**

## 🏗️ 项目架构规范

### 📁 文件组织结构
```
app/
├── controller/          # 控制器
│   ├── admin/          # 后台管理控制器
│   └── api/            # API接口控制器
├── model/              # 数据模型
├── view/               # 视图模板
│   ├── admin/          # 后台管理视图
│   └── index/          # 前台视图
├── middleware/         # 中间件
├── validate/           # 验证器
└── common/             # 公共类库
```

### 🎨 统一的页面风格
**所有新功能必须遵循系统标准头部样式：**
- **使用统一的header结构** - `{include file="admin/common/header"}`
- **标准的内容头部** - 使用`content-header`和`list-header`
- **品牌化图标和标题** - 配合相应图标
- **CSS/JS文件引用** - 使用`{include file="admin/common/css"}`和`{include file="admin/common/js"}`

### 📊 统计卡片标准
```html
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-icon"></i>
        </div>
        <div class="stat-content">
            <div class="stat-number">数字</div>
            <div class="stat-label">标签</div>
        </div>
    </div>
</div>
```

## 💻 代码开发规范

### 🔧 TP6框架规范
- **控制器继承app\BaseController，模型使用think\Model**
- **验证器放在app\validate目录，中间件注册到config\middleware.php**
- **路由定义在route/目录下对应文件**
- **编写代码逻辑一定按照当前框架代码逻辑走不要乱写**

### 📝 开发新功能规范
- **每次开发新模块之前先说明你的理解，确认没有问题之后再开发**
- **一定要按照程序原本逻辑和样式和代码书写格式走**
- **第一时间先借鉴原有框架的完整逻辑去构思、创建、修改**
- **不要乱创建导致出错无法使用**

### 🎯 代码质量控制
- **请不要随意乱创建文件和做调试信息防止做完测试后代码臃肿**
- **写代码的时候不要写打印语句尤其对前端处理的时候**
- **坚持代码简洁原则：能用3行代码解决的绝不写10行**
- **避免重复代码：复用现有方法和逻辑，不重复造轮子**
- **修改代码时只改必要部分，不影响其他功能**
- **优先使用TP6内置方法，避免自定义复杂实现**

### 🔒 安全和性能
- **所有用户输入必须验证和过滤，使用TP6内置的安全机制**
- **页面缓存使用think\facade\Cache，会话数据存储到Redis**
- **优先使用模型操作，使用查询构造器进行复杂查询**
- **敏感操作记录日志，API接口实现权限验证**

## 📚 文档管理规范

### 📄 文档命名和组织
- **根目录README.md使用英文命名，docs/目录下所有文档使用中文命名**
- **开发完代码之后更新需求文档进度状态**
- **保持代码注释和文档同步**

### 📋 开发者信息规范
**所有新创建的文件必须添加开发者信息头部注释：**
```php
<?php
/**
 * 三只鱼网络科技 | 韩总 | 日期
 * 文件描述 - ThinkPHP6企业级应用
 */
```

## 🔄 公共资源复用规范

### 🎨 样式和脚本复用
- **新功能开发前必须先检查现有公共资源：CSS、JS、PHP类**
- **优先使用public/assets/css/common.css中的样式类**
- **复用app/common/目录下的公共控制器、服务类、特性**
- **禁止重复编写已存在的样式和方法**

### 📱 响应式布局规范
- **编写HTML结构时严格遵循Bootstrap 12列网格系统**
- **每行列宽总和必须等于12，强制使用Bootstrap标准间距类**
- **必须添加响应式断点：至少包含col-md-*和col-lg-***
- **优先使用Flexbox对齐，限制自定义CSS类数量<30个**
- **避免容器嵌套，不在container内再嵌套container**

## 🛠️ 工具使用规范

### 🔧 核心工具使用
- **项目检查：php tools/tool_manager.php batch . (项目启动时)**
- **数据库分析：php tools/db_analyzer.php info (获取表结构)**
- **布局检测：php tools/layout_analyzer.php check 文件 (前端开发)**
- **性能分析：php tools/performance_analyzer.php full (性能优化)**
- **CSS/JS优化：php tools/css_js_optimizer.php full (代码清理时)**
- **Windows环境严格使用PowerShell语法，禁止Linux命令**

### 📊 项目检查增强功能
- **架构完整性检测**：自动检查MVC分层、路由配置、中间件注册
- **业务逻辑分析**：验证控制器方法、模型关联、服务层封装
- **数据一致性检查**：表结构与模型字段映射、外键关系验证
- **安全机制审计**：权限验证、输入过滤、SQL注入防护检查
- **性能瓶颈识别**：慢查询检测、缓存策略评估、索引优化建议
- **代码规范审查**：PSR标准、命名规范、注释完整性检查
- **依赖关系分析**：组件耦合度、循环依赖、版本兼容性检查

## 🗄️ 数据库管理规范

### 📋 数据库操作原则
- **优先在website.sql中增加新表和数据，不强制执行整个迁移**
- **增量更新：只添加新字段/内容，不强制全量迁移**
- **数据安全：开发前备份数据库到backups目录**
- **恢复机制：数据损坏时从备份恢复，不重新迁移**

### 🔄 迁移策略
```powershell
# 正确的数据库更新方式
1. 备份当前数据库
2. 只在website.sql中添加新的表结构和数据
3. 创建专门的增量更新脚本
4. 避免使用--force参数强制执行
```

## ⚠️ 错误处理和调试

### 🚨 错误处理规范
- **生产环境禁止显示详细错误信息，统一返回友好提示**
- **开发环境错误信息详细记录到日志文件，不在页面显示**
- **异常处理必须使用try-catch包装，不允许未处理异常**
- **API接口错误使用统一的JSON格式返回**

### 🔍 调试规范
- **禁止在生产代码中留下调试语句**
- **使用框架内置的日志功能记录调试信息**
- **测试完成后及时清理测试文件和调试代码**

## 📱 API接口标准化

### 🌐 接口规范
- **所有API响应必须包含success、message、data三个字段**
- **错误码使用标准HTTP状态码+自定义业务码**
- **接口参数验证使用TP6验证器，不在控制器中验证**
- **API版本通过路由前缀管理：/api/v1/、/api/v2/**

## 🚀 环境配置和部署

### ⚙️ 环境管理
- **开发/测试/生产环境配置分离，使用.env环境变量管理**
- **数据库迁移自动化：php think migrate:run命令执行**
- **配置文件敏感信息不提交到版本库**
- **TP6应用部署：public目录作为Web根目录，runtime目录权限设置**

## 📊 质量控制标准

### 🎯 质量指标
- **布局质量：≥85分(新页面)，≥80分(上线标准)**
- **性能评估：数据库查询<500ms，缓存命中率>80%**
- **代码质量：无Critical问题，High级问题<3个**
- **响应式覆盖：断点覆盖度>60%，移动端适配完整**

## 📝 CKEditor编辑器开发规范

### 🎯 编辑器配置标准
**所有使用CKEditor的页面必须保持配置一致性：**

#### 📋 标准Toolbar配置
```javascript
toolbar: [
    'heading',
    'bold',
    'italic',
    'underline',
    'numberedList',
    'bulletedList',
    'outdent',
    'indent',
    'link',
    'insertTable',
    'blockQuote',
    'undo',
    'redo'
],
```

#### 🎨 Heading选项配置
```javascript
heading: {
    options: [
        { model: 'paragraph', title: '正文', class: 'ck-heading_paragraph' },
        { model: 'heading1', view: 'h1', title: '标题 1', class: 'ck-heading_heading1' },
        { model: 'heading2', view: 'h2', title: '标题 2', class: 'ck-heading_heading2' },
        { model: 'heading3', view: 'h3', title: '标题 3', class: 'ck-heading_heading3' },
        { model: 'heading4', view: 'h4', title: '标题 4', class: 'ck-heading_heading4' }
    ]
},
```

#### 📊 Table功能配置
```javascript
table: {
    contentToolbar: [
        'tableColumn',
        'tableRow',
        'mergeTableCells',
        'tableCellProperties',
        'tableProperties'
    ]
},
```

### 🖼️ 图片上传按钮集成

#### ⚠️ 关键注意事项
1. **统一按钮样式**：必须使用 `ck-button_with-text` 类名
2. **图标标准化**：统一使用FontAwesome `fas fa-images` 图标
3. **文本标签**：显示"图片"文字，提升用户体验
4. **检测机制**：使用 `data-upload-button="true"` 属性避免重复添加

#### 🔧 标准实现代码
```javascript
function addImageUploadButton() {
    // 多种方式查找工具栏
    let toolbarItems = document.querySelector('.ck-toolbar .ck-toolbar__items');
    if (!toolbarItems) {
        toolbarItems = document.querySelector('.ck-toolbar__items');
    }
    if (!toolbarItems) {
        toolbarItems = document.querySelector('.ck-toolbar');
    }

    // 检查按钮是否已存在
    if (document.querySelector('[data-upload-button="true"]')) {
        return true;
    }

    // 创建图片上传按钮
    const imageButton = document.createElement('button');
    imageButton.className = 'ck-button ck-button_with-text';
    imageButton.setAttribute('data-upload-button', 'true');

    // 创建图标容器
    const iconContainer = document.createElement('span');
    iconContainer.className = 'ck-button__icon';

    // 使用FontAwesome图标
    const icon = document.createElement('i');
    icon.className = 'fas fa-images';
    icon.style.cssText = `
        font-size: 12px !important;
        color: rgba(255, 255, 255, 0.8) !important;
        opacity: 1 !important;
        visibility: visible !important;
        display: inline-block !important;
    `;

    // 添加文本标签
    const textLabel = document.createElement('span');
    textLabel.className = 'ck-button__label';
    textLabel.textContent = '图片';

    // 组装按钮
    iconContainer.appendChild(icon);
    imageButton.appendChild(iconContainer);
    imageButton.appendChild(textLabel);

    // 绑定点击事件
    imageButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        if (editorImageUploader) {
            editorImageUploader.show();
        }
    });

    // 插入到工具栏末尾
    toolbarItems.appendChild(imageButton);
    return true;
}
```

### 🎛️ 编辑器初始化规范

#### 📋 必需的CSS和JS引用
```html
<!-- CKEditor 5 CSS -->
<link rel="stylesheet" href="/assets/css/ckeditor.css">

<!-- CKEditor 5 Classic版本 -->
<script src="/assets/js/ckeditor.js"></script>
<!-- CKEditor 5 中文语言包 -->
<script src="/assets/js/zh-cn.js"></script>
<!-- 图片上传弹窗组件 -->
<script src="/assets/js/image-uploader.js"></script>
<!-- 图片选择器扩展 -->
<script src="/assets/js/image-selector-extension.js"></script>
```

#### ⚙️ 编辑器实例化标准
```javascript
ClassicEditor
    .create(document.querySelector('#editor'), {
        language: 'zh-cn',
        placeholder: '请输入内容...',
        // ... toolbar和其他配置
    })
    .then(newEditor => {
        editor = newEditor;
        window.editor = newEditor;

        // 设置编辑器高度
        const editingView = editor.editing.view;
        editingView.change(writer => {
            writer.setStyle('min-height', '300px', editingView.document.getRoot());
            writer.setStyle('max-height', '500px', editingView.document.getRoot());
        });

        // 添加图片上传按钮 - 延时确保DOM完全加载
        setTimeout(() => {
            addImageUploadButton();
        }, 1000);
    })
    .catch(error => {
        console.error('❌ CKEditor 5 初始化失败:', error);
        // 降级到普通textarea
        $('#editor').addClass('form-textarea').attr('rows', 15).show();
        showMessage('编辑器加载失败，已切换到基础模式', 'warning');
        window.editor = null;
    });
```

### 🚨 常见问题和解决方案

#### 1. 图片按钮不显示
- **原因**：工具栏查找失败或按钮样式不正确
- **解决**：使用多种方式查找工具栏，确保使用正确的CSS类名

#### 2. 图片上传功能失效
- **原因**：图片上传组件未正确初始化
- **解决**：确保在编辑器初始化后再添加按钮，检查组件实例

#### 3. 编辑器样式异常
- **原因**：CSS冲突或缺少必要的样式文件
- **解决**：确保引用了ckeditor.css，检查自定义样式冲突

#### 4. 内容同步问题
- **原因**：表单提交时未同步编辑器内容
- **解决**：在表单提交前调用 `editor.getData()` 同步内容

### 📝 表单验证集成
```javascript
// 表单提交前同步编辑器内容
$('form').on('submit', function(e) {
    if (window.editor) {
        const data = window.editor.getData();
        $('#editor').val(data);

        // 验证编辑器内容
        if (!data || data.trim() === '') {
            e.preventDefault();
            showMessage('请输入内容', 'warning');
            setTimeout(() => {
                if (window.editor) {
                    window.editor.editing.view.focus();
                }
            }, 100);
            return false;
        }
    }
});
```

### 🎯 开发检查清单
- [ ] 是否使用了标准的toolbar配置？
- [ ] 是否正确实现了图片上传按钮？
- [ ] 是否引用了所有必需的CSS和JS文件？
- [ ] 是否设置了合适的编辑器高度？
- [ ] 是否实现了内容同步和验证？
- [ ] 是否处理了编辑器初始化失败的降级方案？

## 🔄 开发完成后的清理工作

### 🧹 必须清理的内容
1. **删除测试文件和临时文件**
2. **移除调试代码和打印语句**
3. **清理无用的CSS和JS代码**
4. **删除未使用的图片和资源文件**
5. **更新文档中的开发进度状态**

### 📝 文档更新要求
1. **更新项目状态文档**
2. **记录新功能的使用说明**
3. **更新API文档（如有接口变更）**
4. **记录已知问题和解决方案**

---

## 🎯 开发流程总结

### 📋 标准开发流程
1. **阅读相关文档** → 2. **分析现有代码** → 3. **制定开发计划** → 4. **编写代码** → 5. **测试验证** → 6. **清理优化** → 7. **更新文档**

### ⚡ 快速检查清单
- [ ] 是否阅读了相关文档？
- [ ] 是否检查了现有代码结构？
- [ ] 是否遵循了统一的页面风格？
- [ ] 是否复用了现有的公共资源？
- [ ] 是否添加了开发者信息注释？
- [ ] 是否进行了安全和性能检查？
- [ ] 是否清理了测试文件和调试代码？
- [ ] 是否更新了相关文档？

**严格遵循这些规范，确保代码质量和项目的可维护性！**

---

## 🐛 开发避坑指南

### 1. 控制器和路由问题
- **控制器位置：** 必须放在`app/admin/controller/`目录，不要放错位置
- **路由配置：** 检查`route/admin.php`是否正确配置路由规则
- **命名规范：** 控制器类名与文件名保持一致，遵循驼峰命名

### 2. 删除功能统一性
- **统一处理方式：** 使用`Session::flash + redirect`，不用JSON响应
- **前端检查：** 删除前检查关联数据，如子菜单、关联记录等
- **错误提示：** 显示具体冲突信息："请先删除子菜单：xxx、xxx"

### 3. 模态框定位异常
- **CSS冲突：** 自定义页面样式影响模态框，用`!important`强制覆盖
- **定位修复：** `position: fixed !important; z-index: 99999 !important;`

### 4. 消息提示不一致
- **统一函数：** 每个页面实现相同的`showMessage()`函数
- **样式统一：** 使用相同的alert类和图标，3秒自动消失

### 5. 界面样式问题
- **复用现有样式：** 优先使用已有的CSS类，避免重复定义
- **响应式布局：** 严格遵循Bootstrap网格系统
- **模态框居中：** 使用flexbox确保内容居中显示

### 6. 数据检查缺失
- **前端预检查：** 保存全局数据`window.currentMenus`供检查使用
- **后端完整验证：** 控制器中进行业务逻辑检查
- **友好提示：** 显示具体的冲突原因和解决建议

### 7. CKEditor编辑器问题
- **图片按钮不显示：** 检查toolbar配置和addImageUploadButton函数实现
- **按钮样式异常：** 必须使用`ck-button_with-text`类名和FontAwesome图标
- **图片上传失效：** 确保图片上传组件正确初始化，检查事件绑定
- **内容同步失败：** 表单提交前调用`editor.getData()`同步内容
- **编辑器初始化失败：** 提供textarea降级方案，显示友好错误提示

### 8. 图片删除用户体验
- **原生提示问题：** 避免使用`confirm()`，改用系统内置`showMessage()`
- **操作反馈缺失：** 删除操作后立即显示成功提示
- **状态更新延迟：** 删除后立即隐藏预览区域，更新表单字段

### 9. 编辑器配置不一致
- **toolbar差异：** 所有页面必须使用相同的toolbar配置
- **heading选项不统一：** 统一使用"正文"而非"段落"
- **table功能缺失：** 确保包含tableCellProperties和tableProperties
- **语言包缺失：** 必须引用zh-cn.js中文语言包

### 10. 图片选择器集成问题
- **组件未初始化：** 确保在DOM加载完成后初始化图片上传组件
- **事件冲突：** 避免重复绑定点击事件，使用唯一标识符检测
- **样式冲突：** 图片选择器弹窗可能被页面样式影响，使用!important强制覆盖

**核心原则：先分析现有实现，最小化修改，保持一致性**

### 🎯 CKEditor开发最佳实践
1. **参考新闻管理实现** - 作为标准模板，确保所有编辑器功能一致
2. **延时添加按钮** - 使用setTimeout确保DOM完全加载后再添加图片按钮
3. **多重检测机制** - 工具栏查找、按钮存在性检测、组件初始化检测
4. **降级方案准备** - 编辑器加载失败时自动切换到textarea模式
5. **用户体验优化** - 避免原生弹窗，使用统一的消息提示系统
```

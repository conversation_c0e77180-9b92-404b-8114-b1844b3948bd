// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: true },
  
  // CSS框架
  css: ['~/assets/css/main.css'],
  
  // 模块
  modules: [
    '@nuxtjs/tailwindcss',
    '@nuxt/image',
    '@nuxt/ui',
    '@pinia/nuxt',
    '@vueuse/nuxt',
    '@nuxtjs/google-fonts'
  ],
  
  // 运行时配置
  runtimeConfig: {
    // 私有配置（仅服务端可用）
    apiSecret: process.env.API_SECRET,
    
    // 公共配置（客户端也可用）
    public: {
      apiBase: process.env.API_BASE || 'http://localhost:8000/api',
      siteName: 'QiyeDIY企业建站系统',
      siteDescription: '专业的企业DIY建站系统，让建站变得简单',
      siteUrl: process.env.SITE_URL || 'http://localhost:3001'
    }
  },
  
  // 应用配置
  app: {
    head: {
      title: 'QiyeDIY企业建站系统',
      titleTemplate: '%s - QiyeDIY',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: '专业的企业DIY建站系统，让建站变得简单' },
        { name: 'keywords', content: 'DIY建站,企业网站,网站建设,可视化编辑' },
        { name: 'author', content: '三只鱼网络科技' },
        { property: 'og:type', content: 'website' },
        { property: 'og:site_name', content: 'QiyeDIY企业建站系统' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'canonical', href: process.env.SITE_URL || 'http://localhost:3001' }
      ]
    }
  },
  
  // Google Fonts配置
  googleFonts: {
    families: {
      'Inter': [400, 500, 600, 700],
      'Noto Sans SC': [400, 500, 600, 700]
    },
    display: 'swap',
    preload: true
  },
  
  // 图片优化配置
  image: {
    quality: 80,
    format: ['webp', 'jpg'],
    screens: {
      xs: 320,
      sm: 640,
      md: 768,
      lg: 1024,
      xl: 1280,
      xxl: 1536
    }
  },
  
  // Tailwind CSS配置
  tailwindcss: {
    cssPath: '~/assets/css/tailwind.css',
    configPath: 'tailwind.config.js'
  },
  
  // 构建配置
  build: {
    transpile: ['vue-toastification']
  },
  
  // 服务端渲染配置
  ssr: true,
  
  // 实验性功能
  experimental: {
    payloadExtraction: false
  },
  
  // 开发服务器配置
  devServer: {
    port: 3001,
    host: '0.0.0.0'
  },
  
  // 路由配置
  router: {
    options: {
      strict: false,
      sensitive: false
    }
  },
  
  // 插件配置
  plugins: [
    '~/plugins/api.client.ts',
    '~/plugins/toast.client.ts'
  ],
  
  // 中间件配置
  middleware: [
    'auth'
  ],
  
  // 组件自动导入
  components: [
    {
      path: '~/components',
      pathPrefix: false
    }
  ],
  
  // 自动导入
  imports: {
    dirs: [
      'composables',
      'utils'
    ]
  },
  
  // 类型检查
  typescript: {
    strict: true,
    typeCheck: true
  },
  
  // 安全配置
  security: {
    headers: {
      crossOriginEmbedderPolicy: false,
      contentSecurityPolicy: {
        'img-src': ["'self'", 'data:', 'https:'],
        'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
        'style-src': ["'self'", "'unsafe-inline'", 'https:']
      }
    }
  },
  
  // 预渲染配置
  nitro: {
    prerender: {
      routes: ['/']
    }
  }
})

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
    background: #f5f7fa;
    height: 100vh;
    overflow: hidden;
}

/* ========================================
   自定义滚动条样式
   ======================================== */
/* Webkit浏览器滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

::-webkit-scrollbar-thumb:active {
    background: #999999;
}

/* 属性面板专用滚动条 */
.properties-panel::-webkit-scrollbar {
    width: 6px;
}

.properties-panel::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 3px;
}

.properties-panel::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.properties-panel::-webkit-scrollbar-thumb:hover {
    background: #5a67d8;
    width: 8px;
}

/* 组件库滚动条 */
.component-library::-webkit-scrollbar {
    width: 6px;
}

.component-library::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 3px;
}

.component-library::-webkit-scrollbar-thumb {
    background: #a0aec0;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.component-library::-webkit-scrollbar-thumb:hover {
    background: #718096;
}

/* 编辑区滚动条 */
.editor-area::-webkit-scrollbar {
    width: 8px;
}

.editor-area::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 4px;
}

.editor-area::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.editor-area::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

.builder-container {
    display: flex;
    height: 100vh;
}

/* 顶部工具栏 */
.toolbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: #2d3748;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.toolbar-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.logo {
    color: white;
    font-size: 18px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo i {
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toolbar-right {
    display: flex;
    gap: 10px;
}

.toolbar-btn {
    padding: 8px 16px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.toolbar-btn:hover {
    background: #5a67d8;
}

.toolbar-btn.secondary {
    background: #4a5568;
}

.toolbar-btn.secondary:hover {
    background: #2d3748;
}

/* 组件库 */
.component-library {
    width: 250px;
    background: white;
    border-right: 1px solid #e2e8f0;
    padding: 20px;
    margin-top: 60px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 组件分类容器 */
.component-category {
    display: flex;
    flex-direction: column;
}

.library-title {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #667eea;
}

/* 组件网格容器 */
.components-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 8px;
}

.component-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px 8px;
    background: #f7fafc;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
    text-align: center;
    min-height: 70px;
}

.component-item:hover {
    background: #e6fffa;
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.15);
}

.component-icon {
    font-size: 24px;
    margin-bottom: 6px;
    color: #667eea;
}

.component-name {
    font-size: 12px;
    color: #4a5568;
    font-weight: 500;
    line-height: 1.2;
    word-break: break-all;
}

/* 编辑区 */
.editor-area {
    flex: 1;
    background: #f8fafc;
    margin-top: 60px;
    overflow-y: auto;
    position: relative;
}

.canvas {
    min-height: calc(100vh - 60px);
    padding: 20px;
}

.component-block {
    position: relative;
    margin-bottom: 0;
    border: 2px solid transparent;
    transition: all 0.2s ease;
}

.component-block:hover {
    border-color: #667eea;
}

.component-block.selected {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.component-block.dragging {
    opacity: 0.5;
    transform: scale(0.95);
}

.component-controls {
    position: absolute;
    top: 5px;
    right: 5px;
    display: flex;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: 99999;  /* 提高z-index，确保在导航栏之上 */
}

.component-block:hover .component-controls {
    opacity: 1;
}

.control-btn {
    min-width: 36px;
    height: 28px;
    background: rgba(45, 55, 72, 0.9);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    white-space: nowrap;
    padding: 0 6px;
}

.control-btn:hover {
    background: #667eea;
    transform: scale(1.1);
}



/* 属性区 */
.properties-panel {
    width: 350px;
    background: white;
    border-left: 1px solid #e2e8f0;
    padding: 20px;
    margin-top: 60px;
    overflow-y: auto;
}

.panel-title {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #667eea;
}

.property-group {
    margin-bottom: 20px;
    position: relative;
    overflow: visible;
}

.property-label {
    font-size: 12px;
    color: #718096;
    margin-bottom: 10px;
    display: block;
}

.property-input {
    width: 100%;
    padding: 8px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 14px;
}

.property-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* ========================================
   简洁专业属性面板样式
   ======================================== */

/* 属性区块样式 */
.property-section {
    background: #ffffff;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 16px;
    border: 1px solid #e2e8f0;
    position: relative;
    overflow: visible;
}

/* 区块标题样式 */
.section-title {
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 12px 0;
    padding-bottom: 8px;
    
}

/* 菜单项样式 */
.menu-item-row {
    margin-bottom: 8px;
}

.menu-item-inputs {
    display: flex;
    gap: 8px;
    align-items: center;
}

.menu-item-inputs input {
    flex: 1;
}

.delete-btn {
    background: #e53e3e;
    color: white;
    border: none;
    padding: 6px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    white-space: nowrap;
}

.delete-btn:hover {
    background: #c53030;
}

.add-btn {
    width: 100%;
    padding: 8px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-top: 8px;
}

.add-btn:hover {
    background: #5a67d8;
}

/* 复选框组样式 */
.checkbox-group {
    display: flex;
    flex-direction: row;
    gap: 16px;
    flex-wrap: wrap;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    font-size: 13px;
    color: #4a5568;
    white-space: nowrap;
    flex: 1;
}

.checkbox-item input[type="checkbox"] {
    margin: 0;
    transform: scale(1.1);
}

/* 颜色控件行样式 */
.color-row {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

.color-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.color-item label {
    font-size: 11px;
    color: #718096;
    margin: 0;
}

.color-item input {
    width: 100%;
    height: 32px;
    padding: 2px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
}

/* 风格选择器样式 */
.style-mode-toggle {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

.style-mode-btn {
    flex: 1;
    padding: 10px 12px;
    border: 2px solid #e2e8f0;
    background: #ffffff;
    color: #4a5568;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
    text-align: center;
}

.style-mode-btn:hover {
    border-color: #667eea;
    background: #f7fafc;
}

.style-mode-btn.active {
    border-color: #667eea;
    background: #667eea;
    color: white;
}

.style-mode-desc {
    font-size: 12px;
    color: #718096;
    text-align: center;
    margin-top: 4px;
    font-style: italic;
}

/* 首页模板选择器样式 */
.homepage-templates {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-bottom: 6px;
}

.homepage-template-card {
    display: flex;
    align-items: center;
    padding: 12px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #ffffff;
    position: relative;
    overflow: hidden;
}

.homepage-template-card.compact {
    padding: 8px 12px;
    min-height: 40px;
}

.homepage-template-card:hover {
    border-color: #667eea;
    background: #f7fafc;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.homepage-template-card:active {
    transform: translateY(0);
}

.template-icon {
    font-size: 18px;
    margin-right: 12px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    color: white;
}

.homepage-template-card.compact .template-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
    margin-right: 12px;
    border-radius: 6px;
}

/* 首页模板信息样式 - 统一优化 */
.homepage-template-card .template-info {
    flex: 1;
    min-width: 0;
    display: flex;
    align-items: center;
    text-align: left;
    padding: 0;
}

.homepage-template-card .template-name {
    font-size: 13px;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
    line-height: 1.2;
}

.template-desc {
    font-size: 13px;
    color: #718096;
    line-height: 1.4;
}

.template-apply {
    padding: 6px 12px;
    background: #667eea;
    color: white;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.homepage-template-card.compact .template-apply {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
}

.homepage-template-card:hover .template-apply {
    background: #5a67d8;
    transform: scale(1.05);
}

.template-tips {
    background: #f0f4f8;
    padding: 12px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.template-tips small {
    color: #4a5568;
    font-size: 12px;
    line-height: 1.4;
}

/* 滑动条控件样式 */
.range-wrapper {
    margin-top: 8px;
}

.range-control {
    display: flex;
    align-items: center;
    gap: 12px;
    background: #f8fafc;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.range-slider {
    flex: 1;
    height: 8px;
    border-radius: 4px;
    background: #e2e8f0;
    outline: none;
    -webkit-appearance: none;
    cursor: pointer;
}

.range-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: grab;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
    transition: all 0.2s ease;
    border: 2px solid #ffffff;
}

.range-slider::-webkit-slider-thumb:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: scale(1.15);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.6);
}

.range-slider::-webkit-slider-thumb:active {
    cursor: grabbing;
    transform: scale(1.1);
}

.range-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: grab;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
    transition: all 0.2s ease;
}

.range-slider::-moz-range-thumb:hover {
    transform: scale(1.15);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.6);
}

.range-value {
    min-width: 55px;
    text-align: center;
    font-weight: 600;
    color: #667eea;
    font-size: 13px;
    border-radius: 4px;
}

.range-hint {
    font-size: 11px;
    color: #718096;
    margin-top: 6px;
    text-align: left;
}

.range-tips {
    margin-top: 6px;
}

.range-tips small {
    font-size: 11px;
    color: #718096;
    font-style: italic;
}

/* 按钮组样式 */
.button-group {
    background: #f8fafc;
    padding: 16px;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    margin-bottom: 20px;
}

/* 导航按钮组样式 */
.nav-button-group {
    background: #f8fafc;
    padding: 16px;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    margin-bottom: 16px;
}

.input-group {
    margin-bottom: 10px;
}

/* ========================================
   页脚属性面板样式
   ======================================== */
.footer-setting-group {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
}

.footer-setting-group .property-label {
    color: #495057;
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
}

.footer-setting-group .input-group {
    margin-bottom: 0;
}

.footer-setting-group .color-row {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.footer-setting-group .color-item {
    flex: 1;
    min-width: 120px;
}

.footer-setting-group .color-item label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 4px;
}

.footer-setting-group .checkbox-group {
    margin-bottom: 10px;
}

.footer-setting-group .checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.footer-setting-group .checkbox-item:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.footer-setting-group .checkbox-item input[type="checkbox"] {
    margin: 0;
}

.footer-setting-group .menu-items-container {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 8px;
    margin: 10px 0;
}

.footer-setting-group .menu-item-row {
    margin-bottom: 8px;
}

.footer-setting-group .menu-item-row:last-child {
    margin-bottom: 0;
}

.footer-setting-group .menu-item-inputs {
    display: flex;
    gap: 6px;
    align-items: center;
}

.footer-setting-group .menu-item-inputs input {
    flex: 1;
}

.footer-setting-group .delete-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.2s ease;
}

.footer-setting-group .delete-btn:hover {
    background: #c82333;
}

.footer-setting-group .add-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    transition: background 0.2s ease;
    width: 100%;
}

.footer-setting-group .add-btn:hover {
    background: #0056b3;
}

/* ========================================
   页面信息面板样式
   ======================================== */
.page-info-group {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
}

.page-info-group .property-label {
    color: #495057;
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
}

.page-info-group .input-group {
    margin-bottom: 0;
}

.page-info-group .color-row {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.page-info-group .color-item {
    flex: 1;
    min-width: 120px;
}

.page-info-group .color-item label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 4px;
}

.page-info-tips {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    padding: 12px;
}

.page-info-tips p {
    margin: 0 0 8px 0;
    font-size: 13px;
    color: #1565c0;
    line-height: 1.4;
}

.page-info-tips p:last-child {
    margin-bottom: 0;
}

/* SEO预览样式 */
.seo-preview {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    font-family: Arial, sans-serif;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.seo-title {
    color: #1a0dab;
    font-size: 18px;
    font-weight: normal;
    margin-bottom: 4px;
    cursor: pointer;
    text-decoration: underline;
    line-height: 1.3;
}

.seo-url {
    color: #006621;
    font-size: 14px;
    margin-bottom: 4px;
}

.seo-description {
    color: #545454;
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 8px;
}

.seo-meta {
    color: #70757a;
    font-size: 12px;
    font-style: italic;
}

/* 复选框行样式 */
.checkbox-row {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

/* 按钮组样式 */
.btn-option {
    padding: 6px 12px;
    border: 1px solid #e2e8f0;
    background: #ffffff;
    color: #4a5568;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    margin-right: 8px;
}

.btn-option:hover {
    border-color: #667eea;
    background: #f7fafc;
}

.btn-option.active {
    background: #667eea;
    color: #ffffff;
    border-color: #667eea;
}

/* ========================================
   文本块组件编辑器样式
   ======================================== */
.textblock-component {
    max-width: 800px;
    margin: 20px auto;
    padding: 40px;
    background: #ffffff;
    border-radius: 8px;
    text-align: left;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}


.textblock-title {
    font-size: 32px;
    color: #2d3748;
    font-weight: bold;
    margin-bottom: 15px;
    line-height: 1.2;
}

.textblock-subtitle {
    font-size: 20px;
    color: #4a5568;
    font-weight: normal;
    margin-bottom: 20px;
    line-height: 1.3;
}

.textblock-content p {
    font-size: 16px;
    color: #4a5568;
    line-height: 1.6;
    margin-bottom: 15px;
}

.textblock-list {
    font-size: 16px;
    color: #4a5568;
    margin: 20px 0;
    padding-left: 20px;
}

.textblock-list li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.textblock-quote {
    font-size: 18px;
    color: #667eea;
    font-style: italic;
    margin: 30px 0;
    padding: 20px;
    border-left: 4px solid #667eea;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 4px;
}

.textblock-quote cite {
    display: block;
    margin-top: 10px;
    font-size: 14px;
    font-style: normal;
    opacity: 0.8;
}

.textblock-buttons {
    margin-top: 30px;
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.textblock-btn {
    padding: 12px 24px;
    border: 2px solid;
    border-radius: 6px;
    text-decoration: none;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
    display: inline-block;
    position: relative;
    overflow: hidden;
}

.textblock-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.textblock-btn.primary {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.textblock-btn.primary:hover {
    background: transparent;
    color: #667eea;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    filter: brightness(1.05);
}

.textblock-btn.primary:hover::before {
    left: 100%;
}

.textblock-btn.secondary {
    background: transparent;
    color: #4a5568;
    border-color: #4a5568;
}

.textblock-btn.secondary:hover {
    background: #4a5568;
    color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    filter: brightness(1.05);
}

.textblock-btn.secondary:hover::before {
    left: 100%;
}

/* ========================================
   背景属性面板样式
   ======================================== */
.bg-setting-group {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
}

.bg-setting-group .property-label {
    color: #495057;
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
}

.bg-setting-group .input-group {
    margin-bottom: 0;
}

.bg-options {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    margin-bottom: 12px;
    max-width: 100%;
}

.bg-option {
    width: 100%;
    height: 45px;
    min-width: 45px;
    border-radius: 6px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s ease;
    position: relative;
}

/* 图片背景选项样式 */
.bg-option[style*="url"] {
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
}

/* 渐变背景选项样式 */
.bg-option[style*="linear-gradient"] {
    background-size: auto !important;
    background-position: initial !important;
    background-repeat: initial !important;
}

.bg-option:hover {
    border-color: #667eea;
    transform: scale(1.05);
}

.bg-option.selected {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.bg-option.selected::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    text-shadow: 0 0 3px rgba(0,0,0,0.5);
}

.bg-preview {
    width: 100%;
    height: 60px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    margin-top: 8px;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    position: relative;
    overflow: hidden;
}

.bg-preview::after {
    content: '预览';
    position: absolute;
    bottom: 4px;
    right: 6px;
    background: rgba(0,0,0,0.6);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
}

.no-background-tip {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 12px;
    text-align: center;
}

.no-background-tip p {
    margin: 0 0 6px 0;
    font-size: 13px;
    color: #856404;
    line-height: 1.4;
}

.no-background-tip p:last-child {
    margin-bottom: 0;
}

/* ========================================
   卡片组件属性面板样式
   ======================================== */

/* 布局按钮组 */
.layout-btn {
    flex: 1;
    padding: 10px 16px;
    border: none;
    background: #f8fafc;
    color: #4a5568;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    margin: 0 3px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.layout-btn:hover {
    background: #e6fffa;
    color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}

.layout-btn.active {
    background: #667eea;
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* 无边框的布局按钮组 */
.layout-buttons-clean {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

.layout-buttons-clean .layout-btn {
    margin: 0;
}

/* 样式预设网格 */
.style-presets {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-top: 8px;
}

.style-preset {
    padding: 12px 8px;
    border-radius: 6px;
    cursor: pointer;
    text-align: center;
    transition: all 0.2s ease;
    border: 2px solid transparent;
    font-size: 12px;
    font-weight: bold;
    position: relative;
}

.style-preset:hover {
    transform: scale(1.02);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.style-preset.selected {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3) !important;
    transform: scale(1.05);
}

.style-preset.selected::after {
    content: '✓';
    position: absolute;
    top: 4px;
    right: 4px;
    background: #667eea;
    color: white;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
}

/* 图片库网格 */
.image-library {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    margin-top: 8px;
}

.image-option {
    width: 100%;
    height: 60px;
    border-radius: 6px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s ease;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.image-option:hover {
    border-color: #667eea;
    transform: scale(1.05);
}

.image-option.selected {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.image-option.selected::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    text-shadow: 0 0 3px rgba(0,0,0,0.5);
    font-size: 16px;
}

/* 图标选择器 */
.icon-selector {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
    margin-top: 8px;
}

.icon-option {
    width: 100%;
    height: 45px;
    border-radius: 6px;
    cursor: pointer;
    border: 2px solid #e2e8f0;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    background: #f8fafc;
}

.icon-option:hover {
    border-color: #667eea;
    background: #e6fffa;
    transform: scale(1.1);
}

.icon-option.selected {
    border-color: #667eea;
    background: #667eea;
    color: white;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* ========================================
   卡片管理器样式
   ======================================== */

/* 卡片管理器容器 */
.cards-manager {
    margin-top: 12px;
}

/* 卡片编辑器 */
.card-editor {
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #f1f5f9;
}

.card-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 14px 18px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    cursor: pointer;
    transition: all 0.2s ease;
}

.card-editor-header:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%);
}

.card-editor-title {
    font-weight: 600;
    color: #2d3748;
    font-size: 14px;
}

.card-editor-actions {
    display: flex;
    gap: 8px;
}

.card-action-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.2s ease;
    background: #e2e8f0;
    color: #4a5568;
}

.card-action-btn:hover {
    background: #cbd5e0;
}

.card-action-btn.delete {
    background: #fed7d7;
    color: #c53030;
}

.card-action-btn.delete:hover {
    background: #feb2b2;
}

.toggle-icon {
    transition: transform 0.2s ease;
}

/* 卡片编辑器内容 */
.card-editor-content {
    padding: 16px;
    background: white;
}

/* 迷你图片库 */
.image-library-mini {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 6px;
    margin-top: 8px;
}

.image-option-mini {
    width: 100%;
    height: 40px;
    border-radius: 4px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s ease;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
}

.image-option-mini:hover {
    border-color: #667eea;
    transform: scale(1.05);
}

.image-option-mini.selected {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.image-option-mini.selected::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    text-shadow: 0 0 3px rgba(0,0,0,0.5);
    font-size: 12px;
}

/* 迷你图标选择器 */
.icon-selector-mini {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 4px;
    margin-top: 8px;
    max-height: 120px;
    overflow-y: auto;
    padding: 4px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background: #fafafa;
}

.icon-option-mini {
    width: 100%;
    height: 28px;
    border-radius: 3px;
    cursor: pointer;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    background: #f8fafc;
    transition: all 0.2s ease;
}

.icon-option-mini:hover {
    border-color: #667eea;
    background: #e6fffa;
    transform: scale(1.05);
}

.icon-option-mini.selected {
    border-color: #667eea;
    background: #667eea;
    color: white;
    box-shadow: 0 0 0 1px rgba(102, 126, 234, 0.3);
}

/* ========================================
   Section组件控制台样式
   ======================================== */

/* Section风格预设样式增强 */
.section-style-presets {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-top: 8px;
}

.section-style-preset {
    padding: 12px 8px;
    border-radius: 8px;
    cursor: pointer;
    text-align: center;
    border: 2px solid transparent;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.section-style-preset:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.section-style-preset.selected {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

/* 添加选中状态的对号 */
.section-style-preset.selected::after {
    content: '✓';
    position: absolute;
    top: 4px;
    right: 4px;
    background: #667eea;
    color: white;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.section-style-preset span {
    font-size: 11px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

/* Section对齐按钮样式 */
.section-align-btn {
    padding: 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    background: #f8f9fa;
    color: #6c757d;
}

.section-align-btn:hover {
    transform: scale(1.05);
    background: #e9ecef;
}

.section-align-btn.active {
    background: #667eea;
    color: white;
}

/* Section背景控制区域 */
.section-bg-controls {
    margin-top: 12px;
}

.section-bg-test-buttons {
    margin-top: 8px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.section-bg-test-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    color: white;
    transition: all 0.2s ease;
}

.section-bg-test-btn:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
}

/* Section颜色选择器增强 */
.section-color-row {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
}

.section-color-item {
    text-align: center;
}

.section-color-item label {
    display: block;
    margin-bottom: 6px;
    font-size: 12px;
    color: #6c757d;
}

.section-color-item input[type="color"] {
    width: 100%;
    height: 40px;
    border-radius: 8px;
    border: 2px solid #dee2e6;
    cursor: pointer;
    transition: all 0.2s ease;
}

.section-color-item input[type="color"]:hover {
    transform: scale(1.05);
}

/* Section范围滑块增强 - 修复版 */
.section-range-control {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 8px;
    background: #f8fafc;
    padding: 10px 12px;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.section-range-control:hover {
    border-color: #cbd5e0;
    background: #f1f5f9;
}

.section-range-slider {
    flex: 1;
    height: 8px;
    border-radius: 4px;
    background: linear-gradient(to right, #e9ecef 0%, #e9ecef 100%);
    outline: none;
    -webkit-appearance: none;
    transition: all 0.2s ease;
    cursor: pointer;
}

.section-range-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: grab;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
    transition: all 0.2s ease;
    border: 2px solid #ffffff;
}

.section-range-slider::-webkit-slider-thumb:hover {
    transform: scale(1.15);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.6);
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.section-range-slider::-webkit-slider-thumb:active {
    cursor: grabbing;
    transform: scale(1.1);
}

.section-range-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: grab;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
    transition: all 0.2s ease;
}

.section-range-slider::-moz-range-thumb:hover {
    transform: scale(1.15);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.6);
}

/* 滑块轨道样式 */
.section-range-slider::-webkit-slider-track {
    height: 8px;
    border-radius: 4px;
    background: linear-gradient(to right, #e9ecef 0%, #e9ecef 100%);
}

.section-range-slider::-moz-range-track {
    height: 8px;
    border-radius: 4px;
    background: linear-gradient(to right, #e9ecef 0%, #e9ecef 100%);
    border: none;
}

.section-range-value {
    min-width: 50px;
    text-align: center;
    font-weight: 600;
    color: #667eea;
    font-size: 14px;
}

/* Section复选框组样式 - 修复版 */
.section-checkbox-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-top: 8px;
}

.section-checkbox-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    border: 1px solid #e2e8f0;
    background: #fafafa;
}

.section-checkbox-item:hover {
    background: #f0f4ff;
    border-color: #667eea;
}

.section-checkbox-item input[type="checkbox"] {
    width: 14px;
    height: 14px;
    accent-color: #667eea;
    cursor: pointer;
    margin: 0;
    flex-shrink: 0;
}

.section-checkbox-item label {
    cursor: pointer;
    font-size: 12px;
    color: #495057;
    line-height: 1.2;
    margin: 0;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Section使用提示框 */
.section-usage-tips {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    padding: 12px;
    margin-top: 16px;
}

.section-usage-tips h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #495057;
}

.section-usage-tips div {
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
}

.section-usage-tips p {
    margin: 0 0 6px 0;
}

.section-usage-tips p:last-child {
    margin: 0;
}

/* ========================================
   文本块组件编辑器样式
   在编辑器中显示的文本块组件样式
   ======================================== */
.textblock-component {
    max-width: 800px;
    margin: 20px auto;
    padding: 40px;
    background: #ffffff;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    border: 2px solid transparent;
}

.textblock-title {
    font-size: 32px;
    color: #2d3748;
    font-weight: bold;
    margin-bottom: 15px;
    line-height: 1.2;
}

.textblock-subtitle {
    font-size: 20px;
    color: #4a5568;
    font-weight: normal;
    margin-bottom: 20px;
    line-height: 1.3;
}

.textblock-content {
    text-align: left;
}

.textblock-content p {
    font-size: 16px;
    color: #4a5568;
    line-height: 1.6;
    margin-bottom: 15px;
}

.textblock-list {
    font-size: 16px;
    color: #4a5568;
    margin: 20px 0;
    padding-left: 20px;
}

.textblock-list li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.textblock-quote {
    font-size: 18px;
    color: #667eea;
    font-style: italic;
    margin: 30px 0;
    padding: 20px;
    border-left: 4px solid #667eea;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 4px;
}

.textblock-buttons {
    margin-top: 30px;
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.textblock-btn {
    padding: 12px 24px;
    border: 2px solid;
    border-radius: 6px;
    text-decoration: none;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
    display: inline-block;
}

.textblock-btn.primary {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.textblock-btn.primary:hover {
    background: transparent;
    color: #667eea;
}

.textblock-btn.secondary {
    background: transparent;
    color: #4a5568;
    border-color: #4a5568;
}

.textblock-btn.secondary:hover {
    background: #4a5568;
    color: white;
}

/* ========================================
   统计数字组件编辑器样式
   在编辑器中显示的统计数字组件样式
   ======================================== */

/* 统计数字容器 */
.stats-container {
    display: grid;
    gap: 24px;
    margin: 20px auto;
    max-width: 1200px;
    padding: 0;
}

/* 单个统计项 */
.stats-item {
    background: white;
    border-radius: 12px;
    padding: 32px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
    border: 1px solid #e2e8f0;
}

.stats-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.stats-item.selected {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 统计图标 */
.stats-icon {
    font-size: 48px;
    margin-bottom: 12px;
    display: block;
    color: #667eea;
}

/* 统计数字 */
.stats-number {
    font-size: 36px;
    font-weight: bold;
    color: #2d3748;
    margin-bottom: 8px;
    display: block;
    line-height: 1.2;
}

/* 统计标签 */
.stats-label {
    font-size: 16px;
    color: #4a5568;
    font-weight: normal;
    display: block;
    line-height: 1.4;
}

/* 统计项编辑器样式 */
.stats-manager {
    margin-bottom: 16px;
}

.stat-editor {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: visible; /* 改为visible让图标选择器能显示 */
    position: relative; /* 添加相对定位 */
}

/* 当图标选择器打开时，为统计项添加额外下边距 */
.stat-editor.icon-picker-open {
    margin-bottom: 280px;
}

.stat-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #ffffff;
    border-bottom: 1px solid #e2e8f0;
    cursor: pointer;
}

.stat-editor-title {
    font-weight: 600;
    color: #2d3748;
    font-size: 14px;
}

.stat-editor-actions {
    display: flex;
    gap: 8px;
}

.stat-editor-content {
    padding: 16px;
    background: #f8fafc;
    position: relative;
    z-index: 0; /* 确保内容区域层级较低 */
}

/* 统计数字图标选择器样式 - 优化版 */
.stats-icon-selector {
    position: relative;
    z-index: 1; /* 确保选择器容器有基础层级 */
}

.stats-current-icon {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 44px;
}

.stats-current-icon:hover {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.stats-icon-display {
    font-size: 24px;
    line-height: 1;
}

.stats-icon-arrow {
    font-size: 14px;
    color: #718096;
    font-weight: bold;
    transition: transform 0.2s ease;
}

.stats-icon-picker {
    position: absolute;
    top: calc(100% + 8px);
    left: -10px;
    width: 220px;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    z-index: 9999;
    max-height: 320px;
    overflow: hidden;
}

/* 图标标签切换 */
.stats-icon-tabs {
    display: flex;
    border-bottom: 1px solid #e2e8f0;
}

.icon-tab {
    flex: 1;
    padding: 8px 12px;
    background: #f8fafc;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    color: #718096;
    transition: all 0.2s ease;
}

.icon-tab:hover {
    background: #e6fffa;
    color: #667eea;
}

.icon-tab.active {
    background: white;
    color: #667eea;
    border-bottom-color: #667eea;
}

.stats-icon-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    padding: 12px;
    max-height: 260px;
    overflow-y: auto;
    overflow-x: hidden;
}

.stats-icon-option {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    margin: 0;
}

.stats-icon-option:hover {
    background: #e6fffa;
    border-color: #667eea;
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.stats-icon-option.selected {
    background: #667eea;
    border-color: #667eea;
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.stats-icon-emoji {
    font-size: 20px;
    line-height: 1;
}

.stats-icon-option.selected .stats-icon-emoji {
    color: white;
}

/* FontAwesome图标样式 */
.stats-icon-option i {
    font-size: 18px;
    line-height: 1;
    color: #4a5568;
}

.stats-icon-option.selected i {
    color: white;
}

/* 实力见证模板图标圆圈样式 */
.stats-icon-circle {
    width: 80px;
    height: 80px;
    background-color: #10d5c2;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    transition: all 0.3s ease;
}

.stats-icon-circle i {
    font-size: 32px;
    color: #ffffff;
}

/* 添加统计项按钮 */
.stats-actions {
    text-align: center;
    margin-top: 16px;
}

.add-stat-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.add-stat-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
}

/* 统计数字列数选择器样式 */
.stats-columns-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    margin-top: 8px;
}

.stats-column-btn {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    background: #f8fafc;
    color: #4a5568;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
    text-align: center;
    min-height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stats-column-btn:hover {
    background: #e6fffa;
    color: #667eea;
    border-color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.stats-column-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.stats-column-btn.active:hover {
    background: #5a67d8;
    border-color: #5a67d8;
}

/* ========================================
   团队介绍组件编辑器样式
   在编辑器中显示的团队介绍组件样式
   ======================================== */

/* 团队容器 */
.team-container {
    display: grid;
    gap: 24px;
    padding: 20px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.team-container:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

/* 团队成员卡片 */
.team-member {
    background: #ffffff;
    border-radius: 12px;
    padding: 24px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.team-member:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

/* 头像容器 */
.member-avatar-container {
    margin-bottom: 16px;
    display: flex;
    justify-content: center;
}

.member-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #e2e8f0;
    transition: all 0.3s ease;
}

.member-avatar:hover {
    border-color: #667eea;
    transform: scale(1.05);
}

/* 成员信息 */
.member-info {
    text-align: center;
}

.member-name {
    font-size: 20px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
    line-height: 1.3;
}

.member-position {
    font-size: 14px;
    font-weight: 500;
    color: #667eea;
    margin-bottom: 12px;
}

.member-bio {
    font-size: 14px;
    color: #4a5568;
    line-height: 1.6;
    margin-bottom: 16px;
    opacity: 0.8;
}

/* 社交链接 */
.member-social {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 16px;
}

.social-link {
    font-size: 18px;
    cursor: pointer;
    opacity: 0.7;
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 50%;
    background: #f7fafc;
}

.social-link:hover {
    opacity: 1;
    transform: translateY(-2px);
    background: #e2e8f0;
}

/* 成员编辑器样式 - 借鉴卡片组件的设计 */
.members-manager {
    margin-bottom: 16px;
}

.member-editor {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: hidden;
    background: #ffffff;
}

.member-editor-header {
    background: #f7fafc;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e2e8f0;
}

.member-editor-title {
    font-weight: 500;
    color: #2d3748;
    font-size: 14px;
}

.member-editor-actions {
    display: flex;
    gap: 8px;
}

.member-action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    transition: all 0.2s ease;
    color: #4a5568;
}

.member-action-btn:hover {
    background: #e2e8f0;
    color: #2d3748;
}

.member-action-btn.delete {
    color: #e53e3e;
}

.member-action-btn.delete:hover {
    background: #fed7d7;
    color: #c53030;
}

.member-editor-content {
    padding: 16px;
}

/* 头像选择器 */
.avatar-library-mini {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    margin-top: 8px;
}

.avatar-option-mini {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    cursor: pointer;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.avatar-option-mini:hover {
    border-color: #667eea;
    transform: scale(1.1);
}

.avatar-option-mini.selected {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* ========================================
   客户评价组件编辑器样式
   在编辑器中显示的客户评价组件样式
   ======================================== */

/* 客户评价容器 */
.testimonials-container {
    display: grid;
    gap: 24px;
    padding: 20px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.testimonials-container:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

/* 客户评价卡片 */
.testimonial-item {
    background: #ffffff;
    border-radius: 12px;
    padding: 24px;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.testimonial-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

/* 评价内容 */
.testimonial-content {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* 星级评分 */
.testimonial-rating {
    margin-top: 8px;
    font-size: 16px;
    display: flex;
    align-items: center;
}

.testimonial-rating .star {
    color: #ffd700;
    margin-right: 1px;
    font-size: inherit;
    line-height: 1;
    display: inline-block;
    transition: all 0.2s ease;
}

.testimonial-rating .star:not(.active) {
    color: #e2e8f0;
}

.testimonial-rating .star:hover {
    transform: scale(1.1);
}

/* 评价文本 */
.testimonial-text {
    font-size: 16px;
    color: #4a5568;
    line-height: 1.6;
    margin-bottom: 20px;
    font-style: italic;
    flex: 1;
}

/* 作者信息 */
.testimonial-author {
    display: flex;
    align-items: center;
    margin-top: auto;
}

.author-avatar-container {
    margin-right: 12px;
}

.author-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #e2e8f0;
    transition: all 0.3s ease;
}

.author-info {
    flex: 1;
}

.author-name {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 4px;
    line-height: 1.3;
}

.author-company {
    font-size: 14px;
    font-weight: 500;
    color: #667eea;
    margin-bottom: 2px;
}

.author-position {
    font-size: 12px;
    color: #718096;
    opacity: 0.8;
}

/* 客户评价编辑器样式 - 借鉴团队组件的设计 */
.testimonials-manager {
    margin-bottom: 16px;
}

.testimonial-editor {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: hidden;
    background: #ffffff;
}

.testimonial-editor-header {
    background: #f7fafc;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e2e8f0;
}

.testimonial-editor-title {
    font-weight: 500;
    color: #2d3748;
    font-size: 14px;
}

.testimonial-editor-actions {
    display: flex;
    gap: 8px;
}

.testimonial-action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    transition: all 0.2s ease;
    color: #4a5568;
}

.testimonial-action-btn:hover {
    background: #e2e8f0;
    color: #2d3748;
}

/* ========================================
   联系信息组件编辑器样式
   在编辑器中显示的联系信息组件样式
   ======================================== */

/* 联系信息容器 */
.contact-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 20px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.contact-container:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

/* 布局模式 */
.contact-container.layout-horizontal {
    flex-direction: row;
    flex-wrap: wrap;
}

.contact-container.layout-grid {
    display: grid;
}

/* 联系信息项 */
.contact-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    cursor: default;
}

.contact-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

/* 联系信息图标 */
.contact-icon {
    font-size: 24px;
    margin-right: 12px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 联系信息内容 */
.contact-content {
    flex: 1;
}

.contact-label {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
    opacity: 0.8;
}

.contact-value {
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
}



/* 联系信息编辑器样式 - 借鉴团队组件的设计 */
.contacts-manager {
    margin-bottom: 16px;
    overflow: visible;
    position: relative;
}

.contact-editor {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: visible;
    background: #ffffff;
    position: relative;
    z-index: 1;
}

.contact-editor-header {
    background: #f7fafc;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e2e8f0;
}

.contact-editor-title {
    font-weight: 500;
    color: #2d3748;
    font-size: 14px;
}

.contact-editor-actions {
    display: flex;
    gap: 8px;
}

.contact-action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    transition: all 0.2s ease;
    color: #4a5568;
}

.contact-action-btn:hover {
    background: #e2e8f0;
    color: #2d3748;
}

.contact-action-btn.delete {
    color: #e53e3e;
}

.contact-action-btn.delete:hover {
    background: #fed7d7;
    color: #c53030;
}

.contact-editor-content {
    padding: 16px;
    overflow: visible;
    position: relative;
}

/* 联系信息图标选择器样式 - 完全借鉴统计组件的设计 */
.contact-icon-editor {
    position: relative;
    z-index: 10;
    overflow: visible;
}

.contact-icon-selector-wrapper {
    position: relative;
    z-index: 10;
}

.contact-icon-selector-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 44px;
    width: 100%;
}

.contact-icon-selector-btn:hover {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.contact-selected-icon {
    font-size: 24px;
    line-height: 1;
}

.contact-selector-arrow {
    font-size: 14px;
    color: #718096;
    font-weight: bold;
    transition: transform 0.2s ease;
}

.contact-icon-picker {
    position: absolute;
    top: calc(100% + 8px);
    left: -10px;
    width: 220px;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    z-index: 99999;
    max-height: 280px;
    overflow: hidden;
}

.contact-icon-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    padding: 12px;
    max-height: 260px;
    overflow-y: auto;
    overflow-x: hidden;
}

.contact-icon-option {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    margin: 0;
}

.contact-icon-option:hover {
    background: #e6fffa;
    border-color: #667eea;
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.contact-icon-option.selected {
    background: #667eea;
    border-color: #667eea;
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.contact-icon-emoji {
    font-size: 20px;
    line-height: 1;
}

.contact-icon-option.selected .contact-icon-emoji {
    color: white;
}

/* 添加联系方式按钮 */
.contact-actions {
    text-align: center;
    margin-top: 16px;
}

.add-contact-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.add-contact-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
}

/* 联系信息列数选择器样式 */
.contact-columns-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    margin-top: 8px;
}

.contact-column-btn {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    background: #f8fafc;
    color: #4a5568;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
    text-align: center;
    min-height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-column-btn:hover {
    background: #e6fffa;
    color: #667eea;
    border-color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.contact-column-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.contact-column-btn.active:hover {
    background: #5a67d8;
    border-color: #5a67d8;
}

/* 联系信息背景图选择器样式 */
.contact-bg-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    margin-top: 8px;
}

.contact-bg-option {
    width: 100%;
    height: 60px;
    border-radius: 6px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s ease;
    position: relative;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.contact-bg-option:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.contact-bg-option.selected {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
}

.contact-bg-option.selected::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #667eea;
    font-size: 18px;
    font-weight: bold;
    background: rgba(255, 255, 255, 0.9);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.testimonial-action-btn.delete {
    color: #e53e3e;
}

.testimonial-action-btn.delete:hover {
    background: #fed7d7;
    color: #c53030;
}

.testimonial-editor-content {
    padding: 16px;
}

/* 评分选择器 */
.rating-selector {
    display: flex;
    gap: 4px;
    margin-top: 8px;
}

.rating-star {
    font-size: 20px;
    color: #e2e8f0;
    cursor: pointer;
    transition: all 0.2s ease;
}

.rating-star:hover,
.rating-star.active {
    color: #ffd700;
    transform: scale(1.1);
}

/* 团队组件样式已简化，移除弹窗相关代码 */



/* 社交信息样式 - 简洁舒适设计 */
.member-social {
    margin-top: 18px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
}

.social-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 5px 10px;
    border-radius: 16px;
    background: #f8fafc;
    /* border: 1px solid #e2e8f0; */
    transition: all 0.2s ease;
    font-size: 13px;
    color: #4a5568;
}

.social-item:hover {
    background: #f1f5f9;
    border-color: #cbd5e0;
    transform: translateY(-0.5px);
}

.social-item .social-icon {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    flex-shrink: 0;
    color: white;
    font-weight: 500;
}

.social-item .social-text {
    font-weight: 500;
    line-height: 1.4;
    white-space: nowrap;
}

.wechat-item .social-icon {
    background: #10b981;
}

.wechat-item:hover .social-icon {
    background: #059669;
}

.email-item .social-icon {
    background: #3b82f6;
}

.email-item:hover .social-icon {
    background: #2563eb;
}

.email-item .social-text {
    font-size: 11px;
}

/* ========================================
   样式模板组件编辑器样式
   统一管理页面主题风格的高级组件
   ======================================== */

/* 样式模板组件基础样式 */
.styletemplates-component {
    background: #f8fafc;
    padding: 40px;
    border-radius: 16px;
    max-width: 1200px;
    margin: 20px auto;
    position: relative;
}

.styletemplates-component:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.styletemplates-component.selected {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* 模板头部样式 */
.templates-header {
    text-align: center;
    margin-bottom: 40px;
}

.templates-icon {
    margin-bottom: 16px;
}

.templates-icon i {
    font-size: 48px;
    display: inline-block;
}

.templates-title {
    color: #2d3748;
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 12px;
    line-height: 1.2;
}

.templates-subtitle {
    color: #4a5568;
    font-size: 18px;
    margin-bottom: 0;
    line-height: 1.5;
}

/* 模板网格布局 */
.templates-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    margin-bottom: 40px;
}

/* 模板卡片样式 */
.template-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.template-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.template-card.selected {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

/* 模板预览样式 */
.template-preview {
    width: 100%;
    height: 80px;
    border-radius: 8px;
    margin-bottom: 16px;
    background-size: cover;
    background-position: center;
    position: relative;
    overflow: hidden;
}

.template-preview::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    border-radius: 8px;
}

/* 模板信息样式 - 已移至首页模板区域统一管理 */

.template-desc {
    font-size: 13px;
    color: #718096;
    margin: 0;
    line-height: 1.4;
}

/* 模板应用按钮 */
.template-apply-btn {
    width: 100%;
    padding: 10px 16px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.template-apply-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.template-apply-btn:active {
    transform: translateY(0);
}

.template-apply-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.template-apply-btn:hover::before {
    left: 100%;
}

/* 模板底部提示 */
.templates-footer {
    border-top: 1px solid #e2e8f0;
    padding-top: 32px;
}

.templates-tips h4 {
    color: #2d3748;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
}

.templates-tips ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.templates-tips li {
    color: #4a5568;
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 6px;
    padding-left: 0;
}

.templates-tips li:last-child {
    margin-bottom: 0;
}

/* 当前主题显示 */
.current-theme-display {
    padding: 12px;
    background: #f7fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.current-theme-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.current-theme-preview {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    flex-shrink: 0;
    border: 2px solid #e2e8f0;
}

.current-theme-name {
    font-weight: 500;
    color: #2d3748;
    font-size: 14px;
}

.no-theme-selected {
    text-align: center;
    color: #718096;
    font-size: 14px;
    font-style: italic;
}

/* 主题操作按钮 */
.theme-action-btn {
    width: 100%;
    padding: 10px 16px;
    border: 1px solid #667eea;
    background: #667eea;
    color: white;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 8px;
}

.theme-action-btn:hover {
    background: #5a67d8;
    border-color: #5a67d8;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.theme-action-btn.secondary {
    background: transparent;
    color: #667eea;
}

.theme-action-btn.secondary:hover {
    background: #f7fafc;
    color: #5a67d8;
}

.theme-action-btn.disabled {
    background: #e2e8f0;
    color: #a0aec0;
    border-color: #e2e8f0;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.theme-action-btn.disabled:hover {
    background: #e2e8f0;
    color: #a0aec0;
    border-color: #e2e8f0;
    transform: none;
    box-shadow: none;
}

/* 主题应用成功提示动画 */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.theme-apply-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #10b981;
    color: white;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    z-index: 10000;
    max-width: 300px;
}

.toast-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.toast-icon {
    font-size: 20px;
    flex-shrink: 0;
    margin-top: 2px;
}

.toast-text {
    flex: 1;
    line-height: 1.4;
}

.toast-text strong {
    display: block;
    margin-bottom: 4px;
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .templates-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 16px;
    }
    
    .template-card {
        padding: 16px;
    }
    
    .template-preview {
        height: 60px;
    }
    
    .templates-title {
        font-size: 28px;
    }
    
    .templates-subtitle {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .templates-grid {
        grid-template-columns: 1fr !important;
    }
    
    .styletemplates-component {
        padding: 20px;
        margin: 10px;
    }
    
    .templates-title {
        font-size: 24px;
    }
    
    .templates-subtitle {
        font-size: 15px;
    }
}

/* ========================================
   模板选择器样式
   ======================================== */
.template-selector {
    padding: 16px;
    background: #fff;
    border-radius: 12px;
}

.template-selector-header {
    text-align: center;
    margin-bottom: 24px;
}

.template-selector-header h3 {
    margin: 0;
    color: #1a202c;
    font-size: 20px;
    font-weight: 700;
    letter-spacing: -0.025em;
}

.template-grid {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 20px;
    max-width: 320px;
    margin-left: auto;
    margin-right: auto;
}

.template-card {
    border: 1px solid #e2e8f0;
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: #fff;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.template-card:hover,
.template-card.hover {
    border-color: #667eea;
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
}

.template-preview {
    position: relative;
    height: 160px;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.template-preview-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
}

.template-preview-icon svg {
    opacity: 0.8;
}

.template-preview-text {
    font-size: 14px;
    font-weight: 500;
    opacity: 0.9;
}

.template-card:hover .template-preview-icon {
    transform: scale(1.05);
    color: white;
}

.template-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.template-card:hover .template-preview img {
    transform: scale(1.05);
}

.template-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(4px);
}

.template-card:hover .template-overlay {
    opacity: 1;
}

.template-card:hover .template-preview-icon {
    opacity: 0.3;
    transform: scale(0.95);
}

.template-overlay .btn-preview,
.template-overlay .btn-apply {
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 5px;
    min-width: 70px;
    justify-content: center;
}

.template-overlay .btn-preview {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.template-overlay .btn-preview:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    border-color: rgba(255, 255, 255, 0.7);
}

.template-overlay .btn-apply {
    background: #667eea;
    color: white;
    border: 1px solid #667eea;
}

.template-overlay .btn-apply:hover {
    background: #5a67d8;
    border-color: #5a67d8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 重复的模板信息样式已删除 - 统一使用首页模板区域的样式 */

.template-meta {
    display: flex;
    justify-content: center;
    gap: 6px;
    font-size: 11px;
}

.template-badge {
    display: inline-flex;
    align-items: center;
    padding: 3px 8px;
    background: #f7fafc;
    color: #4a5568;
    border-radius: 10px;
    font-weight: 500;
    border: 1px solid #e2e8f0;
}

.template-actions {
    text-align: center;
    padding-top: 16px;
    margin-top: 16px;
    border-top: 1px solid #f1f5f9;
}

/* 模板卡片内的操作按钮 */
.template-card .template-actions {
    display: flex;
    gap: 8px;
    margin-top: 16px;
    justify-content: center;
    border-top: 1px solid #f1f5f9;
    padding-top: 16px;
}

.template-card .template-actions .btn-preview,
.template-card .template-actions .btn-apply {
    flex: 1;
    padding: 10px 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: #fff;
    color: #4a5568;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    text-decoration: none;
}

.template-card .template-actions .btn-preview:hover {
    border-color: #667eea;
    color: #667eea;
    background: #f7fafc;
    transform: translateY(-1px);
}

.template-card .template-actions .btn-apply {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.template-card .template-actions .btn-apply:hover {
    background: #5a67d8;
    border-color: #5a67d8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-custom-template,
.btn-back-to-templates {
    padding: 10px 20px;
    background: #f8fafc;
    border: 1px dashed #cbd5e0;
    border-radius: 12px;
    color: #64748b;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn-custom-template:hover,
.btn-back-to-templates:hover {
    border-color: #667eea;
    color: #667eea;
    background: #f1f5f9;
    transform: translateY(-1px);
}

/* 页面管理器样式 */
.page-manager {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
}

.page-manager-header {
    padding: 20px;
    background: #f7fafc;
    border-bottom: 1px solid #e2e8f0;
}

.page-manager-header h4 {
    margin: 0;
    color: #2d3748;
    font-size: 18px;
    font-weight: 600;
}

.page-list {
    padding: 10px;
}

.page-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 4px;
}

.page-item:hover {
    background: #f7fafc;
}

.page-item.active {
    background: #667eea;
    color: white;
}

.page-item i {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.page-item span {
    font-size: 14px;
    font-weight: 500;
}

.page-actions {
    padding: 15px 20px;
    border-top: 1px solid #e2e8f0;
    background: #f7fafc;
}

.btn-add-page {
    width: 100%;
    padding: 10px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-add-page:hover {
    background: #5a67d8;
}

/* 模板预览弹窗样式 */
.template-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: 20px;
}

.preview-modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 1200px;
    height: 80%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.preview-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid #e2e8f0;
}

.preview-modal-header h3 {
    margin: 0;
    color: #2d3748;
    font-size: 20px;
    font-weight: 600;
}

.btn-close {
    background: none;
    border: none;
    font-size: 20px;
    color: #718096;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: color 0.3s ease;
}

.btn-close:hover {
    color: #2d3748;
}

.preview-modal-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.preview-pages {
    display: flex;
    border-bottom: 1px solid #e2e8f0;
    background: #f7fafc;
    overflow-x: auto;
}

.preview-page-tab {
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #4a5568;
}

.preview-page-tab:hover {
    background: #edf2f7;
}

.preview-page-tab.active {
    background: white;
    border-bottom-color: #667eea;
    color: #667eea;
}

.preview-content {
    flex: 1;
    overflow: hidden;
}

.preview-content iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.preview-modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #e2e8f0;
    background: #f7fafc;
}

.btn-cancel,
.btn-apply-template {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-cancel {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-cancel:hover {
    background: #cbd5e0;
}

.btn-apply-template {
    background: #667eea;
    color: white;
}

.btn-apply-template:hover {
    background: #5a67d8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .template-grid {
        grid-template-columns: 1fr;
    }

    .preview-modal-content {
        width: 95%;
        height: 90%;
    }

    .preview-pages {
        flex-wrap: wrap;
    }

    .preview-page-tab {
        flex: 1;
        min-width: 120px;
        justify-content: center;
    }
}

/* ========================================
   属性面板按钮样式
   ======================================== */
.property-btn {
    width: 100%;
    padding: 10px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.property-btn:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
    transform: translateY(-1px);
}

.property-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.property-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 数据源配置样式 */
.data-source-config {
    margin-top: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.data-source-config h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
}

.data-source-config .property-group {
    margin-bottom: 12px;
}

.data-source-config .property-group:last-child {
    margin-bottom: 0;
}

/* ========================================
   导航栏组件紧凑布局样式
   ======================================== */

/* 紧凑网格布局 */
.compact-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    margin-top: 8px;
}

.compact-row {
    display: grid;
    grid-template-columns: 1fr 80px;
    gap: 8px;
    margin-top: 8px;
}

.compact-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.compact-item label {
    font-size: 11px;
    color: #718096;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.compact-color-input {
    width: 100%;
    height: 28px;
    padding: 2px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    cursor: pointer;
}

.compact-number-input {
    width: 100%;
    height: 28px;
    padding: 4px 6px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 12px;
}

/* 菜单项紧凑容器 */
.nav-menu-compact {
    background: transparent;
    border: none;
    border-radius: 0;
    padding: 0;
    margin-bottom: 8px;
}

.menu-items-container {
    max-height: 720px;
    overflow-y: auto;
    margin-bottom: 12px;
}

.menu-item-card {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.menu-item-card:hover {
    border-color: #667eea;
    box-shadow: 0 1px 3px rgba(102, 126, 234, 0.1);
}

.menu-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.menu-item-main {
    display: flex;
    gap: 8px;
    flex: 1;
    align-items: center;
}

.menu-name-input {
    width: 90px;
    flex: none;
    padding: 6px 8px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 12px;
}

.menu-type-select {
    width: 65px;
    flex: none;
    padding: 6px 8px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 12px;
    background: #ffffff;
}

.menu-item-actions {
    display: flex;
    gap: 6px;
    align-items: center;
    flex: none;
}

.toggle-children-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    line-height: 1;
    min-width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toggle-children-btn:hover {
    background: #5a67d8;
}

.delete-btn-mini {
    background: #e53e3e;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    line-height: 1;
    font-weight: bold;
    min-width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.delete-btn-mini:hover {
    background: #c53030;
}

.menu-item-link {
    margin-bottom: 6px;
}

.link-input-group {
    display: flex;
    gap: 6px;
    align-items: center;
}

.link-input {
    flex: 1;
    padding: 6px 8px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 12px;
}

.submenu-link-input {
    flex: 1;
    padding: 5px 8px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 12px;
}

.link-select-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    white-space: nowrap;
    flex: none;
}

.link-select-btn:hover {
    background: #5a67d8;
}

.link-select-btn-small {
    background: #667eea;
    color: white;
    border: none;
    padding: 5px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 10px;
    white-space: nowrap;
    flex: none;
}

.link-select-btn-small:hover {
    background: #5a67d8;
}

/* 子菜单容器 */
.submenu-container {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 8px;
    margin-top: 6px;
}

.submenu-header {
    font-size: 12px;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 8px;
    padding-bottom: 4px;
    border-bottom: 1px solid #e2e8f0;
}

.submenu-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 8px;
    position: relative;
}

.submenu-item-row {
    display: flex;
    gap: 6px;
    align-items: center;
}

.submenu-item-row:last-child {
    margin-top: 2px;
}

.submenu-input {
    flex: 1;
    padding: 5px 8px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 12px;
}

.submenu-empty {
    text-align: center;
    color: #a0aec0;
    font-size: 12px;
    padding: 16px;
    font-style: italic;
    background: #f7fafc;
    border: 1px dashed #e2e8f0;
    border-radius: 4px;
    margin-bottom: 8px;
}

.add-submenu-btn {
    background: #38a169;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    width: 100%;
    margin-top: 6px;
}

.add-submenu-btn:hover {
    background: #2f855a;
}

/* 颜色设置紧凑样式 */
.nav-colors-compact {
    background: #f8fafc;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
}

/* 导航栏下拉菜单样式 */
.navbar-dropdown {
    position: relative;
    display: inline-block;
    z-index: 9999;  /* 确保下拉菜单容器在背景图片之上 */
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 4px;
}

.dropdown-arrow {
    font-size: 10px;
    transition: transform 0.2s ease;
}

.navbar-dropdown:hover .dropdown-arrow {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    min-width: 160px;
    z-index: 9999;  /* 提高z-index，确保在背景图片之上 */
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.navbar-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: 8px 16px;
    color: #2d3748;
    text-decoration: none;
    font-size: 14px;
    border-bottom: 1px solid #f7fafc;
    transition: all 0.2s ease;
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background: #f7fafc;
    color: #667eea;
    padding-left: 20px;
}

/* ========================================
   链接选择器弹窗样式
   ======================================== */

.link-selector-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.link-selector-modal {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.link-selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;
}

.link-selector-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: #a0aec0;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
}

.close-btn:hover {
    background: #e2e8f0;
    color: #4a5568;
}

.link-selector-body {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.link-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.link-option {
    padding: 12px 16px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #ffffff;
}

.link-option:hover {
    border-color: #667eea;
    background: #f7fafc;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.link-name {
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 4px;
}

.link-url {
    font-size: 12px;
    color: #718096;
    font-family: 'Courier New', monospace;
}

.link-selector-footer {
    padding: 16px 20px;
    border-top: 1px solid #e2e8f0;
    background: #f8fafc;
    text-align: right;
}

.cancel-btn {
    background: #e2e8f0;
    color: #4a5568;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.cancel-btn:hover {
    background: #cbd5e0;
}
{"version": 3, "file": "eu.js", "sources": ["../../../../../packages/locale/lang/eu.ts"], "sourcesContent": ["export default {\n  name: 'eu',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: '<PERSON><PERSON>',\n      clear: 'Garbitu',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON>',\n      today: '<PERSON><PERSON>r',\n      cancel: 'Utzi',\n      clear: 'Garbitu',\n      confirm: '<PERSON><PERSON>',\n      selectDate: 'Hautatu data',\n      selectTime: 'Hautatu ordua',\n      startDate: 'Hasierako data',\n      startTime: 'Hasierako ordua',\n      endDate: 'Amaierako data',\n      endTime: 'Amaierako ordua',\n      prevYear: 'Aurreko urtea',\n      nextYear: 'Hurrengo urtea',\n      prevMonth: 'Aurreko hilabetea',\n      nextMonth: 'Hurrengo hilabetea',\n      year: '',\n      month1: 'Urtarrila',\n      month2: 'Otsaila',\n      month3: 'Martxoa',\n      month4: 'Apirila',\n      month5: 'Maiatza',\n      month6: 'Ekaina',\n      month7: 'Uztaila',\n      month8: 'Abuztua',\n      month9: '<PERSON><PERSON>',\n      month10: 'Urria',\n      month11: 'Azaroa',\n      month12: 'Abendua',\n      // week: 'astea',\n      weeks: {\n        sun: 'ig.',\n        mon: 'al.',\n        tue: 'ar.',\n        wed: 'az.',\n        thu: 'og.',\n        fri: 'ol.',\n        sat: 'lr.',\n      },\n      months: {\n        jan: 'urt',\n        feb: 'ots',\n        mar: 'mar',\n        apr: 'api',\n        may: 'mai',\n        jun: 'eka',\n        jul: 'uzt',\n        aug: 'abu',\n        sep: 'ira',\n        oct: 'urr',\n        nov: 'aza',\n        dec: 'abe',\n      },\n    },\n    select: {\n      loading: 'Kargatzen',\n      noMatch: 'Bat datorren daturik ez',\n      noData: 'Daturik ez',\n      placeholder: 'Hautatu',\n    },\n    mention: {\n      loading: 'Kargatzen',\n    },\n    cascader: {\n      noMatch: 'Bat datorren daturik ez',\n      loading: 'Kargatzen',\n      placeholder: 'Hautatu',\n      noData: 'Daturik ez',\n    },\n    pagination: {\n      goto: 'Joan',\n      pagesize: '/orria',\n      total: 'Guztira {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Mezua',\n      confirm: 'Ados',\n      cancel: 'Utzi',\n      error: 'Sarrera baliogabea',\n    },\n    upload: {\n      deleteTip: 'sakatu Ezabatu kentzeko',\n      delete: 'Ezabatu',\n      preview: 'Aurrebista',\n      continue: 'Jarraitu',\n    },\n    table: {\n      emptyText: 'Daturik ez',\n      confirmFilter: 'Baieztatu',\n      resetFilter: 'Berrezarri',\n      clearFilter: 'Guztia',\n      sumText: 'Batura',\n    },\n    tour: {\n      next: 'Hurrengoa',\n      previous: 'Aurrekoa',\n      finish: 'Bukatu',\n    },\n    tree: {\n      emptyText: 'Daturik ez',\n    },\n    transfer: {\n      noMatch: 'Bat datorren daturik ez',\n      noData: 'Daturik ez',\n      titles: ['Zerrenda 1', 'Zerrenda 2'], // to be translated\n      filterPlaceholder: 'Sartu gako-hitza', // to be translated\n      noCheckedFormat: '{total} elementu', // to be translated\n      hasCheckedFormat: '{checked}/{total} hautatuta', // to be translated\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,KAAK,EAAE,SAAS;AACtB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,OAAO;AAClB,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,UAAU,EAAE,cAAc;AAChC,MAAM,UAAU,EAAE,eAAe;AACjC,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,SAAS,EAAE,iBAAiB;AAClC,MAAM,OAAO,EAAE,gBAAgB;AAC/B,MAAM,OAAO,EAAE,iBAAiB;AAChC,MAAM,QAAQ,EAAE,eAAe;AAC/B,MAAM,QAAQ,EAAE,gBAAgB;AAChC,MAAM,SAAS,EAAE,mBAAmB;AACpC,MAAM,SAAS,EAAE,oBAAoB;AACrC,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,OAAO,EAAE,OAAO;AACtB,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,OAAO,EAAE,yBAAyB;AACxC,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,WAAW,EAAE,SAAS;AAC5B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,WAAW;AAC1B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,yBAAyB;AACxC,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,WAAW,EAAE,SAAS;AAC5B,MAAM,MAAM,EAAE,YAAY;AAC1B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,QAAQ,EAAE,QAAQ;AACxB,MAAM,KAAK,EAAE,iBAAiB;AAC9B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,KAAK,EAAE,oBAAoB;AACjC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,yBAAyB;AAC1C,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,QAAQ,EAAE,UAAU;AAC1B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,aAAa,EAAE,WAAW;AAChC,MAAM,WAAW,EAAE,YAAY;AAC/B,MAAM,WAAW,EAAE,QAAQ;AAC3B,MAAM,OAAO,EAAE,QAAQ;AACvB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,MAAM,EAAE,QAAQ;AACtB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,YAAY;AAC7B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,yBAAyB;AACxC,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AAC1C,MAAM,iBAAiB,EAAE,kBAAkB;AAC3C,MAAM,eAAe,EAAE,kBAAkB;AACzC,MAAM,gBAAgB,EAAE,6BAA6B;AACrD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}
<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * 基础验证器 - ThinkPHP6企业级应用
 * 功能：提供通用验证规则和安全过滤方法
 */

namespace app\validate;

use think\Validate;

class BaseValidate extends Validate
{
    /**
     * 通用验证规则
     */
    protected $rule = [];
    
    /**
     * 验证消息
     */
    protected $message = [];
    
    /**
     * 验证场景
     */
    protected $scene = [];
    
    /**
     * 安全过滤用户输入
     * @param array $data 输入数据
     * @return array 过滤后的数据
     */
    public function filterInput($data)
    {
        if (!is_array($data)) {
            return $data;
        }
        
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                // XSS防护：过滤HTML标签和特殊字符
                $data[$key] = $this->xssFilter($value);
            } elseif (is_array($value)) {
                $data[$key] = $this->filterInput($value);
            }
        }
        
        return $data;
    }
    
    /**
     * XSS过滤
     * @param string $input 输入字符串
     * @return string 过滤后的字符串
     */
    protected function xssFilter($input)
    {
        // 移除危险的HTML标签
        $dangerousTags = [
            'script', 'iframe', 'object', 'embed', 'form', 'input', 
            'textarea', 'select', 'button', 'link', 'meta', 'style'
        ];
        
        foreach ($dangerousTags as $tag) {
            $input = preg_replace('/<' . $tag . '[^>]*>.*?<\/' . $tag . '>/is', '', $input);
            $input = preg_replace('/<' . $tag . '[^>]*\/?>/is', '', $input);
        }
        
        // 移除javascript:和data:协议
        $input = preg_replace('/javascript:/i', '', $input);
        $input = preg_replace('/data:/i', '', $input);
        $input = preg_replace('/vbscript:/i', '', $input);
        
        // 移除事件处理器
        $input = preg_replace('/on\w+\s*=/i', '', $input);
        
        return trim($input);
    }
    
    /**
     * SQL注入防护检查
     * @param string $input 输入字符串
     * @return bool 是否安全
     */
    protected function checkSqlInjection($input)
    {
        $sqlKeywords = [
            'union', 'select', 'insert', 'update', 'delete', 'drop', 
            'create', 'alter', 'exec', 'execute', 'script', '--', '/*'
        ];
        
        $input = strtolower($input);
        foreach ($sqlKeywords as $keyword) {
            if (strpos($input, $keyword) !== false) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 文件名安全检查
     * @param string $filename 文件名
     * @return bool 是否安全
     */
    protected function checkFilename($filename)
    {
        // 检查文件名是否包含危险字符
        $dangerousChars = ['..', '/', '\\', ':', '*', '?', '"', '<', '>', '|'];
        foreach ($dangerousChars as $char) {
            if (strpos($filename, $char) !== false) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 验证文件扩展名
     * @param string $filename 文件名
     * @param array $allowedExts 允许的扩展名
     * @return bool 是否允许
     */
    protected function checkFileExtension($filename, $allowedExts = ['jpg', 'jpeg', 'png', 'gif', 'webp'])
    {
        $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        return in_array($ext, $allowedExts);
    }
    
    /**
     * 验证图片MIME类型
     * @param string $mimeType MIME类型
     * @return bool 是否为有效图片类型
     */
    protected function checkImageMimeType($mimeType)
    {
        $allowedMimes = [
            'image/jpeg', 'image/jpg', 'image/png', 
            'image/gif', 'image/webp'
        ];
        
        return in_array($mimeType, $allowedMimes);
    }
}

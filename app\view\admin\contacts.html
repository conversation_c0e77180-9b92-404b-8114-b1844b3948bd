<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系表单管理 - 后台管理系统</title>

     <!-- CSS -->
     {include file="admin/common/css"}
     <link rel="stylesheet" href="/assets/css/admin/contacts.css">  
 </head>
 <body>
    <!-- 顶部导航 -->
    {include file="admin/common/header"}

    <div class="admin-layout">
        <div class="admin-container">
            {include file="admin/common/sidebar"}

            <div class="main-content">
                <!-- 引用统一消息组件 -->
                {include file="admin/common/message"}

                <div class="content-body">
                    {if condition="$action == 'list' || empty($action)"}
                    <!-- 联系表单列表容器 -->
                    <div class="contacts-container">
                        <!-- 列表头部 -->
                        <div class="list-header">
                            <div class="list-header-content">
                                <div class="list-title-section">
                                    <div class="list-icon">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <div class="list-title-text">
                                        <h4 class="list-title">联系表单列表</h4>
                                        <p class="list-subtitle">
                                            管理客户联系表单和咨询信息 (共 {$totalItems|default=0} 条)
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 列表主体 -->
                        <div class="list-body" style="padding: 30px;">
                            <!-- 状态统计 -->
                            <div class="status-stats">
                                <div class="stat-item">
                                    <div class="stat-number">{$statusStats.new|default=0}</div>
                                    <div class="stat-label">新消息</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">{$statusStats.read|default=0}</div>
                                    <div class="stat-label">已读</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">{$statusStats.replied|default=0}</div>
                                    <div class="stat-label">已回复</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">{$statusStats.closed|default=0}</div>
                                    <div class="stat-label">已关闭</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">{$totalItems|default=0}</div>
                                    <div class="stat-label">总计</div>
                                </div>
                            </div>

                            <!-- 搜索和筛选 -->
                            <div class="search-filters">
                                <form method="GET" action="">
                                    <div class="filter-row">
                                        <div class="filter-group">
                                            <label for="search">搜索关键词</label>
                                            <input type="text" id="search" name="search"
                                                   value="{$searchKeyword|default=''}"
                                                   placeholder="搜索姓名、邮箱、主题或内容...">
                                        </div>
                                        <div class="filter-group">
                                            <label for="status">状态筛选</label>
                                            <select id="status" name="status">
                                                <option value="">全部状态</option>
                                                <option value="new" {if condition="$statusFilter == 'new'"}selected{/if}>新消息</option>
                                                <option value="read" {if condition="$statusFilter == 'read'"}selected{/if}>已读</option>
                                                <option value="replied" {if condition="$statusFilter == 'replied'"}selected{/if}>已回复</option>
                                                <option value="closed" {if condition="$statusFilter == 'closed'"}selected{/if}>已关闭</option>
                                            </select>
                                        </div>
                                        <div class="filter-actions">
                                            <button type="submit" class="btn-filter">
                                                <i class="fas fa-search"></i>
                                                搜索
                                            </button>
                                            <button type="button" class="btn-filter btn-clear" onclick="window.location.href='/admin/contacts'">
                                                <i class="fas fa-times"></i>
                                                清除
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            {if condition="empty($contacts)"}
                                <div class="empty-state">
                                    <div class="empty-icon">
                                        <i class="fas fa-inbox"></i>
                                    </div>
                                    <h3 class="empty-title">暂无联系表单</h3>
                                    <p class="empty-subtitle">
                                        {if condition="!empty($searchKeyword) || !empty($statusFilter)"}
                                            没有找到符合条件的联系表单，请尝试调整搜索条件
                                        {else /}
                                            还没有收到任何联系表单
                                        {/if}
                                    </p>
                                </div>
                            {else /}
                                <!-- 联系表单列表 -->
                                <div class="contacts-list">
                                    {volist name="contacts" id="contact"}
                                        <div class="contact-card">
                                            <div class="contact-header">
                                                <div class="contact-info">
                                                    <h5 class="contact-name">{$contact.name}</h5>
                                                    <p class="contact-email">
                                                        <i class="fas fa-envelope"></i>
                                                        {$contact.email}
                                                    </p>
                                                    {if condition="!empty($contact.phone)"}
                                                        <p class="contact-phone">
                                                            <i class="fas fa-phone"></i>
                                                            {$contact.phone}
                                                        </p>
                                                    {/if}
                                                </div>
                                                <div class="contact-status">
                                                    <span class="status-badge status-{$contact.status}">
                                                        {switch name="contact.status"}
                                                            {case value="new"}新消息{/case}
                                                            {case value="read"}已读{/case}
                                                            {case value="replied"}已回复{/case}
                                                            {case value="closed"}已关闭{/case}
                                                            {default /}未知
                                                        {/switch}
                                                    </span>
                                                </div>
                                            </div>

                                            <h6 class="contact-subject">{$contact.subject}</h6>

                                            <div class="contact-message">
                                                {$contact.message|mb_substr=0,150,'utf-8'|nl2br}
                                                {if condition="mb_strlen($contact.message, 'utf-8') > 150"}...{/if}
                                            </div>

                                            <div class="contact-meta">
                                                <div class="contact-date">
                                                    <i class="fas fa-clock"></i>
                                                    {$contact.created_at|date='Y-m-d H:i'}
                                                </div>
                                                {if condition="!empty($contact.company)"}
                                                    <div class="contact-company">
                                                        <i class="fas fa-building"></i>
                                                        {$contact.company}
                                                    </div>
                                                {/if}
                                            </div>

                                            <div class="contact-actions">
                                                <a href="/admin/contacts?action=view&id={$contact.id}" class="btn-action btn-view">
                                                    <i class="fas fa-eye"></i>
                                                    查看详情
                                                </a>
                                                <a href="/admin/contacts?action=edit&id={$contact.id}" class="btn-action btn-edit">
                                                    <i class="fas fa-edit"></i>
                                                    处理
                                                </a>
                                                <a href="javascript:void(0)" onclick="deleteItem('{$contact.id}', '{$contact.name|htmlentities}', '/admin/contacts?action=delete&id={$contact.id}')" class="btn-action btn-delete">
                                                    <i class="fas fa-trash"></i>
                                                    删除
                                                </a>
                                            </div>
                                        </div>
                                    {/volist}
                                </div>

                                <!-- 自定义分页 -->
                                {if condition="$totalPages > 1"}
                                    <div class="custom-pagination-container">
                                        <nav class="custom-pagination-nav">
                                            <div class="pagination-info">
                                                <span class="pagination-text">
                                                    显示第 {$page} 页，共 {$totalPages} 页，总计 {$totalItems|default=0} 条记录
                                                </span>
                                            </div>
                                            <div class="pagination-buttons">
                                                {if condition="$page > 1"}
                                                    <a href="/admin/contacts?page=1{if condition='!empty($searchKeyword)'}&search={$searchKeyword}{/if}{if condition='!empty($statusFilter)'}&status={$statusFilter}{/if}" class="pagination-btn pagination-first">
                                                        <i class="fas fa-angle-double-left"></i>
                                                        首页
                                                    </a>
                                                    <a href="/admin/contacts?page={$page-1}{if condition='!empty($searchKeyword)'}&search={$searchKeyword}{/if}{if condition='!empty($statusFilter)'}&status={$statusFilter}{/if}" class="pagination-btn pagination-prev">
                                                        <i class="fas fa-angle-left"></i>
                                                        上一页
                                                    </a>
                                                {else /}
                                                    <span class="pagination-btn pagination-first disabled">
                                                        <i class="fas fa-angle-double-left"></i>
                                                        首页
                                                    </span>
                                                    <span class="pagination-btn pagination-prev disabled">
                                                        <i class="fas fa-angle-left"></i>
                                                        上一页
                                                    </span>
                                                {/if}
                                                
                                                <!-- 页码按钮 -->
                                                {php}
                                                    $currentPage = $page;
                                                    $lastPage = $totalPages;
                                                    
                                                    // 简化逻辑：直接显示所有页码（当页数不多时）
                                                    if ($lastPage <= 7) {
                                                        // 页数少时显示全部
                                                        $startPage = 1;
                                                        $endPage = $lastPage;
                                                    } else {
                                                        // 页数多时显示当前页前后2页
                                                        $startPage = max(1, $currentPage - 2);
                                                        $endPage = min($lastPage, $currentPage + 2);
                                                    }
                                                {/php}
                                                
                                                {if condition="$startPage > 1"}
                                                    <a href="/admin/contacts?page=1{if condition='!empty($searchKeyword)'}&search={$searchKeyword}{/if}{if condition='!empty($statusFilter)'}&status={$statusFilter}{/if}" class="pagination-btn pagination-number">1</a>
                                                    {if condition="$startPage > 2"}
                                                        <span class="pagination-ellipsis">...</span>
                                                    {/if}
                                                {/if}
                                                
                                                {php}
                                                    // 生成页码数组
                                                    $pageNumbers = range($startPage, $endPage);
                                                {/php}
                                                
                                                {volist name="pageNumbers" id="pageNum"}
                                                    {if condition="$pageNum == $currentPage"}
                                                        <span class="pagination-btn pagination-number active">{$pageNum}</span>
                                                    {else /}
                                                        <a href="/admin/contacts?page={$pageNum}{if condition='!empty($searchKeyword)'}&search={$searchKeyword}{/if}{if condition='!empty($statusFilter)'}&status={$statusFilter}{/if}" class="pagination-btn pagination-number">{$pageNum}</a>
                                                    {/if}
                                                {/volist}
                                                
                                                {if condition="$endPage < $lastPage"}
                                                    {if condition="$endPage < $lastPage - 1"}
                                                        <span class="pagination-ellipsis">...</span>
                                                    {/if}
                                                    <a href="/admin/contacts?page={$lastPage}{if condition='!empty($searchKeyword)'}&search={$searchKeyword}{/if}{if condition='!empty($statusFilter)'}&status={$statusFilter}{/if}" class="pagination-btn pagination-number">{$lastPage}</a>
                                                {/if}
                                                
                                                {if condition="$page < $totalPages"}
                                                    <a href="/admin/contacts?page={$page+1}{if condition='!empty($searchKeyword)'}&search={$searchKeyword}{/if}{if condition='!empty($statusFilter)'}&status={$statusFilter}{/if}" class="pagination-btn pagination-next">
                                                        下一页
                                                        <i class="fas fa-angle-right"></i>
                                                    </a>
                                                    <a href="/admin/contacts?page={$totalPages}{if condition='!empty($searchKeyword)'}&search={$searchKeyword}{/if}{if condition='!empty($statusFilter)'}&status={$statusFilter}{/if}" class="pagination-btn pagination-last">
                                                        末页
                                                        <i class="fas fa-angle-double-right"></i>
                                                    </a>
                                                {else /}
                                                    <span class="pagination-btn pagination-next disabled">
                                                        下一页
                                                        <i class="fas fa-angle-right"></i>
                                                    </span>
                                                    <span class="pagination-btn pagination-last disabled">
                                                        末页
                                                        <i class="fas fa-angle-double-right"></i>
                                                    </span>
                                                {/if}
                                            </div>
                                        </nav>
                                    </div>
                                {/if}
                            {/if}
                        </div>
                    </div>
                    {/if}

                    {if condition="$action == 'view' && $contact"}
                    <!-- 联系表单详情页面 -->
                    <div class="contacts-container">
                        <div class="list-header">
                            <div class="list-header-content">
                                <div class="list-title-section">
                                    <div class="list-icon">
                                        <i class="fas fa-eye"></i>
                                    </div>
                                    <div class="list-title-text">
                                        <h4 class="list-title">联系表单详情</h4>
                                        <p class="list-subtitle">查看联系表单的详细信息</p>
                                    </div>
                                </div>
                                <a href="/admin/contacts" class="btn-filter">
                                    <i class="fas fa-arrow-left"></i>
                                    返回列表
                                </a>
                            </div>
                        </div>

                        <div class="list-body" style="padding: 30px;">
                            <div class="contact-detail">
                                <!-- 基本信息 -->
                                <div class="detail-section">
                                    <h5 class="section-title">
                                        <i class="fas fa-user"></i>
                                        基本信息
                                    </h5>
                                    <div class="detail-grid">
                                        <div class="detail-item">
                                            <label>姓名</label>
                                            <div class="detail-value">{$contact.name}</div>
                                        </div>
                                        <div class="detail-item">
                                            <label>邮箱</label>
                                            <div class="detail-value">
                                                <a href="mailto:{$contact.email}" class="email-link">
                                                    {$contact.email}
                                                </a>
                                            </div>
                                        </div>
                                        {if condition="!empty($contact.phone)"}
                                        <div class="detail-item">
                                            <label>电话</label>
                                            <div class="detail-value">
                                                <a href="tel:{$contact.phone}" class="phone-link">
                                                    {$contact.phone}
                                                </a>
                                            </div>
                                        </div>
                                        {/if}
                                        {if condition="!empty($contact.company)"}
                                        <div class="detail-item">
                                            <label>公司</label>
                                            <div class="detail-value">{$contact.company}</div>
                                        </div>
                                        {/if}
                                        <div class="detail-item">
                                            <label>状态</label>
                                            <div class="detail-value">
                                                <span class="status-badge status-{$contact.status}">
                                                    {switch name="contact.status"}
                                                        {case value="new"}新消息{/case}
                                                        {case value="read"}已读{/case}
                                                        {case value="replied"}已回复{/case}
                                                        {case value="closed"}已关闭{/case}
                                                        {default /}未知
                                                    {/switch}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="detail-item">
                                            <label>提交时间</label>
                                            <div class="detail-value">{$contact.created_at|date='Y-m-d H:i:s'}</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 主题和内容 -->
                                <div class="detail-section">
                                    <h5 class="section-title">
                                        <i class="fas fa-comment"></i>
                                        咨询内容
                                    </h5>
                                    <div class="detail-item full-width">
                                        <label>主题</label>
                                        <div class="detail-value subject-value">{$contact.subject}</div>
                                    </div>
                                    <div class="detail-item full-width">
                                        <label>详细内容</label>
                                        <div class="detail-value message-value">{$contact.message|nl2br}</div>
                                    </div>
                                </div>

                                <!-- 管理员回复 -->
                                {if condition="!empty($contact.admin_reply)"}
                                <div class="detail-section">
                                    <h5 class="section-title">
                                        <i class="fas fa-reply"></i>
                                        管理员回复
                                    </h5>
                                    <div class="detail-item full-width">
                                        <label>回复内容</label>
                                        <div class="detail-value reply-value">{$contact.admin_reply|nl2br}</div>
                                    </div>
                                    {if condition="!empty($contact.replied_at)"}
                                    <div class="detail-item" style="margin-top: 10px;">
                                        <label>回复时间</label>
                                        <div class="detail-value">{$contact.replied_at|date='Y-m-d H:i:s'}</div>
                                    </div>
                                    {/if}
                                </div>
                                {/if}

                                <!-- 技术信息 -->
                                <div class="detail-section">
                                    <h5 class="section-title">
                                        <i class="fas fa-info-circle"></i>
                                        技术信息
                                    </h5>
                                    <div class="detail-grid">
                                        <div class="detail-item">
                                            <label>IP地址</label>
                                            <div class="detail-value">{$contact.ip_address|default='未记录'}</div>
                                        </div>
                                        {if condition="!empty($contact.user_agent)"}
                                        <div class="detail-item full-width">
                                            <label>浏览器信息</label>
                                            <div class="detail-value user-agent">{$contact.user_agent}</div>
                                        </div>
                                        {/if}
                                    </div>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="detail-actions">
                                    <a href="/admin/contacts?action=edit&id={$contact.id}" class="btn-filter">
                                        <i class="fas fa-edit"></i>
                                        处理表单
                                    </a>
                                    <a href="mailto:{$contact.email}?subject=Re: {$contact.subject|urlencode}" class="btn-filter btn-clear">
                                        <i class="fas fa-envelope"></i>
                                        发送邮件
                                    </a>
                                                                            <a href="javascript:void(0)" onclick="deleteItem('{$contact.id}', '{$contact.name|htmlentities}', '/admin/contacts?action=delete&id={$contact.id}')" class="btn-action btn-delete">
                                        <i class="fas fa-trash"></i>
                                        删除
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {/if}

                    {if condition="$action == 'edit' && $contact"}
                    <!-- 联系表单处理页面 -->
                    <div class="contacts-container">
                        <div class="list-header">
                            <div class="list-header-content">
                                <div class="list-title-section">
                                    <div class="list-icon">
                                        <i class="fas fa-edit"></i>
                                    </div>
                                    <div class="list-title-text">
                                        <h4 class="list-title">处理联系表单</h4>
                                        <p class="list-subtitle">更新状态和添加回复</p>
                                    </div>
                                </div>
                                <a href="/admin/contacts" class="btn-filter">
                                    <i class="fas fa-arrow-left"></i>
                                    返回列表
                                </a>
                            </div>
                        </div>

                        <div class="list-body" style="padding: 30px;">
                            <!-- 原始信息展示 -->
                            <div class="detail-section">
                                <h5 class="section-title">
                                    <i class="fas fa-info-circle"></i>
                                    原始信息
                                </h5>
                                <div class="original-info">
                                    <div class="info-row">
                                        <span class="info-label">姓名：</span>
                                        <span class="info-value">{$contact.name}</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">邮箱：</span>
                                        <span class="info-value">{$contact.email}</span>
                                    </div>
                                    {if condition="!empty($contact.phone)"}
                                    <div class="info-row">
                                        <span class="info-label">电话：</span>
                                        <span class="info-value">{$contact.phone}</span>
                                    </div>
                                    {/if}
                                    <div class="info-row">
                                        <span class="info-label">主题：</span>
                                        <span class="info-value">{$contact.subject}</span>
                                    </div>
                                    <div class="info-row full-width">
                                        <span class="info-label">内容：</span>
                                        <div class="info-message">{$contact.message|nl2br}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 处理表单 -->
                            <div class="detail-section">
                                <h5 class="section-title">
                                    <i class="fas fa-cog"></i>
                                    处理操作
                                </h5>
                                <form method="POST" action="/admin/contacts?action=update_status&id={$contact.id}" class="edit-form">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="status">更新状态</label>
                                            <select id="status" name="status" class="form-control" required>
                                                <option value="new" {if condition="$contact.status == 'new'"}selected{/if}>新消息</option>
                                                <option value="read" {if condition="$contact.status == 'read'"}selected{/if}>已读</option>
                                                <option value="replied" {if condition="$contact.status == 'replied'"}selected{/if}>已回复</option>
                                                <option value="closed" {if condition="$contact.status == 'closed'"}selected{/if}>已关闭</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="admin_reply">管理员回复 (可选)</label>
                                        <textarea id="admin_reply" name="admin_reply" class="form-control" rows="6"
                                                  placeholder="在此输入回复内容...">{$contact.admin_reply|default=''}</textarea>
                                        <small class="form-text">如果填写回复内容，状态将自动设置为"已回复"</small>
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" class="btn-filter">
                                            <i class="fas fa-save"></i>
                                            保存更改
                                        </button>
                                        <a href="/admin/contacts?action=view&id={$contact.id}" class="btn-filter btn-clear">
                                            <i class="fas fa-eye"></i>
                                            查看详情
                                        </a>
                                        <a href="mailto:{$contact.email}?subject=Re: {$contact.subject|urlencode}" class="btn-filter btn-clear">
                                            <i class="fas fa-envelope"></i>
                                            发送邮件
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    {/if}
                </div>
            </div>
        </div>
    </div>



    <!-- 自定义回复确认模态框 -->
    <div class="custom-modal-overlay" id="replyConfirmModalOverlay" style="display: none;">
        <div class="custom-modal reply-modal">
            <div class="modal-icon reply-icon">
                <i class="fas fa-question-circle"></i>
            </div>
            <div class="modal-content">
                <h3 class="modal-title">状态设置确认</h3>
                <p class="modal-message">
                    检测到您填写了回复内容，是否将状态设置为"已回复"？
                </p>

                <!-- 当前状态显示 -->
                <div class="current-status-info">
                    <div class="status-row">
                        <span class="status-label">当前状态：</span>
                        <span class="current-status-value" id="currentStatusDisplay"></span>
                    </div>
                    <div class="status-row">
                        <span class="status-label">将变更为：</span>
                        <span class="new-status-value">已回复</span>
                    </div>
                </div>

                <!-- 回复内容预览 -->
                <div class="reply-preview">
                    <div class="preview-label">
                        <i class="fas fa-eye"></i>
                        回复内容预览：
                    </div>
                    <div class="preview-content" id="replyContentPreview"></div>
                </div>

                <p class="modal-warning reply-warning">
                    <i class="fas fa-info-circle"></i>
                    建议在填写回复内容时将状态设置为"已回复"
                </p>
            </div>
            <div class="modal-actions">
                <button type="button" class="modal-btn btn-cancel" onclick="submitFormDirectly()">
                    <i class="fas fa-times"></i>
                    <span id="keepCurrentStatusText">保持当前状态</span>
                </button>
                <button type="button" class="modal-btn btn-confirm reply-confirm" id="confirmReplyBtn">
                    <i class="fas fa-check"></i>
                    设置为已回复
                </button>
            </div>
        </div>
    </div>
 

    <!-- JavaScript -->

    

    <script>


    // 显示回复确认模态框
    function showReplyConfirmModal(statusSelect, form) {
        const modal = document.getElementById('replyConfirmModalOverlay');
        const confirmBtn = document.getElementById('confirmReplyBtn');
        const replyTextarea = document.getElementById('admin_reply');

        // 获取当前状态文本
        const currentStatusText = statusSelect.options[statusSelect.selectedIndex].text;
        const currentStatusValue = statusSelect.value;

        // 获取回复内容
        const replyContent = replyTextarea.value.trim();

        // 更新模态框内容
        document.getElementById('currentStatusDisplay').textContent = currentStatusText;
        document.getElementById('replyContentPreview').textContent = replyContent;

        // 更新按钮文字
        const keepStatusBtn = document.getElementById('keepCurrentStatusText');
        keepStatusBtn.textContent = `保持"${currentStatusText}"状态`;

        // 设置确认按钮点击事件
        confirmBtn.onclick = function() {
            statusSelect.value = 'replied';
            closeReplyConfirmModal();
            form.submit();
        };

        // 显示模态框
        modal.style.display = 'flex';
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
    }

    // 关闭回复确认模态框
    function closeReplyConfirmModal() {
        const modal = document.getElementById('replyConfirmModalOverlay');
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }

    // 直接提交表单（不修改状态）
    function submitFormDirectly() {
        const editForm = document.querySelector('.edit-form');
        closeReplyConfirmModal();
        editForm.submit();
    }

    // 表单提交前的验证
    document.addEventListener('DOMContentLoaded', function() {
        // 点击遮罩层关闭模态框
        document.getElementById('deleteModalOverlay').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDeleteModal();
            }
        });

        // 点击遮罩层关闭回复确认模态框
        const replyModal = document.getElementById('replyConfirmModalOverlay');
        if (replyModal) {
            replyModal.addEventListener('click', function(e) {
                if (e.target === this) {
                    closeReplyConfirmModal();
                }
            });
        }

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const deleteModal = document.getElementById('deleteModalOverlay');
                const replyModal = document.getElementById('replyConfirmModalOverlay');

                if (deleteModal && deleteModal.classList.contains('show')) {
                    closeDeleteModal();
                } else if (replyModal && replyModal.classList.contains('show')) {
                    closeReplyConfirmModal();
                }
            }
        });

        // 编辑表单验证
        const editForm = document.querySelector('.edit-form');
        if (editForm) {
            editForm.addEventListener('submit', function(e) {
                const statusSelect = document.getElementById('status');
                const replyTextarea = document.getElementById('admin_reply');

                // 如果有回复内容，自动设置状态为已回复
                if (replyTextarea && replyTextarea.value.trim() && statusSelect.value !== 'replied') {
                    e.preventDefault(); // 阻止表单提交
                    showReplyConfirmModal(statusSelect, this);
                }
            });
        }

        // 快速跳转页面
        window.jumpToPage = function() {
            const jumpInput = document.getElementById('jumpPage');
            const page = parseInt(jumpInput.value);
            const totalPages = parseInt('{$totalPages|default=1}');

            if (page >= 1 && page <= totalPages) {
                const searchKeyword = '{$searchKeyword|default=""}';
                const statusFilter = '{$statusFilter|default=""}';

                let url = '/admin/contacts?page=' + page;
                if (searchKeyword) {
                    url += '&search=' + encodeURIComponent(searchKeyword);
                }
                if (statusFilter) {
                    url += '&status=' + encodeURIComponent(statusFilter);
                }

                window.location.href = url;
            } else {
                alert('请输入有效的页码（1-' + totalPages + '）');
                jumpInput.focus();
            }
        };

        // 回车键快速跳转
        const jumpInput = document.getElementById('jumpPage');
        if (jumpInput) {
            jumpInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    jumpToPage();
                }
            });
        }


    });
    </script>
</body>
</html>

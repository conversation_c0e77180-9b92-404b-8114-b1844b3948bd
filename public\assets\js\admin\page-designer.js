/**
 * 页面设计器专用JavaScript
 */

let editor = null;
let templateId = null;

/**
 * 初始化页面设计器
 */
function initPageDesigner() {
    templateId = window.templateId;

    if (!templateId) {
        showMessage('模板ID无效', 'error');
        return;
    }

    // 检查GrapesJS是否加载
    if (typeof grapesjs === 'undefined') {
        showMessage('GrapesJS未加载，请刷新页面重试', 'error');
        return;
    }

    // 检查必要的DOM元素
    const container = document.getElementById('gjs');
    if (!container) {
        showMessage('编辑器容器未找到', 'error');
        return;
    }

    // 检查面板容器
    const requiredContainers = [
        '.blocks-container',
        '.layers-container',
        '.styles-container',
        '.traits-container'
    ];

    let missingContainers = [];
    requiredContainers.forEach(selector => {
        if (!document.querySelector(selector)) {
            missingContainers.push(selector);
        }
    });

    if (missingContainers.length > 0) {
        console.warn('缺少面板容器:', missingContainers);
        // 创建缺失的容器
        missingContainers.forEach(selector => {
            const div = document.createElement('div');
            div.className = selector.substring(1); // 移除点号
            container.parentNode.appendChild(div);
        });
    }

    // 显示加载动画
    showLoading();

    try {
        // 初始化 GrapesJS 编辑器
        editor = grapesjs.init({
        container: '#gjs',
        height: '100%',
        width: 'auto',

        // 画布配置
        canvas: {
            styles: [
                // 确保画布完全填充
                'body { margin: 0; padding: 0; }',
                '.gjs-cv-canvas { width: 100% !important; height: 100% !important; }',
                '.gjs-frame { width: 100% !important; height: 100% !important; }'
            ]
        },

        // 禁用存储管理器，使用手动保存
        storageManager: false,

        // 设备管理器配置
        deviceManager: {
            devices: [
                {
                    name: 'Desktop',
                    width: '',
                },
                {
                    name: 'Tablet',
                    width: '768px',
                    widthMedia: '992px',
                },
                {
                    name: 'Mobile',
                    width: '320px',
                    widthMedia: '768px',
                }
            ]
        },

        // 面板配置
        panels: {
            defaults: [
                {
                    id: 'panel-switcher',
                    el: '.panel__switcher',
                    buttons: [
                        {
                            id: 'show-layers',
                            active: true,
                            label: '图层',
                            command: 'show-layers',
                            togglable: false,
                        },
                        {
                            id: 'show-style',
                            active: true,
                            label: '样式',
                            command: 'show-styles',
                            togglable: false,
                        },
                        {
                            id: 'show-traits',
                            active: true,
                            label: '属性',
                            command: 'show-traits',
                            togglable: false,
                        }
                    ],
                }
            ]
        },

        // 图层管理器配置
        layerManager: {
            appendTo: '.layers-container'
        },

        // 样式管理器配置
        styleManager: {
            appendTo: '.styles-container',
            sectors: [
                {
                    name: '尺寸',
                    open: false,
                    buildProps: ['width', 'min-height', 'padding'],
                },
                {
                    name: '装饰',
                    open: false,
                    buildProps: ['color', 'background-color', 'border-radius', 'border'],
                },
                {
                    name: '字体',
                    open: false,
                    buildProps: ['font-family', 'font-size', 'font-weight', 'letter-spacing', 'color', 'line-height'],
                },
                {
                    name: '布局',
                    open: false,
                    buildProps: ['display', 'flex-direction', 'justify-content', 'align-items', 'flex-wrap'],
                }
            ]
        },

        // 特征管理器配置
        traitManager: {
            appendTo: '.traits-container',
        },



        // 块管理器配置
        blockManager: {
            appendTo: '.blocks-container'
        },

        // 插件配置 - 检查插件是否存在
        plugins: (typeof window.grapesJSBlocksBasic !== 'undefined') ? ['gjs-blocks-basic'] : [],
        pluginsOpts: {
            'gjs-blocks-basic': {}
        },

        // 主色调配置
        colorPicker: {
            appendTo: 'parent',
            offset: { top: 26, left: -166 },
        },

        // CSS配置
        cssComposer: {
            rules: [
                // 应用主色调变量
                ':root { --gjs-primary-color: #2d3748; --gjs-secondary-color: #4a5568; }',
                '.gjs-editor { background-color: var(--gjs-primary-color) !important; }'
            ]
        }
    });

    // 等待编辑器完全初始化
    editor.on('load', function() {
        // 应用深色主题样式
        applyDarkTheme();

        // 添加自定义组件
        addCustomComponents();

        // 添加自定义命令
        addCustomCommands();

        // 绑定事件
        bindEditorEvents();

        // 加载现有设计
        if (templateId && window.templateData) {
            loadDesign();
        }

        // 延迟优化画布布局，避免冲突
        setTimeout(() => {
            optimizeCanvasLayout();
        }, 1500);
    });

        // 隐藏加载动画
        hideLoading();

    } catch (error) {
        console.error('GrapesJS初始化失败:', error);
        console.error('错误堆栈:', error.stack);
        showMessage('编辑器初始化失败: ' + error.message, 'error');
        hideLoading();

        // 显示详细错误信息用于调试
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            z-index: 10000;
            max-width: 80%;
            max-height: 80%;
            overflow: auto;
        `;
        errorDiv.innerHTML = `
            <h3>调试信息</h3>
            <p><strong>错误:</strong> ${error.message}</p>
            <p><strong>GrapesJS状态:</strong> ${typeof grapesjs !== 'undefined' ? '已加载' : '未加载'}</p>
            <p><strong>模板ID:</strong> ${templateId}</p>
            <p><strong>容器:</strong> ${document.getElementById('gjs') ? '存在' : '不存在'}</p>
            <button onclick="this.parentElement.remove()" style="margin-top: 10px; padding: 5px 10px;">关闭</button>
        `;
        document.body.appendChild(errorDiv);
    }
}

/**
 * 添加自定义组件
 */
function addCustomComponents() {
    if (!editor) {
        console.warn('编辑器未初始化，无法添加自定义组件');
        return;
    }

    try {

    // 添加案例卡片组件
    editor.DomComponents.addType('case-card', {
        model: {
            defaults: {
                tagName: 'div',
                classes: ['case-card'],
                content: `
                    <div class="case-card">
                        <div class="case-image">
                            <img src="/assets/images/cases/default.svg" alt="案例图片">
                        </div>
                        <div class="case-content">
                            <h3>案例标题</h3>
                            <p>案例描述...</p>
                            <div class="case-meta">
                                <span class="industry">行业分类</span>
                                <span class="date">2024-01-01</span>
                            </div>
                        </div>
                    </div>
                `
            }
        }
    });

    // 添加新闻卡片组件
    editor.DomComponents.addType('news-card', {
        model: {
            defaults: {
                tagName: 'div',
                classes: ['news-card'],
                content: `
                    <div class="news-card">
                        <div class="news-image">
                            <img src="/assets/images/news/default.svg" alt="新闻图片">
                        </div>
                        <div class="news-content">
                            <h3>新闻标题</h3>
                            <p>新闻摘要...</p>
                            <div class="news-meta">
                                <span class="date">2024-01-01</span>
                                <span class="views">阅读量: 100</span>
                            </div>
                        </div>
                    </div>
                `
            }
        }
    });

    // 将自定义组件添加到块管理器
    editor.BlockManager.add('case-card', {
        label: '案例卡片',
        category: '业务组件',
        content: { type: 'case-card' }
    });

    editor.BlockManager.add('news-card', {
        label: '新闻卡片',
        category: '业务组件',
        content: { type: 'news-card' }
    });

    } catch (error) {
        console.error('添加自定义组件失败:', error);
    }
}

/**
 * 添加自定义命令
 */
function addCustomCommands() {
    if (!editor) return;

    // 添加保存命令
    editor.Commands.add('save-design', {
        run: function(editor, sender) {
            saveDesign();
        }
    });

    // 添加设备切换命令
    editor.Commands.add('set-device-desktop', {
        run: function(editor) {
            editor.setDevice('Desktop');
            updateDeviceButtons('desktop');
        }
    });

    editor.Commands.add('set-device-tablet', {
        run: function(editor) {
            editor.setDevice('Tablet');
            updateDeviceButtons('tablet');
        }
    });

    editor.Commands.add('set-device-mobile', {
        run: function(editor) {
            editor.setDevice('Mobile');
            updateDeviceButtons('mobile');
        }
    });

    // 添加全屏命令
    editor.Commands.add('fullscreen', {
        run: function(editor, sender) {
            const el = editor.getContainer();
            if (el.requestFullscreen) {
                el.requestFullscreen();
            } else if (el.webkitRequestFullscreen) {
                el.webkitRequestFullscreen();
            } else if (el.mozRequestFullScreen) {
                el.mozRequestFullScreen();
            }
        }
    });

    // 添加代码查看命令
    editor.Commands.add('view-code', {
        run: function(editor, sender) {
            const modal = editor.Modal;
            const htmlCode = editor.getHtml();
            const cssCode = editor.getCss();

            modal.setTitle('查看代码');
            modal.setContent(`
                <div style="padding: 20px;">
                    <h4>HTML:</h4>
                    <textarea style="width: 100%; height: 200px; font-family: monospace;">${htmlCode}</textarea>
                    <h4>CSS:</h4>
                    <textarea style="width: 100%; height: 200px; font-family: monospace;">${cssCode}</textarea>
                </div>
            `);
            modal.open();
        }
    });
}

/**
 * 绑定编辑器事件
 */
function bindEditorEvents() {
    if (!editor) return;

    // 编辑器加载完成事件
    editor.on('load', function() {
        console.log('编辑器加载完成');
    });

    // 组件选择事件
    editor.on('component:selected', function(component) {
        console.log('选择组件:', component);
    });

    // 组件更新事件
    editor.on('component:update', function(component) {
        console.log('组件更新:', component);
    });

    // 存储事件
    editor.on('storage:start', function() {
        showMessage('正在保存...', 'info');
    });

    editor.on('storage:end', function() {
        console.log('存储完成');
    });

    editor.on('storage:error', function(err) {
        console.error('存储错误:', err);
        showMessage('保存失败', 'error');
    });
}

/**
 * 保存设计
 */
function saveDesign() {
    if (!editor || !templateId) {
        showMessage('编辑器未初始化或模板ID无效', 'error');
        return;
    }

    const data = {
        action: 'save_design',
        id: templateId,
        config: JSON.stringify(editor.getProjectData()),
        html: editor.getHtml(),
        css: editor.getCss()
    };

    fetch('/admin/page-designer', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showMessage('设计已保存', 'success');
        } else {
            showMessage('保存失败: ' + result.message, 'error');
        }
    })
    .catch(error => {
        console.error('保存失败:', error);
        showMessage('保存失败: ' + error.message, 'error');
    });
}

/**
 * 加载设计
 */
function loadDesign() {
    if (!templateId || !window.templateData || !editor) {
        console.warn('无法加载设计：缺少必要参数');
        return;
    }

    try {
        const templateData = window.templateData;

        if (templateData.config && templateData.config !== '{}' && templateData.config !== '') {
            try {
                const config = typeof templateData.config === 'string'
                    ? JSON.parse(templateData.config)
                    : templateData.config;

                if (config && typeof config === 'object') {
                    editor.loadProjectData(config);
                    console.log('设计配置加载成功');
                }
            } catch (parseError) {
                console.warn('配置解析失败，尝试加载HTML/CSS:', parseError);
                loadHtmlCss(templateData);
            }
        } else {
            loadHtmlCss(templateData);
        }
    } catch (e) {
        console.error('加载设计失败:', e);
        showMessage('加载设计失败: ' + e.message, 'error');
    }
}

/**
 * 加载HTML和CSS内容
 */
function loadHtmlCss(templateData) {
    try {
        if (templateData.html && templateData.html.trim() !== '') {
            editor.setComponents(templateData.html);
        }
        if (templateData.css && templateData.css.trim() !== '') {
            editor.setStyle(templateData.css);
        }
        console.log('HTML/CSS内容加载成功');
    } catch (e) {
        console.error('HTML/CSS加载失败:', e);
    }
}

/**
 * 更新设备按钮状态
 */
function updateDeviceButtons(activeDevice) {
    const buttons = document.querySelectorAll('.device-btn');
    buttons.forEach(btn => {
        btn.classList.remove('active');
    });

    const activeBtn = document.getElementById(`device-${activeDevice}`);
    if (activeBtn) {
        activeBtn.classList.add('active');
    }
}

/**
 * 显示消息提示
 */
function showMessage(message, type = 'info') {
    // 移除现有的消息提示
    const existingToast = document.querySelector('.message-toast');
    if (existingToast) {
        existingToast.remove();
    }

    // 创建新的消息提示
    const toast = document.createElement('div');
    toast.className = `message-toast ${type}`;
    toast.textContent = message;

    document.body.appendChild(toast);

    // 3秒后自动移除
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

/**
 * 显示加载动画
 */
function showLoading() {
    const overlay = document.createElement('div');
    overlay.className = 'loading-overlay';
    overlay.innerHTML = '<div class="loading-spinner"></div>';

    const container = document.querySelector('.page-designer-container');
    if (container) {
        container.appendChild(overlay);
    }
}

/**
 * 隐藏加载动画
 */
function hideLoading() {
    const overlay = document.querySelector('.loading-overlay');
    if (overlay) {
        overlay.remove();
    }
}

// 全局错误处理
window.addEventListener('error', function(event) {
    console.error('页面设计器错误:', event.error);
    if (event.error && event.error.message) {
        // 只显示关键错误，避免干扰用户
        if (event.error.message.includes('grapesjs') ||
            event.error.message.includes('editor') ||
            event.error.message.includes('template')) {
            showMessage('编辑器运行时错误，请刷新页面重试', 'warning');
        }
    }
});

// 页面加载完成后绑定事件
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面设计器脚本已加载');
    console.log('GrapesJS状态:', typeof grapesjs !== 'undefined' ? '已加载' : '未加载');
    console.log('模板ID:', window.templateId);
    console.log('模板数据:', window.templateData);
    // 绑定保存按钮事件
    const saveBtn = document.getElementById('save-design');
    if (saveBtn) {
        saveBtn.addEventListener('click', saveDesign);
    }

    // 绑定设备切换按钮事件
    const desktopBtn = document.getElementById('device-desktop');
    if (desktopBtn) {
        desktopBtn.addEventListener('click', () => {
            if (editor) {
                editor.runCommand('set-device-desktop');
            }
        });
    }

    const tabletBtn = document.getElementById('device-tablet');
    if (tabletBtn) {
        tabletBtn.addEventListener('click', () => {
            if (editor) {
                editor.runCommand('set-device-tablet');
            }
        });
    }

    const mobileBtn = document.getElementById('device-mobile');
    if (mobileBtn) {
        mobileBtn.addEventListener('click', () => {
            if (editor) {
                editor.runCommand('set-device-mobile');
            }
        });
    }

    // 绑定工具按钮事件
    const fullscreenBtn = document.getElementById('fullscreen-btn');
    if (fullscreenBtn) {
        fullscreenBtn.addEventListener('click', () => {
            if (editor) {
                editor.runCommand('fullscreen');
            }
        });
    }

    const codeBtn = document.getElementById('code-btn');
    if (codeBtn) {
        codeBtn.addEventListener('click', () => {
            if (editor) {
                editor.runCommand('view-code');
            }
        });
    }
});

/**
 * 应用深色主题样式
 */
function applyDarkTheme() {
    if (!editor) return;

    // 获取编辑器容器
    const editorEl = editor.getContainer();
    if (!editorEl) return;

    // 应用主色调CSS变量
    const style = document.createElement('style');
    style.textContent = `
        .gjs-editor {
            --gjs-primary-color: #2d3748 !important;
            --gjs-secondary-color: #4a5568 !important;
            --gjs-tertiary-color: #718096 !important;
            --gjs-text-color: #e2e8f0 !important;
            --gjs-border-color: #4a5568 !important;
        }
    `;
    editorEl.appendChild(style);

    console.log('深色主题样式已应用');
}

/**
 * 优化画布布局
 */
function optimizeCanvasLayout() {
    if (!editor) return;

    try {
        // 获取编辑器容器
        const editorContainer = editor.getContainer();
        if (!editorContainer) return;

        // 只进行必要的样式调整，避免破坏画布
        const canvasContainer = editorContainer.querySelector('.gjs-cv-canvas');
        if (canvasContainer) {
            canvasContainer.style.width = '100%';
            canvasContainer.style.height = '100%';
            canvasContainer.style.flex = '1';
        }

        // 轻量级工具栏隐藏
        const tools = editorContainer.querySelectorAll('.gjs-cv-canvas__tools');
        tools.forEach(tool => {
            if (tool) {
                tool.style.display = 'none';
            }
        });

        console.log('画布布局优化完成');
    } catch (error) {
        console.warn('画布布局优化失败:', error);
    }
}



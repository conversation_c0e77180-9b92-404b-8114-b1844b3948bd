{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/calendar/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Calendar from './src/calendar.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElCalendar: SFCWithInstall<typeof Calendar> = withInstall(Calendar)\nexport default ElCalendar\n\nexport * from './src/calendar'\nexport type {\n  CalendarDateTableInstance,\n  DateTableInstance,\n  CalendarInstance,\n} from './src/instance'\n"], "names": ["withInstall", "Calendar"], "mappings": ";;;;;;;;AAEY,MAAC,UAAU,GAAGA,mBAAW,CAACC,qBAAQ;;;;;;;"}
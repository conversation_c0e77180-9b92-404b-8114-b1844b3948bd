/**
 * 滚动处理器 - 专门处理导航栏滚动效果
 */

(function() {
    'use strict';
    
    // 确保DOM加载完成后执行
    function initScrollHandler() {
        const header = document.querySelector('header');
        if (!header) {
            return;
        }
        
        // 滚动处理函数
        function handleScroll() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            if (scrollTop >= 50) {
                if (!header.classList.contains('scrolled')) {
                    header.classList.add('scrolled');
                }
            } else {
                if (header.classList.contains('scrolled')) {
                    header.classList.remove('scrolled');
                }
            }
        }
        
        // 节流函数
        function throttle(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            }
        }
        
        // 绑定滚动事件（节流处理）
        const throttledScroll = throttle(handleScroll, 16); // 约60fps
        
        window.addEventListener('scroll', throttledScroll, { passive: true });
        
        // 初始检查
        handleScroll();

        // 强制触发一次滚动检查
        setTimeout(() => {
            handleScroll();
        }, 100);
    }
    
    // 多种方式确保初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initScrollHandler);
    } else {
        initScrollHandler();
    }
    
    // 备用初始化
    window.addEventListener('load', initScrollHandler);
    
    // jQuery备用方案
    if (typeof jQuery !== 'undefined') {
        jQuery(function($){
            initScrollHandler();
        });
    }
    
})();
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QiyeDIY企业建站系统2</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f8fafc;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
            text-align: center;
        }
        
        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        .hero p {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .hero-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-primary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .features {
            padding: 80px 0;
        }
        
        .features h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #1e293b;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }
        
        .feature-item {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .feature-item:hover {
            transform: translateY(-5px);
        }
        
        .feature-item h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #1e293b;
        }
        
        .feature-item p {
            color: #64748b;
            line-height: 1.6;
        }
        
        .status {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .status h3 {
            color: #1e293b;
            margin-bottom: 10px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
        }
        
        .status-ok {
            color: #10b981;
        }
        
        .status-error {
            color: #ef4444;
        }
    </style>
</head>
<body>
    <div class="hero">
        <div class="container">
            <h1>QiyeDIY企业建站系统</h1>
            <p>专业的企业级DIY建站解决方案</p>
            <div class="hero-actions">
                <a href="http://localhost:3001" class="btn btn-primary">管理后台</a>
                <a href="http://localhost:8000/api/test" class="btn btn-primary">API测试</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="status">
            <h3>系统状态</h3>
            <div class="status-item">
                <span>前端服务:</span>
                <span class="status-ok">运行中 (localhost:3000)</span>
            </div>
            <div class="status-item">
                <span>后端API:</span>
                <span id="backend-status">检测中...</span>
            </div>
            <div class="status-item">
                <span>管理后台:</span>
                <span id="admin-status">检测中...</span>
            </div>
        </div>
    </div>
    
    <section class="features">
        <div class="container">
            <h2>核心特性</h2>
            <div class="feature-grid">
                <div class="feature-item">
                    <h3>🎨 可视化编辑</h3>
                    <p>拖拽式页面编辑，所见即所得</p>
                </div>
                <div class="feature-item">
                    <h3>📱 响应式设计</h3>
                    <p>自动适配桌面、平板、手机</p>
                </div>
                <div class="feature-item">
                    <h3>🚀 高性能</h3>
                    <p>优化的加载速度和SEO友好</p>
                </div>
                <div class="feature-item">
                    <h3>🎯 模板丰富</h3>
                    <p>多行业精美模板，一键应用</p>
                </div>
            </div>
        </div>
    </section>
    
    <script>
        // 检测后端API状态
        fetch('http://localhost:8000/api/test')
            .then(response => response.json())
            .then(data => {
                document.getElementById('backend-status').innerHTML = '<span class="status-ok">运行正常</span>';
            })
            .catch(error => {
                document.getElementById('backend-status').innerHTML = '<span class="status-error">连接失败</span>';
            });
            
        // 检测管理后台状态
        fetch('http://localhost:3001')
            .then(response => {
                document.getElementById('admin-status').innerHTML = '<span class="status-ok">运行正常</span>';
            })
            .catch(error => {
                document.getElementById('admin-status').innerHTML = '<span class="status-error">连接失败</span>';
            });
    </script>
</body>
</html>

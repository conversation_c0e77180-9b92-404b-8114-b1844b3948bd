/**
 * 通用图片上传弹窗组件
 * 支持多选、排序、预览、上传等功能
 * 作者: AI龙头韩哥
 * 版本: 1.0.0
 */

class ImageUploader {
    constructor(options = {}) {
        this.options = {
            // 基本配置
            multiple: true,                    // 是否支持多选
            maxFiles: 10,                     // 最大文件数量
            maxSize: 5 * 1024 * 1024,        // 最大文件大小 (5MB)
            allowedTypes: ['image/jpeg', 'image/png'], // 严格限制：只允许JPG和PNG
            
            // 上传配置
            uploadUrl: '/admin/upload',       // 上传接口
            uploadField: 'upload',            // 上传字段名
            
            // 回调函数
            onSelect: null,                   // 选择文件后回调
            onConfirm: null,                  // 确认选择回调
            onUpload: null,                   // 上传成功回调
            onError: null,                    // 错误回调
            onClose: null,                    // 关闭弹窗回调
            
            // 图片选择模式配置
            enableImageSelector: false,       // 是否启用图片选择器
            selectorUrl: '/admin/image/selector', // 图片选择器API地址
            
            // 样式配置
            theme: 'dark',                    // 主题: dark/light
            
            ...options
        };
        
        this.selectedFiles = [];              // 已选择的文件
        this.uploadedFiles = [];              // 已上传的文件
        this.selectedImages = [];             // 从服务器选择的图片
        this.isVisible = false;               // 弹窗是否可见
        this.dragCounter = 0;                 // 拖拽计数器
        this.mode = 'upload';                 // 模式: upload(上传) / select(选择)
        
        this.init();
    }
    
    // 初始化组件
    init() {
        this.createModal();
        this.bindEvents();
        
        // 设置初始模式为上传模式
        this.mode = 'upload';
    }
    
    // 创建弹窗HTML结构
    createModal() {
        // 生成唯一的Modal ID
        this.modalId = this.options.instanceId ? 
            `imageUploaderModal_${this.options.instanceId}` : 
            `imageUploaderModal_${Date.now()}`;
        
        const modalHtml = `
            <div class="image-uploader-overlay" id="${this.modalId}">
                <div class="image-uploader-modal">
                    <!-- 弹窗头部 -->
                    <div class="image-uploader-header">
                        <div class="header-title">
                            <i class="fas fa-images"></i>
                            <span>图片上传</span>
                        </div>
                        <div class="header-actions">
                            <button class="btn-minimize" title="最小化">
                                <i class="fas fa-minus"></i>
                            </button>
                            <button class="btn-close" title="关闭">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 弹窗内容 -->
                    <div class="image-uploader-content">
                        <!-- 模式选择选项卡 -->
                        <div class="mode-tabs" id="modeTabs_${this.modalId}" style="display: ${this.options.enableImageSelector ? 'flex' : 'none'};">
                            <button class="mode-tab active" data-mode="upload">
                                <i class="fas fa-upload"></i>
                                <span>上传图片</span>
                            </button>
                            <button class="mode-tab" data-mode="select">
                                <i class="fas fa-images"></i>
                                <span>选择图片</span>
                            </button>
                        </div>
                        
                        <!-- 上传区域 -->
                        <div class="upload-zone" id="uploadZone_${this.modalId}">
                            <div class="upload-zone-content">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <div class="upload-text">
                                    <h3>拖拽图片到此处或点击选择</h3>
                                    <p>支持 JPG、PNG、GIF、WebP 格式，单个文件最大 5MB</p>
                                    <p>最多可选择 ${this.options.maxFiles} 张图片</p>
                                </div>
                                <button class="btn-select-files">
                                    <i class="fas fa-folder-open"></i>
                                    选择图片
                                </button>
                            </div>
                            <input type="file" id="fileInput_${this.modalId}" multiple accept="image/*" style="display: none;">
                        </div>
                        
                        <!-- 图片选择区域 -->
                        <div class="image-selector-area" id="imageSelectorArea_${this.modalId}" style="display: none;">
                            <div class="selector-header">
                                <div class="selector-filters-info">
                                    <div class="selector-filters">
                                        <select class="selector-group-filter" id="selectorGroupFilter_${this.modalId}">
                                            <option value="">所有分组</option>
                                        </select>
                                        <input type="text" class="selector-search" id="selectorSearch_${this.modalId}" placeholder="搜索图片...">
                                        <button class="btn-search" id="btnSearch_${this.modalId}">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                    <div class="selector-info">
                                        <span class="selected-count">已选择 <strong id="selectorSelectedCount_${this.modalId}">0</strong> 张图片</span>
                                    </div>
                                </div>
                            </div>
                            <div class="selector-grid" id="selectorGrid_${this.modalId}">
                                <!-- 图片网格将在这里动态生成 -->
                            </div>
                            <div class="selector-pagination" id="selectorPagination_${this.modalId}">
                                <!-- 分页将在这里动态生成 -->
                            </div>
                        </div>
                        
                        <!-- 图片预览区域 -->
                        <div class="preview-area" id="previewArea_${this.modalId}" style="display: none;">
                            <div class="preview-header">
                                <div class="preview-title">
                                    <i class="fas fa-images"></i>
                                    <span>已选择图片 (<span id="fileCount_${this.modalId}">0</span>/${this.options.maxFiles})</span>
                                </div>
                                <div class="preview-actions">
                                    <button class="btn-add-more">
                                        <i class="fas fa-plus"></i>
                                        继续添加
                                    </button>
                                    <button class="btn-clear-all">
                                        <i class="fas fa-trash"></i>
                                        清空所有
                                    </button>
                                </div>
                            </div>
                            <div class="preview-grid" id="previewGrid_${this.modalId}">
                                <!-- 图片预览项将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- 弹窗底部 -->
                    <div class="image-uploader-footer">
                        <div class="footer-info">
                            <span class="selected-count">已选择 <strong id="selectedCount_${this.modalId}">0</strong> 张图片</span>
                            <span class="upload-progress" id="uploadProgress_${this.modalId}" style="display: none;">
                                上传进度: <strong id="progressText_${this.modalId}">0%</strong>
                            </span>
                        </div>
                        <div class="footer-actions">
                            <button class="btn-cancel">
                                <i class="fas fa-times"></i>
                                取消
                            </button>
                            <button class="btn-confirm" id="btnConfirm_${this.modalId}" disabled>
                                <i class="fas fa-check"></i>
                                确认选择
                            </button>
                            <button class="btn-upload" id="btnUpload_${this.modalId}" style="display: none;">
                                <i class="fas fa-upload"></i>
                                开始上传
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        this.modal = document.getElementById(this.modalId);
    }
    
    // 绑定事件
    bindEvents() {
        const fileInput = document.getElementById(`fileInput_${this.modalId}`);
        const uploadZone = document.getElementById(`uploadZone_${this.modalId}`);
        const btnSelectFiles = this.modal.querySelector('.btn-select-files');
        const btnClose = this.modal.querySelector('.btn-close');
        const btnCancel = this.modal.querySelector('.btn-cancel');
        const btnConfirm = document.getElementById(`btnConfirm_${this.modalId}`);
        const btnUpload = document.getElementById(`btnUpload_${this.modalId}`);
        const btnAddMore = this.modal.querySelector('.btn-add-more');
        const btnClearAll = this.modal.querySelector('.btn-clear-all');
        
        // 检查必要元素是否存在
        if (!fileInput) {
            console.error(`❌ 未找到文件输入元素: fileInput_${this.modalId}`);
            return;
        }
        if (!uploadZone) {
            console.error(`❌ 未找到上传区域元素: uploadZone_${this.modalId}`);
            return;
        }
        if (!btnSelectFiles) {
            console.error(`❌ 未找到选择文件按钮元素`);
            return;
        }
        
        // 文件选择 - 只绑定存在的元素
        if (fileInput) fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        if (btnSelectFiles) btnSelectFiles.addEventListener('click', () => fileInput.click());
        if (btnAddMore) btnAddMore.addEventListener('click', () => fileInput.click());
        
        // 拖拽上传 - 只绑定存在的元素
        if (uploadZone) {
            uploadZone.addEventListener('dragover', (e) => this.handleDragOver(e));
            uploadZone.addEventListener('dragenter', (e) => this.handleDragEnter(e));
            uploadZone.addEventListener('dragleave', (e) => this.handleDragLeave(e));
            uploadZone.addEventListener('drop', (e) => this.handleDrop(e));
        }
        
        // 弹窗控制 - 只绑定存在的元素
        if (btnClose) btnClose.addEventListener('click', () => this.close());
        if (btnCancel) btnCancel.addEventListener('click', () => this.close());
        if (btnConfirm) btnConfirm.addEventListener('click', () => this.confirm());
        if (btnUpload) btnUpload.addEventListener('click', () => this.upload());
        if (btnClearAll) btnClearAll.addEventListener('click', () => this.clearAll());
        
        // 模式切换事件绑定 - 只绑定存在的元素
        const modeTabs = this.modal.querySelectorAll('.mode-tab');
        if (modeTabs.length > 0) {
            modeTabs.forEach(tab => {
                tab.addEventListener('click', (e) => {
                    e.preventDefault();
                    const mode = tab.getAttribute('data-mode');
                    this.switchMode(mode);
                });
            });
        }
        
        // 点击遮罩关闭 - this.modal总是存在的
        if (this.modal) {
            this.modal.addEventListener('click', (e) => {
                if (e.target === this.modal) {
                    this.close();
                }
            });
        }
        
        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible) {
                this.close();
            }
        });
    }
    
    // 显示弹窗
    show() {
        this.modal.style.display = 'flex';
        this.isVisible = true;
        document.body.style.overflow = 'hidden';
        
        // 动画效果
        setTimeout(() => {
            this.modal.classList.add('show');
        }, 10);
    }
    
    // 切换模式
    switchMode(mode) {
        this.mode = mode;
        
        // 更新选项卡状态
        const modeTabs = this.modal.querySelectorAll('.mode-tab');
        modeTabs.forEach(tab => {
            if (tab.getAttribute('data-mode') === mode) {
                tab.classList.add('active');
            } else {
                tab.classList.remove('active');
            }
        });
        
        // 显示/隐藏对应的内容区域
        const uploadZone = this.modal.querySelector(`#uploadZone_${this.modalId}`);
        const imageSelectorArea = this.modal.querySelector(`#imageSelectorArea_${this.modalId}`);
        const previewArea = this.modal.querySelector(`#previewArea_${this.modalId}`);
        
        if (mode === 'upload') {
            // 上传模式
            if (uploadZone) uploadZone.style.display = this.selectedFiles.length > 0 ? 'none' : 'flex';
            if (imageSelectorArea) imageSelectorArea.style.display = 'none';
            if (previewArea) previewArea.style.display = this.selectedFiles.length > 0 ? 'block' : 'none';
        } else if (mode === 'select') {
            // 选择模式
            if (uploadZone) uploadZone.style.display = 'none';
            if (imageSelectorArea) imageSelectorArea.style.display = 'block';
            if (previewArea) previewArea.style.display = 'none';
            
            // 如果开启了图片选择器，加载图片列表
            if (this.options.enableImageSelector) {
                this.loadImageSelector();
            }
        }
        
        this.updateUI();
    }
    
    // 加载图片选择器（简化版，避免复杂的依赖）
    loadImageSelector() {
        const selectorGrid = this.modal.querySelector(`#selectorGrid_${this.modalId}`);
        if (selectorGrid) {
            selectorGrid.innerHTML = '<div style="text-align: center; padding: 40px; color: rgba(255,255,255,0.6);">图片选择器功能需要后端支持</div>';
        }
    }
    
    // 关闭弹窗
    close() {
        if (!this.isVisible) return;
        
            this.isVisible = false;
        
        if (this.modal) {
            this.modal.style.display = 'none';
        }
        
        // 恢复页面滚动
            document.body.style.overflow = '';
        
        // 触发关闭回调
        if (this.options.onClose) {
            this.options.onClose();
        }
        
        console.log('🔒 图片上传弹窗已关闭');
    }
    
    // 处理文件选择
    handleFileSelect(event) {
        const files = Array.from(event.target.files);
        
        // 如果没有文件，直接返回（避免清空input时的重复调用）
        if (files.length === 0) {
            return;
        }
        
        this.addFiles(files);
        
        // 清空input，允许重复选择同一文件
        event.target.value = '';
    }
    
    // 添加文件
    addFiles(files) {
        const validFiles = [];
        
        for (const file of files) {
            // 检查文件数量限制
            if (this.selectedFiles.length >= this.options.maxFiles) {
                this.showMessage(`最多只能选择 ${this.options.maxFiles} 张图片`, 'warning');
                break;
            }
            
                    // 严格检查文件类型
        if (!this.options.allowedTypes.includes(file.type)) {
            this.showMessage(`不支持的文件格式: ${file.name}，仅支持JPG和PNG格式`, 'error');
            continue;
        }
        
        // 检查文件大小
        if (file.size > this.options.maxSize) {
            const sizeMB = (this.options.maxSize / 1024 / 1024).toFixed(1);
            this.showMessage(`文件过大: ${file.name} (最大${sizeMB}MB)`, 'error');
            continue;
        }
        
        // 检查最小文件大小（防止空文件）
        if (file.size < 1024) {
            this.showMessage(`文件过小: ${file.name} (最小1KB)`, 'error');
            continue;
        }
        
        // 检查文件名安全性
        if (file.name.includes('..') || file.name.includes('/') || file.name.includes('\\') || 
            file.name.includes('<') || file.name.includes('>')) {
            this.showMessage(`文件名包含非法字符: ${file.name}`, 'error');
            continue;
        }
        
        // 检查文件名长度
        if (file.name.length > 255) {
            this.showMessage(`文件名过长: ${file.name}`, 'error');
            continue;
        }
            
            // 检查是否已存在（仅在当前实例中检查）
            if (this.selectedFiles.some(f => f.name === file.name && f.size === file.size)) {
                this.showMessage(`文件已存在: ${file.name}`, 'warning');
                continue;
            }
            
            validFiles.push(file);
        }
        
        // 触发选择回调，让外部验证和过滤文件
        let finalFiles = validFiles;
        if (this.options.onSelect) {
            const result = this.options.onSelect(validFiles);
            
            // 如果回调返回false，表示验证失败
            if (result === false) {
                return;
            }
            
            // 如果回调返回了新的文件数组，使用返回的数组
            if (result && Array.isArray(result)) {
                finalFiles = result;
            }
        }
        
        // 添加最终的有效文件
        if (finalFiles.length > 0) {
            this.selectedFiles.push(...finalFiles);
            this.updatePreview();
            this.updateUI();
        }
    }
    
    // 更新预览区域
    updatePreview() {
        const previewArea = this.modal.querySelector(`#previewArea_${this.modalId}`);
        const previewGrid = this.modal.querySelector(`#previewGrid_${this.modalId}`);
        const uploadZone = this.modal.querySelector(`#uploadZone_${this.modalId}`);
        
        if (this.selectedFiles.length > 0) {
            if (previewArea) previewArea.style.display = 'block';
            if (uploadZone) uploadZone.style.display = 'none';
            
            // 清空现有预览
            if (previewGrid) {
            previewGrid.innerHTML = '';
            
            // 生成预览项
            this.selectedFiles.forEach((file, index) => {
                const previewItem = this.createPreviewItem(file, index);
                previewGrid.appendChild(previewItem);
            });
            }
        } else {
            if (previewArea) previewArea.style.display = 'none';
            if (uploadZone) uploadZone.style.display = 'flex';
        }
    }
    
    // 创建预览项
    createPreviewItem(file, index) {
        const item = document.createElement('div');
        item.className = 'preview-item';
        item.setAttribute('data-index', index);
        
        // 创建图片预览
        const reader = new FileReader();
        reader.onload = (e) => {
            item.innerHTML = `
                <div class="preview-image">
                    <img src="${e.target.result}" alt="${file.name}">
                    <div class="preview-overlay">
                        <div class="preview-actions">
                            <button class="btn-preview" title="预览">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn-move-up" title="上移" ${index === 0 ? 'disabled' : ''}>
                                <i class="fas fa-arrow-up"></i>
                            </button>
                            <button class="btn-move-down" title="下移" ${index === this.selectedFiles.length - 1 ? 'disabled' : ''}>
                                <i class="fas fa-arrow-down"></i>
                            </button>
                            <button class="btn-remove" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="preview-info">
                    <div class="file-name" title="${file.name}">${file.name}</div>
                    <div class="file-size">${this.formatFileSize(file.size)}</div>
                    <div class="file-order">第 ${index + 1} 张</div>
                </div>
            `;
            
            // 绑定预览项事件
            this.bindPreviewItemEvents(item, index);
        };
        reader.readAsDataURL(file);
        
        return item;
    }
    
    // 绑定预览项事件
    bindPreviewItemEvents(item, index) {
        const btnRemove = item.querySelector('.btn-remove');
        const btnMoveUp = item.querySelector('.btn-move-up');
        const btnMoveDown = item.querySelector('.btn-move-down');
        const btnPreview = item.querySelector('.btn-preview');
        
        btnRemove.addEventListener('click', () => this.removeFile(index));
        btnMoveUp.addEventListener('click', () => this.moveFile(index, index - 1));
        btnMoveDown.addEventListener('click', () => this.moveFile(index, index + 1));
        btnPreview.addEventListener('click', () => this.previewFile(index));
    }
    
    // 移除文件
    removeFile(index) {
        const fileName = this.selectedFiles[index]?.name || `第 ${index + 1} 个文件`;
        this.selectedFiles.splice(index, 1);
        this.updatePreview();
        this.updateUI();
        console.log(`🗑️ 已移除第 ${index + 1} 个文件`);
        this.showMessage(`已删除 ${fileName}`, 'success');
    }
    
    // 移动文件位置
    moveFile(fromIndex, toIndex) {
        if (toIndex < 0 || toIndex >= this.selectedFiles.length) return;
        
        const file = this.selectedFiles.splice(fromIndex, 1)[0];
        this.selectedFiles.splice(toIndex, 0, file);
        this.updatePreview();
        this.updateUI();
        
        console.log(`📋 文件从位置 ${fromIndex + 1} 移动到 ${toIndex + 1}`);
        this.showMessage(`文件已移动到第 ${toIndex + 1} 位`, 'success');
    }
    
    // 预览文件
    previewFile(index) {
        const file = this.selectedFiles[index];
        // 这里可以实现大图预览功能
        console.log(`👁️ 预览文件: ${file.name}`);
    }
    
    // 清空所有文件
    clearAll() {
        if (this.selectedFiles.length === 0) {
            this.showMessage('没有文件需要清空', 'info');
            return;
        }
        
        // 确认清空
        if (confirm(`确定要清空所有 ${this.selectedFiles.length} 个文件吗？`)) {
            const clearedCount = this.selectedFiles.length;
            this.selectedFiles = [];
            this.updatePreview();
            this.updateUI();
            console.log(`🗑️ 已清空 ${clearedCount} 个文件`);
            this.showMessage(`已清空 ${clearedCount} 个文件`, 'success');
        }
    }
    
    // 更新UI状态
    updateUI() {
        // 使用相对于当前弹窗的选择器，避免ID冲突
        const fileCount = this.modal.querySelector(`#fileCount_${this.modalId}`);
        const selectedCount = this.modal.querySelector(`#selectedCount_${this.modalId}`);
        const btnConfirm = this.modal.querySelector(`#btnConfirm_${this.modalId}`);
        const btnUpload = this.modal.querySelector(`#btnUpload_${this.modalId}`);
        
        const count = this.selectedFiles.length;
        
        if (fileCount) {
            fileCount.textContent = count;
        }
        
        if (selectedCount) {
            selectedCount.textContent = count;
        }
        
        // 更新按钮状态
        if (count > 0) {
            if (btnConfirm) {
                btnConfirm.disabled = false;
            }
            
            // 只在上传模式下显示上传按钮
            if (btnUpload) {
                if (this.mode === 'upload') {
                    btnUpload.style.display = 'inline-flex';
                } else {
                    btnUpload.style.display = 'none';
                }
            }
        } else {
            if (btnConfirm) {
                btnConfirm.disabled = true;
            }
            if (btnUpload) {
                btnUpload.style.display = 'none';
            }
        }
    }
    
    // 确认选择
    confirm() {
        // 检查是否有选择的内容（文件或图片）
        const hasSelectedFiles = this.selectedFiles && this.selectedFiles.length > 0;
        const hasSelectedImages = this.selectedImages && this.selectedImages.length > 0;
        
        if (!hasSelectedFiles && !hasSelectedImages) {
            // 在编辑器环境中，如果没有选择文件，自动切换到选择模式
            const isEditor = this.options.context === 'editor' || this.options.isEditor || this.options.maxFiles >= 999;
            if (isEditor && this.options.enableImageSelector) {
                console.log('🔄 编辑器环境：自动切换到选择图片模式');
                this.switchMode('select');
                return;
            } else {
            this.showMessage('请先选择图片', 'warning');
                return;
            }
        }
        
        // 如果有选择的图片，使用图片选择器的确认逻辑
        if (hasSelectedImages) {
            if (typeof this.confirmSelection === 'function') {
                this.confirmSelection();
            } else {
                // 如果confirmSelection方法不存在，直接触发onConfirm回调
                if (this.options.onConfirm) {
                    this.options.onConfirm(this.selectedImages, 'select');
                }
                this.close();
            }
            return;
        }
        
        // 按照当前顺序处理文件
        const orderedFiles = [...this.selectedFiles];
        
        // 触发确认回调，传递有序的文件列表
        if (this.options.onSelect) {
            this.options.onSelect(orderedFiles);
        }
        
        // 如果有onConfirm回调，也触发它
        if (this.options.onConfirm) {
            this.options.onConfirm(orderedFiles);
        }
        
        this.close();
    }
    
    // 上传文件
    async upload() {
        if (this.selectedFiles.length === 0) {
            // 在编辑器环境中，如果没有选择文件，自动切换到选择模式
            const isEditor = this.options.context === 'editor' || this.options.isEditor || this.options.maxFiles >= 999;
            if (isEditor && this.options.enableImageSelector) {
                console.log('🔄 编辑器环境：自动切换到选择图片模式');
                this.switchMode('select');
                return;
            } else {
            this.showMessage('请先选择图片', 'warning');
            return;
            }
        }
        
        const progressElement = this.modal.querySelector(`#uploadProgress_${this.modalId}`);
        const progressText = this.modal.querySelector(`#progressText_${this.modalId}`);
        const btnUpload = this.modal.querySelector(`#btnUpload_${this.modalId}`);
        
        if (progressElement) progressElement.style.display = 'inline';
        if (btnUpload) {
        btnUpload.disabled = true;
        btnUpload.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 上传中...';
        }
        
        try {
            const uploadedFiles = [];
            const totalFiles = this.selectedFiles.length;
            
            for (let i = 0; i < totalFiles; i++) {
                const file = this.selectedFiles[i];
                const progress = Math.round(((i + 1) / totalFiles) * 100);
                
                if (progressText) progressText.textContent = `${progress}% (${i + 1}/${totalFiles})`;
                
                try {
                    const result = await this.uploadSingleFile(file);
                    uploadedFiles.push(result);
                } catch (error) {
                    console.error(`❌ 文件上传失败: ${file.name}`, error);
                    this.showMessage(`上传失败: ${file.name}`, 'error');
                }
            }
            
            this.uploadedFiles = uploadedFiles;
            
            // 触发上传成功回调
            if (this.options.onUpload) {
                this.options.onUpload(uploadedFiles);
            }
            
            this.showMessage(`成功上传 ${uploadedFiles.length} 张图片`, 'success');
            
            // 判断是否需要跳转到选择选项卡
            const isEditor = this.options.context === 'editor' || this.options.isEditor || this.options.maxFiles >= 999;
            const isSingleImage = this.options.maxFiles === 1;
            
            if (this.options.enableImageSelector && uploadedFiles.length > 0) {
                if (isEditor) {
                    // 编辑器环境：跳转到选择选项卡
                    setTimeout(() => {
                        this.switchMode('select');
                    }, 1000);
                } else if (isSingleImage) {
                    // 单张图片环境（如新闻、轮播图）：直接关闭弹窗
                    setTimeout(() => {
                        this.close();
                    }, 1000);
                } else {
                    // 其他多张图片环境：跳转到选择选项卡
                    setTimeout(() => {
                        this.switchMode('select');
                    }, 1000);
                }
            }
            
        } catch (error) {
            console.error('❌ 上传过程出错:', error);
            this.showMessage('上传过程出错', 'error');
            
            if (this.options.onError) {
                this.options.onError(error);
            }
        } finally {
            if (progressElement) progressElement.style.display = 'none';
            if (btnUpload) {
            btnUpload.disabled = false;
            btnUpload.innerHTML = '<i class="fas fa-upload"></i> 开始上传';
            }
        }
    }
    
    // 上传单个文件
    uploadSingleFile(file) {
        return new Promise((resolve, reject) => {
            const formData = new FormData();
            formData.append(this.options.uploadField, file);
            formData.append('action', 'upload_image');
            
            const xhr = new XMLHttpRequest();
            
            xhr.onload = () => {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.url) {
                            resolve({
                                file: file,
                                url: response.url,
                                response: response
                            });
                        } else {
                            reject(new Error(response.message || '上传失败'));
                        }
                    } catch (e) {
                        reject(new Error('响应解析失败'));
                    }
                } else {
                    reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                }
            };
            
            xhr.onerror = () => reject(new Error('网络错误'));
            xhr.ontimeout = () => reject(new Error('上传超时'));
            
            xhr.timeout = 30000; // 30秒超时
            xhr.open('POST', this.options.uploadUrl);
            xhr.send(formData);
        });
    }
    
    // 拖拽事件处理
    handleDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'copy';
    }
    
    handleDragEnter(e) {
        e.preventDefault();
        this.dragCounter++;
        e.currentTarget.classList.add('drag-over');
    }
    
    handleDragLeave(e) {
        e.preventDefault();
        this.dragCounter--;
        if (this.dragCounter === 0) {
            e.currentTarget.classList.remove('drag-over');
        }
    }
    
    handleDrop(e) {
        e.preventDefault();
        this.dragCounter = 0;
        e.currentTarget.classList.remove('drag-over');
        
        const files = Array.from(e.dataTransfer.files);
        this.addFiles(files);
    }
    
    // 显示消息
    showMessage(message, type = 'info') {
        // 移除现有的消息提示
        const existingToast = document.querySelector('.image-uploader-toast');
        if (existingToast) {
            existingToast.remove();
        }

        // 创建新的消息提示
        const toast = document.createElement('div');
        toast.className = `image-uploader-toast ${type}`;
        toast.textContent = message;
        
        // 添加样式
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            color: #fff;
            font-weight: 600;
            z-index: 10001;
            animation: slideInRight 0.3s ease-out;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            max-width: 300px;
            word-wrap: break-word;
        `;

        // 根据类型设置背景色
        if (type === 'success') {
            toast.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
        } else if (type === 'error') {
            toast.style.background = 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)';
        } else if (type === 'warning') {
            toast.style.background = 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)';
        } else {
            toast.style.background = 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)';
        }

        // 添加关闭按钮
        const closeBtn = document.createElement('span');
        closeBtn.innerHTML = '×';
        closeBtn.style.cssText = `
            position: absolute;
            top: 8px;
            right: 12px;
            cursor: pointer;
            font-size: 18px;
            line-height: 1;
            opacity: 0.8;
        `;
        closeBtn.onclick = () => {
            if (toast.parentNode) {
                toast.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
        }
                }, 300);
            }
        };
        toast.appendChild(closeBtn);

        document.body.appendChild(toast);

        // 添加滑入动画的CSS
        if (!document.getElementById('image-uploader-toast-styles')) {
            const style = document.createElement('style');
            style.id = 'image-uploader-toast-styles';
            style.textContent = `
                @keyframes slideInRight {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
                @keyframes slideOutRight {
                    from {
                        transform: translateX(0);
                        opacity: 1;
                    }
                    to {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }
        }, 3000);

        // 记录日志
        console.log(`📢 ${type.toUpperCase()}: ${message}`);
    }
    
    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    // 获取选中的文件
    getSelectedFiles() {
        return this.selectedFiles;
    }
    
    // 获取已上传的文件
    getUploadedFiles() {
        return this.uploadedFiles;
    }
    
    // 重置组件
    reset() {
        this.selectedFiles = [];
        this.uploadedFiles = [];
        this.selectedImages = [];
        this.updatePreview();
        this.updateUI();
        this.updateSelectorUI();
    }
    
    // 销毁组件
    destroy() {
        // 恢复页面滚动
        document.body.style.overflow = '';
        
        if (this.modal) {
            this.modal.remove();
        }
        this.selectedFiles = [];
        this.uploadedFiles = [];
    }
}

// 全局工厂函数
window.createImageUploader = function(options) {
    return new ImageUploader(options);
};

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ImageUploader;
} 
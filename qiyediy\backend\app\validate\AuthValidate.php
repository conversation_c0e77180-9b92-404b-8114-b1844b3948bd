<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 认证验证器
 */

declare(strict_types=1);

namespace app\validate;

use think\Validate;

/**
 * 认证验证器
 */
class AuthValidate extends Validate
{
    /**
     * 验证规则
     * @var array
     */
    protected $rule = [
        // 登录
        'username' => 'require|length:3,50',
        'password' => 'require|length:6,32',
        
        // 注册
        'email' => 'require|email|length:5,100',
        'real_name' => 'length:2,50',
        'phone' => 'mobile',
        'confirm_password' => 'require|confirm:password',
        
        // 修改密码
        'old_password' => 'require|length:6,32',
        'new_password' => 'require|length:6,32|different:old_password',
        'confirm_new_password' => 'require|confirm:new_password',
        
        // 重置密码
        'token' => 'require|length:32',
        
        // 验证码
        'code' => 'require|length:4,6|number',
        'type' => 'require|in:register,login,reset_password,bind_phone,change_phone',
        
        // 个人资料
        'avatar' => 'url'
    ];

    /**
     * 错误信息
     * @var array
     */
    protected $message = [
        'username.require' => '用户名不能为空',
        'username.length' => '用户名长度必须在3-50个字符之间',
        'password.require' => '密码不能为空',
        'password.length' => '密码长度必须在6-32个字符之间',
        'email.require' => '邮箱不能为空',
        'email.email' => '邮箱格式不正确',
        'email.length' => '邮箱长度必须在5-100个字符之间',
        'real_name.length' => '真实姓名长度必须在2-50个字符之间',
        'phone.mobile' => '手机号格式不正确',
        'confirm_password.require' => '确认密码不能为空',
        'confirm_password.confirm' => '两次输入的密码不一致',
        'old_password.require' => '原密码不能为空',
        'old_password.length' => '原密码长度必须在6-32个字符之间',
        'new_password.require' => '新密码不能为空',
        'new_password.length' => '新密码长度必须在6-32个字符之间',
        'new_password.different' => '新密码不能与原密码相同',
        'confirm_new_password.require' => '确认新密码不能为空',
        'confirm_new_password.confirm' => '两次输入的新密码不一致',
        'token.require' => '重置令牌不能为空',
        'token.length' => '重置令牌格式不正确',
        'code.require' => '验证码不能为空',
        'code.length' => '验证码长度必须在4-6位之间',
        'code.number' => '验证码必须为数字',
        'type.require' => '验证码类型不能为空',
        'type.in' => '验证码类型不正确',
        'avatar.url' => '头像地址格式不正确'
    ];

    /**
     * 验证场景
     * @var array
     */
    protected $scene = [
        // 登录场景
        'login' => ['username', 'password'],
        
        // 注册场景
        'register' => ['username', 'email', 'password', 'confirm_password', 'real_name', 'phone'],
        
        // 修改密码场景
        'changePassword' => ['old_password', 'new_password', 'confirm_new_password'],
        
        // 忘记密码场景
        'forgotPassword' => ['email'],
        
        // 重置密码场景
        'resetPassword' => ['token', 'password', 'confirm_password'],
        
        // 发送验证码场景
        'sendCode' => ['phone', 'type'],
        
        // 验证验证码场景
        'verifyCode' => ['phone', 'code', 'type'],
        
        // 绑定手机号场景
        'bindPhone' => ['phone', 'code'],
        
        // 更新个人资料场景
        'updateProfile' => ['real_name', 'avatar']
    ];

    /**
     * 自定义验证规则：检查用户名格式
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkUsername($value, $rule, array $data)
    {
        // 用户名只能包含字母、数字、下划线
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $value)) {
            return '用户名只能包含字母、数字和下划线';
        }
        
        // 用户名不能以数字开头
        if (is_numeric($value[0])) {
            return '用户名不能以数字开头';
        }
        
        return true;
    }

    /**
     * 自定义验证规则：检查密码强度
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkPasswordStrength($value, $rule, array $data)
    {
        // 密码必须包含字母和数字
        if (!preg_match('/^(?=.*[a-zA-Z])(?=.*\d)/', $value)) {
            return '密码必须包含字母和数字';
        }
        
        // 检查是否包含特殊字符（可选）
        if ($rule === 'strong' && !preg_match('/[!@#$%^&*(),.?":{}|<>]/', $value)) {
            return '密码必须包含特殊字符';
        }
        
        return true;
    }

    /**
     * 自定义验证规则：检查手机号是否已存在
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkPhoneExists($value, $rule, array $data)
    {
        if (empty($value)) {
            return true;
        }
        
        $exists = \app\model\User::where('phone', $value)->exists();
        if ($exists) {
            return '手机号已存在';
        }
        
        return true;
    }

    /**
     * 自定义验证规则：检查邮箱是否已存在
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkEmailExists($value, $rule, array $data)
    {
        $exists = \app\model\User::where('email', $value)->exists();
        if ($exists) {
            return '邮箱已存在';
        }
        
        return true;
    }

    /**
     * 自定义验证规则：检查用户名是否已存在
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkUsernameExists($value, $rule, array $data)
    {
        $exists = \app\model\User::where('username', $value)->exists();
        if ($exists) {
            return '用户名已存在';
        }
        
        return true;
    }

    /**
     * 注册场景验证规则
     * @return AuthValidate
     */
    public function sceneRegister(): AuthValidate
    {
        return $this->only(['username', 'email', 'password', 'confirm_password', 'real_name', 'phone'])
                   ->append('username', 'checkUsername|checkUsernameExists')
                   ->append('email', 'checkEmailExists')
                   ->append('phone', 'checkPhoneExists')
                   ->append('password', 'checkPasswordStrength');
    }

    /**
     * 修改密码场景验证规则
     * @return AuthValidate
     */
    public function sceneChangePassword(): AuthValidate
    {
        return $this->only(['old_password', 'new_password', 'confirm_new_password'])
                   ->append('new_password', 'checkPasswordStrength');
    }

    /**
     * 重置密码场景验证规则
     * @return AuthValidate
     */
    public function sceneResetPassword(): AuthValidate
    {
        return $this->only(['token', 'password', 'confirm_password'])
                   ->append('password', 'checkPasswordStrength');
    }
}

<?php
/**
 * 三只鱼网络科技 | 韩总 | 2025-01-15
 * 文件描述：解决方案管理控制器 - ThinkPHP6企业级应用
 * 技术栈：PHP 8.0+ + ThinkPHP6 + MySQL + Redis
 * 版权所有：三只鱼网络科技有限公司
 */

namespace app\admin\controller;

use app\BaseController;
use app\model\Solution as SolutionModel;
use app\validate\SolutionValidate;
use app\service\FileSecurityService;
use app\validate\SecurityValidate;
use think\facade\Request;
use think\facade\Session;
use think\facade\Filesystem;
use think\exception\ValidateException;

/**
 * 解决方案管理控制器
 */
class Solutions extends BaseController
{
    /**
     * 解决方案列表页面
     */
    public function index()
    {
        // 获取操作类型和参数
        $action = Request::param('action', 'list');
        $id = Request::param('id', 0, 'intval');
        
        // 修复：如果URL中有参数但Request::param()获取不到，尝试从$_GET获取
        if (isset($_GET['action']) && $_GET['action'] !== $action) {
            $action = $_GET['action'];
        }
        if (isset($_GET['id']) && $_GET['id'] != $id) {
            $id = intval($_GET['id']);
        }
        
        // 设置分页查询参数
        $queryParams = [
            'action' => $action
        ];

        // 处理POST请求
        if (Request::isPost()) {
            return $this->handlePost();
        }

        // 处理删除操作
        if ($action === 'delete' && $id > 0) {
            return $this->delete($id);
        }

        // 获取编辑数据
        $editData = null;

        if ($action === 'edit' && $id > 0) {
            $editData = SolutionModel::find($id);
            if (!$editData) {
                Session::flash('message', '解决方案不存在');
                Session::flash('messageType', 'error');
                return redirect('/admin/solutions');
            }
        }

        // 获取数据
        $solutionsList = [];
        $totalItems = 0;

        if ($action === 'list') {
            // 分页参数
            $page = Request::param('page', 1, 'intval');
            $itemsPerPage = 5;

            // 获取解决方案列表
            $totalItems = SolutionModel::count();
            $solutionsList = SolutionModel::order('sort_order', 'desc')
                ->order('id', 'desc')
                ->paginate([
                    'list_rows' => $itemsPerPage,
                    'page' => $page,
                    'query' => $queryParams
                ]);
        }

        return view('admin/solutions', [
            'action' => $action,
            'solutionsList' => $solutionsList,
            'editData' => $editData,
            'totalItems' => $totalItems,
            'message' => Session::pull('message') ?: '',
            'messageType' => Session::pull('messageType') ?: 'info'
        ]);
    }

    /**
     * 处理POST请求
     */
    private function handlePost()
    {
        $action = Request::param('action');

        switch ($action) {
            case 'add':
            case 'edit':
                return $this->save();

            case 'upload_image':
                return $this->uploadEditorImage();

            case 'toggle_status':
                return $this->toggleStatus();

            default:
                return json(['success' => false, 'message' => '无效的操作']);
        }
    }

    /**
     * 保存解决方案
     */
    private function save()
    {
        $data = Request::param();
        $action = $data['action'];
        $id = $data['id'] ?? 0;

        // 安全过滤输入数据
        $validate = new SolutionValidate();
        $data = $validate->filterInput($data);

        // 数据安全验证 - 使用统一安全验证器
        $securityCheck = SecurityValidate::validateDataSecurity($data, [
            'name' => 'checkXss',
            'description' => 'checkSqlInjection|checkXss',
            'short_description' => 'checkXss',
            'features' => 'checkXss',
            'icon' => 'checkXss',
            'slug' => 'checkUsernameSafe',
        ]);

        if (!$securityCheck['valid']) {
            $errors = [];
            foreach ($securityCheck['errors'] as $field => $fieldErrors) {
                $errors[] = $field . ': ' . implode(', ', $fieldErrors);
            }
            Session::flash('message', '数据安全检查失败: ' . implode('; ', $errors));
            Session::flash('messageType', 'error');
            return redirect()->restore();
        }

        // 数据验证
        $scene = $action === 'add' ? 'add' : 'edit';
        try {
            $validate->scene($scene)->check($data);
        } catch (ValidateException $e) {
            Session::flash('message', $e->getError());
            Session::flash('messageType', 'error');
            return redirect()->restore();
        }

        // 处理slug
        $slug = $data['slug'] ?: SolutionModel::generateSlug($data['name']);
        if (!SolutionModel::checkSlugUnique($slug, $id)) {
            $slug = $slug . '-' . time();
        }

        // 处理图片上传
        $imagePath = '';
        $imageChanged = false;

        // 优先处理新的图片选择器传来的image_url
        if (!empty($data['image_url'])) {
            $imagePath = $data['image_url'];
            $imageChanged = true;
        } else {
            // 兼容传统文件上传
            $file = Request::file('image');
            if ($file) {
                // 文件安全验证 - 保持原有逻辑
                $fileValidation = $validate->validateFileUpload($file);
                if ($fileValidation !== true) {
                    Session::flash('message', $fileValidation);
                    Session::flash('messageType', 'error');
                    return redirect()->restore();
                }

                // 额外的深度安全检查（增强）
                $securityCheck = FileSecurityService::validateFile($file, [
                    'check_magic' => true,
                    'check_content' => true,
                    'strict_mode' => false, // 宽松模式，避免过度拦截
                ]);

                if (!$securityCheck['valid']) {
                    Session::flash('message', '文件安全检查失败：' . implode(', ', $securityCheck['errors']));
                    Session::flash('messageType', 'error');
                    return redirect()->restore();
                }

                try {
                    $savename = Filesystem::disk('public')->putFile('uploads/solutions', $file);
                    $imagePath = '/storage/' . $savename;
                    $imageChanged = true;
                } catch (\Exception $e) {
                    Session::flash('message', '图片上传失败：' . $e->getMessage());
                    Session::flash('messageType', 'error');
                    return redirect()->restore();
                }
            }
        }

        // 处理解决方案特性（JSON格式）
        $features = [];
        if (!empty($data['features'])) {
            $featuresArray = explode("\n", $data['features']);
            foreach ($featuresArray as $feature) {
                $feature = trim($feature);
                if (!empty($feature)) {
                    $features[] = $feature;
                }
            }
        }

        // 准备数据
        $saveData = [
            'name' => $data['name'],
            'slug' => $slug,
            'short_description' => $data['short_description'] ?? '',
            'description' => $data['description'],
            'features' => json_encode($features),
            'icon' => $data['icon'] ?? '',
            'sort_order' => $data['sort_order'] ?? 0,
            'status' => isset($data['status']) ? 1 : 0,
        ];

        // 只有在图片有变化时才更新图片字段
        if ($imageChanged && $imagePath) {
            $saveData['image'] = $imagePath;
        }

        try {
            if ($action === 'add') {
                $solution = SolutionModel::create($saveData);
                Session::flash('message', '解决方案添加成功');
            } else {
                $solution = SolutionModel::find($id);
                if (!$solution) {
                    Session::flash('message', '解决方案不存在');
                    Session::flash('messageType', 'error');
                    return redirect('/admin/solutions');
                }

                // 如果上传了新图片，删除旧图片
                if ($imageChanged && $imagePath && $solution->image && $solution->image !== $imagePath && strpos($solution->image, '/storage/') === 0) {
                    try {
                        $oldImagePath = str_replace('/storage/', '', $solution->image);
                        Filesystem::disk('public')->delete($oldImagePath);
                    } catch (\Exception $e) {
                        // 忽略删除失败
                    }
                }

                $solution->save($saveData);
                Session::flash('message', '解决方案更新成功');
            }

            Session::flash('messageType', 'success');
            return redirect('/admin/solutions');

        } catch (\Exception $e) {
            Session::flash('message', '操作失败：' . $e->getMessage());
            Session::flash('messageType', 'error');
            return redirect()->restore();
        }
    }

    /**
     * 删除解决方案
     */
    private function delete($id)
    {
        try {
            $solution = SolutionModel::find($id);
            if (!$solution) {
                Session::flash('message', '解决方案不存在');
                Session::flash('messageType', 'error');
                return redirect('/admin/solutions');
            }

            // 删除图片记录和文件
            if ($solution->image) {
                $this->deleteImageRecord($solution->image);
            }

            $solution->delete();

            Session::flash('message', '解决方案删除成功');
            Session::flash('messageType', 'success');

        } catch (\Exception $e) {
            Session::flash('message', '删除失败：' . $e->getMessage());
            Session::flash('messageType', 'error');
        }

        return redirect('/admin/solutions');
    }

    /**
     * 删除图片记录和文件
     */
    private function deleteImageRecord($imageUrl)
    {
        if (empty($imageUrl)) {
            return;
        }

        try {
            // 查找图片数据库记录
            $image = \app\model\Image::where('file_url', $imageUrl)
                ->whereOr('file_path', $imageUrl)
                ->find();

            if ($image) {
                // 删除数据库记录（模型事件会自动删除物理文件）
                $image->delete();
            } else {
                // 如果数据库中没有记录，尝试删除物理文件（兼容旧数据）
                $this->deletePhysicalFile($imageUrl);
            }
        } catch (\Exception $e) {
            // 记录错误但不影响主流程
            trace('删除图片失败：' . $e->getMessage(), 'error');
            // 尝试删除物理文件作为备用方案
            $this->deletePhysicalFile($imageUrl);
        }
    }

    /**
     * 删除物理文件（备用方案）
     */
    private function deletePhysicalFile($imageUrl)
    {
        try {
            if (strpos($imageUrl, '/storage/') === 0) {
                $imagePath = str_replace('/storage/', '', $imageUrl);
                Filesystem::disk('public')->delete($imagePath);
            } elseif (strpos($imageUrl, '/uploads/') === 0) {
                $filePath = public_path() . $imageUrl;
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }
        } catch (\Exception $e) {
            // 忽略删除失败
        }
    }

    /**
     * 编辑器图片上传 - 增强安全检查
     */
    private function uploadEditorImage()
    {
        $file = Request::file('upload');

        if (!$file) {
            return json(['error' => ['message' => '没有上传文件']]);
        }

        try {
            // 使用原有的验证逻辑
            $validate = new SolutionValidate();
            $fileValidation = $validate->validateFileUpload($file);
            if ($fileValidation !== true) {
                return json(['error' => ['message' => $fileValidation]]);
            }

            // 额外的深度安全检查
            $securityCheck = FileSecurityService::validateFile($file, [
                'check_magic' => true,
                'check_content' => true,
                'strict_mode' => false, // 宽松模式
            ]);

            if (!$securityCheck['valid']) {
                return json(['error' => ['message' => '文件安全检查失败：' . implode(', ', $securityCheck['errors'])]]);
            }

            $savename = Filesystem::disk('public')->putFile('uploads/solutions', $file);
            $url = '/storage/' . $savename;

            return json([
                'url' => $url
            ]);
        } catch (\Exception $e) {
            return json(['error' => ['message' => '上传失败：' . $e->getMessage()]]);
        }
    }

    /**
     * 切换状态
     */
    private function toggleStatus()
    {
        $id = Request::param('id', 0, 'intval');
        $status = Request::param('status', 0, 'intval');

        if ($id <= 0) {
            return json(['success' => false, 'message' => '参数错误']);
        }

        try {
            $solution = SolutionModel::find($id);
            if (!$solution) {
                return json(['success' => false, 'message' => '解决方案不存在']);
            }

            $solution->status = $status;
            $solution->save();

            return json(['success' => true, 'message' => '状态更新成功']);
        } catch (\Exception $e) {
            return json(['success' => false, 'message' => '操作失败：' . $e->getMessage()]);
        }
    }
}

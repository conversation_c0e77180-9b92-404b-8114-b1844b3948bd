/**
 * 团队介绍组件 (Team Component)
 * 专业的团队成员展示组件，支持成员信息、头像、职位、社交链接等展示
 *
 * @features
 * - 支持1-4列自适应布局
 * - 6种预设样式主题
 * - 成员卡片管理
 * - 响应式设计
 * - 社交链接支持
 */

// 预设头像库 - 使用更稳定的图片源
const teamAvatarLibrary = [
    'https://api.dicebear.com/7.x/avataaars/svg?seed=John&backgroundColor=b6e3f4&size=200',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=Jane&backgroundColor=c084fc&size=200',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=Mike&backgroundColor=34d399&size=200',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=Sarah&backgroundColor=fbbf24&size=200',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=David&backgroundColor=f87171&size=200',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=Lisa&backgroundColor=a78bfa&size=200'
];

// 团队样式预设
const teamStylePresets = [
    { 
        name: '商务专业', 
        bgColor: '#ffffff', 
        nameColor: '#2d3748', 
        positionColor: '#667eea', 
        bioColor: '#4a5568',
        cardBg: '#ffffff',
        cardShadow: true
    },
    { 
        name: '科技蓝调', 
        bgColor: '#1a202c', 
        nameColor: '#ffffff', 
        positionColor: '#4299e1', 
        bioColor: '#a0aec0',
        cardBg: '#2d3748',
        cardShadow: true
    },
    { 
        name: '温暖橙色', 
        bgColor: '#fff5f5', 
        nameColor: '#c53030', 
        positionColor: '#ed8936', 
        bioColor: '#2d3748',
        cardBg: '#ffffff',
        cardShadow: false
    },
    { 
        name: '自然绿色', 
        bgColor: '#f0fff4', 
        nameColor: '#22543d', 
        positionColor: '#38a169', 
        bioColor: '#2d3748',
        cardBg: '#ffffff',
        cardShadow: false
    },
    { 
        name: '优雅紫色', 
        bgColor: '#faf5ff', 
        nameColor: '#553c9a', 
        positionColor: '#805ad5', 
        bioColor: '#2d3748',
        cardBg: '#ffffff',
        cardShadow: false
    },
    { 
        name: '现代灰色', 
        bgColor: '#f7fafc', 
        nameColor: '#1a202c', 
        positionColor: '#718096', 
        bioColor: '#4a5568',
        cardBg: '#ffffff',
        cardShadow: true
    }
];

// 社交图标映射
const socialIcons = {
    wechat: '💬',
    weibo: '🐦',
    linkedin: '💼',
    email: '📧',
    phone: '📞',
    website: '🌐'
};

// 团队组件模板
const teamComponent = {
    name: '团队介绍',
    html: `<div class="team-container">
        <div class="team-member">
            <div class="member-avatar-container">
                <img class="member-avatar" src="${teamAvatarLibrary[0]}" alt="团队成员">
            </div>
            <div class="member-info">
                <h3 class="member-name">张三</h3>
                <p class="member-position">CEO & 创始人</p>
                <p class="member-bio">10年行业经验，专注产品创新和团队管理，致力于为客户提供最优质的服务。</p>
                <div class="member-social">
                    <span class="social-link" title="微信">💬</span>
                    <span class="social-link" title="邮箱">📧</span>
                </div>
            </div>
        </div>
    </div>`,
    render: function(component, properties) {
        updateTeamDisplay(component, properties);
    },
    properties: {
        // 团队数据
        members: [
            {
                name: '张三',
                position: 'CEO & 创始人',
                avatar: teamAvatarLibrary[0],
                selectedAvatarIndex: 0,
                bio: '10年行业经验，专注产品创新和团队管理，致力于为客户提供最优质的服务。',
                social: {
                    wechat: 'zhangsan_wx',
                    email: '<EMAIL>'
                }
            },
            {
                name: '李四',
                position: 'CTO & 技术总监',
                avatar: teamAvatarLibrary[1],
                selectedAvatarIndex: 1,
                bio: '15年技术开发经验，专注于系统架构设计和技术团队管理。',
                social: {
                    wechat: 'lisi_wx',
                    email: '<EMAIL>'
                }
            },
            {
                name: '王五',
                position: '产品经理',
                avatar: teamAvatarLibrary[2],
                selectedAvatarIndex: 2,
                bio: '8年产品设计经验，专注用户体验和产品创新，致力于打造优秀产品。',
                social: {
                    wechat: 'wangwu_wx',
                    email: '<EMAIL>'
                }
            }
        ],

        // 布局设置
        columnsCount: 3,
        cardStyle: 'modern',
        
        // 样式预设
        stylePreset: 'business-professional',
        bgColor: '#ffffff',
        bgRadius: 12,
        
        // 样式设置
        nameSize: 20,
        positionSize: 14,
        bioSize: 14,
        nameColor: '#2d3748',
        positionColor: '#667eea',
        bioColor: '#4a5568',
        
        // 卡片设置
        cardBg: '#ffffff',
        cardRadius: 12,
        cardShadow: true,
        cardPadding: 24,
        
        // 头像设置
        avatarSize: 120,
        avatarShape: 'circle',
        
        // 尺寸设置
        maxWidth: 1200,
        marginHorizontal: 20,
        marginVertical: 20,
        positionVertical: 0,
        
        // 间距设置
        memberSpacing: 24,
        
        // 文字对齐
        textAlign: 'center'
    }
};

// 生成团队组件属性面板 - 借鉴卡片组件的成熟设计模式
function generateTeamProperties(component) {
    // 获取或初始化组件属性
    let props;
    if (component._teamProperties) {
        props = component._teamProperties;
    } else {
        props = JSON.parse(JSON.stringify(teamComponent.properties));
        component._teamProperties = props;
    }

    let html = `
        <!-- 布局设置 -->
        <div class="property-section">
            <h4 class="section-title">布局设置</h4>

            <div class="property-group">
                <label class="property-label">列数布局</label>
                <div class="layout-buttons-clean">
                    <button type="button" class="layout-btn ${props.columnsCount === 1 ? 'active' : ''}"
                            onclick="updateTeamColumns('${component.id}', 1)">1列</button>
                    <button type="button" class="layout-btn ${props.columnsCount === 2 ? 'active' : ''}"
                            onclick="updateTeamColumns('${component.id}', 2)">2列</button>
                    <button type="button" class="layout-btn ${props.columnsCount === 3 ? 'active' : ''}"
                            onclick="updateTeamColumns('${component.id}', 3)">3列</button>
                    <button type="button" class="layout-btn ${props.columnsCount === 4 ? 'active' : ''}"
                            onclick="updateTeamColumns('${component.id}', 4)">4列</button>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">成员间距</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.memberSpacing}" min="12" max="48"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTeamPropertySmooth('${component.id}', 'memberSpacing', this.value)"
                           onchange="updateTeamProperty('${component.id}', 'memberSpacing', this.value)">
                    <span class="range-value">${props.memberSpacing}px</span>
                </div>
            </div>
        </div>

        <!-- 样式预设 -->
        <div class="property-section">
            <h4 class="section-title">样式预设</h4>

            <div class="property-group">
                <label class="property-label">快速样式</label>
                <div class="style-presets">
                    ${teamStylePresets.map((preset, index) => {
                        const presetKey = preset.name.toLowerCase().replace(/\s+/g, '-');
                        const isSelected = props.stylePreset === presetKey;
                        return `
                        <div class="style-preset ${isSelected ? 'selected' : ''}"
                             style="background: ${preset.cardBg}; color: ${preset.nameColor}; border: 2px solid ${preset.positionColor};"
                             onclick="applyTeamStylePreset('${component.id}', ${index})"
                             title="${preset.name}">
                            <span style="font-size: 12px; font-weight: bold;">${preset.name}</span>
                        </div>
                        `;
                    }).join('')}
                </div>
            </div>
        </div>

        <!-- 团队成员管理 -->
        <div class="property-section">
            <h4 class="section-title">团队成员 (${props.members.length}个)</h4>

            <div class="members-manager">
                ${props.members.map((member, index) => `
                    <div class="member-editor" data-index="${index}">
                        <div class="member-editor-header">
                            <span class="member-editor-title">成员 ${index + 1}</span>
                            <div class="member-editor-actions">
                                <button type="button" class="member-action-btn" onclick="toggleMemberEditor(this)">
                                    <span class="toggle-icon">▼</span>
                                </button>
                                ${props.members.length > 1 ? `
                                    <button type="button" class="member-action-btn delete" onclick="deleteMember('${component.id}', ${index})">
                                        ✕
                                    </button>
                                ` : ''}
                            </div>
                        </div>

                        <div class="member-editor-content">
                            <div class="property-group">
                                <label class="property-label">姓名</label>
                                <input type="text" class="property-input" value="${member.name}"
                                       onchange="updateMemberContent('${component.id}', ${index}, 'name', this.value)">
                            </div>

                            <div class="property-group">
                                <label class="property-label">职位</label>
                                <input type="text" class="property-input" value="${member.position}"
                                       onchange="updateMemberContent('${component.id}', ${index}, 'position', this.value)">
                            </div>

                            <div class="property-group">
                                <label class="property-label">个人简介</label>
                                <textarea class="property-input" rows="3"
                                          onchange="updateMemberContent('${component.id}', ${index}, 'bio', this.value)">${member.bio}</textarea>
                            </div>

                            <div class="property-group">
                                <label class="property-label">微信号</label>
                                <input type="text" class="property-input" value="${member.social?.wechat || ''}" placeholder="请输入微信号"
                                       onchange="updateMemberSocial('${component.id}', ${index}, 'wechat', this.value)">
                            </div>

                            <div class="property-group">
                                <label class="property-label">邮箱地址</label>
                                <input type="email" class="property-input" value="${member.social?.email || ''}" placeholder="请输入邮箱地址"
                                       onchange="updateMemberSocial('${component.id}', ${index}, 'email', this.value)">
                            </div>

                            <div class="property-group">
                                <label class="property-label">头像选择</label>
                                <div class="avatar-library-mini">
                                    ${teamAvatarLibrary.slice(0, 6).map((avatar, avatarIndex) => `
                                        <div class="avatar-option-mini ${member.selectedAvatarIndex === avatarIndex ? 'selected' : ''}"
                                             style="background-image: url('${avatar}')"
                                             onclick="updateMemberContent('${component.id}', ${index}, 'selectedAvatarIndex', ${avatarIndex})"
                                             title="头像 ${avatarIndex + 1}">
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>

            ${props.members.length < props.columnsCount ? `
                <button type="button" class="add-btn" onclick="addMember('${component.id}')">
                    + 添加成员
                </button>
            ` : ''}
        </div>
    `;

    // 添加颜色设置、尺寸设置等其他属性面板
    html += `
        <!-- 颜色设置 -->
        <div class="property-section">
            <h4 class="section-title">颜色设置</h4>

            <div class="property-group">
                <div class="color-row">
                    <div class="color-item">
                        <label>背景色</label>
                        <input type="color" class="property-input" value="${props.bgColor}"
                               onchange="updateTeamProperty('${component.id}', 'bgColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>姓名色</label>
                        <input type="color" class="property-input" value="${props.nameColor}"
                               onchange="updateTeamProperty('${component.id}', 'nameColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>职位色</label>
                        <input type="color" class="property-input" value="${props.positionColor}"
                               onchange="updateTeamProperty('${component.id}', 'positionColor', this.value)">
                    </div>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">背景圆角</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.bgRadius || 0}" min="0" max="50"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTeamPropertySmooth('${component.id}', 'bgRadius', this.value)"
                           onchange="updateTeamProperty('${component.id}', 'bgRadius', this.value)">
                    <span class="range-value">${props.bgRadius || 0}px</span>
                </div>
            </div>
        </div>

        <!-- 卡片设置 -->
        <div class="property-section">
            <h4 class="section-title">卡片设置</h4>

            <div class="property-group">
                <label class="property-label">卡片背景色</label>
                <div class="color-row">
                    <div class="color-item">
                        <input type="color" class="property-input" value="${props.cardBg}"
                               onchange="updateTeamProperty('${component.id}', 'cardBg', this.value)">
                    </div>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">圆角大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.cardRadius}" min="0" max="30"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTeamPropertySmooth('${component.id}', 'cardRadius', this.value)"
                           onchange="updateTeamProperty('${component.id}', 'cardRadius', this.value)">
                    <span class="range-value">${props.cardRadius}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">内边距</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.cardPadding}" min="12" max="48"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTeamPropertySmooth('${component.id}', 'cardPadding', this.value)"
                           onchange="updateTeamProperty('${component.id}', 'cardPadding', this.value)">
                    <span class="range-value">${props.cardPadding}px</span>
                </div>
            </div>

            <div class="property-group">
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" ${props.cardShadow ? 'checked' : ''}
                               onchange="updateTeamProperty('${component.id}', 'cardShadow', this.checked)">
                        <span>卡片阴影</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 头像设置 -->
        <div class="property-section">
            <h4 class="section-title">头像设置</h4>

            <div class="property-group">
                <label class="property-label">头像大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.avatarSize}" min="80" max="160"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTeamPropertySmooth('${component.id}', 'avatarSize', this.value)"
                           onchange="updateTeamProperty('${component.id}', 'avatarSize', this.value)">
                    <span class="range-value">${props.avatarSize}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">头像形状</label>
                <select class="property-input" onchange="updateTeamProperty('${component.id}', 'avatarShape', this.value)">
                    <option value="circle" ${props.avatarShape === 'circle' ? 'selected' : ''}>圆形</option>
                    <option value="square" ${props.avatarShape === 'square' ? 'selected' : ''}>方形</option>
                    <option value="rounded" ${props.avatarShape === 'rounded' ? 'selected' : ''}>圆角方形</option>
                </select>
            </div>
        </div>

        <!-- 字体设置 -->
        <div class="property-section">
            <h4 class="section-title">字体设置</h4>

            <div class="property-group">
                <label class="property-label">姓名大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.nameSize}" min="16" max="28"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTeamPropertySmooth('${component.id}', 'nameSize', this.value)"
                           onchange="updateTeamProperty('${component.id}', 'nameSize', this.value)">
                    <span class="range-value">${props.nameSize}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">职位大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.positionSize}" min="12" max="18"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTeamPropertySmooth('${component.id}', 'positionSize', this.value)"
                           onchange="updateTeamProperty('${component.id}', 'positionSize', this.value)">
                    <span class="range-value">${props.positionSize}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">简介大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.bioSize}" min="12" max="16"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTeamPropertySmooth('${component.id}', 'bioSize', this.value)"
                           onchange="updateTeamProperty('${component.id}', 'bioSize', this.value)">
                    <span class="range-value">${props.bioSize}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">文字对齐</label>
                <select class="property-input" onchange="updateTeamProperty('${component.id}', 'textAlign', this.value)">
                    <option value="left" ${props.textAlign === 'left' ? 'selected' : ''}>左对齐</option>
                    <option value="center" ${props.textAlign === 'center' ? 'selected' : ''}>居中对齐</option>
                    <option value="right" ${props.textAlign === 'right' ? 'selected' : ''}>右对齐</option>
                </select>
            </div>
        </div>

        <!-- 尺寸设置 -->
        <div class="property-section">
            <h4 class="section-title">尺寸设置</h4>

            <div class="property-group">
                <label class="property-label">最大宽度</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.maxWidth}" min="600" max="1800"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTeamPropertySmooth('${component.id}', 'maxWidth', this.value)"
                           onchange="updateTeamProperty('${component.id}', 'maxWidth', this.value)">
                    <span class="range-value">${props.maxWidth}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">左右外边距</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.marginHorizontal}" min="0" max="100"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTeamPropertySmooth('${component.id}', 'marginHorizontal', this.value)"
                           onchange="updateTeamProperty('${component.id}', 'marginHorizontal', this.value)">
                    <span class="range-value">${props.marginHorizontal}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">上下外边距</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.marginVertical}" min="0" max="100"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTeamPropertySmooth('${component.id}', 'marginVertical', this.value)"
                           onchange="updateTeamProperty('${component.id}', 'marginVertical', this.value)">
                    <span class="range-value">${props.marginVertical}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">上下位置调节</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.positionVertical}" min="-600" max="600"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTeamPropertySmooth('${component.id}', 'positionVertical', this.value)"
                           onchange="updateTeamProperty('${component.id}', 'positionVertical', this.value)">
                    <span class="range-value">${props.positionVertical}px</span>
                </div>
                <div class="range-tips">
                    <small>负值向上移动，正值向下移动。层级控制请使用组件上方的"上层"/"下层"按钮</small>
                </div>
            </div>
        </div>
    `;

    return html;
}

// 更新团队列数 - 参考卡片组件的实现模式
function updateTeamColumns(componentId, columns) {
    const component = document.getElementById(componentId);
    if (!component) return;

    // 获取或初始化组件属性
    let props;
    if (component._teamProperties) {
        props = component._teamProperties;
    } else {
        props = JSON.parse(JSON.stringify(teamComponent.properties));
        component._teamProperties = props;
    }

    props.columnsCount = columns;

    // 调整成员数量以匹配列数
    while (props.members.length < columns) {
        props.members.push({
            name: `成员${props.members.length + 1}`,
            position: '职位名称',
            avatar: teamAvatarLibrary[props.members.length % teamAvatarLibrary.length],
            selectedAvatarIndex: props.members.length % teamAvatarLibrary.length,
            bio: '这里是团队成员的个人简介，可以介绍工作经验、专业技能等。',
            social: {
                email: `member${props.members.length + 1}@company.com`
            }
        });
    }

    // 如果成员数量超过列数，保留前N个
    if (props.members.length > columns) {
        props.members = props.members.slice(0, columns);
    }

    updateTeamDisplay(component, props);
    updatePropertiesPanel(component);
}

// 添加团队成员
function addMember(componentId) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props;
    if (component._teamProperties) {
        props = component._teamProperties;
    } else {
        props = JSON.parse(JSON.stringify(teamComponent.properties));
        component._teamProperties = props;
    }

    if (props.members.length < props.columnsCount) {
        props.members.push({
            name: `成员${props.members.length + 1}`,
            position: '职位名称',
            avatar: teamAvatarLibrary[props.members.length % teamAvatarLibrary.length],
            selectedAvatarIndex: props.members.length % teamAvatarLibrary.length,
            bio: '这里是团队成员的个人简介，可以介绍工作经验、专业技能等。',
            social: {
                email: `member${props.members.length + 1}@company.com`
            }
        });

        updateTeamDisplay(component, props);
        updatePropertiesPanel(component);
    }
}

// 删除团队成员
function deleteMember(componentId, memberIndex) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props;
    if (component._teamProperties) {
        props = component._teamProperties;
    } else {
        props = JSON.parse(JSON.stringify(teamComponent.properties));
        component._teamProperties = props;
    }

    if (props.members.length > 1) {
        props.members.splice(memberIndex, 1);
        updateTeamDisplay(component, props);
        updatePropertiesPanel(component);
    }
}

// 更新成员社交信息
function updateMemberSocial(componentId, memberIndex, socialType, value) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props;
    if (component._teamProperties) {
        props = component._teamProperties;
    } else {
        props = JSON.parse(JSON.stringify(teamComponent.properties));
        component._teamProperties = props;
    }

    if (!props.members[memberIndex].social) {
        props.members[memberIndex].social = {};
    }

    props.members[memberIndex].social[socialType] = value;
    updateTeamDisplay(component, props);
}

// 更新单个成员内容
function updateMemberContent(componentId, memberIndex, property, value) {
    const component = document.getElementById(componentId);
    if (!component) return;

    // 获取组件的属性数据
    let props;
    if (component._teamProperties) {
        props = component._teamProperties;
    } else {
        // 初始化组件属性
        props = JSON.parse(JSON.stringify(teamComponent.properties));
        component._teamProperties = props;
    }

    if (props.members && props.members[memberIndex]) {
        if (property === 'selectedAvatarIndex') {
            props.members[memberIndex][property] = parseInt(value);
            props.members[memberIndex].avatar = teamAvatarLibrary[value];
        } else {
            props.members[memberIndex][property] = value;
        }

        updateTeamDisplay(component, props);
    }
}

// 切换成员编辑器展开/收起
function toggleMemberEditor(button) {
    const header = button.closest('.member-editor-header');
    const content = header.nextElementSibling;
    const icon = button.querySelector('.toggle-icon');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        icon.textContent = '▼';
    } else {
        content.style.display = 'none';
        icon.textContent = '▶';
    }
}

// 应用样式预设 - 参考卡片组件的实现
function applyTeamStylePreset(componentId, presetIndex) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props;
    if (component._teamProperties) {
        props = component._teamProperties;
    } else {
        props = JSON.parse(JSON.stringify(teamComponent.properties));
        component._teamProperties = props;
    }

    const preset = teamStylePresets[presetIndex];

    // 应用预设样式
    props.stylePreset = preset.name.toLowerCase().replace(/\s+/g, '-');
    props.bgColor = preset.bgColor;
    props.nameColor = preset.nameColor;
    props.positionColor = preset.positionColor;
    props.bioColor = preset.bioColor;
    props.cardBg = preset.cardBg;
    props.cardShadow = preset.cardShadow;

    updateTeamDisplay(component, props);
    updatePropertiesPanel(component);
}

// 更新团队属性 - 参考卡片组件的属性更新逻辑
function updateTeamProperty(componentId, property, value, skipPanelUpdate = false) {
    const component = document.getElementById(componentId);
    if (!component) return;

    // 获取或初始化组件属性
    let props;
    if (component._teamProperties) {
        props = component._teamProperties;
    } else {
        props = JSON.parse(JSON.stringify(teamComponent.properties));
        component._teamProperties = props;
    }

    // 类型转换处理 - 参考卡片组件的处理方式
    if (['maxWidth', 'cardPadding', 'cardRadius', 'avatarSize', 'nameSize', 'positionSize', 'bioSize', 'memberSpacing', 'marginHorizontal', 'marginVertical', 'positionVertical', 'bgRadius'].includes(property)) {
        props[property] = parseInt(value);
    } else if (property === 'cardShadow') {
        props[property] = value;
    } else {
        props[property] = value;
    }

    updateTeamDisplay(component, props);

    // 只有在不跳过面板更新时才更新面板
    if (!skipPanelUpdate) {
        updatePropertiesPanel(component);
    }
}

// 优化的拖动条更新函数 - 只更新显示，不重新渲染面板
function updateTeamPropertySmooth(componentId, property, value) {
    updateTeamProperty(componentId, property, value, true);
}

// 更新团队显示 - 核心显示更新函数
function updateTeamDisplay(component, props) {
    const container = component.querySelector('.team-container');
    if (!container) return;

    // 设置容器样式
    container.style.display = 'grid';
    container.style.gridTemplateColumns = `repeat(${props.columnsCount}, 1fr)`;
    container.style.gap = `${props.memberSpacing}px`;
    container.style.margin = `${props.marginVertical}px ${props.marginHorizontal}px`;
    container.style.maxWidth = `${props.maxWidth}px`;
    container.style.marginLeft = 'auto';
    container.style.marginRight = 'auto';
    container.style.backgroundColor = props.bgColor;
    container.style.borderRadius = `${props.bgRadius || 0}px`;
    container.style.padding = props.bgRadius > 0 ? '20px' : '20px';
    container.style.overflow = 'hidden';

    // 确保圆角效果可见
    if (props.bgRadius > 0) {
        container.style.border = '1px solid rgba(0,0,0,0.05)';
        container.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
    } else {
        container.style.border = 'none';
        container.style.boxShadow = 'none';
    }

    // 将transform应用到整个component-block，这样控制按钮也会跟着移动
    component.style.transform = `translateY(${props.positionVertical}px)`;
    component.style.position = 'relative';

    // 生成团队成员HTML
    container.innerHTML = props.members.map((member, memberIndex) => {
        // 生成社交链接
        const socialLinks = [];



        // 微信信息
        if (member.social?.wechat && member.social.wechat.trim()) {
            socialLinks.push(`
                <div class="social-item wechat-item">
                    <span class="social-icon">💬</span>
                    <span class="social-text">${member.social.wechat}</span>
                </div>
            `);
        }

        // 邮箱信息
        if (member.social?.email && member.social.email.trim()) {
            socialLinks.push(`
                <div class="social-item email-item">
                    <span class="social-icon">📧</span>
                    <span class="social-text">${member.social.email}</span>
                </div>
            `);
        }



        return `
            <div class="team-member">
                <div class="member-avatar-container">
                    <img class="member-avatar"
                         src="${member.avatar}"
                         alt="${member.name}"
                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjZjdmYWZjIi8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjgwIiByPSIzMCIgZmlsbD0iIzRhNTU2OCIvPgo8cGF0aCBkPSJNNjAgMTYwYzAtMjIuMDkgMTcuOTEtNDAgNDAtNDBzNDAgMTcuOTEgNDAgNDB2MjBINjB2LTIweiIgZmlsbD0iIzRhNTU2OCIvPgo8L3N2Zz4K'">
                </div>
                <div class="member-info">
                    <h3 class="member-name">${member.name}</h3>
                    <p class="member-position">${member.position}</p>
                    <p class="member-bio">${member.bio}</p>
                    ${socialLinks.length > 0 ? `<div class="member-social">${socialLinks.join('')}</div>` : ''}
                </div>
            </div>
        `;
    }).join('');

    // 应用样式到所有成员卡片
    const memberItems = container.querySelectorAll('.team-member');
    memberItems.forEach((memberItem, index) => {
        const member = props.members[index];

        // 基础卡片样式
        memberItem.style.backgroundColor = props.cardBg;
        memberItem.style.borderRadius = `${props.cardRadius}px`;
        memberItem.style.padding = `${props.cardPadding}px`;
        memberItem.style.textAlign = props.textAlign;
        memberItem.style.boxShadow = props.cardShadow ?
            '0 4px 20px rgba(0,0,0,0.1)' : 'none';
        memberItem.style.transition = 'all 0.3s ease';
        memberItem.style.overflow = 'hidden';

        // 头像样式
        const avatar = memberItem.querySelector('.member-avatar');
        if (avatar) {
            avatar.style.width = `${props.avatarSize}px`;
            avatar.style.height = `${props.avatarSize}px`;
            avatar.style.objectFit = 'cover';
            avatar.style.marginBottom = '16px';

            // 头像形状
            if (props.avatarShape === 'circle') {
                avatar.style.borderRadius = '50%';
            } else if (props.avatarShape === 'square') {
                avatar.style.borderRadius = '0';
            } else if (props.avatarShape === 'rounded') {
                avatar.style.borderRadius = '12px';
            }
        }

        // 姓名样式
        const name = memberItem.querySelector('.member-name');
        if (name) {
            name.style.color = props.nameColor;
            name.style.fontSize = `${props.nameSize}px`;
            name.style.marginBottom = '8px';
            name.style.fontWeight = '600';
            name.style.lineHeight = '1.3';
        }

        // 职位样式
        const position = memberItem.querySelector('.member-position');
        if (position) {
            position.style.color = props.positionColor;
            position.style.fontSize = `${props.positionSize}px`;
            position.style.marginBottom = '12px';
            position.style.fontWeight = '500';
        }

        // 简介样式
        const bio = memberItem.querySelector('.member-bio');
        if (bio) {
            bio.style.color = props.bioColor;
            bio.style.fontSize = `${props.bioSize}px`;
            bio.style.lineHeight = '1.6';
            bio.style.marginBottom = '16px';
            bio.style.opacity = '0.8';
        }

        // 社交信息样式 - 横向排列设计
        const socialContainer = memberItem.querySelector('.member-social');
        if (socialContainer) {
            socialContainer.style.marginTop = '18px';
            socialContainer.style.display = 'flex';
            socialContainer.style.flexWrap = 'wrap';
            socialContainer.style.gap = '8px';
            socialContainer.style.justifyContent = 'center';
        }
    });
}

// 团队组件不需要弹窗功能，只显示联系信息

// 注册团队组件
if (typeof ComponentManager !== 'undefined') {
    ComponentManager.register('team', teamComponent, generateTeamProperties, updateTeamDisplay);
}

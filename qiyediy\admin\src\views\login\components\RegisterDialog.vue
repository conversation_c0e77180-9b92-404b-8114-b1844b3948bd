<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 注册对话框
-->

<template>
  <el-dialog
    v-model="visible"
    title="用户注册"
    width="480px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form
      ref="registerFormRef"
      :model="registerForm"
      :rules="registerRules"
      label-width="80px"
      size="large"
    >
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="registerForm.username"
          placeholder="请输入用户名"
          clearable
          :disabled="loading"
        />
      </el-form-item>

      <el-form-item label="邮箱" prop="email">
        <el-input
          v-model="registerForm.email"
          placeholder="请输入邮箱地址"
          clearable
          :disabled="loading"
        />
      </el-form-item>

      <el-form-item label="密码" prop="password">
        <el-input
          v-model="registerForm.password"
          type="password"
          placeholder="请输入密码"
          show-password
          clearable
          :disabled="loading"
        />
      </el-form-item>

      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input
          v-model="registerForm.confirmPassword"
          type="password"
          placeholder="请再次输入密码"
          show-password
          clearable
          :disabled="loading"
        />
      </el-form-item>

      <el-form-item label="真实姓名" prop="realName">
        <el-input
          v-model="registerForm.realName"
          placeholder="请输入真实姓名"
          clearable
          :disabled="loading"
        />
      </el-form-item>

      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="registerForm.phone"
          placeholder="请输入手机号"
          clearable
          :disabled="loading"
        />
      </el-form-item>

      <el-form-item>
        <el-checkbox v-model="registerForm.agree" :disabled="loading">
          我已阅读并同意
          <el-link type="primary" @click="showTerms">《用户协议》</el-link>
          和
          <el-link type="primary" @click="showPrivacy">《隐私政策》</el-link>
        </el-checkbox>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="loading">取消</el-button>
        <el-button type="primary" @click="handleRegister" :loading="loading">
          {{ loading ? '注册中...' : '注册' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from 'vue'
import { useUserStore } from '@/store/modules/user'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const userStore = useUserStore()

// 表单引用
const registerFormRef = ref<FormInstance>()

// 状态
const loading = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 注册表单
const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  realName: '',
  phone: '',
  agree: false
})

// 验证规则
const registerRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 32, message: '密码长度在 6 到 32 个字符', trigger: 'blur' },
    { 
      pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, 
      message: '密码必须包含字母和数字', 
      trigger: 'blur' 
    }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== registerForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  realName: [
    { min: 2, max: 50, message: '真实姓名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

/**
 * 处理注册
 */
const handleRegister = async () => {
  if (!registerFormRef.value) return

  try {
    await registerFormRef.value.validate()

    if (!registerForm.agree) {
      ElMessage.warning('请先阅读并同意用户协议和隐私政策')
      return
    }

    loading.value = true

    await userStore.register({
      username: registerForm.username,
      email: registerForm.email,
      password: registerForm.password,
      confirm_password: registerForm.confirmPassword,
      real_name: registerForm.realName,
      phone: registerForm.phone
    })

    emit('success')
    resetForm()

  } catch (error) {
    console.error('注册失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 处理关闭
 */
const handleClose = () => {
  if (loading.value) return
  visible.value = false
  resetForm()
}

/**
 * 重置表单
 */
const resetForm = () => {
  registerFormRef.value?.resetFields()
  Object.assign(registerForm, {
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    realName: '',
    phone: '',
    agree: false
  })
}

/**
 * 显示用户协议
 */
const showTerms = () => {
  ElMessageBox.alert(
    '这里是用户协议内容...',
    '用户协议',
    {
      confirmButtonText: '我知道了',
      type: 'info'
    }
  )
}

/**
 * 显示隐私政策
 */
const showPrivacy = () => {
  ElMessageBox.alert(
    '这里是隐私政策内容...',
    '隐私政策',
    {
      confirmButtonText: '我知道了',
      type: 'info'
    }
  )
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog) {
  border-radius: 12px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item) {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

:deep(.el-input) {
  .el-input__wrapper {
    border-radius: 6px;
  }
}

:deep(.el-checkbox) {
  .el-checkbox__label {
    font-size: 14px;
    line-height: 1.5;
  }
}
</style>

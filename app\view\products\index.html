{include file="common/header"}

<!-- 产品页面样式 -->
<link rel="stylesheet" href="{:asset('assets/css/products.css')}?v=7.1">

<!-- 产品页面头部横幅 -->
<section class="products-hero page-top-spacing">
    <div class="container">
        <div class="hero-content">
            <div class="hero-badge">
                <i data-lucide="package"></i>
                {if condition="$currentCategory"}
                    {$currentCategory.name}
                {else /}
                    产品中心
                {/if}
            </div>
            <h1 class="hero-title">
                {if condition="$currentCategory"}
                    {$currentCategory.name}
                {else /}
                    核心产品与解决方案
                {/if}
            </h1>
            <p class="hero-subtitle">
                {if condition="$currentCategory && $currentCategory.description"}
                    {$currentCategory.description}
                {else /}
                    专业的企业级产品和技术解决方案，助力企业数字化转型升级
                {/if}
            </p>
            <div class="hero-stats">
                <div class="hero-stat">
                    <span class="hero-stat-number">{$products->total()}</span>
                    <span class="hero-stat-label">产品总数</span>
                </div>
                <div class="hero-stat">
                    <span class="hero-stat-number">{:count($categories)}</span>
                    <span class="hero-stat-label">产品分类</span>
                </div>
                <div class="hero-stat">
                    <span class="hero-stat-number">{:count($featuredProducts)}</span>
                    <span class="hero-stat-label">推荐产品</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 主要内容区域 -->
<section class="products-main">
    <div class="container">
        <div class="products-container">
            <!-- 侧边栏筛选 -->
            <aside class="products-sidebar">
                <div class="filter-card">
                    <!-- 分类筛选 -->
                    <div class="filter-section">
                        <h5 class="filter-title">
                            <i data-lucide="grid-3x3"></i>
                            产品分类
                        </h5>
                        <div class="filter-options">
                            <a href="/products" class="filter-option {if condition='!$currentCategory'}active{/if}">
                                <i data-lucide="layers"></i>
                                全部产品
                            </a>
                            {volist name="categories" id="category"}
                            <a href="/products?category={$category.slug}" 
                               class="filter-option {if condition='$currentCategory && $currentCategory.id == $category.id'}active{/if}">
                                {if condition="$category.icon"}
                                    <i class="{$category.icon}"></i>
                                {else /}
                                    <i data-lucide="tag"></i>
                                {/if}
                                {$category.name}
                            </a>
                            {/volist}
                        </div>
                    </div>

                    <!-- 推荐产品 -->
                    {if condition="$featuredProducts && count($featuredProducts) > 0"}
                    <div class="featured-section">
                        <h5 class="filter-title">
                            <i data-lucide="star"></i>
                            推荐产品
                        </h5>
                        <div class="featured-products">
                            {volist name="featuredProducts" id="featured" key="k"}
                            {if condition="$k <= 4"}
                            <a href="/products/{$featured.slug ?: $featured.id}" class="featured-item">
                                <div class="featured-image">
                                    {if condition="$featured.image"}
                                        <img src="{$featured.image}" alt="{$featured.name}">
                                    {else /}
                                        <div class="no-image">
                                            <i data-lucide="package"></i>
                                        </div>
                                    {/if}
                                </div>
                                <div class="featured-info">
                                    <div class="featured-name">{$featured.name}</div>
                                    {if condition="$featured.price > 0"}
                                        <div class="featured-price">¥{$featured.price}</div>
                                    {/if}
                                </div>
                            </a>
                            {/if}
                            {/volist}
                        </div>
                    </div>
                    {/if}
                </div>
            </aside>

            <!-- 产品内容区域 -->
            <main class="products-content">
                <!-- 工具栏 -->
                <div class="products-toolbar">
                    <div class="toolbar-left">
                        <div class="results-info">
                            找到 <span class="results-count">{$products->total()}</span> 个产品
                        </div>
                        <form class="search-form" method="get" action="/products/search">
                            <i data-lucide="search" class="search-icon"></i>
                            <input type="text" name="keyword" class="search-input" 
                                   placeholder="搜索产品名称、描述..." 
                                   value="{$Request.param.keyword|default=''}">
                            {if condition="$currentCategory"}
                                <input type="hidden" name="category_id" value="{$currentCategory.id}">
                            {/if}
                            <button type="submit" class="search-btn"><i data-lucide="search"></i></button>
                        </form>
                    </div>
                    <div class="toolbar-right">
                        <div class="sort-select-wrapper">
                            <i data-lucide="arrow-up-down" class="sort-icon"></i>
                            <select class="sort-select" id="sortSelect">
                                <option value="/products{if condition='$currentCategory'}?category={$currentCategory.slug}{/if}" {if condition='!$Request.param.sort'}selected{/if}>默认排序</option>
                                <option value="/products{if condition='$currentCategory'}?category={$currentCategory.slug}&{else /}?{/if}sort=price_asc" {if condition='$Request.param.sort == "price_asc"'}selected{/if}>价格：从高到低</option>
                                <option value="/products{if condition='$currentCategory'}?category={$currentCategory.slug}&{else /}?{/if}sort=price_desc" {if condition='$Request.param.sort == "price_desc"'}selected{/if}>价格：从低到高</option>
                                <option value="/products{if condition='$currentCategory'}?category={$currentCategory.slug}&{else /}?{/if}sort=views_desc" {if condition='$Request.param.sort == "views_desc"'}selected{/if}>热度排序</option>
                                <option value="/products{if condition='$currentCategory'}?category={$currentCategory.slug}&{else /}?{/if}sort=created_desc" {if condition='$Request.param.sort == "created_desc"'}selected{/if}>最新发布</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 产品网格 -->
                {if condition="$products && count($products) > 0"}
                    <div class="products-page-grid">
                        {volist name="products" id="product"}
                        <article class="products-page-card">
                            <!-- 产品图片 -->
                            <div class="products-page-image">
                                <a href="/products/{$product.slug ?: $product.id}">
                                    {if condition="$product.image"}
                                        <img src="{$product.image}" alt="{$product.name}" loading="lazy">
                                    {else /}
                                        <div class="no-image">
                                            <i data-lucide="package"></i>
                                        </div>
                                    {/if}
                                </a>
                                
                                <!-- 产品标识 -->
                                <div class="products-page-badges">
                                    {if condition="$product.is_featured"}
                                        <span class="products-page-badge badge-featured">推荐</span>
                                    {/if}
                                    {if condition="$product.is_hot"}
                                        <span class="products-page-badge badge-hot">热门</span>
                                    {/if}
                                    {if condition="$product.is_new"}
                                        <span class="products-page-badge badge-new">新品</span>
                                    {/if}
                                </div>
                            </div>

                            <!-- 产品信息 -->
                            <div class="products-page-info">
                                {if condition="$product.category"}
                                    <a href="/products?category={$product.category.slug}" class="products-page-category">
                                        {$product.category.name}
                                    </a>
                                {/if}
                                
                                <h3 class="products-page-name">
                                    <a href="/products/{$product.slug ?: $product.id}">
                                        {$product.name}
                                    </a>
                                </h3>
                                
                                {if condition="$product.short_description"}
                                    <p class="products-page-description">{$product.short_description}</p>
                                {/if}
                            </div>

                            <!-- 产品底部信息 -->
                            <div class="products-page-footer">
                                <div class="products-page-meta">
                                    <div class="products-page-price">
                                        {if condition="$product.price > 0"}
                                            <span class="current-price">¥{$product.price}</span>
                                            {if condition="$product.original_price > $product.price"}
                                                <span class="original-price">¥{$product.original_price}</span>
                                            {/if}
                                        {else /}
                                            <span class="current-price">面议</span>
                                        {/if}
                                    </div>
                                    <div class="products-page-stats">
                                        <div class="stat-item">
                                            <i data-lucide="eye"></i>
                                            <span>{$product.views|default=0}</span>
                                        </div>
                                        <div class="stat-item">
                                            <i data-lucide="heart"></i>
                                            <span>{$product.likes|default=0}</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="products-page-actions">
                                    <a href="/products/{$product.slug ?: $product.id}" class="btn-primary-outline">
                                        <i data-lucide="eye"></i>
                                        查看详情
                                    </a>
                                    <a href="/contact?product={$product.id}" class="btn-secondary-outline" title="咨询产品">
                                        <i data-lucide="message-circle"></i>
                                    </a>
                                </div>
                            </div>
                        </article>
                        {/volist}
                    </div>

                    <!-- 分页 -->
                    {if condition="$products->hasPages()"}
                        <div class="pagination-wrapper">
                            {$products->render()}
                        </div>
                    {/if}
                {else /}
                    <!-- 空状态 -->
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i data-lucide="package-x"></i>
                        </div>
                        <h3 class="empty-title">暂无产品</h3>
                        <p class="empty-description">
                            {if condition="$currentCategory"}
                                该分类下暂时没有产品，请查看其他分类或联系我们了解更多信息。
                            {else /}
                                我们正在努力添加更多优质产品，敬请期待！
                            {/if}
                        </p>
                        <a href="/contact" class="empty-action">
                            <i data-lucide="phone"></i>
                            联系我们
                        </a>
                    </div>
                {/if}
            </main>
        </div>
    </div>
</section>

<!-- 页面特定的JavaScript -->
<script>
// 确保页面加载时头部状态正确
document.addEventListener('DOMContentLoaded', function() {
    // 检查页面滚动位置并设置头部状态
    const header = document.querySelector('header');
    if (header && window.scrollY > 50) {
        header.classList.add('scrolled');
    }

    // 初始化Lucide图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // 初始化分类无痕刷新
    initCategoryNavigation();

    // 初始化排序无痕刷新
    initSortNavigation();
});

// 监听滚动事件，确保头部样式正确
window.addEventListener('scroll', function() {
    const header = document.querySelector('header');
    if (header) {
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    }
});

// 分类导航无痕刷新功能
function initCategoryNavigation() {
    const filterOptions = document.querySelectorAll('.filter-option');

    filterOptions.forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const url = this.getAttribute('href');
            const currentActive = document.querySelector('.filter-option.active');

            // 如果点击的是当前激活的分类，不做任何操作
            if (this.classList.contains('active')) {
                return;
            }

            // 添加加载状态
            this.style.opacity = '0.7';
            this.style.pointerEvents = 'none';

            // 显示加载指示器
            showLoadingIndicator();

            // 使用fetch进行无痕刷新
            fetch(url, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.text())
            .then(html => {
                // 更新产品内容
                updateProductContent(html, false);

                // 更新分类激活状态
                if (currentActive) {
                    currentActive.classList.remove('active');
                }
                this.classList.add('active');

                // 更新浏览器URL
                window.history.pushState({}, '', url);

                // 滚动到产品区域
                const productsContent = document.querySelector('.products-content');
                if (productsContent) {
                    productsContent.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            })
            .catch(error => {
                console.error('加载失败:', error);
                // 如果AJAX失败，回退到正常页面跳转
                window.location.href = url;
            })
            .finally(() => {
                // 恢复链接状态
                this.style.opacity = '';
                this.style.pointerEvents = '';

                // 隐藏加载指示器
                hideLoadingIndicator();
            });
        });
    });
}

// 显示加载指示器
function showLoadingIndicator() {
    const productsGrid = document.querySelector('.products-page-grid');
    if (productsGrid) {
        productsGrid.style.opacity = '0.5';
        productsGrid.style.pointerEvents = 'none';
    }

    // 添加加载动画
    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'loading-overlay';
    loadingDiv.innerHTML = `
        <div class="loading-spinner">
            <i data-lucide="loader-2"></i>
            <span>加载中...</span>
        </div>
    `;

    const productsContent = document.querySelector('.products-content');
    if (productsContent) {
        productsContent.appendChild(loadingDiv);

        // 初始化加载图标
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }
}

// 隐藏加载指示器
function hideLoadingIndicator() {
    const productsGrid = document.querySelector('.products-page-grid');
    if (productsGrid) {
        productsGrid.style.opacity = '';
        productsGrid.style.pointerEvents = '';
    }

    const loadingOverlay = document.querySelector('.loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.remove();
    }
}

// 排序无痕刷新功能
function initSortNavigation() {
    const sortSelect = document.getElementById('sortSelect');

    if (sortSelect) {
        sortSelect.addEventListener('change', function(e) {
            const url = this.value;
            const sortWrapper = this.closest('.sort-select-wrapper');

            // 添加加载状态
            if (sortWrapper) {
                sortWrapper.classList.add('loading');
            }
            this.disabled = true;

            // 显示加载指示器
            showLoadingIndicator();

            // 使用fetch进行无痕刷新
            fetch(url, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.text())
            .then(html => {
                // 更新产品内容（带过渡效果）
                updateProductContent(html, true);

                // 更新浏览器URL
                window.history.pushState({}, '', url);

                // 滚动到产品区域
                const productsContent = document.querySelector('.products-content');
                if (productsContent) {
                    productsContent.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            })
            .catch(error => {
                console.error('排序加载失败:', error);
                // 如果AJAX失败，回退到正常页面跳转
                window.location.href = url;
            })
            .finally(() => {
                // 恢复选择器状态
                const sortWrapper = this.closest('.sort-select-wrapper');
                if (sortWrapper) {
                    sortWrapper.classList.remove('loading');
                }
                this.disabled = false;

                // 隐藏加载指示器
                hideLoadingIndicator();
            });
        });
    }
}

// 通用的内容更新函数
function updateProductContent(html, showTransition = false) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    // 更新产品网格
    const newProductsGrid = doc.querySelector('.products-page-grid');
    const currentProductsGrid = document.querySelector('.products-page-grid');

    if (newProductsGrid && currentProductsGrid) {
        if (showTransition) {
            currentProductsGrid.style.transition = 'opacity 0.3s ease';
            currentProductsGrid.style.opacity = '0';

            setTimeout(() => {
                currentProductsGrid.innerHTML = newProductsGrid.innerHTML;
                currentProductsGrid.style.opacity = '1';
            }, 150);
        } else {
            currentProductsGrid.innerHTML = newProductsGrid.innerHTML;
        }
    }

    // 更新结果信息
    const newResultsInfo = doc.querySelector('.results-info');
    const currentResultsInfo = document.querySelector('.results-info');

    if (newResultsInfo && currentResultsInfo) {
        currentResultsInfo.innerHTML = newResultsInfo.innerHTML;
    }

    // 更新分页
    const newPagination = doc.querySelector('.pagination-wrapper');
    const currentPagination = document.querySelector('.pagination-wrapper');

    if (newPagination && currentPagination) {
        currentPagination.innerHTML = newPagination.innerHTML;
    } else if (!newPagination && currentPagination) {
        currentPagination.style.display = 'none';
    } else if (newPagination && !currentPagination) {
        const productsContent = document.querySelector('.products-content');
        if (productsContent) {
            productsContent.appendChild(newPagination);
        }
    }

    // 重新初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}
</script>

{include file="common/footer"}

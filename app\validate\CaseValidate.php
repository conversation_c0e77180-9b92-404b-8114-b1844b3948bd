<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * 案例验证器 - ThinkPHP6企业级应用
 * 功能：案例数据验证和安全过滤
 */

namespace app\validate;

class CaseValidate extends BaseValidate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'title' => 'require|length:1,200|checkSql',
        'slug' => 'require|length:1,200|alphaNum|checkSql',
        'client_name' => 'require|length:1,100|checkSql',
        'industry' => 'length:0,50|checkSql',
        'summary' => 'length:0,500|checkSql',
        'description' => 'checkSql',
        'project_url' => 'url|checkSql',
        'completion_date' => 'date',
        'sort_order' => 'integer|egt:0',
        'status' => 'in:0,1',
        'is_featured' => 'in:0,1',
        'image_url' => 'url|checkSql',
        'gallery' => 'checkSql',
    ];
    
    /**
     * 验证消息
     */
    protected $message = [
        'title.require' => '案例标题不能为空',
        'title.length' => '案例标题长度不能超过200个字符',
        'title.checkSql' => '案例标题包含非法字符',
        'slug.require' => '案例别名不能为空',
        'slug.length' => '案例别名长度不能超过200个字符',
        'slug.alphaNum' => '案例别名只能包含字母和数字',
        'slug.checkSql' => '案例别名包含非法字符',
        'client_name.require' => '客户名称不能为空',
        'client_name.length' => '客户名称长度不能超过100个字符',
        'client_name.checkSql' => '客户名称包含非法字符',
        'industry.length' => '行业长度不能超过50个字符',
        'industry.checkSql' => '行业包含非法字符',
        'summary.length' => '案例摘要长度不能超过500个字符',
        'summary.checkSql' => '案例摘要包含非法字符',
        'description.checkSql' => '案例描述包含非法字符',
        'project_url.url' => '项目URL格式无效',
        'project_url.checkSql' => '项目URL包含非法字符',
        'completion_date.date' => '完成日期格式无效',
        'sort_order.integer' => '排序必须为整数',
        'sort_order.egt' => '排序必须大于等于0',
        'status.in' => '状态值无效',
        'is_featured.in' => '推荐状态值无效',
        'image_url.url' => '图片URL格式无效',
        'image_url.checkSql' => '图片URL包含非法字符',
        'gallery.checkSql' => '图片集包含非法字符',
    ];
    
    /**
     * 验证场景
     */
    protected $scene = [
        'add' => ['title', 'client_name', 'industry', 'summary', 'description', 'project_url', 'completion_date', 'sort_order', 'status', 'is_featured', 'image_url', 'gallery'],
        'edit' => ['title', 'client_name', 'industry', 'summary', 'description', 'project_url', 'completion_date', 'sort_order', 'status', 'is_featured', 'image_url', 'gallery'],
    ];
    
    /**
     * 自定义验证规则：SQL注入检查
     */
    protected function checkSql($value, $rule, $data = [])
    {
        if (!$this->checkSqlInjection($value)) {
            return '输入内容包含非法字符';
        }
        return true;
    }
    
    /**
     * 验证文件上传
     */
    public function validateFileUpload($file)
    {
        if (!$file) {
            return '请选择要上传的文件';
        }
        
        // 检查文件大小（5MB限制）
        if ($file->getSize() > 5 * 1024 * 1024) {
            return '文件大小不能超过5MB';
        }
        
        // 检查文件扩展名
        if (!$this->checkFileExtension($file->getOriginalName())) {
            return '只允许上传jpg、jpeg、png、gif、webp格式的图片';
        }
        
        // 检查MIME类型
        if (!$this->checkImageMimeType($file->getMime())) {
            return '文件类型不正确，只允许上传图片文件';
        }
        
        // 检查文件名安全性
        if (!$this->checkFilename($file->getOriginalName())) {
            return '文件名包含非法字符';
        }
        
        return true;
    }
}

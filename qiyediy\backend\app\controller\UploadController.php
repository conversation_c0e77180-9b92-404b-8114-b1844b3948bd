<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * QiyeDIY企业建站系统 - 文件上传控制器
 */

declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\service\UploadService;
use app\validate\UploadValidate;
use think\Response;
use think\facade\Request;

/**
 * 文件上传控制器
 */
class UploadController extends BaseController
{
    protected UploadService $uploadService;

    public function __construct()
    {
        parent::__construct();
        $this->uploadService = new UploadService();
    }

    /**
     * 上传图片
     * @return Response
     */
    public function image(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('media.upload')) {
                return $this->error('没有权限上传文件', 403);
            }

            $file = Request::file('file');
            if (!$file) {
                return $this->error('请选择要上传的文件');
            }

            // 验证文件
            $this->validate(['file' => $file], UploadValidate::class . '.image');

            // 上传文件
            $result = $this->uploadService->uploadImage($file);

            // 记录操作日志
            $this->logOperation('upload', 'image', null, [
                'filename' => $result['filename'],
                'size' => $result['size']
            ]);

            return $this->success($result, '图片上传成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 上传视频
     * @return Response
     */
    public function video(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('media.upload')) {
                return $this->error('没有权限上传文件', 403);
            }

            $file = Request::file('file');
            if (!$file) {
                return $this->error('请选择要上传的文件');
            }

            // 验证文件
            $this->validate(['file' => $file], UploadValidate::class . '.video');

            // 上传文件
            $result = $this->uploadService->uploadVideo($file);

            // 记录操作日志
            $this->logOperation('upload', 'video', null, [
                'filename' => $result['filename'],
                'size' => $result['size']
            ]);

            return $this->success($result, '视频上传成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 上传文档
     * @return Response
     */
    public function document(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('media.upload')) {
                return $this->error('没有权限上传文件', 403);
            }

            $file = Request::file('file');
            if (!$file) {
                return $this->error('请选择要上传的文件');
            }

            // 验证文件
            $this->validate(['file' => $file], UploadValidate::class . '.document');

            // 上传文件
            $result = $this->uploadService->uploadDocument($file);

            // 记录操作日志
            $this->logOperation('upload', 'document', null, [
                'filename' => $result['filename'],
                'size' => $result['size']
            ]);

            return $this->success($result, '文档上传成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 上传头像
     * @return Response
     */
    public function avatar(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('user.update')) {
                return $this->error('没有权限上传头像', 403);
            }

            $file = Request::file('file');
            if (!$file) {
                return $this->error('请选择要上传的文件');
            }

            // 验证文件
            $this->validate(['file' => $file], UploadValidate::class . '.avatar');

            // 上传文件
            $result = $this->uploadService->uploadAvatar($file);

            // 记录操作日志
            $this->logOperation('upload', 'avatar', null, [
                'filename' => $result['filename'],
                'size' => $result['size']
            ]);

            return $this->success($result, '头像上传成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 批量上传
     * @return Response
     */
    public function batch(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('media.upload')) {
                return $this->error('没有权限上传文件', 403);
            }

            $files = Request::file('files');
            if (!$files || !is_array($files)) {
                return $this->error('请选择要上传的文件');
            }

            // 批量上传
            $results = $this->uploadService->batchUpload($files);

            // 记录操作日志
            $this->logOperation('batch_upload', 'files', null, [
                'count' => count($results['success']),
                'total' => count($files)
            ]);

            return $this->success($results, '批量上传完成');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * Base64上传
     * @return Response
     */
    public function base64(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('media.upload')) {
                return $this->error('没有权限上传文件', 403);
            }

            $data = $this->post();

            // 验证数据
            $this->validate($data, UploadValidate::class . '.base64');

            // 上传文件
            $result = $this->uploadService->uploadBase64($data['data'], $data['type'] ?? 'image');

            // 记录操作日志
            $this->logOperation('upload', 'base64', null, [
                'filename' => $result['filename'],
                'size' => $result['size']
            ]);

            return $this->success($result, '文件上传成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取上传配置
     * @return Response
     */
    public function config(): Response
    {
        try {
            $config = $this->uploadService->getUploadConfig();
            return $this->success($config);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除文件
     * @return Response
     */
    public function delete(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('media.delete')) {
                return $this->error('没有权限删除文件', 403);
            }

            $data = $this->post();
            if (empty($data['path'])) {
                return $this->error('文件路径不能为空');
            }

            // 删除文件
            $result = $this->uploadService->deleteFile($data['path']);

            // 记录操作日志
            $this->logOperation('delete', 'file', null, ['path' => $data['path']]);

            return $this->success(['deleted' => $result], $result ? '文件删除成功' : '文件不存在');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取文件信息
     * @return Response
     */
    public function info(): Response
    {
        try {
            $path = $this->get('path');
            if (empty($path)) {
                return $this->error('文件路径不能为空');
            }

            $info = $this->uploadService->getFileInfo($path);
            return $this->success($info);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 生成缩略图
     * @return Response
     */
    public function thumbnail(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('media.view')) {
                return $this->error('没有权限访问', 403);
            }

            $data = $this->post();
            if (empty($data['path'])) {
                return $this->error('文件路径不能为空');
            }

            // 生成缩略图
            $result = $this->uploadService->generateThumbnail(
                $data['path'],
                $data['width'] ?? 200,
                $data['height'] ?? 200,
                $data['quality'] ?? 80
            );

            return $this->success($result, '缩略图生成成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 图片裁剪
     * @return Response
     */
    public function crop(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('media.edit')) {
                return $this->error('没有权限编辑文件', 403);
            }

            $data = $this->post();

            // 验证数据
            $this->validate($data, UploadValidate::class . '.crop');

            // 裁剪图片
            $result = $this->uploadService->cropImage(
                $data['path'],
                $data['x'],
                $data['y'],
                $data['width'],
                $data['height'],
                $data['quality'] ?? 80
            );

            // 记录操作日志
            $this->logOperation('crop', 'image', null, $data);

            return $this->success($result, '图片裁剪成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 图片压缩
     * @return Response
     */
    public function compress(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('media.edit')) {
                return $this->error('没有权限编辑文件', 403);
            }

            $data = $this->post();
            if (empty($data['path'])) {
                return $this->error('文件路径不能为空');
            }

            // 压缩图片
            $result = $this->uploadService->compressImage(
                $data['path'],
                $data['quality'] ?? 80,
                $data['max_width'] ?? null,
                $data['max_height'] ?? null
            );

            // 记录操作日志
            $this->logOperation('compress', 'image', null, $data);

            return $this->success($result, '图片压缩成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取上传统计
     * @return Response
     */
    public function statistics(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('media.view')) {
                return $this->error('没有权限访问', 403);
            }

            $stats = $this->uploadService->getUploadStatistics();
            return $this->success($stats);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}

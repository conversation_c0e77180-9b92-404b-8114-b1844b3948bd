# 应用配置
APP_DEBUG=true
APP_TRACE=false

# 数据库配置
DATABASE_TYPE=mysql
DATABASE_HOSTNAME=127.0.0.1
DATABASE_DATABASE=qiyediy
DATABASE_USERNAME=root
DATABASE_PASSWORD=
DATABASE_HOSTPORT=3306
DATABASE_CHARSET=utf8mb4
DATABASE_PREFIX=qd_

# Redis配置
REDIS_HOSTNAME=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_SELECT=0

# 缓存配置
CACHE_DRIVER=redis
CACHE_PREFIX=qiyediy:cache:

# 会话配置
SESSION_DRIVER=redis
SESSION_PREFIX=qiyediy:session:
SESSION_EXPIRE=7200

# 队列配置
QUEUE_DRIVER=redis
QUEUE_PREFIX=qiyediy:queue:

# JWT配置
JWT_SECRET=your-jwt-secret-key-here
JWT_TTL=7200
JWT_REFRESH_TTL=20160

# 文件存储配置
FILESYSTEM_DRIVER=local
# 阿里云OSS配置（可选）
ALIOSS_ACCESS_KEY_ID=
ALIOSS_ACCESS_KEY_SECRET=
ALIOSS_BUCKET=
ALIOSS_ENDPOINT=
ALIOSS_DOMAIN=

# 腾讯云COS配置（可选）
QCLOUD_SECRET_ID=
QCLOUD_SECRET_KEY=
QCLOUD_BUCKET=
QCLOUD_REGION=
QCLOUD_DOMAIN=

# 七牛云配置（可选）
QINIU_ACCESS_KEY=
QINIU_SECRET_KEY=
QINIU_BUCKET=
QINIU_DOMAIN=

# 邮件配置
MAIL_DRIVER=smtp
MAIL_HOST=smtp.qq.com
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=
MAIL_FROM_NAME=QiyeDIY

# 短信配置
SMS_DRIVER=aliyun
SMS_ACCESS_KEY_ID=
SMS_ACCESS_KEY_SECRET=
SMS_SIGN_NAME=
SMS_TEMPLATE_CODE=

# 微信配置
WECHAT_APP_ID=
WECHAT_APP_SECRET=
WECHAT_TOKEN=
WECHAT_AES_KEY=

# 支付配置
# 微信支付
WECHAT_PAY_APP_ID=
WECHAT_PAY_MCH_ID=
WECHAT_PAY_KEY=
WECHAT_PAY_CERT_PATH=
WECHAT_PAY_KEY_PATH=

# 支付宝
ALIPAY_APP_ID=
ALIPAY_PUBLIC_KEY=
ALIPAY_PRIVATE_KEY=
ALIPAY_ALIPAY_PUBLIC_KEY=

# 日志配置
LOG_CHANNEL=daily
LOG_LEVEL=debug
LOG_MAX_FILES=30

# API配置
API_RATE_LIMIT=1000
API_RATE_LIMIT_WINDOW=60

# 安全配置
SECURITY_SALT=your-security-salt-here
ENCRYPT_KEY=your-encrypt-key-here

# 跨域配置
CORS_ALLOWED_ORIGINS=*
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=*

# 监控配置
MONITOR_ENABLED=true
MONITOR_SLOW_QUERY_TIME=1000

# 开发工具配置
TELESCOPE_ENABLED=false
DEBUGBAR_ENABLED=false

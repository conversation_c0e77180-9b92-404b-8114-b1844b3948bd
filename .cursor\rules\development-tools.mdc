---
description: 
globs: 
alwaysApply: false
---
# 开发工具使用规范

## 🛠️ 项目工具集概述

### 工具目录结构
- [tools/](mdc:tools) - 项目开发工具集
  - [tools/tool_manager.php](mdc:tools/tool_manager.php) - 工具管理器
  - [tools/db_analyzer.php](mdc:tools/db_analyzer.php) - 数据库分析器
  - [tools/layout_analyzer.php](mdc:tools/layout_analyzer.php) - 布局分析器
  - [tools/performance_analyzer.php](mdc:tools/performance_analyzer.php) - 性能分析器
  - [tools/css_js_optimizer.php](mdc:tools/css_js_optimizer.php) - CSS/JS优化器

### 工具使用原则
- **本地优先**：优先使用本地PHP工具，避免网络调用
- **批量操作**：使用工具进行批量检测和优化
- **质量控制**：每个开发阶段都有对应的质量检测工具
- **性能监控**：持续跟踪项目性能指标

## 🚀 开发流程工具链

### 1. 项目启动阶段
```bash
# 项目全面检查 - 开发开始前必执行
php tools/tool_manager.php batch .

# 输出内容：
# - 项目结构完整性检查
# - 依赖文件存在性验证
# - 配置文件语法检查
# - 权限设置验证
# - 基础性能基线测试
```

### 2. 数据库设计阶段
```bash
# 数据库结构分析
php tools/db_analyzer.php info

# 获取信息：
# - 所有表结构和字段信息
# - 索引使用情况
# - 外键关系
# - 数据统计信息
# - 性能瓶颈识别

# 特定表分析
php tools/db_analyzer.php table 表名

# 索引优化建议
php tools/db_analyzer.php optimize
```

### 3. 前端开发阶段
```bash
# 布局质量检测 - 开发新页面时
php tools/layout_analyzer.php check app/view/admin/页面名.html

# 检测项目：
# - Bootstrap网格系统合规性
# - 响应式断点覆盖度
# - CSS类命名规范
# - 可访问性标准
# - 移动端适配质量

# 批量检测所有页面
php tools/layout_analyzer.php batch app/view/admin/

# 响应式测试
php tools/layout_analyzer.php responsive app/view/admin/页面名.html
```

### 4. 性能优化阶段
```bash
# 全面性能分析
php tools/performance_analyzer.php full

# 分析内容：
# - 页面加载时间
# - 数据库查询性能
# - 缓存命中率
# - 静态资源加载
# - 内存使用情况

# 数据库性能专项
php tools/performance_analyzer.php database

# 前端资源性能
php tools/performance_analyzer.php frontend

# 缓存性能分析
php tools/performance_analyzer.php cache
```

### 5. 代码优化阶段
```bash
# CSS/JS文件优化
php tools/css_js_optimizer.php full

# 优化功能：
# - 重复样式检测和合并
# - 未使用CSS规则清理
# - JS代码压缩
# - 图片资源优化
# - 文件依赖关系分析

# 检测重复样式
php tools/css_js_optimizer.php duplicates

# 压缩静态资源
php tools/css_js_optimizer.php minify

# 分析文件依赖
php tools/css_js_optimizer.php dependencies
```

## 📊 质量控制标准

### 布局质量评分标准
- **优秀（≥90分）**：新功能开发目标
- **良好（85-89分）**：新页面最低要求
- **合格（80-84分）**：上线最低标准
- **需改进（<80分）**：必须优化后才能上线

### 性能指标要求
- **数据库查询**：单次查询<500ms，复杂查询<1000ms
- **页面加载**：首屏<2秒，完整加载<5秒
- **缓存命中率**：Redis缓存>80%，文件缓存>60%
- **CSS文件大小**：单文件<50KB，总体积<200KB
- **响应式覆盖**：断点覆盖度>60%，移动端完全适配

### 代码质量标准
- **Critical问题**：0个（阻塞上线）
- **High级问题**：<3个（需要修复）
- **Medium级问题**：<10个（建议修复）
- **代码重复率**：<15%（DRY原则）

## 🔧 工具配置和自定义

### 工具管理器配置
```php
/**
 * 三只鱼网络科技 | 韩总 | 2025-01-12
 * 工具管理器配置 - ThinkPHP6企业级应用
 */

// tools/config.php
return [
    'quality_standards' => [
        'layout_min_score' => 80,
        'performance_max_query_time' => 500,
        'cache_hit_rate_min' => 80,
        'css_max_file_size' => 50 * 1024, // 50KB
    ],
    'optimization_targets' => [
        'css_reduction_rate' => 50, // 50%减少
        'js_compression_rate' => 30, // 30%压缩
        'image_optimization_rate' => 40, // 40%优化
    ],
    'notification_settings' => [
        'email_alerts' => true,
        'performance_threshold' => 85,
        'quality_threshold' => 80,
    ]
];
```

### 自定义检测规则
```php
// tools/custom_rules.php
return [
    'layout_rules' => [
        'bootstrap_grid_required' => true,
        'responsive_breakpoints_min' => 2,
        'custom_css_classes_max' => 30,
        'container_nesting_max' => 2,
    ],
    'performance_rules' => [
        'database_queries_max' => 20,
        'memory_usage_max' => '128M',
        'execution_time_max' => 30,
    ],
    'security_rules' => [
        'input_validation_required' => true,
        'sql_injection_protection' => true,
        'xss_protection' => true,
        'csrf_protection' => true,
    ]
];
```

## 📈 持续集成工具链

### 开发前检查
```bash
# 开发环境准备检查
php tools/tool_manager.php env-check

# 依赖完整性验证
php tools/tool_manager.php dependencies

# 配置文件验证
php tools/tool_manager.php config-validate
```

### 开发中监控
```bash
# 实时性能监控
php tools/performance_analyzer.php monitor

# 代码质量实时检测
php tools/tool_manager.php watch app/

# 布局变更检测
php tools/layout_analyzer.php watch app/view/
```

### 发布前验证
```bash
# 全面质量检测
php tools/tool_manager.php pre-release

# 性能基准测试
php tools/performance_analyzer.php benchmark

# 安全漏洞扫描
php tools/tool_manager.php security-scan

# 兼容性测试
php tools/tool_manager.php compatibility
```

## 🎯 工具使用最佳实践

### 开发工作流集成
1. **每日开始**：运行 `php tools/tool_manager.php batch .`
2. **新页面开发**：完成后运行布局检测
3. **功能完成**：运行性能分析
4. **代码提交前**：运行全面质量检测
5. **发布前**：运行完整的预发布检测

### 团队协作规范
- **工具版本统一**：所有开发者使用相同版本的工具
- **标准共享**：质量标准和配置在团队内统一
- **结果记录**：重要的检测结果记录到项目文档
- **持续改进**：根据检测结果持续优化开发流程

### 性能优化策略
- **渐进式优化**：先解决Critical问题，再优化性能
- **数据驱动**：基于工具检测结果制定优化计划
- **效果验证**：优化后必须重新检测验证效果
- **回归测试**：确保优化不影响现有功能

## ⚠️ 工具使用注意事项

### Windows环境特殊要求
- **PowerShell语法**：严格使用PowerShell命令语法
- **路径分隔符**：使用反斜杠或正斜杠（工具自动处理）
- **权限设置**：确保PHP有足够权限访问项目文件
- **编码问题**：确保控制台编码设置正确

### 工具调用失败处理
- **自动降级**：网络工具失败时自动使用本地工具
- **错误记录**：工具执行错误记录到日志文件
- **手动备选**：提供手动执行的备选方案
- **状态恢复**：工具异常退出后的状态恢复机制

### 性能影响控制
- **批量操作**：避免频繁调用工具，使用批量模式
- **缓存机制**：工具检测结果适当缓存，避免重复计算
- **后台执行**：大型检测任务在后台执行
- **资源限制**：控制工具占用的系统资源

---

**工具使用原则**：效率优先、质量保证、持续改进、团队协作


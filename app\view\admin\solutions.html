<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>解决方案管理 - 后台管理系统</title>

    <!-- CSS -->
    {include file="admin/common/css"}
    <link rel="stylesheet" href="/assets/css/admin/news.css">
    <link rel="stylesheet" href="/assets/css/image-uploader.css">
    <!-- CKEditor 5 CSS -->
    <link rel="stylesheet" href="/assets/css/ckeditor.css">

    <!-- 图标选择器样式 -->
    <style>
        /* 图标选择器容器 */
        .icon-selector-container {
            margin-top: 10px;
        }

        .icon-input-group {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 15px;
        }

        .icon-input-group .form-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(120, 119, 198, 0.3);
            color: #fff;
        }

        .btn-icon-selector {
            background: linear-gradient(135deg,
                rgba(120, 119, 198, 0.8) 0%,
                rgba(255, 119, 198, 0.6) 50%,
                rgba(120, 219, 255, 0.7) 100%);
            border: 1px solid rgba(120, 119, 198, 0.6);
            color: #ffffff;
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
        }

        .btn-icon-selector:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(120, 119, 198, 0.4);
        }

        .icon-preview {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(120, 119, 198, 0.3);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin-bottom: 10px;
        }

        .preview-icon {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            color: #fff;
        }

        .preview-icon i {
            font-size: 32px;
            color: rgba(120, 219, 255, 0.9);
        }

        .preview-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            color: rgba(255, 255, 255, 0.5);
        }

        .preview-placeholder i {
            font-size: 32px;
            color: rgba(255, 255, 255, 0.3);
        }

        /* 图标选择器弹窗 */
        .icon-selector-modal {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background: rgba(0, 0, 0, 0.85) !important;
            z-index: 99999 !important;
            display: none !important;
            align-items: center !important;
            justify-content: center !important;
            backdrop-filter: blur(5px) !important;
        }

        .icon-selector-modal.show {
            display: flex !important;
        }

        .icon-selector-content {
            background: linear-gradient(135deg, rgba(15, 15, 15, 0.98), rgba(25, 25, 35, 0.98)) !important;
            border: 1px solid rgba(120, 119, 198, 0.5) !important;
            border-radius: 12px !important;
            width: 90% !important;
            max-width: 900px !important;
            max-height: 85vh !important;
            overflow: hidden !important;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8) !important;
            position: relative !important;
            margin: auto !important;
        }

        .icon-selector-header {
            padding: 20px;
            border-bottom: 1px solid rgba(120, 119, 198, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .icon-selector-title {
            color: #fff;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .icon-selector-close {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .icon-selector-close:hover {
            color: #fff;
            background: rgba(255, 255, 255, 0.1);
        }

        .icon-selector-body {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .icon-search {
            width: 100%;
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(120, 119, 198, 0.3);
            border-radius: 8px;
            color: #fff;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .icon-search::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 10px;
        }

        .icon-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(120, 119, 198, 0.3);
            border-radius: 8px;
            padding: 15px 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: rgba(255, 255, 255, 0.8);
        }

        .icon-item:hover {
            background: rgba(120, 119, 198, 0.2);
            border-color: rgba(120, 119, 198, 0.6);
            transform: translateY(-2px);
        }

        .icon-item.selected {
            background: rgba(120, 119, 198, 0.3);
            border-color: rgba(120, 219, 255, 0.8);
            color: #fff;
        }

        .icon-item i {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }

        .icon-item span {
            font-size: 10px;
            display: block;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    {include file="admin/common/header"}
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            {include file="admin/common/sidebar"}

            <!-- 主要内容 -->
            <main class="main-content">
                <!-- 内容头部 -->
                <div class="content-header">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-lightbulb"></i> 解决方案管理
                    </h1>
                </div>

                <!-- 引用统一消息组件 -->
                {include file="admin/common/message"}

                <!-- 页面内容区域 -->
                <div class="content-body">
                    <div class="news-container">

                    {if condition="$action == 'list'"}
                        <!-- 解决方案列表视图 -->

                        <div class="list-header">
                            <div class="list-header-content">
                                <div class="list-title-section">
                                    <div class="list-icon">
                                        <i class="fas fa-lightbulb"></i>
                                    </div>
                                    <div>
                                        <h1 class="list-title">解决方案中心</h1>
                                        <p class="list-subtitle">管理企业解决方案信息</p>
                                    </div>
                                </div>
                                <div class="header-actions">
                                    <a href="/admin/solutions?action=add" class="btn-add-custom">
                                        <i class="fas fa-plus"></i>
                                        <span>添加解决方案</span>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="list-body">
                            {if condition="$solutionsList && count($solutionsList) > 0"}
                                <div class="news-list">
                                    {volist name="solutionsList" id="solution"}
                                    <div class="news-item">
                                        <div class="news-content-wrapper">
                                            <div class="news-thumbnail">
                                                {if condition="$solution.image"}
                                                    <img src="{$solution.image}" alt="{$solution.name}" class="news-thumb-img"
                                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                                    <div class="news-thumb-placeholder" style="display: none;">
                                                        <i class="fas fa-lightbulb"></i>
                                                    </div>
                                                {else /}
                                                    <div class="news-thumb-placeholder">
                                                        <i class="fas fa-lightbulb"></i>
                                                    </div>
                                                {/if}
                                            </div>

                                            <div class="news-info">
                                                <div class="news-header">
                                                    <h3 class="news-title">{$solution.name}</h3>
                                                    <div class="news-badges">
                                                        {if condition="$solution.icon"}
                                                            <span class="badge badge-category">
                                                                <i class="{$solution.icon}"></i>
                                                                图标
                                                            </span>
                                                        {/if}
                                                    </div>
                                                </div>

                                                <div class="news-meta">
                                                    <div class="meta-item">
                                                        <i class="fas fa-calendar"></i>
                                                        <span>{$solution.created_at|date='Y-m-d H:i'}</span>
                                                    </div>
                                                    <div class="meta-item">
                                                        <i class="fas fa-eye"></i>
                                                        <span>{$solution.views ?? 0} 次浏览</span>
                                                    </div>
                                                    <div class="meta-item">
                                                        <i class="fas fa-sort-numeric-up"></i>
                                                        <span>排序: {$solution.sort_order}</span>
                                                    </div>
                                                </div>

                                                {if condition="$solution.short_description"}
                                                    <div class="news-summary">
                                                        {$solution.short_description|mb_substr=0,150,'UTF-8'}
                                                        {if condition="mb_strlen($solution.short_description, 'UTF-8') > 150"}...{/if}
                                                    </div>
                                                {/if}
                                            </div>

                                            <div class="news-actions">
                                                <div class="status-toggle">
                                                    <label class="switch">
                                                        <input type="checkbox"
                                                               {$solution.status ? 'checked' : ''}
                                                               onchange="toggleStatus('{$solution.id}', this.checked ? 1 : 0)">
                                                        <span class="slider"></span>
                                                    </label>
                                                    <span class="status-label">{$solution.status ? '已发布' : '草稿'}</span>
                                                </div>

                                                <div class="action-buttons">
                                                    <a href="/admin/solutions?action=edit&id={$solution.id}" class="btn-action btn-edit" title="编辑">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button onclick="deleteItem('{$solution.id}', '{$solution.name|htmlentities}', '/admin/solutions?action=delete&id={$solution.id}')"
                                                            class="btn-action btn-delete" title="删除">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {/volist}
                                </div>

                                <!-- 分页组件（复用新闻管理的分页样式） -->
                                {if condition="$solutionsList->hasPages()"}
                                    <div class="custom-pagination-container">
                                        <nav class="custom-pagination-nav">
                                            <div class="pagination-info">
                                                <span class="pagination-text">
                                                    显示第 {$solutionsList->currentPage()} 页，共 {$solutionsList->lastPage()} 页，总计 {$solutionsList->total()} 条记录
                                                </span>
                                            </div>
                                            <div class="pagination-buttons">
                                                {if condition="$solutionsList->currentPage() > 1"}
                                                    <a href="/admin/solutions?action=list&page=1" class="pagination-btn pagination-first">
                                                        <i class="fas fa-angle-double-left"></i>
                                                        首页
                                                    </a>
                                                    <a href="/admin/solutions?action=list&page={$solutionsList->currentPage() - 1}" class="pagination-btn pagination-prev">
                                                        <i class="fas fa-angle-left"></i>
                                                        上一页
                                                    </a>
                                                {else /}
                                                    <span class="pagination-btn pagination-first disabled">
                                                        <i class="fas fa-angle-double-left"></i>
                                                        首页
                                                    </span>
                                                    <span class="pagination-btn pagination-prev disabled">
                                                        <i class="fas fa-angle-left"></i>
                                                        上一页
                                                    </span>
                                                {/if}

                                                {php}
                                                    $currentPage = $solutionsList->currentPage();
                                                    $lastPage = $solutionsList->lastPage();

                                                    if ($lastPage <= 7) {
                                                        $startPage = 1;
                                                        $endPage = $lastPage;
                                                    } else {
                                                        $startPage = max(1, $currentPage - 2);
                                                        $endPage = min($lastPage, $currentPage + 2);
                                                    }
                                                {/php}

                                                {if condition="$startPage > 1"}
                                                    <a href="/admin/solutions?action=list&page=1" class="pagination-btn pagination-number">1</a>
                                                    {if condition="$startPage > 2"}
                                                        <span class="pagination-ellipsis">...</span>
                                                    {/if}
                                                {/if}

                                                {php}
                                                    $pageNumbers = range($startPage, $endPage);
                                                {/php}

                                                {volist name="pageNumbers" id="pageNum"}
                                                    {if condition="$pageNum == $currentPage"}
                                                        <span class="pagination-btn pagination-number active">{$pageNum}</span>
                                                    {else /}
                                                        <a href="/admin/solutions?action=list&page={$pageNum}" class="pagination-btn pagination-number">{$pageNum}</a>
                                                    {/if}
                                                {/volist}

                                                {if condition="$endPage < $lastPage"}
                                                    {if condition="$endPage < $lastPage - 1"}
                                                        <span class="pagination-ellipsis">...</span>
                                                    {/if}
                                                    <a href="/admin/solutions?action=list&page={$lastPage}" class="pagination-btn pagination-number">{$lastPage}</a>
                                                {/if}

                                                {if condition="$solutionsList->currentPage() < $solutionsList->lastPage()"}
                                                    <a href="/admin/solutions?action=list&page={$solutionsList->currentPage() + 1}" class="pagination-btn pagination-next">
                                                        下一页
                                                        <i class="fas fa-angle-right"></i>
                                                    </a>
                                                    <a href="/admin/solutions?action=list&page={$solutionsList->lastPage()}" class="pagination-btn pagination-last">
                                                        末页
                                                        <i class="fas fa-angle-double-right"></i>
                                                    </a>
                                                {else /}
                                                    <span class="pagination-btn pagination-next disabled">
                                                        下一页
                                                        <i class="fas fa-angle-right"></i>
                                                    </span>
                                                    <span class="pagination-btn pagination-last disabled">
                                                        末页
                                                        <i class="fas fa-angle-double-right"></i>
                                                    </span>
                                                {/if}
                                            </div>
                                        </nav>
                                    </div>
                                {/if}
                            {else /}
                                <div class="empty-state">
                                    <div class="empty-icon">
                                        <i class="fas fa-lightbulb"></i>
                                    </div>
                                    <h3 class="empty-title">暂无解决方案</h3>
                                    <p class="empty-description">还没有添加任何解决方案，点击上方按钮开始添加第一个解决方案吧！</p>
                                </div>
                            {/if}
                        </div>

                    {elseif condition="$action == 'add' || $action == 'edit'"}
                        <!-- 添加/编辑解决方案表单 -->
                        <div class="form-header">
                            <div class="form-header-content">
                                <div class="form-title-section">
                                    <div class="form-icon">
                                        <i class="fas fa-{$action == 'add' ? 'plus' : 'edit'}"></i>
                                    </div>
                                    <div>
                                        <h1 class="form-title">{$action == 'add' ? '添加解决方案' : '编辑解决方案'}</h1>
                                        <p class="form-subtitle">填写解决方案的详细信息</p>
                                    </div>
                                </div>
                                <a href="/admin/solutions" class="btn-back">
                                    <i class="fas fa-arrow-left"></i>
                                    <span>返回列表</span>
                                </a>
                            </div>
                        </div>

                        <div class="form-body">

                            <form method="POST" action="/admin/solutions" enctype="multipart/form-data" class="news-form">
                                <input type="hidden" name="action" value="{$action}">
                                {if condition="$editData"}
                                    <input type="hidden" name="id" value="{$editData.id}">
                                {/if}

                                <div class="form-grid">
                                    <!-- 基本信息 -->
                                    <div class="form-section">
                                        <div class="section-header">
                                            <h3 class="section-title">
                                                <i class="fas fa-info-circle"></i>
                                                基本信息
                                            </h3>
                                        </div>

                                        <div class="form-row" style="margin-top: 20px;">
                                            <div class="form-group">
                                                <label for="name" class="form-label required">
                                                    <i class="fas fa-lightbulb"></i>
                                                    解决方案名称
                                                </label>
                                                <input type="text" class="form-input" id="name" name="name"
                                                       value="{$editData.name|default=''}"
                                                       placeholder="请输入解决方案名称" required>
                                            </div>

                                            <div class="form-group">
                                                <label for="slug" class="form-label">
                                                    <i class="fas fa-link"></i>
                                                    URL别名
                                                </label>
                                                <input type="text" class="form-input" id="slug" name="slug"
                                                       value="{$editData.slug|default=''}"
                                                       placeholder="自动生成或手动输入">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="short_description" class="form-label">
                                                <i class="fas fa-align-left"></i>
                                                简短描述
                                            </label>
                                            <textarea class="form-textarea" id="short_description" name="short_description" rows="3"
                                                      placeholder="请输入解决方案的简短描述">{$editData.short_description|default=''}</textarea>
                                        </div>

                                        <div class="form-group">
                                            <label for="features" class="form-label">
                                                <i class="fas fa-star"></i>
                                                解决方案特性
                                            </label>
                                            <textarea class="form-textarea" id="features" name="features" rows="4"
                                                      placeholder="每行一个特性，例如：&#10;高效智能分析&#10;实时数据处理&#10;可视化报表展示">{if condition="$editData && $editData.features_array && is_array($editData.features_array)"}{$editData.features_array|implode="\n"}{/if}</textarea>
                                        </div>

                                        <div class="form-group">
                                            <label for="description" class="form-label required">
                                                <i class="fas fa-file-text"></i>
                                                详细描述
                                            </label>
                                            <!-- CKEditor 5编辑器 -->
                                            <div class="ck-editor-container">
                                                <textarea id="editor" name="description">{$editData.description|default=''}</textarea>
                                            </div>
                                        </div>

                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="sort_order" class="form-label">
                                                    <i class="fas fa-sort"></i>
                                                    排序权重
                                                </label>
                                                <input type="number" class="form-input" id="sort_order" name="sort_order"
                                                       value="{$editData.sort_order|default=0}"
                                                       placeholder="0" min="0">
                                            </div>

                                            <div class="checkbox-group" style="margin-top: 40px;">
                                                <label class="checkbox-label">
                                                    <input type="checkbox" name="status" value="1"
                                                           {if condition="!$editData || $editData.status"}checked{/if}>
                                                    <span class="checkbox-custom"></span>
                                                    <span class="checkbox-text">
                                                        <i class="fas fa-eye"></i>
                                                        立即发布
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 解决方案图片和图标 -->
                                    <div class="form-section">
                                        <div class="section-header">
                                            <h3 class="section-title">
                                                <i class="fas fa-image"></i>
                                                解决方案图片和图标
                                            </h3>
                                        </div>

                                        <div class="form-row" style="margin-top: 20px;">
                                            <!-- 解决方案图片 -->
                                            <div class="form-group">
                                                <label class="form-label">
                                                    <i class="fas fa-upload"></i>
                                                    解决方案图片
                                                </label>

                                                <!-- 图片上传弹窗按钮 -->
                                                <div class="image-upload-section">
                                                    <button type="button" class="btn-upload-image" id="btnSelectSolutionImage">
                                                        <i class="fas fa-images"></i>
                                                        <span>选择图片</span>
                                                    </button>
                                                    <div class="upload-help">
                                                        <small>支持上传新图片或从图库中选择，推荐尺寸：800x600px</small>
                                                    </div>
                                                </div>

                                                <!-- 隐藏的表单字段存储选择的图片URL -->
                                                <input type="hidden" id="selectedImageUrl" name="image_url" value="{$editData.image|default=''}">

                                                <!-- 当前选择的图片预览 -->
                                                <div class="selected-image-preview" id="selectedImagePreview" {if condition='!$editData || !$editData.image'}style="display: none;"{/if}>
                                                    <label class="form-label">已选择的图片：</label>
                                                    <div class="image-preview-container">
                                                        <img src="{$editData.image|default=''}" alt="解决方案图片" class="preview-img" id="previewImg">
                                                        <div class="image-actions">
                                                            <button type="button" class="btn-change-image" onclick="changeSolutionImage()">
                                                                <i class="fas fa-edit"></i>
                                                                更换图片
                                                            </button>
                                                            <button type="button" class="btn-remove-image" onclick="removeSolutionImage()">
                                                                <i class="fas fa-trash"></i>
                                                                移除图片
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 解决方案图标 -->
                                            <div class="form-group">
                                                <label for="icon" class="form-label">
                                                    <i class="fas fa-star"></i>
                                                    图标选择
                                                </label>

                                                <!-- 图标选择器 -->
                                                <div class="icon-selector-container">
                                                    <div class="icon-input-group">
                                                        <input type="text" class="form-input" id="icon" name="icon"
                                                               value="{$editData.icon|default=''}"
                                                               placeholder="例如：fas fa-lightbulb"
                                                               readonly>
                                                        <button type="button" class="btn-icon-selector" id="btnSelectIcon">
                                                            <i class="fas fa-icons"></i>
                                                            选择图标
                                                        </button>
                                                    </div>

                                                    <!-- 图标预览 -->
                                                    <div class="icon-preview" id="iconPreview">
                                                        {if condition="$editData && $editData.icon"}
                                                            <div class="preview-icon">
                                                                <i class="{$editData.icon}"></i>
                                                                <span>当前图标</span>
                                                            </div>
                                                        {else/}
                                                            <div class="preview-placeholder">
                                                                <i class="fas fa-question-circle"></i>
                                                                <span>未选择图标</span>
                                                            </div>
                                                        {/if}
                                                    </div>

                                                    <div class="form-help">
                                                        选择FontAwesome图标代表此解决方案
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>

                                <!-- 表单操作按钮 -->
                                <div class="form-actions">
                                    <button type="submit" class="btn-submit">
                                        <i class="fas fa-save"></i>
                                        <span>保存解决方案</span>
                                    </button>
                                    <a href="/admin/solutions" class="btn-cancel">
                                        <i class="fas fa-times"></i>
                                        <span>取消</span>
                                    </a>
                                </div>
                            </form>
                        </div>
                    {/if}

                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 图标选择器弹窗 -->
    <div class="icon-selector-modal" id="iconSelectorModal">
        <div class="icon-selector-content">
            <div class="icon-selector-header">
                <h3 class="icon-selector-title">
                    <i class="fas fa-icons"></i>
                    选择解决方案图标
                </h3>
                <button type="button" class="icon-selector-close" onclick="closeIconSelector()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="icon-selector-body">
                <input type="text" class="icon-search" placeholder="搜索图标..." onkeyup="filterIcons(this.value)">
                <div class="icon-grid" id="iconGrid">
                    <!-- 图标列表将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 图片选择器组件 -->


    <!-- JavaScript -->
    {include file="admin/common/js"}

    <!-- CKEditor 5 Classic版本 -->
    <script src="/assets/js/ckeditor.js"></script>
    <!-- CKEditor 5 中文语言包 -->
    <script src="/assets/js/zh-cn.js"></script>
    <!-- 图片上传弹窗组件 -->
    <script src="/assets/js/image-uploader.js"></script>
    <!-- 图片选择器扩展 -->
    <script src="/assets/js/image-selector-extension.js"></script>

    <script>
        let editor;
        let editorImageUploader; // 编辑器专用图片上传组件实例
        let solutionImageUploader; // 解决方案图片选择器实例

        $(document).ready(function() {
            // 初始化编辑器专用图片上传组件
            editorImageUploader = createImageUploader({
                uploadUrl: '/admin/image/upload?context=solutions',
                uploadField: 'upload',
                maxFiles: 999,
                maxSize: 5 * 1024 * 1024,
                allowedTypes: ['image/jpeg', 'image/png'],
                enableImageSelector: true,
                selectorUrl: '/admin/image/selector',
                isEditor: true,
                context: 'editor',
                instanceId: 'editor-uploader',
                onSelect: function(files) {
                    const validFiles = [];
                    const errors = [];

                    files.forEach(file => {
                        if (file.file_size && file.file_size > 5 * 1024 * 1024) {
                            errors.push(`${file.filename}: 文件大小超过5MB`);
                        } else if (file.file_type && !['image/jpeg', 'image/png'].includes(file.file_type)) {
                            errors.push(`${file.filename}: 不支持的文件类型`);
                        } else {
                            validFiles.push(file);
                        }
                    });

                    if (errors.length > 0) {
                        editorImageUploader.showMessage('部分文件无效：\n' + errors.join('\n'), 'warning');
                    }

                    return validFiles;
                },
                onConfirm: function(orderedData, mode) {
                    if (mode === 'select') {
                        insertSelectedImagesToEditor(orderedData);
                    } else {
                        insertOrderedFilesToEditor(orderedData);
                    }
                },
                onUpload: function(uploadedFiles) {
                    uploadedFiles.forEach((fileData, index) => {
                        setTimeout(() => {
                            insertImageToEditor(fileData.url);
                        }, index * 100);
                    });
                    editorImageUploader.close();
                },
                onError: function(error) {
                    editorImageUploader.showMessage('图片上传失败：' + error.message, 'error');
                }
            });

            // 初始化CKEditor 5编辑器
            if ($('#editor').length) {
                ClassicEditor
                    .create(document.querySelector('#editor'), {
                        language: 'zh-cn',
                        placeholder: '请输入解决方案详情...',
                        toolbar: [
                            'heading',
                            'bold',
                            'italic',
                            'underline',
                            'numberedList',
                            'bulletedList',
                            'outdent',
                            'indent',
                            'link',
                            'insertTable',
                            'blockQuote',
                            'undo',
                            'redo'
                        ],
                        heading: {
                            options: [
                                { model: 'paragraph', title: '正文', class: 'ck-heading_paragraph' },
                                { model: 'heading1', view: 'h1', title: '标题 1', class: 'ck-heading_heading1' },
                                { model: 'heading2', view: 'h2', title: '标题 2', class: 'ck-heading_heading2' },
                                { model: 'heading3', view: 'h3', title: '标题 3', class: 'ck-heading_heading3' },
                                { model: 'heading4', view: 'h4', title: '标题 4', class: 'ck-heading_heading4' }
                            ]
                        },
                        table: {
                            contentToolbar: [
                                'tableColumn',
                                'tableRow',
                                'mergeTableCells',
                                'tableCellProperties',
                                'tableProperties'
                            ]
                        },
                        link: {
                            decorators: {
                                openInNewTab: {
                                    mode: 'manual',
                                    label: '在新标签页中打开',
                                    attributes: {
                                        target: '_blank',
                                        rel: 'noopener noreferrer'
                                    }
                                }
                            }
                        }
                    })
                    .then(newEditor => {
                        editor = newEditor;
                        window.editor = newEditor;

                        // 设置编辑器高度
                        const editingView = editor.editing.view;
                        editingView.change(writer => {
                            writer.setStyle('min-height', '300px', editingView.document.getRoot());
                            writer.setStyle('max-height', '500px', editingView.document.getRoot());
                        });

                        // 设置焦点样式
                        editor.ui.focusTracker.on('change:isFocused', (evt, name, isFocused) => {
                            if (isFocused) {
                                $('.ck-editor-container').addClass('focused');
                            } else {
                                $('.ck-editor-container').removeClass('focused');
                            }
                        });

                        // 添加图片上传按钮
                        setTimeout(() => {
                            addImageUploadButton();
                        }, 1000);

                        console.log('✅ CKEditor 5 Classic 初始化成功');
                    })
                    .catch(error => {
                        console.error('❌ CKEditor 5 初始化失败:', error);
                        $('#editor').addClass('form-textarea').attr('rows', 15).show();
                        showMessage('编辑器加载失败，已切换到基础模式', 'warning');
                        window.editor = null;
                    });
            }

            // 添加图片上传按钮到编辑器工具栏 - 与新闻管理完全一致
            function addImageUploadButton() {
                // 多种方式查找工具栏
                let toolbarItems = document.querySelector('.ck-toolbar .ck-toolbar__items');
                if (!toolbarItems) {
                    toolbarItems = document.querySelector('.ck-toolbar__items');
                }
                if (!toolbarItems) {
                    toolbarItems = document.querySelector('.ck-toolbar');
                }

                if (!toolbarItems) {
                    return false;
                }

                // 检查按钮是否已存在
                if (document.querySelector('[data-upload-button="true"]')) {
                    return true;
                }

                // 创建图片上传按钮
                const imageButton = document.createElement('button');
                imageButton.className = 'ck-button ck-button_with-text';
                imageButton.type = 'button';
                imageButton.setAttribute('data-upload-button', 'true');
                imageButton.setAttribute('title', '上传图片');
                imageButton.setAttribute('aria-label', '上传图片');

                // 创建图标容器
                const iconContainer = document.createElement('span');
                iconContainer.className = 'ck-button__icon';

                // 使用FontAwesome图标
                const icon = document.createElement('i');
                icon.className = 'fas fa-images';
                icon.style.cssText = `
                    font-size: 12px !important;
                    color: rgba(255, 255, 255, 0.8) !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                    display: inline-block !important;
                `;
                iconContainer.appendChild(icon);
                imageButton.appendChild(iconContainer);

                // 添加文本标签
                const textLabel = document.createElement('span');
                textLabel.className = 'ck-button__label';
                textLabel.textContent = '图片';
                imageButton.appendChild(textLabel);

                // 绑定点击事件 - 打开编辑器专用弹窗
                imageButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🖼️ 打开编辑器图片上传弹窗');

                    if (editorImageUploader) {
                        editorImageUploader.show();
                    } else {
                        console.error('❌ 编辑器图片上传组件未初始化');
                    }
                });

                // 插入到工具栏末尾
                toolbarItems.appendChild(imageButton);
                console.log('✅ 图片按钮已添加到工具栏');
                return true;
            }

            // 插入选择的图片到编辑器
            function insertSelectedImagesToEditor(selectedImages) {
                if (!window.editor) {
                    return;
                }

                selectedImages.forEach((image, index) => {
                    setTimeout(() => {
                        try {
                            const imageHtml = `
                                <img src="${image.file_url}" alt="${image.alt_text || image.filename}"
                                     style="max-width: 100%; height: auto; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            `;

                            const selection = window.editor.model.document.selection;
                            const position = selection.getLastPosition();

                            const viewFragment = window.editor.data.processor.toView(imageHtml);
                            const modelFragment = window.editor.data.toModel(viewFragment);

                            window.editor.model.change(writer => {
                                window.editor.model.insertContent(modelFragment, position);
                            });

                            if (index === selectedImages.length - 1) {
                                editorImageUploader.showMessage(`已按顺序插入 ${selectedImages.length} 张图片`, 'success');
                            }
                        } catch (error) {
                            console.error('插入图片失败:', error);
                        }
                    }, index * 200);
                });
            }

            // 插入文件预览到编辑器
            function insertOrderedFilesToEditor(orderedFiles) {
                if (!window.editor) {
                    return;
                }

                orderedFiles.forEach((file, index) => {
                    const localUrl = URL.createObjectURL(file);
                    setTimeout(() => {
                        try {
                            const placeholderHtml = `
                                <div style="border: 2px dashed #ddd; padding: 20px; margin: 10px 0; text-align: center; border-radius: 8px; background: #f9f9f9;">
                                    <img src="${localUrl}" alt="${file.name}" style="max-width: 200px; max-height: 150px; border-radius: 4px; margin-bottom: 10px;">
                                    <p style="margin: 0; color: #666; font-size: 14px;">
                                        <strong>${file.name}</strong><br>
                                        <em>预览图片 - 需要上传后才能保存</em>
                                    </p>
                                </div>
                            `;

                            const selection = window.editor.model.document.selection;
                            const position = selection.getLastPosition();

                            const viewFragment = window.editor.data.processor.toView(placeholderHtml);
                            const modelFragment = window.editor.data.toModel(viewFragment);

                            window.editor.model.change(writer => {
                                window.editor.model.insertContent(modelFragment, position);
                            });

                            if (index === orderedFiles.length - 1) {
                                editorImageUploader.showMessage(`已按顺序插入 ${orderedFiles.length} 张图片预览`, 'success');
                            }
                        } catch (error) {
                            console.error('插入图片预览失败:', error);
                        }
                    }, index * 200);
                });
            }

            // 插入图片到编辑器
            function insertImageToEditor(imageUrl) {
                if (!window.editor) {
                    return;
                }

                try {
                    const imageHtml = `<img src="${imageUrl}" alt="上传的图片" style="max-width: 100%; height: auto; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">`;

                    const selection = window.editor.model.document.selection;
                    const position = selection.getLastPosition();

                    const viewFragment = window.editor.data.processor.toView(imageHtml);
                    const modelFragment = window.editor.data.toModel(viewFragment);

                    window.editor.model.change(writer => {
                        window.editor.model.insertContent(modelFragment, position);
                    });
                } catch (error) {
                    editorImageUploader.showMessage('图片插入失败', 'error');
                }
            }

            // 初始化图标选择器
            initIconSelector();
        });

        // 状态切换函数
        function toggleStatus(id, status) {
            fetch('/admin/solutions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `action=toggle_status&id=${id}&status=${status}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('状态更新成功', 'success');
                } else {
                    showMessage(data.message || '操作失败', 'error');
                    // 恢复开关状态
                    const checkbox = document.querySelector(`input[onchange*="${id}"]`);
                    if (checkbox) {
                        checkbox.checked = !checkbox.checked;
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage('网络错误', 'error');
                // 恢复开关状态
                const checkbox = document.querySelector(`input[onchange*="${id}"]`);
                if (checkbox) {
                    checkbox.checked = !checkbox.checked;
                }
            });
        }

        // 删除确认函数
        function deleteItem(id, name, url) {
            if (confirm(`确定要删除解决方案"${name}"吗？此操作不可恢复。`)) {
                window.location.href = url;
            }
        }

        // 初始化解决方案图片上传器
        $(document).ready(function() {
            // 初始化解决方案图片选择器
            solutionImageUploader = createImageUploader({
                uploadUrl: '/admin/image/upload?context=solutions',
                uploadField: 'upload',
                maxFiles: 1,
                maxSize: 5 * 1024 * 1024,
                allowedTypes: ['image/jpeg', 'image/png'],
                enableImageSelector: true,
                selectorUrl: '/admin/image/selector',
                isEditor: false,
                context: 'solution',
                instanceId: 'solution-image-uploader',
                onSelect: function(files) {
                    if (files.length > 1) {
                        // 使用图片上传器自己的提示
                        solutionImageUploader.showMessage('解决方案图片只能选择一张', 'warning');
                        return [files[0]];
                    }
                    return files;
                },
                onConfirm: function(selectedFiles, mode) {
                    if (selectedFiles.length > 0) {
                        const file = selectedFiles[0];
                        if (mode === 'select') {
                            updateSolutionImagePreview(file.file_url, file.filename);
                            solutionImageUploader.showMessage('图片选择成功', 'success');
                        } else {
                            // 文件上传模式 - 显示预览
                            const reader = new FileReader();
                            reader.onload = function(e) {
                                updateSolutionImagePreview(e.target.result, file.name);
                            };
                            reader.readAsDataURL(file);
                            solutionImageUploader.showMessage('图片预览已设置，提交表单时将自动上传', 'info');
                        }
                    }
                    solutionImageUploader.close();
                },
                onUpload: function(uploadedFiles) {
                    if (uploadedFiles.length > 0) {
                        const fileData = uploadedFiles[0];
                        updateSolutionImagePreview(fileData.url, fileData.filename);
                        // 使用图片上传器自己的提示
                        solutionImageUploader.showMessage('图片上传成功', 'success');
                    }
                    solutionImageUploader.close();
                },
                onError: function(error) {
                    // 使用图片上传器自己的提示
                    solutionImageUploader.showMessage('图片上传失败：' + error.message, 'error');
                }
            });

            // 绑定解决方案图片选择按钮
            $('#btnSelectSolutionImage').on('click', function() {
                if (solutionImageUploader) {
                    solutionImageUploader.show();
                }
            });
        });

        // 更新解决方案图片预览
        function updateSolutionImagePreview(imageUrl, filename) {
            const selectedImageUrl = document.getElementById('selectedImageUrl');
            const selectedImagePreview = document.getElementById('selectedImagePreview');
            const previewImg = document.getElementById('previewImg');

            if (selectedImageUrl && selectedImagePreview && previewImg) {
                selectedImageUrl.value = imageUrl;
                previewImg.src = imageUrl;
                previewImg.alt = filename || '解决方案图片';
                selectedImagePreview.style.display = 'block';
            }
        }

        // 更换解决方案图片
        function changeSolutionImage() {
            if (solutionImageUploader) {
                solutionImageUploader.show();
            }
        }

        // 移除解决方案图片 - 使用图片上传器自己的提示
        function removeSolutionImage() {
            const selectedImageUrl = document.getElementById('selectedImageUrl');
            const selectedImagePreview = document.getElementById('selectedImagePreview');

            if (selectedImageUrl && selectedImagePreview) {
                selectedImageUrl.value = '';
                selectedImagePreview.style.display = 'none';
                if (solutionImageUploader) {
                    solutionImageUploader.showMessage('图片已移除', 'success');
                } else {
                showMessage('图片已移除', 'success');
                }
            }
        }
    </script>

    <!-- 图标选择器JavaScript -->
    <script>
        // 初始化图标选择器
        function initIconSelector() {
            const btnSelectIcon = document.getElementById('btnSelectIcon');
            if (btnSelectIcon) {
                btnSelectIcon.addEventListener('click', function() {
                    openIconSelector();
                });
            }

            // 页面加载时更新图标预览
            const iconInput = document.getElementById('icon');
            if (iconInput && iconInput.value) {
                updateIconPreview(iconInput.value);
            }
        }

        // 常用图标列表
        const iconList = [
            // 解决方案相关图标
            { class: 'fas fa-lightbulb', name: '灯泡' },
            { class: 'fas fa-rocket', name: '火箭' },
            { class: 'fas fa-cogs', name: '齿轮' },
            { class: 'fas fa-chart-line', name: '图表' },
            { class: 'fas fa-shield-alt', name: '盾牌' },
            { class: 'fas fa-cloud', name: '云' },
            { class: 'fas fa-mobile-alt', name: '手机' },
            { class: 'fas fa-laptop', name: '笔记本' },
            { class: 'fas fa-database', name: '数据库' },
            { class: 'fas fa-network-wired', name: '网络' },
            { class: 'fas fa-brain', name: '大脑' },
            { class: 'fas fa-robot', name: '机器人' },
            { class: 'fas fa-microchip', name: '芯片' },
            { class: 'fas fa-wifi', name: 'WiFi' },
            { class: 'fas fa-globe', name: '地球' },
            { class: 'fas fa-server', name: '服务器' },
            { class: 'fas fa-code', name: '代码' },
            { class: 'fas fa-bug', name: '调试' },
            { class: 'fas fa-wrench', name: '扳手' },
            { class: 'fas fa-hammer', name: '锤子' },
            { class: 'fas fa-tools', name: '工具' },
            { class: 'fas fa-magic', name: '魔法' },
            { class: 'fas fa-star', name: '星星' },
            { class: 'fas fa-fire', name: '火焰' },
            { class: 'fas fa-bolt', name: '闪电' },
            { class: 'fas fa-gem', name: '宝石' },
            { class: 'fas fa-crown', name: '皇冠' },
            { class: 'fas fa-trophy', name: '奖杯' },
            { class: 'fas fa-medal', name: '奖牌' },
            { class: 'fas fa-award', name: '奖项' },
            // 业务相关图标
            { class: 'fas fa-handshake', name: '握手' },
            { class: 'fas fa-users', name: '用户' },
            { class: 'fas fa-user-tie', name: '商务' },
            { class: 'fas fa-briefcase', name: '公文包' },
            { class: 'fas fa-building', name: '建筑' },
            { class: 'fas fa-industry', name: '工业' },
            { class: 'fas fa-store', name: '商店' },
            { class: 'fas fa-shopping-cart', name: '购物车' },
            { class: 'fas fa-credit-card', name: '信用卡' },
            { class: 'fas fa-money-bill', name: '钞票' },
            { class: 'fas fa-chart-pie', name: '饼图' },
            { class: 'fas fa-chart-bar', name: '柱状图' },
            { class: 'fas fa-analytics', name: '分析' },
            { class: 'fas fa-search', name: '搜索' },
            { class: 'fas fa-eye', name: '眼睛' },
            { class: 'fas fa-heart', name: '心' },
            { class: 'fas fa-thumbs-up', name: '点赞' },
            { class: 'fas fa-check', name: '勾选' },
            { class: 'fas fa-times', name: '关闭' },
            { class: 'fas fa-plus', name: '加号' },
            { class: 'fas fa-minus', name: '减号' }
        ];

        let allIcons = [...iconList];
        let selectedIcon = '';

        // 打开图标选择器
        function openIconSelector() {
            const modal = document.getElementById('iconSelectorModal');
            modal.classList.add('show');
            generateIconGrid(allIcons);

            // 设置当前选中的图标
            const currentIcon = document.getElementById('icon').value;
            if (currentIcon) {
                selectedIcon = currentIcon;
                highlightSelectedIcon(currentIcon);
            }
        }

        // 关闭图标选择器
        function closeIconSelector() {
            const modal = document.getElementById('iconSelectorModal');
            modal.classList.remove('show');
        }

        // 生成图标网格
        function generateIconGrid(icons) {
            const grid = document.getElementById('iconGrid');
            grid.innerHTML = '';

            icons.forEach(icon => {
                const iconItem = document.createElement('div');
                iconItem.className = 'icon-item';
                iconItem.setAttribute('data-icon', icon.class);
                iconItem.innerHTML = `
                    <i class="${icon.class}"></i>
                    <span>${icon.name}</span>
                `;

                iconItem.addEventListener('click', function() {
                    selectIcon(icon.class);
                });

                grid.appendChild(iconItem);
            });
        }

        // 选择图标
        function selectIcon(iconClass) {
            selectedIcon = iconClass;

            // 更新输入框
            document.getElementById('icon').value = iconClass;

            // 更新预览
            updateIconPreview(iconClass);

            // 高亮选中的图标
            highlightSelectedIcon(iconClass);

            // 关闭选择器
            setTimeout(() => {
                closeIconSelector();
            }, 200);
        }

        // 更新图标预览
        function updateIconPreview(iconClass) {
            const preview = document.getElementById('iconPreview');
            if (iconClass) {
                preview.innerHTML = `
                    <div class="preview-icon">
                        <i class="${iconClass}"></i>
                        <span>当前图标</span>
                    </div>
                `;
            } else {
                preview.innerHTML = `
                    <div class="preview-placeholder">
                        <i class="fas fa-image"></i>
                        <span>点击下方按钮选择图标</span>
                    </div>
                `;
            }
        }

        // 高亮选中的图标
        function highlightSelectedIcon(iconClass) {
            // 移除所有选中状态
            document.querySelectorAll('.icon-item').forEach(item => {
                item.classList.remove('selected');
            });

            // 添加选中状态
            const selectedItem = document.querySelector(`[data-icon="${iconClass}"]`);
            if (selectedItem) {
                selectedItem.classList.add('selected');
            }
        }

        // 过滤图标
        function filterIcons(searchTerm) {
            const filteredIcons = allIcons.filter(icon =>
                icon.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                icon.class.toLowerCase().includes(searchTerm.toLowerCase())
            );
            generateIconGrid(filteredIcons);

            // 如果有选中的图标，重新高亮
            if (selectedIcon) {
                highlightSelectedIcon(selectedIcon);
            }
        }

        // 点击模态框外部关闭
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('iconSelectorModal');
            if (e.target === modal) {
                closeIconSelector();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeIconSelector();
            }
        });

        // showMessage函数已移至message.html统一管理
    </script>
</body>
</html>

<?php

namespace app\controller;

use app\BaseController;
use app\model\Banner;
use app\model\News;
use app\model\Cases;
use app\service\ConfigService;
use app\validate\SecurityValidate;
use think\facade\Cache;

class Index extends BaseController
{
    /**
     * 首页
     */
    public function index()
    {
        // 获取站点配置
        $siteConfig = ConfigService::getAll();
        
        // 检查是否有启用的轮播图来决定显示模式
        $activeBanners = Banner::where('status', 1)->count();
        $useCustomBanners = $activeBanners > 0;

        // 如果使用自定义轮播图，则获取数据
        $banners = [];
        if ($useCustomBanners) {
            $banners = Banner::getActive(5);
            // 处理轮播图URLs，确保使用完整的URL
            foreach ($banners as &$banner) {
                if (!empty($banner['image'])) {
                    $banner['image'] = ConfigService::asset($banner['image']);
                }
                if (!empty($banner['link_url']) && !preg_match('/^https?:\/\//', $banner['link_url'])) {
                    // 如果link_url是相对路径，转换为完整URL
                    $banner['link_url'] = ConfigService::url($banner['link_url']);
                }
            }
        }

        // 获取最新动态（推荐新闻）
        $latestNews = News::getFeaturedNews(3); // 获取3条推荐新闻
        
        // 如果推荐新闻不够，补充最新新闻
        if (count($latestNews) < 3) {
            $remainingCount = 3 - count($latestNews);
            $additionalNews = News::getLatestNews($remainingCount);
            // 过滤掉已经在推荐新闻中的文章
            $existingIds = array_column($latestNews->toArray(), 'id');
            $additionalNews = $additionalNews->filter(function($news) use ($existingIds) {
                return !in_array($news->id, $existingIds);
            });
            $latestNews = $latestNews->merge($additionalNews);
        }

        // 处理新闻URLs，确保使用完整的URL
        foreach ($latestNews as &$news) {
            if (!empty($news['image'])) {
                $news['image'] = ConfigService::asset($news['image']);
            }
            // 生成新闻详情URL
            $news['detail_url'] = ConfigService::url("/news/{$news['slug']}");
        }

        // 页面信息
        $pageData = [
            'pageTitle' => $siteConfig['site_title'] ?? '首页',
            'pageDescription' => $siteConfig['site_description'] ?? '专注于为企业提供专业、可靠、高效的数字化转型解决方案',
            'pageKeywords' => $siteConfig['site_keywords'] ?? '数字化转型,企业解决方案,技术服务,创新科技,专业团队',
            'bodyClass' => 'home-page'
        ];

        return view('index/index', [
            'pageData' => $pageData,
            'banners' => $banners,
            'useCustomBanners' => $useCustomBanners,
            'latestNews' => $latestNews,
            'siteConfig' => $siteConfig
        ]);
    }

    /**
     * 解决方案列表
     */
    public function solutions()
    {
        // 获取站点配置
        $siteConfig = ConfigService::getAll();
        
        // 从solutions表获取解决方案数据
        $solutions = \app\model\Solution::where('status', 1)
            ->order('sort_order', 'desc')
            ->select();
        
        // 页面信息
        $pageData = [
            'pageTitle' => '解决方案 - ' . ($siteConfig['site_title'] ?? ''),
            'pageDescription' => '我们提供全面的企业解决方案，助力企业数字化转型 - ' . ($siteConfig['site_description'] ?? ''),
            'pageKeywords' => '解决方案,企业服务,数字化转型,' . ($siteConfig['site_keywords'] ?? ''),
            'bodyClass' => 'solutions-page'
        ];

        return view('solutions/index', [
            'pageData' => $pageData,
            'solutions' => $solutions,
            'siteConfig' => $siteConfig
        ]);
    }

    /**
     * 解决方案详情
     */
    public function solutionDetail($slug)
    {
        // 参数安全验证
        $params = ['slug' => $slug];
        $securityCheck = SecurityValidate::validateDataSecurity($params, [
            'slug' => 'checkUsernameSafe',
        ]);

        if (!$securityCheck['valid']) {
            abort(400, '参数包含不安全字符');
        }

        // 获取站点配置
        $siteConfig = ConfigService::getAll();

        // 根据slug查找解决方案
        $solution = \app\model\Solution::where('slug', $slug)
            ->where('status', 1)
            ->find();

        if (!$solution) {
            abort(404, '解决方案不存在');
        }

        // 获取相关解决方案（其他解决方案）
        $relatedSolutions = \app\model\Solution::where('id', '<>', $solution->id)
            ->where('status', 1)
            ->order('sort_order', 'desc')
            ->limit(4)
            ->select();
        
        // 页面信息
        $pageData = [
            'pageTitle' => $solution->name . ' - 解决方案详情 - ' . ($siteConfig['site_title'] ?? ''),
            'pageDescription' => $solution->short_description ?: mb_substr(strip_tags($solution->description), 0, 150, 'UTF-8'),
            'pageKeywords' => $solution->name . ',解决方案,' . ($siteConfig['site_keywords'] ?? ''),
            'bodyClass' => 'solution-detail-page'
        ];

        // 强制返回我们的量子科技模板
        return view('solutions/detail', [
            'pageData' => $pageData,
            'solution' => $solution,
            'relatedSolutions' => $relatedSolutions,
            'siteConfig' => $siteConfig
        ]);
    }

    /**
     * 新闻列表
     */
    public function news()
    {
        // 获取站点配置
        $siteConfig = ConfigService::getAll();
        
        // 获取当前分类和标签
        $currentCategory = $this->request->param('category', '');
        $currentTag = $this->request->param('tag', '');
        
        // 获取分页参数
        $page = $this->request->param('page', 1);
        $limit = 8; // 每页显示8篇文章
        
        // 构建查询条件
        $where = ['status' => 1];
        $newsQuery = News::with('category')->where($where);
        
        if ($currentCategory) {
            // 通过分类slug查询分类ID
            $category = \app\model\NewsCategory::where('slug', $currentCategory)->find();
            if ($category) {
                $newsQuery->where('category_id', $category->id);
            }
        }
        
        if ($currentTag) {
            // 通过标签查询新闻
            $newsQuery->where('tags', 'like', '%' . $currentTag . '%');
        }
        
        // 获取新闻列表
        $newsList = $newsQuery
            ->order('sort_order', 'desc')
            ->order('published_at', 'desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);
        
        // 处理新闻URLs
        foreach ($newsList as &$news) {
            if (!empty($news['image'])) {
                $news['image'] = ConfigService::asset($news['image']);
            }
            $news['detail_url'] = ConfigService::url("/news/{$news['slug']}");
        }
        
        // 获取新闻分类（带新闻数量统计）
        $categories = \app\model\NewsCategory::where('status', 1)
            ->withCount('news')
            ->order('sort_order', 'desc')
            ->select();
        
        // 获取热门新闻（按浏览量排序）
        $hotNews = News::where('status', 1)
            ->order('views', 'desc')
            ->order('published_at', 'desc')
            ->limit(5)
            ->select();
        
        // 处理热门新闻URLs
        foreach ($hotNews as &$hot) {
            if (!empty($hot['image'])) {
                $hot['image'] = ConfigService::asset($hot['image']);
            }
            $hot['detail_url'] = ConfigService::url("/news/{$hot['slug']}");
        }
        
        // 获取最新新闻
        $latestNews = News::with('category')
            ->where('status', 1)
            ->order('published_at', 'desc')
            ->limit(6)
            ->select();
        
        // 处理最新新闻URLs
        foreach ($latestNews as &$latest) {
            if (!empty($latest['image'])) {
                $latest['image'] = ConfigService::asset($latest['image']);
            }
            $latest['detail_url'] = ConfigService::url("/news/{$latest['slug']}");
        }
        
        // 获取热门标签
        $popularTags = $this->getPopularTags();
        
        // 页面信息
        $pageData = [
            'pageTitle' => '新闻资讯 - ' . ($siteConfig['site_title'] ?? ''),
            'pageTitlee' => '新闻资讯',
            'pageDescription' => '关注最新动态，掌握行业趋势 - ' . ($siteConfig['site_description'] ?? ''),
            'pageKeywords' => '新闻资讯,行业动态,公司新闻,' . ($siteConfig['site_keywords'] ?? ''),
            'bodyClass' => 'news-page'
        ];

        return view('news/index', [
            'pageData' => $pageData,
            'newsList' => $newsList,
            'categories' => $categories,
            'currentCategory' => $currentCategory,
            'currentTag' => $currentTag,
            'pagination' => $newsList->render(),
            'hotNews' => $hotNews,
            'latestNews' => $latestNews,
            'popularTags' => $popularTags,
            'siteConfig' => $siteConfig
        ]);
    }

    /**
     * 新闻分类页面
     */
    public function newsCategory($category = '')
    {
        // 获取站点配置
        $siteConfig = ConfigService::getAll();

        // 根据slug获取分类信息
        $categoryInfo = \app\model\NewsCategory::where('slug', $category)
            ->where('status', 1)
            ->find();

        if (!$categoryInfo) {
            abort(404, '分类不存在');
        }

        // 获取分页参数
        $page = $this->request->param('page', 1);
        $limit = 12; // 每页显示12篇文章

        // 获取该分类下的新闻
        $newsList = News::with('category')
            ->where('category_id', $categoryInfo->id)
            ->where('status', 1)
            ->order('published_at', 'desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page,
                'path' => $this->request->baseUrl()
            ]);

        // 处理新闻数据
        foreach ($newsList as &$news) {
            if (!empty($news['image'])) {
                $news['image'] = ConfigService::asset($news['image']);
            }
            $news['detail_url'] = ConfigService::url("/news/{$news['slug']}");
        }

        // 获取所有分类（用于侧边栏）
        $categories = \app\model\NewsCategory::where('status', 1)
            ->withCount('news')
            ->order('sort_order', 'desc')
            ->select();

        // 获取热门新闻
        $hotNews = News::where('status', 1)
            ->order('views', 'desc')
            ->limit(5)
            ->select();

        foreach ($hotNews as &$hot) {
            if (!empty($hot['image'])) {
                $hot['image'] = ConfigService::asset($hot['image']);
            }
            $hot['detail_url'] = ConfigService::url("/news/{$hot['slug']}");
        }

        // 页面信息
        $pageData = [
            'title' => $categoryInfo->name . ' - 新闻资讯',
            'keywords' => $categoryInfo->name . ',新闻,资讯',
            'description' => $categoryInfo->description ?: $categoryInfo->name . '相关新闻资讯',
            'canonical' => ConfigService::url("/news/category/{$category}")
        ];

        return view('news/category', [
            'pageData' => $pageData,
            'categoryInfo' => $categoryInfo,
            'newsList' => $newsList,
            'categories' => $categories,
            'hotNews' => $hotNews,
            'pagination' => $newsList->render()
        ]);
    }

    /**
     * 新闻详情
     */
    public function newsDetail($slug)
    {
        // 获取站点配置
        $siteConfig = ConfigService::getAll();
        
        // 根据slug获取新闻详情
        $news = News::with('category')
            ->where('slug', $slug)
            ->where('status', 1)
            ->find();
        
        if (!$news) {
            abort(404, '新闻不存在');
        }
        
        // 处理新闻图片URL
        if (!empty($news['image'])) {
            $news['image'] = ConfigService::asset($news['image']);
        }
        
        // 获取相关新闻（同分类的其他文章）
        $relatedNews = News::where('category_id', $news->category_id)
            ->where('id', '<>', $news->id)
            ->where('status', 1)
            ->order('published_at', 'desc')
            ->limit(3)
            ->select();
        
        // 处理相关新闻URLs
        foreach ($relatedNews as &$related) {
            if (!empty($related['image'])) {
                $related['image'] = ConfigService::asset($related['image']);
            }
        }
        
        // 获取最新文章
        $latestNews = News::where('status', 1)
            ->where('id', '<>', $news->id)
            ->order('published_at', 'desc')
            ->limit(5)
            ->select();
        
        // 处理最新文章URLs
        foreach ($latestNews as &$latest) {
            if (!empty($latest['image'])) {
                $latest['image'] = ConfigService::asset($latest['image']);
            }
        }
        
        // 获取热门文章（按阅读量排序）
        $popularNews = News::where('status', 1)
            ->where('id', '<>', $news->id)
            ->order('views', 'desc')
            ->order('published_at', 'desc')
            ->limit(5)
            ->select();
        
        // 处理热门文章URLs
        foreach ($popularNews as &$popular) {
            if (!empty($popular['image'])) {
                $popular['image'] = ConfigService::asset($popular['image']);
            }
        }
        
        // 获取新闻分类
        $categories = \app\model\NewsCategory::where('status', 1)
            ->withCount('news')
            ->order('sort_order', 'desc')
            ->select();
        
        // 页面信息
        $pageData = [
            'pageTitle' => $news->title . ' - 新闻详情 - ' . ($siteConfig['site_title'] ?? ''),
            'pageDescription' => $news->summary ?: mb_substr(strip_tags($news->content), 0, 150, 'UTF-8'),
            'pageKeywords' => $news->tags . ',' . ($siteConfig['site_keywords'] ?? ''),
            'bodyClass' => 'news-detail-page'
        ];

        return view('news/detail', [
            'pageData' => $pageData,
            'news' => $news,
            'relatedNews' => $relatedNews,
            'latestNews' => $latestNews,
            'popularNews' => $popularNews,
            'categories' => $categories,
            'siteConfig' => $siteConfig
        ]);
    }
    
    /**
     * 增加新闻阅读量
     */
    public function newsView($id)
    {
        if ($this->request->isAjax()) {
            $news = News::find($id);
            if ($news) {
                $news->views = ($news->views ?? 0) + 1;
                $news->save();
                return json(['success' => true]);
            }
        }
        return json(['success' => false]);
    }
    
    /**
     * AJAX获取新闻列表
     */
    public function ajaxList()
    {
        if (!$this->request->isAjax()) {
            return json(['success' => false, 'message' => '非法请求']);
        }
        
        try {
            // 获取当前分类和标签
            $currentCategory = $this->request->param('category', '');
            $currentTag = $this->request->param('tag', '');
            
            // 获取分页参数
            $page = $this->request->param('page', 1);
            $limit = 8;
            
            // 构建查询条件
            $where = ['status' => 1];
            $newsQuery = News::with('category')->where($where);
            
            if ($currentCategory) {
                $category = \app\model\NewsCategory::where('slug', $currentCategory)->find();
                if ($category) {
                    $newsQuery->where('category_id', $category->id);
                }
            }
            
            if ($currentTag) {
                // 通过标签查询新闻
                $newsQuery->where('tags', 'like', '%' . $currentTag . '%');
            }
            
            // 获取新闻列表
            $newsList = $newsQuery
                ->order('sort_order', 'desc')
                ->order('published_at', 'desc')
                ->paginate([
                    'list_rows' => $limit,
                    'page' => $page
                ]);
            
            // 处理新闻URLs
            foreach ($newsList as &$news) {
                if (!empty($news['image'])) {
                    $news['image'] = ConfigService::asset($news['image']);
                }
                $news['detail_url'] = ConfigService::url("/news/{$news['slug']}");
            }
            
            // 渲染新闻列表HTML
            $html = $this->renderNewsList($newsList);
            
            return json([
                'success' => true,
                'html' => $html,
                'pagination' => $newsList->render(),
                'total' => $newsList->total()
            ]);
            
        } catch (\Exception $e) {
            return json(['success' => false, 'message' => $e->getMessage()]);
        }
    }
    
    /**
     * 渲染新闻列表HTML
     */
    private function renderNewsList($newsList)
    {
        $html = '<div class="news-grid" id="news-grid">';
        
        if ($newsList->count() > 0) {
            foreach ($newsList as $key => $news) {
                $delay = $key * 100;
                $featuredBadge = $news['is_featured'] ? '<div class="featured-badge"><i class="fas fa-star"></i><span>精选</span></div>' : '';
                $author = $news['author'] ? '<div class="news-author"><i class="fas fa-user"></i><span>' . htmlspecialchars($news['author']) . '</span></div>' : '';
                
                $html .= '
                <article class="news-item" data-aos="fade-up" data-aos-delay="' . $delay . '">
                    <div class="news-card">
                        <div class="news-image">
                            <a href="' . $news['detail_url'] . '">
                                <img src="' . ($news['image'] ?: '/assets/images/news/default.jpg') . '" alt="' . htmlspecialchars($news['title']) . '" loading="lazy">
                                <div class="image-overlay">
                                    <div class="overlay-content">
                                        <i class="fas fa-eye"></i>
                                        <span>阅读详情</span>
                                    </div>
                                </div>
                            </a>
                            ' . $featuredBadge . '
                        </div>
                        <div class="news-content">
                            <div class="news-meta">
                                <div class="meta-item category">
                                    <i class="fas fa-tag"></i>
                                    <span>' . ($news['category']['name'] ?? '公司动态') . '</span>
                                </div>
                                <div class="meta-item date">
                                    <i class="fas fa-clock"></i>
                                    <time datetime="' . $news['published_at'] . '">' . date('Y-m-d', strtotime($news['published_at'] ?: $news['created_at'])) . '</time>
                                </div>
                                <div class="meta-item views">
                                    <i class="fas fa-eye"></i>
                                    <span>' . ($news['views'] ?: 0) . '</span>
                                </div>
                            </div>
                            <h3 class="news-title">
                                <a href="' . $news['detail_url'] . '">' . htmlspecialchars($news['title']) . '</a>
                            </h3>
                            <p class="news-excerpt">' . htmlspecialchars(mb_substr(strip_tags($news['summary'] ?: $news['content']), 0, 120, 'UTF-8')) . '...</p>
                            <div class="news-actions">
                                <a href="' . $news['detail_url'] . '" class="read-more-btn">
                                    <span>阅读全文</span>
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                                ' . $author . '
                            </div>
                        </div>
                    </div>
                </article>';
            }
        } else {
            $html .= '
            <div class="no-news-found">
                <div class="no-news-content">
                    <div class="no-news-icon">
                        <i class="fas fa-newspaper"></i>
                    </div>
                    <h4>暂无相关新闻</h4>
                    <p>该分类下暂时没有新闻内容，请关注其他分类或稍后再来查看。</p>
                    <a href="/news" class="btn-back-all">
                        <i class="fas fa-arrow-left"></i>
                        返回全部新闻
                    </a>
                </div>
            </div>';
        }
        
        $html .= '</div>';
        
        // 添加分页
        if ($newsList->hasPages()) {
            $html .= '<div class="pagination-wrapper"><div class="pagination-container">' . $newsList->render() . '</div></div>';
        }
        
        return $html;
    }
    
    /**
     * 获取热门标签
     */
    private function getPopularTags()
    {
        try {
            // 使用缓存避免重复计算
            $cacheKey = 'popular_tags_cache';
            $popularTags = Cache::get($cacheKey);

            if ($popularTags !== false && is_array($popularTags)) {
                return $popularTags;
            }

            // 使用SQL聚合优化性能
            $tagCounts = [];
            $newsWithTags = News::where('status', 1)
                ->where('tags', '<>', '')
                ->whereNotNull('tags')
                ->column('tags');

            // 优化：使用单次遍历和预分配数组
            $allTags = [];
            foreach ($newsWithTags as $tags) {
                $tagArray = array_filter(array_map('trim', explode(',', $tags)));
                $allTags = array_merge($allTags, $tagArray);
            }

            // 使用array_count_values一次性统计
            $tagCounts = array_count_values(array_filter($allTags));

            // 按使用次数排序，取前8个
            arsort($tagCounts);
            $popularTags = [];
            $count = 0;

            foreach ($tagCounts as $tag => $tagCount) {
                if ($count >= 8) break;
                $popularTags[] = [
                    'name' => $tag,
                    'count' => $tagCount
                ];
                $count++;
            }

            // 缓存10分钟
            Cache::set($cacheKey, $popularTags, 600);

            return $popularTags;

        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 案例列表
     */
    public function cases()
    {
        // 获取站点配置
        $siteConfig = ConfigService::getAll();

        // 获取当前行业筛选
        $currentIndustry = $this->request->param('industry', '');

        // 获取分页参数
        $page = $this->request->param('page', 1);
        $limit = 12; // 每页显示12个案例

        // 构建查询条件
        $where = ['status' => 1];
        $casesQuery = Cases::where($where);

        if ($currentIndustry) {
            $casesQuery->where('industry', $currentIndustry);
        }

        // 获取案例列表
        $casesList = $casesQuery
            ->order('created_at', 'desc')
            ->order('id', 'desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

        // 处理案例URLs
        foreach ($casesList as &$case) {
            if (!empty($case['image'])) {
                $case['image'] = ConfigService::asset($case['image']);
            }
            $case['detail_url'] = ConfigService::url("/cases/{$case['slug']}");
        }

        // 获取所有行业（用于筛选）
        $industries = Cases::where('status', 1)
            ->where('industry', '<>', '')
            ->group('industry')
            ->column('industry');

        // 获取推荐案例
        $featuredCases = Cases::where('status', 1)
            ->where('is_featured', 1)
            ->order('created_at', 'desc')
            ->limit(6)
            ->select();

        // 处理推荐案例URLs
        foreach ($featuredCases as &$featured) {
            if (!empty($featured['image'])) {
                $featured['image'] = ConfigService::asset($featured['image']);
            }
            $featured['detail_url'] = ConfigService::url("/cases/{$featured['slug']}");
        }

        // 页面信息
        $pageData = [
            'pageTitle' => '客户案例 - ' . ($siteConfig['site_title'] ?? ''),
            'pageTitlee' => '客户案例',
            'pageDescription' => '展示我们的成功案例，见证专业实力 - ' . ($siteConfig['site_description'] ?? ''),
            'pageKeywords' => '客户案例,成功案例,项目展示,' . ($siteConfig['site_keywords'] ?? ''),
            'bodyClass' => 'cases-page'
        ];

        return view('cases/index', [
            'pageData' => $pageData,
            'casesList' => $casesList,
            'industries' => $industries,
            'currentIndustry' => $currentIndustry,
            'pagination' => $casesList->render(),
            'featuredCases' => $featuredCases,
            'siteConfig' => $siteConfig
        ]);
    }

    /**
     * Ajax获取案例列表
     */
    public function ajaxCases()
    {
        // 获取筛选参数
        $currentIndustry = $this->request->param('industry', '');
        $page = $this->request->param('page', 1);
        $limit = 12;

        // 构建查询条件
        $where = ['status' => 1];
        $casesQuery = Cases::where($where);

        if ($currentIndustry) {
            $casesQuery->where('industry', $currentIndustry);
        }

        // 获取案例列表
        $casesList = $casesQuery
            ->order('created_at', 'desc')
            ->order('id', 'desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

        // 处理案例URLs
        foreach ($casesList as &$case) {
            if (!empty($case['image'])) {
                $case['image'] = ConfigService::asset($case['image']);
            }
            $case['detail_url'] = ConfigService::url("/cases/{$case['slug']}");
        }

        // 获取所有行业（用于筛选）
        $industries = Cases::where('status', 1)
            ->where('industry', '<>', '')
            ->group('industry')
            ->column('industry');

        return json([
            'code' => 200,
            'message' => 'success',
            'data' => [
                'cases' => $casesList->items(),
                'pagination' => [
                    'total' => $casesList->total(),
                    'per_page' => $casesList->listRows(),
                    'current_page' => $casesList->currentPage(),
                    'last_page' => $casesList->lastPage(),
                    'has_more' => $casesList->hasPages()
                ],
                'industries' => $industries,
                'current_industry' => $currentIndustry
            ]
        ]);
    }

    /**
     * 案例详情
     */
    public function caseDetail($slug)
    {
        // 获取站点配置
        $siteConfig = ConfigService::getAll();

        // 根据slug获取案例详情
        $case = Cases::where('slug', $slug)
            ->where('status', 1)
            ->find();

        if (!$case) {
            abort(404, '案例不存在');
        }

        // 增加浏览次数
        $case->save(['views' => $case->views + 1]);

        // 处理案例图片URL
        if (!empty($case['image'])) {
            $case['image'] = ConfigService::asset($case['image']);
        }

        // 获取相关案例（同行业的其他案例）
        $relatedCases = Cases::where('industry', $case->industry)
            ->where('id', '<>', $case->id)
            ->where('status', 1)
            ->order('created_at', 'desc')
            ->limit(4)
            ->select();

        // 处理相关案例URLs
        foreach ($relatedCases as &$related) {
            if (!empty($related['image'])) {
                $related['image'] = ConfigService::asset($related['image']);
            }
            $related['detail_url'] = ConfigService::url("/cases/{$related['slug']}");
        }

        // 获取最新案例
        $latestCases = Cases::where('status', 1)
            ->where('id', '<>', $case->id)
            ->order('created_at', 'desc')
            ->limit(6)
            ->select();

        // 处理最新案例URLs
        foreach ($latestCases as &$latest) {
            if (!empty($latest['image'])) {
                $latest['image'] = ConfigService::asset($latest['image']);
            }
            $latest['detail_url'] = ConfigService::url("/cases/{$latest['slug']}");
        }

        // 页面信息
        $pageData = [
            'pageTitle' => $case->title . ' - 案例详情 - ' . ($siteConfig['site_title'] ?? ''),
            'pageDescription' => $case->summary ?: mb_substr(strip_tags($case->description), 0, 150, 'UTF-8'),
            'pageKeywords' => $case->industry . ',客户案例,' . ($siteConfig['site_keywords'] ?? ''),
            'bodyClass' => 'case-detail-page'
        ];

        return view('cases/detail', [
            'pageData' => $pageData,
            'case' => $case,
            'relatedCases' => $relatedCases,
            'latestCases' => $latestCases,
            'siteConfig' => $siteConfig
        ]);
    }

    /**
     * DIY页面展示
     */
    public function diyPage($slug)
    {
        // 获取站点配置
        $siteConfig = ConfigService::getAll();

        // 根据slug查找页面模板
        $template = \app\model\PageTemplate::where('slug', $slug)
            ->where('status', 1)
            ->find();

        if (!$template) {
            abort(404, '页面不存在');
        }

        // 页面信息
        $pageData = [
            'pageTitle' => $template->name . ' - ' . ($siteConfig['site_name'] ?? ''),
            'pageDescription' => $template->description ?: ($siteConfig['site_description'] ?? ''),
            'pageKeywords' => $siteConfig['site_keywords'] ?? '',
            'bodyClass' => 'diy-page'
        ];

        return view('diy/page', [
            'pageData' => $pageData,
            'template' => $template,
            'siteConfig' => $siteConfig
        ]);
    }

    /**
     * DIY页面预览（通过ID）
     */
    public function diyPreview($id)
    {
        // 获取站点配置
        $siteConfig = ConfigService::getAll();

        // 根据ID查找页面模板
        $template = \app\model\PageTemplate::find($id);

        if (!$template) {
            abort(404, '页面不存在');
        }

        // 页面信息
        $pageData = [
            'pageTitle' => $template->name . ' - 预览',
            'pageDescription' => $template->description ?: '页面预览',
            'pageKeywords' => '',
            'bodyClass' => 'diy-preview'
        ];

        return view('diy/page', [
            'pageData' => $pageData,
            'template' => $template,
            'siteConfig' => $siteConfig,
            'isPreview' => true
        ]);
    }

    /**
     * 清理缓存
     */
    public function clearCache()
    {
        try {
            // 清理热门标签缓存
            Cache::delete('popular_tags_cache');

            // 清理配置缓存
            ConfigService::clearCache();

            return json(['success' => true, 'message' => '缓存清理成功']);
        } catch (\Exception $e) {
            return json(['success' => false, 'message' => '缓存清理失败：' . $e->getMessage()]);
        }
    }
}

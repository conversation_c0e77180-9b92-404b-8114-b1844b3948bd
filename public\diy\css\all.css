/* ========================================
   页面构建器样式文件
   包含所有组件样式和3D效果样式
   ======================================== */

/* ========================================
   首页特色服务区域样式
   用于Card组件的系统风格模式
   ======================================== */
.features-section {
    padding: 6rem 0;
    position: relative;
    z-index: 5;
}

.features-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.3);
    z-index: 1;
}

.feature-item {
    padding: 2.5rem 1.5rem;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.12), 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.18), 0 4px 12px rgba(0, 0, 0, 0.08);
}

.feature-item-overlay {
    background: #ffffff !important;
    border-radius: 16px !important;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.12), 0 2px 8px rgba(0, 0, 0, 0.04) !important;
    padding: 2.5rem 1.5rem !important;
    transition: all 0.3s ease !important;
    height: 100% !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
}

.feature-item-overlay:hover {
    transform: translateY(-10px) !important;
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.18), 0 4px 12px rgba(0, 0, 0, 0.08) !important;
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
    color: white;
}

.feature-item h5 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1rem;
}

.feature-item p {
    color: #4a5568;
    line-height: 1.6;
    margin-bottom: 0;
}

/* 图片背景下的文字颜色覆盖 */
.features-section[style*="background-image"] .feature-item h5 {
    color: white !important;
}

.features-section[style*="background-image"] .feature-item p {
    color: rgba(255,255,255,0.8) !important;
}

/* 首页标题样式 */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #37474f;
    margin-bottom: 1rem;
    text-align: center;
}

.section-subtitle {
    font-size: 1.2rem;
    color: rgba(255,255,255,0.9);
    text-align: center;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Bootstrap容器样式 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col-12 {
    flex: 0 0 100%;
    max-width: 100%;
    padding: 0 15px;
}

.col-lg-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
    padding: 0 15px;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 15px;
}

.mb-4 {
    margin-bottom: 1.5rem;
}

.mb-5 {
    margin-bottom: 3rem;
}

.text-center {
    text-align: center;
}

.py-5 {
    padding-top: 3rem;
    padding-bottom: 3rem;
}

/* 响应式布局 */
@media (max-width: 991px) {
    .col-lg-4 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .section-title {
        font-size: 2rem;
    }

    .section-subtitle {
        font-size: 1.1rem;
    }
}

@media (max-width: 767px) {
    .col-lg-4,
    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .features-section {
        padding: 4rem 0;
    }

    .feature-item {
        padding: 2rem 1rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .section-subtitle {
        font-size: 1rem;
    }
}

/* ========================================
   背景图组件样式
   用于页面背景设置，支持渐变和图片背景
   ======================================== */
.bg-component {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 80px 20px;
    text-align: center;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 100%;
    position: relative;
}

.bg-component.image-bg {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.bg-component .bg-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.3);
    z-index: 1;
}

.bg-component .bg-content {
    position: relative;
    z-index: 2;
}

.bg-preview {
    width: 100%;
    height: 60px;
    border-radius: 6px;
    margin-top: 10px;
    border: 2px solid #e2e8f0;
    cursor: pointer;
    transition: all 0.2s ease;
}

.bg-preview:hover {
    border-color: #667eea;
    transform: scale(1.02);
}

.bg-options {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
    margin-top: 10px;
}

.bg-option {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s ease;
}

.bg-option:hover {
    border-color: #667eea;
    transform: scale(1.1);
}

.bg-option.selected {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* ========================================
   导航栏组件样式
   包含Logo、菜单项、按钮等导航元素
   ======================================== */
.navbar-component {
    background: white;
    padding: 15px 40px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 1;
    gap: 30px;
}

.navbar-logo {
    font-size: 24px;
    font-weight: bold;
    color: #2d3748;
    flex-shrink: 0;
    flex: 0 0 auto;
}



.navbar-logo img {
    height: 40px;
    width: auto;
}

.navbar-menu {
    display: flex;
    gap: 10px;
    align-items: center;
    flex: 0 1 auto;
    justify-content: center;
    margin: 0 20px;
}

.navbar-item {
    color: #2d3748;
    text-decoration: none;
    font-size: 16px;
    transition: all 0.2s ease !important;
    padding: 8px 16px;
    border-radius: 6px;
    white-space: nowrap;
    border: 2px solid transparent !important;
    position: relative !important;
    overflow: hidden !important;
}

/* navbar-item hover styles are now dynamically generated by navbar.js */



.navbar-buttons {
    display: flex;
    gap: 15px;
    flex-shrink: 0;
    flex: 0 0 auto;
}

.navbar-btn {
    padding: 10px 20px;
    border: 2px solid;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease !important;
    position: relative !important;
    overflow: hidden !important;
}

.navbar-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.navbar-btn:hover {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15) !important;
    filter: brightness(1.05) !important;
}

.navbar-btn:hover::before {
    left: 100%;
}

/* 响应式导航栏 */
@media (max-width: 768px) {
    .navbar-component {
        flex-direction: column;
        gap: 20px;
        padding: 15px 20px;
    }

    .navbar-menu {
        gap: 15px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .navbar-item {
        font-size: 14px;
        padding: 6px 12px;
    }

    .navbar-buttons {
        gap: 10px;
    }

    .navbar-btn {
        padding: 8px 16px;
        font-size: 12px;
    }
}

/* ========================================
   英雄区组件样式
   全屏展示区域，包含导航栏、标题、副标题和按钮
   ======================================== */
.hero-component {
    color: white;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.hero-navbar {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 40px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    gap: 30px;
}

.hero-logo {
    font-size: 24px;
    font-weight: bold;
    color: white;
    flex-shrink: 0;
    flex: 0 0 auto;
}

.hero-logo img {
    height: 40px;
    width: auto;
}

.hero-nav-menu {
    display: flex;
    gap: 10px;
    align-items: center;
    flex: 0 1 auto;
    justify-content: center;
    margin: 0 20px;
}

.nav-item {
    color: white;
    text-decoration: none;
    font-size: 16px;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 6px;
    white-space: nowrap;
}

.nav-item {
    transition: all 0.2s ease !important;
    border-radius: 6px !important;
    border: 2px solid transparent !important;
    position: relative !important;
    overflow: hidden !important;
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 107, 107, 0.4), transparent);
    transition: left 0.5s ease;
}

.nav-item:hover {
    border: 2px solid #ff6b6b !important;
    background: rgba(255, 107, 107, 0.1) !important;
    color: #ff6b6b !important;
}

.nav-item:hover::before {
    left: 100%;
}

.hero-nav-buttons {
    display: flex;
    gap: 15px;
    flex-shrink: 0;
    flex: 0 0 auto;
}

.hero-nav-btn {
    padding: 10px 20px;
    border: 2px solid;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease !important;
    position: relative !important;
    overflow: hidden !important;
}

.hero-nav-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.hero-nav-btn:hover {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15) !important;
    filter: brightness(1.05) !important;
}

.hero-nav-btn:hover::before {
    left: 100%;
}

/* 英雄区导航栏固定顶部样式 */
.hero-navbar.sticky {
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.hero-navbar.sticky.shadow {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.hero-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 80px 40px;
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 20px;
    font-weight: 700;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 40px;
    opacity: 0.9;
    max-width: 600px;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    justify-content: center;
}

.hero-btn {
    padding: 15px 30px;
    border: 2px solid;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.2s ease !important;
    backdrop-filter: blur(10px);
    position: relative !important;
    overflow: hidden !important;
}

.hero-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.hero-btn:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
    filter: brightness(1.05) !important;
}

.hero-btn:hover::before {
    left: 100%;
}

/* ========================================
   区块组件样式 - 重构版
   现代化通用内容区块，支持多种风格和高级自定义
   前端预览样式，控制台样式在style.css中
   ======================================== */
.section-component {
    padding: 80px 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    max-width: 900px;
    margin: 20px auto;
    border-radius: 16px;
    position: relative;
    overflow: hidden;
    min-height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 10px 40px rgba(0,0,0,0.15);
}

/* Section悬停效果已移除 */

/* Section背景图片支持 */
.section-component.image-bg {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.section-component.image-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.section-component.image-bg > * {
    position: relative;
    z-index: 2;
}

/* Section图标样式 */
.section-icon {
    margin-bottom: 20px;
    display: block;
}

.section-icon i {
    font-size: 48px;
    color: white;
    display: inline-block;
    transition: all 0.3s ease;
    font-style: normal !important;
    text-align: center;
}

/* Section标题样式 */
.section-title {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
    color: white;
    transition: all 0.3s ease;
}

/* Section内容样式 */
.section-content {
    font-size: 18px;
    line-height: 1.6;
    margin-bottom: 30px;
    opacity: 0.9;
    max-width: 600px;
    color: white;
    transition: all 0.3s ease;
}

/* Section风格预设样式 */
.section-style-presets {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-top: 8px;
}

.section-style-preset {
    padding: 12px 8px;
    border-radius: 8px;
    cursor: pointer;
    text-align: center;
    border: 2px solid transparent;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.section-style-preset:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.section-style-preset.selected {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

/* 添加选中状态的对号 */
.section-style-preset.selected::after {
    content: '✓';
    position: absolute;
    top: 4px;
    right: 4px;
    background: #667eea;
    color: white;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.section-style-preset span {
    font-size: 11px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

/* Section按钮容器 */
.section-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Section按钮基础样式 - 借鉴英雄区 */
.section-btn {
    padding: 12px 24px;
    border: 2px solid;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    position: relative;
    overflow: hidden;
}

/* Section按钮光效 */
.section-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.section-btn:hover::before {
    left: 100%;
}

/* Section按钮自动化颜色处理 - 借鉴英雄区方式 */

/* Section响应式样式 */
@media (max-width: 768px) {
    .section-component {
        padding: 40px 20px;
        margin: 10px;
        border-radius: 12px;
    }

    .section-title {
        font-size: 28px;
    }

    .section-content {
        font-size: 16px;
    }

    .section-buttons {
        justify-content: center;
        flex-direction: column;
        align-items: center;
        gap: 12px;
    }

    .section-btn {
        width: 100%;
        max-width: 200px;
        text-align: center;
    }
}

/* ========================================
   动画效果样式
   为区块组件提供各种入场动画
   ======================================== */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.5);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* ========================================
   卡片组件样式 - 多卡片网格系统
   支持1-4列布局、多种模式、图片、图标的灵活卡片系统
   ======================================== */

/* 卡片容器 */
.cards-container {
    display: grid;
    gap: 20px;
    margin: 20px 0;
}

/* 单个卡片项 */
.card-item {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.card-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

/* 图片容器 */
.card-image-container {
    overflow: hidden;
}

.card-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    display: block;
    transition: transform 0.3s ease;
}

.card-item:hover .card-image {
    transform: scale(1.05);
}

/* 图标容器 */
.card-icon-container {
    text-align: center;
    margin-bottom: 16px;
}

.card-icon {
    font-size: 48px;
    display: inline-block;
}

/* 卡片内容 */
.card-content {
    position: relative;
}

.card-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 12px;
    line-height: 1.3;
}

.card-text {
    font-size: 14px;
    line-height: 1.6;
    opacity: 0.8;
    margin-bottom: 16px;
}

.card-btn {
    display: inline-block;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
}

.card-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* ========================================
   新闻卡片样式 - 最新动态模板专用
   ======================================== */
.news-card {
    background: #ffffff;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.4s ease;
    border: 1px solid #e2e8f0;
    height: 100%;
    min-height: 450px;
}

.news-card:hover {
    transform: translateY(-15px) scale(1.03);
    box-shadow: 0 30px 80px rgba(0, 0, 0, 0.35);
    border: 3px solid rgba(0, 123, 255, 0.6);
    background: rgba(255, 255, 255, 0.99);
}

.news-image {
    position: relative;
    overflow: hidden;
    height: 240px;
    border-radius: 15px 15px 0 0;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.4s ease;
    filter: brightness(1.05) contrast(1.1) saturate(1.05);
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    image-rendering: optimizeQuality;
}

.news-card:hover .news-image img {
    transform: scale(1.08);
    filter: brightness(1.1) contrast(1.15) saturate(1.1);
}

.news-date {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    padding: 10px 15px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.news-date .day {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #007bff;
    line-height: 1;
}

.news-date .month {
    display: block;
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
    margin-top: 2px;
}

.news-content {
    padding: 25px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 180px;
}

.news-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 12px;
    color: #6c757d;
}

.news-category {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 12px;
    border-radius: 20px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.news-time {
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.news-title {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 15px;
    line-height: 1.3;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.news-title a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
    text-shadow: inherit;
}

.news-title a:hover {
    color: #007bff;
    text-shadow: 0 1px 3px rgba(0, 123, 255, 0.2);
}

.news-excerpt {
    color: #4a5568;
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 20px;
}

.news-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.read-more-btn {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    text-decoration: none;
    font-weight: 500;
    font-size: 13px;
    padding: 8px 16px;
    border-radius: 20px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    border: none;
}

.read-more-btn:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.news-stats {
    color: #6c757d;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    gap: 3px;
}

.news-more-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    color: white;
    text-decoration: none;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.news-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    color: white;
    text-decoration: none;
}

/* 布局模式样式 */
.layout-image-left,
.layout-image-right {
    display: flex;
    align-items: flex-start;
    gap: 20px;
}

.layout-image-left .card-image-container {
    flex: 0 0 45%;
    margin: 0;
}

.layout-image-right .card-image-container {
    flex: 0 0 45%;
    margin: 0;
    order: 2;
}

.layout-image-left .card-content,
.layout-image-right .card-content {
    flex: 1;
    min-width: 0; /* 防止内容溢出 */
}

.layout-image-right .card-content {
    order: 1;
}

.layout-image-left .card-image,
.layout-image-right .card-image {
    height: 150px;
    min-height: 150px;
}

.layout-icon-top {
    text-align: center;
}

.layout-icon-top .card-content {
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .layout-image-left,
    .layout-image-right {
        flex-direction: column;
    }

    .layout-image-left .card-image-container,
    .layout-image-right .card-image-container {
        flex: none;
        order: 0;
        margin-bottom: 16px;
    }

    .layout-image-right .card-content {
        order: 0;
    }

    .layout-image-left .card-image,
    .layout-image-right .card-image {
        height: 200px;
    }
}

/* 卡片宽度变体 */
.card-component.width-small {
    max-width: 300px;
}

.card-component.width-medium {
    max-width: 400px;
}

.card-component.width-large {
    max-width: 600px;
}

.card-component.width-auto {
    max-width: 100%;
}

/* ========================================
   页脚组件样式
   页面底部信息展示区域
   ======================================== */
.footer-component {
    background: #2d3748;
    color: white;
    padding: 40px 20px;
    text-align: center;
}

.footer-content h4 {
    font-size: 20px;
    margin-bottom: 10px;
    font-weight: 600;
}

.footer-content p {
    font-size: 14px;
    margin-bottom: 15px;
    opacity: 0.9;
}

.footer-links {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.footer-links a {
    color: #a0aec0;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.2s ease !important;
    border-radius: 4px !important;
    padding: 4px 8px !important;
    border: 2px solid transparent !important;
    position: relative !important;
    overflow: hidden !important;
}

.footer-links a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.footer-links a:hover {
    color: #ffffff !important;
    border: 2px solid #ffffff !important;
    background: rgba(255, 255, 255, 0.1) !important;
}

.footer-links a:hover::before {
    left: 100%;
}

/* ========================================
   页脚组件风格1样式 - 企业版
   模仿首页底部的专业企业风格
   ======================================== */
.footer-component.style1 {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%) !important;
    color: white !important;
    padding: 0 !important;
    margin: 0 !important;
    position: relative !important;
    overflow: hidden !important;
    min-height: 400px !important;
    width: 100% !important;
}

/* 页脚装饰背景 */
.footer-component.style1::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.03)"><polygon points="0,0 1000,0 1000,100 0,80"/></svg>') no-repeat top;
    background-size: cover;
    z-index: 1;
}

.footer-main-style1 {
    position: relative;
    z-index: 2;
    padding: 40px 0 20px;
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(10px);
}

.container-style1 {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.row-style1 {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

/* 第一列（公司信息）较宽，包含Logo、描述和二维码 */
.col-style1:first-child {
    flex: 0 0 26%;
    max-width: 26%;
    padding: 0 15px;
    margin-bottom: 30px;
}

/* 第二、三、四列（解决方案、产品服务、帮助支持）较窄 */
.col-style1:nth-child(2),
.col-style1:nth-child(3),
.col-style1:nth-child(4) {
    flex: 0 0 16%;
    max-width: 16%;
    padding: 0 15px;
    margin-bottom: 30px;
}

/* 第五列（联系我们）最宽 */
.col-style1:nth-child(5) {
    flex: 0 0 26%;
    max-width: 26%;
    padding: 0 15px;
    margin-bottom: 30px;
}

.footer-widget-style1 {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 22px;
    height: 100%;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.footer-widget-style1:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}



.footer-logo-style1 {
    margin-bottom: 15px;
}

.footer-logo-style1 img {
    max-height: 45px;
    width: auto;
    filter: brightness(0) invert(1);
    transition: all 0.3s ease;
}

.footer-widget-style1:hover .footer-logo-style1 img {
    transform: scale(1.05);
}

.footer-desc-style1 {
    color: rgba(255, 255, 255, 0.8);
    font-size: 13px;
    line-height: 1.5;
    margin-bottom: 20px;
}

.footer-qrcode-style1 {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

/* 二维码装饰背景 */
.footer-qrcode-style1::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(79, 195, 247, 0.1), rgba(41, 182, 246, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.footer-qrcode-style1:hover::before {
    opacity: 1;
}

.footer-qrcode-style1:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(79, 195, 247, 0.2);
}

.qrcode-img-style1 {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    margin-bottom: 8px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.footer-qrcode-style1:hover .qrcode-img-style1 {
    transform: scale(1.05);
}

.qrcode-text-style1 {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    margin: 0;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.footer-qrcode-style1:hover .qrcode-text-style1 {
    color: white;
}

.footer-title-style1 {
    color: white;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 25px;
    position: relative;
    padding-bottom: 12px;
    text-align: center;
}

/* 为中间三个部分的标题添加图标 */
.footer-widget-style1:nth-child(2) .footer-title-style1::before {
    content: '🔧';
    margin-right: 8px;
    font-size: 16px;
}

.footer-widget-style1:nth-child(3) .footer-title-style1::before {
    content: '📦';
    margin-right: 8px;
    font-size: 16px;
}

.footer-widget-style1:nth-child(4) .footer-title-style1::before {
    content: '🛠️';
    margin-right: 8px;
    font-size: 16px;
}

.footer-title-style1::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 2px;
    background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
    border-radius: 2px;
}

.footer-links-style1 {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links-style1 li {
    margin-bottom: 8px;
}

.footer-links-style1 a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 13px;
    transition: all 0.3s ease;
    display: block;
    padding: 5px 0;
    position: relative;
}

/* 链接前的装饰图标 */
.footer-links-style1 a::before {
    content: '▶';
    position: absolute;
    left: -12px;
    color: #4fc3f7;
    font-size: 8px;
    opacity: 0;
    transition: all 0.3s ease;
}

.footer-links-style1 a:hover {
    color: #4fc3f7;
    padding-left: 15px;
}

.footer-links-style1 a:hover::before {
    opacity: 1;
}



.footer-contact-style1 {
    space-y: 15px;
}

.contact-item-style1 {
    display: flex;
    align-items: center;
    padding: 10px 14px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-left: 3px solid #4fc3f7;
    border-radius: 8px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-height: 40px;
}

.contact-item-style1:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(8px);
    box-shadow: 0 8px 20px rgba(79, 195, 247, 0.25);
}

.icon-style1 {
    width: 16px;
    height: 16px;
    margin-right: 10px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.contact-item-style1 span {
    color: rgba(255, 255, 255, 0.9);
    font-size: 13px;
    font-weight: 400;
    line-height: 1.2;
    flex: 1;
    transition: color 0.3s ease;
}

.contact-item-style1:hover span {
    color: white;
}

/* 地址项特殊样式 - 橙色 */
.contact-item-style1:first-child {
    border-left-color: #ff6b35;
}

.contact-item-style1:first-child .icon-style1 {
    color: #ff6b35;
}

/* 电话项特殊样式 - 绿色 */
.contact-item-style1:nth-child(2) {
    border-left-color: #4caf50;
}

.contact-item-style1:nth-child(2) .icon-style1 {
    color: #4caf50;
}

/* 邮箱项特殊样式 - 紫色 */
.contact-item-style1:nth-child(3) {
    border-left-color: #9c27b0;
}

.contact-item-style1:nth-child(3) .icon-style1 {
    color: #9c27b0;
}

/* QQ项特殊样式 - 橙黄色 */
.contact-item-style1:nth-child(4) {
    border-left-color: #ff9800;
}

.contact-item-style1:nth-child(4) .icon-style1 {
    color: #ff9800;
}

.footer-bottom-style1 {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    z-index: 2;
    backdrop-filter: blur(10px);
}

/* 底部装饰线 */
.footer-bottom-style1::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 2px;
    background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 50%, #ff6b35 100%);
    border-radius: 2px;
}

.col-md-6-style1 {
    flex: 0 0 50%;
    max-width: 50%;
}

.copyright-style1 {
    text-align: center;
}

.copyright-style1 p {
    margin: 0;
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
    transition: all 0.3s ease;
    position: relative;
}

.copyright-style1:hover p {
    color: rgba(255, 255, 255, 0.8);
    text-shadow: 0 0 10px rgba(79, 195, 247, 0.3);
}

.footer-links-bottom-style1 {
    display: flex;
    justify-content: center;
    gap: 25px;
}

.footer-links-bottom-style1 a {
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    position: relative;
    padding: 5px 10px;
    border-radius: 4px;
}

.footer-links-bottom-style1 a::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, #4fc3f7, #29b6f6);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.footer-links-bottom-style1 a:hover {
    color: #4fc3f7;
    background: rgba(79, 195, 247, 0.1);
    transform: translateY(-2px);
}

.footer-links-bottom-style1 a:hover::before {
    width: 80%;
}

/* 风格1响应式设计 */
@media (max-width: 1024px) and (min-width: 769px) {
    .col-style1:first-child {
        flex: 0 0 28%;
        max-width: 28%;
    }

    .col-style1:nth-child(2),
    .col-style1:nth-child(3),
    .col-style1:nth-child(4) {
        flex: 0 0 15%;
        max-width: 15%;
    }

    .col-style1:nth-child(5) {
        flex: 0 0 27%;
        max-width: 27%;
    }
}

@media (max-width: 768px) {
    .col-style1 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .footer-main-style1 {
        padding: 30px 0 15px;
    }

    .footer-widget-style1 {
        margin-bottom: 20px;
        padding: 18px;
    }

    .col-md-6-style1 {
        flex: 0 0 100%;
        max-width: 100%;
        text-align: center;
    }

    .footer-links-bottom-style1 {
        justify-content: center;
        gap: 15px;
    }
}



/* ========================================
   动态图案样式
   英雄区背景动态效果
   ======================================== */

/* 动态图案容器 */
.dynamic-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
    overflow: hidden;
}

/* 浮动圆圈 */
.pattern-circle {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float-circle 6s ease-in-out infinite;
}

@keyframes float-circle {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
    50% { transform: translateY(-20px) rotate(180deg); opacity: 0.8; }
}

/* 气泡图案 */
.pattern-bubble {
    position: absolute;
    bottom: -50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    animation: bubble-rise 6s linear infinite;
}

@keyframes bubble-rise {
    0% {
        transform: translateY(0) scale(0.5);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-50vh) scale(1);
        opacity: 0.6;
    }
    100% {
        transform: translateY(-100vh) scale(0.3);
        opacity: 0;
    }
}

/* 粒子图案 */
.pattern-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: particle-pattern 3s linear infinite;
}

@keyframes particle-pattern {
    0% { transform: translateY(0) scale(1); opacity: 1; }
    100% { transform: translateY(-100px) scale(0); opacity: 0; }
}

/* 叶子图案 */
.pattern-leaf {
    position: absolute;
    top: -20px;
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 0 100% 0 100%;
    animation: leaf-pattern 8s linear infinite;
}

@keyframes leaf-pattern {
    0% { transform: translateY(-20px) rotate(0deg); opacity: 1; }
    100% { transform: translateY(100vh) rotate(360deg); opacity: 0; }
}

/* 火花图案 */
.pattern-spark {
    position: absolute;
    width: 3px;
    height: 3px;
    background: #ff6b6b;
    border-radius: 50%;
    animation: spark-pattern 2s ease-out infinite;
}

@keyframes spark-pattern {
    0% { transform: scale(0) rotate(0deg); opacity: 1; }
    50% { transform: scale(1) rotate(180deg); opacity: 0.8; }
    100% { transform: scale(0) rotate(360deg); opacity: 0; }
}

/* 火焰图案 */
.pattern-flame {
    position: absolute;
    bottom: 0;
    width: 20px;
    height: 40px;
    background: linear-gradient(to top, #ff4b2b, #ff416c, transparent);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    animation: flame-pattern 2s ease-in-out infinite;
}

@keyframes flame-pattern {
    0%, 100% { transform: scaleY(1) scaleX(1); }
    50% { transform: scaleY(1.2) scaleX(0.8); }
}



/* 网格图案 */
.pattern-grid-dot {
    position: absolute;
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: grid-pattern 4s ease-in-out infinite;
}

@keyframes grid-pattern {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.5); }
}

/* 风沙图案 */
.pattern-windstorm {
    position: absolute;
    background: rgba(210, 180, 140, 0.6);
    border-radius: 50%;
    animation: windstorm-pattern 5s ease-in-out infinite;
}

@keyframes windstorm-pattern {
    0% {
        transform: translateX(0) translateY(0) rotate(0deg);
        opacity: 0.3;
    }
    25% {
        transform: translateX(30px) translateY(-20px) rotate(90deg);
        opacity: 0.8;
    }
    50% {
        transform: translateX(-20px) translateY(15px) rotate(180deg);
        opacity: 0.6;
    }
    75% {
        transform: translateX(40px) translateY(-10px) rotate(270deg);
        opacity: 0.9;
    }
    100% {
        transform: translateX(0) translateY(0) rotate(360deg);
        opacity: 0.3;
    }
}

/* 矩阵图案 */
.pattern-matrix {
    position: absolute;
    top: -20px;
    width: 2px;
    height: 20px;
    background: linear-gradient(to bottom, #00ff41, transparent);
    animation: matrix-pattern 3s linear infinite;
}

@keyframes matrix-pattern {
    0% { transform: translateY(-20px); opacity: 1; }
    100% { transform: translateY(100vh); opacity: 0; }
}

/* ========================================
   文本块组件样式
   最基础的内容展示组件，支持标题、正文、列表、引用等
   ======================================== */
.textblock-component {
    max-width: 800px;
    margin: 20px auto;
    padding: 40px;
    background: #f7fafc;
    border-radius: 8px;
    text-align: left;
    transition: all 0.3s ease;
}

.textblock-title {
    font-size: 32px;
    color: #2d3748;
    font-weight: bold;
    margin-bottom: 15px;
    line-height: 1.2;
}

.textblock-subtitle {
    font-size: 20px;
    color: #4a5568;
    font-weight: normal;
    margin-bottom: 20px;
    line-height: 1.3;
}

.textblock-content p {
    font-size: 16px;
    color: #4a5568;
    line-height: 1.6;
    margin-bottom: 15px;
}

.textblock-list {
    font-size: 16px;
    color: #4a5568;
    margin: 20px 0;
    padding-left: 20px;
}

.textblock-list li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.textblock-quote {
    font-size: 18px;
    color: #667eea;
    font-style: italic;
    margin: 30px 0;
    padding: 20px;
    border-left: 4px solid #667eea;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 4px;
}

.textblock-quote cite {
    display: block;
    margin-top: 10px;
    font-size: 14px;
    font-style: normal;
    opacity: 0.8;
}

.textblock-buttons {
    margin-top: 30px;
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.textblock-btn {
    padding: 12px 24px;
    border: 2px solid;
    border-radius: 6px;
    text-decoration: none;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
    display: inline-block;
    position: relative;
    overflow: hidden;
}

.textblock-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.textblock-btn.primary {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.textblock-btn.primary:hover {
    background: transparent;
    color: #667eea;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    filter: brightness(1.05);
}

.textblock-btn.primary:hover::before {
    left: 100%;
}

.textblock-btn.secondary {
    background: transparent;
    color: #4a5568;
    border-color: #4a5568;
}

.textblock-btn.secondary:hover {
    background: #4a5568;
    color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    filter: brightness(1.05);
}

.textblock-btn.secondary:hover::before {
    left: 100%;
}

/* 响应式设计 */
/* 快速颜色选择器样式 */
.color-palette-group {
    display: flex;
    align-items: center;
    gap: 12px;
}

.quick-colors {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

.quick-color-item {
    width: 40px;
    height: 20px;
    border-radius: 4px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s ease;
    position: relative;
}

.quick-color-item:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.quick-color-item.selected {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
}

.quick-color-item.selected::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 背景选择器样式 */
.bg-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    margin-top: 8px;
}

.bg-option {
    width: 100%;
    height: 40px;
    border-radius: 6px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s ease;
    position: relative;
}

.bg-option:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.bg-option.selected {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
}

.bg-option.selected::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 14px;
    font-weight: bold;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
}

.bg-preview {
    width: 100%;
    height: 20px;
    border-radius: 4px;
    margin-top: 8px;
    border: 1px solid #e2e8f0;
}

@media (max-width: 768px) {
    .textblock-component {
        padding: 20px;
        max-width: 100%;
    }

    .textblock-title {
        font-size: 24px;
    }

    .textblock-content p {
        font-size: 14px;
    }

    .textblock-buttons {
        justify-content: center;
    }

    .textblock-btn {
        padding: 10px 20px;
        font-size: 14px;
    }

    /* 移动端快速颜色选择器优化 */
    .quick-colors {
        gap: 4px;
    }

    .quick-color-item {
        width: 32px;
        height: 18px;
    }

    .bg-options {
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
    }

    .bg-option {
        height: 35px;
    }
}

/* ========================================
   统计数字组件样式
   专业的数据统计展示组件，支持多个统计项的可视化展示
   ======================================== */

/* 统计数字容器 */
.stats-container {
    display: grid;
    gap: 24px;
    margin: 20px auto;
    max-width: 1200px;
    padding: 0;
}

/* 单个统计项 */
.stats-item {
    background: white;
    border-radius: 12px;
    padding: 32px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
    border: 1px solid #e2e8f0;
}

.stats-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

/* 统计图标 */
.stats-icon {
    font-size: 48px;
    margin-bottom: 12px;
    display: block;
    color: #667eea;
}

/* 统计数字 */
.stats-number {
    font-size: 36px;
    font-weight: bold;
    color: #2d3748;
    margin-bottom: 8px;
    display: block;
    line-height: 1.2;
}

/* 统计标签 */
.stats-label {
    font-size: 16px;
    color: #4a5568;
    font-weight: normal;
    display: block;
    line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-container {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 16px;
        margin: 15px;
    }

    .stats-item {
        padding: 24px 16px;
    }

    .stats-icon {
        font-size: 36px;
        margin-bottom: 8px;
    }

    .stats-number {
        font-size: 28px;
        margin-bottom: 6px;
    }

    .stats-label {
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .stats-container {
        grid-template-columns: 1fr !important;
        gap: 12px;
        margin: 10px;
    }

    .stats-item {
        padding: 20px 12px;
    }

    .stats-icon {
        font-size: 32px;
    }

    .stats-number {
        font-size: 24px;
    }

    .stats-label {
        font-size: 13px;
    }
}

/* ========================================
   实力见证模板样式 (Stats Strength Template)
   深色主题的统计数字展示，带有玻璃拟态效果和特殊装饰
   ======================================== */

/* 实力见证包装器 */
.stats-wrapper {
    position: relative;
    width: 100%;
    padding: 100px 0;
    overflow: hidden;
    min-height: 600px;
    /* 确保背景只在组件内部显示 */
    isolation: isolate;
}

/* 实力见证背景层 */
.stats-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

/* 实力见证叠加层 */
.stats-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

/* 实力见证标题区域 */
.stats-header {
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
    z-index: 3;
}

/* 实力见证主标题 */
.stats-title {
    font-size: 3rem !important;
    font-weight: 700 !important;
    color: white !important;
    margin-bottom: 1rem !important;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
    display: block !important;
}

/* 实力见证副标题 */
.stats-subtitle {
    font-size: 1.2rem !important;
    color: rgba(255, 255, 255, 0.8) !important;
    margin-bottom: 2rem !important;
    display: block !important;
}

/* 实力见证装饰线 */
.stats-divider {
    width: 100px;
    height: 4px;
    background: linear-gradient(135deg, #10d5c2 0%, #007bff 100%);
    margin: 0 auto;
    border-radius: 2px;
    display: block;
}

/* 实力见证内容区域 */
.stats-content {
    position: relative;
    z-index: 3;
    text-align: center;
}

/* 实力见证信任标识 */
.trust-indicators {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 40px;
    margin-top: 5rem;
    padding: 40px 0;
    flex-wrap: wrap;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    z-index: 4;
}

/* 实力见证装饰线增强效果 */
.stats-divider::before {
    content: '';
    position: absolute;
    top: -3px;
    left: 50%;
    transform: translateX(-50%);
    width: 10px;
    height: 10px;
    background: #10d5c2;
    border-radius: 50%;
    box-shadow: 0 0 20px rgba(16, 213, 194, 0.5);
}

.trust-item {
    display: flex;
    align-items: center;
    gap: 10px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.trust-item:hover {
    color: #10d5c2;
    transform: translateY(-2px);
}

.trust-item i {
    font-size: 1.5rem;
    color: #10d5c2;
}

/* 实力见证统计项 */
.stats-item-strength {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 20px !important;
    padding: 40px 20px !important;
    text-align: center !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
    height: 100% !important;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3), 0 0 20px rgba(16, 213, 194, 0.1) !important;
}

.stats-item-strength:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 8px 30px rgba(0,0,0,0.4), 0 0 30px rgba(16, 213, 194, 0.3) !important;
}

/* 实力见证图标容器 */
.stats-item-strength .stat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

/* 实力见证图标圆形背景 */
.stats-item-strength .stats-icon-circle {
    width: 92px !important;
    height: 92px !important;
    min-width: 92px !important;
    min-height: 92px !important;
    background: linear-gradient(135deg, #10d5c2 0%, #007bff 100%) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 auto 20px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(16, 213, 194, 0.3) !important;
    position: relative !important;
    flex-shrink: 0 !important;
}

/* 实力见证图标圆形背景悬停效果 */
.stats-item-strength:hover .stats-icon-circle {
    transform: scale(1.1) !important;
    box-shadow: 0 0 20px rgba(16, 213, 194, 0.5) !important;
}

/* 实力见证图标样式 */
.stats-item-strength .stats-icon-circle i[data-lucide] {
    width: 36px !important;
    height: 36px !important;
    color: #ffffff !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    top: 10px !important;
}

/* 实力见证Emoji图标样式 */
.stats-item-strength .stats-icon-circle {
    font-size: 36px !important;
    color: #ffffff !important;
    line-height: 1 !important;
    position: relative !important;
    top: 10px !important;
}

/* 实力见证内容区域 */
.stats-item-strength .stat-content {
    position: relative;
    z-index: 2;
}

/* 实力见证图标区域 */
.stats-item-strength .stat-icon {
    position: relative;
    z-index: 2;
}

/* 实力见证数字样式 */
.stats-item-strength .stats-number {
    font-size: 3.5rem !important;
    font-weight: 700 !important;
    color: white !important;
    margin-bottom: 0 !important;
    line-height: 1 !important;
    display: inline-block !important;
}

/* 实力见证后缀样式 */
.stats-item-strength .stat-plus {
    font-size: 2rem !important;
    color: #10d5c2 !important;
    font-weight: 700 !important;
    margin-left: 5px !important;
}

/* 实力见证标签样式 */
.stats-item-strength .stats-label {
    font-size: 1.2rem !important;
    font-weight: 600 !important;
    color: white !important;
    margin: 15px 0 10px !important;
    display: block !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
}

/* 实力见证描述样式 */
.stats-item-strength .stat-description {
    font-size: 0.9rem !important;
    color: rgba(255, 255, 255, 0.7) !important;
    font-weight: normal !important;
    margin-top: 8px !important;
}

/* 实力见证装饰泡泡 - 悬停时显示 */
.stats-item-strength .stat-decoration {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, rgba(16, 213, 194, 0.3) 0%, rgba(0, 123, 255, 0.3) 100%);
    border-radius: 50%;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 1;
}

.stats-item-strength:hover .stat-decoration {
    opacity: 1;
    transform: scale(1.2);
    animation: pulse-decoration 2s infinite;
}

/* 装饰泡泡脉冲动画 */
@keyframes pulse-decoration {
    0%, 100% {
        transform: scale(1.2);
        opacity: 1;
    }
    50% {
        transform: scale(1.4);
        opacity: 0.7;
    }
}

/* 实力见证响应式设计 */
@media (max-width: 768px) {
    /* 包装器响应式 */
    .stats-wrapper {
        padding: 60px 0;
        min-height: 400px;
    }

    /* 标题响应式 */
    .stats-title {
        font-size: 2rem !important;
    }

    .stats-subtitle {
        font-size: 1rem !important;
    }

    /* 统计项响应式 */
    .stats-item-strength {
        padding: 30px 15px !important;
        margin-bottom: 20px !important;
    }

    /* 图标圆形背景响应式 */
    .stats-item-strength .stats-icon-circle {
        width: 72px !important;
        height: 72px !important;
        min-width: 72px !important;
        min-height: 72px !important;
        margin-bottom: 15px !important;
    }

    /* 图标响应式 */
    .stats-item-strength .stats-icon-circle i[data-lucide] {
        width: 28px !important;
        height: 28px !important;
    }

    .stats-item-strength .stats-icon-circle {
        font-size: 28px !important;
    }

    /* 数字和标签响应式 */
    .stats-item-strength .stats-number {
        font-size: 2.5rem !important;
    }

    .stats-item-strength .stat-plus {
        font-size: 1.5rem !important;
    }

    .stats-item-strength .stats-label {
        font-size: 1rem !important;
    }

    /* 信任标识响应式 */
    .trust-indicators {
        gap: 20px;
        margin-top: 3rem;
        padding: 30px 0;
    }
}

/* ========================================
   团队介绍组件样式
   专业的团队成员展示组件，支持成员信息、头像、职位、社交链接等展示
   ======================================== */

/* 团队容器 */
.team-container {
    display: grid;
    gap: 24px;
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

/* 团队成员卡片 */
.team-member {
    background: #ffffff;
    border-radius: 12px;
    padding: 24px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.team-member:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* 头像容器 */
.member-avatar-container {
    margin-bottom: 16px;
    display: flex;
    justify-content: center;
}

.member-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #e2e8f0;
    transition: all 0.3s ease;
}

.team-member:hover .member-avatar {
    border-color: #667eea;
    transform: scale(1.05);
}

/* 成员信息 */
.member-info {
    text-align: center;
}

.member-name {
    font-size: 20px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
    line-height: 1.3;
}

.member-position {
    font-size: 14px;
    font-weight: 500;
    color: #667eea;
    margin-bottom: 12px;
}

.member-bio {
    font-size: 14px;
    color: #4a5568;
    line-height: 1.6;
    margin-bottom: 16px;
    opacity: 0.8;
}

/* 社交链接 */
.member-social {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 16px;
}

.social-link {
    font-size: 18px;
    cursor: pointer;
    opacity: 0.7;
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 50%;
    background: #f7fafc;
}

.social-link:hover {
    opacity: 1;
    transform: translateY(-2px);
    background: #e2e8f0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .team-container {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 16px;
        padding: 16px;
    }

    .team-member {
        padding: 20px;
    }

    .member-avatar {
        width: 100px;
        height: 100px;
    }

    .member-name {
        font-size: 18px;
    }

    .member-position {
        font-size: 13px;
    }

    .member-bio {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .team-container {
        grid-template-columns: 1fr !important;
        gap: 12px;
        padding: 12px;
    }

    .team-member {
        padding: 16px;
    }

    .member-avatar {
        width: 80px;
        height: 80px;
    }

    .member-name {
        font-size: 16px;
    }

    .member-position {
        font-size: 12px;
    }

    .member-bio {
        font-size: 12px;
    }

    .social-link {
        font-size: 16px;
        padding: 6px;
    }
}

/* ========================================
   客户评价组件样式
   专业的客户评价展示组件，支持评价内容、客户信息、星级评分等展示
   ======================================== */

/* 客户评价容器 */
.testimonials-container {
    display: grid;
    gap: 24px;
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

/* 客户评价卡片 */
.testimonial-item {
    background: #ffffff;
    border-radius: 12px;
    padding: 24px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    height: 100%;
}

.testimonial-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* 评价内容 */
.testimonial-content {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* 星级评分 */
.testimonial-rating {
    margin-top: 8px;
    font-size: 16px;
    display: flex;
    align-items: center;
}

.testimonial-rating .star {
    color: #ffd700;
    margin-right: 1px;
    font-size: inherit;
    line-height: 1;
    display: inline-block;
    transition: all 0.2s ease;
}

.testimonial-rating .star:not(.active) {
    color: #e2e8f0;
}

.testimonial-rating .star:hover {
    transform: scale(1.1);
}

/* 评价文本 */
.testimonial-text {
    font-size: 16px;
    color: #4a5568;
    line-height: 1.6;
    margin-bottom: 20px;
    font-style: italic;
    flex: 1;
}

/* 作者信息 */
.testimonial-author {
    display: flex;
    align-items: center;
    margin-top: auto;
}

.author-avatar-container {
    margin-right: 12px;
    flex-shrink: 0;
}

.author-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #e2e8f0;
    transition: all 0.3s ease;
}

.testimonial-item:hover .author-avatar {
    border-color: #667eea;
    transform: scale(1.05);
}

.author-info {
    flex: 1;
    min-width: 0;
}

.author-name {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 4px;
    line-height: 1.3;
}

.author-company {
    font-size: 14px;
    font-weight: 500;
    color: #667eea;
    margin-bottom: 2px;
}

.author-position {
    font-size: 12px;
    color: #718096;
    opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .testimonials-container {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 16px;
        padding: 16px;
    }

    .testimonial-item {
        padding: 20px;
    }

    .author-avatar {
        width: 50px;
        height: 50px;
    }

    .author-name {
        font-size: 16px;
    }

    .author-company {
        font-size: 13px;
    }

    .author-position {
        font-size: 11px;
    }

    .testimonial-text {
        font-size: 14px;
    }

    .testimonial-rating {
        font-size: 14px;
        margin-top: 6px;
    }
}

@media (max-width: 480px) {
    .testimonials-container {
        grid-template-columns: 1fr !important;
        gap: 12px;
        padding: 12px;
    }

    .testimonial-item {
        padding: 16px;
    }

    .author-avatar {
        width: 40px;
        height: 40px;
    }

    .author-name {
        font-size: 14px;
    }

    .author-company {
        font-size: 12px;
    }

    .author-position {
        font-size: 10px;
    }

    .testimonial-text {
        font-size: 13px;
        margin-bottom: 16px;
    }

    .testimonial-rating {
        font-size: 12px;
        margin-top: 4px;
    }
}

/* 团队组件样式已简化，移除弹窗相关代码 */

/* 社交信息样式 - 简洁舒适设计 */
.member-social {
    margin-top: 18px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
}

.social-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 5px 10px;
    border-radius: 16px;
    background: #f8fafc;
    /* border: 1px solid #e2e8f0; */
    transition: all 0.2s ease;
    font-size: 13px;
    color: #4a5568;
}

.social-item:hover {
    background: #f1f5f9;
    border-color: #cbd5e0;
    transform: translateY(-0.5px);
}

.social-item .social-icon {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    flex-shrink: 0;
    color: white;
    font-weight: 500;
}

.social-item .social-text {
    font-weight: 500;
    line-height: 1.4;
    white-space: nowrap;
}

.wechat-item .social-icon {
    background: #10b981;
}

.wechat-item:hover .social-icon {
    background: #059669;
}

.email-item .social-icon {
    background: #3b82f6;
}

.email-item:hover .social-icon {
    background: #2563eb;
}

.email-item .social-text {
    font-size: 11px;
}

/* 响应式设计 */
@media (max-width: 768px) {


    /* 社交信息移动端适配 */
    .member-social {
        flex-direction: column;
        gap: 6px;
    }

    .social-item {
        padding: 4px 8px;
        gap: 5px;
        font-size: 11px;
    }

    .social-item .social-icon {
        width: 18px;
        height: 18px;
        font-size: 9px;
    }

    .email-item .social-text {
        font-size: 10px;
    }
}

    .testimonial-author {
        margin-top: 12px;
    }

    .author-avatar-container {
        margin-right: 8px;
    }

/* ========================================
   联系信息组件样式
   专业的联系方式展示组件，支持多种联系方式的展示和交互功能
   ======================================== */

/* 联系信息容器 */
.contact-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 32px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    max-width: 1200px;
    margin: 20px auto;
}

/* 联系信息背景图布局 */
.contact-container .contact-content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    min-width: 0;
    padding-right: 24px;
}

.contact-container .contact-background-area {
    flex: 0 0 45%;
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    min-height: 250px;
    background-color: #f8fafc;
}

.contact-background-image {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

/* 强制水平布局当有背景图时 */
.contact-container:has(.contact-background-area) {
    display: flex !important;
    flex-direction: row !important;
    align-items: stretch;
    min-height: 300px;
}

.contact-container:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

/* 布局模式 */
.contact-container.layout-horizontal {
    flex-direction: row;
    flex-wrap: wrap;
}

.contact-container.layout-grid {
    display: grid;
}

/* 联系信息项 */
.contact-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    cursor: default;
    background: transparent;
}

.contact-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-color: #e2e8f0;
}

/* 联系信息图标 */
.contact-icon {
    font-size: 24px;
    margin-right: 12px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #667eea;
}

/* 联系信息内容 */
.contact-content {
    flex: 1;
}

.contact-label {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
    opacity: 0.8;
    color: #4a5568;
}

.contact-value {
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
    color: #2d3748;
}



/* 响应式设计 */
@media (max-width: 768px) {
    .contact-container {
        padding: 20px;
        margin: 15px;
        gap: 12px;
    }

    .contact-container.layout-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    .contact-container.layout-horizontal {
        flex-direction: column;
    }

    /* 移动端背景图布局调整 */
    .contact-container:has(.contact-background-area) {
        flex-direction: column !important;
        min-height: auto;
    }

    .contact-container .contact-content-wrapper {
        padding-right: 0;
        margin-bottom: 16px;
    }

    .contact-container .contact-background-area {
        flex: none;
        min-height: 150px;
    }

    .contact-item {
        padding: 12px;
    }

    .contact-icon {
        font-size: 20px;
        margin-right: 10px;
    }

    .contact-label {
        font-size: 12px;
    }

    .contact-value {
        font-size: 14px;
    }


}

@media (max-width: 480px) {
    .contact-container {
        padding: 16px;
        margin: 10px;
        gap: 8px;
    }

    .contact-container.layout-grid {
        grid-template-columns: 1fr !important;
    }

    .contact-item {
        padding: 10px;
    }

    .contact-icon {
        font-size: 18px;
        margin-right: 8px;
    }

    .contact-label {
        font-size: 11px;
    }

    .contact-value {
        font-size: 13px;
    }


}

/* ========================================
   样式模板组件前端预览样式
   统一管理页面主题风格的高级组件
   ======================================== */

/* 样式模板组件基础样式 */
.styletemplates-component {
    background: #f8fafc;
    padding: 40px;
    border-radius: 16px;
    max-width: 1200px;
    margin: 20px auto;
    position: relative;
}

/* 模板头部样式 */
.templates-header {
    text-align: center;
    margin-bottom: 40px;
}

.templates-icon {
    margin-bottom: 16px;
}

.templates-icon i {
    font-size: 48px;
    display: inline-block;
}

.templates-title {
    color: #2d3748;
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 12px;
    line-height: 1.2;
}

.templates-subtitle {
    color: #4a5568;
    font-size: 18px;
    margin-bottom: 0;
    line-height: 1.5;
}

/* 模板网格布局 */
.templates-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    margin-bottom: 40px;
}

/* 模板卡片样式 */
.template-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.template-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.template-card.selected {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

/* 模板预览样式 */
.template-preview {
    width: 100%;
    height: 80px;
    border-radius: 8px;
    margin-bottom: 16px;
    background-size: cover;
    background-position: center;
    position: relative;
    overflow: hidden;
}

.template-preview::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    border-radius: 8px;
}



.template-name {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 6px;
    line-height: 1.3;
}

.template-desc {
    font-size: 13px;
    color: #718096;
    margin: 0;
    line-height: 1.4;
}

/* 模板元信息 */
.template-meta {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin: 12px 0 16px 0;
}

.template-badge {
    background: #f7fafc;
    color: #4a5568;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid #e2e8f0;
}

/* 模板信息区域 */
.template-info {
    margin-bottom: 8px;
}

/* 模板操作按钮区域 */
.template-actions {
    margin-top: 16px;
    width: 100%;
}

/* 全宽应用按钮 */
.btn-apply-full {
    width: 100%;
    padding: 12px 16px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.btn-apply-full:hover {
    background: #5a67d8;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-apply-full:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

/* 白光扫过效果 */
.btn-apply-full::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s ease;
}

.btn-apply-full:hover::before {
    left: 100%;
}

/* 模板应用按钮（兼容旧样式） */
.template-apply-btn {
    width: 100%;
    padding: 10px 16px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.template-apply-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.template-apply-btn:active {
    transform: translateY(0);
}

.template-apply-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.template-apply-btn:hover::before {
    left: 100%;
}

/* 模板底部提示 */
.templates-footer {
    border-top: 1px solid #e2e8f0;
    padding-top: 32px;
}

.templates-tips h4 {
    color: #2d3748;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
}

.templates-tips ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.templates-tips li {
    color: #4a5568;
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 6px;
    padding-left: 0;
}

.templates-tips li:last-child {
    margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .templates-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 16px;
    }
    
    .template-card {
        padding: 16px;
    }
    
    .template-preview {
        height: 60px;
    }
    
    .templates-title {
        font-size: 28px;
    }
    
    .templates-subtitle {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .templates-grid {
        grid-template-columns: 1fr !important;
    }
    
    .styletemplates-component {
        padding: 20px;
        margin: 10px;
    }
    
    .templates-title {
        font-size: 24px;
    }
    
    .templates-subtitle {
        font-size: 15px;
    }
}

/* ========================================
   导航栏下拉菜单样式 - DIY组件支持
   ======================================== */

/* 下拉菜单容器 */
.navbar-component .navbar-dropdown {
    position: relative !important;
    display: inline-block !important;
}

/* 下拉菜单触发器 */
.navbar-component .dropdown-toggle {
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    cursor: pointer !important;
    text-decoration: none !important;
    color: inherit !important;
}

/* 下拉箭头 */
.navbar-component .dropdown-arrow {
    font-size: 10px !important;
    transition: transform 0.3s ease !important;
    margin-left: 4px !important;
    color: inherit !important;
}

/* 悬停时箭头旋转 */
.navbar-component .navbar-dropdown:hover .dropdown-arrow {
    transform: rotate(180deg) !important;
}

/* 下拉菜单面板 */
.navbar-component .navbar-dropdown .dropdown-menu {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    background: #ffffff !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    border-radius: 8px !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    min-width: 200px !important;
    z-index: 10000 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateY(-10px) !important;
    transition: all 0.3s ease !important;
    padding: 0 !important;
    margin-top: 8px !important;
    display: block !important;
    overflow: hidden !important;
}

/* 悬停时显示下拉菜单 */
.navbar-component .navbar-dropdown:hover .dropdown-menu {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
}

/* 下拉菜单项 */
.navbar-component .navbar-dropdown .dropdown-item {
    display: block !important;
    padding: 12px 20px !important;
    text-decoration: none !important;
    font-size: 14px !important;
    font-weight: 400 !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
    width: 100% !important;
    box-sizing: border-box !important;
    margin: 0 !important;
}

.navbar-component .navbar-dropdown .dropdown-item:first-child {
    border-top-left-radius: 8px !important;
    border-top-right-radius: 8px !important;
}

.navbar-component .navbar-dropdown .dropdown-item:last-child {
    border-bottom: none !important;
    border-bottom-left-radius: 8px !important;
    border-bottom-right-radius: 8px !important;
}

/* 下拉菜单项悬停效果 - 支持CSS变量和默认颜色 */
.navbar-component .navbar-dropdown .dropdown-item:hover {
    background: var(--navbar-hover-color, #667eea) !important;
    color: white !important;
    padding-left: 25px !important;
    transform: none !important;
}

/* 下拉菜单项光柱划过效果 */
.navbar-component .navbar-dropdown .dropdown-item::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent) !important;
    transition: left 0.6s ease !important;
    z-index: 1 !important;
}

.navbar-component .navbar-dropdown .dropdown-item:hover::before {
    left: 100% !important;
}

/* 确保文字在光柱之上 */
.navbar-component .navbar-dropdown .dropdown-item {
    position: relative !important;
    z-index: 2 !important;
}

/* 响应式下拉菜单 */
@media (max-width: 768px) {
    .navbar-component .navbar-dropdown {
        display: block !important;
        position: static !important;
    }

    .navbar-component .navbar-dropdown .dropdown-menu {
        position: static !important;
        opacity: 1 !important;
        visibility: visible !important;
        transform: none !important;
        box-shadow: none !important;
        border: none !important;
        background: rgba(255, 255, 255, 0.1) !important;
        margin: 8px 0 !important;
        border-radius: 6px !important;
        backdrop-filter: blur(10px) !important;
        -webkit-backdrop-filter: blur(10px) !important;
    }

    .navbar-component .navbar-dropdown .dropdown-item {
        border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
        padding: 8px 20px !important;
    }

    .navbar-component .navbar-dropdown .dropdown-item:hover {
        background: rgba(102, 126, 234, 0.2) !important;
        color: #667eea !important;
        padding-left: 25px !important;
    }

    .navbar-component .dropdown-arrow {
        display: none !important;
    }
}

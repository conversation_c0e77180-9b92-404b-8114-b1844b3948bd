<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 用户管理控制器
 */

declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\model\User;
use app\service\UserService;
use app\validate\UserValidate;
use think\Response;

/**
 * 用户管理控制器
 */
class UserController extends BaseController
{
    protected UserService $userService;

    public function __construct()
    {
        parent::__construct();
        $this->userService = new UserService();
    }

    /**
     * 用户列表
     * @return Response
     */
    public function index(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('user.view')) {
                return $this->error('没有权限访问', 403);
            }

            $params = $this->getPaginateParams();
            $result = $this->userService->getList($params);

            // 记录操作日志
            $this->logOperation('view', 'user_list');

            return $this->paginate($result);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 用户详情
     * @param int $id 用户ID
     * @return Response
     */
    public function read(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('user.view')) {
                return $this->error('没有权限访问', 403);
            }

            $user = $this->userService->getById($id);
            if (!$user) {
                return $this->error('用户不存在', 404);
            }

            // 记录操作日志
            $this->logOperation('view', 'user', $id);

            return $this->success($user);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 创建用户
     * @return Response
     */
    public function save(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('user.create')) {
                return $this->error('没有权限创建用户', 403);
            }

            $data = $this->post();

            // 数据验证
            $this->validate($data, UserValidate::class . '.create');

            // 创建用户
            $user = $this->userService->create($data);

            // 记录操作日志
            $this->logOperation('create', 'user', $user->id, $data);

            return $this->success($user, '用户创建成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 更新用户
     * @param int $id 用户ID
     * @return Response
     */
    public function update(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('user.update')) {
                return $this->error('没有权限更新用户', 403);
            }

            $data = $this->post();

            // 数据验证
            $this->validate($data, UserValidate::class . '.update');

            // 更新用户
            $user = $this->userService->update($id, $data);

            // 记录操作日志
            $this->logOperation('update', 'user', $id, $data);

            return $this->success($user, '用户更新成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除用户
     * @param int $id 用户ID
     * @return Response
     */
    public function delete(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('user.delete')) {
                return $this->error('没有权限删除用户', 403);
            }

            // 不能删除自己
            if ($id === $this->getUserId()) {
                return $this->error('不能删除自己');
            }

            // 删除用户
            $this->userService->delete($id);

            // 记录操作日志
            $this->logOperation('delete', 'user', $id);

            return $this->success([], '用户删除成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 批量删除用户
     * @return Response
     */
    public function batchDelete(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('user.delete')) {
                return $this->error('没有权限删除用户', 403);
            }

            $ids = $this->post('ids', []);
            $this->validateBatchDelete($ids);

            // 不能删除自己
            $currentUserId = $this->getUserId();
            if (in_array($currentUserId, $ids)) {
                return $this->error('不能删除自己');
            }

            // 批量删除
            $count = $this->userService->batchDelete($ids);

            // 记录操作日志
            $this->logOperation('batch_delete', 'user', null, ['ids' => $ids, 'count' => $count]);

            return $this->success(['count' => $count], "成功删除 {$count} 个用户");

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 启用用户
     * @param int $id 用户ID
     * @return Response
     */
    public function enable(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('user.update')) {
                return $this->error('没有权限操作', 403);
            }

            $this->userService->enable($id);

            // 记录操作日志
            $this->logOperation('enable', 'user', $id);

            return $this->success([], '用户启用成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 禁用用户
     * @param int $id 用户ID
     * @return Response
     */
    public function disable(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('user.update')) {
                return $this->error('没有权限操作', 403);
            }

            // 不能禁用自己
            if ($id === $this->getUserId()) {
                return $this->error('不能禁用自己');
            }

            $this->userService->disable($id);

            // 记录操作日志
            $this->logOperation('disable', 'user', $id);

            return $this->success([], '用户禁用成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 重置密码
     * @param int $id 用户ID
     * @return Response
     */
    public function resetPassword(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('user.reset_password')) {
                return $this->error('没有权限重置密码', 403);
            }

            $newPassword = $this->post('password');
            if (empty($newPassword)) {
                return $this->error('新密码不能为空');
            }

            // 验证密码强度
            if (strlen($newPassword) < 6) {
                return $this->error('密码长度不能少于6位');
            }

            $this->userService->resetPassword($id, $newPassword);

            // 记录操作日志
            $this->logOperation('reset_password', 'user', $id);

            return $this->success([], '密码重置成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 分配角色
     * @param int $id 用户ID
     * @return Response
     */
    public function assignRoles(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('user.assign_roles')) {
                return $this->error('没有权限分配角色', 403);
            }

            $roleIds = $this->post('role_ids', []);
            if (!is_array($roleIds)) {
                return $this->error('角色ID必须是数组');
            }

            $this->userService->assignRoles($id, $roleIds);

            // 记录操作日志
            $this->logOperation('assign_roles', 'user', $id, ['role_ids' => $roleIds]);

            return $this->success([], '角色分配成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取用户统计
     * @return Response
     */
    public function statistics(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('user.view')) {
                return $this->error('没有权限访问', 403);
            }

            $stats = $this->userService->getStatistics();

            return $this->success($stats);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 导出用户
     * @return Response
     */
    public function export(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('user.export')) {
                return $this->error('没有权限导出', 403);
            }

            $params = $this->get();
            $filePath = $this->userService->export($params);

            // 记录操作日志
            $this->logOperation('export', 'user_list', null, $params);

            return $this->success(['file_path' => $filePath], '导出成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 导入用户
     * @return Response
     */
    public function import(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('user.import')) {
                return $this->error('没有权限导入', 403);
            }

            // 处理文件上传
            $fileInfo = $this->handleUpload('file', [
                'size' => 10 * 1024 * 1024, // 10MB
                'ext' => 'xlsx,xls,csv',
                'type' => 'import'
            ]);

            // 导入用户
            $result = $this->userService->import($fileInfo['path']);

            // 记录操作日志
            $this->logOperation('import', 'user_list', null, [
                'file' => $fileInfo['filename'],
                'result' => $result
            ]);

            return $this->success($result, '导入完成');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}

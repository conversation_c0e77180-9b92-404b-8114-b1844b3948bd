<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 角色表单对话框
-->

<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑角色' : '新增角色'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      size="large"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="角色名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入角色名称"
              clearable
              :disabled="loading"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="角色标识" prop="slug">
            <el-input
              v-model="formData.slug"
              placeholder="请输入角色标识"
              clearable
              :disabled="loading"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="角色描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入角色描述"
          :disabled="loading"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="排序" prop="sortOrder">
            <el-input-number
              v-model="formData.sortOrder"
              :min="0"
              :max="999"
              placeholder="排序值"
              style="width: 100%"
              :disabled="loading"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="默认角色" prop="isDefault">
            <el-switch
              v-model="formData.isDefault"
              :active-value="1"
              :inactive-value="0"
              active-text="是"
              inactive-text="否"
              :disabled="loading"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="权限配置" prop="permissions">
        <div class="permissions-container">
          <div class="permissions-header">
            <el-checkbox
              v-model="allSelected"
              :indeterminate="isIndeterminate"
              @change="handleSelectAll"
              :disabled="loading"
            >
              全选
            </el-checkbox>
            <el-button type="text" @click="expandAll" :disabled="loading">
              {{ allExpanded ? '收起全部' : '展开全部' }}
            </el-button>
          </div>
          
          <div class="permissions-content">
            <el-collapse v-model="activeGroups" class="permission-collapse">
              <el-collapse-item
                v-for="group in permissionGroups"
                :key="group.group"
                :title="group.group"
                :name="group.group"
              >
                <template #title>
                  <div class="group-title">
                    <el-checkbox
                      :model-value="isGroupSelected(group)"
                      :indeterminate="isGroupIndeterminate(group)"
                      @change="(val) => handleGroupChange(group, val)"
                      @click.stop
                      :disabled="loading"
                    />
                    <span class="group-name">{{ group.group }}</span>
                    <span class="group-count">({{ group.permissions.length }})</span>
                  </div>
                </template>
                
                <el-checkbox-group
                  v-model="formData.permissions"
                  class="permission-list"
                  :disabled="loading"
                >
                  <el-checkbox
                    v-for="permission in group.permissions"
                    :key="permission.key"
                    :label="permission.key"
                    class="permission-item"
                  >
                    <div class="permission-info">
                      <span class="permission-name">{{ permission.name }}</span>
                      <span class="permission-key">{{ permission.key }}</span>
                    </div>
                  </el-checkbox>
                </el-checkbox-group>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="loading">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, computed, watch, onMounted } from 'vue'
import { roleApi } from '@/api/role'
import type { FormInstance, FormRules } from 'element-plus'
import type { Role, PermissionGroup } from '@/types/auth'

interface Props {
  modelValue: boolean
  roleData?: Role | null
  isEdit: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInstance>()

// 状态
const loading = ref(false)
const permissionGroups = ref<PermissionGroup[]>([])
const activeGroups = ref<string[]>([])
const allExpanded = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单数据
const formData = reactive({
  name: '',
  slug: '',
  description: '',
  permissions: [] as string[],
  isDefault: 0,
  sortOrder: 0
})

// 验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '角色名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  slug: [
    { required: true, message: '请输入角色标识', trigger: 'blur' },
    { min: 2, max: 50, message: '角色标识长度在 2 到 50 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '角色标识只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '描述长度不能超过 200 个字符', trigger: 'blur' }
  ],
  sortOrder: [
    { type: 'number', min: 0, max: 999, message: '排序值必须在 0-999 之间', trigger: 'blur' }
  ]
}

// 全选状态
const allSelected = computed({
  get: () => {
    const allPermissions = getAllPermissions()
    return allPermissions.length > 0 && formData.permissions.length === allPermissions.length
  },
  set: (val) => {
    if (val) {
      formData.permissions = getAllPermissions()
    } else {
      formData.permissions = []
    }
  }
})

// 半选状态
const isIndeterminate = computed(() => {
  const allPermissions = getAllPermissions()
  return formData.permissions.length > 0 && formData.permissions.length < allPermissions.length
})

/**
 * 获取所有权限
 */
const getAllPermissions = (): string[] => {
  const permissions: string[] = []
  permissionGroups.value.forEach(group => {
    group.permissions.forEach(permission => {
      permissions.push(permission.key)
    })
  })
  return permissions
}

/**
 * 检查分组是否全选
 */
const isGroupSelected = (group: PermissionGroup): boolean => {
  const groupPermissions = group.permissions.map(p => p.key)
  return groupPermissions.every(key => formData.permissions.includes(key))
}

/**
 * 检查分组是否半选
 */
const isGroupIndeterminate = (group: PermissionGroup): boolean => {
  const groupPermissions = group.permissions.map(p => p.key)
  const selectedCount = groupPermissions.filter(key => formData.permissions.includes(key)).length
  return selectedCount > 0 && selectedCount < groupPermissions.length
}

/**
 * 处理全选
 */
const handleSelectAll = (val: boolean) => {
  allSelected.value = val
}

/**
 * 处理分组选择
 */
const handleGroupChange = (group: PermissionGroup, val: boolean) => {
  const groupPermissions = group.permissions.map(p => p.key)
  
  if (val) {
    // 添加分组权限
    groupPermissions.forEach(key => {
      if (!formData.permissions.includes(key)) {
        formData.permissions.push(key)
      }
    })
  } else {
    // 移除分组权限
    formData.permissions = formData.permissions.filter(key => !groupPermissions.includes(key))
  }
}

/**
 * 展开/收起全部
 */
const expandAll = () => {
  if (allExpanded.value) {
    activeGroups.value = []
  } else {
    activeGroups.value = permissionGroups.value.map(group => group.group)
  }
  allExpanded.value = !allExpanded.value
}

/**
 * 加载权限列表
 */
const loadPermissions = async () => {
  try {
    const response = await roleApi.getPermissions()
    permissionGroups.value = response.data
    
    // 默认展开第一个分组
    if (permissionGroups.value.length > 0) {
      activeGroups.value = [permissionGroups.value[0].group]
    }
  } catch (error) {
    console.error('加载权限列表失败:', error)
  }
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    const submitData = {
      name: formData.name,
      slug: formData.slug,
      description: formData.description,
      permissions: formData.permissions,
      is_default: formData.isDefault,
      sort_order: formData.sortOrder
    }

    if (props.isEdit && props.roleData) {
      // 编辑角色
      await roleApi.update(props.roleData.id, submitData)
      ElMessage.success('角色更新成功')
    } else {
      // 新增角色
      await roleApi.create(submitData)
      ElMessage.success('角色创建成功')
    }

    emit('success')

  } catch (error: any) {
    ElMessage.error(error.message || '操作失败')
  } finally {
    loading.value = false
  }
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  if (loading.value) return
  visible.value = false
  resetForm()
}

/**
 * 重置表单
 */
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, {
    name: '',
    slug: '',
    description: '',
    permissions: [],
    isDefault: 0,
    sortOrder: 0
  })
  activeGroups.value = []
  allExpanded.value = false
}

/**
 * 初始化表单数据
 */
const initFormData = () => {
  if (props.isEdit && props.roleData) {
    Object.assign(formData, {
      name: props.roleData.name,
      slug: props.roleData.slug,
      description: props.roleData.description || '',
      permissions: [...(props.roleData.permissions || [])],
      isDefault: props.roleData.is_default,
      sortOrder: props.roleData.sort_order
    })
  }
}

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal) {
    loadPermissions()
    initFormData()
  }
})

onMounted(() => {
  loadPermissions()
})
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.permissions-container {
  width: 100%;
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  overflow: hidden;
}

.permissions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: var(--el-fill-color-lighter);
  border-bottom: 1px solid var(--el-border-color-light);
}

.permissions-content {
  max-height: 400px;
  overflow-y: auto;
}

.permission-collapse {
  border: none;
  
  :deep(.el-collapse-item) {
    border-bottom: 1px solid var(--el-border-color-lighter);
    
    &:last-child {
      border-bottom: none;
    }
    
    .el-collapse-item__header {
      padding: 0 16px;
      height: 48px;
      background-color: var(--el-fill-color-extra-light);
      border-bottom: none;
    }
    
    .el-collapse-item__content {
      padding: 16px;
    }
  }
}

.group-title {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  
  .group-name {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }
  
  .group-count {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
}

.permission-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.permission-item {
  margin: 0;
  
  :deep(.el-checkbox__label) {
    width: 100%;
    padding-left: 8px;
  }
}

.permission-info {
  display: flex;
  flex-direction: column;
  
  .permission-name {
    font-size: 14px;
    color: var(--el-text-color-primary);
    line-height: 1.4;
  }
  
  .permission-key {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    font-family: monospace;
  }
}

:deep(.el-dialog) {
  border-radius: 12px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid var(--el-border-color-lighter);
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}
</style>

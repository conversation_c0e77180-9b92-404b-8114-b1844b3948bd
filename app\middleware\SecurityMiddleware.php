<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * 安全中间件 - ThinkPHP6企业级应用
 * 功能：CSRF保护、XSS防护、安全头设置、请求频率限制
 */

namespace app\middleware;

use think\facade\Config;
use think\facade\Session;
use think\facade\Request;
use think\facade\Cache;
use think\Response;

class SecurityMiddleware
{
    /**
     * 处理请求
     */
    public function handle($request, \Closure $next)
    {
        // 1. 设置安全头
        $this->setSecurityHeaders();
        
        // 2. IP访问控制
        if (!$this->checkIpAccess($request)) {
            return Response::create('Access Denied', 'html', 403);
        }
        
        // 3. 请求频率限制
        if (!$this->checkRateLimit($request)) {
            return Response::create('Too Many Requests', 'html', 429);
        }
        
        // 4. CSRF保护
        if (!$this->checkCsrfToken($request)) {
            return Response::create('CSRF Token Mismatch', 'html', 419);
        }
        
        // 5. XSS过滤
        $this->filterXss($request);
        
        $response = $next($request);
        
        // 6. 响应后处理
        $this->afterResponse($response);
        
        return $response;
    }
    
    /**
     * 设置安全头
     */
    protected function setSecurityHeaders()
    {
        $config = Config::get('security.headers', []);
        
        if (!($config['enable'] ?? false)) {
            return;
        }
        
        $headers = $config['list'] ?? [];
        foreach ($headers as $name => $value) {
            header("{$name}: {$value}");
        }
    }
    
    /**
     * IP访问控制
     */
    protected function checkIpAccess($request)
    {
        $config = Config::get('security.ip_control', []);
        
        if (!($config['enable'] ?? false)) {
            return true;
        }
        
        $clientIp = $request->ip();
        
        // 检查黑名单
        $blacklist = $config['blacklist'] ?? [];
        if (in_array($clientIp, $blacklist)) {
            return false;
        }
        
        // 检查白名单
        $whitelist = $config['whitelist'] ?? [];
        if (!empty($whitelist) && !in_array($clientIp, $whitelist)) {
            return false;
        }
        
        // 管理后台IP白名单检查
        if (strpos($request->pathinfo(), 'admin') === 0) {
            $adminWhitelist = $config['admin_whitelist'] ?? [];
            if (!empty($adminWhitelist) && !in_array($clientIp, $adminWhitelist)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 请求频率限制
     */
    protected function checkRateLimit($request)
    {
        $config = Config::get('security.rate_limit', []);
        
        if (!($config['enable'] ?? false)) {
            return true;
        }
        
        $clientIp = $request->ip();
        $path = $request->pathinfo();
        
        // 获取限制规则
        $limit = $config['default'] ?? 60;
        foreach ($config['routes'] ?? [] as $route => $routeLimit) {
            if (strpos($path, $route) !== false) {
                $limit = $routeLimit;
                break;
            }
        }
        
        // 检查频率
        $key = "rate_limit:{$clientIp}:{$path}";
        $current = Cache::get($key, 0);
        
        if ($current >= $limit) {
            return false;
        }
        
        Cache::set($key, $current + 1, 60); // 1分钟过期
        return true;
    }
    
    /**
     * CSRF保护
     */
    protected function checkCsrfToken($request)
    {
        $config = Config::get('security.csrf', []);
        
        if (!($config['enable'] ?? false)) {
            return true;
        }
        
        $method = $request->method();
        $methods = $config['methods'] ?? ['POST', 'PUT', 'DELETE', 'PATCH'];
        
        // 只对指定方法进行CSRF检查
        if (!in_array($method, $methods)) {
            return true;
        }
        
        // 检查排除路由
        $path = $request->pathinfo();
        $except = $config['except'] ?? [];
        foreach ($except as $pattern) {
            if (fnmatch($pattern, $path)) {
                return true;
            }
        }
        
        // 验证CSRF令牌
        $tokenName = $config['token_name'] ?? '__token__';
        $sessionToken = Session::get($tokenName);
        $requestToken = $request->param($tokenName) ?: $request->header('X-CSRF-TOKEN');
        
        if (!$sessionToken || !$requestToken || !hash_equals($sessionToken, $requestToken)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * XSS过滤
     */
    protected function filterXss($request)
    {
        $config = Config::get('security.xss', []);
        
        if (!($config['enable'] ?? false)) {
            return;
        }
        
        $params = $request->param();
        $filteredParams = $this->recursiveXssFilter($params, $config);
        
        // 更新请求参数
        $request->withParam($filteredParams);
    }
    
    /**
     * 递归XSS过滤
     */
    protected function recursiveXssFilter($data, $config)
    {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->recursiveXssFilter($value, $config);
            }
        } elseif (is_string($data)) {
            // 移除危险标签
            $allowedTags = $config['allowed_tags'] ?? '';
            $data = strip_tags($data, $allowedTags);
            
            // 移除危险属性
            $data = preg_replace('/on\w+\s*=/i', '', $data);
            $data = preg_replace('/javascript:/i', '', $data);
            $data = preg_replace('/data:/i', '', $data);
            $data = preg_replace('/vbscript:/i', '', $data);
        }
        
        return $data;
    }
    
    /**
     * 响应后处理
     */
    protected function afterResponse($response)
    {
        // 会话安全处理
        $this->handleSessionSecurity();
    }
    
    /**
     * 会话安全处理
     */
    protected function handleSessionSecurity()
    {
        $config = Config::get('security.session', []);
        
        // 设置会话参数
        if ($config['httponly'] ?? true) {
            ini_set('session.cookie_httponly', 1);
        }
        
        if ($config['secure'] ?? false) {
            ini_set('session.cookie_secure', 1);
        }
        
        $samesite = $config['samesite'] ?? 'Lax';
        ini_set('session.cookie_samesite', $samesite);
        
        // 定期重新生成会话ID
        $interval = $config['regenerate_interval'] ?? 300;
        $lastRegenerate = Session::get('_last_regenerate', 0);
        
        if (time() - $lastRegenerate > $interval) {
            Session::regenerate();
            Session::set('_last_regenerate', time());
        }
    }
    
    /**
     * 生成CSRF令牌
     */
    public static function generateCsrfToken()
    {
        $config = Config::get('security.csrf', []);
        $tokenName = $config['token_name'] ?? '__token__';
        $generator = $config['token_generator'] ?? null;
        
        if ($generator && is_callable($generator)) {
            $token = $generator();
        } else {
            $token = md5(uniqid(mt_rand(), true));
        }
        
        Session::set($tokenName, $token);
        return $token;
    }
    
    /**
     * 获取CSRF令牌
     */
    public static function getCsrfToken()
    {
        $config = Config::get('security.csrf', []);
        $tokenName = $config['token_name'] ?? '__token__';
        
        $token = Session::get($tokenName);
        if (!$token) {
            $token = self::generateCsrfToken();
        }
        
        return $token;
    }
}

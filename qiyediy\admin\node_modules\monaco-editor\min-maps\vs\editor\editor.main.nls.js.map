{"version": 3, "sources": ["out-editor/vs/editor/editor.main.nls.js"], "sourcesContent": ["/*!-----------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)\n * Released under the MIT license\n * https://github.com/microsoft/vscode/blob/main/LICENSE.txt\n *-----------------------------------------------------------*/\n\n/*---------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n *--------------------------------------------------------*/\ndefine(\"vs/editor/editor.main.nls\", {\n\t\"vs/base/browser/ui/actionbar/actionViewItems\": [\n\t\t\"{0} ({1})\"\n\t],\n\t\"vs/base/browser/ui/findinput/findInput\": [\n\t\t\"input\"\n\t],\n\t\"vs/base/browser/ui/findinput/findInputToggles\": [\n\t\t\"Match Case\",\n\t\t\"Match Whole Word\",\n\t\t\"Use Regular Expression\"\n\t],\n\t\"vs/base/browser/ui/findinput/replaceInput\": [\n\t\t\"input\",\n\t\t\"Preserve Case\"\n\t],\n\t\"vs/base/browser/ui/hover/hoverWidget\": [\n\t\t\"Inspect this in the accessible view with {0}.\",\n\t\t\"Inspect this in the accessible view via the command Open Accessible View which is currently not triggerable via keybinding.\"\n\t],\n\t\"vs/base/browser/ui/iconLabel/iconLabelHover\": [\n\t\t\"Loading...\"\n\t],\n\t\"vs/base/browser/ui/inputbox/inputBox\": [\n\t\t\"Error: {0}\",\n\t\t\"Warning: {0}\",\n\t\t\"Info: {0}\",\n\t\t\" or {0} for history\",\n\t\t\" ({0} for history)\",\n\t\t\"Cleared Input\"\n\t],\n\t\"vs/base/browser/ui/keybindingLabel/keybindingLabel\": [\n\t\t\"Unbound\"\n\t],\n\t\"vs/base/browser/ui/selectBox/selectBoxCustom\": [\n\t\t\"Select Box\"\n\t],\n\t\"vs/base/browser/ui/toolbar/toolbar\": [\n\t\t\"More Actions...\"\n\t],\n\t\"vs/base/browser/ui/tree/abstractTree\": [\n\t\t\"Filter\",\n\t\t\"Fuzzy Match\",\n\t\t\"Type to filter\",\n\t\t\"Type to search\",\n\t\t\"Type to search\",\n\t\t\"Close\",\n\t\t\"No elements found.\"\n\t],\n\t\"vs/base/common/actions\": [\n\t\t\"(empty)\"\n\t],\n\t\"vs/base/common/errorMessage\": [\n\t\t\"{0}: {1}\",\n\t\t\"A system error occurred ({0})\",\n\t\t\"An unknown error occurred. Please consult the log for more details.\",\n\t\t\"An unknown error occurred. Please consult the log for more details.\",\n\t\t\"{0} ({1} errors in total)\",\n\t\t\"An unknown error occurred. Please consult the log for more details.\"\n\t],\n\t\"vs/base/common/keybindingLabels\": [\n\t\t\"Ctrl\",\n\t\t\"Shift\",\n\t\t\"Alt\",\n\t\t\"Windows\",\n\t\t\"Ctrl\",\n\t\t\"Shift\",\n\t\t\"Alt\",\n\t\t\"Super\",\n\t\t\"Control\",\n\t\t\"Shift\",\n\t\t\"Option\",\n\t\t\"Command\",\n\t\t\"Control\",\n\t\t\"Shift\",\n\t\t\"Alt\",\n\t\t\"Windows\",\n\t\t\"Control\",\n\t\t\"Shift\",\n\t\t\"Alt\",\n\t\t\"Super\"\n\t],\n\t\"vs/base/common/platform\": [\n\t\t\"_\"\n\t],\n\t\"vs/editor/browser/controller/textAreaHandler\": [\n\t\t\"editor\",\n\t\t\"The editor is not accessible at this time.\",\n\t\t\"{0} To enable screen reader optimized mode, use {1}\",\n\t\t\"{0} To enable screen reader optimized mode, open the quick pick with {1} and run the command Toggle Screen Reader Accessibility Mode, which is currently not triggerable via keyboard.\",\n\t\t\"{0} Please assign a keybinding for the command Toggle Screen Reader Accessibility Mode by accessing the keybindings editor with {1} and run it.\"\n\t],\n\t\"vs/editor/browser/coreCommands\": [\n\t\t\"Stick to the end even when going to longer lines\",\n\t\t\"Stick to the end even when going to longer lines\",\n\t\t\"Removed secondary cursors\"\n\t],\n\t\"vs/editor/browser/editorExtensions\": [\n\t\t\"&&Undo\",\n\t\t\"Undo\",\n\t\t\"&&Redo\",\n\t\t\"Redo\",\n\t\t\"&&Select All\",\n\t\t\"Select All\"\n\t],\n\t\"vs/editor/browser/widget/codeEditorWidget\": [\n\t\t\"The number of cursors has been limited to {0}. Consider using [find and replace](https://code.visualstudio.com/docs/editor/codebasics#_find-and-replace) for larger changes or increase the editor multi cursor limit setting.\",\n\t\t\"Increase Multi Cursor Limit\"\n\t],\n\t\"vs/editor/browser/widget/diffEditor/accessibleDiffViewer\": [\n\t\t\"Icon for 'Insert' in accessible diff viewer.\",\n\t\t\"Icon for 'Remove' in accessible diff viewer.\",\n\t\t\"Icon for 'Close' in accessible diff viewer.\",\n\t\t\"Close\",\n\t\t\"Accessible Diff Viewer. Use arrow up and down to navigate.\",\n\t\t\"no lines changed\",\n\t\t\"1 line changed\",\n\t\t\"{0} lines changed\",\n\t\t\"Difference {0} of {1}: original line {2}, {3}, modified line {4}, {5}\",\n\t\t\"blank\",\n\t\t\"{0} unchanged line {1}\",\n\t\t\"{0} original line {1} modified line {2}\",\n\t\t\"+ {0} modified line {1}\",\n\t\t\"- {0} original line {1}\"\n\t],\n\t\"vs/editor/browser/widget/diffEditor/colors\": [\n\t\t\"The border color for text that got moved in the diff editor.\",\n\t\t\"The active border color for text that got moved in the diff editor.\",\n\t\t\"The color of the shadow around unchanged region widgets.\"\n\t],\n\t\"vs/editor/browser/widget/diffEditor/decorations\": [\n\t\t\"Line decoration for inserts in the diff editor.\",\n\t\t\"Line decoration for removals in the diff editor.\"\n\t],\n\t\"vs/editor/browser/widget/diffEditor/diffEditor.contribution\": [\n\t\t\"Toggle Collapse Unchanged Regions\",\n\t\t\"Toggle Show Moved Code Blocks\",\n\t\t\"Toggle Use Inline View When Space Is Limited\",\n\t\t\"Use Inline View When Space Is Limited\",\n\t\t\"Show Moved Code Blocks\",\n\t\t\"Diff Editor\",\n\t\t\"Switch Side\",\n\t\t\"Exit Compare Move\",\n\t\t\"Collapse All Unchanged Regions\",\n\t\t\"Show All Unchanged Regions\",\n\t\t\"Accessible Diff Viewer\",\n\t\t\"Go to Next Difference\",\n\t\t\"Open Accessible Diff Viewer\",\n\t\t\"Go to Previous Difference\"\n\t],\n\t\"vs/editor/browser/widget/diffEditor/diffEditorDecorations\": [\n\t\t\"Revert Selected Changes\",\n\t\t\"Revert Change\"\n\t],\n\t\"vs/editor/browser/widget/diffEditor/diffEditorEditors\": [\n\t\t\" use {0} to open the accessibility help.\"\n\t],\n\t\"vs/editor/browser/widget/diffEditor/hideUnchangedRegionsFeature\": [\n\t\t\"Fold Unchanged Region\",\n\t\t\"Click or drag to show more above\",\n\t\t\"Show Unchanged Region\",\n\t\t\"Click or drag to show more below\",\n\t\t\"{0} hidden lines\",\n\t\t\"Double click to unfold\"\n\t],\n\t\"vs/editor/browser/widget/diffEditor/inlineDiffDeletedCodeMargin\": [\n\t\t\"Copy deleted lines\",\n\t\t\"Copy deleted line\",\n\t\t\"Copy changed lines\",\n\t\t\"Copy changed line\",\n\t\t\"Copy deleted line ({0})\",\n\t\t\"Copy changed line ({0})\",\n\t\t\"Revert this change\"\n\t],\n\t\"vs/editor/browser/widget/diffEditor/movedBlocksLines\": [\n\t\t\"Code moved with changes to line {0}-{1}\",\n\t\t\"Code moved with changes from line {0}-{1}\",\n\t\t\"Code moved to line {0}-{1}\",\n\t\t\"Code moved from line {0}-{1}\"\n\t],\n\t\"vs/editor/browser/widget/multiDiffEditorWidget/colors\": [\n\t\t\"The background color of the diff editor's header\"\n\t],\n\t\"vs/editor/common/config/editorConfigurationSchema\": [\n\t\t\"Editor\",\n\t\t\"The number of spaces a tab is equal to. This setting is overridden based on the file contents when {0} is on.\",\n\t\t\"The number of spaces used for indentation or `\\\"tabSize\\\"` to use the value from `#editor.tabSize#`. This setting is overridden based on the file contents when `#editor.detectIndentation#` is on.\",\n\t\t\"Insert spaces when pressing `Tab`. This setting is overridden based on the file contents when {0} is on.\",\n\t\t\"Controls whether {0} and {1} will be automatically detected when a file is opened based on the file contents.\",\n\t\t\"Remove trailing auto inserted whitespace.\",\n\t\t\"Special handling for large files to disable certain memory intensive features.\",\n\t\t\"Turn off Word Based Suggestions.\",\n\t\t\"Only suggest words from the active document.\",\n\t\t\"Suggest words from all open documents of the same language.\",\n\t\t\"Suggest words from all open documents.\",\n\t\t\"Controls whether completions should be computed based on words in the document and from which documents they are computed.\",\n\t\t\"Semantic highlighting enabled for all color themes.\",\n\t\t\"Semantic highlighting disabled for all color themes.\",\n\t\t\"Semantic highlighting is configured by the current color theme's `semanticHighlighting` setting.\",\n\t\t\"Controls whether the semanticHighlighting is shown for the languages that support it.\",\n\t\t\"Keep peek editors open even when double-clicking their content or when hitting `Escape`.\",\n\t\t\"Lines above this length will not be tokenized for performance reasons\",\n\t\t\"Controls whether the tokenization should happen asynchronously on a web worker.\",\n\t\t\"Controls whether async tokenization should be logged. For debugging only.\",\n\t\t\"Controls whether async tokenization should be verified against legacy background tokenization. Might slow down tokenization. For debugging only.\",\n\t\t\"Defines the bracket symbols that increase or decrease the indentation.\",\n\t\t\"The opening bracket character or string sequence.\",\n\t\t\"The closing bracket character or string sequence.\",\n\t\t\"Defines the bracket pairs that are colorized by their nesting level if bracket pair colorization is enabled.\",\n\t\t\"The opening bracket character or string sequence.\",\n\t\t\"The closing bracket character or string sequence.\",\n\t\t\"Timeout in milliseconds after which diff computation is cancelled. Use 0 for no timeout.\",\n\t\t\"Maximum file size in MB for which to compute diffs. Use 0 for no limit.\",\n\t\t\"Controls whether the diff editor shows the diff side by side or inline.\",\n\t\t\"If the diff editor width is smaller than this value, the inline view is used.\",\n\t\t\"If enabled and the editor width is too small, the inline view is used.\",\n\t\t\"When enabled, the diff editor shows arrows in its glyph margin to revert changes.\",\n\t\t\"When enabled, the diff editor ignores changes in leading or trailing whitespace.\",\n\t\t\"Controls whether the diff editor shows +/- indicators for added/removed changes.\",\n\t\t\"Controls whether the editor shows CodeLens.\",\n\t\t\"Lines will never wrap.\",\n\t\t\"Lines will wrap at the viewport width.\",\n\t\t\"Lines will wrap according to the {0} setting.\",\n\t\t\"Uses the legacy diffing algorithm.\",\n\t\t\"Uses the advanced diffing algorithm.\",\n\t\t\"Controls whether the diff editor shows unchanged regions.\",\n\t\t\"Controls how many lines are used for unchanged regions.\",\n\t\t\"Controls how many lines are used as a minimum for unchanged regions.\",\n\t\t\"Controls how many lines are used as context when comparing unchanged regions.\",\n\t\t\"Controls whether the diff editor should show detected code moves.\",\n\t\t\"Controls whether the diff editor shows empty decorations to see where characters got inserted or deleted.\"\n\t],\n\t\"vs/editor/common/config/editorOptions\": [\n\t\t\"Use platform APIs to detect when a Screen Reader is attached.\",\n\t\t\"Optimize for usage with a Screen Reader.\",\n\t\t\"Assume a screen reader is not attached.\",\n\t\t\"Controls if the UI should run in a mode where it is optimized for screen readers.\",\n\t\t\"Controls whether a space character is inserted when commenting.\",\n\t\t\"Controls if empty lines should be ignored with toggle, add or remove actions for line comments.\",\n\t\t\"Controls whether copying without a selection copies the current line.\",\n\t\t\"Controls whether the cursor should jump to find matches while typing.\",\n\t\t\"Never seed search string from the editor selection.\",\n\t\t\"Always seed search string from the editor selection, including word at cursor position.\",\n\t\t\"Only seed search string from the editor selection.\",\n\t\t\"Controls whether the search string in the Find Widget is seeded from the editor selection.\",\n\t\t\"Never turn on Find in Selection automatically (default).\",\n\t\t\"Always turn on Find in Selection automatically.\",\n\t\t\"Turn on Find in Selection automatically when multiple lines of content are selected.\",\n\t\t\"Controls the condition for turning on Find in Selection automatically.\",\n\t\t\"Controls whether the Find Widget should read or modify the shared find clipboard on macOS.\",\n\t\t\"Controls whether the Find Widget should add extra lines on top of the editor. When true, you can scroll beyond the first line when the Find Widget is visible.\",\n\t\t\"Controls whether the search automatically restarts from the beginning (or the end) when no further matches can be found.\",\n\t\t\"Enables/Disables font ligatures ('calt' and 'liga' font features). Change this to a string for fine-grained control of the 'font-feature-settings' CSS property.\",\n\t\t\"Explicit 'font-feature-settings' CSS property. A boolean can be passed instead if one only needs to turn on/off ligatures.\",\n\t\t\"Configures font ligatures or font features. Can be either a boolean to enable/disable ligatures or a string for the value of the CSS 'font-feature-settings' property.\",\n\t\t\"Enables/Disables the translation from font-weight to font-variation-settings. Change this to a string for fine-grained control of the 'font-variation-settings' CSS property.\",\n\t\t\"Explicit 'font-variation-settings' CSS property. A boolean can be passed instead if one only needs to translate font-weight to font-variation-settings.\",\n\t\t\"Configures font variations. Can be either a boolean to enable/disable the translation from font-weight to font-variation-settings or a string for the value of the CSS 'font-variation-settings' property.\",\n\t\t\"Controls the font size in pixels.\",\n\t\t\"Only \\\"normal\\\" and \\\"bold\\\" keywords or numbers between 1 and 1000 are allowed.\",\n\t\t\"Controls the font weight. Accepts \\\"normal\\\" and \\\"bold\\\" keywords or numbers between 1 and 1000.\",\n\t\t\"Show Peek view of the results (default)\",\n\t\t\"Go to the primary result and show a Peek view\",\n\t\t\"Go to the primary result and enable Peek-less navigation to others\",\n\t\t\"This setting is deprecated, please use separate settings like 'editor.editor.gotoLocation.multipleDefinitions' or 'editor.editor.gotoLocation.multipleImplementations' instead.\",\n\t\t\"Controls the behavior the 'Go to Definition'-command when multiple target locations exist.\",\n\t\t\"Controls the behavior the 'Go to Type Definition'-command when multiple target locations exist.\",\n\t\t\"Controls the behavior the 'Go to Declaration'-command when multiple target locations exist.\",\n\t\t\"Controls the behavior the 'Go to Implementations'-command when multiple target locations exist.\",\n\t\t\"Controls the behavior the 'Go to References'-command when multiple target locations exist.\",\n\t\t\"Alternative command id that is being executed when the result of 'Go to Definition' is the current location.\",\n\t\t\"Alternative command id that is being executed when the result of 'Go to Type Definition' is the current location.\",\n\t\t\"Alternative command id that is being executed when the result of 'Go to Declaration' is the current location.\",\n\t\t\"Alternative command id that is being executed when the result of 'Go to Implementation' is the current location.\",\n\t\t\"Alternative command id that is being executed when the result of 'Go to Reference' is the current location.\",\n\t\t\"Controls whether the hover is shown.\",\n\t\t\"Controls the delay in milliseconds after which the hover is shown.\",\n\t\t\"Controls whether the hover should remain visible when mouse is moved over it.\",\n\t\t\"Controls the delay in milliseconds after which the hover is hidden. Requires `editor.hover.sticky` to be enabled.\",\n\t\t\"Prefer showing hovers above the line, if there's space.\",\n\t\t\"Assumes that all characters are of the same width. This is a fast algorithm that works correctly for monospace fonts and certain scripts (like Latin characters) where glyphs are of equal width.\",\n\t\t\"Delegates wrapping points computation to the browser. This is a slow algorithm, that might cause freezes for large files, but it works correctly in all cases.\",\n\t\t\"Controls the algorithm that computes wrapping points. Note that when in accessibility mode, advanced will be used for the best experience.\",\n\t\t\"Enables the Code Action lightbulb in the editor.\",\n\t\t\"Don not show the AI icon.\",\n\t\t\"Show an AI icon when the code action menu contains an AI action, but only on code.\",\n\t\t\"Show an AI icon when the code action menu contains an AI action, on code and empty lines.\",\n\t\t\"Show an AI icon along with the lightbulb when the code action menu contains an AI action.\",\n\t\t\"Shows the nested current scopes during the scroll at the top of the editor.\",\n\t\t\"Defines the maximum number of sticky lines to show.\",\n\t\t\"Defines the model to use for determining which lines to stick. If the outline model does not exist, it will fall back on the folding provider model which falls back on the indentation model. This order is respected in all three cases.\",\n\t\t\"Enable scrolling of Sticky Scroll with the editor's horizontal scrollbar.\",\n\t\t\"Enables the inlay hints in the editor.\",\n\t\t\"Inlay hints are enabled\",\n\t\t\"Inlay hints are showing by default and hide when holding {0}\",\n\t\t\"Inlay hints are hidden by default and show when holding {0}\",\n\t\t\"Inlay hints are disabled\",\n\t\t\"Controls font size of inlay hints in the editor. As default the {0} is used when the configured value is less than {1} or greater than the editor font size.\",\n\t\t\"Controls font family of inlay hints in the editor. When set to empty, the {0} is used.\",\n\t\t\"Enables the padding around the inlay hints in the editor.\",\n\t\t\"Controls the line height. \\n - Use 0 to automatically compute the line height from the font size.\\n - Values between 0 and 8 will be used as a multiplier with the font size.\\n - Values greater than or equal to 8 will be used as effective values.\",\n\t\t\"Controls whether the minimap is shown.\",\n\t\t\"Controls whether the minimap is hidden automatically.\",\n\t\t\"The minimap has the same size as the editor contents (and might scroll).\",\n\t\t\"The minimap will stretch or shrink as necessary to fill the height of the editor (no scrolling).\",\n\t\t\"The minimap will shrink as necessary to never be larger than the editor (no scrolling).\",\n\t\t\"Controls the size of the minimap.\",\n\t\t\"Controls the side where to render the minimap.\",\n\t\t\"Controls when the minimap slider is shown.\",\n\t\t\"Scale of content drawn in the minimap: 1, 2 or 3.\",\n\t\t\"Render the actual characters on a line as opposed to color blocks.\",\n\t\t\"Limit the width of the minimap to render at most a certain number of columns.\",\n\t\t\"Controls the amount of space between the top edge of the editor and the first line.\",\n\t\t\"Controls the amount of space between the bottom edge of the editor and the last line.\",\n\t\t\"Enables a pop-up that shows parameter documentation and type information as you type.\",\n\t\t\"Controls whether the parameter hints menu cycles or closes when reaching the end of the list.\",\n\t\t\"Quick suggestions show inside the suggest widget\",\n\t\t\"Quick suggestions show as ghost text\",\n\t\t\"Quick suggestions are disabled\",\n\t\t\"Enable quick suggestions inside strings.\",\n\t\t\"Enable quick suggestions inside comments.\",\n\t\t\"Enable quick suggestions outside of strings and comments.\",\n\t\t\"Controls whether suggestions should automatically show up while typing. This can be controlled for typing in comments, strings, and other code. Quick suggestion can be configured to show as ghost text or with the suggest widget. Also be aware of the '{0}'-setting which controls if suggestions are triggered by special characters.\",\n\t\t\"Line numbers are not rendered.\",\n\t\t\"Line numbers are rendered as absolute number.\",\n\t\t\"Line numbers are rendered as distance in lines to cursor position.\",\n\t\t\"Line numbers are rendered every 10 lines.\",\n\t\t\"Controls the display of line numbers.\",\n\t\t\"Number of monospace characters at which this editor ruler will render.\",\n\t\t\"Color of this editor ruler.\",\n\t\t\"Render vertical rulers after a certain number of monospace characters. Use multiple values for multiple rulers. No rulers are drawn if array is empty.\",\n\t\t\"The vertical scrollbar will be visible only when necessary.\",\n\t\t\"The vertical scrollbar will always be visible.\",\n\t\t\"The vertical scrollbar will always be hidden.\",\n\t\t\"Controls the visibility of the vertical scrollbar.\",\n\t\t\"The horizontal scrollbar will be visible only when necessary.\",\n\t\t\"The horizontal scrollbar will always be visible.\",\n\t\t\"The horizontal scrollbar will always be hidden.\",\n\t\t\"Controls the visibility of the horizontal scrollbar.\",\n\t\t\"The width of the vertical scrollbar.\",\n\t\t\"The height of the horizontal scrollbar.\",\n\t\t\"Controls whether clicks scroll by page or jump to click position.\",\n\t\t\"When set, the horizontal scrollbar will not increase the size of the editor's content.\",\n\t\t\"Controls whether all non-basic ASCII characters are highlighted. Only characters between U+0020 and U+007E, tab, line-feed and carriage-return are considered basic ASCII.\",\n\t\t\"Controls whether characters that just reserve space or have no width at all are highlighted.\",\n\t\t\"Controls whether characters are highlighted that can be confused with basic ASCII characters, except those that are common in the current user locale.\",\n\t\t\"Controls whether characters in comments should also be subject to Unicode highlighting.\",\n\t\t\"Controls whether characters in strings should also be subject to Unicode highlighting.\",\n\t\t\"Defines allowed characters that are not being highlighted.\",\n\t\t\"Unicode characters that are common in allowed locales are not being highlighted.\",\n\t\t\"Controls whether to automatically show inline suggestions in the editor.\",\n\t\t\"Show the inline suggestion toolbar whenever an inline suggestion is shown.\",\n\t\t\"Show the inline suggestion toolbar when hovering over an inline suggestion.\",\n\t\t\"Never show the inline suggestion toolbar.\",\n\t\t\"Controls when to show the inline suggestion toolbar.\",\n\t\t\"Controls how inline suggestions interact with the suggest widget. If enabled, the suggest widget is not shown automatically when inline suggestions are available.\",\n\t\t\"Controls whether bracket pair colorization is enabled or not. Use {0} to override the bracket highlight colors.\",\n\t\t\"Controls whether each bracket type has its own independent color pool.\",\n\t\t\"Enables bracket pair guides.\",\n\t\t\"Enables bracket pair guides only for the active bracket pair.\",\n\t\t\"Disables bracket pair guides.\",\n\t\t\"Controls whether bracket pair guides are enabled or not.\",\n\t\t\"Enables horizontal guides as addition to vertical bracket pair guides.\",\n\t\t\"Enables horizontal guides only for the active bracket pair.\",\n\t\t\"Disables horizontal bracket pair guides.\",\n\t\t\"Controls whether horizontal bracket pair guides are enabled or not.\",\n\t\t\"Controls whether the editor should highlight the active bracket pair.\",\n\t\t\"Controls whether the editor should render indent guides.\",\n\t\t\"Highlights the active indent guide.\",\n\t\t\"Highlights the active indent guide even if bracket guides are highlighted.\",\n\t\t\"Do not highlight the active indent guide.\",\n\t\t\"Controls whether the editor should highlight the active indent guide.\",\n\t\t\"Insert suggestion without overwriting text right of the cursor.\",\n\t\t\"Insert suggestion and overwrite text right of the cursor.\",\n\t\t\"Controls whether words are overwritten when accepting completions. Note that this depends on extensions opting into this feature.\",\n\t\t\"Controls whether filtering and sorting suggestions accounts for small typos.\",\n\t\t\"Controls whether sorting favors words that appear close to the cursor.\",\n\t\t\"Controls whether remembered suggestion selections are shared between multiple workspaces and windows (needs `#editor.suggestSelection#`).\",\n\t\t\"Always select a suggestion when automatically triggering IntelliSense.\",\n\t\t\"Never select a suggestion when automatically triggering IntelliSense.\",\n\t\t\"Select a suggestion only when triggering IntelliSense from a trigger character.\",\n\t\t\"Select a suggestion only when triggering IntelliSense as you type.\",\n\t\t\"Controls whether a suggestion is selected when the widget shows. Note that this only applies to automatically triggered suggestions (`#editor.quickSuggestions#` and `#editor.suggestOnTriggerCharacters#`) and that a suggestion is always selected when explicitly invoked, e.g via `Ctrl+Space`.\",\n\t\t\"Controls whether an active snippet prevents quick suggestions.\",\n\t\t\"Controls whether to show or hide icons in suggestions.\",\n\t\t\"Controls the visibility of the status bar at the bottom of the suggest widget.\",\n\t\t\"Controls whether to preview the suggestion outcome in the editor.\",\n\t\t\"Controls whether suggest details show inline with the label or only in the details widget.\",\n\t\t\"This setting is deprecated. The suggest widget can now be resized.\",\n\t\t\"This setting is deprecated, please use separate settings like 'editor.suggest.showKeywords' or 'editor.suggest.showSnippets' instead.\",\n\t\t\"When enabled IntelliSense shows `method`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `function`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `constructor`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `deprecated`-suggestions.\",\n\t\t\"When enabled IntelliSense filtering requires that the first character matches on a word start. For example, `c` on `Console` or `WebContext` but _not_ on `description`. When disabled IntelliSense will show more results but still sorts them by match quality.\",\n\t\t\"When enabled IntelliSense shows `field`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `variable`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `class`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `struct`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `interface`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `module`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `property`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `event`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `operator`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `unit`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `value`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `constant`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `enum`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `enumMember`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `keyword`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `text`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `color`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `file`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `reference`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `customcolor`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `folder`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `typeParameter`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `snippet`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `user`-suggestions.\",\n\t\t\"When enabled IntelliSense shows `issues`-suggestions.\",\n\t\t\"Whether leading and trailing whitespace should always be selected.\",\n\t\t\"Whether subwords (like 'foo' in 'fooBar' or 'foo_bar') should be selected.\",\n\t\t\"No indentation. Wrapped lines begin at column 1.\",\n\t\t\"Wrapped lines get the same indentation as the parent.\",\n\t\t\"Wrapped lines get +1 indentation toward the parent.\",\n\t\t\"Wrapped lines get +2 indentation toward the parent.\",\n\t\t\"Controls the indentation of wrapped lines.\",\n\t\t\"Controls whether you can drag and drop a file into a text editor by holding down `Shift`-key (instead of opening the file in an editor).\",\n\t\t\"Controls if a widget is shown when dropping files into the editor. This widget lets you control how the file is dropped.\",\n\t\t\"Show the drop selector widget after a file is dropped into the editor.\",\n\t\t\"Never show the drop selector widget. Instead the default drop provider is always used.\",\n\t\t\"Controls whether you can paste content in different ways.\",\n\t\t\"Controls if a widget is shown when pasting content in to the editor. This widget lets you control how the file is pasted.\",\n\t\t\"Show the paste selector widget after content is pasted into the editor.\",\n\t\t\"Never show the paste selector widget. Instead the default pasting behavior is always used.\",\n\t\t\"Controls whether suggestions should be accepted on commit characters. For example, in JavaScript, the semi-colon (`;`) can be a commit character that accepts a suggestion and types that character.\",\n\t\t\"Only accept a suggestion with `Enter` when it makes a textual change.\",\n\t\t\"Controls whether suggestions should be accepted on `Enter`, in addition to `Tab`. Helps to avoid ambiguity between inserting new lines or accepting suggestions.\",\n\t\t\"Controls the number of lines in the editor that can be read out by a screen reader at once. When we detect a screen reader we automatically set the default to be 500. Warning: this has a performance implication for numbers larger than the default.\",\n\t\t\"Editor content\",\n\t\t\"Control whether inline suggestions are announced by a screen reader.\",\n\t\t\"Use language configurations to determine when to autoclose brackets.\",\n\t\t\"Autoclose brackets only when the cursor is to the left of whitespace.\",\n\t\t\"Controls whether the editor should automatically close brackets after the user adds an opening bracket.\",\n\t\t\"Use language configurations to determine when to autoclose comments.\",\n\t\t\"Autoclose comments only when the cursor is to the left of whitespace.\",\n\t\t\"Controls whether the editor should automatically close comments after the user adds an opening comment.\",\n\t\t\"Remove adjacent closing quotes or brackets only if they were automatically inserted.\",\n\t\t\"Controls whether the editor should remove adjacent closing quotes or brackets when deleting.\",\n\t\t\"Type over closing quotes or brackets only if they were automatically inserted.\",\n\t\t\"Controls whether the editor should type over closing quotes or brackets.\",\n\t\t\"Use language configurations to determine when to autoclose quotes.\",\n\t\t\"Autoclose quotes only when the cursor is to the left of whitespace.\",\n\t\t\"Controls whether the editor should automatically close quotes after the user adds an opening quote.\",\n\t\t\"The editor will not insert indentation automatically.\",\n\t\t\"The editor will keep the current line's indentation.\",\n\t\t\"The editor will keep the current line's indentation and honor language defined brackets.\",\n\t\t\"The editor will keep the current line's indentation, honor language defined brackets and invoke special onEnterRules defined by languages.\",\n\t\t\"The editor will keep the current line's indentation, honor language defined brackets, invoke special onEnterRules defined by languages, and honor indentationRules defined by languages.\",\n\t\t\"Controls whether the editor should automatically adjust the indentation when users type, paste, move or indent lines.\",\n\t\t\"Use language configurations to determine when to automatically surround selections.\",\n\t\t\"Surround with quotes but not brackets.\",\n\t\t\"Surround with brackets but not quotes.\",\n\t\t\"Controls whether the editor should automatically surround selections when typing quotes or brackets.\",\n\t\t\"Emulate selection behavior of tab characters when using spaces for indentation. Selection will stick to tab stops.\",\n\t\t\"Controls whether the editor shows CodeLens.\",\n\t\t\"Controls the font family for CodeLens.\",\n\t\t\"Controls the font size in pixels for CodeLens. When set to 0, 90% of `#editor.fontSize#` is used.\",\n\t\t\"Controls whether the editor should render the inline color decorators and color picker.\",\n\t\t\"Make the color picker appear both on click and hover of the color decorator\",\n\t\t\"Make the color picker appear on hover of the color decorator\",\n\t\t\"Make the color picker appear on click of the color decorator\",\n\t\t\"Controls the condition to make a color picker appear from a color decorator\",\n\t\t\"Controls the max number of color decorators that can be rendered in an editor at once.\",\n\t\t\"Enable that the selection with the mouse and keys is doing column selection.\",\n\t\t\"Controls whether syntax highlighting should be copied into the clipboard.\",\n\t\t\"Control the cursor animation style.\",\n\t\t\"Smooth caret animation is disabled.\",\n\t\t\"Smooth caret animation is enabled only when the user moves the cursor with an explicit gesture.\",\n\t\t\"Smooth caret animation is always enabled.\",\n\t\t\"Controls whether the smooth caret animation should be enabled.\",\n\t\t\"Controls the cursor style.\",\n\t\t\"Controls the minimal number of visible leading lines (minimum 0) and trailing lines (minimum 1) surrounding the cursor. Known as 'scrollOff' or 'scrollOffset' in some other editors.\",\n\t\t\"`cursorSurroundingLines` is enforced only when triggered via the keyboard or API.\",\n\t\t\"`cursorSurroundingLines` is enforced always.\",\n\t\t\"Controls when `#cursorSurroundingLines#` should be enforced.\",\n\t\t\"Controls the width of the cursor when `#editor.cursorStyle#` is set to `line`.\",\n\t\t\"Controls whether the editor should allow moving selections via drag and drop.\",\n\t\t\"Use a new rendering method with svgs.\",\n\t\t\"Use a new rendering method with font characters.\",\n\t\t\"Use the stable rendering method.\",\n\t\t\"Controls whether whitespace is rendered with a new, experimental method.\",\n\t\t\"Scrolling speed multiplier when pressing `Alt`.\",\n\t\t\"Controls whether the editor has code folding enabled.\",\n\t\t\"Use a language-specific folding strategy if available, else the indentation-based one.\",\n\t\t\"Use the indentation-based folding strategy.\",\n\t\t\"Controls the strategy for computing folding ranges.\",\n\t\t\"Controls whether the editor should highlight folded ranges.\",\n\t\t\"Controls whether the editor automatically collapses import ranges.\",\n\t\t\"The maximum number of foldable regions. Increasing this value may result in the editor becoming less responsive when the current source has a large number of foldable regions.\",\n\t\t\"Controls whether clicking on the empty content after a folded line will unfold the line.\",\n\t\t\"Controls the font family.\",\n\t\t\"Controls whether the editor should automatically format the pasted content. A formatter must be available and the formatter should be able to format a range in a document.\",\n\t\t\"Controls whether the editor should automatically format the line after typing.\",\n\t\t\"Controls whether the editor should render the vertical glyph margin. Glyph margin is mostly used for debugging.\",\n\t\t\"Controls whether the cursor should be hidden in the overview ruler.\",\n\t\t\"Controls the letter spacing in pixels.\",\n\t\t\"Controls whether the editor has linked editing enabled. Depending on the language, related symbols such as HTML tags, are updated while editing.\",\n\t\t\"Controls whether the editor should detect links and make them clickable.\",\n\t\t\"Highlight matching brackets.\",\n\t\t\"A multiplier to be used on the `deltaX` and `deltaY` of mouse wheel scroll events.\",\n\t\t\"Zoom the font of the editor when using mouse wheel and holding `Ctrl`.\",\n\t\t\"Merge multiple cursors when they are overlapping.\",\n\t\t\"Maps to `Control` on Windows and Linux and to `Command` on macOS.\",\n\t\t\"Maps to `Alt` on Windows and Linux and to `Option` on macOS.\",\n\t\t\"The modifier to be used to add multiple cursors with the mouse. The Go to Definition and Open Link mouse gestures will adapt such that they do not conflict with the [multicursor modifier](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).\",\n\t\t\"Each cursor pastes a single line of the text.\",\n\t\t\"Each cursor pastes the full text.\",\n\t\t\"Controls pasting when the line count of the pasted text matches the cursor count.\",\n\t\t\"Controls the max number of cursors that can be in an active editor at once.\",\n\t\t\"Does not highlight occurrences.\",\n\t\t\"Highlights occurrences only in the current file.\",\n\t\t\"Experimental: Highlights occurrences across all valid open files.\",\n\t\t\"Controls whether occurrences should be highlighted across open files.\",\n\t\t\"Controls whether a border should be drawn around the overview ruler.\",\n\t\t\"Focus the tree when opening peek\",\n\t\t\"Focus the editor when opening peek\",\n\t\t\"Controls whether to focus the inline editor or the tree in the peek widget.\",\n\t\t\"Controls whether the Go to Definition mouse gesture always opens the peek widget.\",\n\t\t\"Controls the delay in milliseconds after which quick suggestions will show up.\",\n\t\t\"Controls whether the editor auto renames on type.\",\n\t\t\"Deprecated, use `editor.linkedEditing` instead.\",\n\t\t\"Controls whether the editor should render control characters.\",\n\t\t\"Render last line number when the file ends with a newline.\",\n\t\t\"Highlights both the gutter and the current line.\",\n\t\t\"Controls how the editor should render the current line highlight.\",\n\t\t\"Controls if the editor should render the current line highlight only when the editor is focused.\",\n\t\t\"Render whitespace characters except for single spaces between words.\",\n\t\t\"Render whitespace characters only on selected text.\",\n\t\t\"Render only trailing whitespace characters.\",\n\t\t\"Controls how the editor should render whitespace characters.\",\n\t\t\"Controls whether selections should have rounded corners.\",\n\t\t\"Controls the number of extra characters beyond which the editor will scroll horizontally.\",\n\t\t\"Controls whether the editor will scroll beyond the last line.\",\n\t\t\"Scroll only along the predominant axis when scrolling both vertically and horizontally at the same time. Prevents horizontal drift when scrolling vertically on a trackpad.\",\n\t\t\"Controls whether the Linux primary clipboard should be supported.\",\n\t\t\"Controls whether the editor should highlight matches similar to the selection.\",\n\t\t\"Always show the folding controls.\",\n\t\t\"Never show the folding controls and reduce the gutter size.\",\n\t\t\"Only show the folding controls when the mouse is over the gutter.\",\n\t\t\"Controls when the folding controls on the gutter are shown.\",\n\t\t\"Controls fading out of unused code.\",\n\t\t\"Controls strikethrough deprecated variables.\",\n\t\t\"Show snippet suggestions on top of other suggestions.\",\n\t\t\"Show snippet suggestions below other suggestions.\",\n\t\t\"Show snippets suggestions with other suggestions.\",\n\t\t\"Do not show snippet suggestions.\",\n\t\t\"Controls whether snippets are shown with other suggestions and how they are sorted.\",\n\t\t\"Controls whether the editor will scroll using an animation.\",\n\t\t\"Controls whether the accessibility hint should be provided to screen reader users when an inline completion is shown.\",\n\t\t\"Font size for the suggest widget. When set to {0}, the value of {1} is used.\",\n\t\t\"Line height for the suggest widget. When set to {0}, the value of {1} is used. The minimum value is 8.\",\n\t\t\"Controls whether suggestions should automatically show up when typing trigger characters.\",\n\t\t\"Always select the first suggestion.\",\n\t\t\"Select recent suggestions unless further typing selects one, e.g. `console.| -> console.log` because `log` has been completed recently.\",\n\t\t\"Select suggestions based on previous prefixes that have completed those suggestions, e.g. `co -> console` and `con -> const`.\",\n\t\t\"Controls how suggestions are pre-selected when showing the suggest list.\",\n\t\t\"Tab complete will insert the best matching suggestion when pressing tab.\",\n\t\t\"Disable tab completions.\",\n\t\t\"Tab complete snippets when their prefix match. Works best when 'quickSuggestions' aren't enabled.\",\n\t\t\"Enables tab completions.\",\n\t\t\"Unusual line terminators are automatically removed.\",\n\t\t\"Unusual line terminators are ignored.\",\n\t\t\"Unusual line terminators prompt to be removed.\",\n\t\t\"Remove unusual line terminators that might cause problems.\",\n\t\t\"Inserting and deleting whitespace follows tab stops.\",\n\t\t\"Use the default line break rule.\",\n\t\t\"Word breaks should not be used for Chinese/Japanese/Korean (CJK) text. Non-CJK text behavior is the same as for normal.\",\n\t\t\"Controls the word break rules used for Chinese/Japanese/Korean (CJK) text.\",\n\t\t\"Characters that will be used as word separators when doing word related navigations or operations.\",\n\t\t\"Lines will never wrap.\",\n\t\t\"Lines will wrap at the viewport width.\",\n\t\t\"Lines will wrap at `#editor.wordWrapColumn#`.\",\n\t\t\"Lines will wrap at the minimum of viewport and `#editor.wordWrapColumn#`.\",\n\t\t\"Controls how lines should wrap.\",\n\t\t\"Controls the wrapping column of the editor when `#editor.wordWrap#` is `wordWrapColumn` or `bounded`.\",\n\t\t\"Controls whether inline color decorations should be shown using the default document color provider\",\n\t\t\"Controls whether the editor receives tabs or defers them to the workbench for navigation.\"\n\t],\n\t\"vs/editor/common/core/editorColorRegistry\": [\n\t\t\"Background color for the highlight of line at the cursor position.\",\n\t\t\"Background color for the border around the line at the cursor position.\",\n\t\t\"Background color of highlighted ranges, like by quick open and find features. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Background color of the border around highlighted ranges.\",\n\t\t\"Background color of highlighted symbol, like for go to definition or go next/previous symbol. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Background color of the border around highlighted symbols.\",\n\t\t\"Color of the editor cursor.\",\n\t\t\"The background color of the editor cursor. Allows customizing the color of a character overlapped by a block cursor.\",\n\t\t\"Color of whitespace characters in the editor.\",\n\t\t\"Color of editor line numbers.\",\n\t\t\"Color of the editor indentation guides.\",\n\t\t\"'editorIndentGuide.background' is deprecated. Use 'editorIndentGuide.background1' instead.\",\n\t\t\"Color of the active editor indentation guides.\",\n\t\t\"'editorIndentGuide.activeBackground' is deprecated. Use 'editorIndentGuide.activeBackground1' instead.\",\n\t\t\"Color of the editor indentation guides (1).\",\n\t\t\"Color of the editor indentation guides (2).\",\n\t\t\"Color of the editor indentation guides (3).\",\n\t\t\"Color of the editor indentation guides (4).\",\n\t\t\"Color of the editor indentation guides (5).\",\n\t\t\"Color of the editor indentation guides (6).\",\n\t\t\"Color of the active editor indentation guides (1).\",\n\t\t\"Color of the active editor indentation guides (2).\",\n\t\t\"Color of the active editor indentation guides (3).\",\n\t\t\"Color of the active editor indentation guides (4).\",\n\t\t\"Color of the active editor indentation guides (5).\",\n\t\t\"Color of the active editor indentation guides (6).\",\n\t\t\"Color of editor active line number\",\n\t\t\"Id is deprecated. Use 'editorLineNumber.activeForeground' instead.\",\n\t\t\"Color of editor active line number\",\n\t\t\"Color of the final editor line when editor.renderFinalNewline is set to dimmed.\",\n\t\t\"Color of the editor rulers.\",\n\t\t\"Foreground color of editor CodeLens\",\n\t\t\"Background color behind matching brackets\",\n\t\t\"Color for matching brackets boxes\",\n\t\t\"Color of the overview ruler border.\",\n\t\t\"Background color of the editor overview ruler.\",\n\t\t\"Background color of the editor gutter. The gutter contains the glyph margins and the line numbers.\",\n\t\t\"Border color of unnecessary (unused) source code in the editor.\",\n\t\t\"Opacity of unnecessary (unused) source code in the editor. For example, \\\"#000000c0\\\" will render the code with 75% opacity. For high contrast themes, use the  'editorUnnecessaryCode.border' theme color to underline unnecessary code instead of fading it out.\",\n\t\t\"Border color of ghost text in the editor.\",\n\t\t\"Foreground color of the ghost text in the editor.\",\n\t\t\"Background color of the ghost text in the editor.\",\n\t\t\"Overview ruler marker color for range highlights. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Overview ruler marker color for errors.\",\n\t\t\"Overview ruler marker color for warnings.\",\n\t\t\"Overview ruler marker color for infos.\",\n\t\t\"Foreground color of brackets (1). Requires enabling bracket pair colorization.\",\n\t\t\"Foreground color of brackets (2). Requires enabling bracket pair colorization.\",\n\t\t\"Foreground color of brackets (3). Requires enabling bracket pair colorization.\",\n\t\t\"Foreground color of brackets (4). Requires enabling bracket pair colorization.\",\n\t\t\"Foreground color of brackets (5). Requires enabling bracket pair colorization.\",\n\t\t\"Foreground color of brackets (6). Requires enabling bracket pair colorization.\",\n\t\t\"Foreground color of unexpected brackets.\",\n\t\t\"Background color of inactive bracket pair guides (1). Requires enabling bracket pair guides.\",\n\t\t\"Background color of inactive bracket pair guides (2). Requires enabling bracket pair guides.\",\n\t\t\"Background color of inactive bracket pair guides (3). Requires enabling bracket pair guides.\",\n\t\t\"Background color of inactive bracket pair guides (4). Requires enabling bracket pair guides.\",\n\t\t\"Background color of inactive bracket pair guides (5). Requires enabling bracket pair guides.\",\n\t\t\"Background color of inactive bracket pair guides (6). Requires enabling bracket pair guides.\",\n\t\t\"Background color of active bracket pair guides (1). Requires enabling bracket pair guides.\",\n\t\t\"Background color of active bracket pair guides (2). Requires enabling bracket pair guides.\",\n\t\t\"Background color of active bracket pair guides (3). Requires enabling bracket pair guides.\",\n\t\t\"Background color of active bracket pair guides (4). Requires enabling bracket pair guides.\",\n\t\t\"Background color of active bracket pair guides (5). Requires enabling bracket pair guides.\",\n\t\t\"Background color of active bracket pair guides (6). Requires enabling bracket pair guides.\",\n\t\t\"Border color used to highlight unicode characters.\",\n\t\t\"Background color used to highlight unicode characters.\"\n\t],\n\t\"vs/editor/common/editorContextKeys\": [\n\t\t\"Whether the editor text has focus (cursor is blinking)\",\n\t\t\"Whether the editor or an editor widget has focus (e.g. focus is in the find widget)\",\n\t\t\"Whether an editor or a rich text input has focus (cursor is blinking)\",\n\t\t\"Whether the editor is read-only\",\n\t\t\"Whether the context is a diff editor\",\n\t\t\"Whether the context is an embedded diff editor\",\n\t\t\"Whether the context is a multi diff editor\",\n\t\t\"Whether all files in multi diff editor are collapsed\",\n\t\t\"Whether the diff editor has changes\",\n\t\t\"Whether a moved code block is selected for comparison\",\n\t\t\"Whether the accessible diff viewer is visible\",\n\t\t\"Whether the diff editor render side by side inline breakpoint is reached\",\n\t\t\"Whether `editor.columnSelection` is enabled\",\n\t\t\"Whether the editor has text selected\",\n\t\t\"Whether the editor has multiple selections\",\n\t\t\"Whether `Tab` will move focus out of the editor\",\n\t\t\"Whether the editor hover is visible\",\n\t\t\"Whether the editor hover is focused\",\n\t\t\"Whether the sticky scroll is focused\",\n\t\t\"Whether the sticky scroll is visible\",\n\t\t\"Whether the standalone color picker is visible\",\n\t\t\"Whether the standalone color picker is focused\",\n\t\t\"Whether the editor is part of a larger editor (e.g. notebooks)\",\n\t\t\"The language identifier of the editor\",\n\t\t\"Whether the editor has a completion item provider\",\n\t\t\"Whether the editor has a code actions provider\",\n\t\t\"Whether the editor has a code lens provider\",\n\t\t\"Whether the editor has a definition provider\",\n\t\t\"Whether the editor has a declaration provider\",\n\t\t\"Whether the editor has an implementation provider\",\n\t\t\"Whether the editor has a type definition provider\",\n\t\t\"Whether the editor has a hover provider\",\n\t\t\"Whether the editor has a document highlight provider\",\n\t\t\"Whether the editor has a document symbol provider\",\n\t\t\"Whether the editor has a reference provider\",\n\t\t\"Whether the editor has a rename provider\",\n\t\t\"Whether the editor has a signature help provider\",\n\t\t\"Whether the editor has an inline hints provider\",\n\t\t\"Whether the editor has a document formatting provider\",\n\t\t\"Whether the editor has a document selection formatting provider\",\n\t\t\"Whether the editor has multiple document formatting providers\",\n\t\t\"Whether the editor has multiple document selection formatting providers\"\n\t],\n\t\"vs/editor/common/languages\": [\n\t\t\"array\",\n\t\t\"boolean\",\n\t\t\"class\",\n\t\t\"constant\",\n\t\t\"constructor\",\n\t\t\"enumeration\",\n\t\t\"enumeration member\",\n\t\t\"event\",\n\t\t\"field\",\n\t\t\"file\",\n\t\t\"function\",\n\t\t\"interface\",\n\t\t\"key\",\n\t\t\"method\",\n\t\t\"module\",\n\t\t\"namespace\",\n\t\t\"null\",\n\t\t\"number\",\n\t\t\"object\",\n\t\t\"operator\",\n\t\t\"package\",\n\t\t\"property\",\n\t\t\"string\",\n\t\t\"struct\",\n\t\t\"type parameter\",\n\t\t\"variable\",\n\t\t\"{0} ({1})\"\n\t],\n\t\"vs/editor/common/languages/modesRegistry\": [\n\t\t\"Plain Text\"\n\t],\n\t\"vs/editor/common/model/editStack\": [\n\t\t\"Typing\"\n\t],\n\t\"vs/editor/common/standaloneStrings\": [\n\t\t\"Developer: Inspect Tokens\",\n\t\t\"Go to Line/Column...\",\n\t\t\"Show all Quick Access Providers\",\n\t\t\"Command Palette\",\n\t\t\"Show And Run Commands\",\n\t\t\"Go to Symbol...\",\n\t\t\"Go to Symbol by Category...\",\n\t\t\"Editor content\",\n\t\t\"Press Alt+F1 for Accessibility Options.\",\n\t\t\"Toggle High Contrast Theme\",\n\t\t\"Made {0} edits in {1} files\"\n\t],\n\t\"vs/editor/common/viewLayout/viewLineRenderer\": [\n\t\t\"Show more ({0})\",\n\t\t\"{0} chars\"\n\t],\n\t\"vs/editor/contrib/anchorSelect/browser/anchorSelect\": [\n\t\t\"Selection Anchor\",\n\t\t\"Anchor set at {0}:{1}\",\n\t\t\"Set Selection Anchor\",\n\t\t\"Go to Selection Anchor\",\n\t\t\"Select from Anchor to Cursor\",\n\t\t\"Cancel Selection Anchor\"\n\t],\n\t\"vs/editor/contrib/bracketMatching/browser/bracketMatching\": [\n\t\t\"Overview ruler marker color for matching brackets.\",\n\t\t\"Go to Bracket\",\n\t\t\"Select to Bracket\",\n\t\t\"Remove Brackets\",\n\t\t\"Go to &&Bracket\",\n\t\t\"Select the text inside and including the brackets or curly braces\"\n\t],\n\t\"vs/editor/contrib/caretOperations/browser/caretOperations\": [\n\t\t\"Move Selected Text Left\",\n\t\t\"Move Selected Text Right\"\n\t],\n\t\"vs/editor/contrib/caretOperations/browser/transpose\": [\n\t\t\"Transpose Letters\"\n\t],\n\t\"vs/editor/contrib/clipboard/browser/clipboard\": [\n\t\t\"Cu&&t\",\n\t\t\"Cut\",\n\t\t\"Cut\",\n\t\t\"Cut\",\n\t\t\"&&Copy\",\n\t\t\"Copy\",\n\t\t\"Copy\",\n\t\t\"Copy\",\n\t\t\"Copy As\",\n\t\t\"Copy As\",\n\t\t\"Share\",\n\t\t\"Share\",\n\t\t\"Share\",\n\t\t\"&&Paste\",\n\t\t\"Paste\",\n\t\t\"Paste\",\n\t\t\"Paste\",\n\t\t\"Copy With Syntax Highlighting\"\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeAction\": [\n\t\t\"An unknown error occurred while applying the code action\"\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionCommands\": [\n\t\t\"Kind of the code action to run.\",\n\t\t\"Controls when the returned actions are applied.\",\n\t\t\"Always apply the first returned code action.\",\n\t\t\"Apply the first returned code action if it is the only one.\",\n\t\t\"Do not apply the returned code actions.\",\n\t\t\"Controls if only preferred code actions should be returned.\",\n\t\t\"Quick Fix...\",\n\t\t\"No code actions available\",\n\t\t\"No preferred code actions for '{0}' available\",\n\t\t\"No code actions for '{0}' available\",\n\t\t\"No preferred code actions available\",\n\t\t\"No code actions available\",\n\t\t\"Refactor...\",\n\t\t\"No preferred refactorings for '{0}' available\",\n\t\t\"No refactorings for '{0}' available\",\n\t\t\"No preferred refactorings available\",\n\t\t\"No refactorings available\",\n\t\t\"Source Action...\",\n\t\t\"No preferred source actions for '{0}' available\",\n\t\t\"No source actions for '{0}' available\",\n\t\t\"No preferred source actions available\",\n\t\t\"No source actions available\",\n\t\t\"Organize Imports\",\n\t\t\"No organize imports action available\",\n\t\t\"Fix All\",\n\t\t\"No fix all action available\",\n\t\t\"Auto Fix...\",\n\t\t\"No auto fixes available\"\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionContributions\": [\n\t\t\"Enable/disable showing group headers in the Code Action menu.\",\n\t\t\"Enable/disable showing nearest Quick Fix within a line when not currently on a diagnostic.\"\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionController\": [\n\t\t\"Context: {0} at line {1} and column {2}.\",\n\t\t\"Hide Disabled\",\n\t\t\"Show Disabled\"\n\t],\n\t\"vs/editor/contrib/codeAction/browser/codeActionMenu\": [\n\t\t\"More Actions...\",\n\t\t\"Quick Fix\",\n\t\t\"Extract\",\n\t\t\"Inline\",\n\t\t\"Rewrite\",\n\t\t\"Move\",\n\t\t\"Surround With\",\n\t\t\"Source Action\"\n\t],\n\t\"vs/editor/contrib/codeAction/browser/lightBulbWidget\": [\n\t\t\"Show Code Actions. Preferred Quick Fix Available ({0})\",\n\t\t\"Show Code Actions ({0})\",\n\t\t\"Show Code Actions\",\n\t\t\"Start Inline Chat ({0})\",\n\t\t\"Start Inline Chat\",\n\t\t\"Trigger AI Action\"\n\t],\n\t\"vs/editor/contrib/codelens/browser/codelensController\": [\n\t\t\"Show CodeLens Commands For Current Line\",\n\t\t\"Select a command\"\n\t],\n\t\"vs/editor/contrib/colorPicker/browser/colorPickerWidget\": [\n\t\t\"Click to toggle color options (rgb/hsl/hex)\",\n\t\t\"Icon to close the color picker\"\n\t],\n\t\"vs/editor/contrib/colorPicker/browser/standaloneColorPickerActions\": [\n\t\t\"Show or Focus Standalone Color Picker\",\n\t\t\"&&Show or Focus Standalone Color Picker\",\n\t\t\"Hide the Color Picker\",\n\t\t\"Insert Color with Standalone Color Picker\"\n\t],\n\t\"vs/editor/contrib/comment/browser/comment\": [\n\t\t\"Toggle Line Comment\",\n\t\t\"&&Toggle Line Comment\",\n\t\t\"Add Line Comment\",\n\t\t\"Remove Line Comment\",\n\t\t\"Toggle Block Comment\",\n\t\t\"Toggle &&Block Comment\"\n\t],\n\t\"vs/editor/contrib/contextmenu/browser/contextmenu\": [\n\t\t\"Minimap\",\n\t\t\"Render Characters\",\n\t\t\"Vertical size\",\n\t\t\"Proportional\",\n\t\t\"Fill\",\n\t\t\"Fit\",\n\t\t\"Slider\",\n\t\t\"Mouse Over\",\n\t\t\"Always\",\n\t\t\"Show Editor Context Menu\"\n\t],\n\t\"vs/editor/contrib/cursorUndo/browser/cursorUndo\": [\n\t\t\"Cursor Undo\",\n\t\t\"Cursor Redo\"\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/copyPasteContribution\": [\n\t\t\"Paste As...\",\n\t\t\"The id of the paste edit to try applying. If not provided, the editor will show a picker.\"\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/copyPasteController\": [\n\t\t\"Whether the paste widget is showing\",\n\t\t\"Show paste options...\",\n\t\t\"Running paste handlers. Click to cancel\",\n\t\t\"Select Paste Action\",\n\t\t\"Running paste handlers\"\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/defaultProviders\": [\n\t\t\"Built-in\",\n\t\t\"Insert Plain Text\",\n\t\t\"Insert Uris\",\n\t\t\"Insert Uri\",\n\t\t\"Insert Paths\",\n\t\t\"Insert Path\",\n\t\t\"Insert Relative Paths\",\n\t\t\"Insert Relative Path\"\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorContribution\": [\n\t\t\"Configures the default drop provider to use for content of a given mime type.\"\n\t],\n\t\"vs/editor/contrib/dropOrPasteInto/browser/dropIntoEditorController\": [\n\t\t\"Whether the drop widget is showing\",\n\t\t\"Show drop options...\",\n\t\t\"Running drop handlers. Click to cancel\"\n\t],\n\t\"vs/editor/contrib/editorState/browser/keybindingCancellation\": [\n\t\t\"Whether the editor runs a cancellable operation, e.g. like 'Peek References'\"\n\t],\n\t\"vs/editor/contrib/find/browser/findController\": [\n\t\t\"The file is too large to perform a replace all operation.\",\n\t\t\"Find\",\n\t\t\"&&Find\",\n\t\t\"Overrides \\\"Use Regular Expression\\\" flag.\\nThe flag will not be saved for the future.\\n0: Do Nothing\\n1: True\\n2: False\",\n\t\t\"Overrides \\\"Match Whole Word\\\" flag.\\nThe flag will not be saved for the future.\\n0: Do Nothing\\n1: True\\n2: False\",\n\t\t\"Overrides \\\"Math Case\\\" flag.\\nThe flag will not be saved for the future.\\n0: Do Nothing\\n1: True\\n2: False\",\n\t\t\"Overrides \\\"Preserve Case\\\" flag.\\nThe flag will not be saved for the future.\\n0: Do Nothing\\n1: True\\n2: False\",\n\t\t\"Find With Arguments\",\n\t\t\"Find With Selection\",\n\t\t\"Find Next\",\n\t\t\"Find Previous\",\n\t\t\"Go to Match...\",\n\t\t\"No matches. Try searching for something else.\",\n\t\t\"Type a number to go to a specific match (between 1 and {0})\",\n\t\t\"Please type a number between 1 and {0}\",\n\t\t\"Please type a number between 1 and {0}\",\n\t\t\"Find Next Selection\",\n\t\t\"Find Previous Selection\",\n\t\t\"Replace\",\n\t\t\"&&Replace\"\n\t],\n\t\"vs/editor/contrib/find/browser/findWidget\": [\n\t\t\"Icon for 'Find in Selection' in the editor find widget.\",\n\t\t\"Icon to indicate that the editor find widget is collapsed.\",\n\t\t\"Icon to indicate that the editor find widget is expanded.\",\n\t\t\"Icon for 'Replace' in the editor find widget.\",\n\t\t\"Icon for 'Replace All' in the editor find widget.\",\n\t\t\"Icon for 'Find Previous' in the editor find widget.\",\n\t\t\"Icon for 'Find Next' in the editor find widget.\",\n\t\t\"Find / Replace\",\n\t\t\"Find\",\n\t\t\"Find\",\n\t\t\"Previous Match\",\n\t\t\"Next Match\",\n\t\t\"Find in Selection\",\n\t\t\"Close\",\n\t\t\"Replace\",\n\t\t\"Replace\",\n\t\t\"Replace\",\n\t\t\"Replace All\",\n\t\t\"Toggle Replace\",\n\t\t\"Only the first {0} results are highlighted, but all find operations work on the entire text.\",\n\t\t\"{0} of {1}\",\n\t\t\"No results\",\n\t\t\"{0} found\",\n\t\t\"{0} found for '{1}'\",\n\t\t\"{0} found for '{1}', at {2}\",\n\t\t\"{0} found for '{1}'\",\n\t\t\"Ctrl+Enter now inserts line break instead of replacing all. You can modify the keybinding for editor.action.replaceAll to override this behavior.\"\n\t],\n\t\"vs/editor/contrib/folding/browser/folding\": [\n\t\t\"Unfold\",\n\t\t\"Unfold Recursively\",\n\t\t\"Fold\",\n\t\t\"Toggle Fold\",\n\t\t\"Fold Recursively\",\n\t\t\"Fold All Block Comments\",\n\t\t\"Fold All Regions\",\n\t\t\"Unfold All Regions\",\n\t\t\"Fold All Except Selected\",\n\t\t\"Unfold All Except Selected\",\n\t\t\"Fold All\",\n\t\t\"Unfold All\",\n\t\t\"Go to Parent Fold\",\n\t\t\"Go to Previous Folding Range\",\n\t\t\"Go to Next Folding Range\",\n\t\t\"Create Folding Range from Selection\",\n\t\t\"Remove Manual Folding Ranges\",\n\t\t\"Fold Level {0}\"\n\t],\n\t\"vs/editor/contrib/folding/browser/foldingDecorations\": [\n\t\t\"Background color behind folded ranges. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Color of the folding control in the editor gutter.\",\n\t\t\"Icon for expanded ranges in the editor glyph margin.\",\n\t\t\"Icon for collapsed ranges in the editor glyph margin.\",\n\t\t\"Icon for manually collapsed ranges in the editor glyph margin.\",\n\t\t\"Icon for manually expanded ranges in the editor glyph margin.\"\n\t],\n\t\"vs/editor/contrib/fontZoom/browser/fontZoom\": [\n\t\t\"Editor Font Zoom In\",\n\t\t\"Editor Font Zoom Out\",\n\t\t\"Editor Font Zoom Reset\"\n\t],\n\t\"vs/editor/contrib/format/browser/formatActions\": [\n\t\t\"Format Document\",\n\t\t\"Format Selection\"\n\t],\n\t\"vs/editor/contrib/gotoError/browser/gotoError\": [\n\t\t\"Go to Next Problem (Error, Warning, Info)\",\n\t\t\"Icon for goto next marker.\",\n\t\t\"Go to Previous Problem (Error, Warning, Info)\",\n\t\t\"Icon for goto previous marker.\",\n\t\t\"Go to Next Problem in Files (Error, Warning, Info)\",\n\t\t\"Next &&Problem\",\n\t\t\"Go to Previous Problem in Files (Error, Warning, Info)\",\n\t\t\"Previous &&Problem\"\n\t],\n\t\"vs/editor/contrib/gotoError/browser/gotoErrorWidget\": [\n\t\t\"Error\",\n\t\t\"Warning\",\n\t\t\"Info\",\n\t\t\"Hint\",\n\t\t\"{0} at {1}. \",\n\t\t\"{0} of {1} problems\",\n\t\t\"{0} of {1} problem\",\n\t\t\"Editor marker navigation widget error color.\",\n\t\t\"Editor marker navigation widget error heading background.\",\n\t\t\"Editor marker navigation widget warning color.\",\n\t\t\"Editor marker navigation widget warning heading background.\",\n\t\t\"Editor marker navigation widget info color.\",\n\t\t\"Editor marker navigation widget info heading background.\",\n\t\t\"Editor marker navigation widget background.\"\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/goToCommands\": [\n\t\t\"Peek\",\n\t\t\"Definitions\",\n\t\t\"No definition found for '{0}'\",\n\t\t\"No definition found\",\n\t\t\"Go to Definition\",\n\t\t\"Go to &&Definition\",\n\t\t\"Open Definition to the Side\",\n\t\t\"Peek Definition\",\n\t\t\"Declarations\",\n\t\t\"No declaration found for '{0}'\",\n\t\t\"No declaration found\",\n\t\t\"Go to Declaration\",\n\t\t\"Go to &&Declaration\",\n\t\t\"No declaration found for '{0}'\",\n\t\t\"No declaration found\",\n\t\t\"Peek Declaration\",\n\t\t\"Type Definitions\",\n\t\t\"No type definition found for '{0}'\",\n\t\t\"No type definition found\",\n\t\t\"Go to Type Definition\",\n\t\t\"Go to &&Type Definition\",\n\t\t\"Peek Type Definition\",\n\t\t\"Implementations\",\n\t\t\"No implementation found for '{0}'\",\n\t\t\"No implementation found\",\n\t\t\"Go to Implementations\",\n\t\t\"Go to &&Implementations\",\n\t\t\"Peek Implementations\",\n\t\t\"No references found for '{0}'\",\n\t\t\"No references found\",\n\t\t\"Go to References\",\n\t\t\"Go to &&References\",\n\t\t\"References\",\n\t\t\"Peek References\",\n\t\t\"References\",\n\t\t\"Go to Any Symbol\",\n\t\t\"Locations\",\n\t\t\"No results for '{0}'\",\n\t\t\"References\"\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition\": [\n\t\t\"Click to show {0} definitions.\"\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesController\": [\n\t\t\"Whether reference peek is visible, like 'Peek References' or 'Peek Definition'\",\n\t\t\"Loading...\",\n\t\t\"{0} ({1})\"\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesTree\": [\n\t\t\"{0} references\",\n\t\t\"{0} reference\",\n\t\t\"References\"\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget\": [\n\t\t\"no preview available\",\n\t\t\"No results\",\n\t\t\"References\"\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/referencesModel\": [\n\t\t\"in {0} on line {1} at column {2}\",\n\t\t\"{0} in {1} on line {2} at column {3}\",\n\t\t\"1 symbol in {0}, full path {1}\",\n\t\t\"{0} symbols in {1}, full path {2}\",\n\t\t\"No results found\",\n\t\t\"Found 1 symbol in {0}\",\n\t\t\"Found {0} symbols in {1}\",\n\t\t\"Found {0} symbols in {1} files\"\n\t],\n\t\"vs/editor/contrib/gotoSymbol/browser/symbolNavigation\": [\n\t\t\"Whether there are symbol locations that can be navigated via keyboard-only.\",\n\t\t\"Symbol {0} of {1}, {2} for next\",\n\t\t\"Symbol {0} of {1}\"\n\t],\n\t\"vs/editor/contrib/hover/browser/hover\": [\n\t\t\"Show or Focus Hover\",\n\t\t\"The hover will not automatically take focus.\",\n\t\t\"The hover will take focus only if it is already visible.\",\n\t\t\"The hover will automatically take focus when it appears.\",\n\t\t\"Show Definition Preview Hover\",\n\t\t\"Scroll Up Hover\",\n\t\t\"Scroll Down Hover\",\n\t\t\"Scroll Left Hover\",\n\t\t\"Scroll Right Hover\",\n\t\t\"Page Up Hover\",\n\t\t\"Page Down Hover\",\n\t\t\"Go To Top Hover\",\n\t\t\"Go To Bottom Hover\"\n\t],\n\t\"vs/editor/contrib/hover/browser/markdownHoverParticipant\": [\n\t\t\"Loading...\",\n\t\t\"Rendering paused for long line for performance reasons. This can be configured via `editor.stopRenderingLineAfter`.\",\n\t\t\"Tokenization is skipped for long lines for performance reasons. This can be configured via `editor.maxTokenizationLineLength`.\"\n\t],\n\t\"vs/editor/contrib/hover/browser/markerHoverParticipant\": [\n\t\t\"View Problem\",\n\t\t\"No quick fixes available\",\n\t\t\"Checking for quick fixes...\",\n\t\t\"No quick fixes available\",\n\t\t\"Quick Fix...\"\n\t],\n\t\"vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace\": [\n\t\t\"Replace with Previous Value\",\n\t\t\"Replace with Next Value\"\n\t],\n\t\"vs/editor/contrib/indentation/browser/indentation\": [\n\t\t\"Convert Indentation to Spaces\",\n\t\t\"Convert Indentation to Tabs\",\n\t\t\"Configured Tab Size\",\n\t\t\"Default Tab Size\",\n\t\t\"Current Tab Size\",\n\t\t\"Select Tab Size for Current File\",\n\t\t\"Indent Using Tabs\",\n\t\t\"Indent Using Spaces\",\n\t\t\"Change Tab Display Size\",\n\t\t\"Detect Indentation from Content\",\n\t\t\"Reindent Lines\",\n\t\t\"Reindent Selected Lines\"\n\t],\n\t\"vs/editor/contrib/inlayHints/browser/inlayHintsHover\": [\n\t\t\"Double-click to insert\",\n\t\t\"cmd + click\",\n\t\t\"ctrl + click\",\n\t\t\"option + click\",\n\t\t\"alt + click\",\n\t\t\"Go to Definition ({0}), right click for more\",\n\t\t\"Go to Definition ({0})\",\n\t\t\"Execute Command\"\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/commands\": [\n\t\t\"Show Next Inline Suggestion\",\n\t\t\"Show Previous Inline Suggestion\",\n\t\t\"Trigger Inline Suggestion\",\n\t\t\"Accept Next Word Of Inline Suggestion\",\n\t\t\"Accept Word\",\n\t\t\"Accept Next Line Of Inline Suggestion\",\n\t\t\"Accept Line\",\n\t\t\"Accept Inline Suggestion\",\n\t\t\"Accept\",\n\t\t\"Hide Inline Suggestion\",\n\t\t\"Always Show Toolbar\"\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/hoverParticipant\": [\n\t\t\"Suggestion:\"\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionContextKeys\": [\n\t\t\"Whether an inline suggestion is visible\",\n\t\t\"Whether the inline suggestion starts with whitespace\",\n\t\t\"Whether the inline suggestion starts with whitespace that is less than what would be inserted by tab\",\n\t\t\"Whether suggestions should be suppressed for the current suggestion\"\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsController\": [\n\t\t\"Inspect this in the accessible view ({0})\"\n\t],\n\t\"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHintsWidget\": [\n\t\t\"Icon for show next parameter hint.\",\n\t\t\"Icon for show previous parameter hint.\",\n\t\t\"{0} ({1})\",\n\t\t\"Previous\",\n\t\t\"Next\"\n\t],\n\t\"vs/editor/contrib/lineSelection/browser/lineSelection\": [\n\t\t\"Expand Line Selection\"\n\t],\n\t\"vs/editor/contrib/linesOperations/browser/linesOperations\": [\n\t\t\"Copy Line Up\",\n\t\t\"&&Copy Line Up\",\n\t\t\"Copy Line Down\",\n\t\t\"Co&&py Line Down\",\n\t\t\"Duplicate Selection\",\n\t\t\"&&Duplicate Selection\",\n\t\t\"Move Line Up\",\n\t\t\"Mo&&ve Line Up\",\n\t\t\"Move Line Down\",\n\t\t\"Move &&Line Down\",\n\t\t\"Sort Lines Ascending\",\n\t\t\"Sort Lines Descending\",\n\t\t\"Delete Duplicate Lines\",\n\t\t\"Trim Trailing Whitespace\",\n\t\t\"Delete Line\",\n\t\t\"Indent Line\",\n\t\t\"Outdent Line\",\n\t\t\"Insert Line Above\",\n\t\t\"Insert Line Below\",\n\t\t\"Delete All Left\",\n\t\t\"Delete All Right\",\n\t\t\"Join Lines\",\n\t\t\"Transpose Characters around the Cursor\",\n\t\t\"Transform to Uppercase\",\n\t\t\"Transform to Lowercase\",\n\t\t\"Transform to Title Case\",\n\t\t\"Transform to Snake Case\",\n\t\t\"Transform to Camel Case\",\n\t\t\"Transform to Kebab Case\"\n\t],\n\t\"vs/editor/contrib/linkedEditing/browser/linkedEditing\": [\n\t\t\"Start Linked Editing\",\n\t\t\"Background color when the editor auto renames on type.\"\n\t],\n\t\"vs/editor/contrib/links/browser/links\": [\n\t\t\"Failed to open this link because it is not well-formed: {0}\",\n\t\t\"Failed to open this link because its target is missing.\",\n\t\t\"Execute command\",\n\t\t\"Follow link\",\n\t\t\"cmd + click\",\n\t\t\"ctrl + click\",\n\t\t\"option + click\",\n\t\t\"alt + click\",\n\t\t\"Execute command {0}\",\n\t\t\"Open Link\"\n\t],\n\t\"vs/editor/contrib/message/browser/messageController\": [\n\t\t\"Whether the editor is currently showing an inline message\"\n\t],\n\t\"vs/editor/contrib/multicursor/browser/multicursor\": [\n\t\t\"Cursor added: {0}\",\n\t\t\"Cursors added: {0}\",\n\t\t\"Add Cursor Above\",\n\t\t\"&&Add Cursor Above\",\n\t\t\"Add Cursor Below\",\n\t\t\"A&&dd Cursor Below\",\n\t\t\"Add Cursors to Line Ends\",\n\t\t\"Add C&&ursors to Line Ends\",\n\t\t\"Add Cursors To Bottom\",\n\t\t\"Add Cursors To Top\",\n\t\t\"Add Selection To Next Find Match\",\n\t\t\"Add &&Next Occurrence\",\n\t\t\"Add Selection To Previous Find Match\",\n\t\t\"Add P&&revious Occurrence\",\n\t\t\"Move Last Selection To Next Find Match\",\n\t\t\"Move Last Selection To Previous Find Match\",\n\t\t\"Select All Occurrences of Find Match\",\n\t\t\"Select All &&Occurrences\",\n\t\t\"Change All Occurrences\",\n\t\t\"Focus Next Cursor\",\n\t\t\"Focuses the next cursor\",\n\t\t\"Focus Previous Cursor\",\n\t\t\"Focuses the previous cursor\"\n\t],\n\t\"vs/editor/contrib/parameterHints/browser/parameterHints\": [\n\t\t\"Trigger Parameter Hints\"\n\t],\n\t\"vs/editor/contrib/parameterHints/browser/parameterHintsWidget\": [\n\t\t\"Icon for show next parameter hint.\",\n\t\t\"Icon for show previous parameter hint.\",\n\t\t\"{0}, hint\",\n\t\t\"Foreground color of the active item in the parameter hint.\"\n\t],\n\t\"vs/editor/contrib/peekView/browser/peekView\": [\n\t\t\"Whether the current code editor is embedded inside peek\",\n\t\t\"Close\",\n\t\t\"Background color of the peek view title area.\",\n\t\t\"Color of the peek view title.\",\n\t\t\"Color of the peek view title info.\",\n\t\t\"Color of the peek view borders and arrow.\",\n\t\t\"Background color of the peek view result list.\",\n\t\t\"Foreground color for line nodes in the peek view result list.\",\n\t\t\"Foreground color for file nodes in the peek view result list.\",\n\t\t\"Background color of the selected entry in the peek view result list.\",\n\t\t\"Foreground color of the selected entry in the peek view result list.\",\n\t\t\"Background color of the peek view editor.\",\n\t\t\"Background color of the gutter in the peek view editor.\",\n\t\t\"Background color of sticky scroll in the peek view editor.\",\n\t\t\"Match highlight color in the peek view result list.\",\n\t\t\"Match highlight color in the peek view editor.\",\n\t\t\"Match highlight border in the peek view editor.\"\n\t],\n\t\"vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess\": [\n\t\t\"Open a text editor first to go to a line.\",\n\t\t\"Go to line {0} and character {1}.\",\n\t\t\"Go to line {0}.\",\n\t\t\"Current Line: {0}, Character: {1}. Type a line number between 1 and {2} to navigate to.\",\n\t\t\"Current Line: {0}, Character: {1}. Type a line number to navigate to.\"\n\t],\n\t\"vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess\": [\n\t\t\"To go to a symbol, first open a text editor with symbol information.\",\n\t\t\"The active text editor does not provide symbol information.\",\n\t\t\"No matching editor symbols\",\n\t\t\"No editor symbols\",\n\t\t\"Open to the Side\",\n\t\t\"Open to the Bottom\",\n\t\t\"symbols ({0})\",\n\t\t\"properties ({0})\",\n\t\t\"methods ({0})\",\n\t\t\"functions ({0})\",\n\t\t\"constructors ({0})\",\n\t\t\"variables ({0})\",\n\t\t\"classes ({0})\",\n\t\t\"structs ({0})\",\n\t\t\"events ({0})\",\n\t\t\"operators ({0})\",\n\t\t\"interfaces ({0})\",\n\t\t\"namespaces ({0})\",\n\t\t\"packages ({0})\",\n\t\t\"type parameters ({0})\",\n\t\t\"modules ({0})\",\n\t\t\"properties ({0})\",\n\t\t\"enumerations ({0})\",\n\t\t\"enumeration members ({0})\",\n\t\t\"strings ({0})\",\n\t\t\"files ({0})\",\n\t\t\"arrays ({0})\",\n\t\t\"numbers ({0})\",\n\t\t\"booleans ({0})\",\n\t\t\"objects ({0})\",\n\t\t\"keys ({0})\",\n\t\t\"fields ({0})\",\n\t\t\"constants ({0})\"\n\t],\n\t\"vs/editor/contrib/readOnlyMessage/browser/contribution\": [\n\t\t\"Cannot edit in read-only input\",\n\t\t\"Cannot edit in read-only editor\"\n\t],\n\t\"vs/editor/contrib/rename/browser/rename\": [\n\t\t\"No result.\",\n\t\t\"An unknown error occurred while resolving rename location\",\n\t\t\"Renaming '{0}' to '{1}'\",\n\t\t\"Renaming {0} to {1}\",\n\t\t\"Successfully renamed '{0}' to '{1}'. Summary: {2}\",\n\t\t\"Rename failed to apply edits\",\n\t\t\"Rename failed to compute edits\",\n\t\t\"Rename Symbol\",\n\t\t\"Enable/disable the ability to preview changes before renaming\"\n\t],\n\t\"vs/editor/contrib/rename/browser/renameInputField\": [\n\t\t\"Whether the rename input widget is visible\",\n\t\t\"Rename input. Type new name and press Enter to commit.\",\n\t\t\"{0} to Rename, {1} to Preview\"\n\t],\n\t\"vs/editor/contrib/smartSelect/browser/smartSelect\": [\n\t\t\"Expand Selection\",\n\t\t\"&&Expand Selection\",\n\t\t\"Shrink Selection\",\n\t\t\"&&Shrink Selection\"\n\t],\n\t\"vs/editor/contrib/snippet/browser/snippetController2\": [\n\t\t\"Whether the editor in current in snippet mode\",\n\t\t\"Whether there is a next tab stop when in snippet mode\",\n\t\t\"Whether there is a previous tab stop when in snippet mode\",\n\t\t\"Go to next placeholder...\"\n\t],\n\t\"vs/editor/contrib/snippet/browser/snippetVariables\": [\n\t\t\"Sunday\",\n\t\t\"Monday\",\n\t\t\"Tuesday\",\n\t\t\"Wednesday\",\n\t\t\"Thursday\",\n\t\t\"Friday\",\n\t\t\"Saturday\",\n\t\t\"Sun\",\n\t\t\"Mon\",\n\t\t\"Tue\",\n\t\t\"Wed\",\n\t\t\"Thu\",\n\t\t\"Fri\",\n\t\t\"Sat\",\n\t\t\"January\",\n\t\t\"February\",\n\t\t\"March\",\n\t\t\"April\",\n\t\t\"May\",\n\t\t\"June\",\n\t\t\"July\",\n\t\t\"August\",\n\t\t\"September\",\n\t\t\"October\",\n\t\t\"November\",\n\t\t\"December\",\n\t\t\"Jan\",\n\t\t\"Feb\",\n\t\t\"Mar\",\n\t\t\"Apr\",\n\t\t\"May\",\n\t\t\"Jun\",\n\t\t\"Jul\",\n\t\t\"Aug\",\n\t\t\"Sep\",\n\t\t\"Oct\",\n\t\t\"Nov\",\n\t\t\"Dec\"\n\t],\n\t\"vs/editor/contrib/stickyScroll/browser/stickyScrollActions\": [\n\t\t\"Toggle Sticky Scroll\",\n\t\t\"&&Toggle Sticky Scroll\",\n\t\t\"Sticky Scroll\",\n\t\t\"&&Sticky Scroll\",\n\t\t\"Focus Sticky Scroll\",\n\t\t\"&&Focus Sticky Scroll\",\n\t\t\"Select next sticky scroll line\",\n\t\t\"Select previous sticky scroll line\",\n\t\t\"Go to focused sticky scroll line\",\n\t\t\"Select Editor\"\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggest\": [\n\t\t\"Whether any suggestion is focused\",\n\t\t\"Whether suggestion details are visible\",\n\t\t\"Whether there are multiple suggestions to pick from\",\n\t\t\"Whether inserting the current suggestion yields in a change or has everything already been typed\",\n\t\t\"Whether suggestions are inserted when pressing Enter\",\n\t\t\"Whether the current suggestion has insert and replace behaviour\",\n\t\t\"Whether the default behaviour is to insert or replace\",\n\t\t\"Whether the current suggestion supports to resolve further details\"\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestController\": [\n\t\t\"Accepting '{0}' made {1} additional edits\",\n\t\t\"Trigger Suggest\",\n\t\t\"Insert\",\n\t\t\"Insert\",\n\t\t\"Replace\",\n\t\t\"Replace\",\n\t\t\"Insert\",\n\t\t\"show less\",\n\t\t\"show more\",\n\t\t\"Reset Suggest Widget Size\"\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidget\": [\n\t\t\"Background color of the suggest widget.\",\n\t\t\"Border color of the suggest widget.\",\n\t\t\"Foreground color of the suggest widget.\",\n\t\t\"Foreground color of the selected entry in the suggest widget.\",\n\t\t\"Icon foreground color of the selected entry in the suggest widget.\",\n\t\t\"Background color of the selected entry in the suggest widget.\",\n\t\t\"Color of the match highlights in the suggest widget.\",\n\t\t\"Color of the match highlights in the suggest widget when an item is focused.\",\n\t\t\"Foreground color of the suggest widget status.\",\n\t\t\"Loading...\",\n\t\t\"No suggestions.\",\n\t\t\"Suggest\",\n\t\t\"{0} {1}, {2}\",\n\t\t\"{0} {1}\",\n\t\t\"{0}, {1}\",\n\t\t\"{0}, docs: {1}\"\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetDetails\": [\n\t\t\"Close\",\n\t\t\"Loading...\"\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetRenderer\": [\n\t\t\"Icon for more information in the suggest widget.\",\n\t\t\"Read More\"\n\t],\n\t\"vs/editor/contrib/suggest/browser/suggestWidgetStatus\": [\n\t\t\"{0} ({1})\"\n\t],\n\t\"vs/editor/contrib/symbolIcons/browser/symbolIcons\": [\n\t\t\"The foreground color for array symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for boolean symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for class symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for color symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for constant symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for constructor symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for enumerator symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for enumerator member symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for event symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for field symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for file symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for folder symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for function symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for interface symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for key symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for keyword symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for method symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for module symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for namespace symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for null symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for number symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for object symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for operator symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for package symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for property symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for reference symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for snippet symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for string symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for struct symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for text symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for type parameter symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for unit symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\",\n\t\t\"The foreground color for variable symbols. These symbols appear in the outline, breadcrumb, and suggest widget.\"\n\t],\n\t\"vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode\": [\n\t\t\"Toggle Tab Key Moves Focus\",\n\t\t\"Pressing Tab will now move focus to the next focusable element\",\n\t\t\"Pressing Tab will now insert the tab character\"\n\t],\n\t\"vs/editor/contrib/tokenization/browser/tokenization\": [\n\t\t\"Developer: Force Retokenize\"\n\t],\n\t\"vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter\": [\n\t\t\"Icon shown with a warning message in the extensions editor.\",\n\t\t\"This document contains many non-basic ASCII unicode characters\",\n\t\t\"This document contains many ambiguous unicode characters\",\n\t\t\"This document contains many invisible unicode characters\",\n\t\t\"The character {0} could be confused with the ASCII character {1}, which is more common in source code.\",\n\t\t\"The character {0} could be confused with the character {1}, which is more common in source code.\",\n\t\t\"The character {0} is invisible.\",\n\t\t\"The character {0} is not a basic ASCII character.\",\n\t\t\"Adjust settings\",\n\t\t\"Disable Highlight In Comments\",\n\t\t\"Disable highlighting of characters in comments\",\n\t\t\"Disable Highlight In Strings\",\n\t\t\"Disable highlighting of characters in strings\",\n\t\t\"Disable Ambiguous Highlight\",\n\t\t\"Disable highlighting of ambiguous characters\",\n\t\t\"Disable Invisible Highlight\",\n\t\t\"Disable highlighting of invisible characters\",\n\t\t\"Disable Non ASCII Highlight\",\n\t\t\"Disable highlighting of non basic ASCII characters\",\n\t\t\"Show Exclude Options\",\n\t\t\"Exclude {0} (invisible character) from being highlighted\",\n\t\t\"Exclude {0} from being highlighted\",\n\t\t\"Allow unicode characters that are more common in the language \\\"{0}\\\".\",\n\t\t\"Configure Unicode Highlight Options\"\n\t],\n\t\"vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators\": [\n\t\t\"Unusual Line Terminators\",\n\t\t\"Detected unusual line terminators\",\n\t\t\"The file '{0}' contains one or more unusual line terminator characters, like Line Separator (LS) or Paragraph Separator (PS).\\n\\nIt is recommended to remove them from the file. This can be configured via `editor.unusualLineTerminators`.\",\n\t\t\"&&Remove Unusual Line Terminators\",\n\t\t\"Ignore\"\n\t],\n\t\"vs/editor/contrib/wordHighlighter/browser/highlightDecorations\": [\n\t\t\"Background color of a symbol during read-access, like reading a variable. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Background color of a symbol during write-access, like writing to a variable. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Background color of a textual occurrence for a symbol. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Border color of a symbol during read-access, like reading a variable.\",\n\t\t\"Border color of a symbol during write-access, like writing to a variable.\",\n\t\t\"Border color of a textual occurrence for a symbol.\",\n\t\t\"Overview ruler marker color for symbol highlights. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Overview ruler marker color for write-access symbol highlights. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Overview ruler marker color of a textual occurrence for a symbol. The color must not be opaque so as not to hide underlying decorations.\"\n\t],\n\t\"vs/editor/contrib/wordHighlighter/browser/wordHighlighter\": [\n\t\t\"Go to Next Symbol Highlight\",\n\t\t\"Go to Previous Symbol Highlight\",\n\t\t\"Trigger Symbol Highlight\"\n\t],\n\t\"vs/editor/contrib/wordOperations/browser/wordOperations\": [\n\t\t\"Delete Word\"\n\t],\n\t\"vs/platform/action/common/actionCommonCategories\": [\n\t\t\"View\",\n\t\t\"Help\",\n\t\t\"Test\",\n\t\t\"File\",\n\t\t\"Preferences\",\n\t\t\"Developer\"\n\t],\n\t\"vs/platform/actionWidget/browser/actionList\": [\n\t\t\"{0} to apply, {1} to preview\",\n\t\t\"{0} to apply\",\n\t\t\"{0}, Disabled Reason: {1}\",\n\t\t\"Action Widget\"\n\t],\n\t\"vs/platform/actionWidget/browser/actionWidget\": [\n\t\t\"Background color for toggled action items in action bar.\",\n\t\t\"Whether the action widget list is visible\",\n\t\t\"Hide action widget\",\n\t\t\"Select previous action\",\n\t\t\"Select next action\",\n\t\t\"Accept selected action\",\n\t\t\"Preview selected action\"\n\t],\n\t\"vs/platform/actions/browser/menuEntryActionViewItem\": [\n\t\t\"{0} ({1})\",\n\t\t\"{0} ({1})\",\n\t\t\"{0}\\n[{1}] {2}\"\n\t],\n\t\"vs/platform/actions/browser/toolbar\": [\n\t\t\"Hide\",\n\t\t\"Reset Menu\"\n\t],\n\t\"vs/platform/actions/common/menuService\": [\n\t\t\"Hide '{0}'\"\n\t],\n\t\"vs/platform/audioCues/browser/audioCueService\": [\n\t\t\"Error on Line\",\n\t\t\"Warning on Line\",\n\t\t\"Folded Area on Line\",\n\t\t\"Breakpoint on Line\",\n\t\t\"Inline Suggestion on Line\",\n\t\t\"Terminal Quick Fix\",\n\t\t\"Debugger Stopped on Breakpoint\",\n\t\t\"No Inlay Hints on Line\",\n\t\t\"Task Completed\",\n\t\t\"Task Failed\",\n\t\t\"Terminal Command Failed\",\n\t\t\"Terminal Bell\",\n\t\t\"Notebook Cell Completed\",\n\t\t\"Notebook Cell Failed\",\n\t\t\"Diff Line Inserted\",\n\t\t\"Diff Line Deleted\",\n\t\t\"Diff Line Modified\",\n\t\t\"Chat Request Sent\",\n\t\t\"Chat Response Received\",\n\t\t\"Chat Response Pending\",\n\t\t\"Clear\",\n\t\t\"Save\",\n\t\t\"Format\"\n\t],\n\t\"vs/platform/configuration/common/configurationRegistry\": [\n\t\t\"Default Language Configuration Overrides\",\n\t\t\"Configure settings to be overridden for the {0} language.\",\n\t\t\"Configure editor settings to be overridden for a language.\",\n\t\t\"This setting does not support per-language configuration.\",\n\t\t\"Configure editor settings to be overridden for a language.\",\n\t\t\"This setting does not support per-language configuration.\",\n\t\t\"Cannot register an empty property\",\n\t\t\"Cannot register '{0}'. This matches property pattern '\\\\\\\\[.*\\\\\\\\]$' for describing language specific editor settings. Use 'configurationDefaults' contribution.\",\n\t\t\"Cannot register '{0}'. This property is already registered.\",\n\t\t\"Cannot register '{0}'. The associated policy {1} is already registered with {2}.\"\n\t],\n\t\"vs/platform/contextkey/browser/contextKeyService\": [\n\t\t\"A command that returns information about context keys\"\n\t],\n\t\"vs/platform/contextkey/common/contextkey\": [\n\t\t\"Empty context key expression\",\n\t\t\"Did you forget to write an expression? You can also put 'false' or 'true' to always evaluate to false or true, respectively.\",\n\t\t\"'in' after 'not'.\",\n\t\t\"closing parenthesis ')'\",\n\t\t\"Unexpected token\",\n\t\t\"Did you forget to put && or || before the token?\",\n\t\t\"Unexpected end of expression\",\n\t\t\"Did you forget to put a context key?\",\n\t\t\"Expected: {0}\\nReceived: '{1}'.\"\n\t],\n\t\"vs/platform/contextkey/common/contextkeys\": [\n\t\t\"Whether the operating system is macOS\",\n\t\t\"Whether the operating system is Linux\",\n\t\t\"Whether the operating system is Windows\",\n\t\t\"Whether the platform is a web browser\",\n\t\t\"Whether the operating system is macOS on a non-browser platform\",\n\t\t\"Whether the operating system is iOS\",\n\t\t\"Whether the platform is a mobile web browser\",\n\t\t\"Quality type of VS Code\",\n\t\t\"Whether keyboard focus is inside an input box\"\n\t],\n\t\"vs/platform/contextkey/common/scanner\": [\n\t\t\"Did you mean {0}?\",\n\t\t\"Did you mean {0} or {1}?\",\n\t\t\"Did you mean {0}, {1} or {2}?\",\n\t\t\"Did you forget to open or close the quote?\",\n\t\t\"Did you forget to escape the '/' (slash) character? Put two backslashes before it to escape, e.g., '\\\\\\\\/'.\"\n\t],\n\t\"vs/platform/history/browser/contextScopedHistoryWidget\": [\n\t\t\"Whether suggestion are visible\"\n\t],\n\t\"vs/platform/keybinding/common/abstractKeybindingService\": [\n\t\t\"({0}) was pressed. Waiting for second key of chord...\",\n\t\t\"({0}) was pressed. Waiting for next key of chord...\",\n\t\t\"The key combination ({0}, {1}) is not a command.\",\n\t\t\"The key combination ({0}, {1}) is not a command.\"\n\t],\n\t\"vs/platform/list/browser/listService\": [\n\t\t\"Workbench\",\n\t\t\"Maps to `Control` on Windows and Linux and to `Command` on macOS.\",\n\t\t\"Maps to `Alt` on Windows and Linux and to `Option` on macOS.\",\n\t\t\"The modifier to be used to add an item in trees and lists to a multi-selection with the mouse (for example in the explorer, open editors and scm view). The 'Open to Side' mouse gestures - if supported - will adapt such that they do not conflict with the multiselect modifier.\",\n\t\t\"Controls how to open items in trees and lists using the mouse (if supported). Note that some trees and lists might choose to ignore this setting if it is not applicable.\",\n\t\t\"Controls whether lists and trees support horizontal scrolling in the workbench. Warning: turning on this setting has a performance implication.\",\n\t\t\"Controls whether clicks in the scrollbar scroll page by page.\",\n\t\t\"Controls tree indentation in pixels.\",\n\t\t\"Controls whether the tree should render indent guides.\",\n\t\t\"Controls whether lists and trees have smooth scrolling.\",\n\t\t\"A multiplier to be used on the `deltaX` and `deltaY` of mouse wheel scroll events.\",\n\t\t\"Scrolling speed multiplier when pressing `Alt`.\",\n\t\t\"Highlight elements when searching. Further up and down navigation will traverse only the highlighted elements.\",\n\t\t\"Filter elements when searching.\",\n\t\t\"Controls the default find mode for lists and trees in the workbench.\",\n\t\t\"Simple keyboard navigation focuses elements which match the keyboard input. Matching is done only on prefixes.\",\n\t\t\"Highlight keyboard navigation highlights elements which match the keyboard input. Further up and down navigation will traverse only the highlighted elements.\",\n\t\t\"Filter keyboard navigation will filter out and hide all the elements which do not match the keyboard input.\",\n\t\t\"Controls the keyboard navigation style for lists and trees in the workbench. Can be simple, highlight and filter.\",\n\t\t\"Please use 'workbench.list.defaultFindMode' and\\t'workbench.list.typeNavigationMode' instead.\",\n\t\t\"Use fuzzy matching when searching.\",\n\t\t\"Use contiguous matching when searching.\",\n\t\t\"Controls the type of matching used when searching lists and trees in the workbench.\",\n\t\t\"Controls how tree folders are expanded when clicking the folder names. Note that some trees and lists might choose to ignore this setting if it is not applicable.\",\n\t\t\"Controls whether sticky scrolling is enabled in trees.\",\n\t\t\"Controls the number of sticky elements displayed in the tree when `#workbench.tree.enableStickyScroll#` is enabled.\",\n\t\t\"Controls how type navigation works in lists and trees in the workbench. When set to `trigger`, type navigation begins once the `list.triggerTypeNavigation` command is run.\"\n\t],\n\t\"vs/platform/markers/common/markers\": [\n\t\t\"Error\",\n\t\t\"Warning\",\n\t\t\"Info\"\n\t],\n\t\"vs/platform/quickinput/browser/commandsQuickAccess\": [\n\t\t\"recently used\",\n\t\t\"similar commands\",\n\t\t\"commonly used\",\n\t\t\"other commands\",\n\t\t\"similar commands\",\n\t\t\"{0}, {1}\",\n\t\t\"Command '{0}' resulted in an error\"\n\t],\n\t\"vs/platform/quickinput/browser/helpQuickAccess\": [\n\t\t\"{0}, {1}\"\n\t],\n\t\"vs/platform/quickinput/browser/quickInput\": [\n\t\t\"Back\",\n\t\t\"Press 'Enter' to confirm your input or 'Escape' to cancel\",\n\t\t\"{0}/{1}\",\n\t\t\"Type to narrow down results.\"\n\t],\n\t\"vs/platform/quickinput/browser/quickInputController\": [\n\t\t\"Toggle all checkboxes\",\n\t\t\"{0} Results\",\n\t\t\"{0} Selected\",\n\t\t\"OK\",\n\t\t\"Custom\",\n\t\t\"Back ({0})\",\n\t\t\"Back\"\n\t],\n\t\"vs/platform/quickinput/browser/quickInputList\": [\n\t\t\"Quick Input\"\n\t],\n\t\"vs/platform/quickinput/browser/quickInputUtils\": [\n\t\t\"Click to execute command '{0}'\"\n\t],\n\t\"vs/platform/theme/common/colorRegistry\": [\n\t\t\"Overall foreground color. This color is only used if not overridden by a component.\",\n\t\t\"Overall foreground for disabled elements. This color is only used if not overridden by a component.\",\n\t\t\"Overall foreground color for error messages. This color is only used if not overridden by a component.\",\n\t\t\"Foreground color for description text providing additional information, for example for a label.\",\n\t\t\"The default color for icons in the workbench.\",\n\t\t\"Overall border color for focused elements. This color is only used if not overridden by a component.\",\n\t\t\"An extra border around elements to separate them from others for greater contrast.\",\n\t\t\"An extra border around active elements to separate them from others for greater contrast.\",\n\t\t\"The background color of text selections in the workbench (e.g. for input fields or text areas). Note that this does not apply to selections within the editor.\",\n\t\t\"Color for text separators.\",\n\t\t\"Foreground color for links in text.\",\n\t\t\"Foreground color for links in text when clicked on and on mouse hover.\",\n\t\t\"Foreground color for preformatted text segments.\",\n\t\t\"Background color for preformatted text segments.\",\n\t\t\"Background color for block quotes in text.\",\n\t\t\"Border color for block quotes in text.\",\n\t\t\"Background color for code blocks in text.\",\n\t\t\"Shadow color of widgets such as find/replace inside the editor.\",\n\t\t\"Border color of widgets such as find/replace inside the editor.\",\n\t\t\"Input box background.\",\n\t\t\"Input box foreground.\",\n\t\t\"Input box border.\",\n\t\t\"Border color of activated options in input fields.\",\n\t\t\"Background color of activated options in input fields.\",\n\t\t\"Background hover color of options in input fields.\",\n\t\t\"Foreground color of activated options in input fields.\",\n\t\t\"Input box foreground color for placeholder text.\",\n\t\t\"Input validation background color for information severity.\",\n\t\t\"Input validation foreground color for information severity.\",\n\t\t\"Input validation border color for information severity.\",\n\t\t\"Input validation background color for warning severity.\",\n\t\t\"Input validation foreground color for warning severity.\",\n\t\t\"Input validation border color for warning severity.\",\n\t\t\"Input validation background color for error severity.\",\n\t\t\"Input validation foreground color for error severity.\",\n\t\t\"Input validation border color for error severity.\",\n\t\t\"Dropdown background.\",\n\t\t\"Dropdown list background.\",\n\t\t\"Dropdown foreground.\",\n\t\t\"Dropdown border.\",\n\t\t\"Button foreground color.\",\n\t\t\"Button separator color.\",\n\t\t\"Button background color.\",\n\t\t\"Button background color when hovering.\",\n\t\t\"Button border color.\",\n\t\t\"Secondary button foreground color.\",\n\t\t\"Secondary button background color.\",\n\t\t\"Secondary button background color when hovering.\",\n\t\t\"Badge background color. Badges are small information labels, e.g. for search results count.\",\n\t\t\"Badge foreground color. Badges are small information labels, e.g. for search results count.\",\n\t\t\"Scrollbar shadow to indicate that the view is scrolled.\",\n\t\t\"Scrollbar slider background color.\",\n\t\t\"Scrollbar slider background color when hovering.\",\n\t\t\"Scrollbar slider background color when clicked on.\",\n\t\t\"Background color of the progress bar that can show for long running operations.\",\n\t\t\"Background color of error text in the editor. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Foreground color of error squigglies in the editor.\",\n\t\t\"If set, color of double underlines for errors in the editor.\",\n\t\t\"Background color of warning text in the editor. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Foreground color of warning squigglies in the editor.\",\n\t\t\"If set, color of double underlines for warnings in the editor.\",\n\t\t\"Background color of info text in the editor. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Foreground color of info squigglies in the editor.\",\n\t\t\"If set, color of double underlines for infos in the editor.\",\n\t\t\"Foreground color of hint squigglies in the editor.\",\n\t\t\"If set, color of double underlines for hints in the editor.\",\n\t\t\"Border color of active sashes.\",\n\t\t\"Editor background color.\",\n\t\t\"Editor default foreground color.\",\n\t\t\"Sticky scroll background color for the editor\",\n\t\t\"Sticky scroll on hover background color for the editor\",\n\t\t\"Background color of editor widgets, such as find/replace.\",\n\t\t\"Foreground color of editor widgets, such as find/replace.\",\n\t\t\"Border color of editor widgets. The color is only used if the widget chooses to have a border and if the color is not overridden by a widget.\",\n\t\t\"Border color of the resize bar of editor widgets. The color is only used if the widget chooses to have a resize border and if the color is not overridden by a widget.\",\n\t\t\"Quick picker background color. The quick picker widget is the container for pickers like the command palette.\",\n\t\t\"Quick picker foreground color. The quick picker widget is the container for pickers like the command palette.\",\n\t\t\"Quick picker title background color. The quick picker widget is the container for pickers like the command palette.\",\n\t\t\"Quick picker color for grouping labels.\",\n\t\t\"Quick picker color for grouping borders.\",\n\t\t\"Keybinding label background color. The keybinding label is used to represent a keyboard shortcut.\",\n\t\t\"Keybinding label foreground color. The keybinding label is used to represent a keyboard shortcut.\",\n\t\t\"Keybinding label border color. The keybinding label is used to represent a keyboard shortcut.\",\n\t\t\"Keybinding label border bottom color. The keybinding label is used to represent a keyboard shortcut.\",\n\t\t\"Color of the editor selection.\",\n\t\t\"Color of the selected text for high contrast.\",\n\t\t\"Color of the selection in an inactive editor. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Color for regions with the same content as the selection. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Border color for regions with the same content as the selection.\",\n\t\t\"Color of the current search match.\",\n\t\t\"Color of the other search matches. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Color of the range limiting the search. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Border color of the current search match.\",\n\t\t\"Border color of the other search matches.\",\n\t\t\"Border color of the range limiting the search. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Color of the Search Editor query matches.\",\n\t\t\"Border color of the Search Editor query matches.\",\n\t\t\"Color of the text in the search viewlet's completion message.\",\n\t\t\"Highlight below the word for which a hover is shown. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Background color of the editor hover.\",\n\t\t\"Foreground color of the editor hover.\",\n\t\t\"Border color of the editor hover.\",\n\t\t\"Background color of the editor hover status bar.\",\n\t\t\"Color of active links.\",\n\t\t\"Foreground color of inline hints\",\n\t\t\"Background color of inline hints\",\n\t\t\"Foreground color of inline hints for types\",\n\t\t\"Background color of inline hints for types\",\n\t\t\"Foreground color of inline hints for parameters\",\n\t\t\"Background color of inline hints for parameters\",\n\t\t\"The color used for the lightbulb actions icon.\",\n\t\t\"The color used for the lightbulb auto fix actions icon.\",\n\t\t\"The color used for the lightbulb AI icon.\",\n\t\t\"Background color for text that got inserted. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Background color for text that got removed. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Background color for lines that got inserted. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Background color for lines that got removed. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Background color for the margin where lines got inserted.\",\n\t\t\"Background color for the margin where lines got removed.\",\n\t\t\"Diff overview ruler foreground for inserted content.\",\n\t\t\"Diff overview ruler foreground for removed content.\",\n\t\t\"Outline color for the text that got inserted.\",\n\t\t\"Outline color for text that got removed.\",\n\t\t\"Border color between the two text editors.\",\n\t\t\"Color of the diff editor's diagonal fill. The diagonal fill is used in side-by-side diff views.\",\n\t\t\"The background color of unchanged blocks in the diff editor.\",\n\t\t\"The foreground color of unchanged blocks in the diff editor.\",\n\t\t\"The background color of unchanged code in the diff editor.\",\n\t\t\"List/Tree background color for the focused item when the list/tree is active. An active list/tree has keyboard focus, an inactive does not.\",\n\t\t\"List/Tree foreground color for the focused item when the list/tree is active. An active list/tree has keyboard focus, an inactive does not.\",\n\t\t\"List/Tree outline color for the focused item when the list/tree is active. An active list/tree has keyboard focus, an inactive does not.\",\n\t\t\"List/Tree outline color for the focused item when the list/tree is active and selected. An active list/tree has keyboard focus, an inactive does not.\",\n\t\t\"List/Tree background color for the selected item when the list/tree is active. An active list/tree has keyboard focus, an inactive does not.\",\n\t\t\"List/Tree foreground color for the selected item when the list/tree is active. An active list/tree has keyboard focus, an inactive does not.\",\n\t\t\"List/Tree icon foreground color for the selected item when the list/tree is active. An active list/tree has keyboard focus, an inactive does not.\",\n\t\t\"List/Tree background color for the selected item when the list/tree is inactive. An active list/tree has keyboard focus, an inactive does not.\",\n\t\t\"List/Tree foreground color for the selected item when the list/tree is inactive. An active list/tree has keyboard focus, an inactive does not.\",\n\t\t\"List/Tree icon foreground color for the selected item when the list/tree is inactive. An active list/tree has keyboard focus, an inactive does not.\",\n\t\t\"List/Tree background color for the focused item when the list/tree is inactive. An active list/tree has keyboard focus, an inactive does not.\",\n\t\t\"List/Tree outline color for the focused item when the list/tree is inactive. An active list/tree has keyboard focus, an inactive does not.\",\n\t\t\"List/Tree background when hovering over items using the mouse.\",\n\t\t\"List/Tree foreground when hovering over items using the mouse.\",\n\t\t\"List/Tree drag and drop background when moving items around using the mouse.\",\n\t\t\"List/Tree foreground color of the match highlights when searching inside the list/tree.\",\n\t\t\"List/Tree foreground color of the match highlights on actively focused items when searching inside the list/tree.\",\n\t\t\"List/Tree foreground color for invalid items, for example an unresolved root in explorer.\",\n\t\t\"Foreground color of list items containing errors.\",\n\t\t\"Foreground color of list items containing warnings.\",\n\t\t\"Background color of the type filter widget in lists and trees.\",\n\t\t\"Outline color of the type filter widget in lists and trees.\",\n\t\t\"Outline color of the type filter widget in lists and trees, when there are no matches.\",\n\t\t\"Shadow color of the type filter widget in lists and trees.\",\n\t\t\"Background color of the filtered match.\",\n\t\t\"Border color of the filtered match.\",\n\t\t\"Tree stroke color for the indentation guides.\",\n\t\t\"Tree stroke color for the indentation guides that are not active.\",\n\t\t\"Table border color between columns.\",\n\t\t\"Background color for odd table rows.\",\n\t\t\"List/Tree foreground color for items that are deemphasized. \",\n\t\t\"Background color of checkbox widget.\",\n\t\t\"Background color of checkbox widget when the element it's in is selected.\",\n\t\t\"Foreground color of checkbox widget.\",\n\t\t\"Border color of checkbox widget.\",\n\t\t\"Border color of checkbox widget when the element it's in is selected.\",\n\t\t\"Please use quickInputList.focusBackground instead\",\n\t\t\"Quick picker foreground color for the focused item.\",\n\t\t\"Quick picker icon foreground color for the focused item.\",\n\t\t\"Quick picker background color for the focused item.\",\n\t\t\"Border color of menus.\",\n\t\t\"Foreground color of menu items.\",\n\t\t\"Background color of menu items.\",\n\t\t\"Foreground color of the selected menu item in menus.\",\n\t\t\"Background color of the selected menu item in menus.\",\n\t\t\"Border color of the selected menu item in menus.\",\n\t\t\"Color of a separator menu item in menus.\",\n\t\t\"Toolbar background when hovering over actions using the mouse\",\n\t\t\"Toolbar outline when hovering over actions using the mouse\",\n\t\t\"Toolbar background when holding the mouse over actions\",\n\t\t\"Highlight background color of a snippet tabstop.\",\n\t\t\"Highlight border color of a snippet tabstop.\",\n\t\t\"Highlight background color of the final tabstop of a snippet.\",\n\t\t\"Highlight border color of the final tabstop of a snippet.\",\n\t\t\"Color of focused breadcrumb items.\",\n\t\t\"Background color of breadcrumb items.\",\n\t\t\"Color of focused breadcrumb items.\",\n\t\t\"Color of selected breadcrumb items.\",\n\t\t\"Background color of breadcrumb item picker.\",\n\t\t\"Current header background in inline merge-conflicts. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Current content background in inline merge-conflicts. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Incoming header background in inline merge-conflicts. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Incoming content background in inline merge-conflicts. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Common ancestor header background in inline merge-conflicts. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Common ancestor content background in inline merge-conflicts. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Border color on headers and the splitter in inline merge-conflicts.\",\n\t\t\"Current overview ruler foreground for inline merge-conflicts.\",\n\t\t\"Incoming overview ruler foreground for inline merge-conflicts.\",\n\t\t\"Common ancestor overview ruler foreground for inline merge-conflicts.\",\n\t\t\"Overview ruler marker color for find matches. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Overview ruler marker color for selection highlights. The color must not be opaque so as not to hide underlying decorations.\",\n\t\t\"Minimap marker color for find matches.\",\n\t\t\"Minimap marker color for repeating editor selections.\",\n\t\t\"Minimap marker color for the editor selection.\",\n\t\t\"Minimap marker color for infos.\",\n\t\t\"Minimap marker color for warnings.\",\n\t\t\"Minimap marker color for errors.\",\n\t\t\"Minimap background color.\",\n\t\t\"Opacity of foreground elements rendered in the minimap. For example, \\\"#000000c0\\\" will render the elements with 75% opacity.\",\n\t\t\"Minimap slider background color.\",\n\t\t\"Minimap slider background color when hovering.\",\n\t\t\"Minimap slider background color when clicked on.\",\n\t\t\"The color used for the problems error icon.\",\n\t\t\"The color used for the problems warning icon.\",\n\t\t\"The color used for the problems info icon.\",\n\t\t\"The foreground color used in charts.\",\n\t\t\"The color used for horizontal lines in charts.\",\n\t\t\"The red color used in chart visualizations.\",\n\t\t\"The blue color used in chart visualizations.\",\n\t\t\"The yellow color used in chart visualizations.\",\n\t\t\"The orange color used in chart visualizations.\",\n\t\t\"The green color used in chart visualizations.\",\n\t\t\"The purple color used in chart visualizations.\"\n\t],\n\t\"vs/platform/theme/common/iconRegistry\": [\n\t\t\"The id of the font to use. If not set, the font that is defined first is used.\",\n\t\t\"The font character associated with the icon definition.\",\n\t\t\"Icon for the close action in widgets.\",\n\t\t\"Icon for goto previous editor location.\",\n\t\t\"Icon for goto next editor location.\"\n\t],\n\t\"vs/platform/undoRedo/common/undoRedoService\": [\n\t\t\"The following files have been closed and modified on disk: {0}.\",\n\t\t\"The following files have been modified in an incompatible way: {0}.\",\n\t\t\"Could not undo '{0}' across all files. {1}\",\n\t\t\"Could not undo '{0}' across all files. {1}\",\n\t\t\"Could not undo '{0}' across all files because changes were made to {1}\",\n\t\t\"Could not undo '{0}' across all files because there is already an undo or redo operation running on {1}\",\n\t\t\"Could not undo '{0}' across all files because an undo or redo operation occurred in the meantime\",\n\t\t\"Would you like to undo '{0}' across all files?\",\n\t\t\"&&Undo in {0} Files\",\n\t\t\"Undo this &&File\",\n\t\t\"Could not undo '{0}' because there is already an undo or redo operation running.\",\n\t\t\"Would you like to undo '{0}'?\",\n\t\t\"&&Yes\",\n\t\t\"No\",\n\t\t\"Could not redo '{0}' across all files. {1}\",\n\t\t\"Could not redo '{0}' across all files. {1}\",\n\t\t\"Could not redo '{0}' across all files because changes were made to {1}\",\n\t\t\"Could not redo '{0}' across all files because there is already an undo or redo operation running on {1}\",\n\t\t\"Could not redo '{0}' across all files because an undo or redo operation occurred in the meantime\",\n\t\t\"Could not redo '{0}' because there is already an undo or redo operation running.\"\n\t],\n\t\"vs/platform/workspace/common/workspace\": [\n\t\t\"Code Workspace\"\n\t]\n});"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DAUA,OAAO,4BAA6B,CACnC,+CAAgD,CAC/C,WACD,EACA,yCAA0C,CACzC,OACD,EACA,gDAAiD,CAChD,aACA,mBACA,wBACD,EACA,4CAA6C,CAC5C,QACA,eACD,EACA,uCAAwC,CACvC,gDACA,6HACD,EACA,8CAA+C,CAC9C,YACD,EACA,uCAAwC,CACvC,aACA,eACA,YACA,sBACA,qBACA,eACD,EACA,qDAAsD,CACrD,SACD,EACA,+CAAgD,CAC/C,YACD,EACA,qCAAsC,CACrC,iBACD,EACA,uCAAwC,CACvC,SACA,cACA,iBACA,iBACA,iBACA,QACA,oBACD,EACA,yBAA0B,CACzB,SACD,EACA,8BAA+B,CAC9B,WACA,gCACA,sEACA,sEACA,4BACA,qEACD,EACA,kCAAmC,CAClC,OACA,QACA,MACA,UACA,OACA,QACA,MACA,QACA,UACA,QACA,SACA,UACA,UACA,QACA,MACA,UACA,UACA,QACA,MACA,OACD,EACA,0BAA2B,CAC1B,GACD,EACA,+CAAgD,CAC/C,SACA,6CACA,sDACA,yLACA,iJACD,EACA,iCAAkC,CACjC,mDACA,mDACA,2BACD,EACA,qCAAsC,CACrC,SACA,OACA,SACA,OACA,eACA,YACD,EACA,4CAA6C,CAC5C,iOACA,6BACD,EACA,2DAA4D,CAC3D,+CACA,+CACA,8CACA,QACA,6DACA,mBACA,iBACA,oBACA,wEACA,QACA,yBACA,0CACA,0BACA,yBACD,EACA,6CAA8C,CAC7C,+DACA,sEACA,0DACD,EACA,kDAAmD,CAClD,kDACA,kDACD,EACA,8DAA+D,CAC9D,oCACA,gCACA,+CACA,wCACA,yBACA,cACA,cACA,oBACA,iCACA,6BACA,yBACA,wBACA,8BACA,2BACD,EACA,4DAA6D,CAC5D,0BACA,eACD,EACA,wDAAyD,CACxD,0CACD,EACA,kEAAmE,CAClE,wBACA,mCACA,wBACA,mCACA,mBACA,wBACD,EACA,kEAAmE,CAClE,qBACA,oBACA,qBACA,oBACA,0BACA,0BACA,oBACD,EACA,uDAAwD,CACvD,0CACA,4CACA,6BACA,8BACD,EACA,wDAAyD,CACxD,kDACD,EACA,oDAAqD,CACpD,SACA,gHACA,oMACA,2GACA,gHACA,4CACA,iFACA,mCACA,+CACA,8DACA,yCACA,6HACA,sDACA,uDACA,mGACA,wFACA,2FACA,wEACA,kFACA,4EACA,mJACA,yEACA,oDACA,oDACA,+GACA,oDACA,oDACA,2FACA,0EACA,0EACA,gFACA,yEACA,oFACA,mFACA,mFACA,8CACA,yBACA,yCACA,gDACA,qCACA,uCACA,4DACA,0DACA,uEACA,gFACA,oEACA,2GACD,EACA,wCAAyC,CACxC,gEACA,2CACA,0CACA,oFACA,kEACA,kGACA,wEACA,wEACA,sDACA,0FACA,qDACA,6FACA,2DACA,kDACA,uFACA,yEACA,6FACA,iKACA,2HACA,mKACA,6HACA,yKACA,gLACA,0JACA,6MACA,oCACA,+EACA,gGACA,0CACA,gDACA,qEACA,kLACA,6FACA,kGACA,8FACA,kGACA,6FACA,+GACA,oHACA,gHACA,mHACA,8GACA,uCACA,qEACA,gFACA,oHACA,0DACA,oMACA,iKACA,6IACA,mDACA,4BACA,qFACA,4FACA,4FACA,8EACA,sDACA,6OACA,4EACA,yCACA,0BACA,+DACA,8DACA,2BACA,+JACA,yFACA,4DACA;AAAA;AAAA;AAAA,wEACA,yCACA,wDACA,2EACA,mGACA,0FACA,oCACA,iDACA,6CACA,oDACA,qEACA,gFACA,sFACA,wFACA,wFACA,gGACA,mDACA,uCACA,iCACA,2CACA,4CACA,4DACA,6UACA,iCACA,gDACA,qEACA,4CACA,wCACA,yEACA,8BACA,yJACA,8DACA,iDACA,gDACA,qDACA,gEACA,mDACA,kDACA,uDACA,uCACA,0CACA,oEACA,yFACA,6KACA,+FACA,yJACA,0FACA,yFACA,6DACA,mFACA,2EACA,6EACA,8EACA,4CACA,uDACA,qKACA,kHACA,yEACA,+BACA,gEACA,gCACA,2DACA,yEACA,8DACA,2CACA,sEACA,wEACA,2DACA,sCACA,6EACA,4CACA,wEACA,kEACA,4DACA,oIACA,+EACA,yEACA,4IACA,yEACA,wEACA,kFACA,qEACA,sSACA,iEACA,yDACA,iFACA,oEACA,6FACA,qEACA,wIACA,wDACA,0DACA,6DACA,4DACA,oQACA,uDACA,0DACA,uDACA,wDACA,2DACA,wDACA,0DACA,uDACA,0DACA,sDACA,uDACA,0DACA,sDACA,4DACA,yDACA,sDACA,uDACA,sDACA,2DACA,6DACA,wDACA,+DACA,yDACA,sDACA,wDACA,qEACA,6EACA,mDACA,wDACA,sDACA,sDACA,6CACA,2IACA,2HACA,yEACA,yFACA,4DACA,4HACA,0EACA,6FACA,uMACA,wEACA,mKACA,0PACA,iBACA,uEACA,uEACA,wEACA,0GACA,uEACA,wEACA,0GACA,uFACA,+FACA,iFACA,2EACA,qEACA,sEACA,sGACA,wDACA,uDACA,2FACA,6IACA,2LACA,wHACA,sFACA,yCACA,yCACA,uGACA,qHACA,8CACA,yCACA,oGACA,0FACA,8EACA,+DACA,+DACA,8EACA,yFACA,+EACA,4EACA,sCACA,sCACA,kGACA,4CACA,iEACA,6BACA,wLACA,oFACA,+CACA,+DACA,iFACA,gFACA,wCACA,mDACA,mCACA,2EACA,kDACA,wDACA,yFACA,8CACA,sDACA,8DACA,qEACA,kLACA,2FACA,4BACA,8KACA,iFACA,kHACA,sEACA,yCACA,mJACA,2EACA,+BACA,qFACA,yEACA,oDACA,oEACA,+DACA,2QACA,gDACA,oCACA,oFACA,8EACA,kCACA,mDACA,oEACA,wEACA,uEACA,mCACA,qCACA,8EACA,oFACA,iFACA,oDACA,kDACA,gEACA,6DACA,mDACA,oEACA,mGACA,uEACA,sDACA,8CACA,+DACA,2DACA,4FACA,gEACA,8KACA,oEACA,iFACA,oCACA,8DACA,oEACA,8DACA,sCACA,+CACA,wDACA,oDACA,oDACA,mCACA,sFACA,8DACA,wHACA,+EACA,yGACA,4FACA,sCACA,0IACA,gIACA,2EACA,2EACA,2BACA,oGACA,2BACA,sDACA,wCACA,iDACA,6DACA,uDACA,mCACA,0HACA,6EACA,qGACA,yBACA,yCACA,gDACA,4EACA,kCACA,wGACA,sGACA,2FACD,EACA,4CAA6C,CAC5C,qEACA,0EACA,uJACA,4DACA,uKACA,6DACA,8BACA,uHACA,gDACA,gCACA,0CACA,6FACA,iDACA,yGACA,8CACA,8CACA,8CACA,8CACA,8CACA,8CACA,qDACA,qDACA,qDACA,qDACA,qDACA,qDACA,qCACA,qEACA,qCACA,kFACA,8BACA,sCACA,4CACA,oCACA,sCACA,iDACA,qGACA,kEACA,mQACA,4CACA,oDACA,oDACA,2HACA,0CACA,4CACA,yCACA,iFACA,iFACA,iFACA,iFACA,iFACA,iFACA,2CACA,+FACA,+FACA,+FACA,+FACA,+FACA,+FACA,6FACA,6FACA,6FACA,6FACA,6FACA,6FACA,qDACA,wDACD,EACA,qCAAsC,CACrC,yDACA,sFACA,wEACA,kCACA,uCACA,iDACA,6CACA,uDACA,sCACA,wDACA,gDACA,2EACA,8CACA,uCACA,6CACA,kDACA,sCACA,sCACA,uCACA,uCACA,iDACA,iDACA,iEACA,wCACA,oDACA,iDACA,8CACA,+CACA,gDACA,oDACA,oDACA,0CACA,uDACA,oDACA,8CACA,2CACA,mDACA,kDACA,wDACA,kEACA,gEACA,yEACD,EACA,6BAA8B,CAC7B,QACA,UACA,QACA,WACA,cACA,cACA,qBACA,QACA,QACA,OACA,WACA,YACA,MACA,SACA,SACA,YACA,OACA,SACA,SACA,WACA,UACA,WACA,SACA,SACA,iBACA,WACA,WACD,EACA,2CAA4C,CAC3C,YACD,EACA,mCAAoC,CACnC,QACD,EACA,qCAAsC,CACrC,4BACA,uBACA,kCACA,kBACA,wBACA,kBACA,8BACA,iBACA,0CACA,6BACA,6BACD,EACA,+CAAgD,CAC/C,kBACA,WACD,EACA,sDAAuD,CACtD,mBACA,wBACA,uBACA,yBACA,+BACA,yBACD,EACA,4DAA6D,CAC5D,qDACA,gBACA,oBACA,kBACA,kBACA,mEACD,EACA,4DAA6D,CAC5D,0BACA,0BACD,EACA,sDAAuD,CACtD,mBACD,EACA,gDAAiD,CAChD,QACA,MACA,MACA,MACA,SACA,OACA,OACA,OACA,UACA,UACA,QACA,QACA,QACA,UACA,QACA,QACA,QACA,+BACD,EACA,kDAAmD,CAClD,0DACD,EACA,0DAA2D,CAC1D,kCACA,kDACA,+CACA,8DACA,0CACA,8DACA,eACA,4BACA,gDACA,sCACA,sCACA,4BACA,cACA,gDACA,sCACA,sCACA,4BACA,mBACA,kDACA,wCACA,wCACA,8BACA,mBACA,uCACA,UACA,8BACA,cACA,yBACD,EACA,+DAAgE,CAC/D,gEACA,4FACD,EACA,4DAA6D,CAC5D,2CACA,gBACA,eACD,EACA,sDAAuD,CACtD,kBACA,YACA,UACA,SACA,UACA,OACA,gBACA,eACD,EACA,uDAAwD,CACvD,yDACA,0BACA,oBACA,0BACA,oBACA,mBACD,EACA,wDAAyD,CACxD,0CACA,kBACD,EACA,0DAA2D,CAC1D,8CACA,gCACD,EACA,qEAAsE,CACrE,wCACA,0CACA,wBACA,2CACD,EACA,4CAA6C,CAC5C,sBACA,wBACA,mBACA,sBACA,uBACA,wBACD,EACA,oDAAqD,CACpD,UACA,oBACA,gBACA,eACA,OACA,MACA,SACA,aACA,SACA,0BACD,EACA,kDAAmD,CAClD,cACA,aACD,EACA,kEAAmE,CAClE,cACA,2FACD,EACA,gEAAiE,CAChE,sCACA,wBACA,0CACA,sBACA,wBACD,EACA,6DAA8D,CAC7D,WACA,oBACA,cACA,aACA,eACA,cACA,wBACA,sBACD,EACA,uEAAwE,CACvE,+EACD,EACA,qEAAsE,CACrE,qCACA,uBACA,wCACD,EACA,+DAAgE,CAC/D,8EACD,EACA,gDAAiD,CAChD,4DACA,OACA,SACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UACA,sBACA,sBACA,YACA,gBACA,iBACA,gDACA,8DACA,yCACA,yCACA,sBACA,0BACA,UACA,WACD,EACA,4CAA6C,CAC5C,0DACA,6DACA,4DACA,gDACA,oDACA,sDACA,kDACA,iBACA,OACA,OACA,iBACA,aACA,oBACA,QACA,UACA,UACA,UACA,cACA,iBACA,+FACA,aACA,aACA,YACA,sBACA,8BACA,sBACA,mJACD,EACA,4CAA6C,CAC5C,SACA,qBACA,OACA,cACA,mBACA,0BACA,mBACA,qBACA,2BACA,6BACA,WACA,aACA,oBACA,+BACA,2BACA,sCACA,+BACA,gBACD,EACA,uDAAwD,CACvD,gHACA,qDACA,uDACA,wDACA,iEACA,+DACD,EACA,8CAA+C,CAC9C,sBACA,uBACA,wBACD,EACA,iDAAkD,CACjD,kBACA,kBACD,EACA,gDAAiD,CAChD,4CACA,6BACA,gDACA,iCACA,qDACA,iBACA,yDACA,oBACD,EACA,sDAAuD,CACtD,QACA,UACA,OACA,OACA,eACA,sBACA,qBACA,+CACA,4DACA,iDACA,8DACA,8CACA,2DACA,6CACD,EACA,oDAAqD,CACpD,OACA,cACA,gCACA,sBACA,mBACA,qBACA,8BACA,kBACA,eACA,iCACA,uBACA,oBACA,sBACA,iCACA,uBACA,mBACA,mBACA,qCACA,2BACA,wBACA,0BACA,uBACA,kBACA,oCACA,0BACA,wBACA,0BACA,uBACA,gCACA,sBACA,mBACA,qBACA,aACA,kBACA,aACA,mBACA,YACA,uBACA,YACD,EACA,qEAAsE,CACrE,gCACD,EACA,iEAAkE,CACjE,iFACA,aACA,WACD,EACA,2DAA4D,CAC3D,iBACA,gBACA,YACD,EACA,6DAA8D,CAC7D,uBACA,aACA,YACD,EACA,uDAAwD,CACvD,mCACA,uCACA,iCACA,oCACA,mBACA,wBACA,2BACA,gCACD,EACA,wDAAyD,CACxD,8EACA,kCACA,mBACD,EACA,wCAAyC,CACxC,sBACA,+CACA,2DACA,2DACA,gCACA,kBACA,oBACA,oBACA,qBACA,gBACA,kBACA,kBACA,oBACD,EACA,2DAA4D,CAC3D,aACA,sHACA,gIACD,EACA,yDAA0D,CACzD,eACA,2BACA,8BACA,2BACA,cACD,EACA,0DAA2D,CAC1D,8BACA,yBACD,EACA,oDAAqD,CACpD,gCACA,8BACA,sBACA,mBACA,mBACA,mCACA,oBACA,sBACA,0BACA,kCACA,iBACA,yBACD,EACA,uDAAwD,CACvD,yBACA,cACA,eACA,iBACA,cACA,+CACA,yBACA,iBACD,EACA,uDAAwD,CACvD,8BACA,kCACA,4BACA,wCACA,cACA,wCACA,cACA,2BACA,SACA,yBACA,qBACD,EACA,+DAAgE,CAC/D,aACD,EACA,0EAA2E,CAC1E,0CACA,uDACA,uGACA,qEACD,EACA,0EAA2E,CAC1E,2CACD,EACA,2EAA4E,CAC3E,qCACA,yCACA,YACA,WACA,MACD,EACA,wDAAyD,CACxD,uBACD,EACA,4DAA6D,CAC5D,eACA,iBACA,iBACA,mBACA,sBACA,wBACA,eACA,iBACA,iBACA,mBACA,uBACA,wBACA,yBACA,2BACA,cACA,cACA,eACA,oBACA,oBACA,kBACA,mBACA,aACA,yCACA,yBACA,yBACA,0BACA,0BACA,0BACA,yBACD,EACA,wDAAyD,CACxD,uBACA,wDACD,EACA,wCAAyC,CACxC,8DACA,0DACA,kBACA,cACA,cACA,eACA,iBACA,cACA,sBACA,WACD,EACA,sDAAuD,CACtD,2DACD,EACA,oDAAqD,CACpD,oBACA,qBACA,mBACA,qBACA,mBACA,qBACA,2BACA,6BACA,wBACA,qBACA,mCACA,wBACA,uCACA,4BACA,yCACA,6CACA,uCACA,2BACA,yBACA,oBACA,0BACA,wBACA,6BACD,EACA,0DAA2D,CAC1D,yBACD,EACA,gEAAiE,CAChE,qCACA,yCACA,YACA,4DACD,EACA,8CAA+C,CAC9C,0DACA,QACA,gDACA,gCACA,qCACA,4CACA,iDACA,gEACA,gEACA,uEACA,uEACA,4CACA,0DACA,6DACA,sDACA,iDACA,iDACD,EACA,4DAA6D,CAC5D,4CACA,oCACA,kBACA,0FACA,uEACD,EACA,8DAA+D,CAC9D,uEACA,8DACA,6BACA,oBACA,mBACA,qBACA,gBACA,mBACA,gBACA,kBACA,qBACA,kBACA,gBACA,gBACA,eACA,kBACA,mBACA,mBACA,iBACA,wBACA,gBACA,mBACA,qBACA,4BACA,gBACA,cACA,eACA,gBACA,iBACA,gBACA,aACA,eACA,iBACD,EACA,yDAA0D,CACzD,iCACA,iCACD,EACA,0CAA2C,CAC1C,aACA,4DACA,0BACA,sBACA,oDACA,+BACA,iCACA,gBACA,+DACD,EACA,oDAAqD,CACpD,6CACA,yDACA,+BACD,EACA,oDAAqD,CACpD,mBACA,qBACA,mBACA,oBACD,EACA,uDAAwD,CACvD,gDACA,wDACA,4DACA,2BACD,EACA,qDAAsD,CACrD,SACA,SACA,UACA,YACA,WACA,SACA,WACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,UACA,WACA,QACA,QACA,MACA,OACA,OACA,SACA,YACA,UACA,WACA,WACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,KACD,EACA,6DAA8D,CAC7D,uBACA,yBACA,gBACA,kBACA,sBACA,wBACA,iCACA,qCACA,mCACA,eACD,EACA,4CAA6C,CAC5C,oCACA,yCACA,sDACA,mGACA,uDACA,kEACA,wDACA,oEACD,EACA,sDAAuD,CACtD,4CACA,kBACA,SACA,SACA,UACA,UACA,SACA,YACA,YACA,2BACD,EACA,kDAAmD,CAClD,0CACA,sCACA,0CACA,gEACA,qEACA,gEACA,uDACA,+EACA,iDACA,aACA,kBACA,UACA,eACA,UACA,WACA,gBACD,EACA,yDAA0D,CACzD,QACA,YACD,EACA,0DAA2D,CAC1D,mDACA,WACD,EACA,wDAAyD,CACxD,WACD,EACA,oDAAqD,CACpD,+GACA,iHACA,+GACA,+GACA,kHACA,qHACA,oHACA,2HACA,+GACA,+GACA,8GACA,gHACA,kHACA,mHACA,6GACA,iHACA,gHACA,gHACA,mHACA,8GACA,gHACA,gHACA,kHACA,iHACA,kHACA,mHACA,iHACA,gHACA,gHACA,8GACA,wHACA,8GACA,iHACD,EACA,kEAAmE,CAClE,6BACA,iEACA,gDACD,EACA,sDAAuD,CACtD,6BACD,EACA,kEAAmE,CAClE,8DACA,iEACA,2DACA,2DACA,yGACA,mGACA,kCACA,oDACA,kBACA,gCACA,iDACA,+BACA,gDACA,8BACA,+CACA,8BACA,+CACA,8BACA,qDACA,uBACA,2DACA,qCACA,uEACA,qCACD,EACA,0EAA2E,CAC1E,2BACA,oCACA,+OACA,oCACA,QACD,EACA,iEAAkE,CACjE,mJACA,uJACA,gIACA,wEACA,4EACA,qDACA,4HACA,yIACA,0IACD,EACA,4DAA6D,CAC5D,8BACA,kCACA,0BACD,EACA,0DAA2D,CAC1D,aACD,EACA,mDAAoD,CACnD,OACA,OACA,OACA,OACA,cACA,WACD,EACA,8CAA+C,CAC9C,+BACA,eACA,4BACA,eACD,EACA,gDAAiD,CAChD,2DACA,4CACA,qBACA,yBACA,qBACA,yBACA,yBACD,EACA,sDAAuD,CACtD,YACA,YACA;AAAA,UACD,EACA,sCAAuC,CACtC,OACA,YACD,EACA,yCAA0C,CACzC,YACD,EACA,gDAAiD,CAChD,gBACA,kBACA,sBACA,qBACA,4BACA,qBACA,iCACA,yBACA,iBACA,cACA,0BACA,gBACA,0BACA,uBACA,qBACA,oBACA,qBACA,oBACA,yBACA,wBACA,QACA,OACA,QACD,EACA,yDAA0D,CACzD,2CACA,4DACA,6DACA,4DACA,6DACA,4DACA,oCACA,mKACA,8DACA,kFACD,EACA,mDAAoD,CACnD,uDACD,EACA,2CAA4C,CAC3C,+BACA,+HACA,oBACA,0BACA,mBACA,mDACA,+BACA,uCACA;AAAA,iBACD,EACA,4CAA6C,CAC5C,wCACA,wCACA,0CACA,wCACA,kEACA,sCACA,+CACA,0BACA,+CACD,EACA,wCAAyC,CACxC,oBACA,2BACA,gCACA,6CACA,6GACD,EACA,yDAA0D,CACzD,gCACD,EACA,0DAA2D,CAC1D,wDACA,sDACA,mDACA,kDACD,EACA,uCAAwC,CACvC,YACA,oEACA,+DACA,sRACA,4KACA,kJACA,gEACA,uCACA,yDACA,0DACA,qFACA,kDACA,iHACA,kCACA,uEACA,iHACA,gKACA,8GACA,oHACA,+FACA,qCACA,0CACA,sFACA,qKACA,yDACA,sHACA,6KACD,EACA,qCAAsC,CACrC,QACA,UACA,MACD,EACA,qDAAsD,CACrD,gBACA,mBACA,gBACA,iBACA,mBACA,WACA,oCACD,EACA,iDAAkD,CACjD,UACD,EACA,4CAA6C,CAC5C,OACA,4DACA,UACA,8BACD,EACA,sDAAuD,CACtD,wBACA,cACA,eACA,KACA,SACA,aACA,MACD,EACA,gDAAiD,CAChD,aACD,EACA,iDAAkD,CACjD,gCACD,EACA,yCAA0C,CACzC,sFACA,sGACA,yGACA,mGACA,gDACA,uGACA,qFACA,4FACA,iKACA,6BACA,sCACA,yEACA,mDACA,mDACA,6CACA,yCACA,4CACA,kEACA,kEACA,wBACA,wBACA,oBACA,qDACA,yDACA,qDACA,yDACA,mDACA,8DACA,8DACA,0DACA,0DACA,0DACA,sDACA,wDACA,wDACA,oDACA,uBACA,4BACA,uBACA,mBACA,2BACA,0BACA,2BACA,yCACA,uBACA,qCACA,qCACA,mDACA,8FACA,8FACA,0DACA,qCACA,mDACA,qDACA,kFACA,uHACA,sDACA,+DACA,yHACA,wDACA,iEACA,sHACA,qDACA,8DACA,qDACA,8DACA,iCACA,2BACA,mCACA,gDACA,yDACA,4DACA,4DACA,gJACA,yKACA,gHACA,gHACA,sHACA,0CACA,2CACA,oGACA,oGACA,gGACA,uGACA,iCACA,gDACA,uHACA,mIACA,mEACA,qCACA,4GACA,iHACA,4CACA,4CACA,wHACA,4CACA,mDACA,gEACA,8HACA,wCACA,wCACA,oCACA,mDACA,yBACA,mCACA,mCACA,6CACA,6CACA,kDACA,kDACA,iDACA,0DACA,4CACA,sHACA,qHACA,uHACA,sHACA,4DACA,2DACA,uDACA,sDACA,gDACA,2CACA,6CACA,kGACA,+DACA,+DACA,6DACA,8IACA,8IACA,2IACA,wJACA,+IACA,+IACA,oJACA,iJACA,iJACA,sJACA,gJACA,6IACA,iEACA,iEACA,+EACA,0FACA,oHACA,4FACA,oDACA,sDACA,iEACA,8DACA,yFACA,6DACA,0CACA,sCACA,gDACA,oEACA,sCACA,uCACA,+DACA,uCACA,4EACA,uCACA,mCACA,wEACA,oDACA,sDACA,2DACA,sDACA,yBACA,kCACA,kCACA,uDACA,uDACA,mDACA,2CACA,gEACA,6DACA,yDACA,mDACA,+CACA,gEACA,4DACA,qCACA,wCACA,qCACA,sCACA,8CACA,8HACA,+HACA,+HACA,gIACA,sIACA,uIACA,sEACA,gEACA,iEACA,wEACA,uHACA,+HACA,yCACA,wDACA,iDACA,kCACA,qCACA,mCACA,4BACA,8HACA,mCACA,iDACA,mDACA,8CACA,gDACA,6CACA,uCACA,iDACA,8CACA,+CACA,iDACA,iDACA,gDACA,gDACD,EACA,wCAAyC,CACxC,iFACA,0DACA,wCACA,0CACA,qCACD,EACA,8CAA+C,CAC9C,kEACA,sEACA,6CACA,6CACA,yEACA,0GACA,mGACA,iDACA,sBACA,mBACA,mFACA,gCACA,QACA,KACA,6CACA,6CACA,yEACA,0GACA,mGACA,kFACD,EACA,yCAA0C,CACzC,gBACD,CACD,CAAC", "names": [], "file": "editor.main.nls.js"}
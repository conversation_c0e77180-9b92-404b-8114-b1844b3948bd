/* 轮播图管理专用样式 - 深色科技风 */
.banners-container {
    max-width: 100%;
    margin: 0;
    padding: 0;
}



/* 页面标题 */
.page-header {
    background: rgba(20, 20, 30, 0.6);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 15px;
    padding: 25px 30px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.8), rgba(255, 119, 198, 0.6));
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.3);
}

.page-title {
    font-family: 'Orbitron', monospace;
    font-size: 28px;
    font-weight: 700;
    color: #fff;
    margin: 0 0 8px 0;
    text-shadow: 0 0 20px rgba(120, 119, 198, 0.6);
}

.page-subtitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    margin: 0;
    font-weight: 400;
}

.banners-container {
background: rgba(15, 15, 15, 0.95);
border: 1px solid rgba(120, 119, 198, 0.3);
border-radius: 20px;
backdrop-filter: blur(25px);
box-shadow:
0 20px 60px rgba(0, 0, 0, 0.4),
0 0 40px rgba(120, 119, 198, 0.1);
overflow: hidden;
position: relative;
margin-bottom: 30px;
}

.banners-container::before {
content: '';
position: absolute;
top: 0;
left: 0;
right: 0;
height: 2px;
background: linear-gradient(90deg,
rgba(120, 119, 198, 0.8),
rgba(255, 119, 198, 0.8),
rgba(120, 219, 255, 0.8));
animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
0%, 100% { opacity: 0.5; }
50% { opacity: 1; }
}

/* 列表头部 */
.list-header {
background: linear-gradient(135deg,
rgba(120, 119, 198, 0.15) 0%,
rgba(255, 119, 198, 0.1) 50%,
rgba(120, 219, 255, 0.15) 100%);
padding: 30px;
border-bottom: 1px solid rgba(120, 119, 198, 0.2);
}

.list-header-content {
display: flex;
justify-content: space-between;
align-items: center;
}

.list-title-section {
display: flex;
align-items: center;
gap: 20px;
}

.list-icon {
width: 60px;
height: 60px;
background: linear-gradient(135deg,
rgba(120, 119, 198, 0.3),
rgba(255, 119, 198, 0.3));
border-radius: 16px;
display: flex;
align-items: center;
justify-content: center;
font-size: 24px;
color: #fff;
text-shadow: 0 0 20px rgba(120, 119, 198, 0.8);
box-shadow: 0 8px 25px rgba(120, 119, 198, 0.3);
}

.list-title {
font-family: 'Orbitron', monospace;
font-size: 28px;
font-weight: 700;
color: #fff;
margin: 0;
text-shadow: 0 0 20px rgba(120, 119, 198, 0.6);
}

.list-subtitle {
font-size: 16px;
color: rgba(255, 255, 255, 0.7);
margin: 5px 0 0 0;
font-weight: 400;
line-height: 1.5;
}

.limit-warning {
color: #ff6b6b;
font-weight: 600;
text-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
animation: pulse-warning 2s ease-in-out infinite;
}

.limit-info {
color: rgba(120, 219, 255, 0.9);
font-weight: 500;
text-shadow: 0 0 8px rgba(120, 219, 255, 0.4);
}

@keyframes pulse-warning {
0%, 100% { opacity: 0.8; }
50% { opacity: 1; }
}

.btn-add-custom {
background: linear-gradient(135deg,
rgba(120, 119, 198, 0.8) 0%,
rgba(255, 119, 198, 0.8) 100%);
border: 1px solid rgba(120, 119, 198, 0.5);
color: #fff;
padding: 12px 24px;
border-radius: 12px;
text-decoration: none;
font-weight: 600;
display: flex;
align-items: center;
gap: 8px;
transition: all 0.3s ease;
box-shadow: 0 8px 25px rgba(120, 119, 198, 0.3);
}

.btn-add-custom:hover {
background: linear-gradient(135deg,
rgba(120, 119, 198, 1) 0%,
rgba(255, 119, 198, 1) 100%);
transform: translateY(-3px);
box-shadow: 0 12px 35px rgba(120, 119, 198, 0.4);
color: #fff;
}

/* 列表主体 */
.list-body {
padding: 30px;
}

/* 轮播图列表 */
.banners-list {
display: flex;
flex-direction: column;
gap: 20px;
}

.banner-item {
background: rgba(20, 20, 30, 0.8);
border: 1px solid rgba(120, 119, 198, 0.2);
border-radius: 16px;
padding: 25px;
transition: all 0.3s ease;
position: relative;
overflow: hidden;
}

.banner-item::before {
content: '';
position: absolute;
top: 0;
left: -100%;
width: 100%;
height: 100%;
background: linear-gradient(90deg,
transparent,
rgba(120, 119, 198, 0.1),
transparent);
transition: left 0.8s ease;
}

.banner-item:hover::before {
left: 100%;
}

.banner-item:hover {
border-color: rgba(120, 119, 198, 0.5);
box-shadow: 0 15px 40px rgba(120, 119, 198, 0.2);
transform: translateY(-3px);
}

/* 轮播图缩略图 */
.banner-thumbnail {
width: 120px;
height: 80px;
border-radius: 12px;
overflow: hidden;
flex-shrink: 0;
background: rgba(30, 30, 40, 0.8);
border: 1px solid rgba(120, 119, 198, 0.2);
display: flex;
align-items: center;
justify-content: center;
}

.banner-thumb-img {
width: 100%;
height: 100%;
object-fit: cover;
transition: transform 0.3s ease;
}

.banner-item:hover .banner-thumb-img {
transform: scale(1.05);
}

.banner-thumb-placeholder {
color: rgba(120, 119, 198, 0.5);
font-size: 24px;
text-shadow: 0 0 10px rgba(120, 119, 198, 0.3);
}

.banner-content {
display: flex;
align-items: center;
justify-content: space-between;
gap: 25px;
position: relative;
z-index: 2;
}

.banner-info {
flex: 1;
min-width: 0;
}

.banner-title {
font-size: 20px;
font-weight: 700;
color: #fff;
margin: 0 0 8px 0;
text-shadow: 0 0 10px rgba(120, 119, 198, 0.5);
white-space: nowrap;
overflow: hidden;
text-overflow: ellipsis;
}

.banner-subtitle {
font-size: 14px;
color: rgba(255, 255, 255, 0.6);
margin: 0 0 5px 0;
white-space: nowrap;
overflow: hidden;
text-overflow: ellipsis;
}

.banner-description {
font-size: 12px;
color: rgba(255, 255, 255, 0.5);
margin: 0;
line-height: 1.4;
display: -webkit-box;
-webkit-line-clamp: 2;
-webkit-box-orient: vertical;
overflow: hidden;
}

.banner-meta {
display: flex;
align-items: center;
gap: 15px;
flex-shrink: 0;
}

/* 状态开关样式 */
.status-switch-wrapper {
    display: flex;
    align-items: center;
}

.status-switch {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    user-select: none;
    transition: all 0.3s ease;
    padding: 6px 10px;
    border-radius: 12px;
    background: rgba(20, 20, 30, 0.6);
    border: 1px solid rgba(120, 119, 198, 0.2);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.status-switch:hover {
    background: rgba(120, 119, 198, 0.1);
    border-color: rgba(120, 119, 198, 0.4);
    transform: scale(1.02);
}

.status-checkbox {
    display: none;
}

.status-slider {
    position: relative;
    width: 36px;
    height: 18px;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    border-radius: 18px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.status-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 14px;
    height: 14px;
    background: #fff;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.status-checkbox:checked + .status-slider {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.status-checkbox:checked + .status-slider::before {
    transform: translateX(18px);
}

.status-text {
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: rgba(255, 255, 255, 0.8);
    transition: color 0.3s ease;
    min-width: 32px;
}

.status-checkbox:checked ~ .status-text {
    color: #2ecc71;
    text-shadow: 0 0 10px rgba(39, 174, 96, 0.6);
}

.status-checkbox:not(:checked) ~ .status-text {
    color: #e74c3c;
    text-shadow: 0 0 10px rgba(231, 76, 60, 0.6);
}

.status-switch:hover .status-text {
    color: #fff;
}

/* 禁用状态 */
.status-checkbox:disabled + .status-slider {
    opacity: 0.6;
    cursor: not-allowed;
}

.status-checkbox:disabled ~ .status-text {
    opacity: 0.6;
}

.sort-badge {
background: linear-gradient(135deg,
rgba(120, 119, 198, 0.8),
rgba(255, 119, 198, 0.8));
color: white;
padding: 8px 12px;
border-radius: 12px;
font-size: 12px;
font-weight: 700;
min-width: 35px;
text-align: center;
box-shadow: 0 4px 15px rgba(120, 119, 198, 0.3);
text-shadow: 0 0 10px rgba(120, 119, 198, 0.8);
}

.banner-actions {
display: flex;
gap: 12px;
flex-shrink: 0;
}

/* 幻灯片操作按钮样式 - 确保与admin.css统一 */
.action-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    border: none;
    border-radius: 10px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    min-width: 85px;
    justify-content: center;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent);
    transition: left 0.5s ease;
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn i {
    transition: transform 0.3s ease;
    filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.3));
}

.action-btn:hover i {
    transform: scale(1.1);
}

/* 编辑按钮 - 统一蓝色渐变 */
.action-btn.btn-edit, .btn-edit {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.8) 0%,
        rgba(37, 99, 235, 0.8) 50%,
        rgba(29, 78, 216, 0.8) 100%) !important;
    border-color: rgba(59, 130, 246, 0.6) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.action-btn.btn-edit:hover, .btn-edit:hover {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 1) 0%,
        rgba(37, 99, 235, 1) 50%,
        rgba(29, 78, 216, 1) 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    color: #ffffff !important;
    text-decoration: none;
}

/* 删除按钮 - 统一红色渐变 */
.action-btn.btn-delete, .btn-delete {
    background: linear-gradient(135deg,
        rgba(239, 68, 68, 0.8) 0%,
        rgba(220, 38, 38, 0.8) 50%,
        rgba(185, 28, 28, 0.8) 100%) !important;
    border-color: rgba(239, 68, 68, 0.6) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.action-btn.btn-delete:hover, .btn-delete:hover {
    background: linear-gradient(135deg,
        rgba(239, 68, 68, 1) 0%,
        rgba(220, 38, 38, 1) 50%,
        rgba(185, 28, 28, 1) 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
    color: #ffffff !important;
    text-decoration: none;
}

/* 空状态 */
.empty-state {
text-align: center;
padding: 80px 40px;
background: rgba(20, 20, 30, 0.6);
border-radius: 16px;
border: 2px dashed rgba(120, 119, 198, 0.3);
}

.empty-icon {
font-size: 64px;
color: rgba(120, 119, 198, 0.5);
margin-bottom: 20px;
text-shadow: 0 0 20px rgba(120, 119, 198, 0.3);
}

.empty-title {
font-size: 24px;
font-weight: 700;
color: #fff;
margin-bottom: 10px;
text-shadow: 0 0 15px rgba(120, 119, 198, 0.5);
}

.empty-subtitle {
font-size: 16px;
color: rgba(255, 255, 255, 0.6);
margin-bottom: 30px;
}

/* 分页样式 */
.pagination-wrapper {
display: flex;
justify-content: center;
margin-top: 40px;
padding-top: 30px;
border-top: 1px solid rgba(120, 119, 198, 0.2);
}

.pagination {
display: flex;
gap: 8px;
align-items: center;
}

.page-btn {
padding: 12px 16px;
background: rgba(20, 20, 30, 0.8);
color: rgba(255, 255, 255, 0.8);
border: 1px solid rgba(120, 119, 198, 0.3);
border-radius: 10px;
text-decoration: none;
font-weight: 600;
transition: all 0.3s ease;
min-width: 48px;
text-align: center;
}

.page-btn:hover {
background: linear-gradient(135deg,
rgba(120, 119, 198, 0.3),
rgba(255, 119, 198, 0.3));
color: #fff;
border-color: rgba(120, 119, 198, 0.6);
transform: translateY(-2px);
box-shadow: 0 6px 20px rgba(120, 119, 198, 0.2);
}

.page-btn.active {
background: linear-gradient(135deg,
rgba(120, 119, 198, 0.8),
rgba(255, 119, 198, 0.8));
color: #fff;
border-color: rgba(120, 119, 198, 0.8);
box-shadow: 0 6px 20px rgba(120, 119, 198, 0.3);
}

/* 表单样式优化 */
.form-container {
background: rgba(15, 15, 15, 0.95);
border: 1px solid rgba(120, 119, 198, 0.3);
border-radius: 20px;
backdrop-filter: blur(25px);
box-shadow:
0 20px 60px rgba(0, 0, 0, 0.4),
0 0 40px rgba(120, 119, 198, 0.1);
overflow: hidden;
position: relative;
margin-bottom: 30px;
}

.form-header {
background: linear-gradient(135deg,
rgba(120, 119, 198, 0.15) 0%,
rgba(255, 119, 198, 0.1) 50%,
rgba(120, 219, 255, 0.15) 100%);
padding: 30px;
border-bottom: 1px solid rgba(120, 119, 198, 0.2);
}

.form-body {
padding: 30px;
}

.form-control {
background: rgba(20, 20, 30, 0.8) !important;
border: 1px solid rgba(120, 119, 198, 0.3) !important;
color: #fff !important;
border-radius: 10px !important;
}

.form-control:focus {
background: rgba(20, 20, 30, 0.9) !important;
border-color: rgba(120, 119, 198, 0.6) !important;
box-shadow: 0 0 0 0.2rem rgba(120, 119, 198, 0.25) !important;
color: #fff !important;
}

.form-label {
color: rgba(255, 255, 255, 0.9) !important;
font-weight: 600;
}

.form-text {
color: rgba(255, 255, 255, 0.6) !important;
}

/* 新表单样式 */
.form-body {
background: rgba(25, 25, 35, 0.95) !important;
border-radius: 0 !important;
padding: 40px !important;
border: none !important;
box-shadow: none !important;
backdrop-filter: none !important;
}

/* 表单网格布局 */
.form-grid {
display: grid;
grid-template-columns: 1fr 400px;
gap: 30px;
margin-bottom: 30px;
}

.form-main {
display: flex;
flex-direction: column;
gap: 25px;
}

.form-sidebar {
position: sticky;
top: 20px;
height: fit-content;
}

/* 表单卡片 */
.form-card, .upload-card {
background: rgba(30, 30, 40, 0.8);
border-radius: 8px;
border: 1px solid rgba(120, 119, 198, 0.15);
overflow: hidden;
box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
background: linear-gradient(135deg,
rgba(120, 119, 198, 0.1) 0%,
rgba(120, 119, 198, 0.05) 100%);
padding: 20px 25px;
border-bottom: 1px solid rgba(120, 119, 198, 0.1);
display: flex;
align-items: center;
gap: 12px;
}

.card-icon {
width: 40px;
height: 40px;
background: linear-gradient(135deg,
rgba(120, 119, 198, 0.2) 0%,
rgba(120, 119, 198, 0.1) 100%);
border-radius: 6px;
display: flex;
align-items: center;
justify-content: center;
color: rgba(120, 119, 198, 0.9);
font-size: 16px;
}

.card-title {
font-size: 18px;
font-weight: 600;
color: rgba(255, 255, 255, 0.9);
margin: 0;
}

.card-content {
padding: 25px;
}

/* 表单组 */
.form-group {
margin-bottom: 25px;
}

.form-label {
display: flex !important;
align-items: center !important;
gap: 8px !important;
margin-bottom: 10px !important;
font-weight: 600 !important;
color: rgba(255, 255, 255, 0.9) !important;
}

.label-text {
font-size: 15px;
}

.label-required {
color: #ff6b6b;
font-size: 16px;
font-weight: 700;
}

/* 输入框样式 */
.input-wrapper, .textarea-wrapper {
position: relative;
}

.input-icon, .textarea-icon {
position: absolute;
left: 15px;
top: 50%;
transform: translateY(-50%);
color: rgba(120, 119, 198, 0.6);
font-size: 14px;
z-index: 2;
}

.textarea-icon {
top: 20px;
transform: none;
}

.form-control {
width: 100% !important;
padding: 15px 15px 15px 45px !important;
background: rgba(20, 20, 30, 0.8) !important;
border: 1px solid rgba(120, 119, 198, 0.2) !important;
border-radius: 6px !important;
color: rgba(255, 255, 255, 0.9) !important;
font-size: 15px !important;
transition: all 0.3s ease !important;
box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

.form-control:focus {
outline: none !important;
border-color: rgba(120, 119, 198, 0.6) !important;
box-shadow:
inset 0 2px 8px rgba(0, 0, 0, 0.2),
0 0 0 3px rgba(120, 119, 198, 0.1),
0 0 20px rgba(120, 119, 198, 0.2) !important;
background: rgba(25, 25, 35, 0.9) !important;
}

.form-control::placeholder {
color: rgba(255, 255, 255, 0.4) !important;
}

textarea.form-control {
resize: vertical !important;
min-height: 100px !important;
padding-top: 15px !important;
line-height: 1.6 !important;
}

/* 表单行布局 */
.form-row {
display: grid;
grid-template-columns: 1fr 1fr;
gap: 20px;
}

.form-col {
display: flex;
flex-direction: column;
}

/* 帮助文本 */
.form-help {
font-size: 13px;
color: rgba(255, 255, 255, 0.5);
margin-top: 8px;
line-height: 1.4;
}

/* 开关样式 */
.switch-wrapper {
display: flex;
align-items: center;
gap: 12px;
margin-top: 8px;
}

.switch {
position: relative;
display: inline-block;
width: 60px;
height: 30px;
}

.switch input {
opacity: 0;
width: 0;
height: 0;
}

.switch-slider {
position: absolute;
cursor: pointer;
top: 0;
left: 0;
right: 0;
bottom: 0;
background: rgba(60, 60, 70, 0.8);
border: 1px solid rgba(120, 119, 198, 0.2);
transition: 0.3s;
border-radius: 30px;
}

.switch-slider:before {
position: absolute;
content: "";
height: 22px;
width: 22px;
left: 3px;
bottom: 3px;
background: rgba(255, 255, 255, 0.8);
transition: 0.3s;
border-radius: 50%;
box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.switch input:checked + .switch-slider {
background: linear-gradient(135deg,
rgba(120, 119, 198, 0.8) 0%,
rgba(120, 119, 198, 0.6) 100%);
border-color: rgba(120, 119, 198, 0.6);
}

.switch input:checked + .switch-slider:before {
transform: translateX(30px);
background: rgba(255, 255, 255, 1);
}

.switch-label {
font-size: 14px;
color: rgba(255, 255, 255, 0.8);
font-weight: 500;
}

/* 上传区域样式 */
.upload-area {
position: relative;
border: 2px dashed rgba(120, 119, 198, 0.3);
border-radius: 12px;
padding: 30px 20px;
text-align: center;
background: rgba(20, 20, 30, 0.5);
transition: all 0.3s ease;
cursor: pointer;
margin-bottom: 20px;
}

.upload-area:hover {
border-color: rgba(120, 119, 198, 0.6);
background: rgba(25, 25, 35, 0.7);
}

.upload-input {
position: absolute;
top: 0;
left: 0;
width: 100%;
height: 100%;
opacity: 0;
cursor: pointer;
}

.upload-content {
pointer-events: none;
}

.upload-icon {
font-size: 48px;
color: rgba(120, 119, 198, 0.6);
margin-bottom: 15px;
}

.upload-title {
font-size: 16px;
font-weight: 600;
color: rgba(255, 255, 255, 0.9);
margin: 0 0 8px 0;
}

.upload-subtitle {
font-size: 14px;
color: rgba(255, 255, 255, 0.6);
margin: 0 0 20px 0;
}

.upload-button {
display: inline-block;
padding: 10px 20px;
background: linear-gradient(135deg,
rgba(120, 119, 198, 0.2) 0%,
rgba(120, 119, 198, 0.1) 100%);
border: 1px solid rgba(120, 119, 198, 0.3);
border-radius: 8px;
color: rgba(120, 119, 198, 0.9);
font-size: 14px;
font-weight: 500;
}

/* 图片预览样式 */
.preview-area {
margin-bottom: 20px;
}

.preview-image {
position: relative;
border-radius: 12px;
overflow: hidden;
background: rgba(20, 20, 30, 0.8);
border: 1px solid rgba(120, 119, 198, 0.2);
}

.preview-img {
width: 100%;
height: 200px;
object-fit: cover;
display: block;
}

.preview-overlay {
position: absolute;
top: 0;
left: 0;
right: 0;
bottom: 0;
background: rgba(0, 0, 0, 0.7);
display: flex;
align-items: center;
justify-content: center;
opacity: 0;
transition: opacity 0.3s ease;
}

.preview-image:hover .preview-overlay {
opacity: 1;
}

.preview-btn {
padding: 10px 20px;
background: linear-gradient(135deg,
rgba(120, 119, 198, 0.8) 0%,
rgba(120, 119, 198, 0.6) 100%);
border: none;
border-radius: 8px;
color: white;
font-size: 14px;
font-weight: 500;
cursor: pointer;
display: flex;
align-items: center;
gap: 8px;
transition: all 0.3s ease;
}

.preview-btn:hover {
background: linear-gradient(135deg,
rgba(120, 119, 198, 1) 0%,
rgba(120, 119, 198, 0.8) 100%);
}

/* 上传提示样式 */
.upload-tips {
background: rgba(20, 20, 30, 0.6);
border-radius: 10px;
padding: 20px;
border: 1px solid rgba(120, 119, 198, 0.1);
}

.tips-title {
font-size: 16px;
font-weight: 600;
color: rgba(255, 255, 255, 0.9);
margin: 0 0 15px 0;
display: flex;
align-items: center;
gap: 8px;
}

.tips-title:before {
content: "💡";
font-size: 18px;
}

.tips-list {
list-style: none;
padding: 0;
margin: 0;
}

.tips-list li {
font-size: 14px;
color: rgba(255, 255, 255, 0.7);
margin-bottom: 8px;
padding-left: 20px;
position: relative;
line-height: 1.4;
}

.tips-list li:before {
content: "•";
color: rgba(120, 119, 198, 0.8);
font-weight: bold;
position: absolute;
left: 0;
}

.tips-list li:last-child {
margin-bottom: 0;
}

/* 表单操作按钮 */
.form-actions {
display: flex;
justify-content: space-between;
align-items: center;
padding: 25px 0 0 0;
border-top: 1px solid rgba(120, 119, 198, 0.1);
margin-top: 30px;
}

.actions-left {
display: flex;
align-items: center;
}

.form-tips {
display: flex;
align-items: center;
gap: 8px;
font-size: 14px;
color: rgba(255, 255, 255, 0.6);
}

.form-tips i {
color: rgba(120, 119, 198, 0.7);
}

.actions-right {
display: flex;
gap: 15px;
}

.btn-cancel, .btn-submit {
display: flex;
align-items: center;
gap: 8px;
padding: 12px 24px;
border-radius: 10px;
font-size: 15px;
font-weight: 600;
text-decoration: none;
border: none;
cursor: pointer;
transition: all 0.3s ease;
min-width: 120px;
justify-content: center;
}

.btn-cancel {
background: rgba(60, 60, 70, 0.8);
color: rgba(255, 255, 255, 0.8);
border: 1px solid rgba(120, 119, 198, 0.2);
}

.btn-cancel:hover {
background: rgba(70, 70, 80, 0.9);
color: rgba(255, 255, 255, 0.9);
border-color: rgba(120, 119, 198, 0.4);
box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
text-decoration: none;
}

.btn-submit {
background: linear-gradient(135deg,
rgba(120, 119, 198, 0.8) 0%,
rgba(120, 119, 198, 0.6) 100%);
color: white;
border: 1px solid rgba(120, 119, 198, 0.6);
box-shadow: 0 5px 15px rgba(120, 119, 198, 0.2);
}

.btn-submit:hover {
background: linear-gradient(135deg,
rgba(120, 119, 198, 1) 0%,
rgba(120, 119, 198, 0.8) 100%);
border-color: rgba(120, 119, 198, 0.8);
transform: translateY(-2px);
}

/* 模态框样式已移至 admin.css 统一管理 */

/* 删除确认模态框样式已移至 admin.css 统一管理 */

/* 响应式设计 */
@media (max-width: 1200px) {
.form-grid {
grid-template-columns: 1fr;
gap: 20px;
}

.form-sidebar {
position: static;
}
}

@media (max-width: 768px) {
.list-header {
padding: 20px;
}

.list-header-content {
flex-direction: column;
gap: 20px;
align-items: flex-start;
}

.list-title-section {
gap: 15px;
}

.list-icon {
width: 50px;
height: 50px;
font-size: 20px;
}

.list-title {
font-size: 24px;
}

.list-subtitle {
font-size: 14px;
}

.list-body {
padding: 20px;
}

.banner-content {
flex-direction: column;
gap: 20px;
align-items: flex-start;
}

.banner-meta {
flex-direction: column;
gap: 10px;
align-items: flex-start;
}

.banner-actions {
width: 100%;
justify-content: flex-end;
}

.form-row {
grid-template-columns: 1fr;
gap: 15px;
}

.form-actions {
flex-direction: column;
gap: 20px;
align-items: stretch;
}

.actions-right {
justify-content: stretch;
}

.btn-cancel, .btn-submit {
flex: 1;
}

.modal-content {
margin: 20px;
max-width: calc(100vw - 40px);
}

.modal-header {
padding: 20px;
}

.modal-body {
padding: 20px;
}
}

@media (max-width: 480px) {
.banner-item {
padding: 20px;
}

.banner-thumbnail {
width: 100px;
height: 70px;
}

.banner-title {
font-size: 18px;
}

.banner-subtitle {
font-size: 13px;
}

.banner-description {
font-size: 11px;
}

.action-btn {
padding: 8px 12px;
font-size: 12px;
min-width: 70px;
}
}

/* 表单样式 */
.form-container {
background: rgba(15, 15, 15, 0.95);
border: 1px solid rgba(120, 119, 198, 0.3);
border-radius: 20px;
backdrop-filter: blur(25px);
box-shadow:
0 20px 60px rgba(0, 0, 0, 0.4),
0 0 40px rgba(120, 119, 198, 0.1);
overflow: hidden;
position: relative;
margin-bottom: 30px;
}

.form-header {
background: rgba(120, 119, 198, 0.1);
border-bottom: 1px solid rgba(120, 119, 198, 0.2);
padding: 25px 30px;
}

.form-body {
padding: 30px;
}

.form-grid {
display: grid;
grid-template-columns: 2fr 1fr;
gap: 30px;
}

.form-card {
background: rgba(20, 20, 30, 0.6);
border: 1px solid rgba(120, 119, 198, 0.2);
border-radius: 15px;
margin-bottom: 25px;
overflow: hidden;
}

.card-header {
background: rgba(120, 119, 198, 0.1);
border-bottom: 1px solid rgba(120, 119, 198, 0.2);
padding: 20px 25px;
display: flex;
align-items: center;
gap: 15px;
}

.card-icon {
width: 40px;
height: 40px;
background: linear-gradient(135deg, rgba(120, 119, 198, 0.8), rgba(255, 119, 198, 0.6));
border-radius: 10px;
display: flex;
align-items: center;
justify-content: center;
font-size: 18px;
color: white;
}

.card-title {
color: #fff;
font-size: 18px;
font-weight: 600;
margin: 0;
}

.card-content {
padding: 25px;
}

.form-group {
margin-bottom: 25px;
}

.form-label {
display: flex;
align-items: center;
gap: 8px;
margin-bottom: 10px;
color: rgba(255, 255, 255, 0.9);
font-weight: 500;
font-size: 14px;
}

.label-required {
color: #ff6b6b;
font-weight: 600;
}

.input-wrapper,
.textarea-wrapper {
position: relative;
}

.input-icon,
.textarea-icon {
position: absolute;
left: 15px;
top: 50%;
transform: translateY(-50%);
color: rgba(120, 119, 198, 0.7);
font-size: 16px;
z-index: 2;
}

.textarea-icon {
top: 20px;
transform: none;
}

.form-control {
width: 100%;
padding: 15px 15px 15px 50px;
background: rgba(30, 30, 40, 0.8);
border: 1px solid rgba(120, 119, 198, 0.3);
border-radius: 12px;
color: #fff;
font-size: 14px;
transition: all 0.3s ease;
box-sizing: border-box;
}

.form-control:focus {
outline: none;
border-color: rgba(120, 119, 198, 0.6);
background: rgba(30, 30, 40, 0.9);
box-shadow: 0 0 20px rgba(120, 119, 198, 0.2);
}

.form-control::placeholder {
color: rgba(255, 255, 255, 0.5);
}

textarea.form-control {
resize: vertical;
min-height: 120px;
padding-top: 15px;
}

.form-help {
margin-top: 8px;
font-size: 12px;
color: rgba(255, 255, 255, 0.6);
display: flex;
align-items: center;
gap: 6px;
}

.form-row {
display: grid;
grid-template-columns: 1fr 1fr;
gap: 20px;
}

.switch-wrapper {
display: flex;
align-items: center;
gap: 15px;
margin-top: 10px;
}

.switch {
position: relative;
display: inline-block;
width: 60px;
height: 34px;
}

.switch input {
opacity: 0;
width: 0;
height: 0;
}

.switch-slider {
position: absolute;
cursor: pointer;
top: 0;
left: 0;
right: 0;
bottom: 0;
background-color: rgba(120, 119, 198, 0.3);
transition: .4s;
border-radius: 34px;
}

.switch-slider:before {
position: absolute;
content: "";
height: 26px;
width: 26px;
left: 4px;
bottom: 4px;
background-color: white;
transition: .4s;
border-radius: 50%;
}

.switch input:checked + .switch-slider {
background: linear-gradient(135deg, #7877c6, #ff77c6);
}

.switch input:checked + .switch-slider:before {
transform: translateX(26px);
}

.switch-label {
color: rgba(255, 255, 255, 0.9);
font-size: 14px;
}

/* 图片上传样式 */
.upload-area {
border: 2px dashed rgba(120, 119, 198, 0.3);
border-radius: 15px;
padding: 30px;
text-align: center;
cursor: pointer;
transition: all 0.3s ease;
position: relative;
overflow: hidden;
}

.upload-area:hover {
border-color: rgba(120, 119, 198, 0.6);
background: rgba(120, 119, 198, 0.05);
}

.upload-placeholder {
display: flex;
flex-direction: column;
align-items: center;
gap: 20px;
}



.upload-text h6 {
color: #fff;
font-size: 18px;
font-weight: 600;
margin: 0 0 8px 0;
}

.upload-text p {
color: rgba(255, 255, 255, 0.7);
font-size: 14px;
margin: 0;
}

.current-image {
position: relative;
border-radius: 12px;
overflow: hidden;
}

.current-image img {
width: 100%;
height: 200px;
object-fit: cover;
display: block;
}

.image-overlay {
position: absolute;
top: 0;
left: 0;
right: 0;
bottom: 0;
background: rgba(0, 0, 0, 0.7);
display: flex;
align-items: center;
justify-content: center;
opacity: 0;
transition: all 0.3s ease;
}

.current-image:hover .image-overlay {
opacity: 1;
}

.overlay-content {
text-align: center;
color: white;
}

.overlay-content i {
font-size: 24px;
margin-bottom: 8px;
display: block;
}

.upload-info {
margin-top: 20px;
padding-top: 20px;
border-top: 1px solid rgba(120, 119, 198, 0.2);
}

.info-item {
display: flex;
align-items: center;
gap: 10px;
margin-bottom: 8px;
color: rgba(255, 255, 255, 0.7);
font-size: 12px;
}

.info-item i {
color: rgba(120, 119, 198, 0.7);
width: 16px;
}

/* 表单按钮 */
.form-actions {
display: flex;
justify-content: space-between;
align-items: center;
margin-top: 40px;
padding-top: 30px;
border-top: 1px solid rgba(120, 119, 198, 0.2);
}

.actions-left {
flex: 1;
}

.form-tips {
display: flex;
align-items: center;
gap: 8px;
color: rgba(255, 255, 255, 0.7);
font-size: 14px;
}

.form-tips i {
color: rgba(120, 119, 198, 0.7);
}

.actions-right {
display: flex;
gap: 15px;
}

.form-actions-bottom {
display: flex;
justify-content: center;
gap: 20px;
margin-top: 30px;
margin-bottom: 30px;
}

.btn-submit {
background: linear-gradient(135deg, #7877c6, #ff77c6);
color: white;
border: none;
padding: 15px 25px;
border-radius: 12px;
font-size: 16px;
font-weight: 600;
cursor: pointer;
transition: all 0.3s ease;
display: flex;
align-items: center;
justify-content: center;
gap: 10px;
text-decoration: none;
box-shadow: 0 4px 15px rgba(120, 119, 198, 0.3);
}

.btn-submit:hover {
background: linear-gradient(135deg, #6b6bb6, #ee66b6);
box-shadow: 0 6px 20px rgba(120, 119, 198, 0.4);
transform: translateY(-2px);
}

.btn-cancel {
background: rgba(120, 119, 198, 0.2);
color: rgba(255, 255, 255, 0.9);
border: 1px solid rgba(120, 119, 198, 0.3);
padding: 15px 25px;
border-radius: 12px;
font-size: 16px;
font-weight: 600;
cursor: pointer;
transition: all 0.3s ease;
display: flex;
align-items: center;
justify-content: center;
gap: 10px;
text-decoration: none;
}

.btn-cancel:hover {
background: rgba(120, 119, 198, 0.3);
color: white;
transform: translateY(-2px);
}

/* 图片选择器相关样式 */


.image-preview-container {
    position: relative;
    width: 100%;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    text-align: center;
    color: #ffffff;
}

.placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.placeholder-icon {
    font-size: 48px;
    color: rgba(120, 219, 255, 0.8);
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.placeholder-text h6 {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 5px 0;
    color: #ffffff;
    font-family: 'Orbitron', monospace;
}

.placeholder-text p {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
    line-height: 1.5;
}

.current-image-preview {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
}

.current-image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.current-image-preview:hover img {
    transform: scale(1.05);
}

.btn-select-image {
    width: 100%;
    margin-top: 15px;
    padding: 12px 20px;
    background: linear-gradient(135deg, 
        rgba(120, 119, 198, 0.8) 0%, 
        rgba(255, 119, 198, 0.6) 100%);
    border: none;
    border-radius: 8px;
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(120, 119, 198, 0.3);
}

.btn-select-image:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(120, 119, 198, 0.4);
    background: linear-gradient(135deg, 
        rgba(120, 119, 198, 1) 0%, 
        rgba(255, 119, 198, 0.8) 100%);
}

.btn-select-image i {
    font-size: 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .image-preview-container {
        height: 150px;
    }
    
    .placeholder-icon {
        font-size: 36px;
    }
    
    .placeholder-text h6 {
        font-size: 16px;
    }
    
    .placeholder-text p {
        font-size: 13px;
    }
    
    .btn-select-image {
        padding: 10px 16px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .image-preview-container {
        height: 120px;
    }
    
    .placeholder-icon {
        font-size: 28px;
    }
    
    .placeholder-text h6 {
        font-size: 14px;
    }
}

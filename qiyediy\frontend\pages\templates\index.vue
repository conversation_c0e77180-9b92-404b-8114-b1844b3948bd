<!--
  三只鱼网络科技 | 韩总 | 2024-12-20
  QiyeDIY企业建站系统 - 模板中心页面
-->

<template>
  <div class="templates-page">
    <!-- 页面头部 -->
    <AppHeader />

    <!-- 页面内容 -->
    <main class="main-content">
      <!-- 页面标题区域 -->
      <section class="page-hero">
        <div class="container mx-auto px-4 py-16">
          <div class="text-center">
            <h1 class="hero-title">模板中心</h1>
            <p class="hero-subtitle">
              精选优质模板，快速搭建专业网站
            </p>
          </div>
        </div>
      </section>

      <!-- 筛选和搜索 -->
      <section class="filters-section">
        <div class="container mx-auto px-4">
          <div class="filters-container">
            <!-- 搜索框 -->
            <div class="search-box">
              <input
                v-model="searchKeyword"
                type="text"
                placeholder="搜索模板..."
                class="search-input"
                @input="handleSearch"
              />
              <button class="search-btn">
                <Icon name="heroicons:magnifying-glass" />
              </button>
            </div>

            <!-- 分类筛选 -->
            <div class="category-filters">
              <button
                v-for="category in categories"
                :key="category.value"
                class="category-btn"
                :class="{ active: selectedCategory === category.value }"
                @click="handleCategoryChange(category.value)"
              >
                {{ category.label }}
              </button>
            </div>

            <!-- 排序选择 -->
            <div class="sort-select">
              <select v-model="sortBy" @change="handleSortChange">
                <option value="created_at">最新发布</option>
                <option value="downloads">下载量</option>
                <option value="rating">评分</option>
                <option value="name">名称</option>
              </select>
            </div>
          </div>
        </div>
      </section>

      <!-- 模板列表 -->
      <section class="templates-section">
        <div class="container mx-auto px-4 py-8">
          <!-- 加载状态 -->
          <div v-if="loading" class="loading-state">
            <div class="loading-grid">
              <div v-for="i in 12" :key="i" class="template-skeleton">
                <div class="skeleton-image"></div>
                <div class="skeleton-content">
                  <div class="skeleton-title"></div>
                  <div class="skeleton-category"></div>
                  <div class="skeleton-actions"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 模板网格 -->
          <div v-else-if="templates.length" class="templates-grid">
            <div
              v-for="template in templates"
              :key="template.id"
              class="template-card"
              @click="handleTemplateClick(template)"
            >
              <!-- 模板预览图 -->
              <div class="template-image">
                <NuxtImg
                  :src="template.preview_image || '/images/template-placeholder.png'"
                  :alt="template.name"
                  loading="lazy"
                />
                <div class="template-overlay">
                  <div class="overlay-actions">
                    <button class="action-btn preview" @click.stop="handlePreview(template)">
                      <Icon name="heroicons:eye" />
                      预览
                    </button>
                    <button class="action-btn use" @click.stop="handleUseTemplate(template)">
                      <Icon name="heroicons:plus" />
                      使用
                    </button>
                  </div>
                </div>
                
                <!-- 模板标签 -->
                <div class="template-badges">
                  <span v-if="template.is_featured" class="badge featured">精选</span>
                  <span v-if="template.is_free" class="badge free">免费</span>
                  <span v-else class="badge premium">付费</span>
                </div>
              </div>

              <!-- 模板信息 -->
              <div class="template-info">
                <h3 class="template-name">{{ template.name }}</h3>
                <p class="template-category">{{ template.category_name }}</p>
                
                <div class="template-meta">
                  <div class="meta-item">
                    <Icon name="heroicons:star" />
                    <span>{{ template.rating || 5.0 }}</span>
                  </div>
                  <div class="meta-item">
                    <Icon name="heroicons:arrow-down-tray" />
                    <span>{{ template.downloads || 0 }}</span>
                  </div>
                </div>

                <div class="template-actions">
                  <button class="btn btn-outline btn-sm" @click.stop="handlePreview(template)">
                    预览
                  </button>
                  <button class="btn btn-primary btn-sm" @click.stop="handleUseTemplate(template)">
                    使用模板
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-state">
            <div class="empty-content">
              <Icon name="heroicons:document-text" class="empty-icon" />
              <h3>暂无模板</h3>
              <p>{{ searchKeyword ? '没有找到匹配的模板' : '该分类下暂无模板' }}</p>
              <button class="btn btn-primary" @click="handleResetFilters">
                重置筛选
              </button>
            </div>
          </div>

          <!-- 分页 -->
          <div v-if="pagination.total > pagination.per_page" class="pagination-container">
            <Pagination
              :current="pagination.current_page"
              :total="pagination.total"
              :page-size="pagination.per_page"
              @change="handlePageChange"
            />
          </div>
        </div>
      </section>
    </main>

    <!-- 页面底部 -->
    <AppFooter />

    <!-- 模板预览弹窗 -->
    <TemplatePreviewModal
      v-model="showPreview"
      :template="selectedTemplate"
    />

    <!-- 使用模板弹窗 -->
    <UseTemplateModal
      v-model="showUseTemplate"
      :template="selectedTemplate"
      @success="handleUseSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import type { DiyTemplate } from '~/types/diy'

// SEO配置
useSeoMeta({
  title: '模板中心',
  description: '精选优质网站模板，涵盖企业官网、电商、博客等多种类型，快速搭建专业网站',
  keywords: '网站模板,企业模板,电商模板,博客模板,响应式模板',
  ogTitle: '模板中心 - QiyeDIY企业建站系统',
  ogDescription: '精选优质网站模板，快速搭建专业网站',
  ogImage: '/images/templates-og.jpg'
})

// 响应式数据
const loading = ref(true)
const templates = ref<DiyTemplate[]>([])
const selectedTemplate = ref<DiyTemplate | null>(null)
const showPreview = ref(false)
const showUseTemplate = ref(false)

// 筛选和搜索
const searchKeyword = ref('')
const selectedCategory = ref('all')
const sortBy = ref('created_at')

// 分页数据
const pagination = reactive({
  current_page: 1,
  per_page: 12,
  total: 0,
  last_page: 1
})

// 分类数据
const categories = ref([
  { label: '全部', value: 'all' },
  { label: '企业官网', value: 'corporate' },
  { label: '电商商城', value: 'ecommerce' },
  { label: '个人博客', value: 'blog' },
  { label: '作品展示', value: 'portfolio' },
  { label: '教育培训', value: 'education' },
  { label: '餐饮美食', value: 'restaurant' },
  { label: '医疗健康', value: 'medical' },
  { label: '房地产', value: 'realestate' },
  { label: '科技创新', value: 'technology' }
])

/**
 * 加载模板数据
 */
const loadTemplates = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.current_page,
      per_page: pagination.per_page,
      keyword: searchKeyword.value,
      category: selectedCategory.value === 'all' ? '' : selectedCategory.value,
      sort_by: sortBy.value,
      sort_order: 'desc'
    }
    
    const response = await $fetch('/api/templates', { params })
    
    if (response.code === 200) {
      templates.value = response.data.data
      Object.assign(pagination, response.data.pagination)
    }
    
  } catch (error) {
    console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败')
  } finally {
    loading.value = false
  }
}

/**
 * 处理搜索
 */
const handleSearch = debounce(() => {
  pagination.current_page = 1
  loadTemplates()
}, 500)

/**
 * 处理分类变化
 */
const handleCategoryChange = (category: string) => {
  selectedCategory.value = category
  pagination.current_page = 1
  loadTemplates()
}

/**
 * 处理排序变化
 */
const handleSortChange = () => {
  pagination.current_page = 1
  loadTemplates()
}

/**
 * 处理页码变化
 */
const handlePageChange = (page: number) => {
  pagination.current_page = page
  loadTemplates()
  
  // 滚动到顶部
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

/**
 * 处理模板点击
 */
const handleTemplateClick = (template: DiyTemplate) => {
  selectedTemplate.value = template
  showPreview.value = true
}

/**
 * 处理预览
 */
const handlePreview = (template: DiyTemplate) => {
  selectedTemplate.value = template
  showPreview.value = true
}

/**
 * 处理使用模板
 */
const handleUseTemplate = (template: DiyTemplate) => {
  selectedTemplate.value = template
  showUseTemplate.value = true
}

/**
 * 处理使用成功
 */
const handleUseSuccess = (pageId: number) => {
  ElMessage.success('模板应用成功')
  navigateTo(`/admin/diy/editor/${pageId}`)
}

/**
 * 重置筛选
 */
const handleResetFilters = () => {
  searchKeyword.value = ''
  selectedCategory.value = 'all'
  sortBy.value = 'created_at'
  pagination.current_page = 1
  loadTemplates()
}

/**
 * 防抖函数
 */
function debounce(func: Function, wait: number) {
  let timeout: NodeJS.Timeout
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 页面加载
onMounted(() => {
  loadTemplates()
})
</script>

<style lang="scss" scoped>
.templates-page {
  min-height: 100vh;
  background: #f8fafc;
}

.main-content {
  padding-top: 70px; // 头部高度
}

// 页面标题区域
.page-hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  
  .hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    
    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }
  
  .hero-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
  }
}

// 筛选区域
.filters-section {
  background: white;
  border-bottom: 1px solid #e5e5e5;
  padding: 2rem 0;
}

.filters-container {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
  
  .search-input {
    width: 100%;
    padding: 0.75rem 3rem 0.75rem 1rem;
    border: 1px solid #e5e5e5;
    border-radius: 0.5rem;
    font-size: 1rem;
    
    &:focus {
      outline: none;
      border-color: #667eea;
    }
  }
  
  .search-btn {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
  }
}

.category-filters {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  
  .category-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #e5e5e5;
    border-radius: 0.5rem;
    background: white;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #667eea;
      color: #667eea;
    }
    
    &.active {
      background: #667eea;
      border-color: #667eea;
      color: white;
    }
  }
}

.sort-select {
  select {
    padding: 0.75rem 1rem;
    border: 1px solid #e5e5e5;
    border-radius: 0.5rem;
    background: white;
    cursor: pointer;
    
    &:focus {
      outline: none;
      border-color: #667eea;
    }
  }
}

// 模板列表
.templates-section {
  min-height: 60vh;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

.template-card {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }
}

.template-image {
  position: relative;
  aspect-ratio: 16/10;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  &:hover img {
    transform: scale(1.05);
  }
}

.template-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  
  .template-card:hover & {
    opacity: 1;
  }
}

.overlay-actions {
  display: flex;
  gap: 1rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: 2px solid white;
  border-radius: 0.5rem;
  background: transparent;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: white;
    color: #333;
  }
  
  &.use {
    background: #667eea;
    border-color: #667eea;
    
    &:hover {
      background: white;
      color: #667eea;
    }
  }
}

.template-badges {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.badge {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  
  &.featured {
    background: #f59e0b;
    color: white;
  }
  
  &.free {
    background: #10b981;
    color: white;
  }
  
  &.premium {
    background: #8b5cf6;
    color: white;
  }
}

.template-info {
  padding: 1.5rem;
}

.template-name {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.template-category {
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.template-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  
  .meta-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    color: #6b7280;
  }
}

.template-actions {
  display: flex;
  gap: 0.5rem;
}

// 加载状态
.loading-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

.template-skeleton {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  
  .skeleton-image {
    aspect-ratio: 16/10;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }
  
  .skeleton-content {
    padding: 1.5rem;
    
    .skeleton-title {
      height: 1.5rem;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
      margin-bottom: 0.5rem;
      border-radius: 0.25rem;
    }
    
    .skeleton-category {
      height: 1rem;
      width: 60%;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
      margin-bottom: 1rem;
      border-radius: 0.25rem;
    }
    
    .skeleton-actions {
      height: 2rem;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
      border-radius: 0.25rem;
    }
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// 空状态
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40vh;
  text-align: center;
}

.empty-content {
  max-width: 400px;
  
  .empty-icon {
    width: 4rem;
    height: 4rem;
    color: #d1d5db;
    margin: 0 auto 1rem;
  }
  
  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #374151;
  }
  
  p {
    color: #6b7280;
    margin-bottom: 2rem;
  }
}

// 分页
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
}

// 按钮样式
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  cursor: pointer;
  
  &.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    }
  }
  
  &.btn-outline {
    border-color: #667eea;
    color: #667eea;
    background: transparent;
    
    &:hover {
      background: #667eea;
      color: white;
    }
  }
  
  &.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
}
</style>

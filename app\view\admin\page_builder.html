<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面装修 - 三只鱼网络</title>
    
    <!-- 引用公共CSS -->
    {include file="admin/common/css"}
    
    <!-- 页面装修专用CSS -->
    <link rel="stylesheet" href="/assets/css/admin/page-builder.css">
</head>
<body>
    <!-- 顶部导航 -->
    {include file="admin/common/header"}
    
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            {include file="admin/common/sidebar"}

            <!-- 主要内容 -->
            <main class="main-content">
                <!-- 引用统一消息组件 -->
                {include file="admin/common/message"}

                <!-- 页面内容区域 -->
                <div class="content-body">
                    <div class="page-builder-container">

                    {if condition="$action == 'list'"}
                        <!-- 模板列表视图 -->
                        <div class="list-header">
                            <div class="list-header-content">
                                <div class="list-title-section">
                                    <div class="list-icon">
                                        <i class="fas fa-paint-brush"></i>
                                    </div>
                                    <div>
                                        <h1 class="list-title">页面装修</h1>
                                        <p class="list-subtitle">可视化页面设计与模板管理</p>
                                    </div>
                                </div>
                                <div class="header-actions">
                                    {if condition="!empty($currentType) && $currentType != 'all'"}
                                        <a href="/admin/page-builder?action=add&preset_type={$currentType}" class="btn-add-custom">
                                            <i class="fas fa-plus"></i>
                                            <span>新建{$templateTypes[$currentType]|default='模板'}</span>
                                        </a>
                                    {else /}
                                        <a href="/admin/page-builder?action=add" class="btn-add-custom">
                                            <i class="fas fa-plus"></i>
                                            <span>新建模板</span>
                                        </a>
                                    {/if}
                                </div>
                            </div>
                        </div>

                        <!-- 类型筛选标签 -->
                        <div class="type-filter-tabs">
                            <div class="tabs-container1">
                                <a href="/admin/page-builder?type=all"
                                   class="tab-item {$currentType == '' || $currentType == 'all' ? 'active' : ''}">
                                    <i class="fas fa-th-large"></i>
                                    <span>全部</span>
                                    <span class="count">({$typeStats.all|default=0})</span>
                                </a>
                                {volist name="templateTypes" id="typeName" key="typeKey"}
                                <div class="tab-item-wrapper">
                                    <a href="/admin/page-builder?type={$key}"
                                       class="tab-item {$currentType == $key ? 'active' : ''}">
                                        {switch name="key"}
                                            {case value="home"}<i class="fas fa-home"></i>{/case}
                                            {case value="product"}<i class="fas fa-box"></i>{/case}
                                            {case value="news"}<i class="fas fa-newspaper"></i>{/case}
                                            {case value="case"}<i class="fas fa-briefcase"></i>{/case}
                                            {case value="contact"}<i class="fas fa-envelope"></i>{/case}
                                            {case value="about"}<i class="fas fa-info-circle"></i>{/case}
                                            {default /}<i class="fas fa-file-alt"></i>
                                        {/switch}
                                        <span>{$typeName}</span>
                                        <span class="count">({$typeStats[$key]|default=0})</span>
                                    </a>
                                    <a href="/admin/page-builder?action=add&preset_type={$key}"
                                       class="add-type-btn" title="添加{$typeName}模板">
                                        <i class="fas fa-plus"></i>
                                    </a>
                                </div>
                                {/volist}
                            </div>
                        </div>

                        <!-- 列表主体 -->
                        <div class="list-body">
                            {if condition="$templatesList && count($templatesList) > 0"}
                                <!-- 模板列表 -->
                                <div class="templates-list">
                                    {volist name="templatesList" id="template" key="key"}
                                    <div class="template-item">
                                        <div class="template-content">
                                            <!-- 缩略图 -->
                                            <div class="template-thumbnail">
                                                <div class="template-thumb-placeholder">
                                                    <i class="fas fa-paint-brush"></i>
                                                </div>
                                            </div>

                                            <!-- 模板信息 -->
                                            <div class="template-info">
                                                <div class="template-header">
                                                    <h3 class="template-name">{$template.name}</h3>
                                                </div>

                                                <div class="template-meta">
                                                    <div class="meta-item">
                                                        <i class="fas fa-clock"></i>
                                                        <span>{$template.updated_at|date='m-d H:i'}</span>
                                                    </div>
                                                    <div class="meta-item">
                                                        <i class="fas fa-cube"></i>
                                                        <span>组件数: {$template.component_stats.total|default=0}</span>
                                                    </div>
                                                </div>

                                                {if condition="$template.description"}
                                                    <div class="template-description">
                                                        {$template.description}
                                                    </div>
                                                {/if}
                                            </div>

                                            <!-- 操作按钮 -->
                                            <div class="template-actions">
                                                <div class="template-badges">
                                                    <span class="badge badge-type">
                                                        <i class="fas fa-tag"></i>
                                                        {$template.type_text}
                                                    </span>
                                                </div>
                                                <div class="status-toggle">
                                                    <label class="switch">
                                                        <input type="checkbox"
                                                               {$template.status ? 'checked' : ''}
                                                               onchange="toggleStatus('{$template.id}', this.checked ? 1 : 0)">
                                                        <span class="slider"></span>
                                                    </label>
                                                    <span class="status-label">{$template.status ? '已发布' : '草稿'}</span>
                                                </div>

                                                <div class="action-buttons">
                                                    {if condition="!empty($template.slug) && $template.status"}
                                                        <a href="/page/{$template.slug}"
                                                           class="btn-action btn-view" title="查看页面"
                                                           style="background: #17a2b8;" target="_blank">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    {/if}
                                                    <!-- <a href="/admin/page-designer?id={$template.id}"
                                                       class="btn-action btn-design" title="GrapesJS设计器">
                                                        <i class="fas fa-paint-brush"></i>
                                                    </a> -->
                                                    <a href="/admin/page-designer/diy?id={$template.id}"
                                                       class="btn-action btn-design" title="DIY设计器" style="background: #28a745;"
                                                       target="_blank">
                                                        <i class="fas fa-magic"></i>
                                                    </a>
                                                    <a href="/admin/page-builder?action=edit&id={$template.id}"
                                                       class="btn-action btn-edit" title="编辑">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button onclick="deleteItem('{$template.id}', '{$template.name|htmlentities}', '/admin/page-builder?action=delete&id={$template.id}')"
                                                            class="btn-action btn-delete" title="删除">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {/volist}
                                </div>
                            {else /}
                                <div class="empty-state">
                                    <div class="empty-icon">
                                        <i class="fas fa-paint-brush"></i>
                                    </div>
                                    {if condition="!empty($currentType) && $currentType != 'all'"}
                                        <h3 class="empty-title">暂无{$templateTypes[$currentType]|default='该类型'}模板</h3>
                                        <p class="empty-subtitle">还没有创建任何{$templateTypes[$currentType]|default='该类型'}模板，点击下方按钮开始创建吧！</p>
                                        <a href="/admin/page-builder?action=add&preset_type={$currentType}" class="btn-add-custom">
                                            <i class="fas fa-plus"></i>
                                            创建{$templateTypes[$currentType]|default='该类型'}模板
                                        </a>
                                    {else /}
                                        <h3 class="empty-title">暂无模板</h3>
                                        <p class="empty-subtitle">还没有创建任何页面模板，点击上方按钮开始创建您的第一个模板吧！</p>
                                    {/if}
                                </div>
                            {/if}
                        </div>



                        <!-- 分页 -->
                        {if condition="$templatesList && $templatesList->hasPages()"}
                        <div class="pagination-wrapper">
                            <div class="pagination-container">
                                {$templatesList->render()|raw}
                            </div>
                        </div>
                        {/if}

                    {elseif condition="$action == 'add' || $action == 'edit'"}
                        <!-- 添加/编辑模板表单 -->
                        <div class="form-header">
                            <div class="form-title-section">
                                <div class="form-icon">
                                    <i class="fas fa-{$action == 'add' ? 'plus' : 'edit'}"></i>
                                </div>
                                <div>
                                    <h1 class="form-title">{$action == 'add' ? '新建模板' : '编辑模板'}</h1>
                                    <p class="form-subtitle">设置模板基础信息</p>
                                </div>
                            </div>
                            <div class="header-actions">
                                <a href="/admin/page-builder" class="btn-add-custom">
                                    <i class="fas fa-arrow-left"></i>
                                    返回列表
                                </a>
                            </div>
                        </div>

                        <div class="form-body">
                            <form method="post" class="template-form">
                                <input type="hidden" name="action" value="{$action}">
                                {if condition="$action == 'edit' && isset($editData) && isset($editData->id)"}
                                <input type="hidden" name="id" value="{$editData->id}">
                                {/if}

                                <!-- 基本信息区域 -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <h3 class="section-title">
                                            <i class="fas fa-info-circle"></i>
                                            基本信息
                                        </h3>
                                    </div>

                                    <div class="row" style="margin-top: 20px;">
                                        <div class="col-lg-6 col-md-12 mb-3">
                                            <div class="form-group">
                                                <label for="name" class="form-label required">
                                                    <i class="fas fa-tag"></i>
                                                    模板名称
                                                </label>
                                                <input type="text" id="name" name="name" class="form-input"
                                                       value="{if condition="isset($editData) && isset($editData->name)"}{$editData->name}{/if}"
                                                       placeholder="请输入模板名称" required>
                                            </div>
                                        </div>

                                        <div class="col-lg-6 col-md-12 mb-3">
                                            <div class="form-group">
                                                <label for="slug" class="form-label required">
                                                    <i class="fas fa-link"></i>
                                                    页面URL标识
                                                </label>
                                                <input type="text" id="slug" name="slug" class="form-input"
                                                       value="{if condition="isset($editData) && isset($editData->slug)"}{$editData->slug}{/if}"
                                                       placeholder="例如: about-us" required>
                                                <div class="form-help">用于生成页面访问链接，只能包含字母、数字、连字符</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-lg-6 col-md-12 mb-3">
                                            <div class="form-group">
                                                <label for="type" class="form-label">
                                                    <i class="fas fa-layer-group"></i>
                                                    模板类型
                                                </label>
                                                <select id="type" name="type" class="form-select">
                                                    {volist name="templateTypes" id="typeName" key="typeKey"}
                                                    <option value="{$key}" {if condition="(isset($editData) && isset($editData->type) && $editData->type == $key) || (isset($editData) && is_object($editData) && property_exists($editData, 'type') && $editData->type == $key)"}selected{/if}>{$typeName}</option>
                                                    {/volist}
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="description" class="form-label">
                                            <i class="fas fa-align-left"></i>
                                            模板描述
                                        </label>
                                        <textarea id="description" name="description" class="form-textarea"
                                                  placeholder="请输入模板描述，说明该模板的用途和特点">{if condition="isset($editData) && isset($editData->description)"}{$editData->description}{/if}</textarea>
                                        <div class="form-help">简要描述该模板的功能和适用场景</div>
                                    </div>

                                    <div class="row">
                                        <div class="col-lg-6 col-md-12 mb-3">
                                            <div class="form-group">
                                                <label for="sort_order" class="form-label">
                                                    <i class="fas fa-sort-numeric-up"></i>
                                                    排序权重
                                                </label>
                                                <input type="number" id="sort_order" name="sort_order" class="form-input"
                                                       value="{if condition="isset($editData) && isset($editData->sort_order)"}{$editData->sort_order|default='0'}{else}0{/if}"
                                                       placeholder="数字越大越靠前" min="0">
                                                <div class="form-help">用于控制模板在列表中的显示顺序</div>
                                            </div>
                                        </div>

                                        <div class="col-lg-6 col-md-12 mb-3">
                                            <div class="form-group">
                                                <label class="form-check" style="margin-top: 25px;">
                                                    <input type="checkbox" id="status" name="status" class="form-check-input"
                                                           {if condition="isset($editData) && isset($editData->status) && $editData->status"}checked{/if}>
                                                    <div class="checkbox-custom"></div>
                                                    <span class="form-check-label">
                                                        <i class="fas fa-eye"></i>
                                                        启用模板
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn-primary">
                                        <i class="fas fa-save"></i>
                                        保存模板
                                    </button>
                                    <a href="/admin/page-builder" class="btn-secondary">
                                        <i class="fas fa-arrow-left"></i>
                                        返回列表
                                    </a>
                                </div>
                            </form>
                        </div>

                    {/if}

                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 引用公共JS -->
    {include file="admin/common/js"}
    
    <!-- 页面装修专用JS -->
    <script src="/assets/js/admin/page-builder.js"></script>
</body>
</html>

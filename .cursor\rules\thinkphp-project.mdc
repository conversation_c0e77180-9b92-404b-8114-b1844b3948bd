---
description: 
globs: 
alwaysApply: false
---
# ThinkPHP 8 企业网站项目开发规范

## 项目概述
这是一个基于 ThinkPHP 8 框架的企业网站项目，包含新闻管理、案例展示、页面构建器等功能。

## 核心架构

### 框架信息
- **框架版本**: ThinkPHP 8.0+
- **PHP版本要求**: PHP 8.0+
- **数据库**: MySQL
- **前端技术**: HTML5, CSS3, JavaScript, Bootstrap

### 目录结构
- `app/` - 应用核心目录
  - `controller/` - 控制器 ([Index.php](mdc:app/controller/Index.php), [Contact.php](mdc:app/controller/Contact.php))
  - `admin/controller/` - 后台管理控制器
  - `model/` - 数据模型
  - `service/` - 业务服务层
  - `view/` - 视图模板
  - `middleware/` - 中间件
- `config/` - 配置文件 ([app.php](mdc:config/app.php), [database.php](mdc:config/database.php))
- `public/` - 公共资源目录
  - `diy/` - 页面构建器相关文件
  - `assets/` - 静态资源
- `database/migrations/` - 数据库迁移文件
- [composer.json](mdc:composer.json) - 依赖管理

## 开发规范

### PHP 代码规范
1. **命名规范**: 遵循 PSR-2 和 PSR-4 规范
2. **类命名**: 使用 PascalCase (如: `IndexController`, `NewsModel`)
3. **方法命名**: 使用 camelCase (如: `getUserInfo`, `updateStatus`)
4. **常量命名**: 使用 UPPER_CASE (如: `DEFAULT_PAGE_SIZE`)

### 控制器开发规范
```php
<?php
namespace app\controller;

use app\BaseController;
use app\model\YourModel;
use app\service\YourService;

class YourController extends BaseController
{
    public function index()
    {
        // 获取配置
        $siteConfig = ConfigService::getAll();
        
        // 业务逻辑
        $data = YourService::getData();
        
        // 返回视图
        return view('template/name', [
            'data' => $data,
            'siteConfig' => $siteConfig
        ]);
    }
}
```

### 模型开发规范
1. 继承 `think\Model`
2. 定义表名、主键、字段类型
3. 使用软删除、时间戳等特性
4. 定义关联关系

### 服务层规范
1. 业务逻辑封装在 Service 层
2. 控制器只负责请求处理和响应
3. Service 类使用静态方法或单例模式

## 页面构建器 (DIY) 开发规范

### 组件开发
页面构建器位于 `public/diy/js/components/` 目录，包含以下组件：
- [navbar.js](mdc:public/diy/js/components/navbar.js) - 导航栏组件
- [hero.js](mdc:public/diy/js/components/hero.js) - 英雄区块组件
- [card.js](mdc:public/diy/js/components/card.js) - 卡片组件
- [footer.js](mdc:public/diy/js/components/footer.js) - 页脚组件

### 组件开发规范
1. **按需CSS生成**: 使用 `componentStylesLibrary` 和 `generateUsedComponentStyles()`
2. **3D效果支持**: 使用 `effect3DStylesLibrary` 管理3D效果样式
3. **属性配置**: 每个组件都应有完整的属性配置面板
4. **响应式设计**: 支持移动端、平板、桌面端适配

### 组件结构模板
```javascript
const componentName = {
    name: 'Component Name',
    icon: 'fas fa-icon',
    category: 'category',
    properties: {
        // 组件属性定义
    },
    template: `
        <!-- HTML模板 -->
    `,
    generateCSS: function() {
        // 动态生成CSS
    }
};
```

## 数据库规范

### 表命名
- 使用小写字母和下划线
- 表名使用复数形式 (如: `news`, `banners`, `cases`)

### 字段规范
- 主键使用 `id` (自增)
- 创建时间: `create_time`
- 更新时间: `update_time`
- 软删除: `delete_time`
- 状态字段: `status` (0=禁用, 1=启用)

## 前端开发规范

### CSS 规范
1. 使用 BEM 命名规范
2. 响应式设计优先
3. 使用 CSS 变量管理主题色彩
4. 避免内联样式

### JavaScript 规范
1. 使用 ES6+ 语法
2. 模块化开发
3. 事件委托处理
4. 避免全局变量污染

## 安全规范

### 输入验证
1. 所有用户输入必须验证
2. 使用 ThinkPHP 验证器
3. SQL 注入防护
4. XSS 攻击防护

### 权限控制
1. 后台管理需要登录验证
2. 使用中间件进行权限检查
3. 敏感操作需要二次确认

## 性能优化

### 数据库优化
1. 合理使用索引
2. 避免 N+1 查询问题
3. 使用缓存减少数据库访问
4. 分页查询大数据集

### 前端优化
1. 图片压缩和懒加载
2. CSS/JS 文件压缩
3. 使用 CDN 加速
4. 浏览器缓存策略

## 部署规范

### 环境配置
1. 开发环境使用 `php think run`
2. 生产环境配置 Nginx/Apache
3. 设置正确的文件权限
4. 配置 HTTPS 证书

### 版本管理
1. 使用 Git 进行版本控制
2. 遵循 Git Flow 工作流
3. 编写清晰的提交信息
4. 定期备份数据库

## 常用命令

### ThinkPHP 命令
```bash
# 启动开发服务器
php think run

# 创建控制器
php think make:controller YourController

# 创建模型
php think make:model YourModel

# 数据库迁移
php think migrate:run

# 清除缓存
php think clear
```

## 故障排除

### 常见问题
1. **路由不生效**: 检查 [route.php](mdc:config/route.php) 配置
2. **数据库连接失败**: 检查 [database.php](mdc:config/database.php) 配置
3. **模板不显示**: 检查视图文件路径和权限
4. **静态资源404**: 检查 public 目录配置

### 调试工具
1. 使用 `dump()` 函数调试变量
2. 查看 `runtime/log/` 目录下的日志文件
3. 开启调试模式查看详细错误信息

## 扩展开发

### 添加新功能模块
1. 创建对应的控制器、模型、视图
2. 配置路由规则
3. 添加数据库迁移文件
4. 编写单元测试

### 页面构建器组件扩展
参考 [页面构建器优化文档.md](mdc:页面构建器优化文档.md) 了解详细的优化计划和实施方案。



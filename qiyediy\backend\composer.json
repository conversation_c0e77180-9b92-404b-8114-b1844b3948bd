{"name": "qiyediy/backend", "description": "QiyeDIY企业建站系统 - 后端API服务", "type": "project", "keywords": ["qiyediy", "cms", "diy", "website builder", "thinkphp"], "homepage": "https://www.qiyediy.com", "license": "MIT", "authors": [{"name": "韩总", "email": "<EMAIL>", "homepage": "https://www.qiyediy.com", "role": "Developer"}], "require": {"php": ">=8.1", "topthink/framework": "^8.0", "topthink/think-orm": "^3.0", "topthink/think-filesystem": "^2.0", "topthink/think-cache": "^3.0", "topthink/think-queue": "^3.0", "topthink/think-captcha": "^3.0", "topthink/think-migration": "^3.1", "firebase/php-jwt": "^6.8", "intervention/image": "^3.5", "league/flysystem": "^3.0", "league/flysystem-aws-s3-v3": "^3.0", "predis/predis": "^2.2", "vlucas/phpdotenv": "^5.5", "monolog/monolog": "^3.4", "guzzlehttp/guzzle": "^7.8", "ramsey/uuid": "^4.7", "nesbot/carbon": "^2.72", "symfony/console": "^6.4", "symfony/var-dumper": "^6.4", "doctrine/dbal": "^3.7", "league/oauth2-server": "^8.5", "spatie/image-optimizer": "^1.7", "endroid/qr-code": "^5.0", "phpoffice/phpspreadsheet": "^1.29"}, "require-dev": {"phpunit/phpunit": "^10.4", "mockery/mockery": "^1.6", "fakerphp/faker": "^1.23", "phpstan/phpstan": "^1.10", "squizlabs/php_codesniffer": "^3.7", "friendsofphp/php-cs-fixer": "^3.40", "pestphp/pest": "^2.24", "nunomaduro/collision": "^7.10"}, "autoload": {"psr-4": {"app\\": "app/"}, "files": ["app/common.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "scripts": {"post-autoload-dump": ["@php think clear --cache", "@php think clear --log"], "test": "pest", "test-coverage": "pest --coverage", "phpstan": "phpstan analyse", "cs-fix": "php-cs-fixer fix", "cs-check": "php-cs-fixer fix --dry-run --diff", "phpcs": "phpcs", "phpcbf": "phpcbf"}, "extra": {"think": {"app_namespace": "app", "app_debug": true, "app_trace": false}}, "minimum-stability": "stable", "prefer-stable": true}
<?php

namespace app\admin\controller;
use app\admin\controller\Base as BaseController;
use app\model\PageTemplate;
use think\facade\Request;
use think\facade\Session;

/**
 * 页面设计器控制器
 */
class PageDesigner extends BaseController
{
    /**
     * DIY设计器页面
     */
    public function diy()
    {
        $id = Request::param('id', 0, 'intval');
        
        // 添加调试信息
        error_log("DIY方法被调用，ID: " . $id);
        
        if ($id <= 0) {
            Session::flash('message', '无效的模板ID');
            Session::flash('messageType', 'error');
            return redirect('/admin/page-builder');
        }

        // 获取模板数据
        $template = PageTemplate::find($id);
        if (!$template) {
            Session::flash('message', '模板不存在');
            Session::flash('messageType', 'error');
            return redirect('/admin/page-builder');
        }

        // 处理POST请求
        if (Request::isPost()) {
            return $this->handlePost();
        }

        // 添加调试信息
        error_log("返回DIY模板视图，模板名称: " . $template->name);

        return view('admin/page_designer_diy', [
            'template' => $template,
            'message' => Session::pull('message') ?: '',
            'messageType' => Session::pull('messageType') ?: 'info'
        ]);
    }

    /**
     * 处理POST请求
     */
    private function handlePost()
    {
        $action = Request::param('action');

        switch ($action) {
            case 'save_design':
                return $this->saveDesign();
            case 'load_design':
                return $this->loadDesign();
            case 'clear_template':
                return $this->clearTemplate();
            case 'clear_cache':
                return $this->clearAllCache();
            default:
                return json(['success' => false, 'message' => '无效的操作']);
        }
    }

    /**
     * 保存设计配置
     */
    private function saveDesign()
    {
        $id = Request::param('id', 0, 'intval');
        $config = Request::param('config', '');
        $html = Request::param('html', '');
        $css = Request::param('css', '');
        $name = Request::param('name', ''); // 接收模板标题

        if ($id <= 0) {
            return json(['success' => false, 'message' => '无效的模板ID']);
        }

        try {
            $template = PageTemplate::find($id);
            if (!$template) {
                return json(['success' => false, 'message' => '模板不存在']);
            }

            $updateData = [
                'config' => $config,
                'html_content' => $html,
                'css_content' => $css,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // 如果提供了模板标题，则更新
            if (!empty($name)) {
                $updateData['name'] = trim($name);
            }

            $result = $template->save($updateData);

            if ($result) {
                // 清除相关缓存
                $this->clearPageCache($template->slug);

                return json(['success' => true, 'message' => '保存成功']);
            } else {
                return json(['success' => false, 'message' => '保存失败']);
            }
        } catch (\Exception $e) {
            return json(['success' => false, 'message' => '保存失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 加载设计配置
     */
    private function loadDesign()
    {
        $id = Request::param('id', 0, 'intval');

        if ($id <= 0) {
            return json(['success' => false, 'message' => '无效的模板ID']);
        }

        try {
            $template = PageTemplate::find($id);
            if (!$template) {
                return json(['success' => false, 'message' => '模板不存在']);
            }

            return json([
                'success' => true,
                'data' => [
                    'config' => $template->config ?: '',
                    'html' => $template->html_content ?: '',
                    'css' => $template->css_content ?: ''
                ]
            ]);
        } catch (\Exception $e) {
            return json(['success' => false, 'message' => '加载失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 清空模板数据
     */
    private function clearTemplate()
    {
        $id = Request::param('id', 0, 'intval');

        if ($id <= 0) {
            return json(['success' => false, 'message' => '无效的模板ID']);
        }

        try {
            $template = PageTemplate::find($id);
            if (!$template) {
                return json(['success' => false, 'message' => '模板不存在']);
            }

            // 清空模板的所有设计数据
            $updateData = [
                'config' => '',
                'html_content' => '',
                'css_content' => '',
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $result = $template->save($updateData);

            if ($result) {
                return json(['success' => true, 'message' => '模板数据已清空']);
            } else {
                return json(['success' => false, 'message' => '清空失败']);
            }
        } catch (\Exception $e) {
            return json(['success' => false, 'message' => '清空失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 预览页面
     */
    public function preview()
    {
        $id = Request::param('id', 0, 'intval');

        if ($id <= 0) {
            Session::flash('message', '无效的模板ID');
            Session::flash('messageType', 'error');
            return redirect('/admin/page-builder');
        }

        // 获取模板数据
        $template = PageTemplate::find($id);
        if (!$template) {
            Session::flash('message', '模板不存在');
            Session::flash('messageType', 'error');
            return redirect('/admin/page-builder');
        }

        return view('admin/page_preview', [
            'template' => $template
        ]);
    }

    /**
     * 清除页面相关缓存
     */
    private function clearPageCache($slug = null)
    {
        try {
            // 清除页面列表缓存
            \think\facade\Cache::delete('diy_pages_list');
            \think\facade\Cache::delete('diy_page_stats');

            // 清除按类型缓存
            $types = ['page', 'home', 'product', 'news', 'case', 'contact', 'about'];
            foreach ($types as $type) {
                \think\facade\Cache::delete('diy_pages_by_type_' . $type);
            }
            \think\facade\Cache::delete('diy_pages_by_type_all');

            // 如果指定了slug，清除该页面的详情缓存
            if ($slug) {
                \think\facade\Cache::delete('diy_page_detail_' . $slug);
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 清除所有页面缓存
     */
    private function clearAllCache()
    {
        try {
            // 清除所有页面相关缓存
            $this->clearPageCache();

            // 清除所有页面详情缓存
            $slugs = PageTemplate::where('status', 1)->column('slug');
            foreach ($slugs as $slug) {
                \think\facade\Cache::delete('diy_page_detail_' . $slug);
            }

            return json(['success' => true, 'message' => '缓存清除成功']);
        } catch (\Exception $e) {
            return json(['success' => false, 'message' => '清除缓存失败：' . $e->getMessage()]);
        }
    }
}

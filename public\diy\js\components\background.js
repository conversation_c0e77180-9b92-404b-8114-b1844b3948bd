/**
 * 背景组件
 * 用于设置页面背景，支持渐变和图片背景
 */

// 背景组件模板
const backgroundComponent = {
    name: '背景图',
    html: `<div class="bg-component">
        <div class="bg-overlay"></div>
        <div class="bg-content">
            <h2>背景区域</h2>
            <p>这里是背景内容区域</p>
        </div>
    </div>`,
    properties: {
        bgType: 'none',
        bgValue: 'transparent',
        bgImages: [
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80',
            'https://images.unsplash.com/photo-1519904981063-b0cf448d479e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80',
            'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80',
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80',
            'https://images.unsplash.com/photo-1519904981063-b0cf448d479e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80'
        ],
        selectedBgImage: 0,
        effect3D: 'none',
        overlayOpacity: 0.3
    }
};

// 背景样式预设
const backgroundStyles = [
    { name: '紫蓝渐变', value: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' },
    { name: '蓝青渐变', value: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)' },
    { name: '粉红渐变', value: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)' },
    { name: '青绿渐变', value: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)' },
    { name: '橙黄渐变', value: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)' },
    { name: '粉紫渐变', value: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)' },
    { name: '深蓝渐变', value: 'linear-gradient(135deg, #2196F3 0%, #21CBF3 100%)' },
    { name: '夜空渐变', value: 'linear-gradient(135deg, #0c3483 0%, #a2b6df 100%)' },
    { name: '日落渐变', value: 'linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%)' },
    { name: '科技蓝', value: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)' },
    { name: '薄荷绿', value: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)' },
    { name: '暗夜红', value: 'linear-gradient(135deg, #d53369 0%, #daae51 100%)' }
];

// 生成背景组件属性面板
function generateBackgroundProperties(component) {
    const props = backgroundComponent.properties;

    let html = `
        <!-- 背景类型设置 -->
        <div class="property-section">
            <h4 class="section-title">背景类型</h4>

            <div class="property-group bg-setting-group">
                <label class="property-label">选择背景类型</label>
                <div class="input-group">
                    <select class="property-input" onchange="changeBgType('${component.id}', this.value)">
                        <option value="none" ${props.bgType === 'none' ? 'selected' : ''}>无背景</option>
                        <option value="gradient" ${props.bgType === 'gradient' ? 'selected' : ''}>渐变背景</option>
                        <option value="image" ${props.bgType === 'image' ? 'selected' : ''}>图片背景</option>
                    </select>
                </div>
            </div>
        </div>
    `;

    if (props.bgType === 'gradient') {
        html += `
            <!-- 渐变设置 -->
            <div class="property-section">
                <h4 class="section-title">渐变设置</h4>

                <div class="property-group bg-setting-group">
                    <label class="property-label">预设渐变样式</label>
                    <div class="bg-options">
                        ${backgroundStyles.map((style, index) => `
                            <div class="bg-option ${props.bgValue === style.value ? 'selected' : ''}"
                                 style="background: ${style.value}"
                                 onclick="updateBgValue('${component.id}', \`${style.value}\`)"
                                 title="${style.name}">
                            </div>
                        `).join('')}
                    </div>
                    <div class="bg-preview" style="background: ${props.bgValue}"></div>
                </div>
            </div>
        `;
    } else if (props.bgType === 'image') {
        html += `
            <!-- 图片设置 -->
            <div class="property-section">
                <h4 class="section-title">图片设置</h4>

                <div class="property-group bg-setting-group">
                    <label class="property-label">背景图片选择</label>
                    <div class="bg-options">
                        ${props.bgImages.map((image, index) => `
                            <div class="bg-option ${props.selectedBgImage === index ? 'selected' : ''}"
                                 style="background: url('${image}') center/cover"
                                 onclick="updateBgImage('${component.id}', ${index})"
                                 title="背景图片 ${index + 1}">
                            </div>
                        `).join('')}
                    </div>
                    <div class="bg-preview" style="background: url('${props.bgImages[props.selectedBgImage]}') center/cover"></div>
                </div>
            </div>
        `;
    } else if (props.bgType === 'none') {
        html += `
            <!-- 无背景提示 -->
            <div class="property-section">
                <h4 class="section-title">背景设置</h4>
                <div class="no-background-tip">
                    <p>🎨 当前选择无背景</p>
                    <p>页面将显示为透明背景</p>
                    <p>可以选择渐变或图片背景来美化页面</p>
                </div>
            </div>
        `;
    }

    // 3D效果设置（仅在有背景时显示）
    if (props.bgType !== 'none') {
        html += `
            <!-- 3D效果设置 -->
            <div class="property-section">
                <h4 class="section-title">3D效果</h4>

                <div class="property-group bg-setting-group">
                    <label class="property-label">动态效果</label>
                    <div class="input-group">
                        <select class="property-input" onchange="updateBgProperty('${component.id}', 'effect3D', this.value)">
                            <option value="none" ${props.effect3D === 'none' ? 'selected' : ''}>无效果</option>
                            <option value="bubbles" ${props.effect3D === 'bubbles' ? 'selected' : ''}>气泡效果</option>
                            <option value="particles" ${props.effect3D === 'particles' ? 'selected' : ''}>粒子效果</option>
                            <option value="geometric" ${props.effect3D === 'geometric' ? 'selected' : ''}>几何图形</option>
                            <option value="stars" ${props.effect3D === 'stars' ? 'selected' : ''}>星空效果</option>
                            <option value="waves" ${props.effect3D === 'waves' ? 'selected' : ''}>波浪效果</option>
                        </select>
                    </div>
                </div>
            </div>
        `;
    }

    return html;
}

// 背景类型切换
function changeBgType(componentId, type) {
    const component = document.getElementById(componentId);
    const props = backgroundComponent.properties;

    props.bgType = type;

    if (type === 'image') {
        props.bgValue = props.bgImages[props.selectedBgImage];
    } else if (type === 'gradient') {
        props.bgValue = backgroundStyles[0].value;
    } else if (type === 'none') {
        props.bgValue = 'transparent';
        props.effect3D = 'none'; // 无背景时清除3D效果
    }

    if (component) {
        updateBackgroundDisplay(component, props);
        updatePropertiesPanel(component);
    } else if (componentId === 'background-layer') {
        // 对于背景层，只更新属性面板
        updateBackgroundProperties();
    }
}

// 更新背景值
function updateBgValue(componentId, bgValue) {
    const component = document.getElementById(componentId);
    const props = backgroundComponent.properties;

    props.bgValue = bgValue;

    if (component) {
        updateBackgroundDisplay(component, props);
        updatePropertiesPanel(component);
    } else if (componentId === 'background-layer') {
        // 对于背景层，只更新属性面板
        updateBackgroundProperties();
    }
}

// 更新背景图片
function updateBgImage(componentId, imageIndex) {
    const component = document.getElementById(componentId);
    const props = backgroundComponent.properties;

    props.selectedBgImage = imageIndex;
    props.bgValue = props.bgImages[imageIndex];

    if (component) {
        updateBackgroundDisplay(component, props);
        updatePropertiesPanel(component);
    } else if (componentId === 'background-layer') {
        // 对于背景层，只更新属性面板
        updateBackgroundProperties();
    }
}

// 更新背景属性
function updateBgProperty(componentId, property, value) {
    const component = document.getElementById(componentId);
    const props = backgroundComponent.properties;

    props[property] = value;

    if (component) {
        updateBackgroundDisplay(component, props);
        if (property === 'effect3D') {
            updatePropertiesPanel(component);
        }
    } else if (componentId === 'background-layer') {
        // 对于背景层，只更新属性面板
        if (property === 'effect3D') {
            updateBackgroundProperties();
        }
    }
}

// 更新背景显示
function updateBackgroundDisplay(component, props) {
    if (!component) return;
    const contentDiv = component.querySelector('.bg-component');
    if (!contentDiv) return;

    if (props.bgType === 'image') {
        contentDiv.style.backgroundImage = `url('${props.bgValue}')`;
        contentDiv.style.backgroundSize = 'cover';
        contentDiv.style.backgroundPosition = 'center';
        contentDiv.style.backgroundRepeat = 'no-repeat';
        contentDiv.style.background = '';
        contentDiv.classList.add('image-bg');
        contentDiv.classList.remove('no-bg');
    } else if (props.bgType === 'gradient') {
        contentDiv.style.backgroundImage = '';
        contentDiv.style.background = props.bgValue;
        contentDiv.classList.remove('image-bg', 'no-bg');
    } else if (props.bgType === 'none') {
        contentDiv.style.backgroundImage = '';
        contentDiv.style.background = 'transparent';
        contentDiv.classList.remove('image-bg');
        contentDiv.classList.add('no-bg');
    }
}

// 调用外部函数（在all.js中定义）
function updateBackgroundProperties() {
    // 延迟调用，确保all.js已加载
    setTimeout(() => {
        if (typeof window.updateBackgroundProperties === 'function') {
            window.updateBackgroundProperties();
        } else {
            // 直接调用全局函数
            const propertiesContent = document.getElementById('properties-content');
            if (propertiesContent && typeof ComponentManager !== 'undefined' && ComponentManager.isRegistered('background')) {
                const mockComponent = { id: 'background-layer', dataset: { type: 'background' } };
                let html = `
                    <div class="property-group">
                        <label class="property-label">组件类型</label>
                        <input type="text" class="property-input" value="背景层" readonly>
                    </div>
                `;
                html += ComponentManager.generateProperties('background', mockComponent);
                propertiesContent.innerHTML = html;
            }
        }
    }, 0);
}

// 注册背景组件
if (typeof ComponentManager !== 'undefined') {
    ComponentManager.register('background', backgroundComponent, generateBackgroundProperties, updateBackgroundDisplay);
}

// 初始化日志
console.log('🎨 背景组件已加载');

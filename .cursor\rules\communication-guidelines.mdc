---
description:
globs:
alwaysApply: false
---
# Communication Guidelines

## Role-Based Communication

### When Acting as Planner
- **Ask for clarification** if user doesn't specify which mode to use
- **Present comprehensive analysis** before execution
- **Create user-reviewable plans** in [.cursor/scratchpad.md](mdc:.cursor/scratchpad.md)
- **Break down tasks** into small, clear, efficient steps
- **Define success criteria** for each task
- **Wait for user approval** before switching to execution mode

### When Acting as Executor  
- **Report progress** at appropriate milestones
- **Ask for help** when encountering blockers
- **Update status** in [.cursor/scratchpad.md](mdc:.cursor/scratchpad.md) after completing tasks
- **Request Planner review** before major changes
- **Notify user** when tasks are complete and ready for manual testing

## Information Gathering

### External Information Needs
- **Tell user the purpose** of information searches
- **Explain expected results** from documentation or function searches
- **Don't make assumptions** about technical details user can't validate
- **Ask specific questions** rather than guessing

### Technical Uncertainty
- **Don't answer uncertain questions** - users can't judge if you're on wrong path
- **Admit knowledge gaps** and ask for guidance
- **Provide options** when multiple approaches are viable
- **Explain trade-offs** of different solutions

## Progress Communication

### Milestone Reporting
- **Describe what was accomplished** in clear terms
- **Include test results** and validation status
- **Request manual testing** before marking tasks complete
- **Update project status** transparently

### Problem Reporting
- **Describe the issue** clearly and concisely
- **Explain attempted solutions** and their results
- **Request specific help** or guidance needed
- **Suggest next steps** if possible

## Documentation Updates

### Scratchpad Maintenance
- **Add new sections** rather than rewriting existing content
- **Mark outdated information** instead of deleting
- **Preserve other role's records** and contributions
- **Use clear section headers** and consistent formatting

### Lessons Learned
- **Record reusable information** especially error solutions
- **Include library versions** and model names when relevant
- **Document workarounds** for common issues
- **Share debugging techniques** that proved effective

## User Interaction Principles

- **Respect user's technical level** - don't assume deep technical knowledge
- **Provide context** for technical decisions and recommendations
- **Use clear, non-technical language** when explaining complex concepts
- **Focus on practical outcomes** rather than implementation details
- **Be honest about limitations** and potential risks

<!--
  三只鱼网络科技 | 韩总 | 2024-12-20
  QiyeDIY企业建站系统 - 应用头部组件
-->

<template>
  <header class="app-header">
    <nav class="navbar">
      <div class="container mx-auto px-4">
        <div class="navbar-content">
          <!-- Logo -->
          <div class="navbar-brand">
            <NuxtLink to="/" class="brand-link">
              <img src="/images/logo.png" alt="QiyeDIY" class="brand-logo" />
              <span class="brand-text">QiyeDIY</span>
            </NuxtLink>
          </div>

          <!-- 导航菜单 -->
          <div class="navbar-menu" :class="{ 'is-active': mobileMenuOpen }">
            <div class="navbar-nav">
              <NuxtLink to="/" class="nav-link" @click="closeMobileMenu">
                首页
              </NuxtLink>
              <NuxtLink to="/templates" class="nav-link" @click="closeMobileMenu">
                模板中心
              </NuxtLink>
              <NuxtLink to="/features" class="nav-link" @click="closeMobileMenu">
                功能特色
              </NuxtLink>
              <NuxtLink to="/pricing" class="nav-link" @click="closeMobileMenu">
                价格方案
              </NuxtLink>
              <NuxtLink to="/cases" class="nav-link" @click="closeMobileMenu">
                成功案例
              </NuxtLink>
              <NuxtLink to="/help" class="nav-link" @click="closeMobileMenu">
                帮助中心
              </NuxtLink>
            </div>

            <!-- 用户操作 -->
            <div class="navbar-actions">
              <template v-if="!userStore.isLoggedIn">
                <NuxtLink to="/login" class="btn btn-outline btn-sm" @click="closeMobileMenu">
                  登录
                </NuxtLink>
                <NuxtLink to="/register" class="btn btn-primary btn-sm" @click="closeMobileMenu">
                  免费注册
                </NuxtLink>
              </template>
              
              <template v-else>
                <div class="user-dropdown" ref="userDropdownRef">
                  <button 
                    class="user-trigger"
                    @click="toggleUserDropdown"
                  >
                    <img 
                      :src="userStore.user?.avatar || '/images/default-avatar.png'" 
                      :alt="userStore.user?.username"
                      class="user-avatar"
                    />
                    <span class="user-name">{{ userStore.user?.username }}</span>
                    <Icon name="heroicons:chevron-down" class="dropdown-icon" />
                  </button>
                  
                  <div class="dropdown-menu" :class="{ 'is-active': userDropdownOpen }">
                    <NuxtLink to="/dashboard" class="dropdown-item" @click="closeUserDropdown">
                      <Icon name="heroicons:squares-2x2" />
                      我的工作台
                    </NuxtLink>
                    <NuxtLink to="/my-sites" class="dropdown-item" @click="closeUserDropdown">
                      <Icon name="heroicons:globe-alt" />
                      我的网站
                    </NuxtLink>
                    <NuxtLink to="/profile" class="dropdown-item" @click="closeUserDropdown">
                      <Icon name="heroicons:user" />
                      个人资料
                    </NuxtLink>
                    <NuxtLink to="/settings" class="dropdown-item" @click="closeUserDropdown">
                      <Icon name="heroicons:cog-6-tooth" />
                      账户设置
                    </NuxtLink>
                    <div class="dropdown-divider"></div>
                    <button class="dropdown-item" @click="handleLogout">
                      <Icon name="heroicons:arrow-right-on-rectangle" />
                      退出登录
                    </button>
                  </div>
                </div>
              </template>
            </div>
          </div>

          <!-- 移动端菜单按钮 -->
          <button 
            class="mobile-menu-btn"
            @click="toggleMobileMenu"
            :class="{ 'is-active': mobileMenuOpen }"
          >
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
          </button>
        </div>
      </div>
    </nav>
  </header>
</template>

<script setup lang="ts">
import { useUserStore } from '~/stores/user'

const userStore = useUserStore()

// 响应式数据
const mobileMenuOpen = ref(false)
const userDropdownOpen = ref(false)
const userDropdownRef = ref<HTMLElement>()

/**
 * 切换移动端菜单
 */
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

/**
 * 关闭移动端菜单
 */
const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}

/**
 * 切换用户下拉菜单
 */
const toggleUserDropdown = () => {
  userDropdownOpen.value = !userDropdownOpen.value
}

/**
 * 关闭用户下拉菜单
 */
const closeUserDropdown = () => {
  userDropdownOpen.value = false
}

/**
 * 处理退出登录
 */
const handleLogout = async () => {
  try {
    await userStore.logout()
    closeUserDropdown()
    await navigateTo('/login')
    ElMessage.success('退出登录成功')
  } catch (error) {
    console.error('退出登录失败:', error)
  }
}

/**
 * 点击外部关闭下拉菜单
 */
const handleClickOutside = (event: Event) => {
  if (userDropdownRef.value && !userDropdownRef.value.contains(event.target as Node)) {
    closeUserDropdown()
  }
}

// 监听点击外部事件
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 监听路由变化，关闭菜单
watch(() => useRoute().path, () => {
  closeMobileMenu()
  closeUserDropdown()
})
</script>

<style lang="scss" scoped>
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.navbar {
  height: 70px;
  display: flex;
  align-items: center;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

// Logo样式
.navbar-brand {
  .brand-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #1f2937;
    font-weight: 700;
    font-size: 1.5rem;
  }
  
  .brand-logo {
    height: 40px;
    margin-right: 12px;
  }
  
  .brand-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

// 导航菜单样式
.navbar-menu {
  display: flex;
  align-items: center;
  gap: 2rem;
  
  @media (max-width: 768px) {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background: white;
    flex-direction: column;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    
    &.is-active {
      transform: translateY(0);
      opacity: 1;
      visibility: visible;
    }
  }
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: 2rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
  }
}

.nav-link {
  color: #4b5563;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  
  &:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
  }
  
  &.router-link-active {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
  }
}

// 用户操作样式
.navbar-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
    width: 100%;
  }
}

// 用户下拉菜单样式
.user-dropdown {
  position: relative;
}

.user-trigger {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: transparent;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(102, 126, 234, 0.1);
  }
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  font-weight: 500;
  color: #1f2937;
}

.dropdown-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
  transition: transform 0.3s ease;
  
  .user-dropdown.is-active & {
    transform: rotate(180deg);
  }
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  min-width: 200px;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0.5rem 0;
  transform: translateY(-10px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  
  &.is-active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem 1rem;
  color: #374151;
  text-decoration: none;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
  }
  
  svg {
    width: 16px;
    height: 16px;
  }
}

.dropdown-divider {
  height: 1px;
  background: rgba(0, 0, 0, 0.1);
  margin: 0.5rem 0;
}

// 移动端菜单按钮样式
.mobile-menu-btn {
  display: none;
  flex-direction: column;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  
  @media (max-width: 768px) {
    display: flex;
  }
}

.hamburger-line {
  width: 24px;
  height: 2px;
  background: #374151;
  margin: 2px 0;
  transition: all 0.3s ease;
  transform-origin: center;
  
  .mobile-menu-btn.is-active & {
    &:nth-child(1) {
      transform: rotate(45deg) translate(5px, 5px);
    }
    
    &:nth-child(2) {
      opacity: 0;
    }
    
    &:nth-child(3) {
      transform: rotate(-45deg) translate(7px, -6px);
    }
  }
}

// 按钮样式
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  
  &.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    }
  }
  
  &.btn-outline {
    border-color: #667eea;
    color: #667eea;
    
    &:hover {
      background: #667eea;
      color: white;
    }
  }
  
  &.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
  
  @media (max-width: 768px) {
    width: 100%;
    justify-content: center;
  }
}

// 滚动时的样式变化
.app-header.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
</style>

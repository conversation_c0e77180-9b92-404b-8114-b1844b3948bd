<?php
declare (strict_types = 1);

namespace app\controller;

use app\BaseController;
use app\service\ConfigService;
use app\validate\SecurityValidate;
use think\Request;
use think\facade\Db;
use think\facade\Validate;
use think\facade\Session;
use think\facade\Log;
use think\facade\Cache;

class Contact extends BaseController
{
    /**
     * 联系我们页面
     */
    public function index()
    {
        return view('index/contact', [
            'pageData' => [
                'pageTitle' => '联系我们',
                'pageDescription' => '联系我们获取专业的技术支持和解决方案',
                'bodyClass' => 'contact-page'
            ]
        ]);
    }
    
    /**
     * 处理联系表单提交
     */
    public function submit(Request $request)
    {
        // 设置响应头
        header('Content-Type: application/json; charset=utf-8');

        if (!$request->isPost()) {
            return json(['success' => false, 'message' => '不支持的请求方法']);
        }

        $data = $request->post();

        // 数据安全验证 - 使用统一安全验证器
        $securityCheck = SecurityValidate::validateDataSecurity($data, [
            'name' => 'checkXss',
            'email' => 'checkEmailSafe',
            'phone' => 'checkUsernameSafe',
            'company' => 'checkXss',
            'subject' => 'checkXss',
            'message' => 'checkSqlInjection|checkXss',
        ]);

        if (!$securityCheck['valid']) {
            $errors = [];
            foreach ($securityCheck['errors'] as $field => $fieldErrors) {
                $errors[] = implode(', ', $fieldErrors);
            }
            Log::warning("联系表单安全检查失败 - IP: {$request->ip()}, 错误: " . implode('; ', $errors));
            return json([
                'success' => false,
                'message' => '输入内容包含不安全字符，请检查后重新提交'
            ]);
        }

        // 基础数据验证
        $validate = Validate::rule([
            'name'    => 'require|max:50',
            'email'   => 'require|email|max:100',
            'phone'   => 'regex:/^1[3-9]\d{9}$/',
            'company' => 'max:100',
            'subject' => 'require|max:200',
            'message' => 'require|max:2000'
        ])->message([
            'name.require'    => '姓名不能为空',
            'name.max'        => '姓名不能超过50个字符',
            'email.require'   => '邮箱不能为空',
            'email.email'     => '邮箱格式不正确',
            'email.max'       => '邮箱不能超过100个字符',
            'phone.regex'     => '手机号格式不正确',
            'company.max'     => '公司名称不能超过100个字符',
            'subject.require' => '主题不能为空',
            'subject.max'     => '主题不能超过200个字符',
            'message.require' => '留言内容不能为空',
            'message.max'     => '留言内容不能超过2000个字符'
        ]);

        if (!$validate->check($data)) {
            return json(['success' => false, 'message' => $validate->getError()]);
        }

        // 增强的垃圾邮件检测
        $spamCheckResult = $this->advancedSpamDetection($data);
        if ($spamCheckResult['isSpam']) {
            Log::warning("垃圾邮件检测 - IP: {$request->ip()}, 原因: {$spamCheckResult['reason']}");
            return json(['success' => false, 'message' => '消息包含不当内容，请检查后重新提交']);
        }

        // 增强的频率限制检查
        $clientIP = $request->ip();
        $rateLimitResult = $this->checkRateLimit($clientIP, $data['email']);
        if (!$rateLimitResult['valid']) {
            Log::warning("频率限制 - IP: {$clientIP}, 邮箱: {$data['email']}, 原因: {$rateLimitResult['message']}");
            return json(['success' => false, 'message' => $rateLimitResult['message']]);
        }

        try {
            $currentTime = time();

            // 保存到数据库
            $contactData = [
                'name'       => $data['name'],
                'email'      => $data['email'],
                'phone'      => $data['phone'] ?? '',
                'company'    => $data['company'] ?? '',
                'subject'    => $data['subject'],
                'message'    => $data['message'],
                'ip_address' => $clientIP,
                'user_agent' => $request->header('user-agent', ''),
                'status'     => 'new', // new-新提交
                'created_at' => date('Y-m-d H:i:s', $currentTime),
                'updated_at' => date('Y-m-d H:i:s', $currentTime)
            ];

            $result = Db::name('contact_forms')->insert($contactData);

            if ($result) {
                // 记录提交时间
                $this->updateRateLimitCache($clientIP, $data['email'], $currentTime);

                // 发送邮件通知管理员
                $emailSent = $this->sendNotificationEmail($contactData);

                // 记录日志
                Log::info("新的联系表单提交 - 姓名: {$data['name']}, 邮箱: {$data['email']}, IP: {$clientIP}");

                return json([
                    'success' => true,
                    'message' => '感谢您的留言！我们会尽快与您联系。',
                    'email_sent' => $emailSent
                ]);

            } else {
                throw new \Exception('数据库插入失败');
            }

        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('联系表单提交失败: ' . $e->getMessage());

            return json([
                'success' => false,
                'message' => '系统错误，请稍后重试或直接联系我们'
            ]);
        }
    }
    
    /**
     * 发送邮件通知
     */
    private function sendNotificationEmail($data)
    {
        try {
            $adminEmail = config('app.admin_email', '<EMAIL>');
            $siteName = ConfigService::get('site_name', '三只鱼网络');

            $emailSubject = "【{$siteName}】新的联系表单提交";
            $emailMessage = "
            <html>
            <head>
                <meta charset='UTF-8'>
                <title>新的联系表单提交</title>
            </head>
            <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                <div style='max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;'>
                    <h2 style='color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 10px;'>新的联系表单提交</h2>

                    <table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>
                        <tr>
                            <td style='padding: 10px; border-bottom: 1px solid #eee; font-weight: bold; width: 120px;'>姓名：</td>
                            <td style='padding: 10px; border-bottom: 1px solid #eee;'>" . htmlspecialchars($data['name']) . "</td>
                        </tr>
                        <tr>
                            <td style='padding: 10px; border-bottom: 1px solid #eee; font-weight: bold;'>邮箱：</td>
                            <td style='padding: 10px; border-bottom: 1px solid #eee;'>" . htmlspecialchars($data['email']) . "</td>
                        </tr>";

            if (!empty($data['phone'])) {
                $emailMessage .= "
                        <tr>
                            <td style='padding: 10px; border-bottom: 1px solid #eee; font-weight: bold;'>电话：</td>
                            <td style='padding: 10px; border-bottom: 1px solid #eee;'>" . htmlspecialchars($data['phone']) . "</td>
                        </tr>";
            }

            if (!empty($data['company'])) {
                $emailMessage .= "
                        <tr>
                            <td style='padding: 10px; border-bottom: 1px solid #eee; font-weight: bold;'>公司：</td>
                            <td style='padding: 10px; border-bottom: 1px solid #eee;'>" . htmlspecialchars($data['company']) . "</td>
                        </tr>";
            }

            $emailMessage .= "
                        <tr>
                            <td style='padding: 10px; border-bottom: 1px solid #eee; font-weight: bold;'>主题：</td>
                            <td style='padding: 10px; border-bottom: 1px solid #eee;'>" . htmlspecialchars($data['subject']) . "</td>
                        </tr>
                        <tr>
                            <td style='padding: 10px; border-bottom: 1px solid #eee; font-weight: bold; vertical-align: top;'>留言：</td>
                            <td style='padding: 10px; border-bottom: 1px solid #eee;'>" . nl2br(htmlspecialchars($data['message'])) . "</td>
                        </tr>
                        <tr>
                            <td style='padding: 10px; border-bottom: 1px solid #eee; font-weight: bold;'>IP地址：</td>
                            <td style='padding: 10px; border-bottom: 1px solid #eee;'>" . htmlspecialchars($data['ip_address']) . "</td>
                        </tr>
                        <tr>
                            <td style='padding: 10px; border-bottom: 1px solid #eee; font-weight: bold;'>提交时间：</td>
                            <td style='padding: 10px; border-bottom: 1px solid #eee;'>" . $data['created_at'] . "</td>
                        </tr>
                    </table>

                    <div style='margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;'>
                        <p style='margin: 0; color: #666; font-size: 14px;'>
                            请及时回复客户咨询。您可以登录后台管理系统查看和管理所有联系表单。
                        </p>
                    </div>
                </div>
            </body>
            </html>";

            // 设置邮件头
            $headers = "From: " . $adminEmail . "\r\n";
            $headers .= "Reply-To: " . $adminEmail . "\r\n";
            $headers .= "Content-Type: text/html; charset=UTF-8\r\n";

            // 发送邮件
            $result = mail($adminEmail, $emailSubject, $emailMessage, $headers);

            if ($result) {
                Log::info("邮件通知发送成功 - 收件人: {$adminEmail}");
                return true;
            } else {
                Log::warning("邮件通知发送失败 - 收件人: {$adminEmail}");
                return false;
            }

        } catch (\Exception $e) {
            Log::error('发送邮件通知失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 增强的垃圾邮件检测
     */
    private function advancedSpamDetection($data)
    {
        $spamScore = 0;
        $reasons = [];

        // 检查常见垃圾邮件关键词
        $spamKeywords = [
            'viagra', 'casino', 'lottery', 'winner', 'congratulations',
            'free money', 'click here', 'urgent', 'limited time', 'act now',
            'make money', 'work from home', 'guaranteed', 'risk free'
        ];

        $content = strtolower($data['message'] . ' ' . $data['subject']);
        foreach ($spamKeywords as $keyword) {
            if (strpos($content, $keyword) !== false) {
                $spamScore += 10;
                $reasons[] = "包含垃圾邮件关键词: {$keyword}";
            }
        }

        // 检查重复字符
        if (preg_match('/(.)\1{4,}/', $data['message'])) {
            $spamScore += 5;
            $reasons[] = "包含过多重复字符";
        }

        // 检查链接数量
        $linkCount = preg_match_all('/https?:\/\//', $data['message']);
        if ($linkCount > 3) {
            $spamScore += $linkCount * 2;
            $reasons[] = "包含过多链接: {$linkCount}个";
        }

        // 检查全大写内容
        $uppercaseRatio = strlen(preg_replace('/[^A-Z]/', '', $data['message'])) / strlen($data['message']);
        if ($uppercaseRatio > 0.5) {
            $spamScore += 8;
            $reasons[] = "包含过多大写字母";
        }

        // 检查特殊字符比例
        $specialCharCount = preg_match_all('/[!@#$%^&*()_+=\[\]{}|;:,.<>?]/', $data['message']);
        if ($specialCharCount > 20) {
            $spamScore += 5;
            $reasons[] = "包含过多特殊字符";
        }

        return [
            'isSpam' => $spamScore > 15,
            'score' => $spamScore,
            'reason' => implode(', ', $reasons)
        ];
    }

    /**
     * 增强的频率限制检查
     */
    private function checkRateLimit($clientIP, $email)
    {
        // IP频率限制
        $ipKey = 'contact_ip_' . md5($clientIP);
        $ipCount = Cache::get($ipKey, 0);
        if ($ipCount >= 5) { // 1小时内最多5次
            return ['valid' => false, 'message' => 'IP提交次数过多，请1小时后再试'];
        }

        // 邮箱频率限制
        $emailKey = 'contact_email_' . md5($email);
        $emailCount = Cache::get($emailKey, 0);
        if ($emailCount >= 3) { // 1小时内同一邮箱最多3次
            return ['valid' => false, 'message' => '该邮箱提交次数过多，请1小时后再试'];
        }

        // 短时间频率限制
        $shortTermKey = 'contact_short_' . md5($clientIP);
        $lastSubmitTime = Cache::get($shortTermKey, 0);
        $currentTime = time();
        if ($currentTime - $lastSubmitTime < 60) { // 1分钟内只能提交一次
            return ['valid' => false, 'message' => '提交过于频繁，请1分钟后再试'];
        }

        return ['valid' => true];
    }

    /**
     * 更新频率限制缓存
     */
    private function updateRateLimitCache($clientIP, $email, $currentTime)
    {
        // 更新IP计数
        $ipKey = 'contact_ip_' . md5($clientIP);
        $ipCount = Cache::get($ipKey, 0);
        Cache::set($ipKey, $ipCount + 1, 3600); // 1小时

        // 更新邮箱计数
        $emailKey = 'contact_email_' . md5($email);
        $emailCount = Cache::get($emailKey, 0);
        Cache::set($emailKey, $emailCount + 1, 3600); // 1小时

        // 更新短时间限制
        $shortTermKey = 'contact_short_' . md5($clientIP);
        Cache::set($shortTermKey, $currentTime, 300); // 5分钟
    }
}

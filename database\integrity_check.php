<?php
/**
 * 三只鱼网络科技 | 开发者：韩总 | 创建时间：2024-12-19
 * 文件描述：数据库完整性检查工具 - ThinkPHP6企业级应用
 * 技术栈：PHP 8.0+ + ThinkPHP6 + MySQL + Redis
 * 版权所有：三只鱼网络科技有限公司
 */

require_once __DIR__ . '/../vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

// 初始化ThinkPHP
$app = new \think\App();
$app->initialize();

class DatabaseIntegrityChecker
{
    private $requiredTables = [
        'admin_users' => '管理员表',
        'banners' => '轮播图表',
        'product_categories' => '产品分类表',
        'products' => '产品表',
        'product_attributes' => '产品属性表',
        'solutions' => '解决方案表',
        'news_categories' => '新闻分类表',
        'news' => '新闻表',
        'cases' => '客户案例表',
        'team_members' => '团队成员表',
        'contact_forms' => '联系表单表',
        'image_groups' => '图片分组表',
        'images' => '图片表',
        'page_templates' => 'DIY页面模板表',
        'site_settings' => '网站设置表'
    ];
    
    private $requiredIndexes = [
        'products' => [
            'idx_category_id' => 'category_id',
            'idx_status' => 'status',
            'idx_featured' => 'is_featured',
            'idx_hot' => 'is_hot',
            'idx_new' => 'is_new'
        ],
        'news' => [
            'category_id' => 'category_id'
        ],
        'images' => [
            'idx_group_id' => 'group_id',
            'idx_status' => 'status'
        ]
    ];
    
    private $requiredData = [
        'admin_users' => '管理员账户',
        'product_categories' => '产品分类',
        'news_categories' => '新闻分类',
        'image_groups' => '图片分组',
        'site_settings' => '网站设置'
    ];
    
    /**
     * 执行完整性检查
     */
    public function check()
    {
        echo "=== 三只鱼网络科技 - 数据库完整性检查 ===\n\n";
        
        $issues = [];
        
        try {
            // 1. 检查数据库连接
            $this->checkConnection();
            
            // 2. 检查表结构
            $tableIssues = $this->checkTables();
            $issues = array_merge($issues, $tableIssues);
            
            // 3. 检查索引
            $indexIssues = $this->checkIndexes();
            $issues = array_merge($issues, $indexIssues);
            
            // 4. 检查必要数据
            $dataIssues = $this->checkRequiredData();
            $issues = array_merge($issues, $dataIssues);
            
            // 5. 检查数据一致性
            $consistencyIssues = $this->checkDataConsistency();
            $issues = array_merge($issues, $consistencyIssues);
            
            // 6. 检查外键约束
            $foreignKeyIssues = $this->checkForeignKeys();
            $issues = array_merge($issues, $foreignKeyIssues);
            
            // 7. 生成报告
            $this->generateReport($issues);
            
        } catch (Exception $e) {
            echo "❌ 检查失败: " . $e->getMessage() . "\n";
            return false;
        }
        
        return empty($issues);
    }
    
    /**
     * 检查数据库连接
     */
    private function checkConnection()
    {
        echo "检查数据库连接...\n";
        
        try {
            $result = Db::query('SELECT VERSION() as version');
            $dbConfig = Config::get('database.connections.mysql');
            $info = $result[0];
            echo "✅ 数据库连接正常\n";
            echo "   版本: {$info['version']}\n";
            echo "   数据库: {$dbConfig['database']}\n\n";
        } catch (Exception $e) {
            throw new Exception("数据库连接失败: " . $e->getMessage());
        }
    }
    
    /**
     * 检查表结构
     */
    private function checkTables()
    {
        echo "检查表结构...\n";
        
        $issues = [];
        $existingTables = $this->getExistingTables();
        
        foreach ($this->requiredTables as $table => $description) {
            if (!in_array($table, $existingTables)) {
                $issues[] = "缺少表: {$table} ({$description})";
                echo "❌ 缺少表: {$table}\n";
            } else {
                echo "✅ {$description}\n";
            }
        }
        
        // 检查多余的表
        $extraTables = array_diff($existingTables, array_keys($this->requiredTables));
        foreach ($extraTables as $table) {
            if (!in_array($table, ['migrations'])) { // 忽略系统表
                echo "⚠️  发现额外表: {$table}\n";
            }
        }
        
        echo "\n";
        return $issues;
    }
    
    /**
     * 检查索引
     */
    private function checkIndexes()
    {
        echo "检查索引...\n";
        
        $issues = [];
        
        foreach ($this->requiredIndexes as $table => $indexes) {
            if (!$this->tableExists($table)) {
                continue;
            }
            
            $existingIndexes = $this->getTableIndexes($table);
            
            foreach ($indexes as $indexName => $column) {
                if (!isset($existingIndexes[$indexName])) {
                    $issues[] = "表 {$table} 缺少索引: {$indexName}";
                    echo "❌ 表 {$table} 缺少索引: {$indexName}\n";
                } else {
                    echo "✅ 表 {$table} 索引 {$indexName} 正常\n";
                }
            }
        }
        
        echo "\n";
        return $issues;
    }
    
    /**
     * 检查必要数据
     */
    private function checkRequiredData()
    {
        echo "检查必要数据...\n";
        
        $issues = [];
        
        foreach ($this->requiredData as $table => $description) {
            if (!$this->tableExists($table)) {
                continue;
            }
            
            try {
                $count = Db::table($table)->count();
                if ($count === 0) {
                    $issues[] = "表 {$table} 缺少{$description}数据";
                    echo "❌ 表 {$table} 无数据\n";
                } else {
                    echo "✅ 表 {$table} 有 {$count} 条记录\n";
                }
            } catch (Exception $e) {
                $issues[] = "无法检查表 {$table}: " . $e->getMessage();
                echo "❌ 无法检查表 {$table}\n";
            }
        }
        
        echo "\n";
        return $issues;
    }
    
    /**
     * 检查数据一致性
     */
    private function checkDataConsistency()
    {
        echo "检查数据一致性...\n";
        
        $issues = [];
        
        // 检查产品分类关联
        if ($this->tableExists('products') && $this->tableExists('product_categories')) {
            $orphanProducts = Db::query("
                SELECT COUNT(*) as count 
                FROM products p 
                LEFT JOIN product_categories pc ON p.category_id = pc.id 
                WHERE pc.id IS NULL AND p.category_id > 0
            ");
            
            if ($orphanProducts[0]['count'] > 0) {
                $issues[] = "发现 {$orphanProducts[0]['count']} 个产品的分类不存在";
                echo "❌ 发现孤立的产品记录\n";
            } else {
                echo "✅ 产品分类关联正常\n";
            }
        }
        
        // 检查新闻分类关联
        if ($this->tableExists('news') && $this->tableExists('news_categories')) {
            $orphanNews = Db::query("
                SELECT COUNT(*) as count 
                FROM news n 
                LEFT JOIN news_categories nc ON n.category_id = nc.id 
                WHERE nc.id IS NULL
            ");
            
            if ($orphanNews[0]['count'] > 0) {
                $issues[] = "发现 {$orphanNews[0]['count']} 个新闻的分类不存在";
                echo "❌ 发现孤立的新闻记录\n";
            } else {
                echo "✅ 新闻分类关联正常\n";
            }
        }
        
        // 检查图片分组关联
        if ($this->tableExists('images') && $this->tableExists('image_groups')) {
            $orphanImages = Db::query("
                SELECT COUNT(*) as count 
                FROM images i 
                LEFT JOIN image_groups ig ON i.group_id = ig.id 
                WHERE ig.id IS NULL AND i.group_id IS NOT NULL
            ");
            
            if ($orphanImages[0]['count'] > 0) {
                $issues[] = "发现 {$orphanImages[0]['count']} 个图片的分组不存在";
                echo "❌ 发现孤立的图片记录\n";
            } else {
                echo "✅ 图片分组关联正常\n";
            }
        }
        
        echo "\n";
        return $issues;
    }
    
    /**
     * 检查外键约束
     */
    private function checkForeignKeys()
    {
        echo "检查外键约束...\n";
        
        $issues = [];
        
        try {
            // 获取所有外键约束
            $dbConfig = Config::get('database.connections.mysql');
            $dbName = $dbConfig['database'];
            $foreignKeys = Db::query("
                SELECT
                    TABLE_NAME,
                    COLUMN_NAME,
                    CONSTRAINT_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM information_schema.KEY_COLUMN_USAGE
                WHERE REFERENCED_TABLE_SCHEMA = '{$dbName}'
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ");
            
            if (empty($foreignKeys)) {
                echo "⚠️  未发现外键约束\n";
            } else {
                echo "✅ 发现 " . count($foreignKeys) . " 个外键约束\n";
                foreach ($foreignKeys as $fk) {
                    echo "   {$fk['TABLE_NAME']}.{$fk['COLUMN_NAME']} -> {$fk['REFERENCED_TABLE_NAME']}.{$fk['REFERENCED_COLUMN_NAME']}\n";
                }
            }
            
        } catch (Exception $e) {
            echo "⚠️  无法检查外键约束: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
        return $issues;
    }
    
    /**
     * 生成报告
     */
    private function generateReport($issues)
    {
        echo "=== 检查报告 ===\n";
        
        if (empty($issues)) {
            echo "🎉 数据库完整性检查通过！\n";
            echo "所有表结构、索引和数据都正常。\n";
        } else {
            echo "⚠️  发现 " . count($issues) . " 个问题：\n\n";
            
            foreach ($issues as $i => $issue) {
                echo ($i + 1) . ". {$issue}\n";
            }
            
            echo "\n💡 建议执行以下操作修复问题：\n";
            echo "php database/migrate.php --force\n";
        }
        
        // 统计信息
        $stats = $this->getStatistics();
        echo "\n=== 统计信息 ===\n";
        echo "数据表数量: {$stats['tables']}\n";
        echo "总记录数: {$stats['records']}\n";
        echo "数据库大小: {$stats['size']}\n";
    }
    
    /**
     * 获取现有表列表
     */
    private function getExistingTables()
    {
        $tables = Db::query('SHOW TABLES');
        $dbConfig = Config::get('database.connections.mysql');
        $dbName = $dbConfig['database'];
        $tableKey = "Tables_in_{$dbName}";

        return array_column($tables, $tableKey);
    }
    
    /**
     * 检查表是否存在
     */
    private function tableExists($table)
    {
        try {
            Db::query("SHOW TABLES LIKE '{$table}'");
            $result = Db::query("SHOW TABLES LIKE '{$table}'");
            return !empty($result);
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取表的索引信息
     */
    private function getTableIndexes($table)
    {
        try {
            $indexes = Db::query("SHOW INDEX FROM `{$table}`");
            $result = [];
            
            foreach ($indexes as $index) {
                $result[$index['Key_name']] = $index['Column_name'];
            }
            
            return $result;
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * 获取统计信息
     */
    private function getStatistics()
    {
        $stats = [
            'tables' => 0,
            'records' => 0,
            'size' => 'Unknown'
        ];
        
        try {
            // 表数量
            $tables = $this->getExistingTables();
            $stats['tables'] = count($tables);
            
            // 记录数
            foreach ($tables as $table) {
                try {
                    $count = Db::table($table)->count();
                    $stats['records'] += $count;
                } catch (Exception $e) {
                    // 忽略错误
                }
            }
            
            // 数据库大小
            $dbConfig = Config::get('database.connections.mysql');
            $dbName = $dbConfig['database'];
            $sizeResult = Db::query("
                SELECT
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables
                WHERE table_schema = '{$dbName}'
            ");
            
            if (!empty($sizeResult)) {
                $stats['size'] = $sizeResult[0]['size_mb'] . ' MB';
            }
            
        } catch (Exception $e) {
            // 忽略错误
        }
        
        return $stats;
    }
}

// 命令行执行
if (php_sapi_name() === 'cli') {
    $checker = new DatabaseIntegrityChecker();
    $success = $checker->check();
    
    exit($success ? 0 : 1);
}

<?php
/**
 * 三只鱼网络科技 | 韩总 | 2025-06-10
 * 产品控制器 - ThinkPHP6企业级应用
 */

namespace app\controller;

use app\BaseController;
use think\facade\Request;
use think\facade\View;
use think\facade\Db;
use app\model\Product as ProductModel;
use app\model\ProductCategory;
use app\validate\ProductValidate;
use app\validate\SecurityValidate;
use think\exception\ValidateException;

class Products extends BaseController
{
    /**
     * 产品列表页
     */
    public function index()
    {
        $categorySlug = Request::param('category', '');
        $sort = Request::param('sort', '');
        $page = Request::param('page', 1, 'intval');
        $limit = 12;

        // 构建查询条件
        $where = [['status', '=', 1]];
        $currentCategory = null;

        if (!empty($categorySlug)) {
            $currentCategory = ProductCategory::where('slug', $categorySlug)->where('status', 1)->find();
            if ($currentCategory) {
                $where[] = ['category_id', '=', $currentCategory->id];
            }
        }

        // 查询产品
        $query = ProductModel::with('category')->where($where);

        // 应用排序
        $this->applySorting($query, $sort);

        $products = $query->paginate([
            'list_rows' => $limit,
            'page' => $page,
            'query' => Request::param()
        ]);
        
        // 获取所有分类
        $categories = ProductCategory::getActiveCategories();
        
        // 获取推荐产品
        $featuredProducts = ProductModel::getFeaturedProducts(6);

        $data = [
            'products' => $products,
            'categories' => $categories,
            'currentCategory' => $currentCategory,
            'featuredProducts' => $featuredProducts,
            'pageTitle' => $currentCategory ? $currentCategory->name . ' - 产品中心' : '产品中心',
            'pageDescription' => $currentCategory ? $currentCategory->description : '查看我们的全部产品和服务'
        ];

        // 如果是AJAX请求，只返回产品列表部分
        if (Request::isAjax()) {
            return View::fetch('products/index', $data);
        }

        return View::fetch('products/index', $data);
    }
    
    /**
     * 产品详情页
     */
    public function detail()
    {
        $id = Request::param('id', 0, 'intval');
        $slug = Request::param('slug', '');
        
        // 直接使用原生SQL查询，避免任何缓存问题
        $productData = null;
        
        if ($id > 0) {
            // 通过ID查询
            $result = Db::query("SELECT * FROM products WHERE id = ? AND status = 1", [$id]);
            if (!empty($result)) {
                $productData = $result[0];
            }
        } elseif (!empty($slug)) {
            // 通过slug查询
            $result = Db::query("SELECT * FROM products WHERE slug = ? AND status = 1", [$slug]);
            if (!empty($result)) {
                $productData = $result[0];
            }
        }
        
        // 如果没找到产品，返回404
        if (!$productData) {
            abort(404, '产品不存在');
        }
        
        // 获取分类信息
        $category = null;
        if ($productData['category_id']) {
            $categoryResult = Db::query("SELECT * FROM product_categories WHERE id = ?", [$productData['category_id']]);
            if (!empty($categoryResult)) {
                $category = $categoryResult[0];
            }
        }
        
        // 增加浏览次数
        Db::execute("UPDATE products SET views = views + 1 WHERE id = ?", [$productData['id']]);
        
        // 获取相关产品
        $relatedProducts = [];
        if ($productData['category_id']) {
            $relatedResult = Db::query(
                "SELECT * FROM products WHERE category_id = ? AND id != ? AND status = 1 ORDER BY sort_order DESC, id DESC LIMIT 4",
                [$productData['category_id'], $productData['id']]
            );
            $relatedProducts = $relatedResult;
        }
        
        // 处理JSON字段
        $features = [];
        $specifications = [];
        $gallery = [];
        
        if (!empty($productData['features'])) {
            $features = json_decode($productData['features'], true) ?: [];
        }
        
        if (!empty($productData['specifications'])) {
            $specifications = json_decode($productData['specifications'], true) ?: [];
        }
        
        if (!empty($productData['gallery'])) {
            $gallery = json_decode($productData['gallery'], true) ?: [];
        }
        
        // 库存状态文本
        $stockStatusMap = [
            'in_stock' => '现货',
            'out_of_stock' => '缺货',
            'pre_order' => '预订'
        ];
        $productData['stock_status_text'] = $stockStatusMap[$productData['stock_status']] ?? '现货';
        
        return View::fetch('products/detail', [
            'productDetail' => $productData,  // 使用不同的变量名避免冲突
            'categoryInfo' => $category,      // 使用不同的变量名避免冲突
            'relatedProducts' => $relatedProducts,
            'features' => $features,
            'specifications' => $specifications,
            'gallery' => $gallery,
            'pageTitle' => $productData['meta_title'] ?: $productData['name'],
            'pageDescription' => $productData['meta_description'] ?: $productData['short_description']
        ]);
    }
    
    /**
     * 产品搜索
     */
    public function search()
    {
        $keyword = Request::param('keyword', '');
        $categoryId = Request::param('category_id', 0, 'intval');
        $page = Request::param('page', 1, 'intval');
        $limit = 12;

        if (empty($keyword)) {
            return redirect('/products');
        }

        // 数据安全验证 - 使用统一安全验证器
        $securityCheck = SecurityValidate::validateDataSecurity([
            'keyword' => $keyword,
            'category_id' => $categoryId
        ], [
            'keyword' => 'checkXss',
            'category_id' => 'checkNumberSafe',
        ]);

        if (!$securityCheck['valid']) {
            return redirect('/products')->with('error', '搜索参数包含不安全字符');
        }

        // 输入验证和安全过滤
        $validate = new ProductValidate();
        $searchData = $validate->filterInput([
            'keyword' => $keyword,
            'category_id' => $categoryId
        ]);

        $validationResult = $validate->validateSearch($searchData);
        if ($validationResult !== true) {
            return redirect('/products')->with('error', $validationResult);
        }

        $keyword = $searchData['keyword'];
        $categoryId = $searchData['category_id'];
        
        // 构建搜索条件
        $where = [
            ['status', '=', 1]
        ];
        
        if ($categoryId > 0) {
            $where[] = ['category_id', '=', $categoryId];
        }
        
        // 搜索产品
        $products = ProductModel::with('category')
            ->where($where)
            ->where(function($query) use ($keyword) {
                $query->whereLike('name', '%' . $keyword . '%')
                      ->whereOr('short_description', 'like', '%' . $keyword . '%')
                      ->whereOr('tags', 'like', '%' . $keyword . '%');
            })
            ->order('sort_order', 'desc')
            ->order('id', 'desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page,
                'query' => Request::param()
            ]);
        
        // 获取所有分类
        $categories = ProductCategory::getActiveCategories();
        
        return View::fetch('products/search', [
            'products' => $products,
            'categories' => $categories,
            'keyword' => $keyword,
            'categoryId' => $categoryId,
            'pageTitle' => '搜索结果：' . $keyword,
            'pageDescription' => '搜索关键词：' . $keyword . ' 的产品结果'
        ]);
    }

    /**
     * 应用排序条件
     */
    private function applySorting($query, $sort)
    {
        switch ($sort) {
            case 'price_asc':
                // 价格从低到高：有价格的产品按价格升序，无价格的放最后
                $query->orderRaw('CASE WHEN price > 0 THEN 0 ELSE 1 END ASC')
                      ->order('price', 'asc')
                      ->order('sort_order', 'desc');
                break;
            case 'price_desc':
                // 价格从高到低：有价格的产品按价格降序，无价格的放最后
                $query->orderRaw('CASE WHEN price > 0 THEN 0 ELSE 1 END ASC')
                      ->order('price', 'desc')
                      ->order('sort_order', 'desc');
                break;
            case 'views_desc':
                $query->order('views', 'desc')
                      ->order('sort_order', 'desc');
                break;
            case 'created_desc':
                $query->order('created_at', 'desc')
                      ->order('sort_order', 'desc');
                break;
            case 'name_asc':
                $query->order('name', 'asc')
                      ->order('sort_order', 'desc');
                break;
            case 'name_desc':
                $query->order('name', 'desc')
                      ->order('sort_order', 'desc');
                break;
            default:
                // 默认排序：推荐排序 + ID倒序
                $query->order('sort_order', 'desc')
                      ->order('id', 'desc');
                break;
        }
    }
}

-- 三只鱼网络科技企业官网数据库结构
-- 开发者：韩总
-- 导出时间：2025-06-09 21:14:15
-- 数据库版本：5.7.44
-- 字符集：utf8mb4

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `san` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `san`;

SET FOREIGN_KEY_CHECKS=0;

-- 
DROP TABLE IF EXISTS `admin_users`;
CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `real_name` varchar(50) DEFAULT NULL,
  `role` enum('admin','editor') DEFAULT 'editor',
  `status` tinyint(1) DEFAULT '1',
  `last_login` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

-- 
DROP TABLE IF EXISTS `banners`;
CREATE TABLE `banners` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `subtitle` varchar(300) DEFAULT NULL,
  `description` text COMMENT '轮播图描述',
  `image` varchar(500) NOT NULL,
  `link_url` varchar(500) DEFAULT NULL,
  `link_target` enum('_self','_blank') DEFAULT '_self',
  `sort_order` int(11) DEFAULT '0',
  `status` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4;

-- 
DROP TABLE IF EXISTS `product_categories`;
CREATE TABLE `product_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `description` text,
  `icon` varchar(200) DEFAULT NULL,
  `parent_id` int(11) DEFAULT '0',
  `sort_order` int(11) DEFAULT '0',
  `status` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4;

-- 
DROP TABLE IF EXISTS `products`;
CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL,
  `name` varchar(200) NOT NULL,
  `slug` varchar(200) NOT NULL,
  `short_description` varchar(500) DEFAULT NULL,
  `description` longtext,
  `features` longtext,
  `specifications` longtext COMMENT '产品规格（JSON格式）',
  `image` varchar(500) DEFAULT NULL,
  `icon` varchar(200) DEFAULT NULL COMMENT '产品图标',
  `gallery` text,
  `price` varchar(50) DEFAULT NULL,
  `original_price` varchar(50) DEFAULT NULL COMMENT '原价',
  `stock_status` enum('in_stock','out_of_stock','pre_order') DEFAULT 'in_stock' COMMENT '库存状态',
  `sku` varchar(100) DEFAULT NULL COMMENT '产品SKU',
  `tags` varchar(200) DEFAULT NULL COMMENT '标签',
  `meta_title` varchar(200) DEFAULT NULL COMMENT 'SEO标题',
  `meta_description` varchar(500) DEFAULT NULL COMMENT 'SEO描述',
  `sort_order` int(11) DEFAULT '0',
  `status` tinyint(1) DEFAULT '1',
  `views` int(11) DEFAULT '0',
  `is_featured` tinyint(1) DEFAULT '0' COMMENT '是否推荐',
  `is_hot` tinyint(1) DEFAULT '0' COMMENT '是否热门',
  `is_new` tinyint(1) DEFAULT '0' COMMENT '是否新品',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `category_id` (`category_id`),
  KEY `idx_featured` (`is_featured`),
  KEY `idx_hot` (`is_hot`),
  KEY `idx_new` (`is_new`),
  KEY `idx_sku` (`sku`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_icon` (`icon`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4;

-- 产品属性表
DROP TABLE IF EXISTS `product_attributes`;
CREATE TABLE `product_attributes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `attribute_name` varchar(100) NOT NULL COMMENT '属性名称',
  `attribute_value` varchar(500) NOT NULL COMMENT '属性值',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品属性表';

-- 
DROP TABLE IF EXISTS `solutions`;
CREATE TABLE `solutions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(200) NOT NULL,
  `slug` varchar(200) NOT NULL,
  `short_description` varchar(500) DEFAULT NULL,
  `description` longtext,
  `icon` varchar(200) DEFAULT NULL,
  `image` varchar(500) DEFAULT NULL,
  `features` longtext,
  `sort_order` int(11) DEFAULT '0',
  `status` tinyint(1) DEFAULT '1',
  `views` int(11) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4;

-- 
DROP TABLE IF EXISTS `news_categories`;
CREATE TABLE `news_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `description` text,
  `sort_order` int(11) DEFAULT '0',
  `status` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4;

-- 
DROP TABLE IF EXISTS `news`;
CREATE TABLE `news` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL,
  `title` varchar(200) NOT NULL,
  `slug` varchar(200) NOT NULL,
  `summary` varchar(500) DEFAULT NULL,
  `content` longtext,
  `image` varchar(500) DEFAULT NULL,
  `author` varchar(50) DEFAULT NULL,
  `tags` varchar(200) DEFAULT NULL,
  `sort_order` int(11) DEFAULT '0',
  `status` tinyint(1) DEFAULT '1',
  `views` int(11) DEFAULT '0',
  `is_featured` tinyint(1) DEFAULT '0',
  `published_at` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `category_id` (`category_id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4;

-- 
DROP TABLE IF EXISTS `cases`;
CREATE TABLE `cases` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `slug` varchar(200) NOT NULL,
  `client_name` varchar(100) NOT NULL,
  `industry` varchar(50) DEFAULT NULL,
  `summary` varchar(500) DEFAULT NULL,
  `description` longtext,
  `image` varchar(500) DEFAULT NULL,
  `gallery` text,
  `project_url` varchar(500) DEFAULT NULL,
  `completion_date` date DEFAULT NULL,
  `sort_order` int(11) DEFAULT '0',
  `status` tinyint(1) DEFAULT '1',
  `views` int(11) DEFAULT '0',
  `is_featured` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4;

-- 
DROP TABLE IF EXISTS `team_members`;
CREATE TABLE `team_members` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `position` varchar(100) NOT NULL,
  `department` varchar(50) DEFAULT NULL,
  `bio` text,
  `photo` varchar(500) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `social_links` text,
  `sort_order` int(11) DEFAULT '0',
  `status` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 
DROP TABLE IF EXISTS `contact_forms`;
CREATE TABLE `contact_forms` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `company` varchar(100) DEFAULT NULL,
  `subject` varchar(200) NOT NULL,
  `message` text NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` varchar(500) DEFAULT NULL,
  `status` enum('new','read','replied','closed') DEFAULT 'new',
  `admin_reply` text,
  `replied_at` datetime DEFAULT NULL,
  `replied_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4;

-- 图片分组表
DROP TABLE IF EXISTS `image_groups`;
CREATE TABLE `image_groups` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '分组ID',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分组名称',
  `slug` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分组别名',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '分组描述',
  `parent_id` int(11) unsigned DEFAULT NULL COMMENT '父分组ID',
  `sort_order` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序权重',
  `image_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '图片数量',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1:启用 0:禁用)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_slug` (`slug`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片分组表';

-- 图片管理表
DROP TABLE IF EXISTS `images`;
CREATE TABLE `images` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '图片ID',
  `filename` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始文件名',
  `stored_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '存储文件名',
  `file_path` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件路径',
  `file_url` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '访问URL',
  `file_size` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '文件大小(字节)',
  `mime_type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'MIME类型',
  `extension` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件扩展名',
  `width` int(11) unsigned DEFAULT NULL COMMENT '图片宽度',
  `height` int(11) unsigned DEFAULT NULL COMMENT '图片高度',
  `group_id` int(11) unsigned DEFAULT NULL COMMENT '分组ID',
  `alt_text` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片描述',
  `tags` text COLLATE utf8mb4_unicode_ci COMMENT '图片标签(JSON格式)',
  `upload_ip` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上传IP',
  `user_agent` text COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1:正常 0:禁用)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_group_id` (`group_id`),
  KEY `idx_mime_type` (`mime_type`),
  KEY `idx_extension` (`extension`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_file_size` (`file_size`)
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片管理表';

-- 页面模板表
DROP TABLE IF EXISTS `page_templates`;
CREATE TABLE `page_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板名称',
  `slug` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '页面URL标识',
  `description` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '模板描述',
  `type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'page' COMMENT '模板类型',
  `config` longtext COLLATE utf8mb4_unicode_ci COMMENT '模板配置JSON',
  `html_content` longtext COLLATE utf8mb4_unicode_ci COMMENT '生成的HTML内容',
  `css_content` longtext COLLATE utf8mb4_unicode_ci COMMENT '生成的CSS内容',
  `preview_image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '预览图片',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态：0=草稿，1=已发布',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_slug` (`slug`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort_order`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='页面模板表';

-- 系统菜单表
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `parent_id` int(11) DEFAULT '0' COMMENT '父级ID，0为顶级菜单',
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单名称',
  `link_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'url' COMMENT '链接类型：url,page,product,solution,case,news,template',
  `link_value` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '链接值',
  `icon` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '菜单图标',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序值，数字越小越靠前',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统菜单表';

-- 
DROP TABLE IF EXISTS `site_settings`;
CREATE TABLE `site_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` longtext,
  `setting_type` enum('text','textarea','number','boolean','json') DEFAULT 'text',
  `description` varchar(200) DEFAULT NULL,
  `group_name` varchar(50) DEFAULT 'general',
  `sort_order` int(11) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4;

-- 插入管理员数据
INSERT INTO `admin_users` (`id`, `username`, `password`, `email`, `real_name`, `role`, `status`, `last_login`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', '系统管理员', 'admin', 1, '2025-06-03 00:57:10', '2025-05-30 02:54:17', '2025-06-03 00:57:10');

-- 插入产品分类数据
INSERT INTO `product_categories` (`id`, `name`, `slug`, `description`, `icon`, `parent_id`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES
(1, '软件开发', 'software-development', '定制软件开发服务', '', 0, 1, 1, '2025-06-09 20:24:11', '2025-06-09 20:40:35'),
(2, '网站建设', 'website-development', '企业网站建设服务', NULL, 0, 2, 1, '2025-06-09 20:24:11', '2025-06-09 20:24:11'),
(3, '移动应用', 'mobile-app', '移动APP开发服务', NULL, 0, 3, 1, '2025-06-09 20:24:11', '2025-06-09 20:24:11'),
(4, '系统集成', 'system-integration', '企业系统集成服务', NULL, 0, 4, 1, '2025-06-09 20:24:11', '2025-06-09 20:24:11');

-- 插入新闻分类数据
INSERT INTO `news_categories` (`id`, `name`, `slug`, `description`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES
(1, '公司动态', 'company-news', '公司最新动态和资讯', 1, 1, '2025-05-31 09:48:35', '2025-05-31 09:48:35'),
(2, '行业资讯', 'industry-news', '行业相关新闻和趋势', 2, 1, '2025-05-31 09:48:35', '2025-06-04 04:38:05'),
(3, '产品发布', 'product-release', '新产品发布和更新', 3, 1, '2025-05-31 09:48:35', '2025-05-31 09:48:35'),
(4, '技术分享', 'tech-sharing', '技术文章和经验分享', 4, 1, '2025-05-31 09:48:35', '2025-05-31 09:48:35');

-- 插入图片分组数据
INSERT INTO `image_groups` (`id`, `name`, `slug`, `description`, `parent_id`, `sort_order`, `image_count`, `status`, `created_at`, `updated_at`) VALUES
(1, '默认分组', 'default', '系统默认图片分组', NULL, 0, 0, 1, '2025-06-03 03:27:15', '2025-06-03 03:27:15'),
(2, '新闻图片', 'news', '新闻相关图片', NULL, 10, 0, 1, '2025-06-03 03:27:15', '2025-06-03 03:27:15'),
(3, '产品图片', 'products', '产品展示图片', NULL, 20, 0, 1, '2025-06-03 03:27:15', '2025-06-03 03:27:15'),
(4, '轮播图片', 'banners', '首页轮播图片', NULL, 30, 0, 1, '2025-06-03 03:27:15', '2025-06-03 03:27:15'),
(5, '其他图片', 'others', '其他类型图片', NULL, 999, 0, 1, '2025-06-03 03:27:15', '2025-06-03 03:27:15');

-- 插入系统菜单数据（基于header.html的菜单结构）
INSERT INTO `sys_menu` (`id`, `parent_id`, `name`, `link_type`, `link_value`, `icon`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES
-- 顶级菜单
(1, 0, '首页', 'url', '/', 'fas fa-home', 1, 1, NOW(), NOW()),
(2, 0, '解决方案', 'url', '/solutions', 'fas fa-lightbulb', 2, 1, NOW(), NOW()),
(3, 0, '产品介绍', 'url', '/products', 'fas fa-cube', 3, 1, NOW(), NOW()),
(4, 0, '客户案例', 'url', '/cases', 'fas fa-briefcase', 4, 1, NOW(), NOW()),
(5, 0, '新闻资讯', 'url', '/news', 'fas fa-newspaper', 5, 1, NOW(), NOW()),
(6, 0, '更多页面', 'url', '#', 'fas fa-ellipsis-h', 6, 1, NOW(), NOW()),
(7, 0, '联系我们', 'url', '/contact', 'fas fa-envelope', 7, 1, NOW(), NOW()),
-- 解决方案子菜单
(8, 2, '社交分销', 'solution', 'social-distribution', '/assets/images/nav/fenxiao.png', 1, 1, NOW(), NOW()),
(9, 2, '智能多门店', 'solution', 'multi-store', '/assets/images/nav/duomendian.png', 2, 1, NOW(), NOW()),
(10, 2, '大型多商户', 'solution', 'multi-merchant', '/assets/images/nav/duoshanghu.png', 3, 1, NOW(), NOW()),
(11, 2, '大货批发', 'solution', 'wholesale', '/assets/images/nav/pifa.png', 4, 1, NOW(), NOW()),
(12, 2, '平台级供货商', 'solution', 'supplier-platform', '/assets/images/nav/gonghuoshang.png', 5, 1, NOW(), NOW()),
(13, 2, '本地生活服务', 'solution', 'local-services', '/assets/images/nav/bendifuwu.png', 6, 1, NOW(), NOW()),
(14, 2, '企业数字化', 'solution', 'enterprise', '/assets/images/nav/shuzihua.png', 7, 1, NOW(), NOW()),
(15, 2, '定制化方案', 'solution', 'custom', '/assets/images/nav/dingzhi.png', 8, 1, NOW(), NOW()),
-- 更多页面子菜单
(16, 6, '关于我们', 'page', 'about', 'fas fa-info-circle', 1, 1, NOW(), NOW()),
-- 产品介绍二级菜单（产品分类）
(17, 3, '软件开发', 'category', 'software-development', 'fas fa-code', 1, 1, NOW(), NOW()),
(18, 3, '网站建设', 'category', 'website-development', 'fas fa-globe', 2, 1, NOW(), NOW()),
(19, 3, '移动应用', 'category', 'mobile-app', 'fas fa-mobile-alt', 3, 1, NOW(), NOW()),
(20, 3, '系统集成', 'category', 'system-integration', 'fas fa-cogs', 4, 1, NOW(), NOW()),
-- 软件开发三级菜单（具体产品）
(21, 17, 'AI智能分析平台', 'product', 'ai-analytics-platform', 'fas fa-chart-line', 1, 1, NOW(), NOW()),
(22, 17, '企业级CRM系统', 'product', 'enterprise-crm-system', 'fas fa-users-cog', 2, 1, NOW(), NOW()),
(23, 17, '智能办公协作平台', 'product', 'smart-office-platform', 'fas fa-laptop-code', 3, 1, NOW(), NOW()),
-- 网站建设三级菜单（具体产品）
(24, 18, '响应式企业官网', 'product', 'responsive-corporate-website', 'fas fa-globe', 1, 1, NOW(), NOW()),
(25, 18, '电商购物平台', 'product', 'ecommerce-platform', 'fas fa-shopping-cart', 2, 1, NOW(), NOW()),
(26, 18, '内容管理系统', 'product', 'content-management-system', 'fas fa-edit', 3, 1, NOW(), NOW()),
-- 移动应用三级菜单（具体产品）
(27, 19, '企业移动APP', 'product', 'enterprise-mobile-app', 'fas fa-mobile-alt', 1, 1, NOW(), NOW()),
(28, 19, '微信小程序开发', 'product', 'wechat-miniprogram', 'fab fa-weixin', 2, 1, NOW(), NOW()),
(29, 19, 'APP UI/UX设计', 'product', 'app-ui-ux-design', 'fas fa-palette', 3, 1, NOW(), NOW()),
-- 系统集成三级菜单（具体产品）
(30, 20, '云服务器部署', 'product', 'cloud-server-deployment', 'fas fa-cloud', 1, 1, NOW(), NOW()),
(31, 20, '数据库优化服务', 'product', 'database-optimization', 'fas fa-database', 2, 1, NOW(), NOW()),
(32, 20, '系统安全加固', 'product', 'system-security-hardening', 'fas fa-shield-alt', 3, 1, NOW(), NOW());

-- 插入网站设置数据
INSERT INTO `site_settings` (`id`, `setting_key`, `setting_value`, `setting_type`, `description`, `group_name`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, 'site_name', '企业官网', 'text', '网站名称', 'basic', 0, '2025-05-30 02:54:17', '2025-06-05 21:39:08'),
(2, 'site_title', '专业的企业解决方案提供商', 'text', '网站标题', 'basic', 0, '2025-05-30 02:54:17', '2025-06-05 21:39:08'),
(3, 'site_description', '我们提供专业的企业级解决方案', 'textarea', '网站描述', 'basic', 0, '2025-05-30 02:54:17', '2025-06-05 21:39:08'),
(4, 'site_keywords', '企业官网,解决方案,技术服务', 'text', '网站关键词', 'basic', 0, '2025-05-30 02:54:17', '2025-06-05 21:39:08'),
(5, 'company_name', '三只鱼科技有限公司', 'text', '公司名称', 'contact', 0, '2025-05-30 02:54:17', '2025-06-03 21:35:56'),
(6, 'company_address', '北京市朝阳区科技园区', 'text', '公司地址', 'contact', 0, '2025-05-30 02:54:17', '2025-06-03 21:35:56'),
(7, 'company_phone', '************', 'text', '联系电话', 'contact', 0, '2025-05-30 02:54:17', '2025-06-03 21:35:56'),
(8, 'company_email', '<EMAIL>', 'text', '联系邮箱', 'contact', 0, '2025-05-30 02:54:17', '2025-06-03 21:35:56'),
(9, 'company_qq', *********, 'text', 'QQ号码', 'contact', 0, '2025-05-30 02:54:17', '2025-06-03 21:35:56'),
(10, 'company_wechat', 'hsdj37', 'text', '微信号', 'contact', 0, '2025-05-30 02:54:17', '2025-06-03 21:35:56'),
(14, 'site_url', 'http://san.com', 'text', '网站URL', 'basic', 0, '2025-05-31 02:36:32', '2025-06-05 21:39:08'),
(15, 'meta_keywords', '', 'text', 'META关键词', 'seo', 0, '2025-05-31 02:36:32', '2025-06-02 09:02:51'),
(16, 'meta_description', '', 'textarea', 'META描述', 'seo', 0, '2025-05-31 02:36:32', '2025-06-02 09:02:51'),
(17, 'analytics_code', '', 'textarea', '统计代码', 'seo', 0, '2025-05-31 02:36:32', '2025-06-02 09:02:51'),
(18, 'baidu_verify', '', 'text', '百度验证码', 'seo', 0, '2025-05-31 02:36:32', '2025-06-02 09:02:51'),
(19, 'max_file_size', 5, 'number', '最大文件上传大小(MB)', 'system', 0, '2025-05-31 02:36:32', '2025-06-03 21:08:54'),
(20, 'items_per_page', 10, 'number', '每页显示条数', 'system', 0, '2025-05-31 02:36:32', '2025-06-03 21:08:54'),
(21, 'cache_enabled', 1, 'boolean', '启用缓存功能', 'system', 0, '2025-05-31 02:36:32', '2025-06-03 21:08:54'),
(22, 'cache_time', 3600, 'number', '缓存时间(秒)', 'system', 0, '2025-05-31 02:36:32', '2025-06-03 21:08:54');

-- 插入新闻测试数据
INSERT INTO `news` (`id`, `category_id`, `title`, `slug`, `summary`, `content`, `image`, `author`, `tags`, `sort_order`, `status`, `views`, `is_featured`, `published_at`, `created_at`, `updated_at`) VALUES
(1, 1, '三只鱼科技荣获2024年度最佳技术创新奖', 'tech-innovation-award-2024', '在刚刚结束的2024年度科技创新大会上，三只鱼科技凭借其在人工智能和大数据领域的突出贡献，荣获最佳技术创新奖。', '<p>2024年12月19日，在北京举办的年度科技创新大会上，三只鱼科技有限公司凭借其在人工智能和大数据领域的卓越表现，荣获"2024年度最佳技术创新奖"。</p><p>该奖项是对我们团队在过去一年中不懈努力和技术突破的认可。我们的AI智能分析平台和企业数字化转型解决方案得到了业界的高度评价。</p><p>未来，我们将继续致力于技术创新，为客户提供更优质的服务。</p>', '/uploads/news/award-2024.jpg', '韩总', '技术创新,人工智能,大数据,获奖', 0, 1, 156, 1, '2024-12-19 10:00:00', NOW(), NOW()),
(2, 2, '2024年企业数字化转型趋势报告发布', 'digital-transformation-trends-2024', '最新发布的行业报告显示，企业数字化转型已成为2024年的主要趋势，云计算、AI和物联网技术应用快速增长。', '<p>根据最新发布的《2024年企业数字化转型趋势报告》，今年企业在数字化转型方面的投入同比增长35%。</p><p>报告指出，云计算、人工智能和物联网技术成为企业数字化转型的三大核心驱动力。超过80%的企业表示将在未来两年内加大对这些技术的投资。</p><p>三只鱼科技作为行业领先的数字化解决方案提供商，将继续为企业提供专业的转型服务。</p>', '/uploads/news/digital-trends-2024.jpg', '技术部', '数字化转型,云计算,人工智能,物联网', 0, 1, 89, 0, '2024-12-18 14:30:00', NOW(), NOW()),
(3, 3, '全新AI智能客服系统正式发布', 'ai-customer-service-system-launch', '经过6个月的精心研发，我们的AI智能客服系统正式发布，支持多语言对话，智能问答准确率达95%以上。', '<p>今日，三只鱼科技正式发布全新的AI智能客服系统V2.0。该系统经过6个月的精心研发和测试，在智能对话、问题理解和回答准确性方面都有了显著提升。</p><p>新系统主要特性包括：</p><ul><li>支持中英文双语对话</li><li>智能问答准确率达95%以上</li><li>7x24小时不间断服务</li><li>与现有CRM系统无缝集成</li></ul><p>目前已有多家企业客户开始试用，反馈良好。</p>', '/uploads/news/ai-customer-service.jpg', '产品经理', '人工智能,客服系统,产品发布,智能对话', 0, 1, 234, 1, '2024-12-17 09:15:00', NOW(), NOW()),
(4, 4, '微服务架构在企业级应用中的最佳实践', 'microservices-best-practices', '本文分享了微服务架构在企业级应用开发中的最佳实践，包括服务拆分、数据管理、监控和部署策略。', '<p>微服务架构已成为现代企业级应用开发的主流选择。本文将分享我们在实际项目中总结的最佳实践。</p><h3>服务拆分原则</h3><p>1. 按业务领域拆分<br>2. 保持服务的高内聚低耦合<br>3. 考虑团队结构和沟通成本</p><h3>数据管理策略</h3><p>1. 每个服务拥有独立的数据库<br>2. 通过API进行数据交互<br>3. 实现最终一致性</p><h3>监控和运维</h3><p>1. 分布式链路追踪<br>2. 统一日志管理<br>3. 服务健康检查</p>', '/uploads/news/microservices-architecture.jpg', '架构师', '微服务,架构设计,最佳实践,技术分享', 0, 1, 167, 0, '2024-12-16 16:20:00', NOW(), NOW()),
(5, 1, '三只鱼科技与知名企业达成战略合作', 'strategic-partnership-announcement', '我们很高兴宣布与多家知名企业达成战略合作伙伴关系，共同推进企业数字化转型和技术创新。', '<p>2024年12月15日，三只鱼科技与多家行业领先企业正式签署战略合作协议，建立长期合作伙伴关系。</p><p>此次合作将重点围绕以下几个方面展开：</p><ul><li>企业数字化转型咨询服务</li><li>定制化软件开发</li><li>云计算和大数据解决方案</li><li>人工智能技术应用</li></ul><p>通过强强联合，我们将为更多企业提供专业、高效的技术服务，推动行业数字化发展。</p>', '/uploads/news/strategic-partnership.jpg', '商务部', '战略合作,企业合作,数字化转型,技术服务', 0, 1, 198, 1, '2024-12-15 11:00:00', NOW(), NOW()),
(6, 4, 'Docker容器化部署实战指南', 'docker-deployment-guide', '详细介绍如何使用Docker进行应用容器化部署，包括镜像构建、容器编排和生产环境最佳实践。', '<p>Docker容器化技术已成为现代应用部署的标准选择。本文将详细介绍Docker在实际项目中的应用。</p><h3>Docker基础概念</h3><p>1. 镜像(Image)和容器(Container)<br>2. Dockerfile编写规范<br>3. 数据卷和网络配置</p><h3>容器编排</h3><p>1. Docker Compose使用<br>2. 服务发现和负载均衡<br>3. 健康检查和自动重启</p><h3>生产环境部署</h3><p>1. 安全配置和权限管理<br>2. 监控和日志收集<br>3. 备份和灾难恢复</p>', '/uploads/news/docker-deployment.jpg', '运维工程师', 'Docker,容器化,部署,运维,技术分享', 0, 1, 145, 0, '2024-12-14 13:45:00', NOW(), NOW());

-- 插入产品测试数据
INSERT INTO `products` (`id`, `category_id`, `name`, `slug`, `short_description`, `description`, `image`, `price`, `sort_order`, `status`, `views`, `is_featured`, `is_hot`, `is_new`, `created_at`, `updated_at`) VALUES
(1, 1, 'AI智能分析平台', 'ai-analytics-platform', '基于机器学习的企业数据智能分析平台，提供实时数据洞察和预测分析。', '<p>AI智能分析平台是我们的旗舰产品，集成了最新的机器学习算法和大数据处理技术。</p><p>主要功能包括：数据可视化、预测分析、异常检测、智能报告生成等。</p>', '/uploads/products/ai-analytics.jpg', '面议', 0, 1, 0, 1, 1, 0, NOW(), NOW()),
(2, 1, '企业级CRM系统', 'enterprise-crm-system', '全功能客户关系管理系统，支持销售管理、客户服务、营销自动化等功能。', '<p>专为中大型企业设计的CRM系统，提供完整的客户生命周期管理解决方案。</p><p>包含销售漏斗、客户档案、工单系统、报表分析等核心模块。</p>', '/uploads/products/crm-system.jpg', '￥50,000起', 0, 1, 0, 1, 0, 1, NOW(), NOW()),
(3, 1, '智能办公协作平台', 'smart-office-platform', '集成即时通讯、文档协作、项目管理、视频会议的一体化办公平台。', '<p>为现代企业打造的智能办公解决方案，提升团队协作效率。</p><p>支持多端同步、权限管理、审批流程、数据安全等企业级功能。</p>', '/uploads/products/office-platform.jpg', '￥30,000起', 0, 1, 0, 0, 1, 1, NOW(), NOW()),
(4, 2, '响应式企业官网', 'responsive-corporate-website', '专业的企业官网建设服务，支持PC、平板、手机多端适配，SEO优化。', '<p>采用最新的响应式设计技术，确保在各种设备上都有完美的显示效果。</p><p>包含内容管理系统、SEO优化、数据统计、在线客服等功能。</p>', '/uploads/products/corporate-website.jpg', '￥15,000起', 0, 1, 0, 1, 0, 0, NOW(), NOW()),
(5, 2, '电商购物平台', 'ecommerce-platform', '功能完善的电商解决方案，支持多商户、多支付方式、库存管理等。', '<p>专业的电商平台开发服务，支持B2C、B2B、O2O等多种商业模式。</p><p>集成支付网关、物流系统、营销工具、数据分析等核心功能。</p>', '/uploads/products/ecommerce-platform.jpg', '￥80,000起', 0, 1, 0, 0, 1, 0, NOW(), NOW()),
(6, 2, '内容管理系统', 'content-management-system', '灵活易用的内容管理系统，支持多站点管理、权限控制、模板定制。', '<p>为企业和机构提供专业的内容管理解决方案，简化网站维护工作。</p><p>支持可视化编辑、模板系统、插件扩展、多语言等功能。</p>', '/uploads/products/cms-system.jpg', '￥25,000起', 0, 1, 0, 0, 0, 1, NOW(), NOW()),
(7, 3, '企业移动APP', 'enterprise-mobile-app', '定制化企业移动应用开发，支持iOS和Android双平台。', '<p>专业的移动应用开发服务，为企业打造专属的移动端解决方案。</p><p>支持原生开发、混合开发、跨平台开发等多种技术方案。</p>', '/uploads/products/mobile-app.jpg', '￥60,000起', 0, 1, 0, 1, 0, 1, NOW(), NOW()),
(8, 3, '微信小程序开发', 'wechat-miniprogram', '专业的微信小程序开发服务，快速上线，功能丰富。', '<p>基于微信生态的小程序开发，帮助企业快速获取微信用户。</p><p>支持商城、服务预约、会员管理、营销活动等多种应用场景。</p>', '/uploads/products/wechat-miniprogram.jpg', '￥20,000起', 0, 1, 0, 0, 1, 0, NOW(), NOW()),
(9, 3, 'APP UI/UX设计', 'app-ui-ux-design', '专业的移动应用界面设计服务，注重用户体验和视觉效果。', '<p>由资深设计师团队提供的专业UI/UX设计服务。</p><p>包含用户研究、交互设计、视觉设计、原型制作等完整流程。</p>', '/uploads/products/app-design.jpg', '￥15,000起', 0, 1, 0, 0, 0, 1, NOW(), NOW()),
(10, 4, '云服务器部署', 'cloud-server-deployment', '专业的云服务器部署和运维服务，确保系统稳定高效运行。', '<p>提供完整的云基础设施部署和管理服务。</p><p>包含服务器配置、安全加固、监控告警、备份恢复等服务。</p>', '/uploads/products/cloud-deployment.jpg', '￥8,000起', 0, 1, 0, 0, 1, 0, NOW(), NOW()),
(11, 4, '数据库优化服务', 'database-optimization', '专业的数据库性能优化服务，提升系统响应速度和稳定性。', '<p>由数据库专家提供的专业优化服务，解决性能瓶颈问题。</p><p>包含查询优化、索引设计、架构调整、监控配置等服务。</p>', '/uploads/products/database-optimization.jpg', '￥12,000起', 0, 1, 0, 0, 0, 0, NOW(), NOW()),
(12, 4, '系统安全加固', 'system-security-hardening', '全面的系统安全加固服务，保护企业数据和系统安全。', '<p>专业的网络安全服务，为企业提供全方位的安全保护。</p><p>包含漏洞扫描、安全配置、防火墙设置、入侵检测等服务。</p>', '/uploads/products/security-hardening.jpg', '￥18,000起', 0, 1, 0, 1, 0, 0, NOW(), NOW());

-- 插入案例测试数据
INSERT INTO `cases` (`id`, `title`, `slug`, `client_name`, `industry`, `summary`, `description`, `image`, `project_url`, `completion_date`, `sort_order`, `status`, `views`, `is_featured`, `created_at`, `updated_at`) VALUES
(1, '某大型制造企业数字化转型项目', 'manufacturing-digital-transformation', '华东制造集团', '制造业', '为华东制造集团打造的全面数字化转型解决方案，包括ERP系统升级、生产管理系统和数据分析平台。', '<p>项目背景：华东制造集团是一家拥有30年历史的大型制造企业，面临传统管理模式效率低下的问题。</p><p>解决方案：我们为其设计了完整的数字化转型方案，包括：</p><ul><li>ERP系统现代化升级</li><li>智能生产管理系统</li><li>实时数据分析平台</li><li>移动端管理应用</li></ul><p>项目成果：生产效率提升35%，管理成本降低25%，获得客户高度认可。</p>', '/uploads/cases/manufacturing-case.jpg', 'https://manufacturing-demo.com', '2024-11-15', 0, 1, 245, 1, NOW(), NOW()),
(2, '知名电商平台开发案例', 'ecommerce-platform-development', '优品商城', '电子商务', '为优品商城开发的多商户电商平台，支持B2C和B2B模式，日处理订单量超过10万单。', '<p>项目需求：客户需要一个功能完善的多商户电商平台，支持大并发访问。</p><p>技术方案：</p><ul><li>微服务架构设计</li><li>Redis缓存优化</li><li>CDN加速部署</li><li>支付系统集成</li><li>物流系统对接</li></ul><p>项目成果：平台上线后日活用户超过50万，订单转化率提升40%。</p>', '/uploads/cases/ecommerce-case.jpg', 'https://youpin-mall.com', '2024-10-20', 0, 1, 189, 1, NOW(), NOW()),
(3, '教育机构在线学习平台', 'online-learning-platform', '智慧教育科技', '教育培训', '为智慧教育科技开发的在线学习平台，支持直播授课、作业管理、学习进度跟踪等功能。', '<p>项目背景：疫情期间，传统教育机构急需线上教学解决方案。</p><p>功能特色：</p><ul><li>高清视频直播系统</li><li>互动白板功能</li><li>作业批改系统</li><li>学习数据分析</li><li>家长监督功能</li></ul><p>项目成果：平台支持同时在线学习人数超过5000人，获得师生一致好评。</p>', '/uploads/cases/education-case.jpg', 'https://smart-edu.com', '2024-09-30', 0, 1, 156, 0, NOW(), NOW()),
(4, '医疗机构信息管理系统', 'hospital-management-system', '仁爱医院', '医疗健康', '为仁爱医院开发的综合信息管理系统，包括挂号、诊疗、药房、财务等全流程管理。', '<p>项目需求：医院需要一套完整的信息化管理系统，提升医疗服务效率。</p><p>系统模块：</p><ul><li>患者挂号系统</li><li>电子病历管理</li><li>药房库存管理</li><li>财务结算系统</li><li>医生工作站</li></ul><p>项目成果：患者就诊时间缩短50%，医院运营效率显著提升。</p>', '/uploads/cases/hospital-case.jpg', 'https://renai-hospital.com', '2024-08-25', 0, 1, 134, 1, NOW(), NOW()),
(5, '金融科技APP开发项目', 'fintech-app-development', '普惠金融', '金融科技', '为普惠金融开发的移动金融服务APP，提供贷款申请、理财投资、账户管理等服务。', '<p>项目挑战：金融行业对安全性和稳定性要求极高，需要严格的风控措施。</p><p>技术亮点：</p><ul><li>多重安全认证</li><li>实时风险控制</li><li>数据加密传输</li><li>生物识别技术</li><li>智能投顾算法</li></ul><p>项目成果：APP上线后用户注册量突破100万，获得金融监管部门认可。</p>', '/uploads/cases/fintech-case.jpg', 'https://puhui-finance.com', '2024-07-10', 0, 1, 178, 0, NOW(), NOW()),
(6, '物流管理系统优化项目', 'logistics-system-optimization', '快运物流', '物流运输', '为快运物流优化的智能物流管理系统，实现路线规划、货物跟踪、成本控制的全面升级。', '<p>项目背景：传统物流企业面临成本上升、效率低下的挑战。</p><p>优化方案：</p><ul><li>智能路线规划算法</li><li>实时货物跟踪系统</li><li>自动化仓储管理</li><li>成本分析与控制</li><li>客户服务优化</li></ul><p>项目成果：运输成本降低30%，客户满意度提升45%，成为行业标杆。</p>', '/uploads/cases/logistics-case.jpg', 'https://kuaiyun-logistics.com', '2024-06-15', 0, 1, 167, 1, NOW(), NOW());

-- 更新产品图标
UPDATE `products` SET `icon` = 'fas fa-chart-line' WHERE `slug` = 'ai-analytics-platform';
UPDATE `products` SET `icon` = 'fas fa-users-cog' WHERE `slug` = 'enterprise-crm-system';
UPDATE `products` SET `icon` = 'fas fa-laptop-code' WHERE `slug` = 'smart-office-platform';
UPDATE `products` SET `icon` = 'fas fa-globe' WHERE `slug` = 'responsive-corporate-website';
UPDATE `products` SET `icon` = 'fas fa-shopping-cart' WHERE `slug` = 'ecommerce-platform';
UPDATE `products` SET `icon` = 'fas fa-edit' WHERE `slug` = 'content-management-system';
UPDATE `products` SET `icon` = 'fas fa-mobile-alt' WHERE `slug` = 'enterprise-mobile-app';
UPDATE `products` SET `icon` = 'fab fa-weixin' WHERE `slug` = 'wechat-miniprogram';
UPDATE `products` SET `icon` = 'fas fa-palette' WHERE `slug` = 'app-ui-ux-design';
UPDATE `products` SET `icon` = 'fas fa-cloud' WHERE `slug` = 'cloud-server-deployment';
UPDATE `products` SET `icon` = 'fas fa-database' WHERE `slug` = 'database-optimization';
UPDATE `products` SET `icon` = 'fas fa-shield-alt' WHERE `slug` = 'system-security-hardening';



-- 更新为hippopx高质量图片链接
UPDATE `news` SET `image` = 'https://p3.hippopx.com/preview/6/923/macbook-pro-turned-on-laptop-programming-code-computer-screen-keyboard-coffee-mug-workspace-technology-coding-software-development.jpg' WHERE `slug` = 'tech-innovation-award-2024';
UPDATE `news` SET `image` = 'https://p3.hippopx.com/preview/6/923/electric-blue-circuits-ai-technology-advanced-wallpaper-computer-cpu-information-hardware.jpg' WHERE `slug` = 'digital-transformation-trends-2024';
UPDATE `news` SET `image` = 'https://p3.hippopx.com/preview/6/923/robotic-hand-blue-background-futuristic-technology-radiant-blue-sky-reaching-out-robotic-arm-advanced-technology-modern-robotics.jpg' WHERE `slug` = 'ai-customer-service-system-launch';
UPDATE `news` SET `image` = 'https://p3.hippopx.com/preview/6/923/futuristic-technology-background-wallpaper-techy-scifi-glowing-texture-dark-surface-lighting.jpg' WHERE `slug` = 'microservices-best-practices';
UPDATE `news` SET `image` = 'https://p3.hippopx.com/preview/6/923/people-smiling-diverse-group-adults-technology-devices-indoors-togetherness-happy-using-technology-group-interaction.jpg' WHERE `slug` = 'strategic-partnership-announcement';
UPDATE `news` SET `image` = 'https://p3.hippopx.com/preview/6/923/electronics-computer-circuits-technology.jpg' WHERE `slug` = 'docker-deployment-guide';

UPDATE `products` SET `image` = 'https://p3.hippopx.com/preview/6/923/quantum-computer-processor-computer-technology.jpg' WHERE `slug` = 'ai-analytics-platform';
UPDATE `products` SET `image` = 'https://p3.hippopx.com/preview/6/923/people-using-computers-male-developers-desks-programming-modern-office-workspace-large-monitors.jpg' WHERE `slug` = 'enterprise-crm-system';
UPDATE `products` SET `image` = 'https://p3.hippopx.com/preview/6/923/technology-laptop-background.jpg' WHERE `slug` = 'smart-office-platform';
UPDATE `products` SET `image` = 'https://p3.hippopx.com/preview/6/923/macbook-pro-website-version-2-stylish-workspace-laptop-design-software-monitors-desk-setup.jpg' WHERE `slug` = 'responsive-corporate-website';
UPDATE `products` SET `image` = 'https://p3.hippopx.com/preview/6/923/ipad-iphone-technology-tech.jpg' WHERE `slug` = 'ecommerce-platform';
UPDATE `products` SET `image` = 'https://p3.hippopx.com/preview/6/923/library-shelf-books-on-internet-technology-books-education-themed-projects-close-up-library.jpg' WHERE `slug` = 'content-management-system';
UPDATE `products` SET `image` = 'https://p3.hippopx.com/preview/6/923/smartphone-close-up-instagram-app-modern-technology-phone-screen-app-icon-technology-device.jpg' WHERE `slug` = 'enterprise-mobile-app';
UPDATE `products` SET `image` = 'https://p3.hippopx.com/preview/6/923/mobile-smartphone-technology-coffee.jpg' WHERE `slug` = 'wechat-miniprogram';
UPDATE `products` SET `image` = 'https://p3.hippopx.com/preview/6/923/photography-gear-technology-tech.jpg' WHERE `slug` = 'app-ui-ux-design';
UPDATE `products` SET `image` = 'https://p3.hippopx.com/preview/6/923/ethernet-technology-server-tech.jpg' WHERE `slug` = 'cloud-server-deployment';
UPDATE `products` SET `image` = 'https://p3.hippopx.com/preview/6/923/circuit-board-capacitors-resistors-black-transistor-modern-technology-detailed-shot-electronic-components.jpg' WHERE `slug` = 'database-optimization';
UPDATE `products` SET `image` = 'https://p3.hippopx.com/preview/6/923/hacker-cyber-illegal-technology-computer-hackers-full-hd-ultra-hacks.jpg' WHERE `slug` = 'system-security-hardening';

UPDATE `cases` SET `image` = 'https://p3.hippopx.com/preview/6/923/industry-pipe-technology-steel.jpg' WHERE `slug` = 'manufacturing-digital-transformation';
UPDATE `cases` SET `image` = 'https://p3.hippopx.com/preview/6/923/mobile-smartphone-technology-phone.jpg' WHERE `slug` = 'ecommerce-platform-development';
UPDATE `cases` SET `image` = 'https://p3.hippopx.com/preview/6/923/students-using-computers-children-educational-activities-modern-computer-lab-kids-learning-technology.jpg' WHERE `slug` = 'online-learning-platform';
UPDATE `cases` SET `image` = 'https://p3.hippopx.com/preview/6/923/ct-scan-radiology-technology-medical-technology-radiation-ct-scan-women-nurse-room-medical.jpg' WHERE `slug` = 'hospital-management-system';
UPDATE `cases` SET `image` = 'https://p3.hippopx.com/preview/6/923/smartwatch-technology-tech-watch.jpg' WHERE `slug` = 'fintech-app-development';
UPDATE `cases` SET `image` = 'https://p3.hippopx.com/preview/6/923/white-satellite-dish-large-satellite-dish-colorful-sunset-broadcasting-technology-silhouette-outdoor-technology.jpg' WHERE `slug` = 'logistics-system-optimization';

-- 插入解决方案测试数据
INSERT INTO `solutions` (`id`, `name`, `slug`, `short_description`, `description`, `icon`, `image`, `features`, `sort_order`, `status`, `views`, `created_at`, `updated_at`) VALUES
(1, '社交分销解决方案', 'social-distribution', '基于社交网络的分销体系，帮助企业快速扩展销售渠道，实现裂变式增长。', '<p>社交分销解决方案是专为现代企业打造的创新营销模式，通过社交网络的力量实现销售渠道的快速扩展。</p><h3>核心优势</h3><ul><li>零成本获客：通过社交分享实现自然传播</li><li>裂变式增长：每个用户都是潜在的分销商</li><li>精准营销：基于社交关系的信任营销</li><li>数据驱动：全程数据跟踪和分析</li></ul><h3>适用场景</h3><p>适合电商、教育、美妆、母婴等行业，特别是需要快速扩展用户群体的企业。</p>', 'fas fa-share-alt', 'https://p3.hippopx.com/preview/6/923/people-smiling-diverse-group-adults-technology-devices-indoors-togetherness-happy-using-technology-group-interaction.jpg', '["零成本获客", "裂变式增长", "精准营销", "数据驱动", "全程跟踪"]', 10, 1, 0, NOW(), NOW()),
(2, '智能多门店管理', 'multi-store', '统一管理多个门店的智能化解决方案，实现库存、订单、会员的一体化管理。', '<p>智能多门店管理系统为连锁企业提供全方位的门店管理解决方案，通过数字化手段提升运营效率。</p><h3>功能特色</h3><ul><li>统一库存管理：实时同步各门店库存信息</li><li>订单一体化：线上线下订单统一处理</li><li>会员通用：会员信息全门店共享</li><li>数据分析：多维度经营数据分析</li></ul><h3>技术亮点</h3><p>采用云原生架构，支持高并发访问，确保系统稳定性和扩展性。</p>', 'fas fa-store', 'https://p3.hippopx.com/preview/6/923/macbook-pro-website-version-2-stylish-workspace-laptop-design-software-monitors-desk-setup.jpg', '["统一库存", "订单一体化", "会员通用", "数据分析", "云原生架构"]', 9, 1, 0, NOW(), NOW()),
(3, '大型多商户平台', 'multi-merchant', '支持大规模商户入驻的电商平台解决方案，提供完整的商户管理和交易体系。', '<p>大型多商户平台是为大规模电商运营而设计的企业级解决方案，支持数万商户同时在线运营。</p><h3>平台优势</h3><ul><li>高并发处理：支持百万级用户同时在线</li><li>商户自主管理：完善的商户后台管理系统</li><li>多样化结算：灵活的佣金和结算模式</li><li>营销工具丰富：多种促销和营销功能</li></ul><h3>安全保障</h3><p>采用多层安全防护，确保交易安全和数据安全。</p>', 'fas fa-building', 'https://p3.hippopx.com/preview/6/923/ipad-iphone-technology-tech.jpg', '["高并发处理", "商户自主管理", "多样化结算", "营销工具丰富", "安全保障"]', 8, 1, 0, NOW(), NOW()),
(4, '大货批发系统', 'wholesale', '专为批发业务设计的B2B交易平台，支持大宗商品交易和供应链管理。', '<p>大货批发系统专注于B2B批发业务，为批发商和采购商提供高效的交易平台。</p><h3>业务特色</h3><ul><li>大宗交易：支持大批量商品交易</li><li>价格体系：灵活的阶梯价格设置</li><li>供应链管理：完整的供应链跟踪</li><li>信用体系：建立商户信用评级</li></ul><h3>行业适用</h3><p>适用于服装、电子、建材、化工等各类批发行业。</p>', 'fas fa-boxes', 'https://p3.hippopx.com/preview/6/923/industry-pipe-technology-steel.jpg', '["大宗交易", "价格体系", "供应链管理", "信用体系", "行业适用"]', 7, 1, 0, NOW(), NOW()),
(5, '平台级供货商系统', 'supplier-platform', '为供货商提供的一站式平台解决方案，实现供应链的数字化管理。', '<p>平台级供货商系统为供货商提供全方位的数字化管理工具，提升供应链效率。</p><h3>核心功能</h3><ul><li>供应商管理：完善的供应商档案管理</li><li>采购管理：智能化采购流程</li><li>库存优化：AI驱动的库存优化</li><li>质量控制：全程质量跟踪体系</li></ul><h3>智能化特色</h3><p>集成AI算法，实现智能补货、需求预测等功能。</p>', 'fas fa-truck', 'https://p3.hippopx.com/preview/6/923/ethernet-technology-server-tech.jpg', '["供应商管理", "采购管理", "库存优化", "质量控制", "AI驱动"]', 6, 1, 0, NOW(), NOW()),
(6, '本地生活服务平台', 'local-services', '连接本地商家和消费者的生活服务平台，涵盖餐饮、娱乐、生活等多个领域。', '<p>本地生活服务平台致力于打造本地化的生活服务生态圈，为用户提供便民服务。</p><h3>服务范围</h3><ul><li>餐饮外卖：在线点餐和配送服务</li><li>生活服务：家政、维修、美容等服务</li><li>娱乐休闲：电影、KTV、健身等预订</li><li>同城配送：快速的同城配送服务</li></ul><h3>技术特点</h3><p>基于LBS技术，提供精准的位置服务和智能推荐。</p>', 'fas fa-map-marker-alt', 'https://p3.hippopx.com/preview/6/923/mobile-smartphone-technology-phone.jpg', '["餐饮外卖", "生活服务", "娱乐休闲", "同城配送", "LBS技术"]', 5, 1, 0, NOW(), NOW()),
(7, '企业数字化', 'enterprise-digital', '全面的企业数字化转型解决方案，助力传统企业向数字化企业转型升级。', '<p>企业数字化转型解决方案为传统企业提供全方位的数字化改造服务，通过先进技术重塑业务流程。</p><h3>转型核心</h3><ul><li>数字化战略：制定符合企业特点的数字化战略</li><li>流程重塑：优化和重构传统业务流程</li><li>技术升级：引入云计算、大数据、AI等新技术</li><li>组织变革：建立适应数字化的组织架构</li></ul><h3>实施效果</h3><p>帮助企业提升运营效率30%以上，降低运营成本25%，增强市场竞争力。</p><h3>服务保障</h3><p>提供从咨询规划到实施落地的全程服务，确保转型成功。</p>', 'fas fa-digital-tachograph', 'https://p3.hippopx.com/preview/6/923/quantum-computer-processor-computer-technology.jpg', '["数字转型", "流程优化", "智能决策", "技术升级", "组织变革"]', 4, 1, 0, NOW(), NOW()),
(8, '定制化方案', 'custom-solution', '根据企业独特需求量身定制的专业解决方案，提供从咨询到实施的全程服务。', '<p>定制化解决方案服务专注于为企业提供个性化的技术解决方案，满足特殊业务需求和行业特点。</p><h3>服务特色</h3><ul><li>深度调研：全面了解企业业务和技术现状</li><li>个性设计：基于企业特点设计专属解决方案</li><li>灵活开发：采用敏捷开发模式，快速响应需求变化</li><li>专业团队：资深技术专家和行业顾问团队</li></ul><h3>适用场景</h3><p>适合有特殊业务需求、复杂业务流程或特定行业要求的企业。</p><h3>成功案例</h3><p>已为金融、制造、医疗、教育等多个行业提供定制化解决方案，获得客户一致好评。</p>', 'fas fa-cogs', 'https://p3.hippopx.com/preview/6/923/circuit-board-capacitors-resistors-black-transistor-modern-technology-detailed-shot-electronic-components.jpg', '["个性定制", "专业服务", "技术支持", "行业专家", "全程保障"]', 3, 1, 0, NOW(), NOW());

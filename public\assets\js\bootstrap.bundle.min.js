/* Bootstrap 4.6.2 JavaScript 简化版 */

(function(factory) {
    if (typeof jQuery !== 'undefined') {
        factory(jQuery);
    }
})(function($) {
    'use strict';

    // 下拉菜单
    var Dropdown = function(element) {
        this.$element = $(element);
        this.init();
    };

    Dropdown.prototype.init = function() {
        var $element = this.$element;
        
        $element.on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var $menu = $element.next('.dropdown-menu');
            var isOpen = $menu.is(':visible');
            
            // 关闭所有其他下拉菜单
            $('.dropdown-menu').hide();
            
            if (!isOpen) {
                $menu.show();
            }
        });
    };

    // 折叠菜单
    var Collapse = function(element) {
        this.$element = $(element);
        this.init();
    };

    Collapse.prototype.init = function() {
        var $element = this.$element;
        var target = $element.attr('data-target') || $element.attr('href');
        var $target = $(target);
        
        $element.on('click', function(e) {
            e.preventDefault();
            
            if ($target.is(':visible')) {
                $target.slideUp(300);
                $element.removeClass('active');
            } else {
                $target.slideDown(300);
                $element.addClass('active');
            }
        });
    };

    // 导航栏切换
    var NavbarToggle = function(element) {
        this.$element = $(element);
        this.init();
    };

    NavbarToggle.prototype.init = function() {
        var $element = this.$element;
        var target = $element.attr('data-target');
        var $target = $(target);
        
        $element.on('click', function(e) {
            e.preventDefault();
            
            if ($target.hasClass('show')) {
                $target.removeClass('show').slideUp(300);
                $element.removeClass('active');
            } else {
                $target.addClass('show').slideDown(300);
                $element.addClass('active');
            }
        });
    };

    // 模态框
    var Modal = function(element, options) {
        this.$element = $(element);
        this.options = $.extend({}, Modal.DEFAULTS, options);
        this.init();
    };

    Modal.DEFAULTS = {
        backdrop: true,
        keyboard: true,
        show: true
    };

    Modal.prototype.init = function() {
        var self = this;
        var $element = this.$element;
        
        // 创建背景
        if (this.options.backdrop) {
            this.$backdrop = $('<div class="modal-backdrop fade"></div>');
        }
        
        // 绑定事件
        $element.on('click', '[data-dismiss="modal"]', function() {
            self.hide();
        });
        
        if (this.options.keyboard) {
            $(document).on('keydown', function(e) {
                if (e.which === 27) { // ESC键
                    self.hide();
                }
            });
        }
    };

    Modal.prototype.show = function() {
        var $element = this.$element;
        
        if (this.$backdrop) {
            $('body').append(this.$backdrop);
            this.$backdrop.addClass('show');
        }
        
        $element.show().addClass('show');
        $('body').addClass('modal-open');
    };

    Modal.prototype.hide = function() {
        var $element = this.$element;
        
        $element.removeClass('show');
        
        if (this.$backdrop) {
            this.$backdrop.removeClass('show');
            setTimeout(() => {
                this.$backdrop.remove();
            }, 300);
        }
        
        setTimeout(() => {
            $element.hide();
            $('body').removeClass('modal-open');
        }, 300);
    };

    // 工具提示
    var Tooltip = function(element, options) {
        this.$element = $(element);
        this.options = $.extend({}, Tooltip.DEFAULTS, options);
        this.init();
    };

    Tooltip.DEFAULTS = {
        placement: 'top',
        trigger: 'hover',
        delay: 0
    };

    Tooltip.prototype.init = function() {
        var self = this;
        var $element = this.$element;
        var trigger = this.options.trigger;
        
        if (trigger === 'hover') {
            $element.on('mouseenter', function() {
                self.show();
            }).on('mouseleave', function() {
                self.hide();
            });
        } else if (trigger === 'click') {
            $element.on('click', function() {
                self.toggle();
            });
        }
    };

    Tooltip.prototype.show = function() {
        var $element = this.$element;
        var title = $element.attr('title') || $element.attr('data-original-title');
        
        if (!title) return;
        
        var $tooltip = $('<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner">' + title + '</div></div>');
        
        $('body').append($tooltip);
        
        var pos = $element.offset();
        var elementWidth = $element.outerWidth();
        var elementHeight = $element.outerHeight();
        var tooltipWidth = $tooltip.outerWidth();
        var tooltipHeight = $tooltip.outerHeight();
        
        var placement = this.options.placement;
        var top, left;
        
        switch (placement) {
            case 'top':
                top = pos.top - tooltipHeight - 5;
                left = pos.left + (elementWidth - tooltipWidth) / 2;
                break;
            case 'bottom':
                top = pos.top + elementHeight + 5;
                left = pos.left + (elementWidth - tooltipWidth) / 2;
                break;
            case 'left':
                top = pos.top + (elementHeight - tooltipHeight) / 2;
                left = pos.left - tooltipWidth - 5;
                break;
            case 'right':
                top = pos.top + (elementHeight - tooltipHeight) / 2;
                left = pos.left + elementWidth + 5;
                break;
        }
        
        $tooltip.css({
            top: top,
            left: left,
            position: 'absolute'
        }).addClass('show');
        
        this.$tooltip = $tooltip;
    };

    Tooltip.prototype.hide = function() {
        if (this.$tooltip) {
            this.$tooltip.remove();
            this.$tooltip = null;
        }
    };

    Tooltip.prototype.toggle = function() {
        if (this.$tooltip) {
            this.hide();
        } else {
            this.show();
        }
    };

    // 选项卡
    var Tab = function(element) {
        this.$element = $(element);
        this.init();
    };

    Tab.prototype.init = function() {
        var self = this;
        var $element = this.$element;

        $element.on('click', function(e) {
            e.preventDefault();
            self.show();
        });
    };

    Tab.prototype.show = function() {
        var $element = this.$element;
        var target = $element.attr('href');
        var $target = $(target);
        var $parent = $element.closest('.nav-tabs');

        // 移除所有活动状态
        $parent.find('.nav-link').removeClass('active').attr('aria-selected', 'false');
        $('.tab-pane').removeClass('show active');

        // 设置当前为活动状态
        $element.addClass('active').attr('aria-selected', 'true');
        $target.addClass('show active');
    };

    // 轮播图
    var Carousel = function(element, options) {
        this.$element = $(element);
        this.options = $.extend({}, Carousel.DEFAULTS, options);
        this.$items = this.$element.find('.carousel-item');
        this.activeIndex = 0;
        this.init();
    };

    Carousel.DEFAULTS = {
        interval: 5000,
        pause: 'hover',
        wrap: true
    };

    Carousel.prototype.init = function() {
        var self = this;
        
        // 设置第一个为活动状态
        this.$items.eq(0).addClass('active');
        
        // 自动播放
        if (this.options.interval) {
            this.start();
        }
        
        // 暂停/恢复
        if (this.options.pause === 'hover') {
            this.$element.on('mouseenter', function() {
                self.pause();
            }).on('mouseleave', function() {
                self.start();
            });
        }
        
        // 指示器
        this.$element.on('click', '.carousel-indicators li', function() {
            var index = $(this).index();
            self.to(index);
        });
        
        // 控制按钮
        this.$element.on('click', '.carousel-control-prev', function() {
            self.prev();
        }).on('click', '.carousel-control-next', function() {
            self.next();
        });
    };

    Carousel.prototype.start = function() {
        var self = this;
        this.interval = setInterval(function() {
            self.next();
        }, this.options.interval);
    };

    Carousel.prototype.pause = function() {
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }
    };

    Carousel.prototype.next = function() {
        var nextIndex = (this.activeIndex + 1) % this.$items.length;
        this.to(nextIndex);
    };

    Carousel.prototype.prev = function() {
        var prevIndex = (this.activeIndex - 1 + this.$items.length) % this.$items.length;
        this.to(prevIndex);
    };

    Carousel.prototype.to = function(index) {
        if (index === this.activeIndex) return;
        
        this.$items.eq(this.activeIndex).removeClass('active');
        this.$items.eq(index).addClass('active');
        
        // 更新指示器
        this.$element.find('.carousel-indicators li').eq(this.activeIndex).removeClass('active');
        this.$element.find('.carousel-indicators li').eq(index).addClass('active');
        
        this.activeIndex = index;
    };

    // jQuery插件
    $.fn.dropdown = function() {
        return this.each(function() {
            new Dropdown(this);
        });
    };

    $.fn.collapse = function() {
        return this.each(function() {
            new Collapse(this);
        });
    };

    $.fn.modal = function(option) {
        return this.each(function() {
            var $this = $(this);
            var data = $this.data('bs.modal');
            var options = typeof option === 'object' && option;

            if (!data) {
                $this.data('bs.modal', (data = new Modal(this, options)));
            }
            
            if (typeof option === 'string') {
                data[option]();
            } else if (options.show) {
                data.show();
            }
        });
    };

    $.fn.tooltip = function(option) {
        return this.each(function() {
            var $this = $(this);
            var data = $this.data('bs.tooltip');
            var options = typeof option === 'object' && option;

            if (!data) {
                $this.data('bs.tooltip', (data = new Tooltip(this, options)));
            }
            
            if (typeof option === 'string') {
                data[option]();
            }
        });
    };

    $.fn.carousel = function(option) {
        return this.each(function() {
            var $this = $(this);
            var data = $this.data('bs.carousel');
            var options = typeof option === 'object' && option;

            if (!data) {
                $this.data('bs.carousel', (data = new Carousel(this, options)));
            }

            if (typeof option === 'string') {
                data[option]();
            }
        });
    };

    $.fn.tab = function() {
        return this.each(function() {
            new Tab(this);
        });
    };

    // 自动初始化
    $(function() {
        // 下拉菜单
        $('[data-toggle="dropdown"]').dropdown();

        // 折叠菜单
        $('[data-toggle="collapse"]').collapse();

        // 选项卡
        $('[data-toggle="tab"]').tab();

        // 导航栏切换
        $('.navbar-toggler').each(function() {
            new NavbarToggle(this);
        });

        // 工具提示
        $('[data-toggle="tooltip"]').tooltip();

        // 轮播图
        $('.carousel').carousel();

        // 点击外部关闭下拉菜单
        $(document).on('click', function() {
            $('.dropdown-menu').hide();
        });
    });

});

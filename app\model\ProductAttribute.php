<?php
/**
 * 三只鱼网络科技 | 开发者：韩总 | 创建时间：2024-12-19
 * 文件描述：产品属性模型 - ThinkPHP6企业级应用
 * 技术栈：PHP 8.0+ + ThinkPHP6 + MySQL + Redis
 * 版权所有：三只鱼网络科技有限公司
 */

namespace app\model;

use think\Model;

/**
 * 产品属性模型
 */
class ProductAttribute extends Model
{
    // 数据表名
    protected $name = 'product_attributes';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = false; // 属性不需要更新时间
    
    // 字段类型转换
    protected $type = [
        'product_id' => 'integer',
        'sort_order' => 'integer',
    ];
    
    // 关联产品
    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }
    
    // 批量保存产品属性
    public static function saveProductAttributes($productId, $attributes)
    {
        // 先删除原有属性
        self::where('product_id', $productId)->delete();
        
        // 保存新属性
        if (!empty($attributes) && is_array($attributes)) {
            $data = [];
            foreach ($attributes as $index => $attr) {
                if (!empty($attr['name']) && !empty($attr['value'])) {
                    $data[] = [
                        'product_id' => $productId,
                        'attribute_name' => $attr['name'],
                        'attribute_value' => $attr['value'],
                        'sort_order' => $index + 1,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
            }
            
            if (!empty($data)) {
                self::insertAll($data);
            }
        }
    }
    
    // 获取产品的所有属性
    public static function getProductAttributes($productId)
    {
        return self::where('product_id', $productId)
            ->order('sort_order', 'asc')
            ->order('id', 'asc')
            ->select();
    }
}

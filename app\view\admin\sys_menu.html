<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$pageTitle} - 后台管理系统</title>

    <!-- CSS -->
    {include file="admin/common/css"}
    <link rel="stylesheet" href="/assets/css/admin/news.css">
    <style>
    /* 统计卡片样式 */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 25px;
        margin: 25px 30px;
        padding: 0;
    }

    /* 响应式设计 */
    @media (max-width: 1200px) {
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 20px 25px;
        }
    }

    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: 1fr;
            gap: 15px;
            margin: 15px 20px;
        }

        .menu-tree {
            margin: 15px 20px;
        }

        .menu-item {
            padding: 15px 20px;
            flex-direction: column;
            align-items: flex-start;
            gap: 15px;
        }

        .menu-actions {
            width: 100%;
            justify-content: flex-end;
        }

        .header-actions {
            flex-direction: column;
            gap: 10px;
        }

        .header-actions button {
            width: 100%;
        }

        /* 三级菜单移动端优化 */
        .menu-children .menu-children {
            margin: 8px 0 0 0;
            padding: 8px 12px;
            border-left: 2px solid rgba(120, 119, 198, 0.4);
        }

        .menu-children .menu-children .menu-item {
            padding: 8px 12px;
            font-size: 12px;
            min-height: auto;
        }

        .menu-level-3 .submenu-indicator {
            left: -32px;
            width: 24px;
            height: 24px;
        }

        .menu-level-3 .menu-actions .btn-action {
            padding: 4px 6px;
            font-size: 10px;
        }

        .menu-level-3 .menu-name {
            font-size: 13px;
        }

        .menu-level-3 .menu-details {
            gap: 6px;
        }

        .menu-level-3 .menu-detail-item {
            font-size: 10px;
            padding: 2px 4px;
        }
    }

    @media (max-width: 576px) {
        .stat-card {
            padding: 20px 15px;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
        }

        .stat-number {
            font-size: 24px;
        }

        .menu-item {
            padding: 12px 15px;
        }

        .form-row {
            flex-direction: column;
        }

        .form-group {
            width: 100%;
        }
    }

    .stat-card {
        background: linear-gradient(135deg,
            rgba(20, 20, 30, 0.95) 0%,
            rgba(25, 25, 35, 0.9) 50%,
            rgba(30, 30, 40, 0.95) 100%);
        border: 1px solid rgba(120, 119, 198, 0.3);
        border-radius: 16px;
        padding: 25px 20px;
        display: flex;
        align-items: center;
        gap: 18px;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        backdrop-filter: blur(15px);
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.3),
            0 2px 8px rgba(120, 119, 198, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.05);
    }

    .stat-card:hover {
        transform: translateY(-3px);
        border-color: rgba(120, 119, 198, 0.45);
        box-shadow:
            0 12px 40px rgba(0, 0, 0, 0.32),
            0 5px 22px rgba(120, 119, 198, 0.18),
            0 0 25px rgba(120, 119, 198, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.06);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg,
            rgba(120, 119, 198, 0.3),
            rgba(255, 119, 198, 0.3));
        box-shadow: 0 8px 25px rgba(120, 119, 198, 0.3);
        position: relative;
        overflow: hidden;
    }

    .stat-icon::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(120, 119, 198, 0.1) 0%,
            rgba(255, 119, 198, 0.1) 50%,
            rgba(120, 219, 255, 0.1) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .stat-card:hover .stat-icon::before {
        opacity: 1;
    }

    .stat-icon i {
        font-size: 24px;
        color: #fff;
        text-shadow: 0 0 20px rgba(120, 119, 198, 0.8);
        position: relative;
        z-index: 2;
    }

    .stat-content {
        flex: 1;
        min-width: 0;
    }

    .stat-number {
        font-size: 28px;
        font-weight: 700;
        color: rgba(120, 219, 255, 0.9);
        margin-bottom: 4px;
        font-family: 'Orbitron', monospace;
        text-shadow: 0 0 20px rgba(120, 219, 255, 0.3);
        line-height: 1;
    }

    .stat-label {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
        line-height: 1.2;
    }

    /* 菜单树样式 */
    .menu-tree {
        list-style: none;
        padding-left: 0;
        margin: 20px 30px;
    }

    .menu-tree li {
        margin-bottom: 12px;
        background: linear-gradient(135deg,
            rgba(20, 20, 30, 0.95) 0%,
            rgba(25, 25, 35, 0.9) 50%,
            rgba(30, 30, 40, 0.95) 100%);
        border: 1px solid rgba(120, 119, 198, 0.25);
        border-radius: 12px;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        backdrop-filter: blur(15px);
        box-shadow:
            0 4px 20px rgba(0, 0, 0, 0.25),
            0 1px 4px rgba(120, 119, 198, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.03);
        overflow: hidden;
        position: relative;
    }

    .menu-tree li:hover {
        border-color: rgba(120, 119, 198, 0.4);
        transform: translateY(-2px);
        box-shadow:
            0 8px 30px rgba(0, 0, 0, 0.3),
            0 3px 12px rgba(120, 119, 198, 0.15),
            0 0 20px rgba(120, 119, 198, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.05);
    }

    .menu-item {
        padding: 18px 25px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        min-height: 80px;
    }

    .menu-info {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 15px;
        min-width: 0; /* 防止flex项目溢出 */
    }

    .menu-icon-display {
        width: 45px;
        height: 45px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg,
            rgba(120, 119, 198, 0.2),
            rgba(255, 119, 198, 0.2));
        border: 1px solid rgba(120, 119, 198, 0.3);
    }

    .menu-icon-display i {
        font-size: 18px;
        color: rgba(120, 219, 255, 0.9);
        text-shadow: 0 0 15px rgba(120, 219, 255, 0.3);
    }

    .menu-text-info {
        flex: 1;
    }

    .menu-name {
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 8px;
        font-size: 16px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        display: flex;
        align-items: center;
        gap: 10px;
        flex-wrap: wrap;
    }

    .menu-details {
        font-size: 13px;
        color: rgba(255, 255, 255, 0.65);
        line-height: 1.5;
        display: flex;
        align-items: center;
        gap: 15px;
        flex-wrap: wrap;
    }

    .menu-detail-item {
        display: flex;
        align-items: center;
        gap: 5px;
        padding: 2px 8px;
        background: rgba(120, 119, 198, 0.1);
        border-radius: 6px;
        font-size: 12px;
    }

    .menu-detail-item i {
        font-size: 11px;
        opacity: 0.8;
    }

    .menu-actions {
        display: flex;
        gap: 8px;
        align-items: center;
        position: relative;
    }

    /* 子菜单展开指示器 */
    .submenu-indicator {
        position: absolute;
        left: -53px;
        top: 50%;
        transform: translateY(-50%);
        width: 43px;
        height: 42px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #df409de0;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        z-index: 10;
    }

    .submenu-indicator:hover {
        background: #e74aa6e0;
    }

    .submenu-indicator i {
        font-size: 14px;
        color: #ffffff;
        transition: transform 0.3s ease;
        position: relative;
        z-index: 2;
    }

    .submenu-indicator.expanded i {
        transform: rotate(90deg);
    }

    .submenu-count {
        position: absolute;
        top: -4px;
        right: -4px;
        background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        color: white;
        border-radius: 50%;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 9px;
        font-weight: 600;
        box-shadow: 0 2px 6px rgba(255, 107, 107, 0.4);
        z-index: 11;
    }



    .menu-children .menu-tree {
        margin: 0;
    }

    /* 二级菜单样式 */
    .menu-children .menu-item {
        padding: 15px 20px;
        background: linear-gradient(135deg,
            rgba(25, 25, 35, 0.6) 0%,
            rgba(30, 30, 40, 0.6) 100%);

        border-radius: 8px;
        border: none;
        border-top: 1px solid rgba(120, 119, 198, 0.15);
        border-left: 1px solid rgba(120, 119, 198, 0.15);
        border-right: 1px solid rgba(120, 119, 198, 0.15);
        border-bottom: none;
        position: relative;
    }

    .menu-children .menu-item:last-child {
        margin-bottom: 0;
    }

    /* 三级菜单容器样式 */
    .menu-children .menu-children {
        margin: 0 0 20px 0;
        padding: 25px 15px 20px 20px;
        position: relative;
        overflow: visible;
    }

    /* 三级菜单li元素样式 */
    .menu-children .menu-children .menu-tree li {
        background: transparent;
        border: none;
        box-shadow: none;
        backdrop-filter: none;
        margin-bottom: 8px;
    }

    .menu-children .menu-children .menu-tree li:hover {
        background: transparent;
        border: none;
        box-shadow: none;
        transform: none;
    }

    /* 三级菜单项样式 */
    .menu-children .menu-children .menu-item {
        padding: 12px 15px;
        background: linear-gradient(135deg,
            rgba(30, 30, 45, 0.8) 0%,
            rgba(35, 35, 50, 0.8) 100%);
        border: 1px solid rgba(120, 119, 198, 0.4);
        margin: 8px 0 6px 0;
        border-radius: 16px;
        font-size: 14px;
        position: relative;
        min-height: 60px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(10px);
    }

    /* 第一个三级菜单项额外上边距 */
    .menu-children .menu-children .menu-item:first-child {
        margin-top: 12px;
    }

    .menu-children .menu-children .menu-item:last-child {
        margin-bottom: 0;
    }

    .menu-children .menu-children .menu-item:hover {
        background: linear-gradient(135deg,
            rgba(40, 40, 60, 0.9) 0%,
            rgba(45, 45, 65, 0.9) 100%);
        border-color: rgba(120, 119, 198, 0.6);
        transform: translateY(-1px);
        box-shadow: 0 4px 16px rgba(120, 119, 198, 0.2);
    }



    /* 子菜单展开/收起样式 */
    .menu-toggle {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg,
            rgba(120, 119, 198, 0.2),
            rgba(255, 119, 198, 0.2));
        border: 1px solid rgba(120, 119, 198, 0.3);
        cursor: pointer;
        transition: all 0.3s ease;
        margin-right: 10px;
    }

    .menu-toggle:hover {
        background: linear-gradient(135deg,
            rgba(120, 119, 198, 0.3),
            rgba(255, 119, 198, 0.3));
        border-color: rgba(120, 119, 198, 0.5);
        transform: scale(1.05);
    }

    .menu-toggle i {
        font-size: 14px;
        color: rgba(120, 219, 255, 0.9);
        transition: transform 0.3s ease;
    }

    .menu-toggle.expanded i {
        transform: rotate(90deg);
    }

    .menu-children {
        display: none;
        background: linear-gradient(135deg,
            rgba(15, 15, 25, 0.8) 0%,
            rgba(20, 20, 30, 0.8) 100%);
        padding: 20px 25px;
        margin: 0;
        overflow: visible;
    }

    .menu-children.expanded {
        display: block;
    }

    /* 层级样式优化 */
    .menu-level-1 {
        z-index: 100;
    }

    .menu-level-2 {
        z-index: 99;
    }

    .menu-level-3 {
        z-index: 98;
    }

    /* 子菜单展开指示器位置调整 */
    .menu-level-2 .submenu-indicator {
        left: -48px;
        width: 38px;
        height: 38px;
    }

    .menu-level-3 .submenu-indicator {
        left: -43px;
        width: 33px;
        height: 33px;
    }

    /* 三级菜单特殊样式优化 */
    .menu-level-3 .menu-name {
        font-size: 14px;
        font-weight: 500;
    }

    .menu-level-3 .menu-actions .btn-action {
        padding: 5px 8px;
        font-size: 11px;
        margin: 0 1px;
    }

    .menu-level-3 .submenu-indicator {
        left: -35px;
        width: 26px;
        height: 26px;
        background: rgba(120, 119, 198, 0.6);
    }

    .menu-level-3 .submenu-indicator i {
        font-size: 11px;
    }

    .menu-level-3 .submenu-count {
        font-size: 8px;
        top: -2px;
        right: -2px;
    }

    .menu-level-3 .menu-details {
        gap: 6px;
    }

    .menu-level-3 .menu-detail-item {
        font-size: 11px;
        padding: 2px 6px;
    }

    .menu-item-header {
        display: flex;
        align-items: center;
        width: 100%;
    }
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(10px);
    }

    .status-enabled {
        background: linear-gradient(135deg,
            rgba(40, 167, 69, 0.25) 0%,
            rgba(34, 139, 58, 0.25) 100%);
        color: #4ade80;
        border: 1px solid rgba(40, 167, 69, 0.4);
        text-shadow: 0 0 10px rgba(40, 167, 69, 0.3);
    }

    .status-disabled {
        background: linear-gradient(135deg,
            rgba(220, 53, 69, 0.25) 0%,
            rgba(185, 45, 58, 0.25) 100%);
        color: #f87171;
        border: 1px solid rgba(220, 53, 69, 0.4);
        text-shadow: 0 0 10px rgba(220, 53, 69, 0.3);
    }

    .link-type-badge {
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 11px;
        margin-left: 8px;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(8px);
    }

    .type-url {
        background: linear-gradient(135deg, rgba(33, 150, 243, 0.25), rgba(25, 118, 210, 0.25));
        color: #60a5fa;
        border: 1px solid rgba(33, 150, 243, 0.3);
    }

    .type-page {
        background: linear-gradient(135deg, rgba(156, 39, 176, 0.25), rgba(123, 31, 162, 0.25));
        color: #c084fc;
        border: 1px solid rgba(156, 39, 176, 0.3);
    }

    .type-product {
        background: linear-gradient(135deg, rgba(76, 175, 80, 0.25), rgba(56, 142, 60, 0.25));
        color: #4ade80;
        border: 1px solid rgba(76, 175, 80, 0.3);
    }

    .type-solution {
        background: linear-gradient(135deg, rgba(255, 152, 0, 0.25), rgba(245, 124, 0, 0.25));
        color: #fbbf24;
        border: 1px solid rgba(255, 152, 0, 0.3);
    }

    .type-case {
        background: linear-gradient(135deg, rgba(233, 30, 99, 0.25), rgba(194, 24, 91, 0.25));
        color: #f472b6;
        border: 1px solid rgba(233, 30, 99, 0.3);
    }

    .type-news {
        background: linear-gradient(135deg, rgba(0, 150, 136, 0.25), rgba(0, 121, 107, 0.25));
        color: #2dd4bf;
        border: 1px solid rgba(0, 150, 136, 0.3);
    }

    .type-template {
        background: linear-gradient(135deg, rgba(139, 195, 74, 0.25), rgba(104, 159, 56, 0.25));
        color: #a3e635;
        border: 1px solid rgba(139, 195, 74, 0.3);
    }

    /* 表单样式 */
    .form-container {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
        border: 1px solid rgba(120, 119, 198, 0.3);
        border-radius: 12px;
        padding: 30px;
    }
    .link-selector { display: none; margin-top: 10px; }
    .link-selector.show { display: block; }
    .link-options {
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid rgba(120, 119, 198, 0.3);
        border-radius: 8px;
        padding: 10px;
        background: rgba(255, 255, 255, 0.05);
    }
    .link-option {
        padding: 8px 12px;
        cursor: pointer;
        border-radius: 6px;
        margin-bottom: 5px;
        border: 1px solid transparent;
        color: #ffffff;
        transition: all 0.3s ease;
    }
    .link-option:hover { background: rgba(120, 119, 198, 0.2); }
    .link-option.selected {
        background: linear-gradient(135deg, rgba(120, 119, 198, 0.8), rgba(255, 119, 198, 0.6));
        color: white;
        border-color: rgba(120, 219, 255, 0.8);
    }
    .custom-url-input { display: none; }
    .custom-url-input.show { display: block; }
    .form-section {
        background: rgba(255, 255, 255, 0.02);
        border: 1px solid rgba(120, 119, 198, 0.2);
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    .form-section h5 {
        color: #ffffff;
        border-bottom: 2px solid rgba(120, 119, 198, 0.3);
        padding-bottom: 10px;
        margin-bottom: 20px;
    }

    /* 按钮样式 */
    .btn-action {
        padding: 8px 12px;
        border-radius: 6px;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 14px;
        margin: 0 2px;
    }
    .btn-edit {
        background: linear-gradient(135deg, rgba(0, 123, 255, 0.8), rgba(0, 86, 179, 0.8));
        color: white;
    }
    .btn-edit:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    }
    .btn-delete {
        background: linear-gradient(135deg, rgba(220, 53, 69, 0.8), rgba(176, 42, 55, 0.8));
        color: white;
    }
    .btn-delete:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
    }
    .btn-success {
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.8), rgba(32, 134, 55, 0.8));
        color: white;
    }
    .btn-success:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    }
    .btn-warning {
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.8), rgba(255, 158, 0, 0.8));
        color: white;
    }
    .btn-warning:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
    }

    /* 表单样式兼容 */
    .input-group {
        display: flex;
        align-items: center;
        gap: 0;
    }

    .input-group .form-input {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-right: none;
    }

    .input-group-text {
        background: rgba(120, 119, 198, 0.1);
        border: 1px solid rgba(120, 119, 198, 0.3);
        border-left: none;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
        color: rgba(120, 119, 198, 0.9);
        padding: 12px 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 50px;
    }

    /* 自定义链接和链接选择器显示控制 */
    .custom-url-input,
    .link-selector {
        display: none;
        margin-top: 15px;
    }

    .custom-url-input.show,
    .link-selector.show {
        display: block;
    }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    {include file="admin/common/header"}
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            {include file="admin/common/sidebar"}

            <!-- 主要内容 -->
            <main class="main-content">


                <!-- 引用统一消息组件 -->
                {include file="admin/common/message"}

                <!-- 页面内容区域 -->
                <div class="content-body">
                    <div class="news-container">

                        {if !$editMode && !$parentId}
                        <!-- 系统菜单管理主页 -->
                        <div class="list-header">
                            <div class="list-header-content">
                                <div class="list-title-section">
                                    <div class="list-icon">
                                        <i class="fas fa-sitemap"></i>
                                    </div>
                                    <div>
                                        <h1 class="list-title">系统菜单</h1>
                                        <p class="list-subtitle">管理网站导航菜单结构和链接</p>
                                    </div>
                                </div>
                                <div class="header-actions">
                                    <button type="button" class="btn-add-custom" onclick="showAddForm()">
                                        <i class="fas fa-plus"></i>
                                        <span>添加菜单</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        {else}
                        <!-- 编辑/添加模式头部 -->
                        <div class="form-header">
                            <div class="form-header-content">
                                <div class="form-title-section">
                                    <div class="form-icon">
                                        <i class="fas fa-{if $editId > 0}edit{else}plus{/if}"></i>
                                    </div>
                                    <div>
                                        <h1 class="form-title">{if $editId > 0}编辑菜单{elseif $parentId > 0}添加子菜单{else}添加菜单{/if}</h1>
                                        <p class="form-subtitle">{if $editId > 0}修改菜单信息和链接设置{elseif $parentId > 0}为选定的父级菜单添加子菜单项{else}创建新的菜单项{/if}</p>
                                    </div>
                                </div>
                                <a href="/admin/sys-menu" class="btn-back">
                                    <i class="fas fa-arrow-left"></i>
                                    <span>返回列表</span>
                                </a>
                            </div>
                        </div>
                        {/if}

                        {if !$editMode && !$parentId}
                        <!-- 菜单统计卡片 -->
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-list"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="total-menus">0</div>
                                    <div class="stat-label">总菜单数</div>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="enabled-menus">0</div>
                                    <div class="stat-label">启用菜单</div>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-layer-group"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="top-menus">0</div>
                                    <div class="stat-label">顶级菜单</div>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-sitemap"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="sub-menus">0</div>
                                    <div class="stat-label">子菜单</div>
                                </div>
                            </div>
                        </div>

                        <!-- 菜单列表 -->
                        <div class="list-body" id="menu-list-card">
                            <div id="menu-container">
                                <div class="text-center py-4">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2">正在加载菜单数据...</p>
                                </div>
                            </div>
                        </div>
                        {/if}



                        <!-- 菜单编辑表单 -->
                        <div class="form-body" id="menu-form-card" style="display: {if $editMode || $parentId > 0}block{else}none{/if};">
                            <form id="menuForm" onsubmit="return saveMenu(event)" class="news-form">
                                <input type="hidden" name="id" value="{$menu.id ?? 0}">
                                <input type="hidden" name="parent_id" value="{$menu.parent_id ?? $parentId ?? 0}">

                                <div class="form-grid">
                                    <!-- 基本信息 -->
                                    <div class="form-section">
                                        <div class="section-header">
                                            <h3 class="section-title">
                                                <i class="fas fa-info-circle"></i>
                                                基本信息
                                            </h3>
                                        </div>
                                        <div class="form-row" style="margin-top: 20px;">
                                            <div class="form-group">
                                                <label for="name" class="form-label required">
                                                    <i class="fas fa-sitemap"></i>
                                                    菜单名称
                                                </label>
                                                <input type="text" class="form-input" id="name" name="name" value="{$menu.name ?? ''}" required maxlength="50" placeholder="请输入菜单名称">
                                            </div>
                                            <div class="form-group">
                                                <label for="icon" class="form-label">
                                                    <i class="fas fa-icons"></i>
                                                    菜单图标
                                                </label>
                                                <div class="input-group">
                                                    <input type="text" class="form-input" id="icon" name="icon" value="{$menu.icon ?? ''}" placeholder="如：fas fa-home">
                                                    <span class="input-group-text">
                                                        <i id="icon-preview" class="{$menu.icon ?? 'fas fa-question'}"></i>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="parent_id_select" class="form-label">
                                                    <i class="fas fa-sitemap"></i>
                                                    父级菜单
                                                </label>
                                                <select class="form-select" id="parent_id_select" onchange="updateParentId()">
                                                    <option value="0">顶级菜单</option>
                                                    {volist name="parentMenus" id="parent"}
                                                    <option value="{$parent.id}" {if condition="($menu.parent_id ?? $parentId ?? 0) == $parent.id"}selected{/if}>
                                                        {$parent.name}
                                                    </option>
                                                    {/volist}
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label for="sort_order" class="form-label">
                                                    <i class="fas fa-sort"></i>
                                                    排序权重
                                                </label>
                                                <input type="number" class="form-input" id="sort_order" name="sort_order" value="{$menu.sort_order ?? 0}" min="0" placeholder="0">
                                                <div class="form-help">数值越大排序越靠前</div>
                                            </div>
                                        </div>

                                        <div class="form-row">
                                            <div class="checkbox-group">
                                                <label class="checkbox-label">
                                                    <input type="checkbox" name="status" value="1" {if condition="($menu.status ?? 1) == 1"}checked{/if}>
                                                    <span class="checkbox-custom"></span>
                                                    <span class="checkbox-text">
                                                        <i class="fas fa-eye"></i>
                                                        启用菜单
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 链接设置 -->
                                    <div class="form-section">
                                        <div class="section-header">
                                            <h3 class="section-title">
                                                <i class="fas fa-link"></i>
                                                链接设置
                                            </h3>
                                        </div>

                                        <div class="form-row" style="margin-top: 20px;">
                                            <div class="form-group">
                                                <label for="link_type" class="form-label required">
                                                    <i class="fas fa-external-link-alt"></i>
                                                    链接类型
                                                </label>
                                                <select class="form-select" id="link_type" name="link_type" onchange="changeLinkType()" required>
                                                    <option value="">请选择链接类型</option>
                                                    <option value="url" {if condition="($menu.link_type ?? '') == 'url'"}selected{/if}>自定义链接</option>
                                                    <option value="page" {if condition="($menu.link_type ?? '') == 'page'"}selected{/if}>页面</option>
                                                    <option value="product_category" {if condition="($menu.link_type ?? '') == 'product_category'"}selected{/if}>产品分类</option>
                                                    <option value="product" {if condition="($menu.link_type ?? '') == 'product'"}selected{/if}>产品</option>
                                                    <option value="solution" {if condition="($menu.link_type ?? '') == 'solution'"}selected{/if}>解决方案</option>
                                                    <option value="case" {if condition="($menu.link_type ?? '') == 'case'"}selected{/if}>客户案例</option>
                                                    <option value="news_category" {if condition="($menu.link_type ?? '') == 'news_category'"}selected{/if}>新闻分类</option>
                                                    <option value="news" {if condition="($menu.link_type ?? '') == 'news'"}selected{/if}>新闻资讯</option>
                                                    <option value="template" {if condition="($menu.link_type ?? '') == 'template'"}selected{/if}>模板页面</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- 自定义链接输入 -->
                                        <div id="custom-url-input" class="custom-url-input">
                                            <div class="form-group">
                                                <label for="custom_url" class="form-label required">
                                                    <i class="fas fa-globe"></i>
                                                    自定义链接
                                                </label>
                                                <input type="text" class="form-input" id="custom_url" placeholder="如：/about 或 https://example.com">
                                                <div class="form-help">输入完整的URL地址或相对路径</div>
                                            </div>
                                        </div>

                                        <!-- 链接选择器 -->
                                        <div id="link-selector" class="link-selector">
                                            <div class="form-group">
                                                <label class="form-label required">
                                                    <i class="fas fa-mouse-pointer"></i>
                                                    选择链接目标
                                                </label>
                                                <div id="link-options" class="link-options">
                                                    <div class="text-center py-3">
                                                        <span>请先选择链接类型</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <input type="hidden" id="link_value" name="link_value" value="{$menu.link_value ?? ''}">
                                    </div>

                                </div>

                                <!-- 表单操作按钮 -->
                                <div class="form-actions">
                                    <button type="submit" class="btn-submit">
                                        <i class="fas fa-save"></i>
                                        <span>保存菜单</span>
                                    </button>
                                    <button type="button" class="btn-cancel" onclick="hideAddForm()">
                                        <i class="fas fa-times"></i>
                                        <span>取消</span>
                                    </button>
                                </div>
                            </form>
                        </div>

                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- JavaScript -->
    {include file="admin/common/js"}
    
    <script>
    $(document).ready(function() {
        {if !$editMode && !$parentId}
        loadMenus();
        {else}
        updateIconPreview();
        // 只在有链接类型值时才调用changeLinkType
        if ($('#link_type').val()) {
            changeLinkType();
        }
        {/if}

        $('#icon').on('input', updateIconPreview);
    });

    // 显示添加表单
    function showAddForm() {
        $('#menu-list-card').hide();
        $('#menu-form-card').show();
        $('#menuForm')[0].reset();
        $('input[name="id"]').val(0);
        $('input[name="parent_id"]').val(0);
        $('#parent_id_select').val(0);
        // 重置后链接类型为空，清空相关区域
        $('#custom-url-input').removeClass('show');
        $('#link-selector').removeClass('show');
        $('#link-options').html('');
    }

    // 隐藏添加表单
    function hideAddForm() {
        $('#menu-form-card').hide();
        $('#menu-list-card').show();
    }

    // 加载菜单数据
    function loadMenus() {
        $.post('/admin/sys-menu', {action: 'getMenuList'}, function(response) {
            if (response.success) {
                // 保存菜单数据到全局变量
                window.currentMenus = response.data;
                renderMenus(response.data);
                updateStats(response.stats);
            } else {
                $('#menu-container').html('<div class="alert alert-danger">加载失败：' + response.message + '</div>');
            }
        }).fail(function() {
            $('#menu-container').html('<div class="alert alert-danger">网络错误，请稍后重试</div>');
        });
    }

    // 渲染菜单
    function renderMenus(menus) {
        if (menus.length === 0) {
            $('#menu-container').html('<div class="alert alert-info">暂无菜单数据，<button class="btn btn-primary btn-sm" onclick="showAddForm()">点击添加</button></div>');
            return;
        }

        let html = '<ul class="menu-tree level-1">';
        menus.forEach(function(menu) {
            html += renderMenuItem(menu, 1);
        });
        html += '</ul>';

        $('#menu-container').html(html);
    }

    // 渲染单个菜单项
    function renderMenuItem(menu, level = 1) {
        let statusClass = menu.status == 1 ? 'status-enabled' : 'status-disabled';
        let statusText = menu.status == 1 ? '启用' : '禁用';
        let typeClass = 'type-' + menu.link_type;
        let typeText = getLinkTypeText(menu.link_type);

        let html = '<li data-id="' + menu.id + '" class="menu-level-' + level + '">';
        let hasSubmenu = menu.children && menu.children.length > 0;
        html += '<div class="menu-item level-' + level + (hasSubmenu ? '' : ' no-submenu') + '">';

        // 菜单主要信息区域
        html += '<div class="menu-info">';

        // 菜单图标显示
        html += '<div class="menu-icon-display">';
        if (menu.icon) {
            html += '<i class="' + menu.icon + '"></i>';
        } else {
            html += '<i class="fas fa-link"></i>';
        }
        html += '</div>';

        // 菜单文本信息
        html += '<div class="menu-text-info">';
        html += '<div class="menu-name">';
        html += '<span class="menu-title">' + menu.name + '</span>';
        html += '<span class="status-badge ' + statusClass + '">' + statusText + '</span>';
        html += '<span class="link-type-badge ' + typeClass + '">' + typeText + '</span>';
        html += '</div>';
        html += '<div class="menu-details">';

        // 链接信息
        html += '<div class="menu-detail-item">';
        html += '<i class="fas fa-link"></i>';
        html += '<span>' + (menu.url || '未设置') + '</span>';
        html += '</div>';

        // 排序信息
        html += '<div class="menu-detail-item">';
        html += '<i class="fas fa-sort"></i>';
        html += '<span>排序: ' + menu.sort_order + '</span>';
        html += '</div>';

        // 子菜单数量
        if (menu.children && menu.children.length > 0) {
            html += '<div class="menu-detail-item">';
            html += '<i class="fas fa-sitemap"></i>';
            html += '<span>' + menu.children.length + '个子菜单</span>';
            html += '</div>';
        }

        html += '</div>';
        html += '</div>';
        html += '</div>';

        // 操作按钮区域
        html += '<div class="menu-actions">';
        html += '<a href="/admin/sys-menu?parent_id=' + menu.id + '" class="btn-action btn-success" title="添加子菜单">';
        html += '<i class="fas fa-plus"></i>';
        html += '</a>';
        html += '<a href="/admin/sys-menu?edit=' + menu.id + '" class="btn-action btn-edit" title="编辑">';
        html += '<i class="fas fa-edit"></i>';
        html += '</a>';
        html += '<button class="btn-action btn-warning" onclick="toggleStatus(' + menu.id + ', ' + (menu.status == 1 ? 0 : 1) + ')" title="' + (menu.status == 1 ? '禁用' : '启用') + '">';
        html += '<i class="fas fa-' + (menu.status == 1 ? 'eye-slash' : 'eye') + '"></i>';
        html += '</button>';
        html += '<button class="btn-action btn-delete" onclick="checkAndDelete(' + menu.id + ', \'' + menu.name.replace(/'/g, '\\\'') + '\')" title="删除">';
        html += '<i class="fas fa-trash"></i>';
        html += '</button>';

        // 子菜单展开指示器（只在有子菜单时显示）
        if (menu.children && menu.children.length > 0) {
            html += '<div class="submenu-indicator" onclick="toggleSubmenu(this)" title="展开/收起子菜单">';
            html += '<i class="fas fa-chevron-right"></i>';
            html += '<span class="submenu-count">' + menu.children.length + '</span>';
            html += '</div>';
        }

        html += '</div>';
        html += '</div>';

        // 渲染子菜单
        if (menu.children && menu.children.length > 0) {
            html += '<div class="menu-children level-' + (level + 1) + '">';
            html += '<ul class="menu-tree level-' + (level + 1) + '">';
            menu.children.forEach(function(child) {
                html += renderMenuItem(child, level + 1);
            });
            html += '</ul>';
            html += '</div>';
        }

        html += '</li>';
        return html;
    }

    // 获取链接类型文本
    function getLinkTypeText(type) {
        const types = {
            'url': '链接', 'page': '页面', 'product': '产品', 'solution': '方案',
            'case': '案例', 'news': '新闻', 'template': '模板'
        };
        return types[type] || type;
    }

    // 更新统计数据
    function updateStats(stats) {
        $('#total-menus').text(stats.total);
        $('#enabled-menus').text(stats.enabled);
        $('#top-menus').text(stats.top_level);
        $('#sub-menus').text(stats.sub_level);
    }

    // 更新图标预览
    function updateIconPreview() {
        const icon = $('#icon').val() || 'fas fa-question';
        $('#icon-preview').attr('class', icon);
    }

    // 更新父级菜单
    function updateParentId() {
        $('input[name="parent_id"]').val($('#parent_id_select').val());
    }

    // 改变链接类型
    function changeLinkType() {
        const linkType = $('#link_type').val();

        $('#custom-url-input').removeClass('show');
        $('#link-selector').removeClass('show');

        if (linkType === 'url') {
            $('#custom-url-input').addClass('show');
            $('#custom_url').val($('#link_value').val());
        } else if (linkType && linkType !== 'url' && linkType !== '') {
            $('#link-selector').addClass('show');
            loadLinkOptions(linkType);
        } else if (!linkType || linkType === '') {
            // 清空链接选项区域，不显示错误信息
            $('#link-options').html('');
        }
    }

    // 加载链接选项
    function loadLinkOptions(type) {
        $('#link-options').html('<div class="text-center py-3"><div class="spinner-border spinner-border-sm"></div><span class="ms-2">加载中...</span></div>');
        
        $.post('/admin/sys-menu', {action: 'getLinkData', type: type}, function(response) {
            if (response.success) {
                renderLinkOptions(response.data, type);
            } else {
                $('#link-options').html('<div class="alert alert-warning">加载失败：' + response.message + '</div>');
            }
        });
    }

    // 渲染链接选项
    function renderLinkOptions(data, type) {
        if (data.length === 0) {
            $('#link-options').html('<div class="alert alert-info">暂无可选项</div>');
            return;
        }

        let html = '';
        const currentValue = $('#link_value').val();
        
        data.forEach(function(item) {
            const isSelected = currentValue === item.slug || currentValue === item.id;
            const dataValue = type === 'page' ? item.id : item.slug;
            html += '<div class="link-option' + (isSelected ? ' selected' : '') + '" data-value="' + dataValue + '" onclick="selectLinkOption(this)">';
            html += '<strong>' + item.name + '</strong>';
            if (item.slug) {
                // 根据类型显示正确的路径
                let displayPath = '';
                if (type === 'page') {
                    displayPath = item.slug; // 页面直接显示slug（如 /、/contact、/products）
                } else if (type === 'template') {
                    displayPath = '/page/' + item.slug; // 模板页面使用/page/前缀
                } else if (type === 'product_category') {
                    displayPath = '/products/' + item.slug; // 产品分类
                } else if (type === 'product') {
                    displayPath = '/products/' + item.slug; // 具体产品
                } else if (type === 'solution') {
                    displayPath = '/solutions/' + item.slug;
                } else if (type === 'case') {
                    displayPath = '/cases/' + item.slug;
                } else if (type === 'news_category') {
                    displayPath = '/news/category/' + item.slug; // 新闻分类
                } else if (type === 'news') {
                    displayPath = '/news/' + item.slug; // 具体新闻
                }
                html += '<br><small class="text-muted">' + displayPath + '</small>';
            }
            html += '</div>';
        });
        
        $('#link-options').html(html);
    }

    // 选择链接选项
    function selectLinkOption(element) {
        $('.link-option').removeClass('selected');
        $(element).addClass('selected');
        $('#link_value').val($(element).data('value'));
    }

    // 保存菜单
    function saveMenu(event) {
        event.preventDefault();
        
        if ($('#link_type').val() === 'url') {
            $('#link_value').val($('#custom_url').val());
        }
        
        if (!$('#name').val().trim()) {
            showMessage('请输入菜单名称', 'warning');
            $('#name').focus();
            return false;
        }

        if (!$('#link_type').val()) {
            showMessage('请选择链接类型', 'warning');
            $('#link_type').focus();
            return false;
        }

        if (!$('#link_value').val().trim()) {
            showMessage('请设置链接值', 'warning');
            $('#link_value').focus();
            return false;
        }
        
        const formData = $('#menuForm').serialize() + '&action=save';
        
        $.post('/admin/sys-menu', formData, function(response) {
            if (response.success) {
                showMessage(response.message || '保存成功', 'success');
                setTimeout(() => {
                    window.location.href = '/admin/sys-menu';
                }, 1500);
            } else {
                showMessage(response.message || '保存失败', 'error');
            }
        }).fail(function() {
            showMessage('网络错误，请重试', 'error');
        });
        
        return false;
    }

    // 切换菜单状态
    function toggleStatus(id, status) {
        $.post('/admin/sys-menu', {action: 'updateStatus', id: id, status: status}, function(response) {
            if (response.success) {
                showMessage(response.message || '状态更新成功', 'success');
                loadMenus();
            } else {
                showMessage(response.message || '操作失败', 'error');
            }
        }).fail(function() {
            showMessage('网络错误，请重试', 'error');
        });
    }



    // 刷新菜单
    function refreshMenus() {
        loadMenus();
    }

    // 检查并删除菜单
    function checkAndDelete(id, name) {
        // 先检查是否有子菜单
        const menuItem = findMenuById(id);
        if (menuItem && menuItem.children && menuItem.children.length > 0) {
            // 有子菜单，显示提示
            const childNames = menuItem.children.map(child => child.name).join('、');
            showMessage('请先删除子菜单：' + childNames, 'error');
            return;
        }

        // 没有子菜单，正常删除
        deleteItem(id, name, '/admin/sys-menu?action=delete&id=' + id);
    }

    // 根据ID查找菜单项
    function findMenuById(id) {
        function searchInMenus(menus) {
            for (let menu of menus) {
                if (menu.id == id) {
                    return menu;
                }
                if (menu.children && menu.children.length > 0) {
                    const found = searchInMenus(menu.children);
                    if (found) return found;
                }
            }
            return null;
        }

        return searchInMenus(window.currentMenus || []);
    }

    // showMessage函数已移至message.html统一管理

    // 展开/收起子菜单
    function toggleSubmenu(toggleBtn) {
        const $indicator = $(toggleBtn);
        const $menuItem = $indicator.closest('li');
        const $submenu = $menuItem.find('> .menu-children');
        const $icon = $indicator.find('i');

        if ($submenu.hasClass('expanded')) {
            // 收起子菜单
            $submenu.removeClass('expanded').slideUp(300);
            $indicator.removeClass('expanded');
            $icon.css('transform', 'rotate(0deg)');
        } else {
            // 展开子菜单
            $submenu.addClass('expanded').slideDown(300);
            $indicator.addClass('expanded');
            $icon.css('transform', 'rotate(90deg)');
        }
    }


    </script>
</body>
</html>

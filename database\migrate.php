<?php
/**
 * 三只鱼网络科技 | 开发者：韩总 | 创建时间：2024-12-19
 * 文件描述：数据库迁移管理工具 - ThinkPHP6企业级应用
 * 技术栈：PHP 8.0+ + ThinkPHP6 + MySQL + Redis
 * 版权所有：三只鱼网络科技有限公司
 */

require_once __DIR__ . '/../vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

// 初始化ThinkPHP
$app = new \think\App();
$app->initialize();

class DatabaseMigrator
{
    private $backupDir;
    private $sqlFile;
    
    public function __construct()
    {
        $this->backupDir = __DIR__ . '/backups';
        $this->sqlFile = __DIR__ . '/../website.sql';
        
        // 确保备份目录存在
        if (!is_dir($this->backupDir)) {
            mkdir($this->backupDir, 0755, true);
        }
    }
    
    /**
     * 执行完整的数据库迁移
     */
    public function migrate($options = [])
    {
        $force = $options['force'] ?? false;
        $backup = $options['backup'] ?? true;
        $verify = $options['verify'] ?? true;
        
        echo "=== 三只鱼网络科技 - 数据库迁移工具 ===\n\n";
        
        try {
            // 1. 检查SQL文件
            $this->checkSqlFile();
            
            // 2. 备份现有数据库
            if ($backup) {
                $this->backupDatabase();
            }
            
            // 3. 检查数据库连接
            $this->checkDatabaseConnection();
            
            // 4. 分析现有数据
            $existingData = $this->analyzeExistingData();
            
            // 5. 确认迁移
            if (!$force && !$this->confirmMigration($existingData)) {
                echo "迁移已取消\n";
                return false;
            }
            
            // 6. 执行迁移
            $this->executeMigration($existingData, $options);
            
            // 7. 验证迁移结果
            if ($verify) {
                $this->verifyMigration();
            }
            
            echo "\n🎉 数据库迁移成功完成！\n";
            return true;
            
        } catch (Exception $e) {
            echo "❌ 迁移失败: " . $e->getMessage() . "\n";
            echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
            
            // 如果有备份，提示恢复
            if ($backup && $this->hasRecentBackup()) {
                echo "\n💡 可以使用以下命令恢复数据库：\n";
                echo "php database/migrate.php --restore\n";
            }
            
            return false;
        }
    }
    
    /**
     * 检查SQL文件
     */
    private function checkSqlFile()
    {
        echo "检查SQL文件...\n";
        
        if (!file_exists($this->sqlFile)) {
            throw new Exception("SQL文件不存在: {$this->sqlFile}");
        }
        
        $size = filesize($this->sqlFile);
        if ($size < 1000) {
            throw new Exception("SQL文件太小，可能不完整");
        }
        
        echo "✅ SQL文件检查通过，大小: " . round($size / 1024, 2) . " KB\n";
    }
    
    /**
     * 备份数据库
     */
    private function backupDatabase()
    {
        echo "备份现有数据库...\n";
        
        $dbConfig = Config::get('database.connections.mysql');
        $dbName = $dbConfig['database'];
        $backupFile = $this->backupDir . '/backup_' . date('Y-m-d_H-i-s') . '.sql';
        
        // 使用mysqldump备份
        $command = sprintf(
            'mysqldump -h%s -u%s -p%s %s > %s',
            $dbConfig['hostname'],
            $dbConfig['username'],
            $dbConfig['password'],
            $dbName,
            $backupFile
        );
        
        // Windows环境处理
        if (PHP_OS_FAMILY === 'Windows') {
            $command = str_replace('>', '| Out-File -FilePath', $command);
        }
        
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            echo "⚠️  自动备份失败，继续迁移...\n";
        } else {
            echo "✅ 数据库备份完成: " . basename($backupFile) . "\n";
        }
    }
    
    /**
     * 检查数据库连接
     */
    private function checkDatabaseConnection()
    {
        echo "检查数据库连接...\n";
        
        try {
            $result = Db::query('SELECT VERSION() as version');
            $version = $result[0]['version'] ?? 'Unknown';
            echo "✅ 数据库连接正常，版本: {$version}\n";
        } catch (Exception $e) {
            throw new Exception("数据库连接失败: " . $e->getMessage());
        }
    }
    
    /**
     * 分析现有数据
     */
    private function analyzeExistingData()
    {
        echo "分析现有数据...\n";
        
        $data = [
            'tables' => [],
            'records' => [],
            'total_records' => 0
        ];
        
        // 获取所有表
        $tables = Db::query('SHOW TABLES');
        $dbName = Config::get('database.connections.mysql.database');
        $tableKey = "Tables_in_{$dbName}";
        
        foreach ($tables as $table) {
            $tableName = $table[$tableKey];
            $data['tables'][] = $tableName;
            
            // 统计记录数
            try {
                $count = Db::table($tableName)->count();
                $data['records'][$tableName] = $count;
                $data['total_records'] += $count;
            } catch (Exception $e) {
                $data['records'][$tableName] = 0;
            }
        }
        
        echo "✅ 发现 " . count($data['tables']) . " 个表，总计 {$data['total_records']} 条记录\n";
        
        return $data;
    }
    
    /**
     * 确认迁移
     */
    private function confirmMigration($existingData)
    {
        if (empty($existingData['tables'])) {
            echo "✅ 数据库为空，可以安全迁移\n";
            return true;
        }
        
        echo "\n⚠️  检测到现有数据:\n";
        foreach ($existingData['records'] as $table => $count) {
            if ($count > 0) {
                echo "  - {$table}: {$count} 条记录\n";
            }
        }
        
        echo "\n此操作将重建数据库结构，可能会影响现有数据。\n";
        echo "是否继续? (y/N): ";
        
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        return strtolower(trim($line)) === 'y';
    }
    
    /**
     * 执行迁移
     */
    private function executeMigration($existingData, $options = [])
    {
        echo "执行数据库迁移...\n";
        
        // 读取SQL文件
        $sql = file_get_contents($this->sqlFile);
        
        // 处理SQL语句
        $sql = $this->preprocessSql($sql);
        
        // 分割SQL语句
        $statements = $this->splitSqlStatements($sql);
        
        $successCount = 0;
        $errorCount = 0;
        
        // 开始事务
        Db::startTrans();
        
        try {
            $createdTables = 0;
            $skippedTables = 0;
            $skipExisting = $options['skip_existing'] ?? false;
            $skipNextCreate = false;

            foreach ($statements as $statement) {
                if (empty(trim($statement))) {
                    continue;
                }

                // 检查是否是DROP TABLE语句
                if (preg_match('/DROP TABLE IF EXISTS\s+`?(\w+)`?/i', $statement, $matches)) {
                    $tableName = $matches[1];
                    if ($this->tableExists($tableName)) {
                        if ($skipExisting) {
                            echo "  ⚠️  表 {$tableName} 已存在，跳过重建\n";
                            $skippedTables++;
                            $skipNextCreate = true;
                            continue;
                        } else {
                            echo "  ⚠️  表 {$tableName} 已存在，将被重建\n";
                        }
                    }
                }

                // 检查是否是CREATE TABLE语句
                if (preg_match('/CREATE TABLE\s+`?(\w+)`?/i', $statement, $matches)) {
                    $tableName = $matches[1];
                    if ($skipNextCreate) {
                        $skipNextCreate = false;
                        continue;
                    }
                    echo "  ✅ 创建表 {$tableName}\n";
                    $createdTables++;
                }

                try {
                    Db::execute($statement);
                    $successCount++;

                    // 显示进度
                    if ($successCount % 10 === 0) {
                        echo "  已执行 {$successCount} 条语句...\n";
                    }

                } catch (Exception $e) {
                    // 对于某些可忽略的错误，记录但继续
                    if ($this->isIgnorableError($e->getMessage())) {
                        echo "  ⚠️  忽略错误: " . substr($statement, 0, 50) . "...\n";
                        continue;
                    }

                    throw $e;
                }
            }
            
            // 提交事务
            Db::commit();
            
            echo "✅ 迁移完成，成功执行 {$successCount} 条语句";
            if ($createdTables > 0 || $skippedTables > 0) {
                echo "，创建 {$createdTables} 个新表，跳过 {$skippedTables} 个已存在的表";
            }
            echo "\n";
            
        } catch (Exception $e) {
            // 回滚事务
            Db::rollback();
            throw new Exception("SQL执行失败: " . $e->getMessage());
        }
    }
    
    /**
     * 预处理SQL
     */
    private function preprocessSql($sql)
    {
        // 移除注释
        $sql = preg_replace('/--.*$/m', '', $sql);
        $sql = preg_replace('/\/\*.*?\*\//s', '', $sql);
        
        // 移除多余的空白
        $sql = preg_replace('/\s+/', ' ', $sql);
        
        return trim($sql);
    }
    
    /**
     * 分割SQL语句
     */
    private function splitSqlStatements($sql)
    {
        // 简单的SQL语句分割
        $statements = explode(';', $sql);
        
        return array_filter(array_map('trim', $statements));
    }
    
    /**
     * 检查表是否存在
     */
    private function tableExists($tableName)
    {
        try {
            $dbConfig = Config::get('database.connections.mysql');
            $dbName = $dbConfig['database'];
            $result = Db::query("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = '{$dbName}' AND table_name = '{$tableName}'");
            return $result[0]['count'] > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 检查是否为可忽略的错误
     */
    private function isIgnorableError($error)
    {
        $ignorableErrors = [
            'Table already exists',
            'Duplicate key name',
            'Duplicate entry',
            'already exists',
            'Table \'san.admin_users\' doesn\'t exist',
            'Unknown table',
            'Can\'t DROP'
        ];

        foreach ($ignorableErrors as $ignorable) {
            if (strpos($error, $ignorable) !== false) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * 验证迁移结果
     */
    private function verifyMigration()
    {
        echo "验证迁移结果...\n";
        
        // 检查关键表是否存在
        $requiredTables = [
            'admin_users', 'products', 'product_categories', 
            'news', 'cases', 'images', 'site_settings'
        ];
        
        $missingTables = [];
        foreach ($requiredTables as $table) {
            try {
                Db::query("SHOW TABLES LIKE '{$table}'");
                $result = Db::query("SHOW TABLES LIKE '{$table}'");
                if (empty($result)) {
                    $missingTables[] = $table;
                }
            } catch (Exception $e) {
                $missingTables[] = $table;
            }
        }
        
        if (!empty($missingTables)) {
            throw new Exception("缺少关键表: " . implode(', ', $missingTables));
        }
        
        echo "✅ 迁移验证通过\n";
    }
    
    /**
     * 检查是否有最近的备份
     */
    private function hasRecentBackup()
    {
        if (!is_dir($this->backupDir)) {
            return false;
        }
        
        $files = glob($this->backupDir . '/backup_*.sql');
        if (empty($files)) {
            return false;
        }
        
        // 检查最新备份是否在1小时内
        $latestFile = max($files);
        $fileTime = filemtime($latestFile);
        
        return (time() - $fileTime) < 3600; // 1小时
    }
}

// 命令行处理
if (php_sapi_name() === 'cli') {
    $options = [];
    
    // 解析命令行参数
    for ($i = 1; $i < $argc; $i++) {
        switch ($argv[$i]) {
            case '--force':
                $options['force'] = true;
                break;
            case '--no-backup':
                $options['backup'] = false;
                break;
            case '--no-verify':
                $options['verify'] = false;
                break;
            case '--skip-existing':
                $options['skip_existing'] = true;
                break;
            case '--help':
                echo "数据库迁移工具使用说明:\n";
                echo "php database/migrate.php [选项]\n\n";
                echo "选项:\n";
                echo "  --force          强制执行，不询问确认\n";
                echo "  --no-backup      跳过数据库备份\n";
                echo "  --no-verify      跳过迁移验证\n";
                echo "  --skip-existing  跳过已存在的表，不重建\n";
                echo "  --help           显示此帮助信息\n";
                exit(0);
        }
    }
    
    $migrator = new DatabaseMigrator();
    $success = $migrator->migrate($options);
    
    exit($success ? 0 : 1);
}

/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * QiyeDIY企业建站系统 - 性能优化组合式函数
 */

import { ref, onMounted, onUnmounted } from 'vue'

interface PerformanceMetrics {
  fcp?: number // First Contentful Paint
  lcp?: number // Largest Contentful Paint
  fid?: number // First Input Delay
  cls?: number // Cumulative Layout Shift
  ttfb?: number // Time to First Byte
  domContentLoaded?: number
  loadComplete?: number
}

interface LazyLoadOptions {
  root?: Element | null
  rootMargin?: string
  threshold?: number | number[]
}

/**
 * 性能优化组合式函数
 */
export const usePerformance = () => {
  const metrics = ref<PerformanceMetrics>({})
  const isLoading = ref(true)
  const loadingProgress = ref(0)
  
  /**
   * 收集性能指标
   */
  const collectMetrics = () => {
    if (typeof window === 'undefined') return
    
    // 获取导航时间
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    if (navigation) {
      metrics.value.ttfb = navigation.responseStart - navigation.requestStart
      metrics.value.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.navigationStart
      metrics.value.loadComplete = navigation.loadEventEnd - navigation.navigationStart
    }
    
    // 获取Paint时间
    const paintEntries = performance.getEntriesByType('paint')
    paintEntries.forEach((entry) => {
      if (entry.name === 'first-contentful-paint') {
        metrics.value.fcp = entry.startTime
      }
    })
    
    // 使用Performance Observer收集其他指标
    if ('PerformanceObserver' in window) {
      // LCP (Largest Contentful Paint)
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        metrics.value.lcp = lastEntry.startTime
      })
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
      
      // FID (First Input Delay)
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          metrics.value.fid = entry.processingStart - entry.startTime
        })
      })
      fidObserver.observe({ entryTypes: ['first-input'] })
      
      // CLS (Cumulative Layout Shift)
      let clsValue = 0
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
            metrics.value.cls = clsValue
          }
        })
      })
      clsObserver.observe({ entryTypes: ['layout-shift'] })
    }
  }
  
  /**
   * 发送性能数据到服务器
   */
  const sendMetrics = async () => {
    if (Object.keys(metrics.value).length === 0) return
    
    try {
      await $fetch('/api/analytics/performance', {
        method: 'POST',
        body: {
          url: window.location.href,
          userAgent: navigator.userAgent,
          metrics: metrics.value,
          timestamp: Date.now()
        }
      })
    } catch (error) {
      console.warn('发送性能数据失败:', error)
    }
  }
  
  /**
   * 图片懒加载
   */
  const useLazyLoad = (options: LazyLoadOptions = {}) => {
    const {
      root = null,
      rootMargin = '50px',
      threshold = 0.1
    } = options
    
    const observer = ref<IntersectionObserver | null>(null)
    const loadedImages = ref(new Set<string>())
    
    const initLazyLoad = () => {
      if (typeof window === 'undefined' || !('IntersectionObserver' in window)) return
      
      observer.value = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement
            const src = img.dataset.src
            
            if (src && !loadedImages.value.has(src)) {
              // 创建新图片对象预加载
              const newImg = new Image()
              newImg.onload = () => {
                img.src = src
                img.classList.add('loaded')
                loadedImages.value.add(src)
                observer.value?.unobserve(img)
              }
              newImg.onerror = () => {
                img.src = '/images/image-error.png'
                img.classList.add('error')
                observer.value?.unobserve(img)
              }
              newImg.src = src
            }
          }
        })
      }, { root, rootMargin, threshold })
    }
    
    const observeImage = (img: HTMLImageElement) => {
      if (observer.value) {
        observer.value.observe(img)
      }
    }
    
    const unobserveImage = (img: HTMLImageElement) => {
      if (observer.value) {
        observer.value.unobserve(img)
      }
    }
    
    const destroy = () => {
      if (observer.value) {
        observer.value.disconnect()
        observer.value = null
      }
    }
    
    return {
      initLazyLoad,
      observeImage,
      unobserveImage,
      destroy
    }
  }
  
  /**
   * 资源预加载
   */
  const preloadResource = (href: string, as: string, type?: string) => {
    if (typeof document === 'undefined') return
    
    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = href
    link.as = as
    if (type) link.type = type
    
    document.head.appendChild(link)
  }
  
  /**
   * 预加载关键图片
   */
  const preloadImages = (urls: string[]) => {
    urls.forEach(url => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = url
      link.as = 'image'
      document.head.appendChild(link)
    })
  }
  
  /**
   * 代码分割和动态导入
   */
  const loadComponent = async (componentPath: string) => {
    try {
      const component = await import(componentPath)
      return component.default || component
    } catch (error) {
      console.error(`加载组件失败: ${componentPath}`, error)
      return null
    }
  }
  
  /**
   * 防抖函数
   */
  const debounce = <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeout)
      timeout = setTimeout(() => func(...args), wait)
    }
  }
  
  /**
   * 节流函数
   */
  const throttle = <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }
  
  /**
   * 监听页面可见性
   */
  const usePageVisibility = () => {
    const isVisible = ref(true)
    
    const handleVisibilityChange = () => {
      isVisible.value = !document.hidden
    }
    
    onMounted(() => {
      if (typeof document !== 'undefined') {
        document.addEventListener('visibilitychange', handleVisibilityChange)
        isVisible.value = !document.hidden
      }
    })
    
    onUnmounted(() => {
      if (typeof document !== 'undefined') {
        document.removeEventListener('visibilitychange', handleVisibilityChange)
      }
    })
    
    return { isVisible }
  }
  
  /**
   * 网络状态监听
   */
  const useNetworkStatus = () => {
    const isOnline = ref(true)
    const connectionType = ref<string>('')
    const effectiveType = ref<string>('')
    
    const updateNetworkStatus = () => {
      isOnline.value = navigator.onLine
      
      if ('connection' in navigator) {
        const connection = (navigator as any).connection
        connectionType.value = connection.type || ''
        effectiveType.value = connection.effectiveType || ''
      }
    }
    
    onMounted(() => {
      if (typeof window !== 'undefined') {
        updateNetworkStatus()
        window.addEventListener('online', updateNetworkStatus)
        window.addEventListener('offline', updateNetworkStatus)
        
        if ('connection' in navigator) {
          (navigator as any).connection.addEventListener('change', updateNetworkStatus)
        }
      }
    })
    
    onUnmounted(() => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('online', updateNetworkStatus)
        window.removeEventListener('offline', updateNetworkStatus)
        
        if ('connection' in navigator) {
          (navigator as any).connection.removeEventListener('change', updateNetworkStatus)
        }
      }
    })
    
    return {
      isOnline,
      connectionType,
      effectiveType
    }
  }
  
  /**
   * 内存使用监控
   */
  const useMemoryMonitor = () => {
    const memoryInfo = ref<any>({})
    
    const updateMemoryInfo = () => {
      if ('memory' in performance) {
        memoryInfo.value = {
          usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
          totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
          jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
        }
      }
    }
    
    onMounted(() => {
      updateMemoryInfo()
      const interval = setInterval(updateMemoryInfo, 5000)
      
      onUnmounted(() => {
        clearInterval(interval)
      })
    })
    
    return { memoryInfo }
  }
  
  /**
   * 加载进度监控
   */
  const useLoadingProgress = () => {
    const updateProgress = (progress: number) => {
      loadingProgress.value = Math.min(100, Math.max(0, progress))
    }
    
    const setLoading = (loading: boolean) => {
      isLoading.value = loading
      if (!loading) {
        loadingProgress.value = 100
      }
    }
    
    return {
      loadingProgress: readonly(loadingProgress),
      isLoading: readonly(isLoading),
      updateProgress,
      setLoading
    }
  }
  
  /**
   * 初始化性能监控
   */
  const initPerformanceMonitoring = () => {
    onMounted(() => {
      // 延迟收集指标，确保页面完全加载
      setTimeout(() => {
        collectMetrics()
        
        // 页面卸载时发送数据
        window.addEventListener('beforeunload', sendMetrics)
      }, 1000)
    })
    
    onUnmounted(() => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('beforeunload', sendMetrics)
      }
    })
  }
  
  return {
    metrics: readonly(metrics),
    collectMetrics,
    sendMetrics,
    useLazyLoad,
    preloadResource,
    preloadImages,
    loadComponent,
    debounce,
    throttle,
    usePageVisibility,
    useNetworkStatus,
    useMemoryMonitor,
    useLoadingProgress,
    initPerformanceMonitoring
  }
}

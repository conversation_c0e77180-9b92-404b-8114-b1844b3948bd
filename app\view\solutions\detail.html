{assign name="pageTitle" value="$pageData.pageTitle" /}

{include file="common/header"}

<!-- 🚀 量子科技风格页面头部 -->
<section class="quantum-detail-hero">
    <!-- 多维背景系统 -->
    <div class="quantum-bg-system">
        <div class="bg-primary-layer"></div>
        <div class="holographic-grid">
            <div class="grid-matrix"></div>
            <div class="grid-scanlines"></div>
        </div>
        <div class="quantum-particles"></div>
        <div class="energy-pulse-waves">
            <div class="pulse-wave wave-alpha"></div>
            <div class="pulse-wave wave-beta"></div>
        </div>
        <div class="depth-gradient-mask"></div>
    </div>

    <div class="container" style="max-width: 1400px;">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="quantum-detail-content">
                    <!-- 面包屑导航 -->
                    <nav class="quantum-breadcrumb" data-aos="fade-up" data-aos-delay="100">
                        <div class="breadcrumb-quantum">
                            <a href="/" class="breadcrumb-link">
                                <i class="fas fa-home"></i>
                                <span>首页</span>
                            </a>
                            <div class="breadcrumb-separator">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                            <a href="/solutions" class="breadcrumb-link">
                                <i class="fas fa-lightbulb"></i>
                                <span>解决方案</span>
                            </a>
                            <div class="breadcrumb-separator">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                            <span class="breadcrumb-current">{$solution.name}</span>
                        </div>
                    </nav>

                    <!-- 解决方案标题区域 -->
                    <div class="solution-hero-title" data-aos="fade-up" data-aos-delay="200">
                        <div class="solution-icon-quantum">
                            {switch name="$solution.name"}
                                {case value="社交分销解决方案"}
                                    <i class="fas fa-share-alt"></i>
                                {/case}
                                {case value="智能多门店管理"}
                                    <i class="fas fa-store"></i>
                                {/case}
                                {case value="大型多商户平台"}
                                    <i class="fas fa-building"></i>
                                {/case}
                                {case value="大货批发系统"}
                                    <i class="fas fa-boxes"></i>
                                {/case}
                                {case value="平台级供货商系统"}
                                    <i class="fas fa-truck"></i>
                                {/case}
                                {case value="本地生活服务平台"}
                                    <i class="fas fa-map-marker-alt"></i>
                                {/case}
                                {default /}
                                    <i class="fas fa-lightbulb"></i>
                            {/switch}
                            <div class="icon-energy-ring"></div>
                        </div>
                        <h1 class="solution-mega-title">
                            <span class="title-glow-effect">{$solution.name}</span>
                        </h1>
                        <p class="solution-hero-subtitle">
                            {$solution.short_description|default='专业的解决方案，助力企业数字化转型'}
                        </p>
                        <div class="title-energy-line"></div>
                    </div>

                    <!-- 快速导航 -->
                    <div class="quantum-quick-nav" data-aos="fade-up" data-aos-delay="300">
                        <a href="#solution-overview" class="nav-quantum-btn">
                            <span>方案概述</span>
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="#solution-features" class="nav-quantum-btn">
                            <span>核心特性</span>
                            <i class="fas fa-star"></i>
                        </a>
                        <a href="#solution-process" class="nav-quantum-btn">
                            <span>实施流程</span>
                            <i class="fas fa-cogs"></i>
                        </a>
                        <a href="#solution-contact" class="nav-quantum-btn">
                            <span>立即咨询</span>
                            <i class="fas fa-phone"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 滚动指示器 -->
    <div class="scroll-indicator" data-aos="fade-up" data-aos-delay="600">
        <div class="scroll-icon">
            <i class="fas fa-chevron-down"></i>
        </div>
        <div class="scroll-text">探索详情</div>
    </div>
</section>

<!-- 🌟 解决方案详情主体 -->
<section class="quantum-solution-detail">
    <div class="container" style="max-width: 1400px;">
        <div class="row justify-content-center">
            <!-- 主要内容 -->
            <div class="col-lg-8 col-md-12">

                <!-- 方案概述 -->
                <div id="solution-overview" class="quantum-content-block" data-aos="fade-up" data-aos-delay="100">
                    <div class="block-header">
                        <div class="block-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <h2 class="block-title">方案概述</h2>
                        <div class="block-energy-line"></div>
                    </div>
                    <div class="block-content">
                        <div class="overview-grid">
                            <div class="overview-visual">
                                <div class="solution-image-quantum">
                                    <img src="{$solution.image}" alt="{$solution.name}" onerror="this.src='/static/images/solutions/enterprise-digital.jpg'; this.onerror=null;">
                                    <div class="image-overlay">
                                        <div class="overlay-pattern"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="overview-content">
                                <div class="solution-description-rich">
                                    {$solution.description|raw}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 核心特性 -->
                <div id="solution-features" class="quantum-content-block" data-aos="fade-up" data-aos-delay="200">
                    <div class="block-header">
                        <div class="block-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <h2 class="block-title">核心特性</h2>
                        <div class="block-energy-line"></div>
                    </div>
                    <div class="block-content">
                        <div class="features-quantum-grid">
                            {if condition="$solution.features"}
                                {php}
                                    // 检查features是否已经是数组
                                    if (is_array($solution['features'])) {
                                        $features = $solution['features'];
                                    } else {
                                        $features = json_decode($solution['features'], true);
                                    }
                                    if (!$features) $features = ['专业定制', '安全可靠', '高效实施', '持续支持', '技术领先'];
                                {/php}
                                {volist name="features" id="feature" key="index"}
                                <div class="feature-quantum-card" data-aos="zoom-in" data-aos-delay="{$index * 100}">
                                    <div class="feature-energy-border"></div>
                                    <div class="feature-icon-quantum">
                                        {switch name="$feature"}
                                            {case value="零成本获客"}
                                                <i class="fas fa-user-plus"></i>
                                            {/case}
                                            {case value="裂变式增长"}
                                                <i class="fas fa-expand-arrows-alt"></i>
                                            {/case}
                                            {case value="精准营销"}
                                                <i class="fas fa-bullseye"></i>
                                            {/case}
                                            {case value="统一库存"}
                                                <i class="fas fa-warehouse"></i>
                                            {/case}
                                            {case value="订单一体化"}
                                                <i class="fas fa-clipboard-list"></i>
                                            {/case}
                                            {case value="会员通用"}
                                                <i class="fas fa-users"></i>
                                            {/case}
                                            {case value="高并发处理"}
                                                <i class="fas fa-tachometer-alt"></i>
                                            {/case}
                                            {case value="商户自主管理"}
                                                <i class="fas fa-user-cog"></i>
                                            {/case}
                                            {case value="安全保障"}
                                                <i class="fas fa-shield-alt"></i>
                                            {/case}
                                            {case value="大宗交易"}
                                                <i class="fas fa-handshake"></i>
                                            {/case}
                                            {case value="供应链管理"}
                                                <i class="fas fa-link"></i>
                                            {/case}
                                            {case value="AI驱动"}
                                                <i class="fas fa-brain"></i>
                                            {/case}
                                            {case value="LBS技术"}
                                                <i class="fas fa-map-marked-alt"></i>
                                            {/case}
                                            {default /}
                                                <i class="fas fa-star"></i>
                                        {/switch}
                                        <div class="icon-pulse-effect"></div>
                                    </div>
                                    <div class="feature-content-quantum">
                                        <h4 class="feature-title">{$feature}</h4>
                                        <p class="feature-description">
                                            {switch name="$feature"}
                                                {case value="零成本获客"}通过社交分享实现自然传播，降低获客成本{/case}
                                                {case value="裂变式增长"}每个用户都是潜在分销商，实现指数级增长{/case}
                                                {case value="精准营销"}基于社交关系的信任营销，提升转化率{/case}
                                                {case value="统一库存"}实时同步各门店库存信息，避免超卖{/case}
                                                {case value="订单一体化"}线上线下订单统一处理，提升效率{/case}
                                                {case value="会员通用"}会员信息全门店共享，提升用户体验{/case}
                                                {case value="高并发处理"}支持百万级用户同时在线访问{/case}
                                                {case value="商户自主管理"}完善的商户后台管理系统{/case}
                                                {case value="安全保障"}多层安全防护，确保交易安全{/case}
                                                {case value="大宗交易"}支持大批量商品交易处理{/case}
                                                {case value="供应链管理"}完整的供应链跟踪体系{/case}
                                                {case value="AI驱动"}智能算法优化业务流程{/case}
                                                {case value="LBS技术"}精准的位置服务和智能推荐{/case}
                                                {default /}先进的技术解决方案，助力企业发展
                                            {/switch}
                                        </p>
                                    </div>
                                    <div class="feature-hover-glow"></div>
                                </div>
                                {/volist}
                            {else /}
                                <!-- 默认特性 -->
                                <div class="feature-quantum-card">
                                    <div class="feature-energy-border"></div>
                                    <div class="feature-icon-quantum">
                                        <i class="fas fa-cogs"></i>
                                        <div class="icon-pulse-effect"></div>
                                    </div>
                                    <div class="feature-content-quantum">
                                        <h4 class="feature-title">专业定制</h4>
                                        <p class="feature-description">根据企业实际需求，提供个性化的解决方案</p>
                                    </div>
                                    <div class="feature-hover-glow"></div>
                                </div>
                                <div class="feature-quantum-card">
                                    <div class="feature-energy-border"></div>
                                    <div class="feature-icon-quantum">
                                        <i class="fas fa-shield-alt"></i>
                                        <div class="icon-pulse-effect"></div>
                                    </div>
                                    <div class="feature-content-quantum">
                                        <h4 class="feature-title">安全可靠</h4>
                                        <p class="feature-description">采用先进的安全技术，确保系统稳定运行</p>
                                    </div>
                                    <div class="feature-hover-glow"></div>
                                </div>
                                <div class="feature-quantum-card">
                                    <div class="feature-energy-border"></div>
                                    <div class="feature-icon-quantum">
                                        <i class="fas fa-rocket"></i>
                                        <div class="icon-pulse-effect"></div>
                                    </div>
                                    <div class="feature-content-quantum">
                                        <h4 class="feature-title">高效实施</h4>
                                        <p class="feature-description">专业团队快速部署，缩短项目周期</p>
                                    </div>
                                    <div class="feature-hover-glow"></div>
                                </div>
                                <div class="feature-quantum-card">
                                    <div class="feature-energy-border"></div>
                                    <div class="feature-icon-quantum">
                                        <i class="fas fa-headset"></i>
                                        <div class="icon-pulse-effect"></div>
                                    </div>
                                    <div class="feature-content-quantum">
                                        <h4 class="feature-title">持续支持</h4>
                                        <p class="feature-description">提供7×24小时技术支持和维护服务</p>
                                    </div>
                                    <div class="feature-hover-glow"></div>
                                </div>
                            {/if}
                        </div>
                    </div>
                </div>

                <!-- 实施流程 -->
                <div id="solution-process" class="quantum-content-block" data-aos="fade-up" data-aos-delay="300">
                    <div class="block-header">
                        <div class="block-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h2 class="block-title">实施流程</h2>
                        <div class="block-energy-line"></div>
                    </div>
                    <div class="block-content">
                        <div class="process-quantum-timeline">
                            <div class="timeline-quantum-line"></div>
                            <div class="timeline-quantum-item" data-aos="fade-right" data-aos-delay="100">
                                <div class="timeline-quantum-marker">
                                    <div class="marker-core">1</div>
                                    <div class="marker-pulse"></div>
                                </div>
                                <div class="timeline-quantum-content">
                                    <h4>需求分析</h4>
                                    <p>深入了解企业需求，制定详细的解决方案</p>
                                    <div class="timeline-tech-tags">
                                        <span class="tech-tag">业务调研</span>
                                        <span class="tech-tag">需求梳理</span>
                                    </div>
                                </div>
                            </div>
                            <div class="timeline-quantum-item" data-aos="fade-left" data-aos-delay="200">
                                <div class="timeline-quantum-marker">
                                    <div class="marker-core">2</div>
                                    <div class="marker-pulse"></div>
                                </div>
                                <div class="timeline-quantum-content">
                                    <h4>方案设计</h4>
                                    <p>基于需求分析，设计最优的技术架构</p>
                                    <div class="timeline-tech-tags">
                                        <span class="tech-tag">架构设计</span>
                                        <span class="tech-tag">原型制作</span>
                                    </div>
                                </div>
                            </div>
                            <div class="timeline-quantum-item" data-aos="fade-right" data-aos-delay="300">
                                <div class="timeline-quantum-marker">
                                    <div class="marker-core">3</div>
                                    <div class="marker-pulse"></div>
                                </div>
                                <div class="timeline-quantum-content">
                                    <h4>系统开发</h4>
                                    <p>专业团队进行系统开发和功能实现</p>
                                    <div class="timeline-tech-tags">
                                        <span class="tech-tag">敏捷开发</span>
                                        <span class="tech-tag">代码审查</span>
                                    </div>
                                </div>
                            </div>
                            <div class="timeline-quantum-item" data-aos="fade-left" data-aos-delay="400">
                                <div class="timeline-quantum-marker">
                                    <div class="marker-core">4</div>
                                    <div class="marker-pulse"></div>
                                </div>
                                <div class="timeline-quantum-content">
                                    <h4>测试部署</h4>
                                    <p>全面测试系统功能，确保稳定上线</p>
                                    <div class="timeline-tech-tags">
                                        <span class="tech-tag">自动化测试</span>
                                        <span class="tech-tag">性能优化</span>
                                    </div>
                                </div>
                            </div>
                            <div class="timeline-quantum-item" data-aos="fade-right" data-aos-delay="500">
                                <div class="timeline-quantum-marker">
                                    <div class="marker-core">5</div>
                                    <div class="marker-pulse"></div>
                                </div>
                                <div class="timeline-quantum-content">
                                    <h4>培训维护</h4>
                                    <p>提供用户培训和持续的技术支持</p>
                                    <div class="timeline-tech-tags">
                                        <span class="tech-tag">用户培训</span>
                                        <span class="tech-tag">7×24支持</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 联系咨询 -->
                <div id="solution-contact" class="quantum-content-block" data-aos="fade-up" data-aos-delay="400">
                    <div class="block-header">
                        <div class="block-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h2 class="block-title">立即咨询</h2>
                        <div class="block-energy-line"></div>
                    </div>
                    <div class="block-content">
                        <div class="contact-quantum-card">
                            <div class="contact-bg-effects">
                                <div class="contact-particle-1"></div>
                                <div class="contact-particle-2"></div>
                                <div class="contact-particle-3"></div>
                            </div>
                            <div class="contact-content-grid">
                                <div class="contact-info-section">
                                    <h3 class="contact-title">
                                        <span class="title-glow">需要专业咨询？</span>
                                    </h3>
                                    <p class="contact-subtitle">
                                        我们的专业顾问将为您提供详细的解决方案咨询，助力企业数字化转型
                                    </p>
                                    <div class="contact-features">
                                        <div class="contact-feature">
                                            <i class="fas fa-user-tie"></i>
                                            <span>专业顾问团队</span>
                                        </div>
                                        <div class="contact-feature">
                                            <i class="fas fa-clock"></i>
                                            <span>快速响应服务</span>
                                        </div>
                                        <div class="contact-feature">
                                            <i class="fas fa-shield-alt"></i>
                                            <span>方案保密承诺</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="contact-actions-section">
                                    <div class="contact-buttons-quantum">
                                        <a href="/contact" class="btn-contact-primary">
                                            <div class="btn-core">
                                                <i class="fas fa-envelope"></i>
                                                <span>在线咨询</span>
                                            </div>
                                            <div class="btn-energy-wave"></div>
                                        </a>
                                        <a href="tel:{$siteConfig.company_phone|default='************'}" class="btn-contact-secondary">
                                            <div class="btn-core">
                                                <i class="fas fa-phone"></i>
                                                <span>电话咨询</span>
                                            </div>
                                            <div class="btn-neural-effect"></div>
                                        </a>
                                        <a href="javascript:void(0)" onclick="showQRCode()" class="btn-contact-tertiary">
                                            <div class="btn-core">
                                                <i class="fab fa-weixin"></i>
                                                <span>微信咨询</span>
                                            </div>
                                            <div class="btn-scan-effect"></div>
                                        </a>
                                    </div>
                                    <div class="contact-info-quick">
                                        <div class="info-item">
                                            <span class="info-label">咨询热线</span>
                                            <span class="info-value">{$siteConfig.company_phone|default='************'}</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">邮箱地址</span>
                                            <span class="info-value">{$siteConfig.company_email|default='<EMAIL>'}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 量子侧边栏 -->
            <div class="col-lg-4 col-md-12">
                <div class="quantum-sidebar" data-aos="fade-left" data-aos-delay="200">

                    <!-- 相关解决方案 -->
                    {if condition="$relatedSolutions && count($relatedSolutions) > 0"}
                    <div class="sidebar-quantum-widget">
                        <div class="widget-header">
                            <div class="widget-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <h4 class="widget-title">相关解决方案</h4>
                            <div class="widget-energy-line"></div>
                        </div>
                        <div class="widget-content">
                            <div class="related-solutions-quantum">
                                {volist name="relatedSolutions" id="related" key="index"}
                                <div class="related-quantum-item" data-aos="fade-up" data-aos-delay="{$index * 100}">
                                    <div class="related-energy-border"></div>
                                    <div class="related-icon-quantum">
                                        {switch name="$related.name"}
                                            {case value="社交分销"}
                                                <i class="fas fa-share-alt"></i>
                                            {/case}
                                            {case value="智能多门店"}
                                                <i class="fas fa-store"></i>
                                            {/case}
                                            {case value="大型多商户"}
                                                <i class="fas fa-building"></i>
                                            {/case}
                                            {case value="大货批发"}
                                                <i class="fas fa-boxes"></i>
                                            {/case}
                                            {case value="平台级供货商"}
                                                <i class="fas fa-truck"></i>
                                            {/case}
                                            {case value="本地生活服务"}
                                                <i class="fas fa-map-marker-alt"></i>
                                            {/case}
                                            {case value="企业数字化"}
                                                <i class="fas fa-digital-tachograph"></i>
                                            {/case}
                                            {case value="定制化方案"}
                                                <i class="fas fa-cogs"></i>
                                            {/case}
                                            {default /}
                                                <i class="fas fa-lightbulb"></i>
                                        {/switch}
                                        <div class="icon-glow-effect"></div>
                                    </div>
                                    <div class="related-content-quantum">
                                        <h5 class="related-title">
                                            <a href="/solutions/{$related.link_value|default=$related.slug}">{$related.name}</a>
                                        </h5>
                                        <p class="related-desc">{$related.remark|default='专业的解决方案'}</p>
                                    </div>
                                    <div class="related-hover-effect"></div>
                                </div>
                                {/volist}
                            </div>
                        </div>
                    </div>
                    {/if}

                    <!-- 技术优势 -->
                    <div class="sidebar-quantum-widget">
                        <div class="widget-header">
                            <div class="widget-icon">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <h4 class="widget-title">技术优势</h4>
                            <div class="widget-energy-line"></div>
                        </div>
                        <div class="widget-content">
                            <div class="tech-advantages-quantum">
                                <div class="advantage-quantum-item">
                                    <div class="advantage-icon">
                                        <i class="fas fa-code"></i>
                                    </div>
                                    <div class="advantage-content">
                                        <span class="advantage-title">最新技术框架</span>
                                        <span class="advantage-desc">采用前沿技术栈</span>
                                    </div>
                                </div>
                                <div class="advantage-quantum-item">
                                    <div class="advantage-icon">
                                        <i class="fas fa-cloud"></i>
                                    </div>
                                    <div class="advantage-content">
                                        <span class="advantage-title">云端部署</span>
                                        <span class="advantage-desc">支持多种部署方式</span>
                                    </div>
                                </div>
                                <div class="advantage-quantum-item">
                                    <div class="advantage-icon">
                                        <i class="fas fa-expand-arrows-alt"></i>
                                    </div>
                                    <div class="advantage-content">
                                        <span class="advantage-title">良好扩展性</span>
                                        <span class="advantage-desc">模块化架构设计</span>
                                    </div>
                                </div>
                                <div class="advantage-quantum-item">
                                    <div class="advantage-icon">
                                        <i class="fas fa-book"></i>
                                    </div>
                                    <div class="advantage-content">
                                        <span class="advantage-title">完整文档</span>
                                        <span class="advantage-desc">API接口文档齐全</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 服务保障 -->
                    <div class="sidebar-quantum-widget">
                        <div class="widget-header">
                            <div class="widget-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h4 class="widget-title">服务保障</h4>
                            <div class="widget-energy-line"></div>
                        </div>
                        <div class="widget-content">
                            <div class="service-guarantees">
                                <div class="guarantee-item">
                                    <div class="guarantee-icon">
                                        <i class="fas fa-award"></i>
                                    </div>
                                    <span>专业团队</span>
                                </div>
                                <div class="guarantee-item">
                                    <div class="guarantee-icon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <span>快速响应</span>
                                </div>
                                <div class="guarantee-item">
                                    <div class="guarantee-icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <span>质量保证</span>
                                </div>
                                <div class="guarantee-item">
                                    <div class="guarantee-icon">
                                        <i class="fas fa-headset"></i>
                                    </div>
                                    <span>持续支持</span>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</section>

<!-- 🎨 量子科技风格样式 -->
<style>
/* ===== 量子详情页Hero区域 ===== */
.quantum-detail-hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: radial-gradient(ellipse at center, #0a0a0a 0%, #000000 100%);
}

/* 多维背景系统 */
.quantum-bg-system {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.bg-primary-layer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.1) 0%, rgba(255, 0, 255, 0.08) 100%);
}

.holographic-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.3;
}

.grid-matrix {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 20s linear infinite;
}

.grid-scanlines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        0deg,
        transparent,
        transparent 2px,
        rgba(0, 255, 255, 0.03) 2px,
        rgba(0, 255, 255, 0.03) 4px
    );
    animation: scanlineMove 3s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

@keyframes scanlineMove {
    0% { transform: translateY(0); }
    100% { transform: translateY(4px); }
}

.quantum-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 30%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(255, 0, 255, 0.08) 0%, transparent 50%);
    animation: quantumFloat 25s ease-in-out infinite;
}

@keyframes quantumFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.energy-pulse-waves {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.pulse-wave {
    position: absolute;
    width: 200px;
    height: 200px;
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 50%;
    animation: pulseExpand 4s ease-out infinite;
}

.wave-alpha {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.wave-beta {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
    border-color: rgba(255, 0, 255, 0.3);
}

@keyframes pulseExpand {
    0% { transform: scale(0.5); opacity: 1; }
    100% { transform: scale(2); opacity: 0; }
}

.depth-gradient-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.3) 100%);
}

/* 量子详情内容 */
.quantum-detail-content {
    position: relative;
    z-index: 10;
    text-align: center;
}

/* 面包屑导航 */
.quantum-breadcrumb {
    margin-bottom: 3rem;
}

.breadcrumb-quantum {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.breadcrumb-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.breadcrumb-link:hover {
    color: #00ffff;
    background: rgba(0, 255, 255, 0.2);
    border-color: rgba(0, 255, 255, 0.4);
    transform: translateY(-2px);
}

.breadcrumb-separator {
    color: rgba(255, 255, 255, 0.5);
}

.breadcrumb-current {
    color: #00ffff;
    padding: 0.5rem 1rem;
    background: rgba(0, 255, 255, 0.2);
    border-radius: 20px;
    border: 1px solid rgba(0, 255, 255, 0.4);
}

/* 解决方案标题区域 */
.solution-hero-title {
    margin-bottom: 3rem;
}

.solution-icon-quantum {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto 2rem;
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
}

.icon-energy-ring {
    position: absolute;
    top: -15px;
    left: -15px;
    right: -15px;
    bottom: -15px;
    border: 2px solid rgba(0, 255, 255, 0.4);
    border-radius: 50%;
    animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); opacity: 0.6; }
    50% { transform: scale(1.1); opacity: 1; }
}

.solution-mega-title {
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    font-family: 'Orbitron', monospace;
}

.title-glow-effect {
    background: linear-gradient(135deg, #ffffff 0%, #00ffff 50%, #ff00ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
    animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% { filter: brightness(1) drop-shadow(0 0 20px rgba(0, 255, 255, 0.3)); }
    100% { filter: brightness(1.3) drop-shadow(0 0 40px rgba(0, 255, 255, 0.6)); }
}

.solution-hero-subtitle {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.title-energy-line {
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, transparent, #00ffff, transparent);
    margin: 0 auto;
    animation: energyLinePulse 2s ease-in-out infinite;
}

@keyframes energyLinePulse {
    0%, 100% { opacity: 0.5; transform: scaleX(1); }
    50% { opacity: 1; transform: scaleX(1.5); }
}

/* 快速导航 */
.quantum-quick-nav {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.nav-quantum-btn {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1rem 2rem;
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 30px;
    color: #00ffff;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-quantum-btn:hover {
    background: rgba(0, 255, 255, 0.2);
    border-color: rgba(0, 255, 255, 0.5);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 255, 255, 0.3);
}

.nav-quantum-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.nav-quantum-btn:hover::before {
    left: 100%;
}

/* 滚动指示器 */
.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
}

.scroll-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    animation: scrollBounce 2s ease-in-out infinite;
}

@keyframes scrollBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(10px); }
}

.scroll-text {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* ===== 解决方案详情主体 ===== */
.quantum-solution-detail {
    padding: 60px 0;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    position: relative;
    overflow: hidden;
}

/* 量子内容块 */
.quantum-content-block {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 20px;
    padding: 3rem 2.5rem;
    margin-bottom: 3rem;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.quantum-content-block::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff7f, #ffd700);
    border-radius: 20px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.quantum-content-block:hover::before {
    opacity: 0.3;
}

.block-header {
    display: flex;
    align-items: center;
    margin-bottom: 2.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2);
}

.block-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    font-size: 1.5rem;
    color: white;
}

.block-title {
    font-size: 2rem;
    font-weight: 700;
    color: #ffffff;
    margin: 0;
    font-family: 'Orbitron', monospace;
}

.block-energy-line {
    flex: 1;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00ffff, transparent);
    margin-left: 2rem;
    animation: energyFlow 2s ease-in-out infinite;
}

@keyframes energyFlow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* 概述网格 */
.overview-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.solution-image-quantum {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
}

.solution-image-quantum img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 15px;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.2) 0%, rgba(255, 0, 255, 0.1) 100%);
}

.overlay-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 48%, rgba(255, 255, 255, 0.1) 49%, rgba(255, 255, 255, 0.1) 51%, transparent 52%);
    background-size: 20px 20px;
    animation: patternMove 8s linear infinite;
}

@keyframes patternMove {
    0% { background-position: 0 0; }
    100% { background-position: 20px 20px; }
}

.solution-description-rich {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.8;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.solution-stats {
    display: flex;
    gap: 2rem;
    justify-content: space-around;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #00ffff;
    font-family: 'Orbitron', monospace;
    text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

/* 特性量子网格 */
.features-quantum-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-quantum-card {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 15px;
    padding: 2rem;
    transition: all 0.4s ease;
    overflow: hidden;
}

.feature-quantum-card:hover {
    transform: translateY(-10px);
    border-color: rgba(0, 255, 255, 0.5);
    box-shadow: 0 20px 40px rgba(0, 255, 255, 0.2);
}

.feature-energy-border {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff7f);
    border-radius: 15px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.feature-quantum-card:hover .feature-energy-border {
    opacity: 0.3;
}

.feature-icon-quantum {
    position: relative;
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
    color: white;
}

.icon-pulse-effect {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border: 2px solid rgba(0, 255, 255, 0.4);
    border-radius: 50%;
    animation: iconPulseEffect 2s ease-in-out infinite;
}

@keyframes iconPulseEffect {
    0%, 100% { transform: scale(1); opacity: 0.6; }
    50% { transform: scale(1.1); opacity: 1; }
}

.feature-content-quantum {
    position: relative;
    z-index: 2;
}

.feature-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
    font-family: 'Orbitron', monospace;
}

.feature-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin: 0;
}

.feature-hover-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.feature-quantum-card:hover .feature-hover-glow {
    opacity: 1;
}

/* 量子时间线 */
.process-quantum-timeline {
    position: relative;
    padding: 2rem 0;
}

.timeline-quantum-line {
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(180deg, #00ffff 0%, #ff00ff 50%, #00ff7f 100%);
    transform: translateX(-50%);
    animation: timelineGlow 3s ease-in-out infinite;
}

@keyframes timelineGlow {
    0%, 100% { box-shadow: 0 0 10px rgba(0, 255, 255, 0.5); }
    50% { box-shadow: 0 0 20px rgba(0, 255, 255, 0.8); }
}

.timeline-quantum-item {
    position: relative;
    margin-bottom: 4rem;
    display: flex;
    align-items: center;
}

.timeline-quantum-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-quantum-marker {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.marker-core {
    font-size: 1.5rem;
    font-weight: 800;
    color: white;
    font-family: 'Orbitron', monospace;
}

.marker-pulse {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border: 2px solid rgba(0, 255, 255, 0.4);
    border-radius: 50%;
    animation: markerPulse 2s ease-in-out infinite;
}

@keyframes markerPulse {
    0%, 100% { transform: scale(1); opacity: 0.6; }
    50% { transform: scale(1.2); opacity: 1; }
}

.timeline-quantum-content {
    width: 45%;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 15px;
    padding: 2rem;
    backdrop-filter: blur(10px);
}

.timeline-quantum-item:nth-child(even) .timeline-quantum-content {
    margin-right: auto;
}

.timeline-quantum-item:nth-child(odd) .timeline-quantum-content {
    margin-left: auto;
}

.timeline-quantum-content h4 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
    font-family: 'Orbitron', monospace;
}

.timeline-quantum-content p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.timeline-tech-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tech-tag {
    padding: 0.3rem 0.8rem;
    background: rgba(0, 255, 255, 0.2);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 15px;
    color: #00ffff;
    font-size: 0.8rem;
    font-weight: 500;
}

/* 联系量子卡片 */
.contact-quantum-card {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 20px;
    padding: 3rem;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.contact-bg-effects {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.contact-particle-1,
.contact-particle-2,
.contact-particle-3 {
    position: absolute;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    opacity: 0.1;
    animation: particleFloat 8s ease-in-out infinite;
}

.contact-particle-1 {
    top: 10%;
    left: 10%;
    background: radial-gradient(circle, #00ffff 0%, transparent 70%);
    animation-delay: 0s;
}

.contact-particle-2 {
    top: 60%;
    right: 20%;
    background: radial-gradient(circle, #ff00ff 0%, transparent 70%);
    animation-delay: 2s;
}

.contact-particle-3 {
    bottom: 20%;
    left: 30%;
    background: radial-gradient(circle, #00ff7f 0%, transparent 70%);
    animation-delay: 4s;
}

@keyframes particleFloat {
    0%, 100% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-20px) scale(1.1); }
}

.contact-content-grid {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.contact-title {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    font-family: 'Orbitron', monospace;
}

.title-glow {
    background: linear-gradient(135deg, #ffffff 0%, #00ffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.contact-subtitle {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 2rem;
}

.contact-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-feature {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: rgba(255, 255, 255, 0.9);
}

.contact-feature i {
    color: #00ffff;
    font-size: 1.2rem;
}

.contact-buttons-quantum {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.btn-contact-primary,
.btn-contact-secondary,
.btn-contact-tertiary {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 1.2rem 2rem;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.4s ease;
    overflow: hidden;
}

.btn-contact-primary {
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    color: white;
}

.btn-contact-secondary {
    background: transparent;
    border: 2px solid rgba(0, 255, 255, 0.5);
    color: #00ffff;
}

.btn-contact-tertiary {
    background: transparent;
    border: 2px solid rgba(0, 255, 127, 0.5);
    color: #00ff7f;
}

.btn-contact-primary:hover,
.btn-contact-secondary:hover,
.btn-contact-tertiary:hover {
    transform: translateY(-3px);
    color: white;
}

.btn-contact-primary:hover {
    box-shadow: 0 15px 30px rgba(0, 255, 255, 0.4);
}

.btn-contact-secondary:hover {
    background: rgba(0, 255, 255, 0.1);
    border-color: rgba(0, 255, 255, 0.8);
}

.btn-contact-tertiary:hover {
    background: rgba(0, 255, 127, 0.1);
    border-color: rgba(0, 255, 127, 0.8);
}

.contact-info-quick {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem 1rem;
    background: rgba(0, 255, 255, 0.1);
    border-radius: 10px;
    border: 1px solid rgba(0, 255, 255, 0.2);
}

.info-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.info-value {
    color: #00ffff;
    font-weight: 600;
}

/* ===== 量子侧边栏 ===== */
.quantum-sidebar {
    position: sticky;
}

.sidebar-quantum-widget {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.sidebar-quantum-widget:hover {
    border-color: rgba(0, 255, 255, 0.4);
    box-shadow: 0 10px 25px rgba(0, 255, 255, 0.1);
}

.widget-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2);
}

.widget-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: white;
}

.widget-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: #ffffff;
    margin: 0;
    font-family: 'Orbitron', monospace;
}

.widget-energy-line {
    flex: 1;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00ffff, transparent);
    margin-left: 1rem;
}

/* 相关解决方案 */
.related-solutions-quantum {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.related-quantum-item {
    position: relative;
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(0, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.1);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.related-quantum-item:hover {
    background: rgba(0, 255, 255, 0.1);
    border-color: rgba(0, 255, 255, 0.3);
    transform: translateX(5px);
}

.related-energy-border {
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(45deg, #00ffff, #ff00ff);
    border-radius: 10px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.related-quantum-item:hover .related-energy-border {
    opacity: 0.3;
}

.related-icon-quantum {
    position: relative;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.icon-glow-effect {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border: 1px solid rgba(0, 255, 255, 0.4);
    border-radius: 50%;
    animation: iconGlow 2s ease-in-out infinite;
}

@keyframes iconGlow {
    0%, 100% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
}

.related-content-quantum {
    flex: 1;
}

.related-title {
    margin: 0 0 0.5rem 0;
}

.related-title a {
    color: #ffffff;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    transition: color 0.3s ease;
}

.related-title a:hover {
    color: #00ffff;
}

.related-desc {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.85rem;
    margin: 0;
}

/* 技术优势 */
.tech-advantages-quantum {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.advantage-quantum-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(0, 255, 255, 0.05);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.advantage-quantum-item:hover {
    background: rgba(0, 255, 255, 0.1);
    transform: translateX(3px);
}

.advantage-icon {
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
}

.advantage-content {
    display: flex;
    flex-direction: column;
}

.advantage-title {
    color: #ffffff;
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 0.2rem;
}

.advantage-desc {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8rem;
}

/* 服务保障 */
.service-guarantees {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.guarantee-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1rem;
    background: rgba(0, 255, 255, 0.05);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.guarantee-item:hover {
    background: rgba(0, 255, 255, 0.1);
    transform: translateY(-2px);
}

.guarantee-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-bottom: 0.5rem;
}

.guarantee-item span {
    color: #ffffff;
    font-weight: 600;
    font-size: 0.9rem;
}

/* ===== 响应式设计 ===== */
@media (max-width: 1400px) {
    .overview-grid,
    .contact-content-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .timeline-quantum-content {
        width: 80%;
    }

    .timeline-quantum-marker {
        left: 10%;
        transform: translateX(0);
    }

    .timeline-quantum-item {
        flex-direction: row !important;
        padding-left: 15%;
    }

    .timeline-quantum-content {
        margin-left: 0 !important;
        margin-right: 0 !important;
    }
}

@media (max-width: 768px) {
    .quantum-detail-hero {
        min-height: 80vh;
        padding: 2rem 0;
    }

    .solution-mega-title {
        font-size: 2.5rem;
    }

    .solution-icon-quantum {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .quantum-quick-nav {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .nav-quantum-btn {
        width: 100%;
        max-width: 280px;
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    .quantum-content-block {
        padding: 1.5rem 1rem;
        margin-bottom: 2rem;
    }

    .features-quantum-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .solution-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .contact-buttons-quantum {
        flex-direction: column;
        gap: 1rem;
    }

    .btn-contact-primary,
    .btn-contact-secondary,
    .btn-contact-tertiary {
        width: 100%;
        padding: 1rem 1.5rem;
        justify-content: center;
    }

    .service-guarantees {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    /* 移动端恢复正常滚动 */
    .quantum-solution-detail {
        height: auto;
        overflow: visible;
        padding: 0;
    }

    .main-content-scroll {
        height: auto;
        overflow: visible;
        padding: 20px 15px;
    }

    .sidebar-fixed {
        height: auto;
        padding: 20px 15px;
        margin-top: 2rem;
    }

    .quantum-sidebar {
        height: auto;
        overflow: visible;
        padding-right: 0;
    }

    /* 移动端侧边栏样式调整 */
    .sidebar-quantum-widget {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .widget-header {
        margin-bottom: 1rem;
        padding-bottom: 0.8rem;
    }

    .widget-title {
        font-size: 1.1rem;
    }

    .related-quantum-item,
    .advantage-quantum-item {
        padding: 0.8rem;
    }

    .guarantee-item {
        padding: 0.8rem;
    }

    /* 移动端优化触摸体验 */
    .related-quantum-item:hover,
    .advantage-quantum-item:hover,
    .guarantee-item:hover {
        transform: none;
    }

    /* 移动端字体大小调整 */
    .block-title {
        font-size: 1.8rem;
    }

    .feature-title {
        font-size: 1.2rem;
    }

    .feature-description {
        font-size: 0.9rem;
    }

    /* 移动端禁用复杂动画 */
    .quantum-particles,
    .energy-pulse-waves,
    .contact-bg-effects {
        display: none;
    }
}

@media (max-width: 480px) {
    .solution-mega-title {
        font-size: 2rem;
    }

    .block-title {
        font-size: 1.5rem;
    }

    .quantum-content-block {
        padding: 1.2rem 0.8rem;
        margin-bottom: 1.5rem;
    }

    .timeline-quantum-content {
        width: 90%;
        padding: 1.2rem;
    }

    .contact-quantum-card {
        padding: 1.5rem 1rem;
    }

    /* 超小屏幕侧边栏优化 */
    .sidebar-quantum-widget {
        padding: 1.2rem;
        margin-bottom: 1.2rem;
    }

    .widget-title {
        font-size: 1rem;
    }

    .related-quantum-item,
    .advantage-quantum-item {
        padding: 0.6rem;
        gap: 0.8rem;
    }

    .guarantee-item {
        padding: 0.6rem;
    }

    .nav-quantum-btn {
        max-width: 100%;
        padding: 0.7rem 1.2rem;
        font-size: 0.85rem;
    }

    /* 超小屏幕字体调整 */
    .feature-title {
        font-size: 1.1rem;
    }

    .feature-description {
        font-size: 0.85rem;
    }

    .related-title a {
        font-size: 0.9rem;
    }

    .related-desc {
        font-size: 0.8rem;
    }

    .advantage-title {
        font-size: 0.9rem;
    }

    .advantage-desc {
        font-size: 0.75rem;
    }
}

</style>

<!-- 🚀 JavaScript功能 -->
<script>
// AOS动画初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化AOS
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 800,
            easing: 'ease-out-cubic',
            once: true,
            offset: 100
        });
    }

    // 平滑滚动 - 桌面端在主内容区域内滚动，移动端使用页面滚动
    document.querySelectorAll('.nav-quantum-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            const mainContent = document.querySelector('.main-content-scroll');
            const isMobile = window.innerWidth <= 768;

            if (targetElement) {
                if (isMobile) {
                    // 移动端使用页面滚动
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                } else if (mainContent) {
                    // 桌面端在主内容区域内滚动
                    const targetOffset = targetElement.offsetTop - mainContent.offsetTop;
                    mainContent.scrollTo({
                        top: targetOffset - 20,
                        behavior: 'smooth'
                    });
                }
            }
        });
    });

    // 滚动指示器点击
    document.querySelector('.scroll-indicator')?.addEventListener('click', function() {
        const mainContent = document.querySelector('.main-content-scroll');
        const isMobile = window.innerWidth <= 768;

        if (isMobile) {
            // 移动端滚动到详情区域
            document.querySelector('.quantum-solution-detail').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        } else if (mainContent) {
            // 桌面端在主内容区域内滚动
            mainContent.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
    });

    // 动态粒子效果
    createQuantumParticles();

    // 视差滚动效果
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const parallax = document.querySelector('.quantum-bg-system');
        if (parallax) {
            parallax.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });
});

// 创建量子粒子效果
function createQuantumParticles() {
    const particleContainer = document.querySelector('.quantum-particles');
    if (!particleContainer) return;

    for (let i = 0; i < 20; i++) {
        const particle = document.createElement('div');
        particle.className = 'quantum-particle';
        particle.style.cssText = `
            position: absolute;
            width: ${Math.random() * 4 + 2}px;
            height: ${Math.random() * 4 + 2}px;
            background: ${Math.random() > 0.5 ? '#00ffff' : '#ff00ff'};
            border-radius: 50%;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation: particleMove ${Math.random() * 10 + 10}s linear infinite;
            opacity: ${Math.random() * 0.5 + 0.3};
        `;
        particleContainer.appendChild(particle);
    }
}

// 显示微信二维码
function showQRCode() {
    // 创建模态框
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        backdrop-filter: blur(10px);
    `;

    const content = document.createElement('div');
    content.style.cssText = `
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
        padding: 2rem;
        border-radius: 20px;
        text-align: center;
        border: 1px solid rgba(0, 255, 255, 0.3);
        box-shadow: 0 20px 40px rgba(0, 255, 255, 0.2);
    `;

    content.innerHTML = `
        <h3 style="color: #00ffff; margin-bottom: 1rem; font-family: 'Orbitron', monospace;">微信咨询</h3>
        <div style="width: 200px; height: 200px; background: white; margin: 0 auto 1rem; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: #333;">
            二维码占位
        </div>
        <p style="color: rgba(255, 255, 255, 0.8); margin-bottom: 1.5rem;">扫描二维码添加微信咨询</p>
        <button onclick="this.closest('.modal').remove()" style="
            background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
            color: white;
            border: none;
            padding: 0.8rem 2rem;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
        ">关闭</button>
    `;

    modal.className = 'modal';
    modal.appendChild(content);
    document.body.appendChild(modal);

    // 点击背景关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// 添加粒子移动动画
const style = document.createElement('style');
style.textContent = `
    @keyframes particleMove {
        0% { transform: translateY(0) rotate(0deg); }
        100% { transform: translateY(-100vh) rotate(360deg); }
    }
`;
document.head.appendChild(style);
</script>

{include file="common/footer"}
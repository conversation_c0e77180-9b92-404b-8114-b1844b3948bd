<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 用户验证器
 */

declare(strict_types=1);

namespace app\validate;

use think\Validate;

/**
 * 用户验证器
 */
class UserValidate extends Validate
{
    /**
     * 验证规则
     * @var array
     */
    protected $rule = [
        'username' => 'require|length:3,50|alphaNum|unique:users',
        'email' => 'require|email|length:5,100|unique:users',
        'password' => 'require|length:6,32',
        'real_name' => 'length:2,50',
        'phone' => 'mobile|unique:users',
        'status' => 'in:0,1',
        'role_ids' => 'array',
        'avatar' => 'url'
    ];

    /**
     * 错误信息
     * @var array
     */
    protected $message = [
        'username.require' => '用户名不能为空',
        'username.length' => '用户名长度必须在3-50个字符之间',
        'username.alphaNum' => '用户名只能包含字母和数字',
        'username.unique' => '用户名已存在',
        'email.require' => '邮箱不能为空',
        'email.email' => '邮箱格式不正确',
        'email.length' => '邮箱长度必须在5-100个字符之间',
        'email.unique' => '邮箱已存在',
        'password.require' => '密码不能为空',
        'password.length' => '密码长度必须在6-32个字符之间',
        'real_name.length' => '真实姓名长度必须在2-50个字符之间',
        'phone.mobile' => '手机号格式不正确',
        'phone.unique' => '手机号已存在',
        'status.in' => '状态值不正确',
        'role_ids.array' => '角色ID必须是数组',
        'avatar.url' => '头像地址格式不正确'
    ];

    /**
     * 验证场景
     * @var array
     */
    protected $scene = [
        // 创建场景
        'create' => ['username', 'email', 'password', 'real_name', 'phone', 'status', 'role_ids'],
        
        // 更新场景
        'update' => ['username', 'email', 'real_name', 'phone', 'status', 'role_ids', 'avatar'],
        
        // 更新个人资料场景
        'profile' => ['real_name', 'avatar']
    ];

    /**
     * 创建场景验证规则
     * @return UserValidate
     */
    public function sceneCreate(): UserValidate
    {
        return $this->only(['username', 'email', 'password', 'real_name', 'phone', 'status', 'role_ids'])
                   ->append('username', 'checkUsername')
                   ->append('password', 'checkPasswordStrength');
    }

    /**
     * 更新场景验证规则
     * @return UserValidate
     */
    public function sceneUpdate(): UserValidate
    {
        return $this->only(['username', 'email', 'real_name', 'phone', 'status', 'role_ids', 'avatar'])
                   ->remove('username', 'unique')
                   ->remove('email', 'unique')
                   ->remove('phone', 'unique')
                   ->append('username', 'checkUsernameUpdate')
                   ->append('email', 'checkEmailUpdate')
                   ->append('phone', 'checkPhoneUpdate');
    }

    /**
     * 个人资料场景验证规则
     * @return UserValidate
     */
    public function sceneProfile(): UserValidate
    {
        return $this->only(['real_name', 'avatar']);
    }

    /**
     * 自定义验证规则：检查用户名格式
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkUsername($value, $rule, array $data)
    {
        // 用户名只能包含字母、数字、下划线
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $value)) {
            return '用户名只能包含字母、数字和下划线';
        }
        
        // 用户名不能以数字开头
        if (is_numeric($value[0])) {
            return '用户名不能以数字开头';
        }
        
        // 用户名不能全是数字
        if (is_numeric($value)) {
            return '用户名不能全是数字';
        }
        
        return true;
    }

    /**
     * 自定义验证规则：检查密码强度
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkPasswordStrength($value, $rule, array $data)
    {
        // 密码必须包含字母和数字
        if (!preg_match('/^(?=.*[a-zA-Z])(?=.*\d)/', $value)) {
            return '密码必须包含字母和数字';
        }
        
        // 检查常见弱密码
        $weakPasswords = ['123456', 'password', 'admin123', 'qwerty', '111111', '000000'];
        if (in_array(strtolower($value), $weakPasswords)) {
            return '密码过于简单，请使用更复杂的密码';
        }
        
        return true;
    }

    /**
     * 自定义验证规则：检查用户名更新时的唯一性
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkUsernameUpdate($value, $rule, array $data)
    {
        if (empty($value)) {
            return true;
        }

        // 检查用户名格式
        $formatCheck = $this->checkUsername($value, $rule, $data);
        if ($formatCheck !== true) {
            return $formatCheck;
        }

        // 检查唯一性（排除当前用户）
        $userId = $data['id'] ?? 0;
        $exists = \app\model\User::where('username', $value)
                                ->where('id', '<>', $userId)
                                ->exists();
        if ($exists) {
            return '用户名已存在';
        }
        
        return true;
    }

    /**
     * 自定义验证规则：检查邮箱更新时的唯一性
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkEmailUpdate($value, $rule, array $data)
    {
        if (empty($value)) {
            return true;
        }

        // 检查唯一性（排除当前用户）
        $userId = $data['id'] ?? 0;
        $exists = \app\model\User::where('email', $value)
                                ->where('id', '<>', $userId)
                                ->exists();
        if ($exists) {
            return '邮箱已存在';
        }
        
        return true;
    }

    /**
     * 自定义验证规则：检查手机号更新时的唯一性
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkPhoneUpdate($value, $rule, array $data)
    {
        if (empty($value)) {
            return true;
        }

        // 检查唯一性（排除当前用户）
        $userId = $data['id'] ?? 0;
        $exists = \app\model\User::where('phone', $value)
                                ->where('id', '<>', $userId)
                                ->exists();
        if ($exists) {
            return '手机号已存在';
        }
        
        return true;
    }

    /**
     * 自定义验证规则：检查角色ID是否存在
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkRoleIds($value, $rule, array $data)
    {
        if (empty($value) || !is_array($value)) {
            return true;
        }

        // 检查角色是否存在
        $existingCount = \app\model\Role::whereIn('id', $value)->count();
        if ($existingCount !== count($value)) {
            return '部分角色不存在';
        }
        
        return true;
    }
}

<?php

namespace app\model;

use think\Model;

/**
 * 图片分组模型
 */
class ImageGroup extends Model
{
    // 表名
    protected $name = 'image_groups';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'parent_id' => 'integer',
        'sort_order' => 'integer',
        'image_count' => 'integer',
        'status' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    
    // 允许写入的字段
    protected $field = [
        'name', 'slug', 'description', 'parent_id', 'sort_order', 'status'
    ];
    
    // 关联图片
    public function images()
    {
        return $this->hasMany(Image::class, 'group_id', 'id');
    }
    
    // 关联父分组
    public function parent()
    {
        return $this->belongsTo(self::class, 'parent_id', 'id');
    }
    
    // 关联子分组
    public function children()
    {
        return $this->hasMany(self::class, 'parent_id', 'id');
    }
    
    // 获取所有启用的分组
    public static function getEnabledGroups()
    {
        return self::where('status', 1)
            ->order('sort_order', 'asc')
            ->order('id', 'asc')
            ->select();
    }
    
    // 获取分组树形结构
    public static function getGroupTree($parentId = null)
    {
        $groups = self::where('status', 1)
            ->where('parent_id', $parentId)
            ->order('sort_order', 'asc')
            ->order('id', 'asc')
            ->select();
            
        foreach ($groups as $group) {
            $group->children = self::getGroupTree($group->id);
        }
        
        return $groups;
    }
    
    // 验证分组数据
    public static function validateGroupData($data, $id = null)
    {
        $errors = [];
        
        // 验证名称
        if (empty($data['name'])) {
            $errors[] = '分组名称不能为空';
        } elseif (mb_strlen($data['name']) > 100) {
            $errors[] = '分组名称不能超过100个字符';
        }
        
        // 验证别名
        if (empty($data['slug'])) {
            $errors[] = '分组别名不能为空';
        } elseif (!preg_match('/^[a-z0-9_-]+$/', $data['slug'])) {
            $errors[] = '分组别名只能包含小写字母、数字、下划线和连字符';
        } else {
            // 检查别名唯一性
            $query = self::where('slug', $data['slug']);
            if ($id) {
                $query->where('id', '<>', $id);
            }
            if ($query->count() > 0) {
                $errors[] = '分组别名已存在';
            }
        }
        
        // 验证父分组
        if (!empty($data['parent_id'])) {
            if ($id && $data['parent_id'] == $id) {
                $errors[] = '不能将自己设为父分组';
            } elseif (!self::where('id', $data['parent_id'])->where('status', 1)->count()) {
                $errors[] = '父分组不存在或已禁用';
            }
        }
        
        // 验证排序权重
        if (isset($data['sort_order']) && (!is_numeric($data['sort_order']) || $data['sort_order'] < 0)) {
            $errors[] = '排序权重必须为非负整数';
        }
        
        return $errors;
    }
    
    // 生成别名
    public static function generateSlug($name)
    {
        // 简单的拼音转换（这里可以集成更完善的拼音库）
        $slug = strtolower(trim($name));
        $slug = preg_replace('/[^a-z0-9\-_]/', '-', $slug);
        $slug = preg_replace('/\-+/', '-', $slug);
        $slug = trim($slug, '-');
        
        // 如果为空，使用时间戳
        if (empty($slug)) {
            $slug = 'group-' . time();
        }
        
        // 确保唯一性
        $originalSlug = $slug;
        $counter = 1;
        while (self::where('slug', $slug)->count() > 0) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    // 更新图片数量
    public function updateImageCount()
    {
        $count = Image::where('group_id', $this->id)->where('status', 1)->count();
        $this->save(['image_count' => $count]);
        return $count;
    }
    
    // 检查是否可以删除
    public function canDelete()
    {
        // 检查是否有图片
        if ($this->image_count > 0) {
            return false;
        }
        
        // 检查是否有子分组
        if ($this->children()->count() > 0) {
            return false;
        }
        
        return true;
    }
    
    // 模型事件 - 删除前检查
    public static function onBeforeDelete($group)
    {
        if (!$group->canDelete()) {
            throw new \Exception('该分组下还有图片或子分组，无法删除');
        }
    }
} 
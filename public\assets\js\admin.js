/* 后台管理系统JavaScript - 酷炫科技风 */

$(function() {
    // 初始化酷炫效果
    initCoolEffects();

    // 初始化工具提示（安全检查）
    if (typeof $.fn.tooltip !== 'undefined') {
        $('[data-toggle="tooltip"]').tooltip();
    }

    // 初始化弹出框（安全检查）
    if (typeof $.fn.popover !== 'undefined') {
        $('[data-toggle="popover"]').popover();
    }

    // 初始化选项卡功能
    initTabs();

    // 选项卡初始化函数
    function initTabs() {

        // 移除之前可能存在的事件监听器
        $(document).off('click.tabs');

        // 处理选项卡点击事件 - 使用事件委托
        $(document).on('click', '[data-bs-toggle="tab"], [data-toggle="tab"]', function(e) {
            e.preventDefault();
            e.stopPropagation();

            var $clickedTab = $(this);
            var targetId = $clickedTab.attr('href') || $clickedTab.data('bs-target') || $clickedTab.data('target');

            // 确保targetId是有效的
            if (!targetId || targetId === '#') {
                return;
            }

            var $target = $(targetId);

            if ($target.length) {

                // 找到当前选项卡组
                var $tabContainer = $clickedTab.closest('.nav-tabs');

                // 移除同组所有选项卡的活动状态
                $tabContainer.find('.nav-link').removeClass('active').attr('aria-selected', 'false');

                // 隐藏所有选项卡内容 - 查找所有.tab-pane
                $('.tab-pane').removeClass('show active');

                // 激活当前选项卡
                $clickedTab.addClass('active').attr('aria-selected', 'true');

                // 显示对应内容
                $target.addClass('show active');

                // 触发自定义事件
                $clickedTab.trigger('shown.bs.tab', { target: targetId });

            } else {
                console.error('未找到目标元素:', targetId);
            }
        });

        // 确保第一个选项卡是活动的
        setTimeout(function() {
            $('.nav-tabs').each(function() {
                var $tabContainer = $(this);
                if ($tabContainer.find('.nav-link.active').length === 0) {
                    var $firstTab = $tabContainer.find('.nav-link:first');
                    if ($firstTab.length) {
                        $firstTab.addClass('active').attr('aria-selected', 'true');
                        var firstTargetId = $firstTab.attr('href') || $firstTab.data('bs-target') || $firstTab.data('target');
                        if (firstTargetId && firstTargetId !== '#') {
                            $(firstTargetId).addClass('show active');
                        }
                    }
                }
            });
        }, 100);
    }

    // 酷炫效果初始化
    function initCoolEffects() {
        // 卡片悬浮效果增强
        $('.card').each(function() {
            $(this).on('mouseenter', function() {
                $(this).addClass('card-hover');
            }).on('mouseleave', function() {
                $(this).removeClass('card-hover');
            });
        });

        // 侧边栏菜单项点击效果
        $('.sidebar .nav-link').on('click', function(e) {
            // 移除其他活动状态
            $('.sidebar .nav-link').removeClass('active');
            // 添加当前活动状态
            $(this).addClass('active');

            // 添加点击波纹效果
            createRippleEffect(this, e);
        });

        // 按钮点击波纹效果
        $('.btn').on('click', function(e) {
            createRippleEffect(this, e);
        });

        // 数字计数动画
        animateNumbers();
    }

    // 创建波纹效果
    function createRippleEffect(element, event) {
        const $element = $(element);
        const $ripple = $('<span class="ripple"></span>');

        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        $ripple.css({
            width: size,
            height: size,
            left: x,
            top: y
        });

        $element.append($ripple);

        setTimeout(() => {
            $ripple.remove();
        }, 600);
    }

    // 数字计数动画
    function animateNumbers() {
        $('.text-gray-800').each(function() {
            const $this = $(this);
            const finalNumber = parseInt($this.text()) || 0;

            if (finalNumber > 0) {
                $this.text('0');
                $({ counter: 0 }).animate({ counter: finalNumber }, {
                    duration: 2000,
                    easing: 'swing',
                    step: function() {
                        $this.text(Math.ceil(this.counter));
                    }
                });
            }
        });
    }
    
    // 侧边栏切换（移动端）
    $('#sidebarToggle').on('click', function() {
        $('.sidebar').toggleClass('show');
        $('.sidebar-overlay').toggleClass('show');
    });

    // 点击遮罩层隐藏侧边栏
    $('.sidebar-overlay').on('click', function() {
        $('.sidebar').removeClass('show');
        $('.sidebar-overlay').removeClass('show');
    });

    // 点击外部区域隐藏侧边栏（移动端）
    $(document).on('click', function(e) {
        if ($(window).width() <= 767.98) {
            if (!$(e.target).closest('.sidebar, #sidebarToggle').length) {
                $('.sidebar').removeClass('show');
                $('.sidebar-overlay').removeClass('show');
            }
        }
    });

    // 窗口大小改变时处理侧边栏
    $(window).resize(function() {
        if ($(window).width() > 767.98) {
            $('.sidebar').removeClass('show');
            $('.sidebar-overlay').removeClass('show');
        }
    });
    
    // 确认删除对话框 - 禁用原生confirm，使用页面自定义模态框
    $('.btn-delete').on('click', function(e) {
        e.preventDefault();
        // 不执行任何操作，让页面自己的删除确认模态框处理
        return false;
    });

    // 统一删除确认组件初始化
    initDeleteConfirmSystem();
    
    // 批量操作
    $('#selectAll').on('change', function() {
        $('.item-checkbox').prop('checked', this.checked);
        updateBatchActions();
    });
    
    $('.item-checkbox').on('change', function() {
        updateBatchActions();
    });
    
    function updateBatchActions() {
        var checkedCount = $('.item-checkbox:checked').length;
        if (checkedCount > 0) {
            $('.batch-actions').show();
            $('.batch-count').text(checkedCount);
        } else {
            $('.batch-actions').hide();
        }
    }
    
    // 表单验证
    $('form.needs-validation').on('submit', function(e) {
        if (!this.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        $(this).addClass('was-validated');
    });

    // 设置页面表单验证
    $('form').on('submit', function(e) {
        var form = $(this);
        var isValid = true;

        // 检查必填字段
        form.find('[required]').each(function() {
            if (!$(this).val().trim()) {
                isValid = false;
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            showAlert('请填写所有必填字段！', 'danger');
        }
    });

    // 输入验证
    $('[required]').on('blur', function() {
        if (!$(this).val().trim()) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });

    // 自动保存提示
    $('input, textarea').on('change', function() {
        var form = $(this).closest('form');
        if (!form.hasClass('changed')) {
            form.addClass('changed');
            //form.find('button[type="submit"]').addClass('btn-warning').removeClass('btn-primary');
        }
    });

    // 提交后重置状态
    $('form').on('submit', function() {
        $(this).removeClass('changed');
        //$(this).find('button[type="submit"]').removeClass('btn-warning').addClass('btn-primary');
    });
    
    // 自动保存草稿
    var autoSaveTimer;
    $('.auto-save').on('input', function() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(function() {
            saveDraft();
        }, 2000);
    });
    
    function saveDraft() {
        // 实现自动保存功能
    }
    
    // 图片预览
    $('.image-upload').on('change', function() {
        var input = this;
        var preview = $(this).siblings('.image-preview');
        
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                preview.attr('src', e.target.result).show();
            };
            reader.readAsDataURL(input.files[0]);
        }
    });
    
    // 富文本编辑器初始化（如果使用）
    if (typeof tinymce !== 'undefined') {
        tinymce.init({
            selector: '.rich-editor',
            height: 400,
            plugins: 'advlist autolink lists link image charmap print preview anchor',
            toolbar: 'undo redo | formatselect | bold italic backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help'
        });
    }
    
    // 数据表格排序
    $('.sortable th').on('click', function() {
        var column = $(this).data('column');
        var order = $(this).hasClass('asc') ? 'desc' : 'asc';
        
        // 移除其他列的排序类
        $('.sortable th').removeClass('asc desc');
        $(this).addClass(order);
        
        // 重新加载数据
        loadTableData(column, order);
    });
    
    function loadTableData(column, order) {
        // 实现表格数据加载
    }
    
    // 状态切换
    $('.status-toggle').on('change', function() {
        var id = $(this).data('id');
        var status = $(this).prop('checked') ? 1 : 0;
        var url = $(this).data('url');
        
        $.ajax({
            url: url,
            method: 'POST',
            data: {
                id: id,
                status: status
            },
            success: function(response) {
                if (response.success) {
                    showAlert('状态更新成功', 'success');
                } else {
                    showAlert('状态更新失败', 'danger');
                }
            },
            error: function() {
                showAlert('网络错误', 'danger');
            }
        });
    });
    
    // 显示提示消息
    function showAlert(message, type) {
        var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                       message +
                       '<button type="button" class="close" data-dismiss="alert">' +
                       '<span>&times;</span></button></div>';
        
        $('.alerts-container').html(alertHtml);
        
        // 3秒后自动隐藏
        setTimeout(function() {
            $('.alert').alert('close');
        }, 3000);
    }
});

/**
 * 统一删除确认系统
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * 现代化删除确认对话框 - ThinkPHP6企业级应用
 */

// 删除确认系统全局变量
let deleteConfirmSystem = {
    modal: null,
    deleteId: null,
    deleteTitle: '',
    deleteUrl: '',
    onConfirm: null,
    onCancel: null
};

/**
 * 初始化删除确认系统
 */
function initDeleteConfirmSystem() {
    // 获取模态框元素
    deleteConfirmSystem.modal = document.getElementById('deleteModal');

    if (!deleteConfirmSystem.modal) {
        console.warn('删除确认模态框未找到，请确保页面包含 message.html');
        return;
    }

    // 绑定事件
    bindDeleteConfirmEvents();

    // 键盘事件
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && deleteConfirmSystem.modal.classList.contains('show')) {
            closeDeleteModal();
        }
    });

    // 点击背景关闭
    deleteConfirmSystem.modal.addEventListener('click', function(e) {
        if (e.target === this) {
            closeDeleteModal();
        }
    });

    // 窗口滚动和大小改变时重新定位
    window.addEventListener('scroll', function() {
        if (deleteConfirmSystem.modal && deleteConfirmSystem.modal.classList.contains('show')) {
            positionModalInViewport();
        }
    });

    window.addEventListener('resize', function() {
        if (deleteConfirmSystem.modal && deleteConfirmSystem.modal.classList.contains('show')) {
            positionModalInViewport();
        }
    });
}

/**
 * 绑定删除确认事件
 */
function bindDeleteConfirmEvents() {
    // 确认删除按钮
    const confirmBtn = document.getElementById('confirmDeleteBtn');
    if (confirmBtn) {
        confirmBtn.onclick = function() {
            performDelete();
        };
    }

    // 取消按钮和关闭按钮已在HTML中绑定onclick="closeDeleteModal()"
}

/**
 * 显示删除确认对话框
 * @param {Object} options 配置选项
 */
function showDeleteConfirm(options) {
    const defaults = {
        id: null,
        title: '此项目',
        message: '',
        url: '',
        type: 'item', // item, category, template等
        onConfirm: null,
        onCancel: null
    };

    const config = Object.assign({}, defaults, options);

    // 保存配置
    deleteConfirmSystem.deleteId = config.id;
    deleteConfirmSystem.deleteTitle = config.title;
    deleteConfirmSystem.deleteUrl = config.url;
    deleteConfirmSystem.onConfirm = config.onConfirm;
    deleteConfirmSystem.onCancel = config.onCancel;

    // 更新模态框内容
    updateDeleteModalContent(config);

    // 计算当前可视区域中心位置
    positionModalInViewport();

    // 显示模态框
    if (deleteConfirmSystem.modal) {
        deleteConfirmSystem.modal.classList.add('show');

        // 聚焦到取消按钮（安全选择）
        setTimeout(() => {
            const cancelBtn = deleteConfirmSystem.modal.querySelector('.btn-cancel');
            if (cancelBtn) {
                cancelBtn.focus();
            }
        }, 100);
    }
}



/**
 * 计算并设置模态框在当前可视区域的中心位置
 */
function positionModalInViewport() {
    if (!deleteConfirmSystem.modal) return;

    // 获取当前滚动位置和视窗尺寸
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 获取模态框尺寸
    const modalWidth = 400; // CSS中设置的宽度
    const modalHeight = 200; // 估算高度

    // 计算居中位置
    const centerX = scrollLeft + (viewportWidth - modalWidth) / 2;
    const centerY = scrollTop + (viewportHeight - modalHeight) / 2;

    // 设置位置
    deleteConfirmSystem.modal.style.left = '35%';
    deleteConfirmSystem.modal.style.top = Math.max(0, centerY) + 'px';
}

/**
 * 更新删除模态框内容
 */
function updateDeleteModalContent(config) {
    // 更新删除项目标题显示
    const deleteItemTitle = document.getElementById('deleteItemTitle');
    if (deleteItemTitle && config.title) {
        deleteItemTitle.innerHTML = `<strong>"${config.title}"</strong>`;

    }
}

/**
 * 关闭删除确认模态框
 */
function closeDeleteModal() {
    if (deleteConfirmSystem.modal) {
        deleteConfirmSystem.modal.classList.remove('show');
    }

    // 执行取消回调
    if (deleteConfirmSystem.onCancel && typeof deleteConfirmSystem.onCancel === 'function') {
        deleteConfirmSystem.onCancel();
    }

    // 重置状态
    resetDeleteConfirmState();
}

/**
 * 执行删除操作
 */
function performDelete() {
    if (!deleteConfirmSystem.deleteId) {
        console.error('删除ID未设置');
        return;
    }

    // 显示加载状态
    const confirmBtn = document.getElementById('confirmDeleteBtn');
    if (confirmBtn) {
        const originalText = confirmBtn.innerHTML;
        confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 删除中...';
        confirmBtn.disabled = true;

        // 如果有自定义确认回调，执行它
        if (deleteConfirmSystem.onConfirm && typeof deleteConfirmSystem.onConfirm === 'function') {
            try {
                deleteConfirmSystem.onConfirm(deleteConfirmSystem.deleteId, deleteConfirmSystem.deleteUrl);
            } catch (error) {
                console.error('删除确认回调执行失败:', error);
                // 恢复按钮状态
                confirmBtn.innerHTML = originalText;
                confirmBtn.disabled = false;
            }
            return;
        }

        // 默认删除逻辑
        executeDefaultDelete(originalText);
    }
}

/**
 * 执行默认删除逻辑
 */
function executeDefaultDelete(originalButtonText) {
    let url = deleteConfirmSystem.deleteUrl;

    // 如果没有提供URL，根据当前页面自动构建
    if (!url) {
        url = buildDeleteUrl();
    }

    if (!url) {
        console.error('无法确定删除URL');
        restoreDeleteButton(originalButtonText);
        return;
    }

    // 执行删除请求
    fetch(url, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (response.ok) {
            // 删除成功，刷新页面
            window.location.reload();
        } else {
            throw new Error('删除请求失败');
        }
    })
    .catch(error => {
        console.error('删除失败:', error);
        restoreDeleteButton(originalButtonText);
        showMessage('删除失败，请重试', 'danger');
        closeDeleteModal();
    });
}

/**
 * 构建删除URL - 备用方案，优先使用传入的URL
 */
function buildDeleteUrl() {
    const currentPath = window.location.pathname;
    const id = deleteConfirmSystem.deleteId;

    // 简单的备用URL构建，主要依赖传入的URL参数
    if (currentPath.includes('/admin/')) {
        const module = currentPath.split('/admin/')[1];
        return `/admin/${module}?action=delete&id=${id}`;
    }

    return null;
}

/**
 * 恢复删除按钮状态
 */
function restoreDeleteButton(originalText) {
    const confirmBtn = document.getElementById('confirmDeleteBtn');
    if (confirmBtn) {
        confirmBtn.innerHTML = originalText;
        confirmBtn.disabled = false;
    }
}

/**
 * 重置删除确认状态
 */
function resetDeleteConfirmState() {
    deleteConfirmSystem.deleteId = null;
    deleteConfirmSystem.deleteTitle = '';
    deleteConfirmSystem.deleteUrl = '';
    deleteConfirmSystem.onConfirm = null;
    deleteConfirmSystem.onCancel = null;
    
    // 清空标题显示
    const deleteItemTitle = document.getElementById('deleteItemTitle');
    if (deleteItemTitle) {
        deleteItemTitle.innerHTML = '';
    }
}

// 兼容性函数 - 保持向后兼容
function confirmDelete(id, title, url = null) {
    deleteItem(id, title, url);
}

function deleteItem(id, title, url = null) {
    showDeleteConfirm({
        id: id,
        title: title,
        url: url
    });
}

// 全局函数
window.adminUtils = {
    // 格式化文件大小
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        var k = 1024;
        var sizes = ['Bytes', 'KB', 'MB', 'GB'];
        var i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 格式化日期
    formatDate: function(date) {
        return new Date(date).toLocaleString('zh-CN');
    }
};

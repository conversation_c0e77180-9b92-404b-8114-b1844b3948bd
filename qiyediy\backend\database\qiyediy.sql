-- QiyeDIY企业建站系统数据库结构
-- 三只鱼网络科技 | 韩总 | 2024-12-19
-- MySQL 8.0+ 兼容

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 用户和权限管理模块
-- ----------------------------

-- 用户表
DROP TABLE IF EXISTS `qd_users`;
CREATE TABLE `qd_users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `email_verified_at` timestamp NULL DEFAULT NULL COMMENT '邮箱验证时间',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `login_count` int unsigned NOT NULL DEFAULT '0' COMMENT '登录次数',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_username_unique` (`username`),
  UNIQUE KEY `users_email_unique` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 角色表
DROP TABLE IF EXISTS `qd_roles`;
CREATE TABLE `qd_roles` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `slug` varchar(50) NOT NULL COMMENT '角色标识',
  `description` text COMMENT '角色描述',
  `permissions` json DEFAULT NULL COMMENT '权限列表',
  `is_default` tinyint NOT NULL DEFAULT '0' COMMENT '是否默认角色',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `roles_slug_unique` (`slug`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 用户角色关联表
DROP TABLE IF EXISTS `qd_user_roles`;
CREATE TABLE `qd_user_roles` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `role_id` bigint unsigned NOT NULL COMMENT '角色ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_roles_user_role_unique` (`user_id`,`role_id`),
  KEY `fk_user_roles_role_id` (`role_id`),
  CONSTRAINT `fk_user_roles_role_id` FOREIGN KEY (`role_id`) REFERENCES `qd_roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_roles_user_id` FOREIGN KEY (`user_id`) REFERENCES `qd_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- 权限表
DROP TABLE IF EXISTS `qd_permissions`;
CREATE TABLE `qd_permissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `name` varchar(100) NOT NULL COMMENT '权限名称',
  `slug` varchar(100) NOT NULL COMMENT '权限标识',
  `description` text COMMENT '权限描述',
  `module` varchar(50) NOT NULL COMMENT '所属模块',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `resource` varchar(100) DEFAULT NULL COMMENT '资源标识',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `permissions_slug_unique` (`slug`),
  KEY `idx_module` (`module`),
  KEY `idx_action` (`action`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- ----------------------------
-- DIY系统核心模块
-- ----------------------------

-- DIY组件表
DROP TABLE IF EXISTS `qd_diy_components`;
CREATE TABLE `qd_diy_components` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '组件ID',
  `name` varchar(100) NOT NULL COMMENT '组件名称',
  `slug` varchar(100) NOT NULL COMMENT '组件标识',
  `type` varchar(50) NOT NULL COMMENT '组件类型',
  `category` varchar(50) NOT NULL COMMENT '组件分类',
  `description` text COMMENT '组件描述',
  `icon` varchar(100) DEFAULT NULL COMMENT '组件图标',
  `preview` varchar(255) DEFAULT NULL COMMENT '预览图',
  `config` json DEFAULT NULL COMMENT '组件配置',
  `default_props` json DEFAULT NULL COMMENT '默认属性',
  `style_config` json DEFAULT NULL COMMENT '样式配置',
  `is_system` tinyint NOT NULL DEFAULT '0' COMMENT '是否系统组件',
  `is_active` tinyint NOT NULL DEFAULT '1' COMMENT '是否启用',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `usage_count` int unsigned NOT NULL DEFAULT '0' COMMENT '使用次数',
  `created_by` bigint unsigned DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `diy_components_slug_unique` (`slug`),
  KEY `idx_type` (`type`),
  KEY `idx_category` (`category`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `fk_diy_components_created_by` (`created_by`),
  CONSTRAINT `fk_diy_components_created_by` FOREIGN KEY (`created_by`) REFERENCES `qd_users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='DIY组件表';

-- DIY模板表
DROP TABLE IF EXISTS `qd_diy_templates`;
CREATE TABLE `qd_diy_templates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `slug` varchar(100) NOT NULL COMMENT '模板标识',
  `description` text COMMENT '模板描述',
  `preview` varchar(255) DEFAULT NULL COMMENT '预览图',
  `thumbnail` varchar(255) DEFAULT NULL COMMENT '缩略图',
  `content` longtext COMMENT '模板内容(JSON格式)',
  `category` varchar(50) NOT NULL COMMENT '模板分类',
  `tags` json DEFAULT NULL COMMENT '模板标签',
  `is_system` tinyint NOT NULL DEFAULT '0' COMMENT '是否系统模板',
  `is_free` tinyint NOT NULL DEFAULT '1' COMMENT '是否免费',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '模板价格',
  `download_count` int unsigned NOT NULL DEFAULT '0' COMMENT '下载次数',
  `rating` decimal(3,2) DEFAULT '0.00' COMMENT '评分',
  `rating_count` int unsigned NOT NULL DEFAULT '0' COMMENT '评分人数',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `created_by` bigint unsigned DEFAULT NULL COMMENT '创建者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `diy_templates_slug_unique` (`slug`),
  KEY `idx_category` (`category`),
  KEY `idx_is_free` (`is_free`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_rating` (`rating`),
  KEY `fk_diy_templates_created_by` (`created_by`),
  CONSTRAINT `fk_diy_templates_created_by` FOREIGN KEY (`created_by`) REFERENCES `qd_users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='DIY模板表';

-- DIY页面表
DROP TABLE IF EXISTS `qd_diy_pages`;
CREATE TABLE `qd_diy_pages` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '页面ID',
  `title` varchar(200) NOT NULL COMMENT '页面标题',
  `slug` varchar(200) NOT NULL COMMENT '页面别名',
  `description` text COMMENT '页面描述',
  `content` longtext COMMENT '页面内容(JSON格式)',
  `template_id` bigint unsigned DEFAULT NULL COMMENT '模板ID',
  `layout` varchar(50) DEFAULT 'default' COMMENT '布局类型',
  `theme` varchar(50) DEFAULT 'default' COMMENT '主题样式',
  `custom_css` text COMMENT '自定义CSS',
  `custom_js` text COMMENT '自定义JS',
  `seo_title` varchar(200) DEFAULT NULL COMMENT 'SEO标题',
  `seo_description` text COMMENT 'SEO描述',
  `seo_keywords` varchar(500) DEFAULT NULL COMMENT 'SEO关键词',
  `og_image` varchar(255) DEFAULT NULL COMMENT 'OG图片',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态:0草稿,1发布,2下线',
  `is_home` tinyint NOT NULL DEFAULT '0' COMMENT '是否首页',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `view_count` int unsigned NOT NULL DEFAULT '0' COMMENT '浏览次数',
  `created_by` bigint unsigned DEFAULT NULL COMMENT '创建者',
  `updated_by` bigint unsigned DEFAULT NULL COMMENT '更新者',
  `published_at` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `diy_pages_slug_unique` (`slug`),
  KEY `idx_status` (`status`),
  KEY `idx_is_home` (`is_home`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_created_by` (`created_by`),
  KEY `fk_diy_pages_template_id` (`template_id`),
  CONSTRAINT `fk_diy_pages_created_by` FOREIGN KEY (`created_by`) REFERENCES `qd_users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_diy_pages_template_id` FOREIGN KEY (`template_id`) REFERENCES `qd_diy_templates` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='DIY页面表';

-- DIY页面区块表
DROP TABLE IF EXISTS `qd_diy_page_blocks`;
CREATE TABLE `qd_diy_page_blocks` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '区块ID',
  `page_id` bigint unsigned NOT NULL COMMENT '页面ID',
  `component_id` bigint unsigned NOT NULL COMMENT '组件ID',
  `block_id` varchar(100) NOT NULL COMMENT '区块标识',
  `parent_id` bigint unsigned DEFAULT NULL COMMENT '父区块ID',
  `props` json DEFAULT NULL COMMENT '组件属性',
  `styles` json DEFAULT NULL COMMENT '样式配置',
  `children` json DEFAULT NULL COMMENT '子组件',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `is_visible` tinyint NOT NULL DEFAULT '1' COMMENT '是否可见',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_page_id` (`page_id`),
  KEY `idx_component_id` (`component_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_sort_order` (`sort_order`),
  CONSTRAINT `fk_diy_page_blocks_component_id` FOREIGN KEY (`component_id`) REFERENCES `qd_diy_components` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_diy_page_blocks_page_id` FOREIGN KEY (`page_id`) REFERENCES `qd_diy_pages` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_diy_page_blocks_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `qd_diy_page_blocks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='DIY页面区块表';

-- ----------------------------
-- 内容管理模块
-- ----------------------------

-- 统一内容表
DROP TABLE IF EXISTS `qd_contents`;
CREATE TABLE `qd_contents` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '内容ID',
  `type` varchar(50) NOT NULL COMMENT '内容类型:article,product,case,page',
  `title` varchar(200) NOT NULL COMMENT '标题',
  `slug` varchar(200) NOT NULL COMMENT '别名',
  `excerpt` text COMMENT '摘要',
  `content` longtext COMMENT '内容',
  `featured_image` varchar(255) DEFAULT NULL COMMENT '特色图片',
  `images` json DEFAULT NULL COMMENT '图片集合',
  `meta_data` json DEFAULT NULL COMMENT '元数据',
  `category_id` bigint unsigned DEFAULT NULL COMMENT '分类ID',
  `author_id` bigint unsigned DEFAULT NULL COMMENT '作者ID',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态:0草稿,1发布,2下线',
  `is_featured` tinyint NOT NULL DEFAULT '0' COMMENT '是否推荐',
  `is_top` tinyint NOT NULL DEFAULT '0' COMMENT '是否置顶',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `view_count` int unsigned NOT NULL DEFAULT '0' COMMENT '浏览次数',
  `like_count` int unsigned NOT NULL DEFAULT '0' COMMENT '点赞次数',
  `comment_count` int unsigned NOT NULL DEFAULT '0' COMMENT '评论次数',
  `seo_title` varchar(200) DEFAULT NULL COMMENT 'SEO标题',
  `seo_description` text COMMENT 'SEO描述',
  `seo_keywords` varchar(500) DEFAULT NULL COMMENT 'SEO关键词',
  `published_at` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `contents_type_slug_unique` (`type`,`slug`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_author_id` (`author_id`),
  KEY `idx_is_featured` (`is_featured`),
  KEY `idx_is_top` (`is_top`),
  KEY `idx_published_at` (`published_at`),
  CONSTRAINT `fk_contents_author_id` FOREIGN KEY (`author_id`) REFERENCES `qd_users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='统一内容表';

-- 内容分类表
DROP TABLE IF EXISTS `qd_content_categories`;
CREATE TABLE `qd_content_categories` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `slug` varchar(100) NOT NULL COMMENT '分类别名',
  `type` varchar(50) NOT NULL COMMENT '分类类型',
  `description` text COMMENT '分类描述',
  `image` varchar(255) DEFAULT NULL COMMENT '分类图片',
  `parent_id` bigint unsigned DEFAULT NULL COMMENT '父分类ID',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `is_active` tinyint NOT NULL DEFAULT '1' COMMENT '是否启用',
  `seo_title` varchar(200) DEFAULT NULL COMMENT 'SEO标题',
  `seo_description` text COMMENT 'SEO描述',
  `seo_keywords` varchar(500) DEFAULT NULL COMMENT 'SEO关键词',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `content_categories_type_slug_unique` (`type`,`slug`),
  KEY `idx_type` (`type`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_is_active` (`is_active`),
  CONSTRAINT `fk_content_categories_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `qd_content_categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容分类表';

-- 内容标签表
DROP TABLE IF EXISTS `qd_content_tags`;
CREATE TABLE `qd_content_tags` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  `slug` varchar(50) NOT NULL COMMENT '标签别名',
  `color` varchar(7) DEFAULT '#007bff' COMMENT '标签颜色',
  `usage_count` int unsigned NOT NULL DEFAULT '0' COMMENT '使用次数',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `content_tags_slug_unique` (`slug`),
  KEY `idx_usage_count` (`usage_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容标签表';

-- 内容标签关联表
DROP TABLE IF EXISTS `qd_content_tag_relations`;
CREATE TABLE `qd_content_tag_relations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `content_id` bigint unsigned NOT NULL COMMENT '内容ID',
  `tag_id` bigint unsigned NOT NULL COMMENT '标签ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `content_tag_relations_unique` (`content_id`,`tag_id`),
  KEY `fk_content_tag_relations_tag_id` (`tag_id`),
  CONSTRAINT `fk_content_tag_relations_content_id` FOREIGN KEY (`content_id`) REFERENCES `qd_contents` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_content_tag_relations_tag_id` FOREIGN KEY (`tag_id`) REFERENCES `qd_content_tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容标签关联表';

-- 媒体文件表
DROP TABLE IF EXISTS `qd_media_files`;
CREATE TABLE `qd_media_files` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `filename` varchar(255) NOT NULL COMMENT '文件名',
  `original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `mime_type` varchar(100) NOT NULL COMMENT 'MIME类型',
  `size` bigint unsigned NOT NULL COMMENT '文件大小',
  `path` varchar(500) NOT NULL COMMENT '文件路径',
  `url` varchar(500) DEFAULT NULL COMMENT '访问URL',
  `alt_text` varchar(200) DEFAULT NULL COMMENT '替代文本',
  `caption` text COMMENT '说明文字',
  `meta_data` json DEFAULT NULL COMMENT '元数据',
  `storage_driver` varchar(50) DEFAULT 'local' COMMENT '存储驱动',
  `folder` varchar(200) DEFAULT NULL COMMENT '文件夹',
  `uploaded_by` bigint unsigned DEFAULT NULL COMMENT '上传者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_mime_type` (`mime_type`),
  KEY `idx_folder` (`folder`),
  KEY `idx_uploaded_by` (`uploaded_by`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_media_files_uploaded_by` FOREIGN KEY (`uploaded_by`) REFERENCES `qd_users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='媒体文件表';

SET FOREIGN_KEY_CHECKS = 1;

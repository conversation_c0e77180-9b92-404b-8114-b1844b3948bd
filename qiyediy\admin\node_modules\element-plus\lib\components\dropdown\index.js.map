{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/dropdown/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON><PERSON>Install } from '@element-plus/utils'\n\nimport Dropdown from './src/dropdown.vue'\nimport DropdownItem from './src/dropdown-item.vue'\nimport DropdownMenu from './src/dropdown-menu.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElDropdown: SFCWithInstall<typeof Dropdown> & {\n  DropdownItem: typeof DropdownItem\n  DropdownMenu: typeof DropdownMenu\n} = withInstall(Dropdown, {\n  DropdownItem,\n  DropdownMenu,\n})\nexport default ElDropdown\nexport const ElDropdownItem: SFCWithInstall<typeof DropdownItem> =\n  withNoopInstall(DropdownItem)\nexport const ElDropdownMenu: SFCWithInstall<typeof DropdownMenu> =\n  withNoopInstall(DropdownMenu)\nexport * from './src/dropdown'\nexport * from './src/instance'\nexport * from './src/tokens'\n"], "names": ["withInstall", "Dropdown", "DropdownItem", "DropdownMenu", "withNoopInstall"], "mappings": ";;;;;;;;;;;AAIY,MAAC,UAAU,GAAGA,mBAAW,CAACC,qBAAQ,EAAE;AAChD,gBAAEC,uBAAY;AACd,gBAAEC,uBAAY;AACd,CAAC,EAAE;AAES,MAAC,cAAc,GAAGC,uBAAe,CAACF,uBAAY,EAAE;AAChD,MAAC,cAAc,GAAGE,uBAAe,CAACD,uBAAY;;;;;;;;;;;;;;;;;;;"}
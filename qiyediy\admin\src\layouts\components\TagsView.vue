<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 标签页视图组件
-->

<template>
  <div class="tags-view-container">
    <div class="tags-view-wrapper" ref="scrollContainer">
      <div class="tags-view-content" ref="scrollContent">
        <router-link
          v-for="tag in tagsViewStore.visitedViews"
          :key="tag.path"
          :to="{ path: tag.path, query: tag.query }"
          class="tags-view-item"
          :class="{
            'is-active': isActive(tag),
            'is-affix': tag.affix
          }"
          @click.middle="!tag.affix && closeSelectedTag(tag)"
          @contextmenu.prevent="openMenu(tag, $event)"
        >
          <span class="tag-title">{{ tag.title }}</span>
          <el-icon
            v-if="!tag.affix"
            class="tag-close"
            @click.prevent.stop="closeSelectedTag(tag)"
          >
            <Close />
          </el-icon>
        </router-link>
      </div>
    </div>

    <!-- 右键菜单 -->
    <ul
      v-show="visible"
      :style="{ left: left + 'px', top: top + 'px' }"
      class="contextmenu"
    >
      <li @click="refreshSelectedTag(selectedTag)">
        <el-icon><Refresh /></el-icon>
        刷新页面
      </li>
      <li v-if="!selectedTag?.affix" @click="closeSelectedTag(selectedTag)">
        <el-icon><Close /></el-icon>
        关闭标签
      </li>
      <li @click="closeOthersTags">
        <el-icon><Remove /></el-icon>
        关闭其他
      </li>
      <li @click="closeAllTags(selectedTag)">
        <el-icon><CircleClose /></el-icon>
        关闭所有
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTagsViewStore, type TagView } from '@/store/modules/tagsView'
import { Close, Refresh, Remove, CircleClose } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const tagsViewStore = useTagsViewStore()

// 引用
const scrollContainer = ref<HTMLElement>()
const scrollContent = ref<HTMLElement>()

// 右键菜单状态
const visible = ref(false)
const top = ref(0)
const left = ref(0)
const selectedTag = ref<TagView | null>(null)

// 计算属性
const visitedViews = computed(() => tagsViewStore.visitedViews)

/**
 * 判断是否为活动标签
 */
const isActive = (tag: TagView) => {
  return tag.path === route.path
}

/**
 * 关闭选中的标签
 */
const closeSelectedTag = async (view: TagView | null) => {
  if (!view || view.affix) return
  
  const { visitedViews } = await tagsViewStore.delView(view)
  
  if (isActive(view)) {
    toLastView(visitedViews, view)
  }
}

/**
 * 关闭其他标签
 */
const closeOthersTags = async () => {
  if (!selectedTag.value) return
  
  await router.push(selectedTag.value.path)
  await tagsViewStore.delOthersViews(selectedTag.value)
  
  moveToCurrentTag()
}

/**
 * 关闭所有标签
 */
const closeAllTags = async (view: TagView | null) => {
  const { visitedViews } = await tagsViewStore.delAllViews()
  
  if (visitedViews.some(tag => tag.path === route.path)) {
    return
  }
  
  toLastView(visitedViews, view)
}

/**
 * 刷新选中的标签
 */
const refreshSelectedTag = async (view: TagView | null) => {
  if (!view) return
  
  await tagsViewStore.delCachedView(view)
  
  const { fullPath } = view
  
  await nextTick()
  
  await router.replace({
    path: '/redirect' + fullPath
  })
}

/**
 * 跳转到最后一个视图
 */
const toLastView = (visitedViews: TagView[], view: TagView | null) => {
  const latestView = visitedViews.slice(-1)[0]
  
  if (latestView) {
    router.push(latestView.fullPath)
  } else {
    // 如果没有标签了，跳转到首页
    if (view?.name === 'Dashboard') {
      router.replace({ path: '/redirect/dashboard' })
    } else {
      router.push('/')
    }
  }
}

/**
 * 打开右键菜单
 */
const openMenu = (tag: TagView, e: MouseEvent) => {
  const menuMinWidth = 105
  const offsetLeft = scrollContainer.value?.getBoundingClientRect().left || 0
  const offsetWidth = scrollContainer.value?.offsetWidth || 0
  const maxLeft = offsetWidth - menuMinWidth
  const left_ = e.clientX - offsetLeft + 15
  
  if (left_ > maxLeft) {
    left.value = maxLeft
  } else {
    left.value = left_
  }
  
  top.value = e.clientY
  visible.value = true
  selectedTag.value = tag
}

/**
 * 关闭右键菜单
 */
const closeMenu = () => {
  visible.value = false
}

/**
 * 移动到当前标签
 */
const moveToCurrentTag = () => {
  nextTick(() => {
    const tags = scrollContent.value?.querySelectorAll('.tags-view-item') || []
    
    for (const tag of Array.from(tags)) {
      if ((tag as HTMLElement).classList.contains('is-active')) {
        moveToTarget(tag as HTMLElement)
        break
      }
    }
  })
}

/**
 * 移动到目标元素
 */
const moveToTarget = (currentTag: HTMLElement) => {
  const container = scrollContainer.value
  const content = scrollContent.value
  
  if (!container || !content) return
  
  const containerWidth = container.offsetWidth
  const contentWidth = content.offsetWidth
  
  if (contentWidth < containerWidth) return
  
  const currentOffsetLeft = currentTag.offsetLeft
  const currentOffsetWidth = currentTag.offsetWidth
  const currentScrollLeft = container.scrollLeft
  
  if (currentOffsetLeft < currentScrollLeft) {
    // 标签在可视区域左侧
    container.scrollLeft = currentOffsetLeft
  } else if (currentOffsetLeft + currentOffsetWidth > currentScrollLeft + containerWidth) {
    // 标签在可视区域右侧
    container.scrollLeft = currentOffsetLeft + currentOffsetWidth - containerWidth
  }
}

// 监听路由变化
watch(route, () => {
  tagsViewStore.addView(route)
  moveToCurrentTag()
})

// 监听点击事件，关闭右键菜单
watch(visible, (value) => {
  if (value) {
    document.body.addEventListener('click', closeMenu)
  } else {
    document.body.removeEventListener('click', closeMenu)
  }
})

onMounted(() => {
  tagsViewStore.addView(route)
  moveToCurrentTag()
})
</script>

<style lang="scss" scoped>
.tags-view-container {
  height: 40px;
  width: 100%;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  position: relative;
}

.tags-view-wrapper {
  height: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  
  &::-webkit-scrollbar {
    height: 0;
  }
}

.tags-view-content {
  display: flex;
  height: 100%;
  align-items: center;
  padding: 0 8px;
  gap: 4px;
  min-width: min-content;
}

.tags-view-item {
  display: inline-flex;
  align-items: center;
  height: 28px;
  padding: 0 12px;
  font-size: 12px;
  color: var(--el-text-color-regular);
  background: var(--el-fill-color-lighter);
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  text-decoration: none;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: var(--el-fill-color-light);
    color: var(--el-text-color-primary);
  }
  
  &.is-active {
    background: var(--el-color-primary);
    color: #fff;
    border-color: var(--el-color-primary);
    
    .tag-close {
      color: #fff;
      
      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }
  
  &.is-affix {
    .tag-title {
      font-weight: 500;
    }
  }
}

.tag-title {
  margin-right: 4px;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tag-close {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: var(--el-text-color-secondary);
  transition: all 0.3s ease;
  
  &:hover {
    background: var(--el-fill-color);
    color: var(--el-text-color-primary);
  }
}

// 右键菜单样式
.contextmenu {
  margin: 0;
  background: var(--el-bg-color-overlay);
  z-index: 3000;
  position: absolute;
  list-style-type: none;
  padding: 8px 0;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 400;
  color: var(--el-text-color-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--el-border-color-light);
  
  li {
    margin: 0;
    padding: 8px 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s ease;
    
    &:hover {
      background: var(--el-fill-color-light);
    }
    
    .el-icon {
      font-size: 14px;
    }
  }
}

// 暗色主题适配
:deep(.dark) {
  .tags-view-item {
    background: var(--el-fill-color);
    border-color: var(--el-border-color);
    
    &:hover {
      background: var(--el-fill-color-dark);
    }
    
    &.is-active {
      background: var(--el-color-primary);
      border-color: var(--el-color-primary);
    }
  }
  
  .contextmenu {
    background: var(--el-bg-color-overlay);
    border-color: var(--el-border-color);
    
    li:hover {
      background: var(--el-fill-color);
    }
  }
}
</style>

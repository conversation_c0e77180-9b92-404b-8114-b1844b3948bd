<?php /*a:4:{s:53:"D:\EServer\core\www\san.com\app\view\admin\index.html";i:1749680158;s:58:"D:\EServer\core\www\san.com\app\view\admin\common\css.html";i:1749480866;s:61:"D:\EServer\core\www\san.com\app\view\admin\common\header.html";i:1749773302;s:62:"D:\EServer\core\www\san.com\app\view\admin\common\sidebar.html";i:1749580694;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - 三只鱼网络</title>
    <link rel="stylesheet" href="/assets/css/bootstrap.min.css">
<link rel="stylesheet" href="/assets/css/all.min.css">
<link rel="stylesheet" href="/assets/css/admin.css">
<link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="/assets/css/admin/index.css">
</head>
<body>
    <!-- 顶部导航 -->
    <!-- 顶部导航 -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <div class="navbar-left">
            <button class="btn btn-link d-md-none" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
            <a class="navbar-brand" href="/admin">
                <i class="fas fa-cogs"></i>
                <span>后台管理</span>
            </a>
        </div>

        <div class="navbar-nav">
            <a class="nav-link" href="/" target="_blank" style="margin-right: 15px;">
                <i class="fas fa-external-link-alt"></i>
                <span>查看网站</span>
            </a>
            <a class="nav-link" href="javascript:void(0);" onclick="clearAllCache()" style="margin-right: 15px; color: #28a745;">
                <i class="fas fa-broom"></i>
                <span>清理缓存</span>
            </a>
            <span class="nav-link" style="margin-right: 15px; color: rgba(255,255,255,0.8);">
                <i class="fas fa-user"></i>
                <span>
                    <?php if(isset($admin_info['real_name']) && $admin_info['real_name']): ?>
                        <?php echo htmlentities((string) $admin_info['real_name']); elseif(isset($admin_info['username']) && $admin_info['username']): ?>
                        <?php echo htmlentities((string) $admin_info['username']); else: ?>
                        系统管理员
                    <?php endif; ?>
                </span>
            </span>
            <a class="nav-link" href="javascript:void(0);" onclick="confirmLogout()" style="color: #ff6b6b;">
                <i class="fas fa-sign-out-alt"></i>
                <span>退出登录</span>
            </a>
        </div>
    </div>
</nav>

<style>
/* 缓存清理模态框样式 */
.cache-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.cache-modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.cache-modal {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border-radius: 15px;
    padding: 0;
    min-width: 400px;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(0, 255, 255, 0.3);
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.cache-modal-overlay.show .cache-modal {
    transform: scale(1);
}

.cache-modal-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 15px 15px 0 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.cache-modal-header i {
    font-size: 1.5rem;
}

.cache-modal-header h4 {
    margin: 0;
    font-weight: 600;
    font-family: 'Orbitron', monospace;
}

.cache-modal-body {
    padding: 2rem;
    text-align: center;
    color: #ffffff;
}

.loading-spinner {
    margin-bottom: 1rem;
}

.loading-spinner i {
    font-size: 2rem;
    color: #28a745;
}

.cache-modal-body p {
    margin: 0;
    font-size: 1rem;
    line-height: 1.5;
}

/* 退出登录模态框样式优化 */
.logout-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.logout-modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.logout-modal {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border-radius: 15px;
    padding: 0;
    min-width: 400px;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 107, 107, 0.3);
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.logout-modal-overlay.show .logout-modal {
    transform: scale(1);
}

.logout-modal-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 15px 15px 0 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logout-modal-header i {
    font-size: 1.5rem;
}

.logout-modal-header h4 {
    margin: 0;
    font-weight: 600;
    font-family: 'Orbitron', monospace;
}

.logout-modal-body {
    padding: 2rem;
    text-align: center;
    color: #ffffff;
}

.logout-modal-body p {
    margin: 0;
    font-size: 1.1rem;
    line-height: 1.5;
}

.logout-modal-footer {
    padding: 1.5rem 2rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.btn-cancel, .btn-confirm {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-cancel {
    background: #6c757d;
    color: white;
}

.btn-cancel:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-confirm {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
}

.btn-confirm:hover {
    background: linear-gradient(135deg, #ee5a52 0%, #dc3545 100%);
    transform: translateY(-2px);
}
</style>

<script>
// 退出登录确认对话框
function confirmLogout() {
    // 创建自定义确认对话框
    const modal = document.createElement('div');
    modal.className = 'logout-modal-overlay';
    modal.innerHTML = `
        <div class="logout-modal">
            <div class="logout-modal-header">
                <i class="fas fa-sign-out-alt"></i>
                <h4>确认退出登录</h4>
            </div>
            <div class="logout-modal-body">
                <p>您确定要退出登录吗？</p>
            </div>
            <div class="logout-modal-footer">
                <button class="btn-cancel" onclick="closeLogoutModal()">
                    <i class="fas fa-times"></i> 取消
                </button>
                <button class="btn-confirm" onclick="doLogout()">
                    <i class="fas fa-check"></i> 确认退出
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 添加动画效果
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
}

function closeLogoutModal() {
    const modal = document.querySelector('.logout-modal-overlay');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

function doLogout() {
    window.location.href = '/admin/logout';
}

// 点击遮罩层关闭对话框
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('logout-modal-overlay')) {
        closeLogoutModal();
    }
});

// 清理缓存功能
function clearAllCache() {
    // 创建加载提示
    const loadingModal = document.createElement('div');
    loadingModal.className = 'cache-modal-overlay';
    loadingModal.innerHTML = `
        <div class="cache-modal">
            <div class="cache-modal-header">
                <i class="fas fa-broom"></i>
                <h4>正在清理缓存</h4>
            </div>
            <div class="cache-modal-body">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                </div>
                <p id="cache-status">正在清理系统缓存，请稍候...</p>
            </div>
        </div>
    `;

    document.body.appendChild(loadingModal);

    // 添加动画效果
    setTimeout(() => {
        loadingModal.classList.add('show');
    }, 10);

    // 发起清理缓存请求
    fetch('/api/clear-cache', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        const statusElement = document.getElementById('cache-status');
        if (data.success) {
            statusElement.innerHTML = '<i class="fas fa-check-circle" style="color: #28a745;"></i> 缓存清理成功！';
            setTimeout(() => {
                closeCacheModal();
                // 可选：刷新当前页面
                // window.location.reload();
            }, 1500);
        } else {
            statusElement.innerHTML = '<i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i> 清理失败：' + (data.message || '未知错误');
            setTimeout(() => {
                closeCacheModal();
            }, 3000);
        }
    })
    .catch(error => {
        console.error('清理缓存失败:', error);
        const statusElement = document.getElementById('cache-status');
        statusElement.innerHTML = '<i class="fas fa-times-circle" style="color: #dc3545;"></i> 网络错误，请稍后重试';
        setTimeout(() => {
            closeCacheModal();
        }, 3000);
    });
}

function closeCacheModal() {
    const modal = document.querySelector('.cache-modal-overlay');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

</script>


    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <!-- 侧边栏 -->
<nav class="col-md-3 col-lg-2 sidebar">
    <div class="sidebar-sticky">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="/admin" data-controller="Index">
                    <i class="fas fa-tachometer-alt"></i> 仪表盘
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/banners" data-controller="Banners">
                    <i class="fas fa-images"></i> 轮播图管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/products" data-controller="Products">
                    <i class="fas fa-cube"></i> 产品管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/solutions" data-controller="Solutions">
                    <i class="fas fa-lightbulb"></i> 解决方案
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/news" data-controller="News">
                    <i class="fas fa-newspaper"></i> 新闻管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/cases" data-controller="Cases">
                    <i class="fas fa-briefcase"></i> 客户案例
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/contacts" data-controller="Contacts">
                    <i class="fas fa-envelope"></i> 联系表单
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/page-builder" data-controller="PageBuilder">
                    <i class="fas fa-paint-brush"></i> 页面装修
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/sys-menu" data-controller="SysMenu">
                    <i class="fas fa-sitemap"></i> 系统菜单
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/settings" data-controller="Settings">
                    <i class="fas fa-cog"></i> 系统设置
                </a>
            </li>
        </ul>
    </div>
</nav>

<!-- 移动端遮罩层 -->
<div class="sidebar-overlay d-md-none"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取当前URL路径
    const currentPath = window.location.pathname;
    console.log('当前路径:', currentPath);

    // 移除所有active状态
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });

    // 根据URL路径设置active状态
    if (currentPath === '/admin' || currentPath === '/admin/' || currentPath.endsWith('/admin/index')) {
        const indexLink = document.querySelector('[data-controller="Index"]');
        if (indexLink) {
            indexLink.classList.add('active');
            console.log('设置仪表盘为active');
        }
    } else if (currentPath.includes('/admin/banners')) {
        const bannersLink = document.querySelector('[data-controller="Banners"]');
        if (bannersLink) {
            bannersLink.classList.add('active');
            console.log('设置轮播图为active');
        }
    } else if (currentPath.includes('/admin/contacts')) {
        const contactsLink = document.querySelector('[data-controller="Contacts"]');
        if (contactsLink) {
            contactsLink.classList.add('active');
            console.log('设置联系表单为active');
        }
    } else if (currentPath.includes('/admin/products')) {
        const productsLink = document.querySelector('[data-controller="Products"]');
        if (productsLink) {
            productsLink.classList.add('active');
            console.log('设置产品管理为active');
        }
    } else if (currentPath.includes('/admin/solutions')) {
        const solutionsLink = document.querySelector('[data-controller="Solutions"]');
        if (solutionsLink) {
            solutionsLink.classList.add('active');
            console.log('设置解决方案为active');
        }
    } else if (currentPath.includes('/admin/news')) {
        const newsLink = document.querySelector('[data-controller="News"]');
        if (newsLink) {
            newsLink.classList.add('active');
            console.log('设置新闻管理为active');
        }
    } else if (currentPath.includes('/admin/cases')) {
        const casesLink = document.querySelector('[data-controller="Cases"]');
        if (casesLink) {
            casesLink.classList.add('active');
            console.log('设置客户案例为active');
        }
    } else if (currentPath.includes('/admin/page-builder')) {
        const pageBuilderLink = document.querySelector('[data-controller="PageBuilder"]');
        if (pageBuilderLink) {
            pageBuilderLink.classList.add('active');
            console.log('设置页面装修为active');
        }
    } else if (currentPath.includes('/admin/sys-menu')) {
        const sysMenuLink = document.querySelector('[data-controller="SysMenu"]');
        if (sysMenuLink) {
            sysMenuLink.classList.add('active');
            console.log('设置系统菜单为active');
        }
    } else if (currentPath.includes('/admin/settings')) {
        const settingsLink = document.querySelector('[data-controller="Settings"]');
        if (settingsLink) {
            settingsLink.classList.add('active');
            console.log('设置系统设置为active');
        }
    }
});
</script>


            <!-- 主要内容 -->
            <main class="main-content">
                <!-- 内容头部 -->
                <div class="content-header">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-tachometer-alt"></i> 仪表盘
                    </h1>
                </div>

                <!-- 页面内容区域 -->
                <div class="content-body">
                    <!-- 统计卡片 -->
                    <div class="stats-grid">
                        <!-- 联系表单统计 -->
                        <div class="stat-card">
                            <div class="stat-card-content">
                                <div class="stat-icon stat-icon-primary">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-label">联系表单</div>
                                    <div class="stat-value"><?php echo htmlentities((string) (isset($stats['contact_forms']) && ($stats['contact_forms'] !== '')?$stats['contact_forms']:0)); ?></div>
                                    <div class="stat-detail">
                                        <?php if(isset($stats['new_contact_forms']) && $stats['new_contact_forms'] > 0): ?>
                                            <span class="stat-badge stat-badge-new"><?php echo htmlentities((string) $stats['new_contact_forms']); ?> 条新消息</span>
                                        <?php else: ?>
                                            <span class="stat-badge stat-badge-normal">全部已处理</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="stat-card-footer">
                                <a href="/admin/contacts" class="stat-link">
                                    <span>查看详情</span>
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>

                        <!-- 产品统计 -->
                        <div class="stat-card">
                            <div class="stat-card-content">
                                <div class="stat-icon stat-icon-success">
                                    <i class="fas fa-cube"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-label">产品数量</div>
                                    <div class="stat-value"><?php echo htmlentities((string) (isset($stats['products']) && ($stats['products'] !== '')?$stats['products']:0)); ?></div>
                                    <div class="stat-detail">
                                        <span class="stat-badge stat-badge-normal">已发布产品</span>
                                    </div>
                                </div>
                            </div>
                            <div class="stat-card-footer">
                                <a href="/admin/products" class="stat-link">
                                    <span>管理产品</span>
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>

                        <!-- 新闻统计 -->
                        <div class="stat-card">
                            <div class="stat-card-content">
                                <div class="stat-icon stat-icon-info">
                                    <i class="fas fa-newspaper"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-label">新闻文章</div>
                                    <div class="stat-value"><?php echo htmlentities((string) (isset($stats['news']) && ($stats['news'] !== '')?$stats['news']:0)); ?></div>
                                    <div class="stat-detail">
                                        <span class="stat-badge stat-badge-normal">已发布文章</span>
                                    </div>
                                </div>
                            </div>
                            <div class="stat-card-footer">
                                <a href="/admin/news" class="stat-link">
                                    <span>管理新闻</span>
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>

                        <!-- 客户案例统计 -->
                        <div class="stat-card">
                            <div class="stat-card-content">
                                <div class="stat-icon stat-icon-warning">
                                    <i class="fas fa-briefcase"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-label">客户案例</div>
                                    <div class="stat-value"><?php echo htmlentities((string) (isset($stats['cases']) && ($stats['cases'] !== '')?$stats['cases']:0)); ?></div>
                                    <div class="stat-detail">
                                        <span class="stat-badge stat-badge-normal">成功案例</span>
                                    </div>
                                </div>
                            </div>
                            <div class="stat-card-footer">
                                <a href="/admin/cases" class="stat-link">
                                    <span>管理案例</span>
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 最新联系表单 -->
                    <div class="contacts-section">
                        <div class="section-header">
                            <div class="section-title-area">
                                <div class="section-icon">
                                    <i class="fas fa-envelope-open"></i>
                                </div>
                                <div class="section-title-text">
                                    <h3 class="section-title">最新联系表单</h3>
                                    <p class="section-subtitle">最近收到的客户咨询信息</p>
                                </div>
                            </div>
                            <div class="section-actions">
                                <a href="/admin/contacts" class="btn-view-all">
                                    <span>查看全部</span>
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>

                        <div class="contacts-content">
                            <?php if(!empty($latestContacts)): ?>
                                <div class="contacts-list">
                                    <?php foreach($latestContacts as $contact): ?>
                                        <div class="contact-item">
                                            <div class="contact-avatar">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div class="contact-info">
                                                <div class="contact-header">
                                                    <h4 class="contact-name"><?php echo htmlentities((string) $contact['name']); ?></h4>
                                                    <div class="contact-status-actions">
                                                        <div class="contact-status">
                                                            <?php switch($contact['status']): case "new": ?>
                                                                    <span class="status-badge status-new">
                                                                        <i class="fas fa-circle"></i>
                                                                        新消息
                                                                    </span>
                                                                <?php break; case "read": ?>
                                                                    <span class="status-badge status-read">
                                                                        <i class="fas fa-eye"></i>
                                                                        已读
                                                                    </span>
                                                                <?php break; case "replied": ?>
                                                                    <span class="status-badge status-replied">
                                                                        <i class="fas fa-reply"></i>
                                                                        已回复
                                                                    </span>
                                                                <?php break; case "closed": ?>
                                                                    <span class="status-badge status-closed">
                                                                        <i class="fas fa-check-circle"></i>
                                                                        已关闭
                                                                    </span>
                                                                <?php break; default: ?>
                                                                    <span class="status-badge status-unknown">
                                                                        <i class="fas fa-question-circle"></i>
                                                                        未知
                                                                    </span>
                                                            <?php endswitch; ?>
                                                        </div>
                                                        <div class="contact-actions">
                                                            <a href="/admin/contacts?action=view&id=<?php echo htmlentities((string) $contact['id']); ?>" class="btn-action btn-view">
                                                                <i class="fas fa-eye"></i>
                                                                <span>查看</span>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="contact-details">
                                                    <div class="contact-email">
                                                        <i class="fas fa-envelope"></i>
                                                        <span><?php echo htmlentities((string) $contact['email']); ?></span>
                                                    </div>
                                                    <div class="contact-subject">
                                                        <i class="fas fa-tag"></i>
                                                        <span><?php echo htmlentities((string) $contact['subject']); ?></span>
                                                    </div>
                                                    <div class="contact-time">
                                                        <i class="fas fa-clock"></i>
                                                        <span><?php echo htmlentities((string) date('Y-m-d H:i',!is_numeric($contact['created_at'])? strtotime($contact['created_at']) : $contact['created_at'])); ?></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <div class="empty-state">
                                    <div class="empty-icon">
                                        <i class="fas fa-inbox"></i>
                                    </div>
                                    <h3 class="empty-title">暂无联系表单</h3>
                                    <p class="empty-subtitle">还没有收到任何客户咨询信息</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/assets/js/jquery.min.js"></script>
    <script src="/assets/js/bootstrap.bundle.min.js"></script>
    <script src="/assets/js/admin.js"></script>

    <script>
        $(document).ready(function() {
            // 侧边栏切换（移动端）
            $('#sidebarToggle').on('click', function() {
                $('.sidebar').toggleClass('show');
                $('.sidebar-overlay').toggleClass('show');
            });

            // 点击遮罩层隐藏侧边栏
            $('.sidebar-overlay').on('click', function() {
                $('.sidebar').removeClass('show');
                $('.sidebar-overlay').removeClass('show');
            });

            // 数字动画
            $('.stat-value').each(function() {
                const $this = $(this);
                const finalNumber = parseInt($this.text()) || 0;

                if (finalNumber > 0) {
                    $this.text('0');
                    $({ counter: 0 }).animate({ counter: finalNumber }, {
                        duration: 2000,
                        easing: 'swing',
                        step: function() {
                            $this.text(Math.ceil(this.counter));
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>

<!--
  三只鱼网络科技 | 韩总 | 2024-12-20
  QiyeDIY企业建站系统 - 页面预览对话框
-->

<template>
  <el-dialog
    v-model="dialogVisible"
    title="页面预览"
    :width="dialogWidth"
    :fullscreen="isFullscreen"
    class="preview-dialog"
    @close="handleClose"
  >
    <!-- 预览工具栏 -->
    <template #header>
      <div class="preview-header">
        <div class="header-left">
          <h3 class="preview-title">页面预览</h3>
          <span class="preview-url">{{ previewUrl }}</span>
        </div>
        
        <div class="header-right">
          <!-- 设备切换 -->
          <el-radio-group v-model="currentDevice" size="small" @change="handleDeviceChange">
            <el-radio-button label="desktop">
              <el-icon><Monitor /></el-icon>
              桌面端
            </el-radio-button>
            <el-radio-button label="tablet">
              <el-icon><Iphone /></el-icon>
              平板
            </el-radio-button>
            <el-radio-button label="mobile">
              <el-icon><Cellphone /></el-icon>
              手机
            </el-radio-button>
          </el-radio-group>
          
          <!-- 操作按钮 -->
          <el-button-group class="action-buttons">
            <el-button :icon="Refresh" @click="handleRefresh" title="刷新">
              刷新
            </el-button>
            <el-button :icon="Share" @click="handleShare" title="分享">
              分享
            </el-button>
            <el-button 
              :icon="isFullscreen ? 'FullScreen' : 'ScaleToOriginal'" 
              @click="toggleFullscreen" 
              :title="isFullscreen ? '退出全屏' : '全屏'"
            >
              {{ isFullscreen ? '退出全屏' : '全屏' }}
            </el-button>
            <el-button :icon="Link" @click="handleOpenInNewTab" title="新窗口打开">
              新窗口
            </el-button>
          </el-button-group>
        </div>
      </div>
    </template>

    <!-- 预览内容 -->
    <div class="preview-content" :class="deviceClass">
      <div class="preview-container">
        <!-- 设备框架 -->
        <div class="device-frame" :class="frameClass">
          <!-- 设备状态栏 (仅移动端) -->
          <div v-if="currentDevice === 'mobile'" class="device-statusbar">
            <div class="status-left">
              <span class="time">9:41</span>
            </div>
            <div class="status-right">
              <div class="signal-bars">
                <span></span>
                <span></span>
                <span></span>
                <span></span>
              </div>
              <div class="battery">
                <div class="battery-level"></div>
              </div>
            </div>
          </div>
          
          <!-- 浏览器地址栏 (桌面端和平板) -->
          <div v-if="currentDevice !== 'mobile'" class="browser-bar">
            <div class="browser-controls">
              <span class="control close"></span>
              <span class="control minimize"></span>
              <span class="control maximize"></span>
            </div>
            <div class="address-bar">
              <el-icon><Link /></el-icon>
              <span>{{ previewUrl }}</span>
            </div>
          </div>
          
          <!-- 页面内容iframe -->
          <div class="iframe-container">
            <iframe
              ref="previewIframe"
              :src="iframeSrc"
              :style="iframeStyles"
              frameborder="0"
              @load="handleIframeLoad"
            ></iframe>
            
            <!-- 加载状态 -->
            <div v-if="loading" class="loading-overlay">
              <el-icon class="loading-icon"><Loading /></el-icon>
              <span>页面加载中...</span>
            </div>
            
            <!-- 错误状态 -->
            <div v-if="error" class="error-overlay">
              <el-icon class="error-icon"><Warning /></el-icon>
              <div class="error-content">
                <h4>预览失败</h4>
                <p>{{ error }}</p>
                <el-button type="primary" @click="handleRetry">重试</el-button>
              </div>
            </div>
          </div>
          
          <!-- 设备底部 (仅移动端) -->
          <div v-if="currentDevice === 'mobile'" class="device-bottom">
            <div class="home-indicator"></div>
          </div>
        </div>
        
        <!-- 预览信息 -->
        <div class="preview-info">
          <div class="info-item">
            <span class="label">设备:</span>
            <span class="value">{{ deviceInfo.name }}</span>
          </div>
          <div class="info-item">
            <span class="label">分辨率:</span>
            <span class="value">{{ deviceInfo.resolution }}</span>
          </div>
          <div class="info-item">
            <span class="label">缩放:</span>
            <span class="value">{{ Math.round(deviceInfo.scale * 100) }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作 -->
    <template #footer>
      <div class="preview-footer">
        <div class="footer-left">
          <el-button @click="handleEditPage">编辑页面</el-button>
          <el-button @click="handleViewSource">查看源码</el-button>
        </div>
        <div class="footer-right">
          <el-button @click="handleClose">关闭</el-button>
          <el-button type="primary" @click="handlePublish">发布页面</el-button>
        </div>
      </div>
    </template>
  </el-dialog>

  <!-- 分享对话框 -->
  <ShareDialog
    v-model="showShareDialog"
    :url="previewUrl"
    :title="pageTitle"
  />

  <!-- 源码查看对话框 -->
  <SourceCodeDialog
    v-model="showSourceDialog"
    :page-id="pageId"
  />
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useUserStore } from '@/store/modules/user'
import {
  Monitor, Iphone, Cellphone, Refresh, Share, Link, Loading, Warning
} from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
  pageId?: string | number
  device?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'edit'): void
  (e: 'publish'): void
}

const props = withDefaults(defineProps<Props>(), {
  device: 'desktop'
})

const emit = defineEmits<Emits>()

const userStore = useUserStore()

// 响应式数据
const dialogVisible = ref(false)
const currentDevice = ref(props.device)
const isFullscreen = ref(false)
const loading = ref(false)
const error = ref<string | null>(null)
const previewIframe = ref<HTMLIFrameElement>()
const showShareDialog = ref(false)
const showSourceDialog = ref(false)

// 计算属性
const dialogWidth = computed(() => {
  if (isFullscreen.value) return '100%'
  
  switch (currentDevice.value) {
    case 'mobile':
      return '400px'
    case 'tablet':
      return '900px'
    default:
      return '1200px'
  }
})

const deviceClass = computed(() => {
  return `device-${currentDevice.value}`
})

const frameClass = computed(() => {
  return {
    [`frame-${currentDevice.value}`]: true,
    'frame-fullscreen': isFullscreen.value
  }
})

const deviceInfo = computed(() => {
  const devices = {
    desktop: {
      name: '桌面端',
      resolution: '1920x1080',
      scale: 1
    },
    tablet: {
      name: 'iPad',
      resolution: '768x1024',
      scale: 0.8
    },
    mobile: {
      name: 'iPhone',
      resolution: '375x812',
      scale: 0.7
    }
  }
  
  return devices[currentDevice.value] || devices.desktop
})

const previewUrl = computed(() => {
  if (!props.pageId) return ''
  
  const baseUrl = import.meta.env.VITE_FRONTEND_URL || 'http://localhost:3000'
  return `${baseUrl}/preview/${props.pageId}?token=${userStore.token}&device=${currentDevice.value}`
})

const pageTitle = computed(() => {
  return `页面预览 - ${currentDevice.value}`
})

const iframeSrc = computed(() => {
  if (!previewUrl.value) return ''
  
  // 添加时间戳防止缓存
  const timestamp = Date.now()
  return `${previewUrl.value}&t=${timestamp}`
})

const iframeStyles = computed(() => {
  const styles: Record<string, any> = {
    width: '100%',
    height: '100%',
    border: 'none'
  }
  
  // 根据设备类型调整样式
  if (currentDevice.value === 'mobile') {
    styles.transform = `scale(${deviceInfo.value.scale})`
    styles.transformOrigin = 'top left'
    styles.width = `${100 / deviceInfo.value.scale}%`
    styles.height = `${100 / deviceInfo.value.scale}%`
  }
  
  return styles
})

/**
 * 处理设备切换
 */
const handleDeviceChange = () => {
  loading.value = true
  error.value = null
  
  nextTick(() => {
    if (previewIframe.value) {
      previewIframe.value.src = iframeSrc.value
    }
  })
}

/**
 * 处理iframe加载完成
 */
const handleIframeLoad = () => {
  loading.value = false
  error.value = null
}

/**
 * 处理刷新
 */
const handleRefresh = () => {
  if (previewIframe.value) {
    loading.value = true
    error.value = null
    previewIframe.value.src = iframeSrc.value
  }
}

/**
 * 处理分享
 */
const handleShare = () => {
  showShareDialog.value = true
}

/**
 * 切换全屏
 */
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}

/**
 * 在新窗口打开
 */
const handleOpenInNewTab = () => {
  if (previewUrl.value) {
    window.open(previewUrl.value, '_blank')
  }
}

/**
 * 处理编辑页面
 */
const handleEditPage = () => {
  emit('edit')
  handleClose()
}

/**
 * 查看源码
 */
const handleViewSource = () => {
  showSourceDialog.value = true
}

/**
 * 处理发布
 */
const handlePublish = () => {
  emit('publish')
}

/**
 * 处理重试
 */
const handleRetry = () => {
  handleRefresh()
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  dialogVisible.value = false
  emit('update:modelValue', false)
}

// 监听props变化
watch(() => props.modelValue, (value) => {
  dialogVisible.value = value
  if (value) {
    currentDevice.value = props.device
    loading.value = true
    error.value = null
  }
})

watch(() => props.device, (value) => {
  currentDevice.value = value
})

// 监听页面ID变化
watch(() => props.pageId, () => {
  if (dialogVisible.value && props.pageId) {
    handleRefresh()
  }
})
</script>

<style lang="scss" scoped>
.preview-dialog {
  :deep(.el-dialog) {
    margin: 0;
    
    &.is-fullscreen {
      width: 100vw !important;
      height: 100vh !important;
      margin: 0;
      border-radius: 0;
    }
  }
  
  :deep(.el-dialog__header) {
    padding: 0;
    border-bottom: 1px solid var(--el-border-color-light);
  }
  
  :deep(.el-dialog__body) {
    padding: 0;
    height: calc(100vh - 120px);
    overflow: hidden;
  }
  
  :deep(.el-dialog__footer) {
    padding: 16px 20px;
    border-top: 1px solid var(--el-border-color-light);
  }
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: var(--el-bg-color);
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .preview-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
    
    .preview-url {
      font-size: 12px;
      color: var(--el-text-color-secondary);
      background: var(--el-fill-color-light);
      padding: 4px 8px;
      border-radius: 4px;
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .action-buttons {
    :deep(.el-button) {
      padding: 8px 12px;
    }
  }
}

.preview-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  
  &.device-desktop {
    padding: 20px;
  }
  
  &.device-tablet {
    padding: 30px 20px;
  }
  
  &.device-mobile {
    padding: 40px 20px;
  }
}

.preview-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.device-frame {
  position: relative;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  &.frame-desktop {
    width: 100%;
    max-width: 1200px;
    height: 700px;
  }
  
  &.frame-tablet {
    width: 768px;
    height: 600px;
    border-radius: 20px;
  }
  
  &.frame-mobile {
    width: 375px;
    height: 600px;
    border-radius: 25px;
    border: 8px solid #333;
  }
  
  &.frame-fullscreen {
    width: 100% !important;
    height: 100% !important;
    max-width: none !important;
    border-radius: 0 !important;
    border: none !important;
  }
}

.device-statusbar {
  height: 24px;
  background: #000;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  font-size: 12px;
  font-weight: 600;
  
  .status-right {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .signal-bars {
    display: flex;
    gap: 2px;
    
    span {
      width: 3px;
      background: #fff;
      border-radius: 1px;
      
      &:nth-child(1) { height: 4px; }
      &:nth-child(2) { height: 6px; }
      &:nth-child(3) { height: 8px; }
      &:nth-child(4) { height: 10px; }
    }
  }
  
  .battery {
    width: 20px;
    height: 10px;
    border: 1px solid #fff;
    border-radius: 2px;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      right: -3px;
      top: 3px;
      width: 2px;
      height: 4px;
      background: #fff;
      border-radius: 0 1px 1px 0;
    }
    
    .battery-level {
      width: 80%;
      height: 100%;
      background: #4ade80;
      border-radius: 1px;
    }
  }
}

.browser-bar {
  height: 40px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  padding: 0 16px;
  gap: 12px;
  
  .browser-controls {
    display: flex;
    gap: 8px;
    
    .control {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      
      &.close { background: #ff5f57; }
      &.minimize { background: #ffbd2e; }
      &.maximize { background: #28ca42; }
    }
  }
  
  .address-bar {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0 12px;
    height: 28px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
}

.iframe-container {
  position: relative;
  flex: 1;
  width: 100%;
  overflow: hidden;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  gap: 16px;
}

.loading-icon {
  font-size: 32px;
  color: var(--el-color-primary);
  animation: spin 1s linear infinite;
}

.error-icon {
  font-size: 32px;
  color: var(--el-color-danger);
}

.error-content {
  text-align: center;
  
  h4 {
    margin: 0 0 8px 0;
    color: var(--el-text-color-primary);
  }
  
  p {
    margin: 0 0 16px 0;
    color: var(--el-text-color-secondary);
  }
}

.device-bottom {
  height: 20px;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .home-indicator {
    width: 120px;
    height: 4px;
    background: #fff;
    border-radius: 2px;
    opacity: 0.8;
  }
}

.preview-info {
  display: flex;
  gap: 24px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  
  .info-item {
    display: flex;
    gap: 4px;
    
    .label {
      font-weight: 500;
    }
  }
}

.preview-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .footer-left,
  .footer-right {
    display: flex;
    gap: 12px;
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 768px) {
  .preview-header {
    flex-direction: column;
    gap: 12px;
    
    .header-left,
    .header-right {
      width: 100%;
      justify-content: center;
    }
  }
  
  .device-frame {
    &.frame-tablet {
      width: 100%;
      max-width: 600px;
    }
    
    &.frame-mobile {
      width: 100%;
      max-width: 300px;
    }
  }
  
  .preview-info {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
  
  .preview-footer {
    flex-direction: column;
    gap: 12px;
    
    .footer-left,
    .footer-right {
      width: 100%;
      justify-content: center;
    }
  }
}
</style>

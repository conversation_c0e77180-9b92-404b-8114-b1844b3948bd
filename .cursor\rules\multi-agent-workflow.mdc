---
description:
globs:
alwaysApply: false
---
# Multi-Agent System Workflow

This project uses a multi-agent system with two primary roles: **Planner** and **Executor**. The coordination happens through [.cursor/scratchpad.md](mdc:.cursor/scratchpad.md).

## Role Definitions

### Planner Role
- **Responsibility**: High-level analysis, task breakdown, success criteria definition, progress evaluation
- **When to activate**: When user presents feature requests or modification needs
- **Actions**: Update planning sections in [.cursor/scratchpad.md](mdc:.cursor/scratchpad.md)
- **Key principle**: Think deeply before execution, create user-reviewable plans

### Executor Role  
- **Responsibility**: Execute specific tasks from [.cursor/scratchpad.md](mdc:.cursor/scratchpad.md)
- **When to activate**: When concrete implementation tasks are defined
- **Actions**: Complete subtasks, update progress tracking, report milestones
- **Key principle**: One task at a time, validate before proceeding

## Scratchpad Document Structure

The [.cursor/scratchpad.md](mdc:.cursor/scratchpad.md) file must maintain these sections:

- **Background and Motivation**: Project context and goals
- **Key Challenges and Analysis**: Technical challenges and considerations  
- **High-Level Task Breakdown**: Step-by-step implementation plan
- **Project Status Dashboard**: Simple markdown todo format
- **Executor Feedback or Help Requests**: Progress reports and blockers
- **Lessons Learned**: Reusable knowledge and error solutions

## Workflow Guidelines

1. **New Task Reception**: Update "Background and Motivation", then switch to Planner mode
2. **Planning Phase**: Document "Key Challenges" and "High-Level Task Breakdown"
3. **Execution Phase**: Complete one task from status dashboard at a time
4. **Progress Reporting**: Update dashboard and feedback sections after each milestone
5. **Validation**: Use Test-Driven Development (TDD) approach
6. **Completion**: Only Planner can declare project completion

## Critical Rules

- **Never rewrite entire scratchpad document**
- **Don't delete other role's records** - add new sections or mark as outdated
- **Ask user for external information needs** with clear purpose
- **Notify Planner before major changes** via feedback section
- **Record reusable information** in "Lessons Learned"
- **Don't guess uncertain technical details** - users can't validate wrong paths

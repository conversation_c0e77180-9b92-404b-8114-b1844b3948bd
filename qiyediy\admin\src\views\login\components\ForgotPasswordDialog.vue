<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 忘记密码对话框
-->

<template>
  <el-dialog
    v-model="visible"
    title="忘记密码"
    width="400px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-steps :active="currentStep" align-center class="steps">
      <el-step title="验证邮箱" />
      <el-step title="重置密码" />
      <el-step title="完成" />
    </el-steps>

    <!-- 步骤1：验证邮箱 -->
    <div v-if="currentStep === 0" class="step-content">
      <el-form
        ref="emailFormRef"
        :model="emailForm"
        :rules="emailRules"
        label-width="0"
        size="large"
      >
        <el-form-item prop="email">
          <el-input
            v-model="emailForm.email"
            placeholder="请输入注册邮箱"
            prefix-icon="Message"
            clearable
            :disabled="loading"
          />
        </el-form-item>
      </el-form>
      
      <div class="step-tip">
        <el-alert
          title="我们将向您的邮箱发送重置密码链接"
          type="info"
          :closable="false"
          show-icon
        />
      </div>
    </div>

    <!-- 步骤2：重置密码 -->
    <div v-if="currentStep === 1" class="step-content">
      <el-form
        ref="resetFormRef"
        :model="resetForm"
        :rules="resetRules"
        label-width="0"
        size="large"
      >
        <el-form-item prop="token">
          <el-input
            v-model="resetForm.token"
            placeholder="请输入邮箱中的重置码"
            prefix-icon="Key"
            clearable
            :disabled="loading"
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="resetForm.password"
            type="password"
            placeholder="请输入新密码"
            prefix-icon="Lock"
            show-password
            clearable
            :disabled="loading"
          />
        </el-form-item>

        <el-form-item prop="confirmPassword">
          <el-input
            v-model="resetForm.confirmPassword"
            type="password"
            placeholder="请确认新密码"
            prefix-icon="Lock"
            show-password
            clearable
            :disabled="loading"
          />
        </el-form-item>
      </el-form>
    </div>

    <!-- 步骤3：完成 -->
    <div v-if="currentStep === 2" class="step-content">
      <div class="success-content">
        <el-result
          icon="success"
          title="密码重置成功"
          sub-title="您的密码已成功重置，请使用新密码登录"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button v-if="currentStep > 0 && currentStep < 2" @click="prevStep" :disabled="loading">
          上一步
        </el-button>
        <el-button @click="handleClose" :disabled="loading">
          {{ currentStep === 2 ? '完成' : '取消' }}
        </el-button>
        <el-button
          v-if="currentStep < 2"
          type="primary"
          @click="nextStep"
          :loading="loading"
        >
          {{ getButtonText() }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from 'vue'
import { authApi } from '@/api/auth'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单引用
const emailFormRef = ref<FormInstance>()
const resetFormRef = ref<FormInstance>()

// 状态
const loading = ref(false)
const currentStep = ref(0)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 邮箱表单
const emailForm = reactive({
  email: ''
})

// 重置表单
const resetForm = reactive({
  token: '',
  password: '',
  confirmPassword: ''
})

// 验证规则
const emailRules: FormRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

const resetRules: FormRules = {
  token: [
    { required: true, message: '请输入重置码', trigger: 'blur' },
    { len: 32, message: '重置码长度不正确', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 32, message: '密码长度在 6 到 32 个字符', trigger: 'blur' },
    { 
      pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, 
      message: '密码必须包含字母和数字', 
      trigger: 'blur' 
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== resetForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

/**
 * 获取按钮文本
 */
const getButtonText = () => {
  if (loading.value) {
    return currentStep.value === 0 ? '发送中...' : '重置中...'
  }
  return currentStep.value === 0 ? '发送重置邮件' : '重置密码'
}

/**
 * 下一步
 */
const nextStep = async () => {
  if (currentStep.value === 0) {
    await sendResetEmail()
  } else if (currentStep.value === 1) {
    await resetPassword()
  }
}

/**
 * 上一步
 */
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

/**
 * 发送重置邮件
 */
const sendResetEmail = async () => {
  if (!emailFormRef.value) return

  try {
    await emailFormRef.value.validate()
    loading.value = true

    await authApi.forgotPassword({ email: emailForm.email })
    
    ElMessage.success('重置邮件已发送，请查收')
    currentStep.value = 1

  } catch (error: any) {
    ElMessage.error(error.message || '发送失败')
  } finally {
    loading.value = false
  }
}

/**
 * 重置密码
 */
const resetPassword = async () => {
  if (!resetFormRef.value) return

  try {
    await resetFormRef.value.validate()
    loading.value = true

    await authApi.resetPassword({
      token: resetForm.token,
      password: resetForm.password,
      confirm_password: resetForm.confirmPassword
    })
    
    ElMessage.success('密码重置成功')
    currentStep.value = 2

  } catch (error: any) {
    ElMessage.error(error.message || '重置失败')
  } finally {
    loading.value = false
  }
}

/**
 * 处理关闭
 */
const handleClose = () => {
  if (loading.value) return
  visible.value = false
  resetAll()
}

/**
 * 重置所有状态
 */
const resetAll = () => {
  currentStep.value = 0
  emailFormRef.value?.resetFields()
  resetFormRef.value?.resetFields()
  
  Object.assign(emailForm, { email: '' })
  Object.assign(resetForm, {
    token: '',
    password: '',
    confirmPassword: ''
  })
}
</script>

<style lang="scss" scoped>
.steps {
  margin-bottom: 30px;
}

.step-content {
  min-height: 200px;
  padding: 20px 0;
}

.step-tip {
  margin-top: 20px;
}

.success-content {
  text-align: center;
  
  :deep(.el-result) {
    padding: 20px 0;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog) {
  border-radius: 12px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item) {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

:deep(.el-input) {
  .el-input__wrapper {
    border-radius: 6px;
  }
}

:deep(.el-steps) {
  .el-step__title {
    font-size: 14px;
  }
}
</style>

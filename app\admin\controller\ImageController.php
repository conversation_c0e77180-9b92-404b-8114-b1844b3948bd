<?php

namespace app\admin\controller;

use app\model\Image;
use app\model\ImageGroup;
use app\service\ImageUploadService;
use think\Request;
use think\Response;

/**
 * 图片管理控制器
 */
class ImageController extends Base
{
    /**
     * 上传图片
     */
    public function upload(Request $request)
    {
        try {
            // 安全检查：验证请求方法
            if (!$request->isPost()) {
                return json([
                    'success' => false,
                    'message' => '仅支持POST请求',
                    'error' => ['message' => '仅支持POST请求']
                ]);
            }
            
            // 安全检查：验证Content-Type
            $contentType = $request->header('content-type', '');
            if (strpos($contentType, 'multipart/form-data') === false) {
                return json([
                    'success' => false,
                    'message' => '无效的请求类型',
                    'error' => ['message' => '无效的请求类型']
                ]);
            }
            
            // 安全检查：验证Referer（防止CSRF）
            $referer = $request->header('referer', '');
            $host = $request->host();
            if (!empty($referer) && strpos($referer, $host) === false) {
                return json([
                    'success' => false,
                    'message' => '非法的请求来源',
                    'error' => ['message' => '非法的请求来源']
                ]);
            }
            
            // 获取上传的文件
            $file = $request->file('upload');
            if (!$file) {
                return json([
                    'success' => false,
                    'message' => '没有上传文件',
                    'error' => ['message' => '没有上传文件']
                ]);
            }
            
            // 安全检查：验证文件是否有效
            if (!$file->isValid()) {
                return json([
                    'success' => false,
                    'message' => '文件上传失败',
                    'error' => ['message' => '文件上传失败']
                ]);
            }
            
            // 安全检查：验证文件大小
            if ($file->getSize() <= 0) {
                return json([
                    'success' => false,
                    'message' => '文件大小无效',
                    'error' => ['message' => '文件大小无效']
                ]);
            }
            
            // 获取上传选项并进行安全过滤
            $groupId = (int) $request->param('group_id', 0);
            $context = $request->param('context', ''); // 上传上下文：news, products, banners, cases, solutions
            $altText = trim(strip_tags($request->param('alt_text', '')));
            $tags = $request->param('tags', []);

            // 智能选择默认分组
            if ($groupId <= 0) {
                $groupId = $this->getDefaultGroupByContext($context);
            }

            // 验证分组ID
            if ($groupId <= 0) {
                $groupId = 1; // 最终默认为默认分组
            }
            
            // 验证并清理alt_text
            if (strlen($altText) > 255) {
                $altText = substr($altText, 0, 255);
            }
            
            // 处理标签
            if (is_string($tags)) {
                $tags = array_filter(array_map('trim', explode(',', $tags)));
            }
            if (!is_array($tags)) {
                $tags = [];
            }
            
            // 限制标签数量和长度
            $tags = array_slice($tags, 0, 10); // 最多10个标签
            $tags = array_map(function($tag) {
                return substr(strip_tags(trim($tag)), 0, 50); // 每个标签最多50字符
            }, $tags);
            $tags = array_filter($tags); // 移除空标签
            
            // 上传图片
            $result = ImageUploadService::uploadSingle($file, [
                'group_id' => $groupId,
                'alt_text' => $altText,
                'tags' => $tags,
                'validate' => true,
                'save_to_db' => true,
            ]);
            
            if ($result['success']) {
                // 记录上传日志
                $this->logUpload($result['data'], $request);
                
                // 返回CKEditor需要的格式
                return json([
                    'url' => $result['data']['file_url'],
                    'uploaded' => 1,
                    'fileName' => $result['data']['filename'],
                    'data' => $result['data']
                ]);
            } else {
                return json([
                    'success' => false,
                    'message' => $result['message'],
                    'error' => ['message' => $result['message']]
                ]);
            }
            
        } catch (\Exception $e) {
            // 记录错误日志
            $this->logError($e, $request);
            
            return json([
                'success' => false,
                'message' => '上传失败：系统错误',
                'error' => ['message' => '上传失败：系统错误']
            ]);
        }
    }
    
    /**
     * 批量上传图片
     */
    public function uploadMultiple(Request $request)
    {
        try {
            $files = $request->file('files');
            if (!$files || !is_array($files)) {
                return json([
                    'success' => false,
                    'message' => '没有上传文件'
                ]);
            }
            
            $groupId = (int) $request->param('group_id', 0);
            $context = $request->param('context', '');
            $altText = $request->param('alt_text', '');
            $tags = $request->param('tags', []);

            // 智能选择默认分组
            if ($groupId <= 0) {
                $groupId = $this->getDefaultGroupByContext($context);
            }

            // 验证分组ID
            if ($groupId <= 0) {
                $groupId = 1; // 最终默认为默认分组
            }
            
            if (is_string($tags)) {
                $tags = array_filter(explode(',', $tags));
            }
            
            $result = ImageUploadService::uploadMultiple($files, [
                'group_id' => $groupId,
                'alt_text' => $altText,
                'tags' => $tags,
                'validate' => true,
                'save_to_db' => true,
            ]);
            
            return json($result);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '批量上传失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取图片列表
     */
    public function index(Request $request)
    {
        try {
            $page = $request->param('page', 1);
            $limit = $request->param('limit', 20);
            $groupId = $request->param('group_id', '');
            $keyword = $request->param('keyword', '');
            $extension = $request->param('extension', '');
            
            $query = Image::with(['group']);
            
            // 分组筛选
            if ($groupId !== '') {
                $query->where('group_id', $groupId);
            }
            
            // 关键词搜索
            if ($keyword) {
                $query->where(function($q) use ($keyword) {
                    $q->whereLike('filename', "%{$keyword}%")
                      ->whereOr('alt_text', 'like', "%{$keyword}%");
                });
            }
            
            // 文件类型筛选
            if ($extension) {
                $query->where('extension', $extension);
            }
            
            // 只显示正常状态的图片
            $query->where('status', 1);
            
            // 排序
            $query->order('created_at', 'desc');
            
            // 分页
            $images = $query->paginate([
                'list_rows' => $limit,
                'page' => $page,
            ]);
            
            return json([
                'success' => true,
                'message' => '获取成功',
                'data' => [
                    'list' => $images->items(),
                    'total' => $images->total(),
                    'page' => $page,
                    'limit' => $limit,
                    'pages' => $images->lastPage(),
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取图片详情
     */
    public function detail(Request $request)
    {
        try {
            $id = $request->param('id');
            $image = Image::with(['group'])->find($id);
            
            if (!$image) {
                return json([
                    'success' => false,
                    'message' => '图片不存在'
                ]);
            }
            
            return json([
                'success' => true,
                'message' => '获取成功',
                'data' => $image
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 更新图片信息
     */
    public function update(Request $request)
    {
        try {
            $id = $request->param('id');
            $image = Image::find($id);
            
            if (!$image) {
                return json([
                    'success' => false,
                    'message' => '图片不存在'
                ]);
            }
            
            $data = $request->only(['alt_text', 'tags', 'group_id']);
            
            // 处理标签
            if (isset($data['tags']) && is_string($data['tags'])) {
                $data['tags'] = array_filter(explode(',', $data['tags']));
            }
            
            $image->save($data);
            
            return json([
                'success' => true,
                'message' => '更新成功',
                'data' => $image
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '更新失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 删除图片
     */
    public function delete(Request $request)
    {
        try {
            $id = $request->param('id');
            $result = ImageUploadService::deleteImage($id);
            
            return json($result);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '删除失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 批量删除图片
     */
    public function deleteMultiple(Request $request)
    {
        try {
            $ids = $request->param('ids', []);
            if (empty($ids)) {
                return json([
                    'success' => false,
                    'message' => '请选择要删除的图片'
                ]);
            }
            
            if (is_string($ids)) {
                $ids = explode(',', $ids);
            }
            
            $result = ImageUploadService::deleteMultiple($ids);
            
            return json($result);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '批量删除失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 移动图片到分组
     */
    public function moveToGroup(Request $request)
    {
        try {
            $imageId = $request->param('image_id');
            $groupId = $request->param('group_id');
            
            $result = ImageUploadService::moveToGroup($imageId, $groupId);
            
            return json($result);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '移动失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取分组列表
     */
    public function groups(Request $request)
    {
        try {
            $groups = ImageGroup::getEnabledGroups();
            
            // 为每个分组计算实际的图片数量
            $groupsWithCount = [];
            foreach ($groups as $group) {
                $imageCount = Image::where('group_id', $group->id)
                    ->where('status', 1)
                    ->count();
                
                $groupsWithCount[] = [
                    'id' => $group->id,
                    'name' => $group->name,
                    'slug' => $group->slug,
                    'description' => $group->description,
                    'parent_id' => $group->parent_id,
                    'sort_order' => $group->sort_order,
                    'image_count' => $imageCount,
                    'status' => $group->status,
                    'created_at' => $group->created_at,
                    'updated_at' => $group->updated_at,
                ];
            }
            
            return json([
                'success' => true,
                'message' => '获取成功',
                'data' => $groupsWithCount
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取统计信息
     */
    public function statistics(Request $request)
    {
        try {
            $stats = ImageUploadService::getStatistics();
            
            return json([
                'success' => true,
                'message' => '获取成功',
                'data' => $stats
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 清理无效记录
     */
    public function clean(Request $request)
    {
        try {
            $result = ImageUploadService::cleanInvalidRecords();
            
            return json($result);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '清理失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 图片选择器 - 获取图片列表（用于弹窗选择）
     */
    public function selector(Request $request)
    {
        try {
            $page = $request->param('page', 1);
            $limit = $request->param('limit', 12);
            $groupId = $request->param('group_id', '');
            $keyword = $request->param('keyword', '');
            
            $query = Image::with(['group']);
            
            // 分组筛选
            if ($groupId !== '') {
                $query->where('group_id', $groupId);
            }
            
            // 关键词搜索
            if ($keyword) {
                $query->where(function($q) use ($keyword) {
                    $q->whereLike('filename', "%{$keyword}%")
                      ->whereOr('alt_text', 'like', "%{$keyword}%");
                });
            }
            
            // 只显示正常状态的图片
            $query->where('status', 1);
            
            // 排序
            $query->order('created_at', 'desc');
            
            // 分页
            $images = $query->paginate([
                'list_rows' => $limit,
                'page' => $page,
            ]);
            
            // 格式化数据
            $list = [];
            foreach ($images->items() as $image) {
                $list[] = [
                    'id' => $image->id,
                    'filename' => $image->filename,
                    'file_url' => $image->file_url,
                    'full_url' => $image->full_url,
                    'file_size_text' => $image->file_size_text,
                    'dimensions_text' => $image->dimensions_text,
                    'alt_text' => $image->alt_text,
                    'group_name' => $image->group ? $image->group->name : '默认分组',
                    'created_at' => is_object($image->created_at) ? $image->created_at->format('Y-m-d H:i:s') : $image->created_at,
                ];
            }
            
            return json([
                'success' => true,
                'message' => '获取成功',
                'data' => [
                    'list' => $list,
                    'total' => $images->total(),
                    'page' => $page,
                    'limit' => $limit,
                    'pages' => $images->lastPage(),
                    'has_more' => $images->hasPages() && $page < $images->lastPage(),
                ]
            ]);
            
        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '获取失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 记录上传日志
     */
    private function logUpload($imageData, $request)
    {
        try {
            $logData = [
                'action' => 'image_upload',
                'image_id' => $imageData['id'] ?? null,
                'filename' => $imageData['filename'] ?? '',
                'file_size' => $imageData['file_size'] ?? 0,
                'ip_address' => $request->ip(),
                'user_agent' => $request->header('User-Agent', ''),
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
            // 写入日志文件
            $logFile = runtime_path() . 'log' . DIRECTORY_SEPARATOR . date('Ym') . DIRECTORY_SEPARATOR . 'upload_' . date('d') . '.log';
            $logDir = dirname($logFile);
            
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }
            
            $logContent = json_encode($logData, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            file_put_contents($logFile, $logContent, FILE_APPEND | LOCK_EX);
            
        } catch (\Exception $e) {
            // 日志记录失败不影响主流程
        }
    }

    /**
     * 根据上下文智能选择默认分组
     */
    private function getDefaultGroupByContext($context)
    {
        $contextGroupMap = [
            'news' => 2,        // 新闻图片
            'products' => 3,    // 产品图片
            'banners' => 4,     // 轮播图片
            'cases' => 5,       // 其他图片（案例）
            'solutions' => 5,   // 其他图片（解决方案）
            'editor' => 2,      // 编辑器默认使用新闻图片分组
        ];

        return $contextGroupMap[$context] ?? 1; // 默认分组
    }

    /**
     * 记录错误日志
     */
    private function logError($exception, $request)
    {
        try {
            $logData = [
                'action' => 'image_upload_error',
                'error_message' => $exception->getMessage(),
                'error_file' => $exception->getFile(),
                'error_line' => $exception->getLine(),
                'ip_address' => $request->ip(),
                'user_agent' => $request->header('User-Agent', ''),
                'request_data' => [
                    'group_id' => $request->param('group_id', ''),
                    'alt_text' => $request->param('alt_text', ''),
                    'file_info' => $request->file('upload') ? [
                        'name' => $request->file('upload')->getOriginalName(),
                        'size' => $request->file('upload')->getSize(),
                        'type' => $request->file('upload')->getMime()
                    ] : null
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
            // 写入错误日志文件
            $logFile = runtime_path() . 'log' . DIRECTORY_SEPARATOR . date('Ym') . DIRECTORY_SEPARATOR . 'upload_error_' . date('d') . '.log';
            $logDir = dirname($logFile);
            
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }
            
            $logContent = json_encode($logData, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            file_put_contents($logFile, $logContent, FILE_APPEND | LOCK_EX);
            
        } catch (\Exception $e) {
            // 日志记录失败不影响主流程
        }
    }
} 
<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * QiyeDIY企业建站系统 - DIY组件模型
 */

declare(strict_types=1);

namespace app\model;

use think\Model;
use think\model\concern\SoftDelete;

/**
 * DIY组件模型
 */
class DiyComponent extends Model
{
    use SoftDelete;

    // 表名
    protected $name = 'diy_components';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 软删除字段
    protected $deleteTime = 'deleted_at';

    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'page_id' => 'integer',
        'config' => 'json',
        'content' => 'json',
        'sort_order' => 'integer',
        'is_enabled' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    // 允许批量赋值的字段
    protected $fillable = [
        'page_id', 'type', 'name', 'config', 'content', 'sort_order', 'is_enabled'
    ];

    // JSON字段
    protected $json = ['config', 'content'];

    // 字段默认值
    protected $field = [
        'id', 'page_id', 'type', 'name', 'config', 'content', 
        'sort_order', 'is_enabled', 'created_at', 'updated_at', 'deleted_at'
    ];

    /**
     * 关联DIY页面
     * @return \think\model\relation\BelongsTo
     */
    public function page()
    {
        return $this->belongsTo(DiyPage::class, 'page_id', 'id');
    }

    /**
     * 搜索器：按类型搜索
     * @param \think\db\Query $query
     * @param string $value
     */
    public function searchTypeAttr($query, $value)
    {
        if (!empty($value)) {
            $query->where('type', $value);
        }
    }

    /**
     * 搜索器：按名称搜索
     * @param \think\db\Query $query
     * @param string $value
     */
    public function searchNameAttr($query, $value)
    {
        if (!empty($value)) {
            $query->where('name', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器：按页面ID搜索
     * @param \think\db\Query $query
     * @param int $value
     */
    public function searchPageIdAttr($query, $value)
    {
        if (!empty($value)) {
            $query->where('page_id', $value);
        }
    }

    /**
     * 搜索器：按启用状态搜索
     * @param \think\db\Query $query
     * @param int $value
     */
    public function searchEnabledAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('is_enabled', $value);
        }
    }

    /**
     * 修改器：配置数据
     * @param mixed $value
     * @return string
     */
    public function setConfigAttr($value)
    {
        if (is_array($value)) {
            return json_encode($value, JSON_UNESCAPED_UNICODE);
        }
        return $value;
    }

    /**
     * 修改器：内容数据
     * @param mixed $value
     * @return string
     */
    public function setContentAttr($value)
    {
        if (is_array($value)) {
            return json_encode($value, JSON_UNESCAPED_UNICODE);
        }
        return $value;
    }

    /**
     * 获取器：配置数据
     * @param string $value
     * @return array
     */
    public function getConfigAttr($value)
    {
        if (empty($value)) {
            return [];
        }
        
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return $decoded ?: [];
        }
        
        return is_array($value) ? $value : [];
    }

    /**
     * 获取器：内容数据
     * @param string $value
     * @return array
     */
    public function getContentAttr($value)
    {
        if (empty($value)) {
            return [];
        }
        
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return $decoded ?: [];
        }
        
        return is_array($value) ? $value : [];
    }

    /**
     * 获取器：启用状态文本
     * @param int $value
     * @param array $data
     * @return string
     */
    public function getEnabledTextAttr($value, $data)
    {
        return $data['is_enabled'] ? '启用' : '禁用';
    }

    /**
     * 创建组件
     * @param array $data 组件数据
     * @return static
     * @throws \Exception
     */
    public static function createComponent(array $data): self
    {
        // 验证必要字段
        if (empty($data['page_id']) || empty($data['type'])) {
            throw new \Exception('页面ID和组件类型不能为空');
        }

        // 设置默认值
        $data = array_merge([
            'name' => '',
            'config' => [],
            'content' => [],
            'sort_order' => 0,
            'is_enabled' => 1
        ], $data);

        // 如果没有指定排序，自动设置为最大值+1
        if (!isset($data['sort_order']) || $data['sort_order'] === 0) {
            $maxOrder = self::where('page_id', $data['page_id'])->max('sort_order');
            $data['sort_order'] = $maxOrder + 1;
        }

        return self::create($data);
    }

    /**
     * 更新组件
     * @param array $data 更新数据
     * @return bool
     * @throws \Exception
     */
    public function updateComponent(array $data): bool
    {
        // 过滤不允许更新的字段
        $allowedFields = ['type', 'name', 'config', 'content', 'sort_order', 'is_enabled'];
        $updateData = array_intersect_key($data, array_flip($allowedFields));

        if (empty($updateData)) {
            throw new \Exception('没有可更新的数据');
        }

        return $this->save($updateData);
    }

    /**
     * 启用组件
     * @return bool
     */
    public function enable(): bool
    {
        $this->is_enabled = 1;
        return $this->save();
    }

    /**
     * 禁用组件
     * @return bool
     */
    public function disable(): bool
    {
        $this->is_enabled = 0;
        return $this->save();
    }

    /**
     * 移动组件位置
     * @param int $newOrder 新的排序值
     * @return bool
     */
    public function moveTo(int $newOrder): bool
    {
        $oldOrder = $this->sort_order;
        
        if ($oldOrder == $newOrder) {
            return true;
        }

        // 开始事务
        $this->startTrans();
        try {
            if ($newOrder > $oldOrder) {
                // 向下移动：将中间的组件向上移动
                self::where('page_id', $this->page_id)
                    ->where('sort_order', '>', $oldOrder)
                    ->where('sort_order', '<=', $newOrder)
                    ->dec('sort_order');
            } else {
                // 向上移动：将中间的组件向下移动
                self::where('page_id', $this->page_id)
                    ->where('sort_order', '>=', $newOrder)
                    ->where('sort_order', '<', $oldOrder)
                    ->inc('sort_order');
            }

            // 更新当前组件的排序
            $this->sort_order = $newOrder;
            $this->save();

            $this->commit();
            return true;

        } catch (\Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * 复制组件
     * @param int $targetPageId 目标页面ID
     * @return static
     */
    public function copy(int $targetPageId): self
    {
        $data = $this->toArray();
        
        // 移除ID和时间戳
        unset($data['id'], $data['created_at'], $data['updated_at'], $data['deleted_at']);
        
        // 设置新的页面ID
        $data['page_id'] = $targetPageId;
        
        // 设置新的排序值
        $maxOrder = self::where('page_id', $targetPageId)->max('sort_order');
        $data['sort_order'] = $maxOrder + 1;

        return self::create($data);
    }

    /**
     * 获取组件类型列表
     * @return array
     */
    public static function getTypeList(): array
    {
        return [
            'text' => '文本',
            'image' => '图片',
            'video' => '视频',
            'button' => '按钮',
            'form' => '表单',
            'list' => '列表',
            'grid' => '网格',
            'carousel' => '轮播图',
            'tabs' => '选项卡',
            'accordion' => '手风琴',
            'map' => '地图',
            'chart' => '图表',
            'custom' => '自定义'
        ];
    }

    /**
     * 获取组件类型名称
     * @param string $type 组件类型
     * @return string
     */
    public static function getTypeName(string $type): string
    {
        $typeList = self::getTypeList();
        return $typeList[$type] ?? $type;
    }

    /**
     * 验证组件配置
     * @param string $type 组件类型
     * @param array $config 配置数据
     * @return bool
     */
    public static function validateConfig(string $type, array $config): bool
    {
        // 根据组件类型验证配置
        switch ($type) {
            case 'text':
                return isset($config['content']) && is_string($config['content']);
            
            case 'image':
                return isset($config['src']) && is_string($config['src']);
            
            case 'video':
                return isset($config['src']) && is_string($config['src']);
            
            case 'button':
                return isset($config['text']) && isset($config['action']);
            
            case 'form':
                return isset($config['fields']) && is_array($config['fields']);
            
            default:
                return true;
        }
    }
}

<!--
  三只鱼网络科技 | 韩总 | 2024-12-20
  QiyeDIY企业建站系统 - 首页
-->

<template>
  <div class="home-page">
    <!-- 页面头部 -->
    <AppHeader />

    <!-- 主要内容 -->
    <main class="main-content">
      <!-- Hero区域 -->
      <section class="hero-section">
        <div class="container mx-auto px-4 py-20">
          <div class="text-center">
            <h1 class="hero-title">
              专业的企业DIY建站系统
            </h1>
            <p class="hero-subtitle">
              让建站变得简单，让创意无限可能
            </p>
            <div class="hero-actions">
              <NuxtLink to="/templates" class="btn btn-primary">
                开始建站
              </NuxtLink>
              <NuxtLink to="/demo" class="btn btn-outline">
                查看演示
              </NuxtLink>
            </div>
          </div>
        </div>
      </section>

      <!-- 特性介绍 -->
      <section class="features-section">
        <div class="container mx-auto px-4 py-16">
          <div class="text-center mb-12">
            <h2 class="section-title">核心特性</h2>
            <p class="section-subtitle">
              强大的功能，简单的操作
            </p>
          </div>
          
          <div class="features-grid">
            <div 
              v-for="feature in features" 
              :key="feature.id"
              class="feature-card"
            >
              <div class="feature-icon">
                <Icon :name="feature.icon" />
              </div>
              <h3 class="feature-title">{{ feature.title }}</h3>
              <p class="feature-description">{{ feature.description }}</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 模板展示 -->
      <section class="templates-section">
        <div class="container mx-auto px-4 py-16">
          <div class="text-center mb-12">
            <h2 class="section-title">精美模板</h2>
            <p class="section-subtitle">
              多种行业模板，一键应用
            </p>
          </div>
          
          <div class="templates-grid">
            <div 
              v-for="template in templates" 
              :key="template.id"
              class="template-card"
              @click="previewTemplate(template)"
            >
              <div class="template-image">
                <NuxtImg 
                  :src="template.preview_image" 
                  :alt="template.name"
                  loading="lazy"
                />
              </div>
              <div class="template-info">
                <h3 class="template-name">{{ template.name }}</h3>
                <p class="template-category">{{ template.category }}</p>
                <div class="template-actions">
                  <button class="btn btn-sm btn-primary">
                    预览
                  </button>
                  <button class="btn btn-sm btn-outline">
                    使用
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <div class="text-center mt-12">
            <NuxtLink to="/templates" class="btn btn-outline">
              查看更多模板
            </NuxtLink>
          </div>
        </div>
      </section>

      <!-- 成功案例 -->
      <section class="cases-section">
        <div class="container mx-auto px-4 py-16">
          <div class="text-center mb-12">
            <h2 class="section-title">成功案例</h2>
            <p class="section-subtitle">
              已有数千家企业选择我们
            </p>
          </div>
          
          <div class="cases-grid">
            <div 
              v-for="case_ in cases" 
              :key="case_.id"
              class="case-card"
            >
              <div class="case-image">
                <NuxtImg 
                  :src="case_.image" 
                  :alt="case_.name"
                  loading="lazy"
                />
              </div>
              <div class="case-info">
                <h3 class="case-name">{{ case_.name }}</h3>
                <p class="case-industry">{{ case_.industry }}</p>
                <p class="case-description">{{ case_.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- CTA区域 -->
      <section class="cta-section">
        <div class="container mx-auto px-4 py-20">
          <div class="text-center">
            <h2 class="cta-title">
              准备开始您的建站之旅？
            </h2>
            <p class="cta-subtitle">
              立即注册，免费体验专业的DIY建站服务
            </p>
            <div class="cta-actions">
              <NuxtLink to="/register" class="btn btn-primary btn-lg">
                免费注册
              </NuxtLink>
              <NuxtLink to="/contact" class="btn btn-outline btn-lg">
                联系我们
              </NuxtLink>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 页面底部 -->
    <AppFooter />

    <!-- 模板预览弹窗 -->
    <TemplatePreviewModal 
      v-model="showPreview"
      :template="selectedTemplate"
    />
  </div>
</template>

<script setup lang="ts">
import type { DiyTemplate } from '~/types/diy'

// SEO配置
useSeoMeta({
  title: '首页',
  description: '专业的企业DIY建站系统，让建站变得简单，让创意无限可能',
  keywords: 'DIY建站,企业网站,网站建设,可视化编辑,模板建站',
  ogTitle: 'QiyeDIY企业建站系统',
  ogDescription: '专业的企业DIY建站系统，让建站变得简单',
  ogImage: '/images/og-image.jpg',
  twitterCard: 'summary_large_image'
})

// 响应式数据
const showPreview = ref(false)
const selectedTemplate = ref<DiyTemplate | null>(null)

// 特性数据
const features = ref([
  {
    id: 1,
    icon: 'heroicons:cursor-arrow-rays',
    title: '拖拽式编辑',
    description: '直观的可视化编辑器，拖拽即可完成页面设计'
  },
  {
    id: 2,
    icon: 'heroicons:device-phone-mobile',
    title: '响应式设计',
    description: '自动适配各种设备，确保完美的移动端体验'
  },
  {
    id: 3,
    icon: 'heroicons:puzzle-piece',
    title: '丰富组件',
    description: '提供多种页面组件，满足不同的设计需求'
  },
  {
    id: 4,
    icon: 'heroicons:bolt',
    title: '快速部署',
    description: '一键发布，快速上线，无需复杂的技术配置'
  },
  {
    id: 5,
    icon: 'heroicons:shield-check',
    title: '安全可靠',
    description: '企业级安全保障，数据加密存储，稳定可靠'
  },
  {
    id: 6,
    icon: 'heroicons:chart-bar',
    title: '数据分析',
    description: '内置访问统计，帮助您了解网站运营情况'
  }
])

// 模板数据
const templates = ref<DiyTemplate[]>([])

// 案例数据
const cases = ref([
  {
    id: 1,
    name: '科技创新公司',
    industry: '科技行业',
    image: '/images/cases/case1.jpg',
    description: '专业的科技公司官网，展示企业实力和产品优势'
  },
  {
    id: 2,
    name: '教育培训机构',
    industry: '教育行业',
    image: '/images/cases/case2.jpg',
    description: '现代化的教育网站，提供在线课程和学习资源'
  },
  {
    id: 3,
    name: '餐饮连锁品牌',
    industry: '餐饮行业',
    image: '/images/cases/case3.jpg',
    description: '美食展示网站，在线订餐和门店查询功能'
  }
])

/**
 * 预览模板
 */
const previewTemplate = (template: DiyTemplate) => {
  selectedTemplate.value = template
  showPreview.value = true
}

/**
 * 加载模板数据
 */
const loadTemplates = async () => {
  try {
    const { data } = await $fetch('/api/templates/featured')
    templates.value = data
  } catch (error) {
    console.error('加载模板失败:', error)
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadTemplates()
})
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.main-content {
  flex: 1;
}

// Hero区域样式
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  
  .hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    
    @media (max-width: 768px) {
      font-size: 2.5rem;
    }
  }
  
  .hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
  }
  
  .hero-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
  }
}

// 特性区域样式
.features-section {
  background: white;
  
  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }
  
  .feature-card {
    text-align: center;
    padding: 2rem;
    border-radius: 1rem;
    background: #f8fafc;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
  }
  
  .feature-icon {
    width: 4rem;
    height: 4rem;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
  }
  
  .feature-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
  }
  
  .feature-description {
    color: #6b7280;
    line-height: 1.6;
  }
}

// 模板区域样式
.templates-section {
  background: #f8fafc;
  
  .templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }
  
  .template-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }
  }
  
  .template-image {
    aspect-ratio: 16/10;
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }
    
    &:hover img {
      transform: scale(1.05);
    }
  }
  
  .template-info {
    padding: 1.5rem;
  }
  
  .template-name {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
  }
  
  .template-category {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 1rem;
  }
  
  .template-actions {
    display: flex;
    gap: 0.5rem;
  }
}

// 案例区域样式
.cases-section {
  background: white;
  
  .cases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
  }
  
  .case-card {
    display: flex;
    background: #f8fafc;
    border-radius: 1rem;
    overflow: hidden;
    transition: transform 0.3s ease;
    
    &:hover {
      transform: translateY(-3px);
    }
  }
  
  .case-image {
    width: 120px;
    flex-shrink: 0;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .case-info {
    padding: 1.5rem;
    flex: 1;
  }
  
  .case-name {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
  }
  
  .case-industry {
    color: #667eea;
    font-size: 0.875rem;
    margin-bottom: 0.75rem;
  }
  
  .case-description {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.5;
  }
}

// CTA区域样式
.cta-section {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  color: white;
  
  .cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    
    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }
  
  .cta-subtitle {
    font-size: 1.125rem;
    margin-bottom: 2rem;
    opacity: 0.9;
  }
  
  .cta-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
  }
}

// 公共样式
.section-title {
  font-size: 2.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1f2937;
}

.section-subtitle {
  font-size: 1.125rem;
  color: #6b7280;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  
  &.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    }
  }
  
  &.btn-outline {
    border-color: currentColor;
    color: inherit;
    
    &:hover {
      background: currentColor;
      color: white;
    }
  }
  
  &.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
  
  &.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
  }
}
</style>

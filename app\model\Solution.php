<?php
/**
 * 三只鱼网络科技 | 韩总 | 2025-01-15
 * 文件描述：解决方案模型 - ThinkPHP6企业级应用
 * 技术栈：PHP 8.0+ + ThinkPHP6 + MySQL + Redis
 * 版权所有：三只鱼网络科技有限公司
 */

namespace app\model;

use think\Model;

/**
 * 解决方案模型
 */
class Solution extends Model
{
    // 数据表名
    protected $name = 'solutions';

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    // 字段类型转换
    protected $type = [
        'status' => 'integer',
        'views' => 'integer',
        'sort_order' => 'integer',
        'features' => 'json',
    ];

    // 可填充字段
    protected $fillable = [
        'name', 'slug', 'short_description', 'description',
        'features', 'image', 'icon', 'sort_order', 'status', 'views'
    ];

    // 获取格式化的创建时间
    public function getCreatedAtTextAttr($value, $data)
    {
        return date('Y-m-d H:i', strtotime($data['created_at']));
    }

    // 获取解决方案特性数组
    public function getFeaturesArrayAttr($value, $data)
    {
        if (empty($data['features'])) {
            return [];
        }
        $result = is_string($data['features']) ? json_decode($data['features'], true) : $data['features'];
        return is_array($result) ? $result : [];
    }

    // 生成URL别名
    public static function generateSlug($title)
    {
        if (empty($title)) {
            return '';
        }
        
        $result = '';
        
        // 遍历每个字符
        for ($i = 0; $i < strlen($title); $i++) {
            $char = $title[$i];
            
            // 如果是英文字母或数字，直接保留
            if (preg_match('/[a-zA-Z0-9]/', $char)) {
                $result .= strtolower($char);
            }
            // 如果是空格或其他分隔符，转换为连字符
            elseif (preg_match('/[\s\-_]/', $char)) {
                $result .= '-';
            }
        }
        
        // 清理结果
        $result = preg_replace('/[-]+/', '-', $result);  // 多个连字符合并为一个
        $result = trim($result, '-'); // 去除首尾连字符
        $result = substr($result, 0, 50); // 限制长度
        
        // 如果结果为空或太短，使用时间戳
        if (empty($result) || strlen($result) < 2) {
            $result = 'solution-' . date('YmdHi');
        }
        
        return $result;
    }

    // 检查slug唯一性
    public static function checkSlugUnique($slug, $excludeId = null)
    {
        $query = self::where('slug', $slug);
        if ($excludeId) {
            $query->where('id', '<>', $excludeId);
        }
        return !$query->find();
    }

    // 获取启用的解决方案
    public static function getActiveSolutions($limit = null)
    {
        $query = self::where('status', 1)
            ->order('sort_order', 'desc')
            ->order('id', 'desc');
            
        if ($limit) {
            $query->limit($limit);
        }
        
        return $query->select();
    }

    // 获取推荐解决方案
    public static function getFeaturedSolutions($limit = 6)
    {
        return self::where('status', 1)
            ->order('sort_order', 'desc')
            ->order('id', 'desc')
            ->limit($limit)
            ->select();
    }

    // 增加浏览次数
    public function incrementViews()
    {
        $this->views++;
        $this->save();
    }

    // 获取相关解决方案
    public function getRelatedSolutions($limit = 4)
    {
        return self::where('status', 1)
            ->where('id', '<>', $this->id)
            ->order('sort_order', 'desc')
            ->order('id', 'desc')
            ->limit($limit)
            ->select();
    }
}

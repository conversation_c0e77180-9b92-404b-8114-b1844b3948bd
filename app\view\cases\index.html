    {assign name="pageTitle" value="$pageData.pageTitlee" /}
    {assign name="bodyClass" value="$pageData.bodyClass" /}
    {include file="common/header"}
    <!-- 案例页面专用样式 -->
    <link rel="stylesheet" href="/assets/css/cases.css">
    
    <!-- AOS动画库 -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <style>
        /* 加载动画样式 */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            border-radius: 12px;
        }

        .loading-spinner {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            color: #6c757d;
        }

        .loading-spinner i {
            font-size: 2rem;
            color: #007bff;
        }

        .loading-spinner span {
            font-size: 0.9rem;
            font-weight: 500;
        }
    </style>    
    <!-- 页面头部横幅 -->
    <section class="page-hero">
        <!-- 3D背景动画层 -->
        <div class="hero-3d-background">
            <div class="floating-elements">

                <!-- 3D几何图形 -->
                <div class="geometric-shape shape-1"></div>
                <div class="geometric-shape shape-2"></div>
                <div class="geometric-shape shape-3"></div>
                <div class="geometric-shape shape-4"></div>

                <!-- 粒子效果 -->
                <div class="particle-system">
                    <div class="particle particle-1"></div>
                    <div class="particle particle-2"></div>
                    <div class="particle particle-3"></div>
                    <div class="particle particle-4"></div>
                    <div class="particle particle-5"></div>
                    <div class="particle particle-6"></div>
                </div>
            </div>


        </div>

        <div class="container">
            <div class="hero-content" data-aos="fade-up">
                <div class="hero-badge" data-aos="zoom-in" data-aos-delay="100">
                    <span class="badge-icon"><i class="fas fa-star"></i></span>
                    <span class="badge-text">专业案例展示</span>
                </div>

                <h1 class="hero-title" data-aos="fade-up" data-aos-delay="200">
                    <span class="title-main">客户案例</span>
                    <span class="title-sub">CASE STUDIES</span>
                </h1>

                <p class="hero-subtitle" data-aos="fade-up" data-aos-delay="300">
                    展示我们的成功案例，见证专业实力与创新成果
                    <br>
                    <span class="subtitle-highlight">用技术驱动业务增长，用创新引领行业发展</span>
                </p>

                <!-- 行动按钮 -->
                <div class="hero-actions" data-aos="fade-up" data-aos-delay="900">
                    <a href="#cases-section" class="btn-primary-3d">
                        <span class="btn-text">查看案例</span>
                        <span class="btn-icon"><i class="fas fa-arrow-down"></i></span>
                    </a>
                    <a href="/contact" class="btn-secondary-3d">
                        <span class="btn-text">联系我们</span>
                        <span class="btn-icon"><i class="fas fa-comments"></i></span>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- 主要内容区域 -->
    <main class="main-content" id="cases-section">
        <div class="container">
            <!-- 筛选区域 -->
            <section class="filter-section">
                <div class="filter-container">
                    <div class="filter-header">
                        <h3 class="filter-title">
                            <i class="fas fa-filter"></i>
                            按行业筛选
                        </h3>
                        <div class="filter-count">
                            共 <span class="count-number">{$casesList->total()}</span> 个案例
                        </div>
                    </div>
                    <div class="filter-tabs">
                        <a href="/cases" class="filter-tab {if condition='!$currentIndustry'}active{/if}">
                            <i class="fas fa-th-large"></i>
                            <span>全部</span>
                        </a>
                        {volist name="industries" id="industry"}
                        <a href="/cases?industry={$industry}" class="filter-tab {if condition='$currentIndustry == $industry'}active{/if}">
                            <i class="fas fa-industry"></i>
                            <span>{$industry}</span>
                        </a>
                        {/volist}
                    </div>
                </div>
            </section>

            <!-- 案例网格 -->
            <section class="cases-section">
                <div class="cases-grid" id="cases-grid">
                    {volist name="casesList" id="case" key="key"}
                    <article class="case-item">
                        <div class="case-card">
                            <div class="case-image">
                                <a href="{$case.detail_url}">
                                    <img src="{$case.image ?: '/assets/images/cases/default.svg'}"
                                         alt="{$case.title}" loading="lazy">
                                    <div class="image-overlay">
                                        <div class="overlay-content">
                                            <i class="fas fa-eye"></i>
                                            <span>查看详情</span>
                                        </div>
                                    </div>
                                </a>
                                {if condition="$case.is_featured"}
                                <div class="featured-badge">
                                    <i class="fas fa-star"></i>
                                    <span>精选</span>
                                </div>
                                {/if}
                            </div>
                            <div class="case-content">
                                <div class="case-meta">
                                    <div class="meta-item industry">
                                        <i class="fas fa-industry"></i>
                                        <span>{$case.industry}</span>
                                    </div>
                                    <div class="meta-item client">
                                        <i class="fas fa-user"></i>
                                        <span>{$case.client_name}</span>
                                    </div>
                                    <div class="meta-item date">
                                        <i class="fas fa-calendar"></i>
                                        <time datetime="{$case.completion_date}">
                                            {$case.completion_date ? date('Y-m-d', strtotime($case.completion_date)) : '未设置'}
                                        </time>
                                    </div>
                                </div>
                                <h3 class="case-title">
                                    <a href="{$case.detail_url}">{$case.title}</a>
                                </h3>
                                <p class="case-excerpt">
                                    {$case.summary ?: mb_substr(strip_tags($case.description), 0, 120, 'UTF-8')}...
                                </p>
                                <div class="case-actions">
                                    <a href="{$case.detail_url}" class="view-more-btn">
                                        <span>查看详情</span>
                                        <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </article>
                    {/volist}
                </div>

                <!-- 空状态 -->
                {empty name="casesList"}
                <div class="no-cases-found" data-aos="fade-up">
                    <div class="no-cases-content">
                        <div class="no-cases-icon">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <h4>暂无相关案例</h4>
                        <p>该行业下暂时没有案例内容，请关注其他行业或稍后再来查看。</p>
                        <a href="/cases" class="btn-back-all">
                            <i class="fas fa-arrow-left"></i>
                            返回全部案例
                        </a>
                    </div>
                </div>
                {/empty}

                <!-- 分页 -->
                {if condition="$casesList->hasPages()"}
                <div class="pagination-wrapper" data-aos="fade-up">
                    <div class="pagination-container">
                        {$pagination|raw}
                    </div>
                </div>
                {/if}
            </section>
        </div>
    </main>

    <!-- 引入底部 -->
    {include file="common/footer"}

    <!-- JavaScript -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // 初始化AOS动画
        AOS.init({
            duration: 800,
            easing: 'ease-out-cubic',
            once: true,
            offset: 100
        });

        // 筛选功能
        document.addEventListener('DOMContentLoaded', function() {
            // Ajax筛选功能
            const filterTabs = document.querySelectorAll('.filter-tab');
            const casesGrid = document.getElementById('cases-grid');
            const filterCount = document.querySelector('.count-number');
            const paginationWrapper = document.querySelector('.pagination-wrapper');

            filterTabs.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();

                    // 防止重复点击同一筛选项
                    if (this.classList.contains('active')) {
                        return;
                    }

                    // 获取行业参数
                    const href = this.getAttribute('href');
                    const urlParams = new URLSearchParams(href.split('?')[1] || '');
                    const industry = urlParams.get('industry') || '';

                    // 更新活动状态
                    filterTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');

                    // 显示加载状态
                    showLoadingState();

                    // 发送Ajax请求
                    loadCasesData(industry, 1);
                });
            });

            // 显示加载状态
            function showLoadingState() {
                if (casesGrid) {
                    casesGrid.style.opacity = '0.5';
                    casesGrid.style.pointerEvents = 'none';

                    // 添加加载动画
                    const loadingHtml = `
                        <div class="loading-overlay">
                            <div class="loading-spinner">
                                <i class="fas fa-spinner fa-spin"></i>
                                <span>加载中...</span>
                            </div>
                        </div>
                    `;
                    casesGrid.style.position = 'relative';
                    casesGrid.insertAdjacentHTML('beforeend', loadingHtml);
                }
            }

            // 隐藏加载状态
            function hideLoadingState() {
                if (casesGrid) {
                    casesGrid.style.opacity = '1';
                    casesGrid.style.pointerEvents = 'auto';

                    // 移除加载动画
                    const loadingOverlay = casesGrid.querySelector('.loading-overlay');
                    if (loadingOverlay) {
                        loadingOverlay.remove();
                    }
                }
            }

            // 加载案例数据
            function loadCasesData(industry, page) {
                const params = new URLSearchParams();
                if (industry) params.append('industry', industry);
                if (page) params.append('page', page);

                fetch(`/ajax-cases?${params.toString()}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 200) {
                            // 更新案例列表
                            updateCasesList(data.data.cases);

                            // 更新统计数量
                            if (filterCount) {
                                filterCount.textContent = data.data.pagination.total;
                            }

                            // 更新分页
                            updatePagination(data.data.pagination, industry);

                            // 更新URL（不刷新页面）
                            const newUrl = industry ? `/cases?industry=${industry}` : '/cases';
                            window.history.pushState({industry: industry}, '', newUrl);

                            // 重新初始化AOS动画
                            if (typeof AOS !== 'undefined') {
                                AOS.refresh();
                            }
                        } else {
                            console.error('加载失败:', data.message);
                        }
                    })
                    .catch(error => {
                        console.error('请求失败:', error);
                    })
                    .finally(() => {
                        hideLoadingState();
                    });
            }

            // 更新案例列表
            function updateCasesList(cases) {
                if (!casesGrid) return;

                if (cases.length === 0) {
                    // 显示空状态
                    casesGrid.innerHTML = `
                        <div class="no-cases-found" data-aos="fade-up">
                            <div class="no-cases-content">
                                <div class="no-cases-icon">
                                    <i class="fas fa-briefcase"></i>
                                </div>
                                <h4>暂无相关案例</h4>
                                <p>该行业下暂时没有案例内容，请关注其他行业或稍后再来查看。</p>
                                <a href="/cases" class="btn-back-all">
                                    <i class="fas fa-arrow-left"></i>
                                    返回全部案例
                                </a>
                            </div>
                        </div>
                    `;
                } else {
                    // 生成案例HTML
                    let casesHtml = '';
                    cases.forEach(caseItem => {
                        casesHtml += generateCaseHtml(caseItem);
                    });
                    casesGrid.innerHTML = casesHtml;
                }
            }

            // 生成单个案例HTML
            function generateCaseHtml(caseItem) {
                const featuredBadge = caseItem.is_featured ?
                    `<div class="featured-badge">
                        <i class="fas fa-star"></i>
                        <span>精选</span>
                    </div>` : '';

                const completionDate = caseItem.completion_date ?
                    new Date(caseItem.completion_date).toLocaleDateString('zh-CN') : '未设置';

                return `
                    <article class="case-item" data-aos="fade-up">
                        <div class="case-card">
                            <div class="case-image">
                                <a href="${caseItem.detail_url}">
                                    <img src="${caseItem.image || '/assets/images/cases/default.jpg'}"
                                         alt="${caseItem.title}"
                                         loading="lazy">
                                    <div class="image-overlay">
                                        <div class="overlay-content">
                                            <i class="fas fa-eye"></i>
                                            <span>查看详情</span>
                                        </div>
                                    </div>
                                </a>
                                ${featuredBadge}
                            </div>
                            <div class="case-content">
                                <div class="case-meta">
                                    <div class="meta-item industry">
                                        <i class="fas fa-industry"></i>
                                        <span>${caseItem.industry}</span>
                                    </div>
                                    <div class="meta-item client">
                                        <i class="fas fa-user"></i>
                                        <span>${caseItem.client_name}</span>
                                    </div>
                                    <div class="meta-item date">
                                        <i class="fas fa-calendar"></i>
                                        <time datetime="${caseItem.completion_date}">
                                            ${completionDate}
                                        </time>
                                    </div>
                                </div>
                                <h3 class="case-title">
                                    <a href="${caseItem.detail_url}">${caseItem.title}</a>
                                </h3>
                                <p class="case-excerpt">
                                    ${caseItem.summary || caseItem.description.substring(0, 120) + '...'}
                                </p>
                                <div class="case-actions">
                                    <a href="${caseItem.detail_url}" class="view-more-btn">
                                        <span>查看详情</span>
                                        <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </article>
                `;
            }

            // 更新分页
            function updatePagination(pagination, industry) {
                if (!paginationWrapper) return;

                if (pagination.last_page <= 1) {
                    paginationWrapper.style.display = 'none';
                } else {
                    paginationWrapper.style.display = 'block';
                    // 这里可以根据需要生成分页HTML
                    // 暂时隐藏，因为需要更复杂的分页逻辑
                    paginationWrapper.style.display = 'none';
                }
            }

            // 图片懒加载错误处理
            const images = document.querySelectorAll('img[loading="lazy"]');
            images.forEach(img => {
                img.addEventListener('error', function() {
                    this.src = '/assets/images/cases/default.jpg';
                });
            });
        });
    </script>

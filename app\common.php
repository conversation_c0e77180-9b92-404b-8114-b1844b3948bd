<?php
// 应用公共文件

use app\service\ConfigService;

/**
 * 安全输出HTML内容
 */
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

/**
 * 生成URL（使用配置的site_url）
 */
function url($path = '') {
    try {
        return ConfigService::url($path);
    } catch (\Exception $e) {
        // 降级处理 - 使用完整的域名包含端口
        $baseUrl = rtrim(request()->domain(true), '/');  // true参数包含端口
        $path = ltrim($path, '/');
        return $baseUrl . '/' . $path;
    }
}

/**
 * 生成资源URL（图片、CSS、JS等）
 */
function asset($path) {
    try {
        return ConfigService::asset($path);
    } catch (\Exception $e) {
        // 降级处理
        if (strpos($path, '/') !== 0) {
            $path = '/' . $path;
        }
        return url($path);
    }
}

/**
 * 获取站点配置
 */
function config_get($key, $default = null) {
    try {
        return ConfigService::get($key, $default);
    } catch (\Exception $e) {
        return $default;
    }
}

/**
 * 获取站点URL
 */
function site_url() {
    try {
        return ConfigService::getSiteUrl();
    } catch (\Exception $e) {
        return rtrim(request()->domain(true), '/');  // true参数包含端口
    }
}

/**
 * 格式化日期
 */
function formatDate($date, $format = 'Y-m-d H:i:s') {
    if (empty($date)) return '';
    return date($format, strtotime($date));
}

/**
 * 截取字符串
 */
function truncate($string, $length = 100, $suffix = '...') {
    if (mb_strlen($string, 'UTF-8') <= $length) {
        return $string;
    }
    return mb_substr($string, 0, $length, 'UTF-8') . $suffix;
}

/**
 * 生成随机字符串
 */
function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

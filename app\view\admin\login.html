<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台登录 - 三只鱼网络</title>

    <!-- CSS -->
    <link rel="stylesheet" href="/assets/css/all.min.css">
    <link rel="stylesheet" href="/assets/css/admin/login.css">
</head>
<body>
    <!-- 粒子背景 -->
    <div class="particles"></div>

    <div class="login-container">
        <div class="login-header">
            <h2><i class="fas fa-shield-alt"></i> 三只鱼网络</h2>
            <p>管理系统</p>
        </div>

        <div class="login-body">
            <form id="loginForm">
                <div class="form-group">
                    <label for="username" class="form-label">用户名</label>
                    <div class="input-wrapper">
                        <i class="fas fa-user input-icon"></i>
                        <input type="text" class="form-control" id="username" name="username"
                               value="admin"
                               placeholder="请输入用户名" required autofocus>
                    </div>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">密码</label>
                    <div class="input-wrapper">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password" class="form-control" id="password" name="password"
                               value="password"
                               placeholder="请输入密码" required>
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="remember" name="remember" checked>
                        <label class="form-check-label" for="remember">
                            记住登录状态
                        </label>
                    </div>
                </div>

                <button type="submit" class="btn btn-login">
                    <i class="fas fa-sign-in-alt"></i> 登录
                </button>
            </form>
        </div>

        <div class="login-footer">
            <p>&copy; 2024 三只鱼网络. 版权所有</p>
            <p><a href="/" target="_blank">返回网站首页</a></p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/assets/js/jquery.min.js"></script>
    <script src="/assets/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            // 创建粒子效果
            function createParticles() {
                const particlesContainer = $('.particles');
                const particleCount = 50;

                for (let i = 0; i < particleCount; i++) {
                    const particle = $('<div class="particle"></div>');
                    const x = Math.random() * window.innerWidth;
                    const y = Math.random() * window.innerHeight;
                    const delay = Math.random() * 6;

                    particle.css({
                        left: x + 'px',
                        top: y + 'px',
                        animationDelay: delay + 's'
                    });

                    particlesContainer.append(particle);
                }
            }

            // 初始化粒子
            createParticles();

            // 表单验证
            $('#loginForm').on('submit', function(e) {
                e.preventDefault();

                var username = $('#username').val().trim();
                var password = $('#password').val();

                if (!username || !password) {
                    showAlert('请填写用户名和密码', 'error');
                    return false;
                }

                // 添加加载效果
                const btn = $('.btn-login');
                const originalHtml = btn.html();
                btn.html('<i class="fas fa-spinner fa-spin"></i> 登录中...');
                btn.prop('disabled', true);

                var formData = $(this).serialize();

                $.ajax({
                    url: '/admin/login/doLogin',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        if (response.code === 1) {
                            showAlert('登录成功！', 'success');
                            // 立即重定向，不等待
                            window.location.href = '/admin/index';
                        } else {
                            showAlert('登录失败：' + response.msg, 'error');
                        }
                    },
                    error: function() {
                        showAlert('网络错误，请稍后重试', 'error');
                    },
                    complete: function() {
                        // 恢复提交按钮
                        btn.prop('disabled', false).html(originalHtml);
                    }
                });
            });

            // 自定义提示框
            function showAlert(message, type) {
                const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
                const icon = type === 'error' ? 'fas fa-exclamation-circle' : 'fas fa-check-circle';

                const alertHtml = `
                    <div class="alert ${alertClass}" role="alert" style="animation: slideInDown 0.5s ease;">
                        <i class="${icon}"></i> ${message}
                    </div>
                `;

                $('.login-body').prepend(alertHtml);

                setTimeout(() => {
                    $('.alert').fadeOut(500, function() {
                        $(this).remove();
                    });
                }, 3000);
            }

            // 输入框焦点效果
            $('.form-control').on('focus', function() {
                $(this).parent().addClass('focused');
            }).on('blur', function() {
                if (!$(this).val()) {
                    $(this).parent().removeClass('focused');
                }
            });

            // 回车键提交
            $(document).keypress(function(e) {
                if (e.which == 13) {
                    $('#loginForm').submit();
                }
            });


        });
    </script>
</body>
</html>

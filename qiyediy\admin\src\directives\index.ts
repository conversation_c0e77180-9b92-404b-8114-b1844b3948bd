/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 全局指令
 */

import type { App } from 'vue'

// 权限指令
const permission = {
  mounted(el: HTMLElement, binding: any) {
    const { value } = binding
    const permissions = JSON.parse(localStorage.getItem('permissions') || '[]')
    
    if (value && !permissions.includes(value)) {
      el.style.display = 'none'
    }
  },
  updated(el: HTMLElement, binding: any) {
    const { value } = binding
    const permissions = JSON.parse(localStorage.getItem('permissions') || '[]')
    
    if (value && !permissions.includes(value)) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
}

// 防抖指令
const debounce = {
  mounted(el: HTMLElement, binding: any) {
    let timer: NodeJS.Timeout
    el.addEventListener('click', () => {
      if (timer) {
        clearTimeout(timer)
      }
      timer = setTimeout(() => {
        binding.value()
      }, binding.arg || 300)
    })
  }
}

// 节流指令
const throttle = {
  mounted(el: HTMLElement, binding: any) {
    let timer: NodeJS.Timeout | null = null
    el.addEventListener('click', () => {
      if (timer) {
        return
      }
      timer = setTimeout(() => {
        binding.value()
        timer = null
      }, binding.arg || 300)
    })
  }
}

// 复制指令
const copy = {
  mounted(el: HTMLElement, binding: any) {
    el.addEventListener('click', () => {
      const text = binding.value
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
          ElMessage.success('复制成功')
        }).catch(() => {
          ElMessage.error('复制失败')
        })
      } else {
        // 兼容旧浏览器
        const textarea = document.createElement('textarea')
        textarea.value = text
        document.body.appendChild(textarea)
        textarea.select()
        document.execCommand('copy')
        document.body.removeChild(textarea)
        ElMessage.success('复制成功')
      }
    })
  }
}

export function setupDirectives(app: App) {
  app.directive('permission', permission)
  app.directive('debounce', debounce)
  app.directive('throttle', throttle)
  app.directive('copy', copy)
}

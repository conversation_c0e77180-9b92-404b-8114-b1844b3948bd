/**
 * 卡片组件 - 全新设计
 * 支持多种布局模式、图片、图标的灵活卡片系统
 */

// 预设图片库
const cardImageLibrary = [
    'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=400&h=300&fit=crop',
    'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=300&fit=crop',
    'https://images.unsplash.com/photo-1556761175-b413da4baf72?w=400&h=300&fit=crop',
    'https://images.unsplash.com/photo-1504384308090-c894fdcc538d?w=400&h=300&fit=crop',
    'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=300&fit=crop',
    'https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?w=400&h=300&fit=crop'
];

// 卡片风格选项
const cardStyleOptions = [
    {
        name: '系统风格',
        key: 'system',
        description: '使用首页设计风格',
        bgColor: '#ffffff',
        titleColor: '#2d3748',
        textColor: '#4a5568',
        buttonColor: '#667eea',
        buttonTextColor: '#ffffff',
        borderRadius: 16,
        shadow: true,
        shadowIntensity: 0.12,
        padding: 32,
        titleSize: 22,
        contentSize: 15,
        isSystemStyle: true
    },
    {
        name: '自定义风格',
        key: 'custom',
        description: '完全自定义样式',
        bgColor: '#ffffff',
        titleColor: '#2d3748',
        textColor: '#4a5568',
        buttonColor: '#667eea',
        isSystemStyle: false
    }
];

// 首页专用模板预设
const homepageTemplates = {
    'features': {
        name: '为什么选择我们',
        description: '首页特色服务区块',
        icon: '⭐',
        data: {
            styleMode: 'system',
            columnsCount: 3,
            layoutMode: 'icon-top',
            cardSpacing: 30,

            // 区块设置
            sectionTitle: '为什么选择我们',
            sectionSubtitle: '专业、可靠、高效的企业级解决方案',
            sectionBackground: 'image',
            sectionBackgroundImage: '/assets/images/ceo-home-vip-bg.png',
            sectionBackgroundColor: '#667eea',
            sectionBackgroundGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            // 特殊标识
            isWhyChooseUs: true,
            cards: [
                {
                    title: '快速部署',
                    content: '专业团队快速响应，高效部署实施，让您的项目快速上线',
                    showIcon: true,
                    iconText: '⚡',
                    showButton: false,
                    buttonText: '',
                    buttonLink: '#'
                },
                {
                    title: '安全可靠',
                    content: '企业级安全保障，多重防护机制，确保数据安全无忧',
                    showIcon: true,
                    iconText: '🛡️',
                    showButton: false,
                    buttonText: '',
                    buttonLink: '#'
                },
                {
                    title: '定制开发',
                    content: '根据业务需求量身定制，满足个性化要求，提供最佳解决方案',
                    showIcon: true,
                    iconText: '⚙️',
                    showButton: false,
                    buttonText: '',
                    buttonLink: '#'
                },
                {
                    title: '7×24服务',
                    content: '全天候技术支持，专业客服团队，及时响应解决问题',
                    showIcon: true,
                    iconText: '🎧',
                    showButton: false,
                    buttonText: '',
                    buttonLink: '#'
                },
                {
                    title: '数据分析',
                    content: '智能数据分析，深度挖掘业务价值，助力决策优化',
                    showIcon: true,
                    iconText: '📊',
                    showButton: false,
                    buttonText: '',
                    buttonLink: '#'
                },
                {
                    title: '团队协作',
                    content: '高效团队协作工具，提升工作效率，促进团队沟通',
                    showIcon: true,
                    iconText: '👥',
                    showButton: false,
                    buttonText: '',
                    buttonLink: '#'
                }
            ],
            bgColor: '#ffffff',
            titleColor: '#2d3748',
            textColor: '#4a5568',
            borderRadius: 16,
            padding: 40,
            shadow: true,
            shadowIntensity: 0.1,
            titleSize: 20,
            contentSize: 15,
            iconSize: 48,
            iconColor: '#667eea'
        }
    },
    'news': {
        name: '最新动态',
        description: '首页新闻资讯区块',
        icon: '📰',
        data: {
            styleMode: 'system',
            columnsCount: 3,
            layoutMode: 'image-top',
            cardSpacing: 30,

            // 区块设置
            sectionTitle: '最新动态',
            sectionSubtitle: '关注我们的最新资讯和行业动态',
            sectionBackground: 'image',
            sectionBackgroundImage: '/assets/images/ceo-home-vip-bg.png',
            sectionBackgroundColor: '#f8fafc',
            sectionBackgroundGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            isLatestNews: true, // 标识为最新动态模板，移除遮罩层
            cards: [
                {
                    title: '企业数字化转型的关键要素',
                    content: '随着数字化时代的到来，企业数字化转型已成为提升竞争力的重要途径。本文将深入探讨数字化转型的核心要素...',
                    showImage: true,
                    imageUrl: '/assets/images/news1.png',
                    selectedImageIndex: 0,
                    showButton: true,
                    buttonText: '阅读更多',
                    buttonLink: '/news',
                    // 新闻专用字段
                    category: '行业资讯',
                    date: '2025-06-04',
                    day: '04',
                    month: 'Jun',
                    views: '1,234',
                    excerpt: '随着数字化时代的到来，企业数字化转型已成为提升竞争力的重要途径。本文将深入探讨数字化转型的核心要素，包括技术架构、组织变革、数据驱动决策等关键方面，为企业提供实用的转型指导。'
                },
                {
                    title: '智能支付系统的安全防护策略',
                    content: '在移动支付快速发展的今天，支付安全成为用户和企业最关心的问题。本文分析当前支付系统面临的安全挑战...',
                    showImage: true,
                    imageUrl: '/assets/images/news2.png',
                    selectedImageIndex: 1,
                    showButton: true,
                    buttonText: '阅读更多',
                    buttonLink: '/news',
                    // 新闻专用字段
                    category: '技术分享',
                    date: '2025-06-03',
                    day: '03',
                    month: 'Jun',
                    views: '856',
                    excerpt: '在移动支付快速发展的今天，支付安全成为用户和企业最关心的问题。本文分析当前支付系统面临的安全挑战，并提出多层次的安全防护策略，确保交易过程的安全可靠。'
                },
                {
                    title: '云端SAAS服务的发展趋势',
                    content: '云计算技术的成熟为SAAS服务带来了新的发展机遇。本文探讨SAAS服务的最新发展趋势...',
                    showImage: true,
                    imageUrl: '/assets/images/news3.png',
                    selectedImageIndex: 2,
                    showButton: true,
                    buttonText: '阅读更多',
                    buttonLink: '/news',
                    // 新闻专用字段
                    category: '产品发布',
                    date: '2025-06-02',
                    day: '02',
                    month: 'Jun',
                    views: '642',
                    excerpt: '云计算技术的成熟为SAAS服务带来了新的发展机遇。本文探讨SAAS服务的最新发展趋势，分析市场需求变化，并展望未来云端服务的创新方向和商业模式。'
                }
            ],
            bgColor: '#ffffff',
            titleColor: '#2d3748',
            textColor: '#4a5568',
            borderRadius: 12,
            padding: 24,
            shadow: true,
            shadowIntensity: 0.08,
            titleSize: 18,
            contentSize: 14,
            imageHeight: 200,

            // 底部按钮设置
            showMoreButton: true,
            moreButtonText: '查看更多动态',
            moreButtonLink: '/news',
            moreButtonIcon: '📰'
        }
    }
};

// 卡片样式预设（保留原有功能）
const cardStylePresets = [
    { name: '简约白色', bgColor: '#ffffff', titleColor: '#2d3748', textColor: '#4a5568', buttonColor: '#667eea' },
    { name: '科技蓝', bgColor: '#1a202c', titleColor: '#ffffff', textColor: '#a0aec0', buttonColor: '#4299e1' },
    { name: '温暖橙', bgColor: '#fff5f5', titleColor: '#c53030', textColor: '#2d3748', buttonColor: '#ed8936' },
    { name: '自然绿', bgColor: '#f0fff4', titleColor: '#22543d', textColor: '#2d3748', buttonColor: '#38a169' },
    { name: '优雅紫', bgColor: '#faf5ff', titleColor: '#553c9a', textColor: '#2d3748', buttonColor: '#805ad5' },
    { name: '现代灰', bgColor: '#f7fafc', titleColor: '#1a202c', textColor: '#4a5568', buttonColor: '#718096' }
];

// 卡片组件模板
const cardComponent = {
    name: '卡片',
    html: `<div class="cards-wrapper">
        <div class="cards-background"></div>
        <div class="cards-overlay"></div>
        <div class="cards-content">
            <div class="cards-container">
                <div class="card-item">
                    <div class="card-image-container" style="display: none;">
                        <img class="card-image" src="" alt="卡片图片">
                    </div>
                    <div class="card-content">
                        <div class="card-icon-container" style="display: none;">
                            <span class="card-icon">🎯</span>
                        </div>
                        <h3 class="card-title">卡片标题1</h3>
                        <p class="card-text">这里是第一个卡片的内容，可以放置图片、文字等信息。</p>
                        <button class="card-btn">了解更多</button>
                    </div>
                </div>
            </div>
        </div>
    </div>`,
    render: function(component, properties) {
        updateCardDisplay(component, properties);
    },
    properties: {
        // 布局设置
        columnsCount: 1, // 1, 2, 3, 4
        layoutMode: 'text-only', // text-only, image-top, image-left, image-right, icon-top
        cardSpacing: 20,

        // 数据源设置
        dataSource: 'static',        // static, news, products, cases
        dataLimit: 6,                // 数据条数限制
        autoRefresh: false,          // 自动刷新数据

        // 字段映射配置
        fieldMapping: {
            title: 'title',          // 标题字段映射
            content: 'content',      // 内容字段映射
            image: 'image',          // 图片字段映射
            link: 'id'               // 链接字段映射
        },

        // 卡片数据数组
        cards: [
            {
                title: '卡片标题1',
                content: '这里是第一个卡片的内容，可以放置图片、文字等信息。',
                showImage: false,
                imageUrl: '',
                selectedImageIndex: 0,
                showIcon: false,
                iconText: '🎯',
                showButton: true,
                buttonText: '了解更多',
                buttonLink: '#'
            }
        ],

        // 风格设置
        styleMode: 'custom', // system 或 custom
        stylePreset: 'simple-white', // 样式预设

        // 区块设置（系统风格专用）
        sectionTitle: '',
        sectionSubtitle: '',
        sectionBackground: 'gradient', // gradient, image, color
        sectionBackgroundImage: '',
        sectionBackgroundColor: '#f8fafc',
        sectionBackgroundGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        bgColor: '#ffffff',
        textColor: '#4a5568',
        titleColor: '#2d3748',
        buttonColor: '#667eea',
        buttonTextColor: '#ffffff',
        buttonStyle: 'filled',

        // 尺寸设置
        maxWidth: 1200,
        borderRadius: 12,
        padding: 24,
        marginHorizontal: 20,
        marginVertical: 20,
        positionVertical: 0,
        imageHeight: 200,
        iconSize: 48,
        iconColor: '#667eea',

        // 阴影设置
        shadow: true,
        shadowIntensity: 0.1,

        // 字体设置
        titleSize: 20,
        contentSize: 14,
        buttonSize: 14,
        textAlign: 'left'
    }
};

// 生成卡片组件属性面板
function generateCardProperties(component) {
    // 获取或初始化组件属性
    let props;
    if (component._cardProperties) {
        props = component._cardProperties;
    } else {
        props = JSON.parse(JSON.stringify(cardComponent.properties));
        component._cardProperties = props;
    }

    let html = `
        <!-- 布局设置 -->
        <div class="property-section">
            <h4 class="section-title">布局设置</h4>

            <div class="property-group">
                <label class="property-label">列数布局</label>
                <div class="layout-buttons-clean">
                    <button type="button" class="layout-btn ${props.columnsCount === 1 ? 'active' : ''}"
                            onclick="updateCardColumns('${component.id}', 1)">1列</button>
                    <button type="button" class="layout-btn ${props.columnsCount === 2 ? 'active' : ''}"
                            onclick="updateCardColumns('${component.id}', 2)">2列</button>
                    <button type="button" class="layout-btn ${props.columnsCount === 3 ? 'active' : ''}"
                            onclick="updateCardColumns('${component.id}', 3)">3列</button>
                    <button type="button" class="layout-btn ${props.columnsCount === 4 ? 'active' : ''}"
                            onclick="updateCardColumns('${component.id}', 4)">4列</button>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">卡片模式</label>
                <select class="property-input" onchange="updateCardProperty('${component.id}', 'layoutMode', this.value)">
                    <option value="text-only" ${props.layoutMode === 'text-only' ? 'selected' : ''}>纯文字</option>
                    <option value="image-top" ${props.layoutMode === 'image-top' ? 'selected' : ''}>图片在上</option>
                    <option value="image-left" ${props.layoutMode === 'image-left' ? 'selected' : ''}>图片在左</option>
                    <option value="image-right" ${props.layoutMode === 'image-right' ? 'selected' : ''}>图片在右</option>
                    <option value="icon-top" ${props.layoutMode === 'icon-top' ? 'selected' : ''}>图标模式</option>
                </select>
            </div>

            <div class="property-group">
                <label class="property-label">卡片间距</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.cardSpacing}" min="10" max="40"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateCardProperty('${component.id}', 'cardSpacing', this.value)">
                    <span class="range-value">${props.cardSpacing}px</span>
                </div>
            </div>
        </div>

        <!-- 数据源配置 -->
        <div class="property-section">
            <h4 class="section-title">数据源配置</h4>

            <div class="property-group">
                <label class="property-label">数据类型</label>
                <select class="property-input" onchange="updateCardDataSource('${component.id}', this.value)">
                    <option value="static" ${props.dataSource === 'static' ? 'selected' : ''}>静态数据</option>
                    <option value="news" ${props.dataSource === 'news' ? 'selected' : ''}>新闻文章</option>
                    <option value="products" ${props.dataSource === 'products' ? 'selected' : ''}>产品信息</option>
                    <option value="cases" ${props.dataSource === 'cases' ? 'selected' : ''}>案例展示</option>
                </select>
            </div>

            ${props.dataSource !== 'static' ? `
                <div class="property-group">
                    <label class="property-label">显示数量</label>
                    <input type="number" class="property-input" value="${props.dataLimit}"
                           min="1" max="20" onchange="updateCardProperty('${component.id}', 'dataLimit', this.value)">
                </div>

                <div class="property-group">
                    <label class="property-label">
                        <input type="checkbox" ${props.autoRefresh ? 'checked' : ''}
                               onchange="updateCardProperty('${component.id}', 'autoRefresh', this.checked)">
                        自动刷新数据
                    </label>
                </div>

                <div class="property-group">
                    <button type="button" class="property-btn" onclick="refreshCardData('${component.id}')">
                        🔄 立即刷新数据
                    </button>
                </div>
            ` : ''}
        </div>

        <!-- 风格选择 -->
        <div class="property-section">
            <h4 class="section-title">风格选择</h4>

            <div class="property-group">
                <label class="property-label">卡片风格</label>
                <div class="style-mode-toggle">
                    <button type="button" class="style-mode-btn ${props.styleMode === 'custom' ? 'active' : ''}"
                            onclick="switchCardStyleMode('${component.id}', 'custom')">
                        自定义风格
                    </button>
                    <button type="button" class="style-mode-btn ${props.styleMode === 'system' ? 'active' : ''}"
                            onclick="switchCardStyleMode('${component.id}', 'system')">
                        ✨ 系统风格
                    </button>
                </div>
                <div class="style-mode-desc">
                    ${props.styleMode === 'system' ? '使用首页设计风格，简洁现代' : '完全自定义样式，可调节所有参数'}
                </div>
            </div>
        </div>

        ${props.styleMode === 'system' ? `
        <!-- 区块设置（系统风格专用） -->
        <div class="property-section">
            <h4 class="section-title">区块设置</h4>

            <div class="property-group">
                <label class="property-label">区块标题</label>
                <input type="text" class="property-input" value="${props.sectionTitle}"
                       onchange="updateCardProperty('${component.id}', 'sectionTitle', this.value)"
                       placeholder="输入区块标题">
            </div>

            <div class="property-group">
                <label class="property-label">区块副标题</label>
                <input type="text" class="property-input" value="${props.sectionSubtitle}"
                       onchange="updateCardProperty('${component.id}', 'sectionSubtitle', this.value)"
                       placeholder="输入区块副标题">
            </div>

            <div class="property-group">
                <label class="property-label">背景类型</label>
                <select class="property-input" onchange="updateCardProperty('${component.id}', 'sectionBackground', this.value)">
                    <option value="gradient" ${props.sectionBackground === 'gradient' ? 'selected' : ''}>渐变背景</option>
                    <option value="image" ${props.sectionBackground === 'image' ? 'selected' : ''}>图片背景</option>
                    <option value="color" ${props.sectionBackground === 'color' ? 'selected' : ''}>纯色背景</option>
                </select>
            </div>

            ${props.sectionBackground === 'gradient' ? `
                <div class="property-group">
                    <label class="property-label">渐变背景推荐</label>
                    <div class="gradient-options" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; margin-bottom: 10px;">
                        <div class="gradient-option" onclick="updateCardGradient('${component.id}', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)')"
                             style="cursor: pointer; height: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 6px; border: 3px solid ${props.sectionBackgroundGradient === 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' ? '#333' : '#e2e8f0'}; box-shadow: ${props.sectionBackgroundGradient === 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' ? '0 0 8px rgba(102, 126, 234, 0.5)' : 'none'};"></div>
                        <div class="gradient-option" onclick="updateCardGradient('${component.id}', 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)')"
                             style="cursor: pointer; height: 40px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 6px; border: 3px solid ${props.sectionBackgroundGradient === 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)' ? '#333' : '#e2e8f0'}; box-shadow: ${props.sectionBackgroundGradient === 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)' ? '0 0 8px rgba(240, 147, 251, 0.5)' : 'none'};"></div>
                        <div class="gradient-option" onclick="updateCardGradient('${component.id}', 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)')"
                             style="cursor: pointer; height: 40px; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 6px; border: 3px solid ${props.sectionBackgroundGradient === 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)' ? '#333' : '#e2e8f0'}; box-shadow: ${props.sectionBackgroundGradient === 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)' ? '0 0 8px rgba(79, 172, 254, 0.5)' : 'none'};"></div>
                        <div class="gradient-option" onclick="updateCardGradient('${component.id}', 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)')"
                             style="cursor: pointer; height: 40px; background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); border-radius: 6px; border: 3px solid ${props.sectionBackgroundGradient === 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)' ? '#333' : '#e2e8f0'}; box-shadow: ${props.sectionBackgroundGradient === 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)' ? '0 0 8px rgba(67, 233, 123, 0.5)' : 'none'};"></div>
                        <div class="gradient-option" onclick="updateCardGradient('${component.id}', 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)')"
                             style="cursor: pointer; height: 40px; background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); border-radius: 6px; border: 3px solid ${props.sectionBackgroundGradient === 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)' ? '#333' : '#e2e8f0'}; box-shadow: ${props.sectionBackgroundGradient === 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)' ? '0 0 8px rgba(250, 112, 154, 0.5)' : 'none'};"></div>
                        <div class="gradient-option" onclick="updateCardGradient('${component.id}', 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)')"
                             style="cursor: pointer; height: 40px; background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); border-radius: 6px; border: 3px solid ${props.sectionBackgroundGradient === 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)' ? '#333' : '#e2e8f0'}; box-shadow: ${props.sectionBackgroundGradient === 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)' ? '0 0 8px rgba(168, 237, 234, 0.5)' : 'none'};"></div>
                    </div>
                </div>
            ` : ''}

            ${props.sectionBackground === 'image' ? `
                <div class="property-group">
                    <label class="property-label">背景图片选择</label>
                    <div class="image-options-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; margin-bottom: 10px;">
                        <div class="image-option ${props.sectionBackgroundImage === '/assets/images/ceo-home-vip-bg.png' ? 'selected' : ''}"
                             onclick="updateCardProperty('${component.id}', 'sectionBackgroundImage', '/assets/images/ceo-home-vip-bg.png')"
                             style="cursor: pointer; border: 3px solid ${props.sectionBackgroundImage === '/assets/images/ceo-home-vip-bg.png' ? '#667eea' : '#e2e8f0'}; border-radius: 6px; overflow: hidden; aspect-ratio: 16/9; box-shadow: ${props.sectionBackgroundImage === '/assets/images/ceo-home-vip-bg.png' ? '0 0 0 2px rgba(102, 126, 234, 0.3)' : 'none'}; transform: ${props.sectionBackgroundImage === '/assets/images/ceo-home-vip-bg.png' ? 'scale(1.05)' : 'scale(1)'}; transition: all 0.2s ease;">
                            <img src="/assets/images/ceo-home-vip-bg.png" style="width: 100%; height: 100%; object-fit: cover;" alt="VIP背景">
                        </div>
                        <div class="image-option ${props.sectionBackgroundImage === '/assets/images/ceo-apply-bg1.png' ? 'selected' : ''}"
                             onclick="updateCardProperty('${component.id}', 'sectionBackgroundImage', '/assets/images/ceo-apply-bg1.png')"
                             style="cursor: pointer; border: 3px solid ${props.sectionBackgroundImage === '/assets/images/ceo-apply-bg1.png' ? '#667eea' : '#e2e8f0'}; border-radius: 6px; overflow: hidden; aspect-ratio: 16/9; box-shadow: ${props.sectionBackgroundImage === '/assets/images/ceo-apply-bg1.png' ? '0 0 0 2px rgba(102, 126, 234, 0.3)' : 'none'}; transform: ${props.sectionBackgroundImage === '/assets/images/ceo-apply-bg1.png' ? 'scale(1.05)' : 'scale(1)'}; transition: all 0.2s ease;">
                            <img src="/assets/images/ceo-apply-bg1.png" style="width: 100%; height: 100%; object-fit: cover;" alt="商家入驻">
                        </div>
                        <div class="image-option ${props.sectionBackgroundImage === '/assets/images/bg1.jpg' ? 'selected' : ''}"
                             onclick="updateCardProperty('${component.id}', 'sectionBackgroundImage', '/assets/images/bg1.jpg')"
                             style="cursor: pointer; border: 3px solid ${props.sectionBackgroundImage === '/assets/images/bg1.jpg' ? '#667eea' : '#e2e8f0'}; border-radius: 6px; overflow: hidden; aspect-ratio: 16/9; box-shadow: ${props.sectionBackgroundImage === '/assets/images/bg1.jpg' ? '0 0 0 2px rgba(102, 126, 234, 0.3)' : 'none'}; transform: ${props.sectionBackgroundImage === '/assets/images/bg1.jpg' ? 'scale(1.05)' : 'scale(1)'}; transition: all 0.2s ease;">
                            <img src="/assets/images/bg1.jpg" style="width: 100%; height: 100%; object-fit: cover;" alt="背景1">
                        </div>
                    </div>
                    <input type="url" class="property-input" value="${props.sectionBackgroundImage}"
                           onchange="updateCardProperty('${component.id}', 'sectionBackgroundImage', this.value)"
                           placeholder="或输入自定义图片URL" style="font-size: 12px;">
                </div>
            ` : ''}

            ${props.sectionBackground === 'color' ? `
                <div class="property-group">
                    <label class="property-label">背景颜色</label>
                    <div class="color-palette" style="display: grid; grid-template-columns: repeat(6, 1fr); gap: 6px; margin-bottom: 10px;">
                        <div class="color-option" onclick="updateCardProperty('${component.id}', 'sectionBackgroundColor', '#667eea')"
                             style="width: 30px; height: 30px; background: #667eea; border-radius: 4px; cursor: pointer; border: 3px solid ${props.sectionBackgroundColor === '#667eea' ? '#333' : '#e2e8f0'}; box-shadow: ${props.sectionBackgroundColor === '#667eea' ? '0 0 0 2px #667eea' : 'none'};"></div>
                        <div class="color-option" onclick="updateCardProperty('${component.id}', 'sectionBackgroundColor', '#764ba2')"
                             style="width: 30px; height: 30px; background: #764ba2; border-radius: 4px; cursor: pointer; border: 3px solid ${props.sectionBackgroundColor === '#764ba2' ? '#333' : '#e2e8f0'}; box-shadow: ${props.sectionBackgroundColor === '#764ba2' ? '0 0 0 2px #764ba2' : 'none'};"></div>
                        <div class="color-option" onclick="updateCardProperty('${component.id}', 'sectionBackgroundColor', '#f093fb')"
                             style="width: 30px; height: 30px; background: #f093fb; border-radius: 4px; cursor: pointer; border: 3px solid ${props.sectionBackgroundColor === '#f093fb' ? '#333' : '#e2e8f0'}; box-shadow: ${props.sectionBackgroundColor === '#f093fb' ? '0 0 0 2px #f093fb' : 'none'};"></div>
                        <div class="color-option" onclick="updateCardProperty('${component.id}', 'sectionBackgroundColor', '#f5576c')"
                             style="width: 30px; height: 30px; background: #f5576c; border-radius: 4px; cursor: pointer; border: 3px solid ${props.sectionBackgroundColor === '#f5576c' ? '#333' : '#e2e8f0'}; box-shadow: ${props.sectionBackgroundColor === '#f5576c' ? '0 0 0 2px #f5576c' : 'none'};"></div>
                        <div class="color-option" onclick="updateCardProperty('${component.id}', 'sectionBackgroundColor', '#4facfe')"
                             style="width: 30px; height: 30px; background: #4facfe; border-radius: 4px; cursor: pointer; border: 3px solid ${props.sectionBackgroundColor === '#4facfe' ? '#333' : '#e2e8f0'}; box-shadow: ${props.sectionBackgroundColor === '#4facfe' ? '0 0 0 2px #4facfe' : 'none'};"></div>
                        <div class="color-option" onclick="updateCardProperty('${component.id}', 'sectionBackgroundColor', '#43e97b')"
                             style="width: 30px; height: 30px; background: #43e97b; border-radius: 4px; cursor: pointer; border: 3px solid ${props.sectionBackgroundColor === '#43e97b' ? '#333' : '#e2e8f0'}; box-shadow: ${props.sectionBackgroundColor === '#43e97b' ? '0 0 0 2px #43e97b' : 'none'};"></div>
                    </div>
                    <input type="color" class="property-input" value="${props.sectionBackgroundColor}"
                           onchange="updateCardProperty('${component.id}', 'sectionBackgroundColor', this.value)"
                           style="width: 100%; height: 40px;">
                </div>
            ` : ''}
        </div>
        ` : ''}

        ${props.styleMode === 'system' ? `
        <!-- 首页模板选择 -->
        <div class="property-section">
            <h4 class="section-title">🏠 首页模板</h4>

            <div class="property-group">
                <label class="property-label">一键应用首页区块</label>
                <div class="homepage-templates">
                    ${Object.keys(homepageTemplates).map(key => {
                        const template = homepageTemplates[key];
                        return `
                        <div class="homepage-template-card compact" onclick="applyHomepageTemplate('${component.id}', '${key}')">
                            <div class="template-icon">${template.icon}</div>
                            <div class="template-info">
                                <div class="template-name">${template.name}</div>
                            </div>
                            <div class="template-apply">应用</div>
                        </div>
                        `;
                    }).join('')}
                </div>
            </div>
        </div>
        ` : ''}

        <!-- 卡片内容管理 (仅静态数据时显示) -->
        ${props.dataSource === 'static' ? `
        <div class="property-section">
            <h4 class="section-title">卡片内容 (${props.cards.length}个)</h4>

            <div class="cards-manager">
                ${props.cards.map((card, index) => `
                    <div class="card-editor" data-index="${index}">
                        <div class="card-editor-header">
                            <span class="card-editor-title">卡片 ${index + 1}</span>
                            <div class="card-editor-actions">
                                <button type="button" class="card-action-btn" onclick="toggleCardEditor(this)">
                                    <span class="toggle-icon">▼</span>
                                </button>
                                ${props.cards.length > 1 ? `
                                    <button type="button" class="card-action-btn delete" onclick="deleteCard('${component.id}', ${index})">
                                        ✕
                                    </button>
                                ` : ''}
                            </div>
                        </div>

                        <div class="card-editor-content">
                            <div class="property-group">
                                <label class="property-label">标题</label>
                                <input type="text" class="property-input" value="${card.title}"
                                       onchange="updateCardContent('${component.id}', ${index}, 'title', this.value)">
                            </div>

                            <div class="property-group">
                                <label class="property-label">内容</label>
                                <textarea class="property-input" rows="2"
                                          onchange="updateCardContent('${component.id}', ${index}, 'content', this.value)">${card.content}</textarea>
                            </div>

                            ${props.layoutMode.includes('image') ? `
                                <div class="property-group">
                                    <label class="property-label">图片</label>
                                    <div class="image-library-mini">
                                        ${cardImageLibrary.slice(0, 3).map((image, imgIndex) => `
                                            <div class="image-option-mini ${card.selectedImageIndex === imgIndex ? 'selected' : ''}"
                                                 style="background-image: url('${image}')"
                                                 onclick="updateCardContent('${component.id}', ${index}, 'selectedImageIndex', ${imgIndex})"
                                                 title="图片 ${imgIndex + 1}">
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            ` : ''}

                            ${props.layoutMode === 'icon-top' ? `
                                <div class="property-group">
                                    <label class="property-label">图标</label>
                                    <div class="icon-selector-mini">
                                        ${['💡', '🚀', '⭐', '🎨', '🔥', '⚡', '🛡️', '⚙️', '🎯', '💎', '🏆', '🔧', '📊', '🌟', '💪', '👥', '🎪', '🔮', '⚖️', '🎭', '🎵', '🎲', '🎳', '🎸', '🎺', '🎻', '🎤', '🎧', '🎬', '🎮'].map(icon => `
                                            <div class="icon-option-mini ${card.iconText === icon ? 'selected' : ''}"
                                                 onclick="updateCardContent('${component.id}', ${index}, 'iconText', '${icon}')">
                                                ${icon}
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            ` : ''}

                            <div class="property-group">
                                <div class="checkbox-group">
                                    <div class="checkbox-item">
                                        <input type="checkbox" ${card.showButton ? 'checked' : ''}
                                               onchange="updateCardContent('${component.id}', ${index}, 'showButton', this.checked)">
                                        <span>显示按钮</span>
                                    </div>
                                </div>
                            </div>

                            ${card.showButton ? `
                                <div class="property-group">
                                    <input type="text" class="property-input" value="${card.buttonText}" placeholder="按钮文字"
                                           onchange="updateCardContent('${component.id}', ${index}, 'buttonText', this.value)">
                                </div>

                                <div class="property-group">
                                    <input type="url" class="property-input" value="${card.buttonLink}" placeholder="按钮链接"
                                           onchange="updateCardContent('${component.id}', ${index}, 'buttonLink', this.value)">
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `).join('')}
            </div>

            ${props.cards.length < props.columnsCount ? `
                <button type="button" class="add-btn" onclick="addCard('${component.id}')">
                    + 添加卡片
                </button>
            ` : ''}
        </div>
        ` : ''}
    `;

    // 图片设置（仅在图片模式下显示）
    if (props.layoutMode.includes('image')) {
        html += `
            <!-- 图片设置 -->
            <div class="property-section">
                <h4 class="section-title">图片设置</h4>



                <div class="property-group">
                    <label class="property-label">图片高度</label>
                    <div class="range-control">
                        <input type="range" class="range-slider" value="${props.imageHeight}" min="100" max="400"
                               oninput="this.nextElementSibling.textContent = this.value + 'px'; updateCardProperty('${component.id}', 'imageHeight', this.value)">
                        <span class="range-value">${props.imageHeight}px</span>
                    </div>
                </div>
            </div>
        `;
    }

    // 图标设置（仅在图标模式下显示）
    if (props.layoutMode === 'icon-top') {
        html += `
            <!-- 图标设置 -->
            <div class="property-section">
                <h4 class="section-title">图标设置</h4>



                <div class="property-group">
                    <label class="property-label">图标大小</label>
                    <div class="range-control">
                        <input type="range" class="range-slider" value="${props.iconSize}" min="24" max="80"
                               oninput="this.nextElementSibling.textContent = this.value + 'px'; updateCardProperty('${component.id}', 'iconSize', this.value)">
                        <span class="range-value">${props.iconSize}px</span>
                    </div>
                </div>
            </div>
        `;
    }

    if (props.styleMode === 'custom') {
        html += `
        <!-- 样式预设 -->
        <div class="property-section">
            <h4 class="section-title">样式预设</h4>

            <div class="property-group">
                <label class="property-label">快速样式</label>
                <div class="style-presets">
                    ${cardStylePresets.map((preset, index) => {
                        const presetKey = preset.name.toLowerCase().replace(/\s+/g, '-');
                        const isSelected = props.stylePreset === presetKey;
                        return `
                        <div class="style-preset ${isSelected ? 'selected' : ''}"
                             style="background: ${preset.bgColor}; color: ${preset.titleColor}; border: 2px solid ${preset.buttonColor};"
                             onclick="applyCardStylePreset('${component.id}', ${index})"
                             title="${preset.name}">
                            <span style="font-size: 12px; font-weight: bold;">${preset.name}</span>
                        </div>
                        `;
                    }).join('')}
                </div>
            </div>
        </div>

        <!-- 按钮设置 -->
        <div class="property-section">
            <h4 class="section-title">按钮设置</h4>

            <div class="property-group">
                <label class="property-label">按钮样式</label>
                <select class="property-input" onchange="updateCardProperty('${component.id}', 'buttonStyle', this.value)">
                    <option value="filled" ${props.buttonStyle === 'filled' ? 'selected' : ''}>填充样式</option>
                    <option value="outline" ${props.buttonStyle === 'outline' ? 'selected' : ''}>边框样式</option>
                    <option value="text" ${props.buttonStyle === 'text' ? 'selected' : ''}>文字样式</option>
                </select>
            </div>

            <div class="property-group">
                <label class="property-label">按钮颜色</label>
                <div class="color-row">
                    <div class="color-item">
                        <label>背景色</label>
                        <input type="color" class="property-input" value="${props.buttonColor}"
                               onchange="updateCardProperty('${component.id}', 'buttonColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>文字色</label>
                        <input type="color" class="property-input" value="${props.buttonTextColor}"
                               onchange="updateCardProperty('${component.id}', 'buttonTextColor', this.value)">
                    </div>
                </div>
            </div>
        </div>

        <!-- 颜色设置 -->
        <div class="property-section">
            <h4 class="section-title">颜色设置</h4>

            <div class="property-group">
                <div class="color-row">
                    <div class="color-item">
                        <label>背景色</label>
                        <input type="color" class="property-input" value="${props.bgColor}"
                               onchange="updateCardProperty('${component.id}', 'bgColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>标题色</label>
                        <input type="color" class="property-input" value="${props.titleColor}"
                               onchange="updateCardProperty('${component.id}', 'titleColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>文字色</label>
                        <input type="color" class="property-input" value="${props.textColor}"
                               onchange="updateCardProperty('${component.id}', 'textColor', this.value)">
                    </div>
                </div>
            </div>
        </div>

        <!-- 尺寸设置 -->
        <div class="property-section">
            <h4 class="section-title">尺寸设置</h4>

            <div class="property-group">
                <label class="property-label">最大宽度</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.maxWidth}" min="600" max="1800"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateCardProperty('${component.id}', 'maxWidth', this.value)">
                    <span class="range-value">${props.maxWidth}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">内边距</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.padding}" min="12" max="60"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateCardProperty('${component.id}', 'padding', this.value)">
                    <span class="range-value">${props.padding}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">圆角大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.borderRadius}" min="0" max="30"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateCardProperty('${component.id}', 'borderRadius', this.value)">
                    <span class="range-value">${props.borderRadius}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">左右外边距</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.marginHorizontal}" min="0" max="100"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateCardProperty('${component.id}', 'marginHorizontal', this.value)">
                    <span class="range-value">${props.marginHorizontal}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">上下外边距</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.marginVertical}" min="0" max="100"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateCardProperty('${component.id}', 'marginVertical', this.value)">
                    <span class="range-value">${props.marginVertical}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">上下位置调节</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.positionVertical}" min="-600" max="600"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateCardProperty('${component.id}', 'positionVertical', this.value)">
                    <span class="range-value">${props.positionVertical}px</span>
                </div>
                <div class="range-tips">
                    <small>负值向上移动，正值向下移动。层级控制请使用组件上方的"上层"/"下层"按钮</small>
                </div>
            </div>
        </div>

        <!-- 字体设置 -->
        <div class="property-section">
            <h4 class="section-title">字体设置</h4>

            <div class="property-group">
                <label class="property-label">标题大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.titleSize}" min="14" max="32"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateCardProperty('${component.id}', 'titleSize', this.value)">
                    <span class="range-value">${props.titleSize}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">内容大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.contentSize}" min="12" max="20"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateCardProperty('${component.id}', 'contentSize', this.value)">
                    <span class="range-value">${props.contentSize}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">文字对齐</label>
                <select class="property-input" onchange="updateCardProperty('${component.id}', 'textAlign', this.value)">
                    <option value="left" ${props.textAlign === 'left' ? 'selected' : ''}>左对齐</option>
                    <option value="center" ${props.textAlign === 'center' ? 'selected' : ''}>居中对齐</option>
                    <option value="right" ${props.textAlign === 'right' ? 'selected' : ''}>右对齐</option>
                </select>
            </div>
        </div>

        <!-- 阴影设置 -->
        <div class="property-section">
            <h4 class="section-title">阴影设置</h4>

            <div class="property-group">
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" ${props.shadow ? 'checked' : ''}
                               onchange="updateCardProperty('${component.id}', 'shadow', this.checked)">
                        <span>阴影效果</span>
                    </div>
                </div>
            </div>

            ${props.shadow ? `
                <div class="property-group">
                    <label class="property-label">阴影强度</label>
                    <div class="range-control">
                        <input type="range" class="range-slider" value="${props.shadowIntensity}" min="0" max="0.3" step="0.05"
                               oninput="this.nextElementSibling.textContent = Math.round(this.value * 100) + '%'; updateCardProperty('${component.id}', 'shadowIntensity', this.value)">
                        <span class="range-value">${Math.round(props.shadowIntensity * 100)}%</span>
                    </div>
                </div>
            ` : ''}
        </div>
        `;
    }

    return html;
}

// 实时更新卡片间距显示值
function updateCardSpacing(componentId, value) {
    const valueDisplay = document.getElementById(`spacing-value-${componentId}`);
    if (valueDisplay) {
        valueDisplay.textContent = `${value}px`;
    }
}

// 实时更新按钮大小显示值
function updateButtonSize(componentId, value) {
    const valueDisplay = document.getElementById(`button-size-value-${componentId}`);
    if (valueDisplay) {
        valueDisplay.textContent = `${value}px`;
    }
}

// 更新卡片列数
function updateCardColumns(componentId, columns) {
    const component = document.getElementById(componentId);
    if (!component) return;

    // 获取或初始化组件属性
    let props;
    if (component._cardProperties) {
        props = component._cardProperties;
    } else {
        props = JSON.parse(JSON.stringify(cardComponent.properties));
        component._cardProperties = props;
    }

    props.columnsCount = columns;

    // 调整卡片数量以匹配列数
    while (props.cards.length < columns) {
        props.cards.push({
            title: `卡片标题${props.cards.length + 1}`,
            content: `这里是第${props.cards.length + 1}个卡片的内容。`,
            showImage: false,
            imageUrl: '',
            selectedImageIndex: props.cards.length % cardImageLibrary.length,
            showIcon: false,
            iconText: ['🎯', '💡', '🚀', '⭐', '🎨'][props.cards.length % 5],
            showButton: true,
            buttonText: '了解更多',
            buttonLink: '#'
        });
    }

    // 如果卡片数量超过列数，保留前N个
    if (props.cards.length > columns) {
        props.cards = props.cards.slice(0, columns);
    }

    updateCardDisplay(component, props);
    updatePropertiesPanel(component);
}

// 添加卡片
function addCard(componentId) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props;
    if (component._cardProperties) {
        props = component._cardProperties;
    } else {
        props = JSON.parse(JSON.stringify(cardComponent.properties));
        component._cardProperties = props;
    }

    if (props.cards.length < props.columnsCount) {
        props.cards.push({
            title: `卡片标题${props.cards.length + 1}`,
            content: `这里是第${props.cards.length + 1}个卡片的内容。`,
            showImage: false,
            imageUrl: '',
            selectedImageIndex: props.cards.length % cardImageLibrary.length,
            showIcon: false,
            iconText: ['🎯', '💡', '🚀', '⭐', '🎨'][props.cards.length % 5],
            showButton: true,
            buttonText: '了解更多',
            buttonLink: '#'
        });

        updateCardDisplay(component, props);
        updatePropertiesPanel(component);
    }
}

// 删除卡片
function deleteCard(componentId, cardIndex) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props;
    if (component._cardProperties) {
        props = component._cardProperties;
    } else {
        props = JSON.parse(JSON.stringify(cardComponent.properties));
        component._cardProperties = props;
    }

    if (props.cards.length > 1) {
        props.cards.splice(cardIndex, 1);
        updateCardDisplay(component, props);
        updatePropertiesPanel(component);
    }
}

// 更新单个卡片内容
function updateCardContent(componentId, cardIndex, property, value) {
    const component = document.getElementById(componentId);
    if (!component) return;

    // 获取组件的属性数据
    let props;
    if (component._cardProperties) {
        props = component._cardProperties;
    } else {
        // 初始化组件属性
        props = JSON.parse(JSON.stringify(cardComponent.properties));
        component._cardProperties = props;
    }

    if (props.cards && props.cards[cardIndex]) {
        if (typeof value === 'boolean') {
            props.cards[cardIndex][property] = value;
        } else if (property === 'selectedImageIndex') {
            props.cards[cardIndex][property] = parseInt(value);
            props.cards[cardIndex].imageUrl = cardImageLibrary[value];
            props.cards[cardIndex].showImage = true;
        } else {
            props.cards[cardIndex][property] = value;
        }

        updateCardDisplay(component, props);

        // 如果是按钮显示状态改变，需要重新生成属性面板
        if (property === 'showButton') {
            updatePropertiesPanel(component);
        }
    }
}

// 切换卡片编辑器展开/收起
function toggleCardEditor(button) {
    const header = button.closest('.card-editor-header');
    const content = header.nextElementSibling;
    const icon = button.querySelector('.toggle-icon');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        icon.textContent = '▼';
    } else {
        content.style.display = 'none';
        icon.textContent = '▶';
    }
}

// 应用样式预设
function applyCardStylePreset(componentId, presetIndex) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props;
    if (component._cardProperties) {
        props = component._cardProperties;
    } else {
        props = JSON.parse(JSON.stringify(cardComponent.properties));
        component._cardProperties = props;
    }

    const preset = cardStylePresets[presetIndex];

    // 应用预设样式
    props.stylePreset = preset.name.toLowerCase().replace(/\s+/g, '-');
    props.bgColor = preset.bgColor;
    props.titleColor = preset.titleColor;
    props.textColor = preset.textColor;
    props.buttonColor = preset.buttonColor;

    updateCardDisplay(component, props);
    updatePropertiesPanel(component);
}

// 更新卡片属性
function updateCardProperty(componentId, property, value) {
    const component = document.getElementById(componentId);
    if (!component) return;

    // 获取或初始化组件属性
    let props;
    if (component._cardProperties) {
        props = component._cardProperties;
    } else {
        props = JSON.parse(JSON.stringify(cardComponent.properties));
        component._cardProperties = props;
    }

    if (typeof value === 'boolean') {
        props[property] = value;
    } else if (['padding', 'borderRadius', 'titleSize', 'contentSize', 'buttonSize', 'imageHeight', 'iconSize', 'cardSpacing', 'columnsCount', 'marginHorizontal', 'marginVertical', 'positionVertical'].includes(property)) {
        props[property] = parseInt(value);
    } else if (property === 'shadowIntensity') {
        props[property] = parseFloat(value);
    } else {
        props[property] = value;
    }

    // 特殊处理：布局模式改变时的逻辑
    if (property === 'layoutMode') {
        // 更新所有卡片的图片和图标显示状态
        props.cards.forEach(card => {
            card.showImage = value.includes('image');
            card.showIcon = value === 'icon-top';
        });
    }

    updateCardDisplay(component, props);

    // 需要重新生成属性面板的情况
    if (['layoutMode', 'sectionBackground', 'sectionBackgroundImage', 'sectionBackgroundColor', 'sectionBackgroundGradient'].includes(property)) {
        updatePropertiesPanel(component);
    }
}

// 更新卡片显示
function updateCardDisplay(component, props) {
    // 如果是系统风格，需要创建区块包装
    if (props.styleMode === 'system') {
        updateSystemStyleDisplay(component, props);
        return;
    }

    const container = component.querySelector('.cards-container');
    if (!container) return;

    // 设置容器样式
    container.style.display = 'grid';
    container.style.gridTemplateColumns = `repeat(${props.columnsCount}, 1fr)`;
    container.style.gap = `${props.cardSpacing}px`;
    container.style.margin = `${props.marginVertical}px ${props.marginHorizontal}px`;
    container.style.maxWidth = `${props.maxWidth}px`;
    container.style.marginLeft = 'auto';
    container.style.marginRight = 'auto';

    // 将transform应用到整个component-block，这样控制按钮也会跟着移动
    component.style.transform = `translateY(${props.positionVertical}px)`;
    component.style.position = 'relative';

    // 生成卡片HTML
    container.innerHTML = props.cards.map((card, index) => {
        return `
            <div class="card-item layout-${props.layoutMode}">
                ${props.layoutMode.includes('image') && card.showImage ? `
                    <div class="card-image-container">
                        <img class="card-image" src="${card.imageUrl || cardImageLibrary[card.selectedImageIndex]}" alt="卡片图片">
                    </div>
                ` : ''}

                <div class="card-content">
                    ${props.layoutMode === 'icon-top' && card.showIcon ? `
                        <div class="card-icon-container">
                            <span class="card-icon">${card.iconText}</span>
                        </div>
                    ` : ''}

                    <h3 class="card-title">${card.title}</h3>
                    <p class="card-text">${card.content}</p>

                    ${card.showButton ? `
                        <button class="card-btn" onclick="handleCardButtonClick('${card.buttonLink}')">
                            ${card.buttonText}
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    }).join('');

    // 应用样式到所有卡片
    const cardItems = container.querySelectorAll('.card-item');
    cardItems.forEach((cardItem, index) => {
        const card = props.cards[index];

        // 基础卡片样式
        cardItem.style.backgroundColor = props.bgColor;
        cardItem.style.color = props.textColor;
        cardItem.style.borderRadius = `${props.borderRadius}px`;
        cardItem.style.padding = `${props.padding}px`;
        cardItem.style.textAlign = props.textAlign;
        cardItem.style.boxShadow = props.shadow ?
            `0 4px 20px rgba(0,0,0,${props.shadowIntensity})` : 'none';
        cardItem.style.transition = 'all 0.3s ease';
        cardItem.style.overflow = 'hidden';

        // 系统风格特殊效果
        if (props.styleMode === 'system') {
            cardItem.style.border = '1px solid #e2e8f0';
            cardItem.style.background = 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)';
            cardItem.style.boxShadow = '0 8px 32px rgba(102, 126, 234, 0.12), 0 2px 8px rgba(0, 0, 0, 0.04)';

            // 添加悬停效果
            cardItem.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px)';
                this.style.boxShadow = '0 12px 40px rgba(102, 126, 234, 0.18), 0 4px 12px rgba(0, 0, 0, 0.08)';
            });

            cardItem.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 8px 32px rgba(102, 126, 234, 0.12), 0 2px 8px rgba(0, 0, 0, 0.04)';
            });
        }

        // 图片样式
        const image = cardItem.querySelector('.card-image');
        if (image) {
            image.style.height = `${props.imageHeight}px`;
            image.style.objectFit = 'cover';
            image.style.width = '100%';

            if (props.layoutMode === 'image-top') {
                image.style.borderRadius = `${props.borderRadius}px ${props.borderRadius}px 0 0`;
                const imageContainer = cardItem.querySelector('.card-image-container');
                imageContainer.style.margin = `-${props.padding}px -${props.padding}px 16px -${props.padding}px`;
            } else {
                image.style.borderRadius = `${props.borderRadius}px`;
            }
        }

        // 图标样式
        const icon = cardItem.querySelector('.card-icon');
        if (icon) {
            icon.style.fontSize = `${props.iconSize}px`;
            icon.style.color = props.iconColor;
            icon.style.marginBottom = '16px';
        }

        // 标题样式
        const title = cardItem.querySelector('.card-title');
        if (title) {
            title.style.color = props.titleColor;
            title.style.fontSize = `${props.titleSize}px`;
            title.style.marginBottom = '12px';
            title.style.fontWeight = '600';
            title.style.lineHeight = '1.3';
        }

        // 文本样式
        const text = cardItem.querySelector('.card-text');
        if (text) {
            text.style.fontSize = `${props.contentSize}px`;
            text.style.lineHeight = '1.6';
            text.style.marginBottom = card.showButton ? '16px' : '0';
            text.style.opacity = '0.8';
        }

        // 按钮样式
        const button = cardItem.querySelector('.card-btn');
        if (button) {
            button.style.marginTop = '16px';
            button.style.padding = '10px 20px';
            button.style.border = 'none';
            button.style.borderRadius = '6px';
            button.style.cursor = 'pointer';
            button.style.fontSize = `${props.buttonSize !== undefined ? props.buttonSize : 14}px`;
            button.style.transition = 'all 0.3s ease';

            // 按钮样式
            if (props.buttonStyle === 'filled') {
                button.style.backgroundColor = props.buttonColor;
                button.style.color = props.buttonTextColor;
                button.style.border = 'none';
            } else if (props.buttonStyle === 'outline') {
                button.style.backgroundColor = 'transparent';
                button.style.color = props.buttonColor;
                button.style.border = `2px solid ${props.buttonColor}`;
            } else if (props.buttonStyle === 'text') {
                button.style.backgroundColor = 'transparent';
                button.style.color = props.buttonColor;
                button.style.border = 'none';
                button.style.textDecoration = 'underline';
            }
        }
    });
}

// 处理卡片按钮点击
function handleCardButtonClick(link) {
    if (link && link !== '#') {
        window.open(link, '_blank');
    }
}

// 切换卡片风格模式
function switchCardStyleMode(componentId, mode) {
    const component = document.getElementById(componentId);
    if (!component) return;

    // 获取组件属性
    let props;
    if (component._cardProperties) {
        props = component._cardProperties;
    } else {
        props = JSON.parse(JSON.stringify(cardComponent.properties));
        component._cardProperties = props;
    }

    // 更新风格模式
    props.styleMode = mode;

    // 如果切换到系统风格，应用系统风格设置
    if (mode === 'system') {
        const systemStyle = cardStyleOptions.find(option => option.key === 'system');
        if (systemStyle) {
            props.bgColor = systemStyle.bgColor;
            props.titleColor = systemStyle.titleColor;
            props.textColor = systemStyle.textColor;
            props.buttonColor = systemStyle.buttonColor;
            props.buttonTextColor = systemStyle.buttonTextColor;
            props.borderRadius = systemStyle.borderRadius;
            props.shadow = systemStyle.shadow;
            props.shadowIntensity = systemStyle.shadowIntensity;
            props.padding = systemStyle.padding;
            props.titleSize = systemStyle.titleSize;
            props.contentSize = systemStyle.contentSize;
            props.buttonStyle = 'filled';
            props.textAlign = 'left';
        }

        // 系统风格默认使用新闻文章数据源
        props.dataSource = 'news';
        props.dataLimit = 6;

        // 获取动态数据
        if (typeof fetchCardData === 'function') {
            fetchCardData(component, props);
        }
    } else if (mode === 'custom') {
        // 切换到自定义风格时，确保所有属性都有默认值
        const defaultProps = JSON.parse(JSON.stringify(cardComponent.properties));

        // 保留当前的重要设置
        const currentColumnsCount = props.columnsCount;
        const currentLayoutMode = props.layoutMode;
        const currentCardSpacing = props.cardSpacing;
        const currentCards = props.cards;

        // 合并默认属性，确保没有undefined值
        Object.keys(defaultProps).forEach(key => {
            if (props[key] === undefined) {
                props[key] = defaultProps[key];
            }
        });

        // 恢复保留的设置
        props.columnsCount = currentColumnsCount;
        props.layoutMode = currentLayoutMode;
        props.cardSpacing = currentCardSpacing;
        props.cards = currentCards;
        props.styleMode = 'custom';
        props.dataSource = 'static';
    }

    // 更新显示和属性面板
    updateCardDisplay(component, props);
    updatePropertiesPanel(component);
}

// 应用首页模板
function applyHomepageTemplate(componentId, templateKey) {
    const component = document.getElementById(componentId);
    if (!component || !homepageTemplates[templateKey]) return;

    const template = homepageTemplates[templateKey];

    // 应用模板数据
    const props = JSON.parse(JSON.stringify(template.data));

    // 首页模板默认使用新闻数据源（因为是系统风格）
    if (props.styleMode === 'system') {
        props.dataSource = 'news';
        props.dataLimit = 6;
    }

    component._cardProperties = props;

    // 更新显示和属性面板
    updateCardDisplay(component, props);
    updatePropertiesPanel(component);

    // 如果是动态数据源，获取数据
    if (props.dataSource && props.dataSource !== 'static') {
        if (typeof fetchCardData === 'function') {
            fetchCardData(component, props);
        }
    }

    // 显示成功提示
    showToast(`✅ 已应用"${template.name}"模板，完美还原首页效果！`);
}

// 显示提示信息
function showToast(message) {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = 'template-toast';
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        font-size: 14px;
        font-weight: 500;
        animation: slideInRight 0.3s ease;
    `;

    // 添加动画样式
    if (!document.querySelector('#toast-styles')) {
        const style = document.createElement('style');
        style.id = 'toast-styles';
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(toast);

    // 3秒后自动消失
    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// 系统风格显示更新 - 完整借鉴 stats.js 的实力见证模板
function updateSystemStyleDisplay(component, props) {
    const wrapper = component.querySelector('.cards-wrapper');
    const container = component.querySelector('.cards-container');
    if (!container || !wrapper) return;

    // 动态管理HTML结构 - 根据模板类型添加或移除元素
    const content = wrapper.querySelector('.cards-content');
    if (!content) return;

    // 管理标题区域 - 借鉴 stats.js 的标题处理
    let header = content.querySelector('.section-header');
    if (props.sectionTitle || props.sectionSubtitle) {
        // 需要标题区域
        if (!header) {
            header = document.createElement('div');
            header.className = 'section-header';
            header.innerHTML = `
                <h2 class="section-title">${props.sectionTitle || ''}</h2>
                <p class="section-subtitle">${props.sectionSubtitle || ''}</p>
            `;
            content.insertBefore(header, container);
        }
    } else {
        // 不需要标题区域
        if (header) {
            header.remove();
        }
    }

    // 更新整体包装器样式 - 完全借鉴 stats.js 实力见证模板
    wrapper.style.position = 'relative';
    wrapper.style.padding = '0';  // 移除内边距，让背景图片完全铺开
    wrapper.style.overflow = 'hidden';
    wrapper.style.minHeight = '100vh';  // 确保至少占满视窗高度
    wrapper.style.margin = '0';  // 移除外边距
    wrapper.style.border = 'none';  // 确保没有边框
    
    // 为最新动态模板添加特殊标识
    if (props.isLatestNews) {
        wrapper.setAttribute('data-template', 'news');
    } else {
        wrapper.removeAttribute('data-template');
    }

    // 设置背景 - 完全借鉴 stats.js 的背景处理方式
    const background = component.querySelector('.cards-background');
    const overlay = component.querySelector('.cards-overlay');

    if (background) {
        // 清除之前的样式
        background.style.background = '';
        background.style.backgroundImage = '';
        background.style.opacity = '';

        background.style.position = 'absolute';
        background.style.top = '0';
        background.style.left = '0';
        background.style.width = '100%';
        background.style.height = '100%';
        background.style.zIndex = '1';

        switch (props.sectionBackground) {
            case 'image':
                background.style.backgroundImage = `url('${props.sectionBackgroundImage}')`;

                // 针对不同背景图片应用特殊样式
                if (props.sectionBackgroundImage === '/assets/images/ceo-apply-bg1.png') {
                    // 图2：新闻动态背景图片
                    background.style.setProperty('background-size', 'contain', 'important');
                    background.style.setProperty('min-height', '700px', 'important');
                    background.style.setProperty('background-position', 'center top', 'important');
                    background.style.setProperty('background-repeat', 'no-repeat', 'important');
                } else if (props.sectionBackgroundImage === '/assets/images/ceo-home-vip-bg.png') {
                    // 图1：为什么选择我们背景图片，确保全屏铺开
                    background.style.setProperty('background-size', 'cover', 'important');
                    background.style.setProperty('background-position', 'center center', 'important');
                    background.style.setProperty('background-repeat', 'no-repeat', 'important');
                    background.style.setProperty('min-height', '100vh', 'important');
                    background.style.setProperty('width', '100%', 'important');
                } else if (props.sectionBackgroundImage === '/assets/images/bg1.jpg') {
                    // 图3：深色背景图片，确保全屏铺开
                    background.style.setProperty('background-size', 'cover', 'important');
                    background.style.setProperty('background-position', 'center center', 'important');
                    background.style.setProperty('background-repeat', 'no-repeat', 'important');
                    background.style.setProperty('min-height', '100vh', 'important');
                    background.style.setProperty('width', '100%', 'important');
                } else {
                    // 其他图片使用默认样式
                    background.style.backgroundSize = 'cover';
                    background.style.backgroundPosition = 'center';
                }

                background.style.backgroundRepeat = 'no-repeat';
                background.style.opacity = '1';
                break;
            case 'gradient':
                background.style.background = props.sectionBackgroundGradient || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                background.style.opacity = '1';
                break;
            case 'color':
                background.style.background = props.sectionBackgroundColor;
                background.style.opacity = '1';
                break;
        }
    }

    // 设置叠加层 - 借鉴 stats.js 的叠加层处理
    if (overlay) {
        overlay.style.position = 'absolute';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.width = '100%';
        overlay.style.height = '100%';
        overlay.style.zIndex = '2';

        // 所有背景图片都不加遮罩层，让图片更清晰
        overlay.style.background = 'transparent';
    }

    // 显示并更新标题区域 - 借鉴 stats.js 的标题样式处理
    if (header) {
        header.style.display = 'block';
        header.style.textAlign = 'center';
        header.style.marginBottom = '4rem';
        header.style.position = 'relative';
        header.style.zIndex = '3';

        // 针对新闻动态背景图片增加上边距，让标题和内容向下移动
        if (props.sectionBackground === 'image' && props.sectionBackgroundImage === '/assets/images/ceo-apply-bg1.png') {
            header.style.marginTop = '8rem';
            header.style.paddingTop = '2rem';
        } else {
            header.style.marginTop = '0';
            header.style.paddingTop = '0';
        }
    }

    // 更新标题样式 - 借鉴 stats.js 的标题颜色处理
    const title = component.querySelector('.section-title');
    const subtitle = component.querySelector('.section-subtitle');

    if (title) {
        title.textContent = props.sectionTitle || '';
        title.style.fontSize = '3rem';
        title.style.fontWeight = '700';
        title.style.marginBottom = '1rem';
        title.style.display = 'block';

        // 为最新动态模板和为什么选择我们模板添加特殊的外发光效果
        if (props.isLatestNews || props.isWhyChooseUs) {
            // 根据背景图片判断文字颜色：图1和图2用黑色，图3用白色
            const isVeryDarkBackground = props.sectionBackgroundImage === '/assets/images/bg1.jpg';

            if (isVeryDarkBackground) {
                // 图3（bg1.jpg）使用纯白色文字，不要光影
                title.style.color = '#ffffff';
                title.style.textShadow = 'none';
            } else if (props.sectionBackgroundImage === '/assets/images/ceo-apply-bg1.png') {
                // 图2（ceo-apply-bg1.png）使用#2c3e50颜色，要白色光影
                title.style.color = '#2c3e50';
                title.style.textShadow = `
                    0 0 10px rgba(255, 255, 255, 0.8),
                    0 0 20px rgba(255, 255, 255, 0.6),
                    0 0 30px rgba(255, 255, 255, 0.4),
                    0 2px 4px rgba(255, 255, 255, 0.2)
                `;
            } else {
                // 图1（ceo-home-vip-bg.png）使用#37474f颜色，不要光影
                title.style.color = '#37474f';
                title.style.textShadow = 'none';
            }

            title.style.letterSpacing = '2px';
            title.style.position = 'relative';

            // 针对ceo-apply-bg1.png背景图片添加分割线，与文字颜色一致
            if (props.sectionBackgroundImage === '/assets/images/ceo-apply-bg1.png') {
                title.style.paddingBottom = '1.5rem';
                title.style.marginBottom = '2rem';
                title.style.borderImage = 'none';
            } else {
                // 清除边框样式
                title.style.paddingBottom = '1.5rem';
                title.style.marginBottom = '2rem';
                title.style.borderBottom = 'none';
                title.style.borderImage = 'none';
            }

        } else {
            // 根据背景图片判断文字颜色
            if (props.sectionBackground === 'image') {
                // 检查是否为深色背景图（背景图2和3）
                const isDarkBackground = props.sectionBackgroundImage === '/assets/images/ceo-apply-bg1.png' ||
                                        props.sectionBackgroundImage === '/assets/images/bg1.jpg';

                if (isDarkBackground) {
                    // 深色背景图使用白色文字
                    title.style.color = 'block';
                    title.style.textShadow = '0 4px 20px rgba(0, 0, 0, 0.3)';

                    // 针对ceo-apply-bg1.png背景图片添加白色分割线，与文字颜色一致
                    if (props.sectionBackgroundImage === '/assets/images/ceo-apply-bg1.png') {
                        title.style.paddingBottom = '1rem';
                        title.style.marginBottom = '2rem';
                    } else {
                        title.style.borderBottom = 'none';
                    }
                } else {
                    // 浅色背景图使用深色文字
                    title.style.color = '#37474f';
                    title.style.textShadow = '0 2px 8px rgba(55, 71, 79, 0.3)';
                    title.style.borderBottom = 'none';
                }
            } else {
                title.style.color = 'white';
                title.style.textShadow = '0 4px 20px rgba(0, 0, 0, 0.3)';
                title.style.borderBottom = 'none';
            }
        }
    }
    if (subtitle) {
        subtitle.textContent = props.sectionSubtitle || '';
        subtitle.style.fontSize = '1.2rem';
        subtitle.style.marginBottom = '2rem';
        subtitle.style.display = 'block';

        // 为最新动态模板和为什么选择我们模板添加特殊的外发光效果
        if (props.isLatestNews || props.isWhyChooseUs) {
            // 根据背景图片判断文字颜色：图1和图2用黑色，图3用白色
            const isVeryDarkBackground = props.sectionBackgroundImage === '/assets/images/bg1.jpg';

            if (isVeryDarkBackground) {
                // 图3（bg1.jpg）使用纯白色文字，不要光影
                subtitle.style.color = 'rgba(255, 255, 255, 0.9)';
                subtitle.style.textShadow = 'none';
            } else if (props.sectionBackgroundImage === '/assets/images/ceo-apply-bg1.png') {
                // 图2（ceo-apply-bg1.png）使用#2c3e50颜色（与主标题相同），要白色光影
                subtitle.style.color = '#2c3e50';
                subtitle.style.textShadow = `
                    0 0 8px rgba(255, 255, 255, 0.6),
                    0 0 16px rgba(255, 255, 255, 0.4),
                    0 0 24px rgba(255, 255, 255, 0.2),
                    0 1px 3px rgba(255, 255, 255, 0.1)
                `;
            } else {
                // 图1（ceo-home-vip-bg.png）使用#546e7a颜色，不要光影
                subtitle.style.color = '#546e7a';
                subtitle.style.textShadow = 'none';
            }

            subtitle.style.letterSpacing = '1px';
            subtitle.style.fontWeight = '400';
        } else {
            // 根据背景图片判断文字颜色
            if (props.sectionBackground === 'image') {
                // 检查是否为深色背景图（背景图2和3）
                const isDarkBackground = props.sectionBackgroundImage === '/assets/images/ceo-apply-bg1.png' ||
                                        props.sectionBackgroundImage === '/assets/images/bg1.jpg';

                if (isDarkBackground) {
                    // 深色背景图使用白色文字
                    subtitle.style.color = 'rgba(255, 255, 255, 0.8)';
                } else {
                    // 浅色背景图使用深色文字
                    subtitle.style.color = '#546e7a';
                }
            } else {
                subtitle.style.color = 'rgba(255, 255, 255, 0.8)';
            }
        }
    }

    // 设置内容区域 - 借鉴 stats.js 的内容区域处理
    if (content) {
        content.style.position = 'relative';
        content.style.zIndex = '3';
        content.style.textAlign = 'center';
        content.style.padding = '100px 20px';  // 给内容区域添加内边距，而不是给wrapper
        content.style.minHeight = '100vh';  // 确保内容区域至少占满视窗高度
    }

    // 设置容器样式
    if (container) {
        container.style.display = 'grid';
        container.style.gridTemplateColumns = `repeat(${props.columnsCount}, 1fr)`;
        container.style.gap = `${props.cardSpacing}px`;
        container.style.maxWidth = `${props.maxWidth}px`;
        container.style.margin = '0 auto';

        // 生成卡片HTML - 为最新动态模板使用特殊布局
        if (props.isLatestNews) {
            container.innerHTML = props.cards.map((card) => `
                <div class="news-card" style="
                    background: rgba(255, 255, 255, 0.97);
                    border-radius: 25px;
                    overflow: hidden;
                    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.2);
                    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                    height: 100%;
                    position: relative;
                    border: 3px solid rgba(255, 255, 255, 0.95);
                    backdrop-filter: blur(15px);
                    z-index: 2;
                    display: flex;
                    flex-direction: column;
                    width: 100%;
                    max-width: 380px;
                    margin: 0 auto;
                " onmouseover="
                    this.style.transform='translateY(-15px) scale(1.03)';
                    this.style.boxShadow='0 30px 80px rgba(0, 0, 0, 0.35)';
                    this.style.border='3px solid rgba(0, 123, 255, 0.6)';
                    this.style.background='rgba(255, 255, 255, 0.99)';
                    this.querySelector('.news-image img').style.transform='scale(1.08)';
                    this.querySelector('.news-image img').style.filter='brightness(1.1) contrast(1.15) saturate(1.1)';
                    this.querySelector('.news-overlay').style.opacity='1';
                " onmouseout="
                    this.style.transform='translateY(0) scale(1)';
                    this.style.boxShadow='0 15px 50px rgba(0, 0, 0, 0.2)';
                    this.style.border='3px solid rgba(255, 255, 255, 0.95)';
                    this.style.background='rgba(255, 255, 255, 0.97)';
                    this.querySelector('.news-image img').style.transform='scale(1)';
                    this.querySelector('.news-image img').style.filter='brightness(1.05) contrast(1.1) saturate(1.05)';
                    this.querySelector('.news-overlay').style.opacity='0';
                ">
                    <div class="news-image" style="
                        position: relative;
                        overflow: hidden;
                        height: 240px;
                        border-radius: 15px 15px 0 0;
                    ">
                        <img src="${card.imageUrl || cardImageLibrary[card.selectedImageIndex]}"
                             alt="${card.title}" style="
                             width: 100%;
                             height: 100%;
                             object-fit: cover;
                             transition: all 0.4s ease;
                             filter: brightness(1.05) contrast(1.1) saturate(1.05);
                             image-rendering: -webkit-optimize-contrast;
                             image-rendering: crisp-edges;
                             image-rendering: optimizeQuality;
                        ">
                        <div class="news-overlay" style="
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            background: linear-gradient(45deg, rgba(0, 123, 255, 0.8) 0%, rgba(0, 86, 179, 0.8) 100%);
                            opacity: 0;
                            transition: opacity 0.3s ease;
                            display: flex;
                            align-items: flex-end;
                            padding: 20px;
                        "></div>
                        <div class="news-date" style="
                            background: rgba(255, 255, 255, 0.95);
                            border-radius: 8px;
                            padding: 10px 15px;
                            text-align: center;
                            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                            position: absolute;
                            top: 15px;
                            right: 15px;
                        ">
                            <span class="day" style="
                                display: block;
                                font-size: 1.5rem;
                                font-weight: 700;
                                color: #007bff;
                                line-height: 1;
                            ">${card.day || '04'}</span>
                            <span class="month" style="
                                display: block;
                                font-size: 0.8rem;
                                color: #6c757d;
                                text-transform: uppercase;
                                margin-top: 2px;
                            ">${card.month || 'Jun'}</span>
                        </div>
                    </div>
                    <div class="news-content" style="
                        padding: 30px;
                        position: relative;
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                    ">
                        <div class="news-meta" style="
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 18px;
                            flex-wrap: wrap;
                            gap: 12px;
                        ">
                            <span class="news-category" style="
                                background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
                                color: #1976d2;
                                padding: 6px 12px;
                                border-radius: 20px;
                                font-weight: 600;
                                box-shadow: 0 2px 8px rgba(25, 118, 210, 0.15);
                                transition: all 0.3s ease;
                                font-size: 0.85rem;
                                display: flex;
                                align-items: center;
                                gap: 6px;
                            " onmouseover="
                                this.style.transform='translateY(-1px)';
                                this.style.boxShadow='0 4px 12px rgba(25, 118, 210, 0.25)';
                            " onmouseout="
                                this.style.transform='translateY(0)';
                                this.style.boxShadow='0 2px 8px rgba(25, 118, 210, 0.15)';
                            ">
                                <i class="fa fa-tag"></i>
                                ${card.category || '公司动态'}
                            </span>
                            <span class="news-time" style="
                                font-size: 0.85rem;
                                color: #6c757d;
                                display: flex;
                                align-items: center;
                                gap: 6px;
                            ">
                                <i class="fa fa-clock-o"></i>
                                ${card.date || '2025-06-04'}
                            </span>
                        </div>
                        <h5 class="news-title" style="
                            margin-bottom: 18px;
                            font-size: ${props.titleSize}px;
                        ">
                            <a href="${card.buttonLink}" style="
                                color: #2c3e50;
                                text-decoration: none;
                                font-weight: 700;
                                font-size: 1.2rem;
                                line-height: 1.4;
                                transition: all 0.3s ease;
                                display: block;
                            " onmouseover="
                                this.style.color='#007bff';
                                this.style.transform='translateX(3px)';
                            " onmouseout="
                                this.style.color='#2c3e50';
                                this.style.transform='translateX(0)';
                            ">${card.title}</a>
                        </h5>
                        <p class="news-excerpt" style="
                            color: #6c757d;
                            font-size: 1rem;
                            line-height: 1.7;
                            margin-bottom: 25px;
                            display: -webkit-box;
                            -webkit-line-clamp: 3;
                            -webkit-box-orient: vertical;
                            overflow: hidden;
                            text-align: justify;
                            flex: 1;
                        ">
                            ${card.excerpt || card.content}
                        </p>
                        <div class="news-footer" style="
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            padding-top: 20px;
                            border-top: 2px solid #f1f3f4;
                            margin-top: auto;
                        ">
                            <a href="${card.buttonLink}" class="read-more-btn" style="
                                color: #007bff;
                                text-decoration: none;
                                font-weight: 600;
                                font-size: 0.95rem;
                                transition: all 0.3s ease;
                                display: flex;
                                align-items: center;
                                gap: 6px;
                                padding: 8px 16px;
                                border-radius: 25px;
                                background: rgba(0, 123, 255, 0.1);
                                border: 1px solid rgba(0, 123, 255, 0.2);
                            " onmouseover="
                                this.style.color='white';
                                this.style.background='#007bff';
                                this.style.borderColor='#007bff';
                                this.style.textDecoration='none';
                                this.style.transform='translateX(5px)';
                                this.style.boxShadow='0 4px 15px rgba(0, 123, 255, 0.3)';
                            " onmouseout="
                                this.style.color='#007bff';
                                this.style.background='rgba(0, 123, 255, 0.1)';
                                this.style.borderColor='rgba(0, 123, 255, 0.2)';
                                this.style.textDecoration='none';
                                this.style.transform='translateX(0)';
                                this.style.boxShadow='none';
                            ">
                                ${card.buttonText} <i class="fa fa-arrow-right"></i>
                            </a>
                            <div class="news-stats" style="
                                color: #6c757d;
                                font-size: 0.85rem;
                                display: flex;
                                align-items: center;
                                gap: 5px;
                            ">
                                <span><i class="fa fa-eye"></i> ${card.views || '0'}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            // 添加底部"查看更多"按钮
            if (props.showMoreButton) {
                container.innerHTML += `
                    <div class="text-center mt-5" style="grid-column: 1 / -1; margin-top: 3rem;">
                        <a href="${props.moreButtonLink}" class="btn btn-primary btn-lg news-more-btn" style="
                            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
                            border: none !important;
                            color: white !important;
                            padding: 15px 40px !important;
                            font-size: 1.1rem !important;
                            font-weight: 600 !important;
                            border-radius: 50px !important;
                            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3) !important;
                            transition: all 0.3s ease !important;
                            position: relative !important;
                            overflow: hidden !important;
                            text-decoration: none !important;
                            display: inline-flex !important;
                            align-items: center !important;
                            gap: 10px !important;
                            z-index: 1 !important;
                        " onmouseover="
                            this.style.background='linear-gradient(135deg, #0056b3 0%, #004085 100%) !important';
                            this.style.transform='translateY(-2px)';
                            this.style.boxShadow='0 12px 35px rgba(0, 123, 255, 0.4) !important';
                        " onmouseout="
                            this.style.background='linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important';
                            this.style.transform='translateY(0)';
                            this.style.boxShadow='0 8px 25px rgba(0, 123, 255, 0.3) !important';
                        ">
                            <span style="font-size: 18px;">${props.moreButtonIcon}</span> ${props.moreButtonText}
                        </a>
                    </div>
                `;
            }
        } else {
            // 原有的卡片布局
            container.innerHTML = props.cards.map((card) => `
                        <div class="card-item layout-${props.layoutMode}" style="
                            background: ${props.bgColor};
                            color: ${props.textColor};
                            border-radius: ${props.borderRadius}px;
                            padding: ${props.padding}px;
                            text-align: ${props.textAlign};
                            box-shadow: ${props.shadow ? `0 8px 32px rgba(102, 126, 234, 0.12), 0 2px 8px rgba(0, 0, 0, 0.04)` : 'none'};
                            transition: all 0.3s ease;
                            border: 1px solid #e2e8f0;
                            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
                        ">
                            ${props.layoutMode.includes('image') && card.showImage ? `
                                <div class="card-image-container" style="margin-bottom: 20px;">
                                    <img class="card-image" src="${card.imageUrl || cardImageLibrary[card.selectedImageIndex]}"
                                         alt="卡片图片" style="width: 100%; height: ${props.imageHeight}px; object-fit: cover; border-radius: 8px;">
                                </div>
                            ` : ''}

                            ${props.layoutMode === 'icon-top' && card.showIcon ? `
                                <div class="card-icon-container" style="text-align: center; margin-bottom: 20px;">
                                    <span class="card-icon" style="
                                        font-size: ${props.iconSize}px;
                                        color: ${props.iconColor};
                                        display: inline-flex;
                                        align-items: center;
                                        justify-content: center;
                                        width: 80px;
                                        height: 80px;
                                        background: linear-gradient(135deg, #667eea20 0%, #764ba220 100%);
                                        border-radius: 50%;
                                        margin-bottom: 10px;
                                    ">${card.iconText}</span>
                                </div>
                            ` : ''}

                            <div class="card-content">
                                <h3 class="card-title" style="
                                    color: ${props.titleColor};
                                    font-size: ${props.titleSize}px;
                                    font-weight: 600;
                                    margin-bottom: 15px;
                                    line-height: 1.3;
                                ">${card.title}</h3>

                                <p class="card-text" style="
                                    color: ${props.textColor};
                                    font-size: ${props.contentSize}px;
                                    line-height: 1.6;
                                    margin-bottom: ${card.showButton ? '20px' : '0'};
                                ">${card.content}</p>

                                ${card.showButton ? `
                                    <button class="card-btn" style="
                                        background: ${props.buttonColor};
                                        color: ${props.buttonTextColor};
                                        border: none;
                                        padding: 12px 24px;
                                        border-radius: 6px;
                                        font-size: ${props.buttonSize}px;
                                        font-weight: 500;
                                        cursor: pointer;
                                        transition: all 0.3s ease;
                                        position: relative;
                                        overflow: hidden;
                                    " onclick="handleCardButtonClick('${card.buttonLink}')"
                                       onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 12px rgba(102, 126, 234, 0.3)'"
                                       onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                        ${card.buttonText}
                                    </button>
                                ` : ''}
                            </div>
                        </div>
                    `).join('');
        }

        // 添加悬停效果
        if (props.isLatestNews) {
            // 新闻卡片悬停效果已在CSS中处理
            console.log('新闻卡片样式已应用');
        } else {
            // 原有卡片悬停效果
            const cardItems = container.querySelectorAll('.card-item');
            cardItems.forEach(cardItem => {
                cardItem.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                    this.style.boxShadow = '0 12px 40px rgba(102, 126, 234, 0.18), 0 4px 12px rgba(0, 0, 0, 0.08)';
                });

                cardItem.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 8px 32px rgba(102, 126, 234, 0.12), 0 2px 8px rgba(0, 0, 0, 0.04)';
                });
            });
        }
    }
}

// 更新渐变背景
function updateCardGradient(componentId, gradient) {
    const component = document.getElementById(componentId);
    if (!component) return;

    // 获取组件的属性数据
    let props;
    if (component._cardProperties) {
        props = component._cardProperties;
    } else {
        props = JSON.parse(JSON.stringify(cardComponent.properties));
        component._cardProperties = props;
    }

    props.sectionBackgroundGradient = gradient;
    updateCardDisplay(component, props);
    updatePropertiesPanel(component);
}

// 数据源处理函数
function updateCardDataSource(componentId, dataSource) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props = component._cardProperties || JSON.parse(JSON.stringify(cardComponent.properties));
    component._cardProperties = props;

    props.dataSource = dataSource;

    if (dataSource !== 'static') {
        // 切换到动态数据源，自动切换到系统风格
        props.styleMode = 'system';
        fetchCardData(component, props);
    } else {
        // 切换回静态数据，保留当前布局设置并切换到自定义风格
        const defaultProps = JSON.parse(JSON.stringify(cardComponent.properties));

        // 保留当前的重要设置
        const currentColumnsCount = props.columnsCount;
        const currentLayoutMode = props.layoutMode;
        const currentCardSpacing = props.cardSpacing;

        // 只恢复卡片数据和数据源，保持其他设置
        props.dataSource = 'static';
        props.styleMode = 'custom'; // 静态数据使用自定义风格
        props.cards = defaultProps.cards;

        // 恢复保留的设置
        props.columnsCount = currentColumnsCount;
        props.layoutMode = currentLayoutMode;
        props.cardSpacing = currentCardSpacing;

        // 根据列数生成对应数量的卡片
        if (props.cards.length < props.columnsCount) {
            while (props.cards.length < props.columnsCount) {
                const newCard = JSON.parse(JSON.stringify(defaultProps.cards[0]));
                newCard.title = `卡片标题${props.cards.length + 1}`;
                newCard.content = `这里是第${props.cards.length + 1}个卡片的内容，可以放置图片、文字等信息。`;
                props.cards.push(newCard);
            }
        }

        updateCardDisplay(component, props);
    }

    updatePropertiesPanel(component);
}

function refreshCardData(componentId) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props = component._cardProperties;
    if (!props || props.dataSource === 'static') return;

    fetchCardData(component, props);
}

function fetchCardData(component, props) {
    const apiUrl = `/api/pages/getDiyData?type=${props.dataSource}&limit=${props.dataLimit}`;

    // 显示加载状态
    const container = component.querySelector('.cards-container');
    if (container) {
        container.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;">正在加载数据...</div>';
    }

    fetch(apiUrl)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(result => {
            console.log('API返回数据:', result);

            if (result.success && result.data && Array.isArray(result.data)) {
                // 将API数据转换为卡片数据格式
                const cardData = result.data.map(item => {
                    // 安全地获取字段值
                    const title = item.title || item.name || '标题';
                    // 优先使用摘要，如果没有摘要则使用截取的内容
                    const content = item.excerpt || item.summary || item.content || item.description || '内容描述';
                    const image = item.image || '';
                    const id = item.id || 1;

                    return {
                        title: title,
                        content: content,
                        imageUrl: image,
                        buttonLink: `/news/${id}`,
                        showImage: !!image,
                        showButton: true,
                        buttonText: '查看详情',
                        selectedImageIndex: 0,
                        showIcon: false,
                        iconText: '📰'
                    };
                });

                if (cardData.length === 0) {
                    // 如果没有数据，显示提示
                    if (container) {
                        container.innerHTML = `
                            <div style="text-align: center; padding: 40px; color: #666;">
                                <div>暂无${props.dataSource}数据</div>
                                <button onclick="refreshCardData('${component.id}')"
                                        style="margin-top: 12px; padding: 8px 16px; background: #667eea; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                    重新加载
                                </button>
                            </div>
                        `;
                    }
                    return;
                }

                // 更新组件属性中的卡片数据
                props.cards = cardData;

                // 重新渲染组件
                updateCardDisplay(component, props);

                console.log(`✅ 成功加载 ${cardData.length} 条${props.dataSource}数据`);
            } else {
                throw new Error(result.message || '数据格式错误');
            }
        })
        .catch(error => {
            console.error('获取数据失败:', error);
            // 显示错误状态
            if (container) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #e53e3e;">
                        <div>数据加载失败</div>
                        <div style="font-size: 12px; margin-top: 8px;">${error.message}</div>
                        <button onclick="refreshCardData('${component.id}')"
                                style="margin-top: 12px; padding: 8px 16px; background: #667eea; color: white; border: none; border-radius: 4px; cursor: pointer;">
                            重试
                        </button>
                    </div>
                `;
            }
        });
}

// 注册卡片组件
if (typeof ComponentManager !== 'undefined') {
    ComponentManager.register('card', cardComponent, generateCardProperties, updateCardDisplay);
}

<?php
/**
 * 图片上传安全配置
 */

return [
    // 文件类型限制
    'allowed_types' => [
        'image/jpeg',
        'image/png'
    ],
    
    // 文件扩展名限制
    'allowed_extensions' => [
        'jpg',
        'jpeg', 
        'png'
    ],
    
    // 文件大小限制
    'max_size' => 5 * 1024 * 1024, // 5MB
    'min_size' => 1024, // 1KB
    
    // 图片尺寸限制
    'max_width' => 4000,
    'max_height' => 4000,
    'min_width' => 50,
    'min_height' => 50,
    
    // 文件名限制
    'max_filename_length' => 255,
    'forbidden_chars' => ['..', '/', '\\', '<', '>', '|', ':', '*', '?', '"'],
    
    // 上传限制
    'max_files_per_request' => 10,
    'max_uploads_per_hour' => 100, // 每小时最大上传次数
    'max_uploads_per_day' => 500,  // 每天最大上传次数
    
    // 存储配置
    'storage_path' => 'uploads/images',
    'url_prefix' => '/uploads/images',
    
    // 安全配置
    'enable_csrf_check' => true,
    'enable_referer_check' => true,
    'enable_ip_whitelist' => false,
    'ip_whitelist' => [],
    
    // 文件头验证（魔数检查）
    'magic_numbers' => [
        'jpeg' => [
            "\xFF\xD8\xFF\xE0", // JFIF
            "\xFF\xD8\xFF\xE1", // EXIF
            "\xFF\xD8\xFF\xDB", // JPEG
        ],
        'png' => [
            "\x89\x50\x4E\x47\x0D\x0A\x1A\x0A", // PNG
        ]
    ],
    
    // 日志配置
    'enable_upload_log' => true,
    'enable_error_log' => true,
    'log_retention_days' => 30,
    
    // 缩略图配置
    'enable_thumbnail' => false,
    'thumbnail_sizes' => [
        'small' => [150, 150],
        'medium' => [300, 300],
        'large' => [800, 600]
    ],
    
    // 水印配置
    'enable_watermark' => false,
    'watermark_image' => '',
    'watermark_position' => 'bottom-right',
    'watermark_opacity' => 50,
]; 
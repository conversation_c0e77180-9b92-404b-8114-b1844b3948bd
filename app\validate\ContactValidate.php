<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * 联系表单验证器 - ThinkPHP6企业级应用
 * 功能：联系表单数据验证和安全过滤
 */

namespace app\validate;

class ContactValidate extends BaseValidate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'name' => 'require|length:1,100|checkSql',
        'email' => 'require|email|length:1,100|checkSql',
        'phone' => 'length:0,20|checkSql',
        'company' => 'length:0,100|checkSql',
        'subject' => 'require|length:1,200|checkSql',
        'message' => 'require|length:1,1000|checkSql',
        'status' => 'in:new,read,replied,closed',
        'admin_reply' => 'length:0,1000|checkSql',
    ];
    
    /**
     * 验证消息
     */
    protected $message = [
        'name.require' => '姓名不能为空',
        'name.length' => '姓名长度不能超过100个字符',
        'name.checkSql' => '姓名包含非法字符',
        'email.require' => '邮箱不能为空',
        'email.email' => '邮箱格式不正确',
        'email.length' => '邮箱长度不能超过100个字符',
        'email.checkSql' => '邮箱包含非法字符',
        'phone.length' => '电话长度不能超过20个字符',
        'phone.checkSql' => '电话包含非法字符',
        'company.length' => '公司名称长度不能超过100个字符',
        'company.checkSql' => '公司名称包含非法字符',
        'subject.require' => '主题不能为空',
        'subject.length' => '主题长度不能超过200个字符',
        'subject.checkSql' => '主题包含非法字符',
        'message.require' => '留言内容不能为空',
        'message.length' => '留言内容长度不能超过1000个字符',
        'message.checkSql' => '留言内容包含非法字符',
        'status.in' => '状态值无效',
        'admin_reply.length' => '管理员回复长度不能超过1000个字符',
        'admin_reply.checkSql' => '管理员回复包含非法字符',
    ];
    
    /**
     * 验证场景
     */
    protected $scene = [
        'submit' => ['name', 'email', 'phone', 'company', 'subject', 'message'],
        'admin_reply' => ['status', 'admin_reply'],
    ];
    
    /**
     * 自定义验证规则：SQL注入检查
     */
    protected function checkSql($value, $rule, $data = [])
    {
        if (!$this->checkSqlInjection($value)) {
            return '输入内容包含非法字符';
        }
        return true;
    }
    
    /**
     * 验证搜索参数
     */
    public function validateSearch($data)
    {
        $rules = [
            'search' => 'length:0,100|checkSql',
            'status' => 'in:new,read,replied,closed',
        ];
        
        $messages = [
            'search.length' => '搜索关键词长度不能超过100个字符',
            'search.checkSql' => '搜索关键词包含非法字符',
            'status.in' => '状态值无效',
        ];
        
        $validate = new self();
        $validate->rule($rules)->message($messages);
        
        if (!$validate->check($data)) {
            return $validate->getError();
        }
        
        return true;
    }
}

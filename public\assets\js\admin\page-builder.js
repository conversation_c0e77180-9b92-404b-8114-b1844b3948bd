/**
 * 页面装修模块JavaScript - 列表管理功能
 */

/**
 * 切换模板状态
 */
function toggleStatus(id, status) {
    fetch('/admin/page-builder', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            action: 'toggle_status',
            id: id,
            status: status
        })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showMessage('状态更新成功', 'success');
            // 延迟1秒后无痕刷新页面，让用户看到成功提示
            setTimeout(() => {
                window.location.reload();
            }, 500);
        } else {
            showMessage('状态更新失败: ' + result.message, 'error');
            // 恢复开关状态
            const checkbox = document.querySelector(`input[onchange*="${id}"]`);
            if (checkbox) {
                checkbox.checked = !status;
            }
        }
    })
    .catch(error => {
        console.error('状态更新失败:', error);
        showMessage('状态更新失败', 'error');
        // 恢复开关状态
        const checkbox = document.querySelector(`input[onchange*="${id}"]`);
        if (checkbox) {
            checkbox.checked = !status;
        }
    });
}

// 删除模板功能已移至admin.js统一管理

/**
 * 显示消息提示
 */
function showMessage(message, type = 'info') {
    // 移除现有的消息提示
    const existingToast = document.querySelector('.message-toast');
    if (existingToast) {
        existingToast.remove();
    }

    // 创建新的消息提示
    const toast = document.createElement('div');
    toast.className = `message-toast ${type}`;
    toast.textContent = message;

    // 添加样式
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        color: #fff;
        font-weight: 600;
        z-index: 10000;
        animation: slideIn 0.3s ease;
    `;

    // 根据类型设置背景色
    if (type === 'success') {
        toast.style.background = 'linear-gradient(135deg, rgba(34, 197, 94, 0.9) 0%, rgba(22, 163, 74, 0.9) 100%)';
    } else if (type === 'error') {
        toast.style.background = 'linear-gradient(135deg, rgba(239, 68, 68, 0.9) 0%, rgba(220, 38, 38, 0.9) 100%)';
    } else {
        toast.style.background = 'linear-gradient(135deg, rgba(59, 130, 246, 0.9) 0%, rgba(37, 99, 235, 0.9) 100%)';
    }

    document.body.appendChild(toast);

    // 3秒后自动移除
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// 页面加载完成后绑定事件
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面装修模块已加载');

    // 绑定表单提交事件
    const forms = document.querySelectorAll('.template-form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const nameInput = form.querySelector('input[name="name"]');
            if (nameInput && !nameInput.value.trim()) {
                e.preventDefault();
                showMessage('请输入模板名称', 'error');
                nameInput.focus();
                return false;
            }
        });
    });
});

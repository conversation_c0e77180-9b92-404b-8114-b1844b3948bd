<?php /*a:4:{s:57:"D:\EServer\core\www\san.com\app\view\solutions\index.html";i:1749769968;s:55:"D:\EServer\core\www\san.com\app\view\common\header.html";i:1749620980;s:55:"D:\EServer\core\www\san.com\app\view\common\styles.html";i:1748797665;s:55:"D:\EServer\core\www\san.com\app\view\common\footer.html";i:1749617057;}*/ ?>
<?php $pageTitle = $pageData['pageTitle']; ?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <title><?php echo htmlentities((string) (isset($pageTitle) && ($pageTitle !== '')?$pageTitle:'首页')); ?> - <?php echo htmlentities((string) (isset($siteConfig['site_name']) && ($siteConfig['site_name'] !== '')?$siteConfig['site_name']:'三只鱼网络')); ?></title>
    <meta name="description" content="<?php echo htmlentities((string) ((isset($pageDescription) && ($pageDescription !== '')?$pageDescription:$siteConfig['site_description']) ?: '专注于为企业提供专业、可靠、高效的数字化转型解决方案')); ?>">
    <meta name="keywords" content="<?php echo htmlentities((string) ((isset($pageKeywords) && ($pageKeywords !== '')?$pageKeywords:$siteConfig['site_keywords']) ?: '数字化转型,企业解决方案,技术服务,创新科技,专业团队')); ?>">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?php echo htmlentities((string) ((isset($pageTitle) && ($pageTitle !== '')?$pageTitle:$siteConfig['site_name']) ?: '三只鱼网络')); ?>">
    <meta property="og:description" content="<?php echo htmlentities((string) ((isset($pageDescription) && ($pageDescription !== '')?$pageDescription:$siteConfig['site_description']) ?: '专注于为企业提供专业、可靠、高效的数字化转型解决方案')); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo htmlentities((string) app('request')->domain()); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo asset('assets/images/favicon.ico'); ?>">
    
    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo asset('assets/css/bootstrap.min.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset('assets/css/all.min.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset('assets/css/animate.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset('assets/css/swiper-bundle.min.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset('assets/css/style.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset('assets/css/contact.css'); ?>">

    <!-- Lucide Icons - 本地版本 -->
    <script src="<?php echo asset('assets/js/lucide.js'); ?>"></script>
    
    <!-- 导航菜单层级修复 -->
    <link rel="stylesheet" href="<?php echo asset('assets/css/nav-fix.css'); ?>">

    <!-- 导航栏样式 -->
    <style>
    /* ===== 下拉菜单基础样式 ===== */
    .dropdown-menu {
        background: #ffffff;
        border: none;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        border-radius: 12px;
        padding: 12px 0;
    }

    .dropdown-item {
        padding: 10px 20px;
        font-size: 14px;
        transition: all 0.2s ease;
        border: none;
        background: transparent;
    }

    .dropdown-item:hover {
        background: rgba(0, 123, 255, 0.08);
        color: #007bff;
    }

    /* ===== 大型下拉菜单容器 ===== */
    .mega-dropdown {
        position: relative;
    }

    .mega-dropdown .dropdown-menu {
        display: none;
        position: fixed;
        top: 80px;
        left: 50%;
        transform: translateX(-50%);
        margin: 0;
        border-radius: 16px;
        background: white;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        border: none;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        pointer-events: none;
        width: 1200px;
        max-width: 90vw;
        max-height: 80vh;
        overflow-y: auto;
    }

    /* 下拉菜单显示状态 - 通过JavaScript控制 */
    .mega-dropdown.show .dropdown-menu {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: auto !important;
    }

    /* 确保菜单内容有正确的显示状态 */
    .dropdown-menu {
        display: none;
        opacity: 0;
        visibility: hidden;
        pointer-events: none;
    }

    /* 当菜单被JavaScript激活时的样式 */
    .dropdown-menu.show,
    .dropdown:hover .dropdown-menu {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: auto !important;
    }

    /* 增加菜单和触发器之间的连接区域 */
    .mega-dropdown::before {
        content: '';
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        height: 20px;
        background: transparent;
        z-index: 9999;
        pointer-events: auto;
    }

    /* ===== 简单可靠的三角形指示器 ===== */
    .mega-dropdown .dropdown-menu::before {
        content: '';
        position: absolute;
        top: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-bottom: 8px solid white;
        z-index: 1001;
    }

    /* 三角形阴影 */
    .mega-dropdown .dropdown-menu::after {
        content: '';
        position: absolute;
        top: -9px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 9px solid transparent;
        border-right: 9px solid transparent;
        border-bottom: 9px solid rgba(0, 0, 0, 0.1);
        z-index: 1000;
    }

    /* ===== 下拉箭头指示器 ===== */
    .dropdown-toggle::after {
        display: inline-block;
        margin-left: 6px;
        vertical-align: middle;
        content: "▼";
        font-size: 10px;
        transition: transform 0.3s ease;
        line-height: 1;
    }

    .dropdown-toggle[aria-expanded="true"]::after,
    .mega-dropdown.show .dropdown-toggle::after {
        transform: rotate(180deg);
    }

    /* 解决方案菜单特殊样式 */
    .solutions-menu {
        padding: 0;
    }

    .solutions-menu .mega-menu-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 25px 30px;
        text-align: center;
        color: white;
        border-radius: 16px 16px 0 0;
    }

    .solutions-menu .mega-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 8px;
        color: white;
    }

    .solutions-menu .mega-subtitle {
        font-size: 0.9rem;
        opacity: 0.9;
        margin: 0;
        color: white;
    }

    /* 解决方案网格布局 */
    .solutions-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
        padding: 30px;
        background: white;
    }

    .solution-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 20px 15px;
        border-radius: 12px;
        transition: all 0.3s ease;
        cursor: pointer;
        text-decoration: none;
        color: inherit;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        min-height: 140px;
        justify-content: flex-start;
    }

    .solution-item:hover {
        background: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.12);
        text-decoration: none;
        color: inherit;
        border-color: rgba(102, 126, 234, 0.3);
    }

    .solution-item .solution-icon {
        width: 64px;
        height: 64px;
        background: transparent;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 12px;
        transition: all 0.3s ease;
        flex-shrink: 0;
    }

    .solution-item .solution-icon img {
        width: 48px;
        height: 48px;
        object-fit: contain;
    }

    .solution-item:hover .solution-icon {
        transform: scale(1.1);
    }

    .solution-item .solution-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 100%;
    }

    .solution-item .solution-content h4 {
        font-size: 1rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        line-height: 1.3;
    }

    .solution-item .solution-content p {
        font-size: 0.8rem;
        color: #666;
        line-height: 1.4;
        margin: 0;
    }

    /* 产品网格布局修正 */
    .products-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 25px;
        padding: 30px;
        background: white;
        min-width: 800px;
    }

    .products-column {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .product-item {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        border-radius: 10px;
        transition: all 0.3s ease;
        cursor: pointer;
        background: white;
        border: 1px solid rgba(102, 126, 234, 0.1);
        text-decoration: none;
        color: inherit;
        margin-bottom: 8px;
    }

    .product-item:hover {
        background: rgba(102, 126, 234, 0.12);
        border-color: rgba(102, 126, 234, 0.25);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
        text-decoration: none;
        color: inherit;
    }

    .product-icon {
        width: 35px;
        height: 35px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        flex-shrink: 0;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .product-icon.purple {
        background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
        color: white;
    }

    .product-icon i {
        font-size: 18px;
        color: white;
        line-height: 1;
        width: auto;
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    /* Lucide图标样式 */
    .product-icon svg {
        width: 18px;
        height: 18px;
        color: white;
        stroke: white;
        stroke-width: 2;
    }

    .product-item:hover .product-icon {
        transform: scale(1.1);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.35);
    }

    .product-item:hover .product-icon i {
        transform: translate(-50%, -50%) scale(1);
    }

    .product-name {
        font-size: 0.9rem;
        font-weight: 500;
        color: #333;
        line-height: 1.4;
        transition: color 0.3s ease;
    }

    .product-item:hover .product-name {
        color: #667eea;
    }

    .product-item-placeholder {
        padding: 12px 15px;
        color: #999;
        font-size: 0.9rem;
        text-align: center;
        font-style: italic;
    }

    /* 菜单底部 */
    .mega-menu-footer {
        text-align: center;
        padding-top: 25px;
        border-top: 1px solid rgba(0, 0, 0, 0.08);
        margin-top: 20px;
    }

    .mega-menu-footer .btn {
        padding: 12px 30px;
        font-size: 0.95rem;
        font-weight: 600;
        border-radius: 25px;
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        color: white;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.25);
    }

    .mega-menu-footer .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(0, 123, 255, 0.35);
        background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
        text-decoration: none;
        color: white;
    }

    /* ===== 产品介绍菜单样式 ===== */
    .products-menu {
        padding: 0;
        z-index: 10000 !important; /* 比解决方案菜单更高的z-index */
    }

    .products-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 30px;
        padding: 30px;
        background: white;
        min-width: 800px;
    }

    .products-column {
        display: flex;
        flex-direction: column;
    }

    .mega-menu-category {
        font-size: 1.1rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #667eea;
        text-align: center;
    }

    .product-item {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        border-radius: 8px;
        transition: all 0.3s ease;
        text-decoration: none;
        color: #333;
        margin-bottom: 8px;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
    }

    .product-item:hover {
        background: white;
        transform: translateX(5px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.15);
        text-decoration: none;
        color: #667eea;
        border-color: #667eea;
    }

    .product-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        transition: all 0.3s ease;
        flex-shrink: 0;
        position: relative;
        overflow: hidden;
    }

    .product-icon.purple {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .product-icon i {
        font-size: 18px;
        line-height: 1;
        width: auto;
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .product-item:hover .product-icon {
        transform: scale(1.1);
    }

    .product-item:hover .product-icon i {
        transform: translate(-50%, -50%) scale(1);
    }

    .product-name {
        font-size: 0.95rem;
        font-weight: 600;
        color: inherit;
    }

    .product-item-placeholder {
        padding: 12px 15px;
        color: #999;
        font-size: 0.9rem;
        text-align: center;
        font-style: italic;
    }

    /* ===== 导航栏基础样式增强 ===== */
    .header {
        position: relative;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    }

    .navbar {
        position: relative;
        z-index: 1001;
    }

    /* 确保所有下拉菜单都有足够高的z-index */
    .mega-dropdown .dropdown-menu {
        z-index: 10001 !important;
    }

    .solutions-menu {
        z-index: 10002 !important;
    }

    .products-menu {
        z-index: 10003 !important;
    }

    /* 确保三角形指示器也有正确的z-index */
    .mega-dropdown .dropdown-menu::before {
        z-index: 10004;
    }

    .mega-dropdown .dropdown-menu::after {
        z-index: 10003;
    }

    /* ===== 三级菜单样式 ===== */
    .dropdown-submenu {
        position: relative;
    }

    .dropdown-submenu .dropdown-menu {
        position: absolute;
        top: 0;
        left: 100%;
        margin-top: -1px;
        display: none;
        min-width: 200px;
    }

    .dropdown-submenu:hover .dropdown-menu {
        display: block;
    }

    .dropdown-submenu .dropdown-toggle::after {
        content: "▶";
        float: right;
        margin-top: 2px;
        border: none;
    }

    /* 响应式处理 */
    @media (max-width: 991px) {
        .dropdown-submenu .dropdown-menu {
            position: static;
            float: none;
            width: auto;
            margin-top: 0;
            background-color: #f8f9fa;
            border: none;
            box-shadow: none;
            padding-left: 20px;
        }
    }
    </style>
</head>
<body class="<?php echo htmlentities((string) (isset($bodyClass) && ($bodyClass !== '')?$bodyClass:'home-page')); ?>">
    <!-- 导航栏 -->
    <header class="header">
        <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="/">
                <img src="<?php echo asset('assets/images/logo.png'); ?>" alt="<?php echo htmlentities((string) (isset($siteConfig['site_name']) && ($siteConfig['site_name'] !== '')?$siteConfig['site_name']:'三只鱼网络')); ?>" height="40">
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto" id="main-nav">
                    <!-- 动态菜单将在这里加载 -->
                </ul>


                <!-- 右侧按钮组 -->
                <div class="navbar-actions ms-3">
                    <a href="#" class="search-btn me-2"><i data-lucide="search"></i></a>
                    <a href="/login" class="btn btn-outline-light btn-sm me-2">登录</a>
                    <a href="/register" class="btn btn-primary btn-sm">注册</a>
                </div>
            </div>
        </div>
    </nav>
</header>

    <!-- 主要内容 -->
    <main>

<script>
// 菜单加载 - 添加调试信息

// URL格式化函数
function formatUrl(url) {
    if (!url || url === '#') {
        return '#';
    }

    // 转换为字符串并清理
    url = String(url).trim();

    // 如果是完整的URL（包含协议），直接返回
    if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
    }

    // 如果已经是正确的相对路径（以/开头），直接返回
    if (url.startsWith('/')) {
        return url;
    }

    // 如果是相对路径，添加/前缀
    return '/' + url;
}

document.addEventListener('DOMContentLoaded', function() {
    loadMenus();
});

function loadMenus() {
    fetch('/api/sys-menus')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {

            if (data && data.success && data.data) {
                renderMenus(data.data);
            } else {
            }
        })
        .catch(error => {
            console.error('菜单加载失败:', error);
            console.error('错误详情:', error.message);
        });
}

function renderMenus(menus) {
    const nav = document.getElementById('main-nav');
    if (!nav) {
        console.error('找不到导航容器 #main-nav');
        return;
    }

    // 清空现有菜单（保留首页）
    const existing = nav.querySelectorAll('li:not(:first-child)');
    existing.forEach(item => item.remove());

    // 添加新菜单
    let hasMorePages = false;
    let hasContact = false;
    
    menus.forEach((menu, index) => {
        const li = createMenuItem(menu);
        nav.appendChild(li);
        
        // 检查是否已经有"更多页面"和"联系我们"菜单
        if (menu.name === '更多页面') {
            hasMorePages = true;
            // 为动态的"更多页面"菜单添加id，以便DIY页面加载
            const dropdownMenu = li.querySelector('.dropdown-menu');
            if (dropdownMenu && !dropdownMenu.id) {
                dropdownMenu.id = 'diy-pages-menu';
            }
        }
        if (menu.name === '联系我们') {
            hasContact = true;
        }
    });

    // 只有当动态菜单中没有"更多页面"时才添加固定的"更多页面"菜单
    if (!hasMorePages) {
        const morePages = document.createElement('li');
        morePages.className = 'nav-item dropdown';
        morePages.innerHTML = `
            <a class="nav-link dropdown-toggle" href="#" id="diyPagesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                更多页面
            </a>
            <ul class="dropdown-menu" aria-labelledby="diyPagesDropdown" id="diy-pages-menu">
            </ul>
        `;
        nav.appendChild(morePages);

        // 为"更多页面"添加悬停事件
        morePages.addEventListener('mouseenter', function() {
            const dropdown = morePages.querySelector('.dropdown-menu');
            if (dropdown) {
                dropdown.style.display = 'block';
                morePages.classList.add('show');
            }
        });

        morePages.addEventListener('mouseleave', function() {
            const dropdown = morePages.querySelector('.dropdown-menu');
            if (dropdown) {
                dropdown.style.display = 'none';
                morePages.classList.remove('show');
            }
        });
    }

    // 只有当动态菜单中没有"联系我们"时才添加固定的"联系我们"菜单
    if (!hasContact) {
        const contact = document.createElement('li');
        contact.className = 'nav-item';
        contact.innerHTML = '<a class="nav-link" href="/contact">联系我们</a>';
        nav.appendChild(contact);
    }


    // 初始化下拉菜单事件
    initDropdownEvents();

    // 确保Lucide图标正确渲染
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // 加载DIY页面到导航菜单
    loadDiyPagesToMenu();
    
}

function createMenuItem(menu) {
    const li = document.createElement('li');
    const url = formatUrl(menu.url || menu.link_value || '#');

    if (menu.children && menu.children.length > 0) {
        // 检查是否是特殊的mega-dropdown菜单
        if (menu.name === '解决方案') {
            return createSolutionsMenuItem(menu, url);
        } else if (menu.name === '产品介绍') {
            return createProductsMenuItem(menu, url);
        } else {
            // 普通下拉菜单
            li.className = 'nav-item dropdown';

            // 创建主链接
            const mainLink = document.createElement('a');
            mainLink.className = 'nav-link dropdown-toggle';
            mainLink.href = url;
            mainLink.setAttribute('role', 'button');
            mainLink.setAttribute('aria-expanded', 'false');
            mainLink.textContent = menu.name;

            // 创建下拉菜单
            const dropdownMenu = document.createElement('ul');
            dropdownMenu.className = 'dropdown-menu';
            dropdownMenu.innerHTML = renderSubMenus(menu.children);

            li.appendChild(mainLink);
            li.appendChild(dropdownMenu);

            // 添加悬停事件
            li.addEventListener('mouseenter', function() {
                const dropdown = li.querySelector('.dropdown-menu');
                if (dropdown) {
                    dropdown.style.display = 'block';
                    li.classList.add('show');
                }
            });

            li.addEventListener('mouseleave', function() {
                const dropdown = li.querySelector('.dropdown-menu');
                if (dropdown) {
                    dropdown.style.display = 'none';
                    li.classList.remove('show');
                }
            });

        }
    } else {
        // 普通菜单项
        li.className = 'nav-item';

        const link = document.createElement('a');
        link.className = 'nav-link';
        link.href = url;
        link.textContent = menu.name;
        li.appendChild(link);
    }

    return li;
}

// 创建解决方案菜单项
function createSolutionsMenuItem(menu, url) {
    const li = document.createElement('li');
    li.className = 'nav-item mega-dropdown';

    const mainLink = document.createElement('a');
    mainLink.className = 'nav-link dropdown-toggle';
    mainLink.href = url;
    mainLink.id = 'solutionsDropdown';
    mainLink.setAttribute('role', 'button');
    mainLink.setAttribute('aria-expanded', 'false');
    mainLink.textContent = menu.name;

    const dropdownMenu = document.createElement('div');
    dropdownMenu.className = 'dropdown-menu solutions-menu';
    
    // 动态生成解决方案网格
    let solutionsHtml = '<div class="solutions-grid">';
    
    if (menu.children && menu.children.length > 0) {
        // 使用真实的子菜单数据
        menu.children.forEach(solution => {
            const solutionUrl = formatUrl(solution.url || solution.link_value || '#');
            const iconPath = solution.icon || '/assets/images/nav/default.png';
            const description = solution.description || solution.remark || '专业的解决方案';
            
            solutionsHtml += `
                <a href="${solutionUrl}" class="solution-item">
                    <div class="solution-icon">
                        <img src="${iconPath}" alt="${solution.name}">
                    </div>
                    <div class="solution-content">
                        <h4>${solution.name}</h4>
                        <p>${description}</p>
                    </div>
                </a>
            `;
        });
    }
    
    solutionsHtml += '</div>';
    dropdownMenu.innerHTML = solutionsHtml;

    li.appendChild(mainLink);
    li.appendChild(dropdownMenu);

    // 添加mega-dropdown悬停事件
    addMegaDropdownEvents(li);

    return li;
}

// 创建产品介绍菜单项
function createProductsMenuItem(menu, url) {
    const li = document.createElement('li');
    li.className = 'nav-item mega-dropdown';

    const mainLink = document.createElement('a');
    mainLink.className = 'nav-link dropdown-toggle';
    mainLink.href = url;
    mainLink.id = 'productsDropdown';
    mainLink.setAttribute('role', 'button');
    mainLink.setAttribute('aria-expanded', 'false');
    mainLink.textContent = menu.name;

    const dropdownMenu = document.createElement('div');
    dropdownMenu.className = 'dropdown-menu products-menu';
    
    // 动态生成产品网格
    let productsHtml = '<div class="products-grid">';
    
    if (menu.children && menu.children.length > 0) {
        // 使用真实的子菜单数据，每个子菜单作为一个分类
        menu.children.forEach(category => {
            productsHtml += '<div class="products-column">';
            productsHtml += `<h6 class="mega-menu-category">${category.name}</h6>`;
            
            // 如果分类有子项目（产品），显示产品列表
            if (category.children && category.children.length > 0) {
                category.children.forEach(product => {
                    const productUrl = formatUrl(product.url || product.link_value || '#');
                    const productIcon = product.icon || 'fas fa-box';
                    
                    productsHtml += `
                        <a href="${productUrl}" class="product-item">
                            <div class="product-icon purple">
                                <i class="${productIcon}"></i>
                            </div>
                            <span class="product-name">${product.name}</span>
                        </a>
                    `;
                });
            } else {
                // 如果分类没有子产品，将分类本身作为产品显示
                const categoryUrl = formatUrl(category.url || category.link_value || '#');
                const categoryIcon = category.icon || 'fas fa-box';
                
                productsHtml += `
                    <a href="${categoryUrl}" class="product-item">
                        <div class="product-icon purple">
                            <i class="${categoryIcon}"></i>
                        </div>
                        <span class="product-name">${category.name}</span>
                    </a>
                `;
            }
            
            productsHtml += '</div>';
        });
    }
    
    productsHtml += '</div>';
    dropdownMenu.innerHTML = productsHtml;

    li.appendChild(mainLink);
    li.appendChild(dropdownMenu);

    // 添加mega-dropdown悬停事件
    addMegaDropdownEvents(li);

    return li;
}

// 添加mega-dropdown悬停事件
function addMegaDropdownEvents(dropdown) {
    const menu = dropdown.querySelector('.dropdown-menu');
    const toggle = dropdown.querySelector('.dropdown-toggle');
    let hoverTimer = null;
    let isMenuHovered = false;
    let isToggleHovered = false;
    
    // 显示菜单的函数
    function showMenu() {
        if (menu) {
            clearTimeout(hoverTimer);
            menu.style.display = 'block';
            menu.style.opacity = '1';
            menu.style.visibility = 'visible';
            menu.style.pointerEvents = 'auto';
            dropdown.classList.add('show');
            toggle.setAttribute('aria-expanded', 'true');
        }
    }
    
    // 隐藏菜单的函数
    function hideMenu() {
        if (menu) {
            hoverTimer = setTimeout(function() {
                if (!isMenuHovered && !isToggleHovered) {
                    menu.style.display = 'none';
                    menu.style.opacity = '0';
                    menu.style.visibility = 'hidden';
                    menu.style.pointerEvents = 'none';
                    dropdown.classList.remove('show');
                    toggle.setAttribute('aria-expanded', 'false');
                }
            }, 150);
        }
    }
    
    // 触发器悬停事件
    toggle.addEventListener('mouseenter', function() {
        isToggleHovered = true;
        showMenu();
    });
    
    toggle.addEventListener('mouseleave', function() {
        isToggleHovered = false;
        hideMenu();
    });
    
    // 菜单悬停事件
    if (menu) {
        menu.addEventListener('mouseenter', function() {
            isMenuHovered = true;
            clearTimeout(hoverTimer);
        });
        
        menu.addEventListener('mouseleave', function() {
            isMenuHovered = false;
            hideMenu();
        });
    }
    
    // 点击触发器时阻止默认行为
    toggle.addEventListener('click', function(e) {
        e.preventDefault();
        if (menu.style.display === 'block') {
            hideMenu();
            isToggleHovered = false;
            isMenuHovered = false;
        } else {
            showMenu();
        }
    });
}

function renderSubMenus(children) {
    const items = children.map(child => {
        const url = formatUrl(child.url || child.link_value || '#');

        const li = document.createElement('li');

        if (child.children && child.children.length > 0) {
            // 有三级菜单的二级菜单项
            li.className = 'dropdown-submenu';

            const link = document.createElement('a');
            link.className = 'dropdown-item';
            link.href = url;
            link.textContent = child.name + ' ▶';

            const submenu = document.createElement('ul');
            submenu.className = 'dropdown-menu submenu';

            child.children.forEach(grandChild => {
                const grandUrl = formatUrl(grandChild.url || grandChild.link_value || '#');
                const grandLi = document.createElement('li');
                const grandLink = document.createElement('a');
                grandLink.className = 'dropdown-item';
                grandLink.href = grandUrl;
                grandLink.textContent = grandChild.name;
                grandLi.appendChild(grandLink);
                submenu.appendChild(grandLi);

            });

            li.appendChild(link);
            li.appendChild(submenu);

        } else {
            // 普通二级菜单项
            const link = document.createElement('a');
            link.className = 'dropdown-item';
            link.href = url;
            link.textContent = child.name;
            li.appendChild(link);

        }

        return li.outerHTML;
    });

    return items.join('');
}

function initDropdownEvents() {
    // Bootstrap下拉菜单自动处理，但需要处理三级菜单
    document.querySelectorAll('.dropdown-submenu').forEach(submenu => {
        const toggle = submenu.querySelector('.dropdown-item');
        const menu = submenu.querySelector('.dropdown-menu');

        if (toggle && menu) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // 切换显示状态
                menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
            });

            // 鼠标悬停显示三级菜单
            submenu.addEventListener('mouseenter', function() {
                menu.style.display = 'block';
            });

            submenu.addEventListener('mouseleave', function() {
                menu.style.display = 'none';
            });
        }
    });

    // 点击页面其他地方时关闭所有mega-dropdown菜单
    document.addEventListener('click', function(e) {
        // 检查点击的目标是否在下拉菜单内
        const isDropdownClick = e.target.closest('.mega-dropdown');
        
        if (!isDropdownClick) {
            const megaDropdowns = document.querySelectorAll('.mega-dropdown');
            megaDropdowns.forEach(function(dropdown) {
                const menu = dropdown.querySelector('.dropdown-menu');
                const toggle = dropdown.querySelector('.dropdown-toggle');
                
                if (menu) {
                    menu.style.display = 'none';
                    menu.style.opacity = '0';
                    menu.style.visibility = 'hidden';
                    menu.style.pointerEvents = 'none';
                    dropdown.classList.remove('show');
                    toggle.setAttribute('aria-expanded', 'false');
                }
            });
        }
    });
}

// 加载DIY页面到导航菜单
function loadDiyPagesToMenu() {


    // 获取菜单容器
    const menuContainer = document.getElementById('diy-pages-menu');
    if (!menuContainer) {
        console.error('未找到DIY页面菜单容器');
        return;
    }

    // 发起API请求
    fetch('/api/diy-pages')
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败: ' + response.status);
            }
            return response.json();
        })
        .then(data => {

            if (data.success && data.pages && data.pages.length > 0) {
                // 清空现有的DIY页面项（保留默认的"关于我们"）
                const existingDiyItems = menuContainer.querySelectorAll('.diy-page-item');
                existingDiyItems.forEach(item => item.remove());

                // 不添加分隔线，保持菜单简洁

                // 添加DIY页面链接
                data.pages.forEach(page => {
                    const li = document.createElement('li');
                    li.className = 'diy-page-item';
                    li.innerHTML = `
                        <a class="dropdown-item" href="/page/${page.slug}" title="${page.description || page.name}">
                            <i class="fas fa-file-alt me-2"></i>
                            ${page.name}
                        </a>
                    `;
                    menuContainer.appendChild(li);
                });

            } else {

            }
        })
        .catch(error => {
            console.error('加载DIY页面失败:', error);

            // 在菜单中显示错误提示（仅在开发环境）
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                const li = document.createElement('li');
                li.innerHTML = '<span class="dropdown-item text-muted">加载页面失败</span>';
                menuContainer.appendChild(li);
            }
        });
}
</script>
<!-- 公共样式文件 -->
<style>
/* ===== Header和Navbar样式 ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar {
    padding: 12px 0 !important;
    background: transparent !important;
    border: none !important;
    transition: background 0.3s ease, backdrop-filter 0.3s ease !important;
    min-height: 70px !important;
    display: flex !important;
    align-items: center !important;
}

/* 确保navbar容器正确布局 */
.navbar .container {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    width: 100% !important;
}

/* 滚动时header背景 */
header.scrolled {
    background: rgba(47, 76, 153, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    box-shadow: 0 2px 20px rgba(47, 76, 153, 0.3) !important;
}

/* 确保navbar也有背景 */
header.scrolled .navbar,
header.scrolled .navbar-expand-lg,
header.scrolled .navbar-light {
    background: #2f4c99f5 !important;
    background-color: #2f4c99f5 !important;
}

.navbar-brand {
    font-size: 1.6rem !important;
    font-weight: 600 !important;
    color: white !important;
    text-decoration: none !important;
    display: flex !important;
    align-items: center !important;
    background: none !important;
    -webkit-background-clip: unset !important;
    -webkit-text-fill-color: white !important;
    background-clip: unset !important;
}

.navbar-brand .logo {
    height: 45px !important;
    width: auto !important;
    margin-right: 10px !important;
    filter: brightness(0) invert(1) !important;
    transition: all 0.3s ease !important;
    vertical-align: middle !important;
}

/* 滚动时Logo颜色变化 */
header.scrolled .navbar-brand .logo {
    filter: none !important;
}

/* 导航菜单容器对齐 */
.navbar-nav {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    margin: 0 !important;
    list-style: none !important;
    padding: 0 !important;
}

/* 导航链接样式 */
.navbar-nav .nav-item {
    margin: 0 5px !important;
}

.navbar-nav .nav-link {
    color: white !important;
    font-weight: 400 !important;
    padding: 12px 20px !important;
    position: relative !important;
    transition: color 0.3s ease !important;
    font-size: 15px !important;
    background: none !important;
    border: none !important;
    display: flex !important;
    align-items: center !important;
}

/* 导航链接悬停效果 */
.navbar-nav .nav-link:hover {
    color: #ff6b35 !important;
}

/* 导航链接下划线效果 - 排除下拉菜单项 */
.navbar-nav .nav-link:not(.dropdown-toggle)::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:not(.dropdown-toggle):hover::after {
    width: 80%;
}

/* 激活状态的导航链接样式 */
.navbar-nav .nav-link.active {
    color: #ff6b35 !important;
}

.navbar-nav .nav-link.active:not(.dropdown-toggle)::after {
    width: 80%;
    background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
}

/* 右侧按钮组对齐 */
.navbar-actions {
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
    margin-left: 20px !important;
    height: 45px !important;
    flex-shrink: 0 !important;
}

.search-btn {
    color: white !important;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    text-decoration: none;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white !important;
    text-decoration: none;
}

.search-btn svg {
    width: 16px;
    height: 16px;
    stroke: currentColor;
    stroke-width: 2;
}

/* 登录注册按钮样式 */
.navbar-actions .btn {
    padding: 8px 18px !important;
    font-size: 14px !important;
    border-radius: 4px !important;
    font-weight: 400 !important;
    transition: color 0.3s ease, background 0.3s ease, border-color 0.3s ease !important;
    text-decoration: none !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.navbar-actions .btn-outline-light {
    color: white !important;
    background: transparent !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
}

.navbar-actions .btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: white !important;
}

.navbar-actions .btn-primary {
    background: #ff6b35 !important;
    border-color: #ff6b35 !important;
    color: white !important;
}

.navbar-actions .btn-primary:hover {
    background: #e55a2b !important;
    border-color: #e55a2b !important;
}

/* 滚动时按钮样式调整 */
header.scrolled .navbar-actions .btn-outline-light {
    color: white !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
}

header.scrolled .navbar-actions .btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: white !important;
}

header.scrolled .navbar-actions .btn-primary {
    background: #ff6b35 !important;
    border-color: #ff6b35 !important;
    color: white !important;
}

header.scrolled .navbar-actions .btn-primary:hover {
    background: #e55a2b !important;
    border-color: #e55a2b !important;
    color: white !important;
}

/* 强制确保滚动时按钮样式不被覆盖 */
header.scrolled .btn-primary,
header.scrolled .navbar-actions .btn-primary,
header.scrolled .navbar .btn-primary {
    background: #ff6b35 !important;
    background-color: #ff6b35 !important;
    border-color: #ff6b35 !important;
    color: white !important;
}

header.scrolled .btn-primary:hover,
header.scrolled .navbar-actions .btn-primary:hover,
header.scrolled .navbar .btn-primary:hover {
    background: #e55a2b !important;
    background-color: #e55a2b !important;
    border-color: #e55a2b !important;
    color: white !important;
}

/* 移动端菜单按钮样式 */
.navbar-toggler {
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    padding: 4px 8px !important;
    transition: all 0.3s ease !important;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
    transition: all 0.3s ease !important;
}

/* 确保主要内容不被固定header遮挡 */
main {
    padding-top: 70px;
}

/* 首页轮播图需要全屏显示，移除顶部间距 */
.home-page main {
    padding-top: 0;
}

/* ===== 产品菜单特殊样式 ===== */
.products-menu {
    padding: 0;
}

.products-header {
    background: #f8f9fa;
    padding: 25px 30px;
    border-bottom: 1px solid #e9ecef;
    border-radius: 16px 16px 0 0;
}

/* 产品网格布局 */
.products-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
    padding: 30px;
}

.products-column {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.product-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-radius: 10px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
    border: 1px solid rgba(102, 126, 234, 0.1);
    text-decoration: none;
    color: inherit;
}

.product-item:hover {
    background: rgba(102, 126, 234, 0.12);
    border-color: rgba(102, 126, 234, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
    text-decoration: none;
    color: inherit;
}

.product-icon {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.product-icon.purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
}

.product-icon i {
    font-size: 18px;
    color: white;
}

/* Lucide图标样式 */
.product-icon svg {
    width: 18px;
    height: 18px;
    color: white;
    stroke: white;
    stroke-width: 2;
}

.product-item:hover .product-icon {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.35);
}

.product-name {
    font-size: 0.9rem;
    font-weight: 500;
    color: #333;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.product-item:hover .product-name {
    color: #667eea;
}

/* ===== 菜单分类标题 ===== */
.mega-menu-category {
    font-size: 1.1rem;
    font-weight: 600;
    color: #007bff;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid rgba(0, 123, 255, 0.2);
    position: relative;
}

.mega-menu-category::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 30px;
    height: 2px;
    background: #007bff;
    border-radius: 1px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .solutions-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 900px) {
    .solutions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 600px) {
    .solutions-grid {
        grid-template-columns: 1fr;
    }
    .products-grid {
        grid-template-columns: 1fr;
    }
}
</style>


<!-- 🚀 超级科技感Hero区域 -->
<section class="quantum-hero-zone">
    <!-- 多维背景系统 -->
    <div class="quantum-bg-system">
        <!-- 主背景图层 -->
        <div class="bg-primary-layer" style="background-image: url('<?php echo asset("assets/images/banner1.png"); ?>')"></div>

        <!-- 全息网格 -->
        <div class="holographic-grid">
            <div class="grid-matrix"></div>
            <div class="grid-scanlines"></div>
        </div>

        <!-- 量子粒子场 -->
        <div class="quantum-particles" id="quantumField"></div>

        <!-- 能量脉冲波 -->
        <div class="energy-pulse-waves">
            <div class="pulse-wave wave-alpha"></div>
            <div class="pulse-wave wave-beta"></div>
            <div class="pulse-wave wave-gamma"></div>
        </div>

        <!-- 数字雨效果 -->
        <div class="digital-rain" id="digitalRain"></div>

        <!-- 深度渐变遮罩 -->
        <div class="depth-gradient-mask"></div>
    </div>

    <!-- 核心内容区域 -->
    <div class="quantum-content-core">
        <div class="container-fluid px-4">
            <div class="row justify-content-center">
                <div class="col-xl-11 col-lg-12">

                    <!-- 🌟 超级主标题系统 -->
                    <div class="mega-title-matrix text-center" data-aos="fade-up" data-aos-delay="200">
                        <h1 class="quantum-mega-title text-center">

                            <div class="title-segment segment-1 text-center">
                                <span class="segment-bg-effect"></span>
                                <span class="segment-text">解决方案</span>
                                <span class="segment-energy-trail"></span>
                            </div>
                        </h1>
                        <div class="title-quantum-subtitle text-center">
                            <span class="subtitle-main">重新定义企业数字化转型边界</span>
                            <span class="subtitle-accent">让不可能成为可能</span>
                        </div>
                        <div class="title-hologram-effect"></div>
                    </div>

                    <!-- ⚡ 核心技术能力矩阵 -->
                    <div class="tech-capability-matrix" data-aos="fade-up" data-aos-delay="300">
                        <div class="capability-grid-system">
                            <div class="tech-node" data-tech="AI">
                                <div class="node-core">
                                    <div class="core-icon">
                                        <i class="fas fa-brain"></i>
                                    </div>
                                    <div class="core-pulse"></div>
                                </div>
                                <div class="node-label">人工智能</div>
                                <div class="node-energy-field"></div>
                                <div class="node-connections"></div>
                            </div>

                            <div class="tech-node" data-tech="QUANTUM">
                                <div class="node-core">
                                    <div class="core-icon">
                                        <i class="fas fa-atom"></i>
                                    </div>
                                    <div class="core-pulse"></div>
                                </div>
                                <div class="node-label">电商系统</div>
                                <div class="node-energy-field"></div>
                                <div class="node-connections"></div>
                            </div>

                            <div class="tech-node" data-tech="BLOCKCHAIN">
                                <div class="node-core">
                                    <div class="core-icon">
                                        <i class="fas fa-cube"></i>
                                    </div>
                                    <div class="core-pulse"></div>
                                </div>
                                <div class="node-label">区块链</div>
                                <div class="node-energy-field"></div>
                                <div class="node-connections"></div>
                            </div>

                            <div class="tech-node" data-tech="IOT">
                                <div class="node-core">
                                    <div class="core-icon">
                                        <i class="fas fa-network-wired"></i>
                                    </div>
                                    <div class="core-pulse"></div>
                                </div>
                                <div class="node-label">物联网</div>
                                <div class="node-energy-field"></div>
                                <div class="node-connections"></div>
                            </div>
                        </div>
                        <div class="matrix-connection-lines"></div>
                    </div>

                    <!-- 🎮 量子行动控制台 -->
                    <div class="quantum-action-console" data-aos="fade-up" data-aos-delay="500">
                        <div class="console-interface">
                            <a href="#quantum-solutions" class="btn-quantum-primary">
                                <div class="btn-quantum-core">
                                    <span class="btn-text">启动探索模式</span>
                                    <div class="btn-icon">
                                        <i class="fas fa-rocket"></i>
                                    </div>
                                </div>
                                <div class="btn-energy-matrix"></div>
                                <div class="btn-quantum-particles"></div>
                                <div class="btn-hologram-border"></div>
                            </a>

                            <a href="/contact" class="btn-quantum-secondary">
                                <div class="btn-quantum-core">
                                    <span class="btn-text">连接专家</span>
                                    <div class="btn-icon">
                                        <i class="fas fa-satellite-dish"></i>
                                    </div>
                                </div>
                                <div class="btn-neural-network"></div>
                                <div class="btn-signal-waves"></div>
                            </a>
                        </div>
                    </div>

                    <!-- 🌀 量子滚动传送器 -->
                    <div class="quantum-scroll-portal" data-aos="fade-up" data-aos-delay="600">
                        <div class="portal-core">
                            <div class="portal-rings">
                                <div class="p-ring p-ring-1"></div>
                                <div class="p-ring p-ring-2"></div>
                                <div class="p-ring p-ring-3"></div>
                            </div>
                            <div class="portal-center">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>
                        <div class="portal-text">传送至解决方案维度</div>
                        <div class="portal-energy-field"></div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- 🔮 边缘量子装饰 -->
    <div class="quantum-edge-decorations">
        <div class="edge-quantum-element edge-q-1"></div>
        <div class="edge-quantum-element edge-q-2"></div>
        <div class="edge-quantum-element edge-q-3"></div>
        <div class="edge-quantum-element edge-q-4"></div>
        <div class="quantum-field-lines"></div>
    </div>
</section>

<!-- 🚀 解决方案展示区域 -->
<section id="quantum-solutions" class="solutions-showcase-zone">
    <!-- 背景装饰系统 -->
    <div class="showcase-bg-system">
        <div class="neural-network-bg"></div>
        <div class="data-stream-lines"></div>
        <div class="floating-particles"></div>
    </div>

    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-xl-10 col-lg-11">

                <!-- 🎯 区域标题 -->
                <div class="section-title-matrix text-center" data-aos="fade-up" data-aos-delay="100">
                    <h2 class="section-mega-title">
                        <span class="title-glow-effect">企业级解决方案矩阵</span>
                    </h2>
                    <p class="section-subtitle">
                        基于前沿技术架构，为不同行业量身定制的数字化转型解决方案
                    </p>
                    <div class="title-energy-line"></div>
                </div>

                <!-- 🌐 解决方案网格 -->
                <div class="solutions-grid-matrix" data-aos="fade-up" data-aos-delay="200">
                    <?php if(is_array($solutions) || $solutions instanceof \think\Collection || $solutions instanceof \think\Paginator): $i = 0; $__LIST__ = $solutions;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$solution): $mod = ($i % 2 );++$i;?>
                    <div class="solution-quantum-card" data-aos="zoom-in" data-aos-delay="<?php echo htmlentities((string) $key * 100); ?>">
                        <!-- 卡片能量边框 -->
                        <div class="card-energy-border"></div>

                        <!-- 卡片内容核心 -->
                        <div class="card-content-core">
                            <!-- 图标区域 -->
                            <div class="solution-icon-zone">
                                <div class="icon-quantum-core">
                                    <?php switch($solution['name']): case "社交分销": ?>
                                            <i class="fas fa-share-alt"></i>
                                        <?php break; case "智能多门店": ?>
                                            <i class="fas fa-store"></i>
                                        <?php break; case "大型多商户": ?>
                                            <i class="fas fa-building"></i>
                                        <?php break; case "大货批发": ?>
                                            <i class="fas fa-boxes"></i>
                                        <?php break; case "平台级供货商": ?>
                                            <i class="fas fa-truck"></i>
                                        <?php break; case "本地生活服务": ?>
                                            <i class="fas fa-map-marker-alt"></i>
                                        <?php break; case "企业数字化": ?>
                                            <i class="fas fa-digital-tachograph"></i>
                                        <?php break; case "定制化方案": ?>
                                            <i class="fas fa-cogs"></i>
                                        <?php break; default: ?>
                                            <i class="fas fa-lightbulb"></i>
                                    <?php endswitch; ?>
                                    <div class="icon-pulse-ring"></div>
                                </div>
                                <div class="icon-energy-field"></div>
                            </div>

                            <!-- 标题区域 -->
                            <div class="solution-title-zone">
                                <h3 class="solution-title"><?php echo htmlentities((string) $solution['name']); ?></h3>
                                <div class="title-underline-effect"></div>
                            </div>

                            <!-- 描述区域 -->
                            <div class="solution-desc-zone">
                                <p class="solution-description">
                                    <?php echo htmlentities((string) (isset($solution['short_description']) && ($solution['short_description'] !== '')?$solution['short_description']:$solution['description'])); ?>
                                </p>
                            </div>

                            <!-- 特性标签 -->
                            <div class="solution-features-zone">
                                <div class="features-grid">
                                    <?php switch($solution['name']): case "社交分销": ?>
                                            <span class="feature-tag">零成本获客</span>
                                            <span class="feature-tag">裂变增长</span>
                                            <span class="feature-tag">精准营销</span>
                                        <?php break; case "智能多门店": ?>
                                            <span class="feature-tag">统一管理</span>
                                            <span class="feature-tag">实时同步</span>
                                            <span class="feature-tag">数据分析</span>
                                        <?php break; case "大型多商户": ?>
                                            <span class="feature-tag">高并发</span>
                                            <span class="feature-tag">商户自管</span>
                                            <span class="feature-tag">安全保障</span>
                                        <?php break; case "大货批发": ?>
                                            <span class="feature-tag">大宗交易</span>
                                            <span class="feature-tag">供应链</span>
                                            <span class="feature-tag">信用体系</span>
                                        <?php break; case "平台级供货商": ?>
                                            <span class="feature-tag">AI驱动</span>
                                            <span class="feature-tag">智能补货</span>
                                            <span class="feature-tag">质量控制</span>
                                        <?php break; case "本地生活服务": ?>
                                            <span class="feature-tag">LBS定位</span>
                                            <span class="feature-tag">同城配送</span>
                                            <span class="feature-tag">生活服务</span>
                                        <?php break; case "企业数字化": ?>
                                            <span class="feature-tag">数字转型</span>
                                            <span class="feature-tag">流程优化</span>
                                            <span class="feature-tag">智能决策</span>
                                        <?php break; case "定制化方案": ?>
                                            <span class="feature-tag">个性定制</span>
                                            <span class="feature-tag">专业服务</span>
                                            <span class="feature-tag">技术支持</span>
                                        <?php break; default: ?>
                                            <span class="feature-tag">智能化</span>
                                            <span class="feature-tag">高效率</span>
                                            <span class="feature-tag">可扩展</span>
                                    <?php endswitch; ?>
                                </div>
                            </div>

                            <!-- 行动按钮 -->
                            <div class="solution-action-zone">
                                <a href="/solutions/<?php echo htmlentities((string) $solution['slug']); ?>" class="btn-solution-explore">
                                    <span class="btn-text">深度探索</span>
                                    <div class="btn-quantum-arrow">
                                        <i class="fas fa-arrow-right"></i>
                                    </div>
                                    <div class="btn-hover-effect"></div>
                                </a>
                            </div>
                        </div>

                        <!-- 卡片悬浮效果 -->
                        <div class="card-hover-glow"></div>
                        <div class="card-data-streams"></div>
                    </div>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                </div>

                <!-- 🎮 底部行动区域 -->
                <div class="bottom-action-matrix text-center" data-aos="fade-up" data-aos-delay="600">
                    <div class="action-content-core">
                        <h3 class="action-title">
                            <span class="title-quantum-effect">需要定制化解决方案？</span>
                        </h3>
                        <p class="action-subtitle">
                            我们的专家团队随时为您提供专业的技术咨询和定制化服务
                        </p>
                        <div class="action-buttons-group">
                            <a href="/contact" class="btn-action-primary">
                                <div class="btn-core">
                                    <span class="btn-text">联系专家</span>
                                    <i class="fas fa-user-tie"></i>
                                </div>
                                <div class="btn-energy-wave"></div>
                            </a>
                            <a href="/cases" class="btn-action-secondary">
                                <div class="btn-core">
                                    <span class="btn-text">查看案例</span>
                                    <i class="fas fa-briefcase"></i>
                                </div>
                                <div class="btn-neural-effect"></div>
                            </a>
                        </div>
                    </div>
                    <div class="action-bg-effects"></div>
                </div>

            </div>
        </div>
    </div>

    <!-- 区域装饰元素 -->
    <div class="showcase-decorations">
        <div class="deco-element deco-1"></div>
        <div class="deco-element deco-2"></div>
        <div class="deco-element deco-3"></div>
    </div>
</section>

<!-- 🎨 解决方案页面样式 -->
<style>

/* ===== 量子Hero区域 ===== */
.quantum-hero-zone {
    position: relative;
    min-height: 100vh;
    align-items: center;
    overflow: hidden;
    background: radial-gradient(ellipse at center, #0a0a0a 0%, #000000 100%);
}

/* 多维背景系统 */
.quantum-bg-system {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.bg-primary-layer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0.15;
    filter: blur(1px);
}

/* 全息网格 */
.holographic-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.3;
}

.grid-matrix {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 20s linear infinite;
}

.grid-scanlines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        0deg,
        transparent,
        transparent 2px,
        rgba(0, 255, 255, 0.03) 2px,
        rgba(0, 255, 255, 0.03) 4px
    );
    animation: scanlineMove 3s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

@keyframes scanlineMove {
    0% { transform: translateY(0); }
    100% { transform: translateY(4px); }
}

/* 量子粒子场 */
.quantum-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 30%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(255, 0, 255, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(0, 255, 127, 0.06) 0%, transparent 50%);
    animation: quantumFloat 25s ease-in-out infinite;
}

@keyframes quantumFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-30px) rotate(90deg); }
    50% { transform: translateY(-15px) rotate(180deg); }
    75% { transform: translateY(-45px) rotate(270deg); }
}

/* 能量脉冲波 */
.energy-pulse-waves {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.pulse-wave {
    position: absolute;
    width: 200px;
    height: 200px;
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 50%;
    animation: pulseExpand 4s ease-out infinite;
}

.wave-alpha {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.wave-beta {
    top: 60%;
    right: 15%;
    animation-delay: 1.3s;
    border-color: rgba(255, 0, 255, 0.3);
}

.wave-gamma {
    bottom: 30%;
    left: 30%;
    animation-delay: 2.6s;
    border-color: rgba(0, 255, 127, 0.3);
}

@keyframes pulseExpand {
    0% {
        transform: scale(0.5);
        opacity: 1;
    }
    100% {
        transform: scale(3);
        opacity: 0;
    }
}

/* 深度渐变遮罩 */
.depth-gradient-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.8) 0%,
        rgba(10, 20, 40, 0.6) 30%,
        rgba(20, 40, 80, 0.4) 70%,
        rgba(0, 0, 0, 0.8) 100%
    );
    z-index: 2;
}

/* 核心内容区域 */
.quantum-content-core {
    position: relative;
    z-index: 10;
    padding: 120px 0 80px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 80vh;
}

.quantum-content-core .container-fluid {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
}

.quantum-content-core .row {
    justify-content: center;
    align-items: center;
}

.quantum-content-core .col-xl-11,
.quantum-content-core .col-lg-12 {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}



/* 超级主标题系统 */
.mega-title-matrix {
    margin-bottom: 4rem;
    position: relative;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.quantum-mega-title {
    font-family: 'Orbitron', monospace;
    font-size: 5rem;
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 2rem;
    position: relative;
    text-align: center;
    width: 100%;
}

.title-segment {
    display: flex;
    position: relative;
    margin-bottom: 1rem;
    overflow: hidden;
    text-align: center;
    width: 100%;
    justify-content: center;
    align-items: center;
}

.segment-bg-effect {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
    animation: segmentScan 3s ease-in-out infinite;
    z-index: 1;
}

.segment-text {
    position: relative;
    z-index: 2;
    background: linear-gradient(135deg, #8282e1 0%, #ffffff 50%, #1a1a2e 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
    animation: textGlow 2s ease-in-out infinite alternate;
    display: block;
    text-align: center;
    width: 100%;
    flex: 1;
    font-weight: 700;
    

}

.segment-energy-trail {
    position: absolute;
    top: 50%;
    right: -50px;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, #00ffff, transparent);
    transform: translateY(-50%);
    animation: energyTrail 2s ease-in-out infinite;
    pointer-events: none;
}

@keyframes segmentScan {
    0%, 100% { left: -100%; }
    50% { left: 100%; }
}

@keyframes textGlow {
    0% { filter: brightness(1) drop-shadow(0 0 10px rgba(0, 255, 255, 0.3)); }
    100% { filter: brightness(1.2) drop-shadow(0 0 20px rgba(0, 255, 255, 0.6)); }
}

@keyframes energyTrail {
    0%, 100% { opacity: 0; transform: translateY(-50%) translateX(0); }
    50% { opacity: 1; transform: translateY(-50%) translateX(20px); }
}

.title-quantum-subtitle {
    text-align: center;
    margin-bottom: 2rem;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.subtitle-main {
    display: block;
    font-size: 1.4rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0.5rem;
    font-weight: 300;
    text-align: center;
    width: 100%;
}

.subtitle-accent {
    display: block;
    font-size: 1.1rem;
    color: #00ffff;
    font-weight: 500;
    text-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
    text-align: center;
    width: 100%;
}

.title-hologram-effect {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        45deg,
        transparent 30%,
        rgba(0, 255, 255, 0.1) 50%,
        transparent 70%
    );
    animation: hologramShift 4s ease-in-out infinite;
    pointer-events: none;
}

@keyframes hologramShift {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

/* 响应式标题居中 */
@media (max-width: 768px) {
    .quantum-mega-title {
        font-size: 3.5rem;
    }

    .title-segment {
        margin-bottom: 0.5rem;
    }

    .subtitle-main {
        font-size: 1.2rem;
    }

    .subtitle-accent {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .quantum-mega-title {
        font-size: 2.5rem;
    }

    .subtitle-main {
        font-size: 1rem;
    }

    .subtitle-accent {
        font-size: 0.9rem;
    }
}

/* 核心技术能力矩阵 */
.tech-capability-matrix {
    margin-bottom: 4rem;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.capability-grid-system {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    justify-items: center;
    align-items: center;
}

.tech-node {
    position: relative;
    background: rgba(0, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem 1.5rem;
    text-align: center;
    transition: all 0.4s ease;
    overflow: hidden;
}

.tech-node::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.tech-node:hover::before {
    left: 100%;
}

.tech-node:hover {
    transform: translateY(-10px);
    border-color: rgba(0, 255, 255, 0.5);
    box-shadow: 0 20px 40px rgba(0, 255, 255, 0.2);
}

.node-core {
    position: relative;
    width: 60px;
    height: 60px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.core-icon i {
    font-size: 1.5rem;
    color: white;
}

.core-pulse {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border: 2px solid rgba(0, 255, 255, 0.5);
    border-radius: 50%;
    animation: corePulse 2s ease-in-out infinite;
}

@keyframes corePulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.5; }
}

.node-label {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 1rem;
}

.node-energy-field {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120%;
    height: 120%;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.tech-node:hover .node-energy-field {
    opacity: 1;
}

.matrix-connection-lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.matrix-connection-lines::before,
.matrix-connection-lines::after {
    content: '';
    position: absolute;
    background: linear-gradient(90deg, transparent 0%, rgba(0, 255, 255, 0.3) 50%, transparent 100%);
    height: 1px;
    animation: connectionPulse 3s ease-in-out infinite;
}

.matrix-connection-lines::before {
    top: 30%;
    left: 0;
    right: 0;
}

.matrix-connection-lines::after {
    bottom: 30%;
    left: 0;
    right: 0;
    animation-delay: 1.5s;
}

@keyframes connectionPulse {
    0%, 100% { opacity: 0; transform: scaleX(0); }
    50% { opacity: 1; transform: scaleX(1); }
}



/* 量子行动控制台 */
.quantum-action-console {
    margin-bottom: 4rem;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.console-interface {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
    width: 100%;
}

.btn-quantum-primary,
.btn-quantum-secondary {
    position: relative;
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    border-radius: 50px;
    padding: 1.5rem 3rem;
    font-size: 1.1rem;
    font-weight: 700;
    transition: all 0.4s ease;
    overflow: hidden;
    min-width: 250px;
    justify-content: center;
}

.btn-quantum-primary {
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    color: white;
}

.btn-quantum-secondary {
    background: transparent;
    color: #00ffff;
    border: 2px solid rgba(0, 255, 255, 0.5);
}

.btn-quantum-core {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.btn-icon {
    transition: transform 0.3s ease;
}

.btn-quantum-primary:hover .btn-icon,
.btn-quantum-secondary:hover .btn-icon {
    transform: translateX(5px);
}

.btn-quantum-primary:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 255, 255, 0.4);
    color: white;
}

.btn-quantum-secondary:hover {
    background: rgba(0, 255, 255, 0.1);
    border-color: rgba(0, 255, 255, 0.8);
    transform: translateY(-5px);
    color: #00ffff;
}

.btn-energy-matrix {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff7f, #ffd700);
    border-radius: 50px;
    z-index: -1;
    opacity: 0;
    animation: energyMatrix 3s ease-in-out infinite;
}

@keyframes energyMatrix {
    0%, 100% { opacity: 0; }
    50% { opacity: 0.3; }
}

.btn-quantum-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    animation: btnParticles 2s ease-in-out infinite;
    z-index: 1;
}

@keyframes btnParticles {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

.btn-hologram-border {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    animation: hologramBorder 4s ease-in-out infinite;
}

@keyframes hologramBorder {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.05); }
}

.btn-neural-network {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 30%, rgba(0, 255, 255, 0.1) 50%, transparent 70%);
    animation: neuralPulse 3s ease-in-out infinite;
}

@keyframes neuralPulse {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

.btn-signal-waves {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid rgba(0, 255, 255, 0.5);
    border-radius: 50%;
    animation: signalWaves 2s ease-out infinite;
}

@keyframes signalWaves {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
    }
}

/* 量子滚动传送器 */
.quantum-scroll-portal {
    position: absolute;
    bottom: -80px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    animation: portalFloat 3s ease-in-out infinite;
}

.portal-core {
    position: relative;
    width: 60px;
    height: 60px;
    margin: 0 auto 1rem;
}

.portal-rings {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.p-ring {
    position: absolute;
    border: 1px solid rgba(0, 255, 255, 0.4);
    border-radius: 50%;
    animation: portalRingRotate 4s linear infinite;
}

.p-ring-1 {
    top: 5px;
    left: 5px;
    right: 5px;
    bottom: 5px;
    animation-duration: 2s;
}

.p-ring-2 {
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    animation-duration: 3s;
    animation-direction: reverse;
}

.p-ring-3 {
    top: 15px;
    left: 15px;
    right: 15px;
    bottom: 15px;
    animation-duration: 4s;
}

.portal-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.2rem;
    color: #00ffff;
    animation: portalPulse 2s ease-in-out infinite;
}

@keyframes portalFloat {
    0%, 100% { transform: translateX(-50%) translateY(0); }
    50% { transform: translateX(-50%) translateY(-10px); }
}

@keyframes portalRingRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes portalPulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.2); }
}

.portal-text {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.portal-energy-field {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: portalEnergyField 3s ease-in-out infinite;
}

@keyframes portalEnergyField {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.3); }
}

/* 边缘量子装饰 */
.quantum-edge-decorations {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 5;
    pointer-events: none;
}

.edge-quantum-element {
    position: absolute;
    width: 100px;
    height: 100px;
    border: 1px solid rgba(0, 255, 255, 0.2);
    animation: edgeElementFloat 8s ease-in-out infinite;
}

.edge-q-1 {
    top: 10%;
    left: 5%;
    border-radius: 50%;
    animation-delay: 0s;
}

.edge-q-2 {
    top: 20%;
    right: 8%;
    transform: rotate(45deg);
    animation-delay: 2s;
}

.edge-q-3 {
    bottom: 30%;
    left: 10%;
    border-radius: 20px;
    animation-delay: 4s;
}

.edge-q-4 {
    bottom: 15%;
    right: 5%;
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    animation-delay: 6s;
}

@keyframes edgeElementFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.3; }
    50% { transform: translateY(-30px) rotate(180deg); opacity: 0.8; }
}

.quantum-field-lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 49%, rgba(0, 255, 255, 0.1) 50%, transparent 51%),
        linear-gradient(-45deg, transparent 49%, rgba(255, 0, 255, 0.1) 50%, transparent 51%);
    background-size: 200px 200px;
    animation: fieldLinesMove 10s linear infinite;
}

@keyframes fieldLinesMove {
    0% { background-position: 0 0, 0 0; }
    100% { background-position: 200px 200px, -200px 200px; }
}

/* ===== 解决方案展示区域样式 ===== */
.solutions-showcase-zone {
    position: relative;
    padding: 120px 0;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    overflow: hidden;
}

/* 背景装饰系统 */
.showcase-bg-system {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.neural-network-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 0, 255, 0.08) 0%, transparent 50%);
    animation: neuralPulse 8s ease-in-out infinite;
}

.data-stream-lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 48%, rgba(0, 255, 255, 0.1) 49%, rgba(0, 255, 255, 0.1) 51%, transparent 52%),
        linear-gradient(-45deg, transparent 48%, rgba(255, 0, 255, 0.08) 49%, rgba(255, 0, 255, 0.08) 51%, transparent 52%);
    background-size: 100px 100px;
    animation: streamFlow 15s linear infinite;
}

.floating-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 30%, rgba(0, 255, 127, 0.05) 0%, transparent 40%),
        radial-gradient(circle at 80% 70%, rgba(255, 127, 0, 0.05) 0%, transparent 40%);
    animation: particleFloat 12s ease-in-out infinite;
}

@keyframes neuralPulse {
    0%, 100% { opacity: 0.6; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.05); }
}

@keyframes streamFlow {
    0% { background-position: 0 0, 0 0; }
    100% { background-position: 100px 100px, -100px 100px; }
}

@keyframes particleFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(-10px) rotate(240deg); }
}

/* 区域标题样式 */
.section-title-matrix {
    position: relative;
    z-index: 10;
    margin-bottom: 4rem;
}

.section-mega-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    font-family: 'Orbitron', monospace;
}

.title-glow-effect {
    background: linear-gradient(135deg, #ffffff 0%, #00ffff 50%, #ff00ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
    animation: titleGlow 3s ease-in-out infinite alternate;
}

.section-subtitle {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.title-energy-line {
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, transparent, #00ffff, transparent);
    margin: 0 auto;
    animation: energyLinePulse 2s ease-in-out infinite;
}

@keyframes titleGlow {
    0% { filter: brightness(1) drop-shadow(0 0 20px rgba(0, 255, 255, 0.3)); }
    100% { filter: brightness(1.3) drop-shadow(0 0 40px rgba(0, 255, 255, 0.6)); }
}

@keyframes energyLinePulse {
    0%, 100% { opacity: 0.5; transform: scaleX(1); }
    50% { opacity: 1; transform: scaleX(1.5); }
}

/* 解决方案网格 */
.solutions-grid-matrix {
    position: relative;
    z-index: 10;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
    margin-bottom: 5rem;
}

/* 解决方案卡片 */
.solution-quantum-card {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2.5rem 2rem;
    transition: all 0.4s ease;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.solution-quantum-card:hover {
    transform: translateY(-10px);
    border-color: rgba(0, 255, 255, 0.5);
    box-shadow: 0 25px 50px rgba(0, 255, 255, 0.2);
}

.card-energy-border {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff7f, #ffd700);
    border-radius: 20px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.solution-quantum-card:hover .card-energy-border {
    opacity: 0.3;
}

.card-content-core {
    position: relative;
    z-index: 2;
}

/* 图标区域 */
.solution-icon-zone {
    position: relative;
    text-align: center;
    margin-bottom: 2rem;
}

.icon-quantum-core {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto;
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
}

.icon-pulse-ring {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border: 2px solid rgba(0, 255, 255, 0.4);
    border-radius: 50%;
    animation: iconPulse 2s ease-in-out infinite;
}

.icon-energy-field {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.solution-quantum-card:hover .icon-energy-field {
    opacity: 1;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); opacity: 0.6; }
    50% { transform: scale(1.1); opacity: 1; }
}

/* 标题区域 */
.solution-title-zone {
    text-align: center;
    margin-bottom: 1.5rem;
}

.solution-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.5rem;
    font-family: 'Orbitron', monospace;
}

.title-underline-effect {
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00ffff, transparent);
    margin: 0 auto;
    animation: underlinePulse 2s ease-in-out infinite;
}

@keyframes underlinePulse {
    0%, 100% { opacity: 0.5; transform: scaleX(1); }
    50% { opacity: 1; transform: scaleX(1.2); }
}

/* 描述区域 */
.solution-desc-zone {
    margin-bottom: 2rem;
}

.solution-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    font-size: 1rem;
    text-align: center;
}

/* 特性标签 */
.solution-features-zone {
    margin-bottom: 2rem;
}

.features-grid {
    display: flex;
    justify-content: center;
    gap: 0.8rem;
    flex-wrap: wrap;
}

.feature-tag {
    padding: 0.4rem 1rem;
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 20px;
    color: #00ffff;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.feature-tag:hover {
    background: rgba(0, 255, 255, 0.2);
    border-color: rgba(0, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* 行动按钮 */
.solution-action-zone {
    text-align: center;
}

.btn-solution-explore {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1rem 2rem;
    background: transparent;
    border: 2px solid rgba(0, 255, 255, 0.4);
    border-radius: 30px;
    color: #00ffff;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.4s ease;
    overflow: hidden;
}

.btn-solution-explore:hover {
    border-color: rgba(0, 255, 255, 0.8);
    color: #ffffff;
    transform: translateY(-2px);
}

.btn-quantum-arrow {
    transition: transform 0.3s ease;
}

.btn-solution-explore:hover .btn-quantum-arrow {
    transform: translateX(5px);
}

.btn-hover-effect {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.btn-solution-explore:hover .btn-hover-effect {
    left: 100%;
}

/* 卡片悬浮效果 */
.card-hover-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.solution-quantum-card:hover .card-hover-glow {
    opacity: 1;
}

.card-data-streams {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 48%, rgba(0, 255, 255, 0.05) 49%, rgba(0, 255, 255, 0.05) 51%, transparent 52%);
    background-size: 20px 20px;
    animation: cardStreamFlow 8s linear infinite;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.solution-quantum-card:hover .card-data-streams {
    opacity: 1;
}

@keyframes cardStreamFlow {
    0% { background-position: 0 0; }
    100% { background-position: 20px 20px; }
}

/* 底部行动区域 */
.bottom-action-matrix {
    position: relative;
    z-index: 10;
    padding: 3rem 2rem;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 20px;
    border: 1px solid rgba(0, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.action-content-core {
    position: relative;
    z-index: 2;
}

.action-title {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    font-family: 'Orbitron', monospace;
}

.title-quantum-effect {
    background: linear-gradient(135deg, #ffffff 0%, #00ffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.action-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2.5rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.action-buttons-group {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.btn-action-primary,
.btn-action-secondary {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1.2rem 2.5rem;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.4s ease;
    overflow: hidden;
}

.btn-action-primary {
    background: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
    color: white;
}

.btn-action-secondary {
    background: transparent;
    border: 2px solid rgba(0, 255, 255, 0.5);
    color: #00ffff;
}

.btn-action-primary:hover,
.btn-action-secondary:hover {
    transform: translateY(-3px);
    color: white;
}

.btn-action-primary:hover {
    box-shadow: 0 15px 30px rgba(0, 255, 255, 0.4);
}

.btn-action-secondary:hover {
    background: rgba(0, 255, 255, 0.1);
    border-color: rgba(0, 255, 255, 0.8);
}

.btn-energy-wave {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transition: all 0.6s ease;
}

.btn-action-primary:hover .btn-energy-wave {
    width: 200%;
    height: 200%;
}

.btn-neural-effect {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.btn-action-secondary:hover .btn-neural-effect {
    left: 100%;
}

.action-bg-effects {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 0, 255, 0.05) 0%, transparent 50%);
    animation: actionBgPulse 4s ease-in-out infinite;
}

@keyframes actionBgPulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* 区域装饰元素 */
.showcase-decorations {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 5;
    pointer-events: none;
}

.deco-element {
    position: absolute;
    width: 60px;
    height: 60px;
    border: 1px solid rgba(0, 255, 255, 0.2);
    animation: decoFloat 6s ease-in-out infinite;
}

.deco-1 {
    top: 10%;
    left: 5%;
    border-radius: 50%;
    animation-delay: 0s;
}

.deco-2 {
    top: 70%;
    right: 8%;
    transform: rotate(45deg);
    animation-delay: 2s;
}

.deco-3 {
    bottom: 20%;
    left: 10%;
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    animation-delay: 4s;
}

@keyframes decoFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.3; }
    50% { transform: translateY(-20px) rotate(180deg); opacity: 0.8; }
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .quantum-mega-title {
        font-size: 4rem;
    }

    .capability-grid-system {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .quantum-content-core {
        padding: 100px 0 60px;
        min-height: 70vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .quantum-mega-title {
        font-size: 2.8rem;
    }

    .title-segment {
        margin-bottom: 0.5rem;
    }

    .subtitle-main {
        font-size: 1.1rem;
    }

    .subtitle-accent {
        font-size: 1rem;
    }

    .capability-grid-system {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .console-interface {
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
    }

    .btn-quantum-primary,
    .btn-quantum-secondary {
        width: 100%;
        max-width: 300px;
        padding: 1.2rem 2rem;
    }

    /* 解决方案展示区域移动端适配 */
    .solutions-showcase-zone {
        padding: 80px 0;
    }

    .section-mega-title {
        font-size: 2.5rem;
    }

    .section-subtitle {
        font-size: 1.1rem;
        padding: 0 1rem;
    }

    .solutions-grid-matrix {
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 0 1rem;
    }

    .solution-quantum-card {
        padding: 2rem 1.5rem;
    }

    .icon-quantum-core {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .solution-title {
        font-size: 1.3rem;
    }

    .action-title {
        font-size: 1.8rem;
    }

    .action-buttons-group {
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
    }

    .btn-action-primary,
    .btn-action-secondary {
        width: 100%;
        max-width: 280px;
        padding: 1rem 2rem;
    }

    /* 移动端禁用复杂动画 */
    .quantum-particles,
    .holographic-grid,
    .energy-pulse-waves,
    .digital-rain,
    .neural-network-bg,
    .data-stream-lines,
    .floating-particles {
        display: none;
    }

    .bg-primary-layer {
        opacity: 0.1;
    }

    .showcase-bg-system {
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(26, 26, 46, 0.6) 100%);
    }
}

@media (max-width: 480px) {
    .quantum-content-core {
        padding: 80px 0 50px;
        min-height: 60vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .quantum-mega-title {
        font-size: 2.2rem;
    }

    /* 解决方案展示区域小屏幕适配 */
    .solutions-showcase-zone {
        padding: 60px 0;
    }

    .section-mega-title {
        font-size: 2rem;
    }

    .section-subtitle {
        font-size: 1rem;
        padding: 0 1.5rem;
    }

    .solutions-grid-matrix {
        padding: 0 1.5rem;
        gap: 1.5rem;
    }

    .solution-quantum-card {
        padding: 1.5rem 1rem;
    }

    .icon-quantum-core {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .solution-title {
        font-size: 1.2rem;
    }

    .solution-description {
        font-size: 0.9rem;
    }

    .feature-tag {
        padding: 0.3rem 0.8rem;
        font-size: 0.8rem;
    }

    .btn-solution-explore {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    .bottom-action-matrix {
        padding: 2rem 1rem;
        margin: 0 1rem;
    }

    .action-title {
        font-size: 1.5rem;
    }

    .action-subtitle {
        font-size: 1rem;
    }

    .btn-action-primary,
    .btn-action-secondary {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }
}
</style>

<!-- 🚀 量子级JavaScript系统 -->
<script src="assets/js/aos.js"></script>
<script>

// 初始化AOS动画系统
AOS.init({
    duration: 1000,
    easing: 'ease-out-cubic',
    once: true,
    offset: 100,
    delay: 100
});

// 解决方案页面系统
document.addEventListener('DOMContentLoaded', function() {
    // 添加页面类名
    document.body.classList.add('solutions-page', 'page-loaded');

    // 平滑滚动
    initSmoothScrolling();

    // 初始化解决方案链接
    initSolutionLinks();
});

// 平滑滚动
function initSmoothScrolling() {
    const scrollLinks = document.querySelectorAll('a[href^="#"]');

    scrollLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            // 只对锚点链接进行平滑滚动处理
            const href = this.getAttribute('href');
            if (href.startsWith('#') && href.length > 1) {
                e.preventDefault();
                const targetId = href.substring(1);
                const target = document.getElementById(targetId);

                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });
    });
}

// 确保解决方案链接正常工作
function initSolutionLinks() {
    const solutionLinks = document.querySelectorAll('.btn-solution-explore');

    solutionLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            // 确保链接正常跳转，不阻止默认行为
            const href = this.getAttribute('href');
            if (href && href.startsWith('/solutions/')) {
                // 让链接正常跳转
                window.location.href = href;
            }
        });
    });
}

// 页面加载完成
window.addEventListener('load', function() {
    document.body.style.opacity = '1';
    document.body.classList.add('page-loaded');
});

// 页面加载样式
const loadStyle = document.createElement('style');
loadStyle.textContent = `
    body.solutions-page {
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
    }

    body.solutions-page.page-loaded {
        opacity: 1;
    }
`;
document.head.appendChild(loadStyle);
</script>

</main>

    <!-- 页脚 -->
    <footer class="footer">
        <!-- 主要页脚内容 -->
        <div class="footer-main">
            <div class="container">
                <div class="row">
                    <!-- 公司信息 -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="footer-widget">
                            <div class="footer-logo">
                                <img src="<?php echo asset('assets/images/logo.png'); ?>" alt="<?php echo htmlentities((string) (isset($siteConfig['site_name']) && ($siteConfig['site_name'] !== '')?$siteConfig['site_name']:'三只鱼网络')); ?>">
                            </div>
                            <p class="footer-desc">
                                <?php echo htmlentities((string) (isset($siteConfig['site_description']) && ($siteConfig['site_description'] !== '')?$siteConfig['site_description']:'我们提供专业的企业解决方案，助力企业数字化转型，打造专业品牌形象，提供一站式服务支持。')); ?>
                            </p>
                            
                            <!-- 微信二维码 -->
                            <div class="footer-qrcode">
                                <img src="<?php echo asset('assets/images/weixin.png'); ?>" alt="微信二维码" class="qrcode-img">
                                <p class="qrcode-text">扫码关注微信</p>
                            </div>
                            
                            <div class="footer-social">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 解决方案 -->
                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">解决方案</h5>
                            <ul class="footer-links">
                                <?php if(is_array($footerSolutions) || $footerSolutions instanceof \think\Collection || $footerSolutions instanceof \think\Paginator): $i = 0; $__LIST__ = $footerSolutions;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$solution): $mod = ($i % 2 );++$i;?>
                                <li><a href="/solutions/<?php echo htmlentities((string) $solution['link_value']); ?>"><?php echo htmlentities((string) $solution['name']); ?></a></li>
                                <?php endforeach; endif; else: echo "" ;endif; if(empty($footerSolutions) || (($footerSolutions instanceof \think\Collection || $footerSolutions instanceof \think\Paginator ) && $footerSolutions->isEmpty())): ?>
                                <li><a href="/solutions">暂无解决方案</a></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 产品分类 -->
                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">产品分类</h5>
                            <ul class="footer-links">
                                <?php if(is_array($footerProductCategories) || $footerProductCategories instanceof \think\Collection || $footerProductCategories instanceof \think\Paginator): $i = 0; $__LIST__ = $footerProductCategories;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$category): $mod = ($i % 2 );++$i;?>
                                <li><a href="/products?category=<?php echo htmlentities((string) $category['slug']); ?>"><?php echo htmlentities((string) $category['name']); ?></a></li>
                                <?php endforeach; endif; else: echo "" ;endif; if(empty($footerProductCategories) || (($footerProductCategories instanceof \think\Collection || $footerProductCategories instanceof \think\Paginator ) && $footerProductCategories->isEmpty())): ?>
                                <li><a href="/products">暂无产品分类</a></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 普通页面 -->
                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">帮助中心</h5>
                            <ul class="footer-links">
                                <?php if(is_array($footerDiyPages) || $footerDiyPages instanceof \think\Collection || $footerDiyPages instanceof \think\Paginator): $i = 0; $__LIST__ = $footerDiyPages;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$page): $mod = ($i % 2 );++$i;?>
                                <li><a href="/page/<?php echo htmlentities((string) $page['slug']); ?>"><?php echo htmlentities((string) $page['name']); ?></a></li>
                                <?php endforeach; endif; else: echo "" ;endif; if(empty($footerDiyPages) || (($footerDiyPages instanceof \think\Collection || $footerDiyPages instanceof \think\Paginator ) && $footerDiyPages->isEmpty())): ?>
                                <li><a href="/about">关于我们</a></li>
                                <li><a href="/contact">联系我们</a></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 联系信息 -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">联系我们</h5>
                            <div class="footer-contact">
                                <div class="contact-item">
                                    <i data-lucide="map-pin"></i>
                                    <span><?php echo htmlentities((string) (isset($siteConfig['company_address']) && ($siteConfig['company_address'] !== '')?$siteConfig['company_address']:'北京市朝阳区科技园区')); ?></span>
                                </div>
                                <div class="contact-item">
                                    <i data-lucide="phone"></i>
                                    <span><?php echo htmlentities((string) (isset($siteConfig['company_phone']) && ($siteConfig['company_phone'] !== '')?$siteConfig['company_phone']:'1800001111')); ?></span>
                                </div>
                                <div class="contact-item">
                                    <i data-lucide="mail"></i>
                                    <span><?php echo htmlentities((string) (isset($siteConfig['company_email']) && ($siteConfig['company_email'] !== '')?$siteConfig['company_email']:'<EMAIL>')); ?></span>
                                </div>
                                <div class="contact-item">
                                    <i data-lucide="message-square"></i>
                                    <span><?php echo htmlentities((string) (isset($siteConfig['company_qq']) && ($siteConfig['company_qq'] !== '')?$siteConfig['company_qq']:'*********')); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 版权信息 -->
        <div class="footer-bottom">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="copyright">
                            <p>&copy; <?php echo date('Y'); ?> <?php echo htmlentities((string) (isset($siteConfig['site_name']) && ($siteConfig['site_name'] !== '')?$siteConfig['site_name']:'三只鱼科技有限公司')); ?>. 版权所有</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="footer-links-bottom">
                            <a href="/privacy">隐私政策</a>
                            <a href="/terms">服务条款</a>
                            <a href="/sitemap">网站地图</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- 返回顶部按钮 -->
    <div class="back-to-top" id="backToTop">
        <i class="fa fa-angle-up"></i>
    </div>

    <!-- 在线客服 -->
    <div class="online-service">
        <div class="service-btn" id="serviceBtn">
            <i data-lucide="message-circle"></i>
            <span>在线客服</span>
        </div>
        <div class="service-panel" id="servicePanel">
            <div class="service-header">
                <h6>在线客服</h6>
                <button class="close-btn" id="closeService">&times;</button>
            </div>
            <div class="service-content">
                <div class="service-item">
                    <div class="service-icon qq-icon">
                        <i data-lucide="message-circle"></i>
                    </div>
                    <div class="service-info">
                        <h6>QQ咨询</h6>
                        <p><?php echo htmlentities((string) (isset($siteConfig['company_qq']) && ($siteConfig['company_qq'] !== '')?$siteConfig['company_qq']:'*********')); ?></p>
                    </div>
                    <a href="http://wpa.qq.com/msgrd?v=3&uin=<?php echo htmlentities((string) (isset($siteConfig['company_qq']) && ($siteConfig['company_qq'] !== '')?$siteConfig['company_qq']:'*********')); ?>&site=qq&menu=yes" target="_blank" class="btn btn-sm btn-primary">咨询</a>
                </div>
                <div class="service-item">
                    <div class="service-icon wechat-icon">
                        <i data-lucide="smartphone"></i>
                    </div>
                    <div class="service-info">
                        <h6>微信咨询</h6>
                        <p><?php echo htmlentities((string) (isset($siteConfig['company_wechat']) && ($siteConfig['company_wechat'] !== '')?$siteConfig['company_wechat']:'hsdj37')); ?></p>
                    </div>
                    <button class="btn btn-sm btn-success" onclick="showWechatQR()">扫码</button>
                </div>
                <div class="service-item">
                    <div class="service-icon phone-icon">
                        <i data-lucide="phone"></i>
                    </div>
                    <div class="service-info">
                        <h6>电话咨询</h6>
                        <p><?php echo htmlentities((string) (isset($siteConfig['company_phone']) && ($siteConfig['company_phone'] !== '')?$siteConfig['company_phone']:'************')); ?></p>
                    </div>
                    <a href="tel:<?php echo htmlentities((string) (isset($siteConfig['company_phone']) && ($siteConfig['company_phone'] !== '')?$siteConfig['company_phone']:'************')); ?>" class="btn btn-sm btn-warning">拨打</a>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="<?php echo asset('assets/js/jquery.min.js'); ?>"></script>
    <script src="<?php echo asset('assets/js/bootstrap.bundle.min.js'); ?>"></script> 
    <script src="<?php echo asset('assets/js/swiper-bundle.min.js'); ?>"></script>
    <script src="<?php echo asset('assets/js/main.js'); ?>"></script>
    <script src="<?php echo asset('assets/js/hero-slider.js'); ?>"></script>
    <script src="<?php echo asset('assets/js/scroll-handler.js'); ?>"></script>

    <!-- 初始化Lucide图标 -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    });
    </script>

</body>
</html>

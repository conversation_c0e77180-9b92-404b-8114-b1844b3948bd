<!-- 顶部导航 -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <div class="navbar-left">
            <button class="btn btn-link d-md-none" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
            <a class="navbar-brand" href="/admin">
                <i class="fas fa-cogs"></i>
                <span>后台管理</span>
            </a>
        </div>

        <div class="navbar-nav">
            <a class="nav-link" href="/" target="_blank" style="margin-right: 15px;">
                <i class="fas fa-external-link-alt"></i>
                <span>查看网站</span>
            </a>
            <a class="nav-link" href="javascript:void(0);" onclick="clearAllCache()" style="margin-right: 15px; color: #28a745;">
                <i class="fas fa-broom"></i>
                <span>清理缓存</span>
            </a>
            <span class="nav-link" style="margin-right: 15px; color: rgba(255,255,255,0.8);">
                <i class="fas fa-user"></i>
                <span>
                    {if condition="isset($admin_info['real_name']) && $admin_info['real_name']"}
                        {$admin_info.real_name}
                    {elseif condition="isset($admin_info['username']) && $admin_info['username']"/}
                        {$admin_info.username}
                    {else/}
                        系统管理员
                    {/if}
                </span>
            </span>
            <a class="nav-link" href="javascript:void(0);" onclick="confirmLogout()" style="color: #ff6b6b;">
                <i class="fas fa-sign-out-alt"></i>
                <span>退出登录</span>
            </a>
        </div>
    </div>
</nav>

<style>
/* 缓存清理模态框样式 */
.cache-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.cache-modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.cache-modal {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border-radius: 15px;
    padding: 0;
    min-width: 400px;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(0, 255, 255, 0.3);
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.cache-modal-overlay.show .cache-modal {
    transform: scale(1);
}

.cache-modal-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 15px 15px 0 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.cache-modal-header i {
    font-size: 1.5rem;
}

.cache-modal-header h4 {
    margin: 0;
    font-weight: 600;
    font-family: 'Orbitron', monospace;
}

.cache-modal-body {
    padding: 2rem;
    text-align: center;
    color: #ffffff;
}

.loading-spinner {
    margin-bottom: 1rem;
}

.loading-spinner i {
    font-size: 2rem;
    color: #28a745;
}

.cache-modal-body p {
    margin: 0;
    font-size: 1rem;
    line-height: 1.5;
}

/* 退出登录模态框样式优化 */
.logout-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.logout-modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.logout-modal {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border-radius: 15px;
    padding: 0;
    min-width: 400px;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 107, 107, 0.3);
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.logout-modal-overlay.show .logout-modal {
    transform: scale(1);
}

.logout-modal-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 15px 15px 0 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logout-modal-header i {
    font-size: 1.5rem;
}

.logout-modal-header h4 {
    margin: 0;
    font-weight: 600;
    font-family: 'Orbitron', monospace;
}

.logout-modal-body {
    padding: 2rem;
    text-align: center;
    color: #ffffff;
}

.logout-modal-body p {
    margin: 0;
    font-size: 1.1rem;
    line-height: 1.5;
}

.logout-modal-footer {
    padding: 1.5rem 2rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.btn-cancel, .btn-confirm {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-cancel {
    background: #6c757d;
    color: white;
}

.btn-cancel:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-confirm {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
}

.btn-confirm:hover {
    background: linear-gradient(135deg, #ee5a52 0%, #dc3545 100%);
    transform: translateY(-2px);
}
</style>

<script>
// 退出登录确认对话框
function confirmLogout() {
    // 创建自定义确认对话框
    const modal = document.createElement('div');
    modal.className = 'logout-modal-overlay';
    modal.innerHTML = `
        <div class="logout-modal">
            <div class="logout-modal-header">
                <i class="fas fa-sign-out-alt"></i>
                <h4>确认退出登录</h4>
            </div>
            <div class="logout-modal-body">
                <p>您确定要退出登录吗？</p>
            </div>
            <div class="logout-modal-footer">
                <button class="btn-cancel" onclick="closeLogoutModal()">
                    <i class="fas fa-times"></i> 取消
                </button>
                <button class="btn-confirm" onclick="doLogout()">
                    <i class="fas fa-check"></i> 确认退出
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 添加动画效果
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
}

function closeLogoutModal() {
    const modal = document.querySelector('.logout-modal-overlay');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

function doLogout() {
    window.location.href = '/admin/logout';
}

// 点击遮罩层关闭对话框
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('logout-modal-overlay')) {
        closeLogoutModal();
    }
});

// 清理缓存功能
function clearAllCache() {
    // 创建加载提示
    const loadingModal = document.createElement('div');
    loadingModal.className = 'cache-modal-overlay';
    loadingModal.innerHTML = `
        <div class="cache-modal">
            <div class="cache-modal-header">
                <i class="fas fa-broom"></i>
                <h4>正在清理缓存</h4>
            </div>
            <div class="cache-modal-body">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                </div>
                <p id="cache-status">正在清理系统缓存，请稍候...</p>
            </div>
        </div>
    `;

    document.body.appendChild(loadingModal);

    // 添加动画效果
    setTimeout(() => {
        loadingModal.classList.add('show');
    }, 10);

    // 发起清理缓存请求
    fetch('/api/clear-cache', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        const statusElement = document.getElementById('cache-status');
        if (data.success) {
            statusElement.innerHTML = '<i class="fas fa-check-circle" style="color: #28a745;"></i> 缓存清理成功！';
            setTimeout(() => {
                closeCacheModal();
                // 可选：刷新当前页面
                // window.location.reload();
            }, 1500);
        } else {
            statusElement.innerHTML = '<i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i> 清理失败：' + (data.message || '未知错误');
            setTimeout(() => {
                closeCacheModal();
            }, 3000);
        }
    })
    .catch(error => {
        console.error('清理缓存失败:', error);
        const statusElement = document.getElementById('cache-status');
        statusElement.innerHTML = '<i class="fas fa-times-circle" style="color: #dc3545;"></i> 网络错误，请稍后重试';
        setTimeout(() => {
            closeCacheModal();
        }, 3000);
    });
}

function closeCacheModal() {
    const modal = document.querySelector('.cache-modal-overlay');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

</script>

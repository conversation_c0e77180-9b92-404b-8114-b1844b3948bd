{include file="common/header-bg" /}

<!-- 产品详情页面样式 -->
<link rel="stylesheet" href="{:asset('assets/css/products.css')}?v=7.1">

<!-- 强制模板刷新 - {date('Y-m-d H:i:s')} -->

<!-- 产品详情页面 -->
<div class="page-top-spacing">
    <!-- 面包屑导航 -->
    <div class="breadcrumb-section">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="/">
                            <i data-lucide="home"></i>
                            首页
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="/products">
                            <i data-lucide="package"></i>
                            产品中心
                        </a>
                    </li>
                    {if condition="$categoryInfo"}
                    <li class="breadcrumb-item">
                        <a href="/products?category={$categoryInfo.slug}">
                            {$categoryInfo.name}
                        </a>
                    </li>
                    {/if}
                    <li class="breadcrumb-item active" aria-current="page">
                        {$productDetail.name}
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- 产品主要内容 -->
    <div class="product-detail-main">
        <div class="container">
            <div class="row g-5">
                <!-- 产品图片展示 -->
                <div class="col-lg-6">
                    <div class="product-gallery sticky-top">
                        <!-- 主图展示 -->
                        <div class="main-image-container">
                            {if condition="$productDetail.image"}
                            <img src="{$productDetail.image}" alt="{$productDetail.name}" class="main-product-image" id="mainImage">
                            {else/}
                            <div class="no-image-placeholder">
                                <i data-lucide="image"></i>
                                <span>暂无图片</span>
                            </div>
                            {/if}
                            
                            <!-- 图片放大镜效果 -->
                            <div class="image-zoom-overlay">
                                <i data-lucide="zoom-in"></i>
                                <span>点击放大</span>
                            </div>
                        </div>
                        
                        <!-- 缩略图网格 -->
                        {if condition="$gallery && count($gallery) > 0"}
                        <div class="thumbnail-gallery">
                            <div class="thumbnail-grid">
                                <div class="thumbnail-item active" data-image="{$productDetail.image}">
                                    <img src="{$productDetail.image}" alt="{$productDetail.name}">
                                </div>
                                {volist name="gallery" id="image"}
                                <div class="thumbnail-item" data-image="{$image}">
                                    <img src="{$image}" alt="{$productDetail.name}">
                                </div>
                                {/volist}
                            </div>
                        </div>
                        {/if}
                    </div>
                </div>

                <!-- 产品信息 -->
                <div class="col-lg-6">
                    <div class="product-info-panel">
                        <!-- 产品标识徽章 -->
                        <div class="product-badges-section">
                            {if condition="$productDetail.is_featured"}
                            <span class="product-badge badge-featured">
                                <i data-lucide="star"></i>
                                推荐产品
                            </span>
                            {/if}
                            {if condition="$productDetail.is_hot"}
                            <span class="product-badge badge-hot">
                                <i data-lucide="flame"></i>
                                热门产品
                            </span>
                            {/if}
                            {if condition="$productDetail.is_new"}
                            <span class="product-badge badge-new">
                                <i data-lucide="sparkles"></i>
                                新品上市
                            </span>
                            {/if}
                        </div>

                        <!-- 产品标题 -->
                        <h1 class="product-main-title">{$productDetail.name}</h1>

                        <!-- 产品分类标签 -->
                        {if condition="$categoryInfo"}
                        <div class="product-category-tag">
                            <a href="/products?category={$categoryInfo.slug}" class="category-tag-link">
                                <i data-lucide="tag"></i>
                                {$categoryInfo.name}
                            </a>
                        </div>
                        {/if}

                        <!-- 产品简介卡片 -->
                        {if condition="$productDetail.short_description"}
                        <div class="product-summary-card">
                            <div class="summary-icon">
                                <i data-lucide="info"></i>
                            </div>
                            <div class="summary-content">
                                <p>{$productDetail.short_description}</p>
                            </div>
                        </div>
                        {/if}

                        <!-- 产品价格展示 -->
                        {if condition="$productDetail.price > 0"}
                        <div class="product-pricing-card">
                            <div class="price-header">
                                
                                <span>产品价格</span>
                            </div>
                            <div class="price-content">
                                <span class="current-price">¥{$productDetail.price}</span>
                                {if condition="$productDetail.original_price > $productDetail.price"}
                                <span class="original-price">¥{$productDetail.original_price}</span>
                                <span class="discount-badge">
                                    省¥{$productDetail.original_price - $productDetail.price}
                                </span>
                                {/if}
                            </div>
                        </div>
                        {/if}

                        <!-- 产品元信息网格 -->
                        <div class="product-meta-grid">
                            <div class="meta-card">
                                <div class="meta-icon">
                                    <i data-lucide="package"></i>
                                </div>
                                <div class="meta-content">
                                    <span class="meta-label">库存状态</span>
                                    <span class="meta-value stock-{$productDetail.stock_status}">
                                        {$productDetail.stock_status_text}
                                    </span>
                                </div>
                            </div>
                            
                            <div class="meta-card">
                                <div class="meta-icon">
                                    <i data-lucide="eye"></i>
                                </div>
                                <div class="meta-content">
                                    <span class="meta-label">浏览次数</span>
                                    <span class="meta-value">{$productDetail.views}</span>
                                </div>
                            </div>
                            
                            <div class="meta-card">
                                <div class="meta-icon">
                                    <i data-lucide="calendar"></i>
                                </div>
                                <div class="meta-content">
                                    <span class="meta-label">发布时间</span>
                                    <span class="meta-value">{$productDetail.created_at|date='Y-m-d'}</span>
                                </div>
                            </div>
                            
                            {if condition="$productDetail.tags"}
                            <div class="meta-card meta-tags">
                                <div class="meta-icon">
                                    <i data-lucide="hash"></i>
                                </div>
                                <div class="meta-content">
                                    <span class="meta-label">产品标签</span>
                                    <div class="tags-list">
                                        {php}
                                        $tags = explode(',', $productDetail['tags']);
                                        foreach($tags as $tag) {
                                            echo '<span class="tag-item">' . trim($tag) . '</span>';
                                        }
                                        {/php}
                                    </div>
                                </div>
                            </div>
                            {/if}
                        </div>

                        <!-- 操作按钮组 -->
                        <div class="product-actions-section">
                            <div class="action-buttons-group">
                                <button class="btn btn-action-primary" onclick="contactUs()">
                                    <i data-lucide="message-circle"></i>
                                    立即咨询
                                </button>
                                <button class="btn btn-action-secondary" onclick="shareProduct()">
                                    <i data-lucide="share-2"></i>
                                    分享产品
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 产品详细信息选项卡 -->
    <div class="product-details-section">
        <div class="container">
            <div class="details-tabs-container">
                <!-- 选项卡导航 -->
                <ul class="nav nav-tabs details-nav" id="productTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="description-tab" data-bs-toggle="tab" data-bs-target="#description" type="button" role="tab">
                            <i data-lucide="file-text"></i>
                            <span>产品详情</span>
                        </button>
                    </li>
                    {if condition="$features && count($features) > 0"}
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="features-tab" data-bs-toggle="tab" data-bs-target="#features" type="button" role="tab">
                            <i data-lucide="check-circle"></i>
                            <span>产品特性</span>
                        </button>
                    </li>
                    {/if}
                    {if condition="$specifications && count($specifications) > 0"}
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="specifications-tab" data-bs-toggle="tab" data-bs-target="#specifications" type="button" role="tab">
                            <i data-lucide="settings"></i>
                            <span>技术规格</span>
                        </button>
                    </li>
                    {/if}
                </ul>

                <!-- 选项卡内容 -->
                <div class="tab-content details-content" id="productTabsContent">
                    <!-- 产品详情 -->
                    <div class="tab-pane fade show active" id="description" role="tabpanel">
                        <div class="detail-content-wrapper">
                            {if condition="$productDetail.description"}
                            <div class="rich-content">
                                {$productDetail.description|raw}
                            </div>
                            {else/}
                            <div class="empty-content">
                                <i data-lucide="file-text"></i>
                                <p>暂无详细描述信息</p>
                            </div>
                            {/if}
                        </div>
                    </div>

                    <!-- 产品特性 -->
                    {if condition="$features && count($features) > 0"}
                    <div class="tab-pane fade" id="features" role="tabpanel">
                        <div class="features-grid-container">
                            {volist name="features" id="feature"}
                            <div class="feature-card">
                                <div class="feature-icon-wrapper">
                                    <i data-lucide="check-circle"></i>
                                </div>
                                <div class="feature-content">
                                    <h4 class="feature-title">{$feature.title}</h4>
                                    <p class="feature-description">{$feature.description}</p>
                                </div>
                            </div>
                            {/volist}
                        </div>
                    </div>
                    {/if}

                    <!-- 技术规格 -->
                    {if condition="$specifications && count($specifications) > 0"}
                    <div class="tab-pane fade" id="specifications" role="tabpanel">
                        <div class="specifications-wrapper">
                            <div class="spec-table-container">
                                <table class="table spec-table">
                                    <tbody>
                                        {volist name="specifications" id="spec"}
                                        <tr>
                                            <td class="spec-label">{$spec.name}</td>
                                            <td class="spec-value">{$spec.value}</td>
                                        </tr>
                                        {/volist}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {/if}
                </div>
            </div>
        </div>
    </div>

    <!-- 相关产品推荐 -->
    {if condition="$relatedProducts && count($relatedProducts) > 0"}
    <div class="related-products-section">
        <div class="container">
            <div class="section-header">
                <div class="section-title-wrapper">
                    <h3 class="section-title">
                        <i data-lucide="package"></i>
                        相关产品推荐
                    </h3>
                    <p class="section-subtitle">您可能还感兴趣的其他产品</p>
                </div>
            </div>

            <div class="related-products-grid">
                {volist name="relatedProducts" id="item"}
                <div class="related-product-card">
                    <div class="related-product-image">
                        {if condition="$item.image"}
                        <img src="{$item.image}" alt="{$item.name}">
                        {else/}
                        <div class="no-image">
                            <i data-lucide="image"></i>
                        </div>
                        {/if}
                        
                        <div class="product-overlay">
                            <a href="/products/{$item.slug}" class="overlay-btn">
                                <i data-lucide="eye"></i>
                                查看详情
                            </a>
                        </div>
                        
                        <div class="products-page-badges">
                            {if condition="$item.is_featured"}
                            <span class="badge badge-featured">推荐</span>
                            {/if}
                            {if condition="$item.is_hot"}
                            <span class="badge badge-hot">热门</span>
                            {/if}
                            {if condition="$item.is_new"}
                            <span class="badge badge-new">新品</span>
                            {/if}
                        </div>
                    </div>
                    
                    <div class="related-product-info">
                        <h4 class="related-product-name">
                            <a href="/products/{$item.slug}">{$item.name}</a>
                        </h4>
                        
                        {if condition="$item.short_description"}
                        <p class="related-product-description">{$item.short_description}</p>
                        {/if}
                        
                        {if condition="$item.price > 0"}
                        <div class="related-product-price">
                            <span class="current-price">¥{$item.price}</span>
                            {if condition="$item.original_price > $item.price"}
                            <span class="original-price">¥{$item.original_price}</span>
                            {/if}
                        </div>
                        {/if}
                    </div>
                </div>
                {/volist}
            </div>
        </div>
    </div>
    {/if}
</div>

<!-- JavaScript -->
<script>
// 产品详情页面交互功能
document.addEventListener('DOMContentLoaded', function() {
    console.log('产品详情页面初始化...');
    
    // 图片切换功能
    initImageGallery();
    
    // 图片放大功能
    initImageZoom();
    
    // 选项卡切换动画
    initTabAnimations();
    
    // 初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    console.log('产品详情页面初始化完成');
});

// 图片画廊功能
function initImageGallery() {
    const thumbnails = document.querySelectorAll('.thumbnail-item');
    const mainImage = document.getElementById('mainImage');
    
    if (!mainImage || thumbnails.length === 0) return;
    
    thumbnails.forEach(function(thumbnail) {
        thumbnail.addEventListener('click', function() {
            // 移除所有活动状态
            thumbnails.forEach(t => t.classList.remove('active'));
            
            // 添加当前活动状态
            this.classList.add('active');
            
            // 切换主图
            const newImageSrc = this.dataset.image;
            if (newImageSrc) {
                mainImage.src = newImageSrc;
                
                // 添加切换动画
                mainImage.style.opacity = '0';
                setTimeout(() => {
                    mainImage.style.opacity = '1';
                }, 150);
            }
        });
    });
}

// 图片放大功能
function initImageZoom() {
    const mainImage = document.getElementById('mainImage');
    const zoomOverlay = document.querySelector('.image-zoom-overlay');
    
    if (!mainImage || !zoomOverlay) return;
    
    mainImage.addEventListener('click', function() {
        // 创建模态框显示大图
        const modal = document.createElement('div');
        modal.className = 'image-modal';
        modal.innerHTML = `
            <div class="modal-backdrop" onclick="closeImageModal()"></div>
            <div class="modal-content">
                <img src="${this.src}" alt="${this.alt}">
                <button class="modal-close" onclick="closeImageModal()">
                    <i data-lucide="x"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(modal);
        document.body.style.overflow = 'hidden';
        
        // 重新初始化图标
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    });
}

// 关闭图片模态框
function closeImageModal() {
    const modal = document.querySelector('.image-modal');
    if (modal) {
        modal.remove();
        document.body.style.overflow = '';
    }
}

// 选项卡动画
function initTabAnimations() {
    const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
    
    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function(e) {
            const targetPane = document.querySelector(e.target.dataset.bsTarget);
            if (targetPane) {
                targetPane.style.opacity = '0';
                targetPane.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    targetPane.style.transition = 'all 0.3s ease';
                    targetPane.style.opacity = '1';
                    targetPane.style.transform = 'translateY(0)';
                }, 50);
            }
        });
    });
}

// 联系我们功能
function contactUs() {
    // 可以跳转到联系页面或打开模态框
    const productName = '{$productDetail.name}';
    const contactUrl = `/contact?product=${encodeURIComponent(productName)}`;
    window.location.href = contactUrl;
}

// 分享产品功能
function shareProduct() {
    const productData = {
        title: '{$productDetail.name}',
        text: '{$productDetail.short_description}',
        url: window.location.href
    };
    
    if (navigator.share) {
        navigator.share(productData).catch(err => {
            console.log('分享失败:', err);
            fallbackShare();
        });
    } else {
        fallbackShare();
    }
}

// 备用分享方法
function fallbackShare() {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(window.location.href).then(() => {
            showToast('产品链接已复制到剪贴板', 'success');
        }).catch(() => {
            showToast('复制失败，请手动复制链接', 'error');
        });
    } else {
        showToast('您的浏览器不支持自动复制，请手动复制链接', 'info');
    }
}

// 收藏功能
function toggleFavorite() {
    const btn = event.target.closest('.btn-action-icon');
    const icon = btn.querySelector('i');
    
    // 切换收藏状态
    if (btn.classList.contains('favorited')) {
        btn.classList.remove('favorited');
        icon.setAttribute('data-lucide', 'heart');
        showToast('已取消收藏', 'info');
    } else {
        btn.classList.add('favorited');
        icon.setAttribute('data-lucide', 'heart');
        btn.classList.add('animate-heart');
        showToast('已添加到收藏', 'success');
        
        setTimeout(() => {
            btn.classList.remove('animate-heart');
        }, 600);
    }
    
    // 重新初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}

// 显示提示消息
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast-message toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i data-lucide="${type === 'success' ? 'check-circle' : type === 'error' ? 'x-circle' : 'info'}"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    // 初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    // 显示动画
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            toast.remove();
        }, 300);
    }, 3000);
}

// ESC键关闭模态框
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeImageModal();
    }
});
</script>

{include file="common/footer" /}

/**
 * 联系信息组件 (Contact Component)
 * 专业的联系方式展示组件，支持多种联系方式的展示和交互功能
 * 
 * @features
 * - 支持电话、邮箱、地址、微信、QQ等多种联系方式
 * - 智能交互：点击拨号、发邮件、复制文本等
 * - 灵活布局：垂直、水平、网格布局
 * - 6种专业样式预设
 * - 响应式设计
 * - 图标选择器
 */

// 联系方式图标库
const contactIconLibrary = [
    '📞', '📱', '☎️', '📧', '✉️', '📮', '📍', '🏢', '🏠', '💬',
    '📲', '🌐', '💻', '📠', '📋', '📝', '🎯', '⭐', '💼', '🏆',
    '🔗', '📊', '📈', '💡', '🚀', '⚡', '🎨', '🔧', '⚙️', '🛠️'
];

// 联系信息背景图库 - 商务风格背景图
const contactBackgroundLibrary = [
    // 现代办公楼
    'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80',

    // 商务会议室
    'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80',

    // 科技网络
    'https://images.unsplash.com/photo-1519904981063-b0cf448d479e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80'
];

// 联系方式类型配置
const contactTypes = {
    phone: { label: '电话', icon: '📞' },
    mobile: { label: '手机', icon: '📱' },
    email: { label: '邮箱', icon: '📧' },
    address: { label: '地址', icon: '📍' },
    wechat: { label: '微信', icon: '💬' },
    qq: { label: 'QQ', icon: '📲' },
    website: { label: '网站', icon: '🌐' },
    fax: { label: '传真', icon: '📠' }
};

// 联系信息样式预设 - 借鉴卡片组件设计
const contactStylePresets = [
    {
        name: '商务专业',
        bgColor: '#ffffff',
        labelColor: '#4a5568',
        valueColor: '#2d3748',
        iconColor: '#667eea',
        borderColor: '#e2e8f0',
        hoverBg: '#f7fafc'
    },
    {
        name: '科技蓝调',
        bgColor: '#1a202c',
        labelColor: '#a0aec0',
        valueColor: '#e2e8f0',
        iconColor: '#4299e1',
        borderColor: '#2d3748',
        hoverBg: '#2d3748'
    },
    {
        name: '清新绿意',
        bgColor: '#f0fff4',
        labelColor: '#2f855a',
        valueColor: '#1a365d',
        iconColor: '#38a169',
        borderColor: '#c6f6d5',
        hoverBg: '#e6fffa'
    },
    {
        name: '温暖橙色',
        bgColor: '#fffaf0',
        labelColor: '#c05621',
        valueColor: '#744210',
        iconColor: '#ed8936',
        borderColor: '#fbd38d',
        hoverBg: '#fef5e7'
    },
    {
        name: '优雅紫调',
        bgColor: '#faf5ff',
        labelColor: '#553c9a',
        valueColor: '#322659',
        iconColor: '#805ad5',
        borderColor: '#d6bcfa',
        hoverBg: '#f3e8ff'
    },
    {
        name: '极简黑白',
        bgColor: '#ffffff',
        labelColor: '#718096',
        valueColor: '#1a202c',
        iconColor: '#2d3748',
        borderColor: '#e2e8f0',
        hoverBg: '#f7fafc'
    }
];

// 联系信息组件模板
const contactComponent = {
    name: '联系信息',
    html: `<div class="contact-container">
        <div class="contact-item">
            <div class="contact-icon">📞</div>
            <div class="contact-content">
                <div class="contact-label">联系电话</div>
                <div class="contact-value">************</div>
            </div>
        </div>
        <div class="contact-item">
            <div class="contact-icon">📧</div>
            <div class="contact-content">
                <div class="contact-label">邮箱地址</div>
                <div class="contact-value"><EMAIL></div>
            </div>
        </div>
    </div>`,
    render: function(component, properties) {
        updateContactDisplay(component, properties);
    },
    properties: {
        // 联系信息数据
        contacts: [
            { type: 'phone', label: '联系电话', value: '************', icon: '📞' },
            { type: 'email', label: '邮箱地址', value: '<EMAIL>', icon: '📧' },
            { type: 'address', label: '公司地址', value: '北京市朝阳区xxx大厦', icon: '📍' },
            { type: 'wechat', label: '微信号', value: 'company_wechat', icon: '💬' }
        ],
        
        // 布局设置
        layout: 'vertical', // vertical, horizontal, grid
        columns: 2,
        itemSpacing: 16,

        // 背景图设置
        showBackgroundImage: true,
        backgroundImageIndex: 0,
        
        // 样式设置
        stylePreset: 'business-professional',
        bgColor: '#ffffff',
        labelColor: '#4a5568',
        valueColor: '#2d3748',
        iconColor: '#667eea',
        borderColor: '#e2e8f0',
        hoverBg: '#f7fafc',
        
        // 尺寸设置
        labelSize: 14,
        valueSize: 16,
        iconSize: 24,
        padding: 32,
        borderRadius: 12,
        maxWidth: 1200,
        

        
        // 效果设置
        shadow: true,
        shadowIntensity: 0.1,
        showBorder: false,
        animation: true
    },
    generateCSS: function(componentId) {
        const component = document.getElementById(componentId);
        if (!component) return '';
        
        const props = component._contactProperties || this.properties;
        
        return `
            #${componentId} .contact-container {
                max-width: ${props.maxWidth}px;
                margin: 0 auto;
                padding: ${props.padding}px;
                background: ${props.bgColor};
                border-radius: ${props.borderRadius}px;
                box-shadow: ${props.shadow ? `0 4px 20px rgba(0,0,0,${props.shadowIntensity})` : 'none'};
                border: ${props.showBorder ? `1px solid ${props.borderColor}` : 'none'};
            }
            
            #${componentId} .contact-item {
                color: ${props.valueColor};
                transition: all 0.3s ease;
            }
            
            #${componentId} .contact-item:hover {
                background: ${props.hoverBg};
            }
            
            #${componentId} .contact-label {
                color: ${props.labelColor};
                font-size: ${props.labelSize}px;
            }
            
            #${componentId} .contact-value {
                color: ${props.valueColor};
                font-size: ${props.valueSize}px;
            }
            
            #${componentId} .contact-icon {
                color: ${props.iconColor};
                font-size: ${props.iconSize}px;
            }
        `;
    }
};

// 生成联系信息属性面板 - 完全借鉴卡片组件设计
function generateContactProperties(component) {
    const props = component._contactProperties || JSON.parse(JSON.stringify(contactComponent.properties));
    
    return `
        <!-- 样式预设 - 直接借鉴卡片组件 -->
        <div class="property-section">
            <h4 class="section-title">快速样式</h4>
            <div class="style-presets">
                ${contactStylePresets.map((preset, index) => {
                    const isActive = props.stylePreset === preset.name.toLowerCase().replace(/\s+/g, '-');
                    return `
                        <div class="style-preset ${isActive ? 'active' : ''}"
                             onclick="applyContactStylePreset('${component.id}', ${index})">
                            <div class="preset-preview" style="background: ${preset.bgColor}; border: 1px solid ${preset.borderColor};">
                                <div style="color: ${preset.iconColor}; font-size: 16px;">📞</div>
                                <div style="color: ${preset.labelColor}; font-size: 10px;">联系电话</div>
                                <div style="color: ${preset.valueColor}; font-size: 11px;">************</div>
                            </div>
                            <span class="preset-name">${preset.name}</span>
                        </div>
                    `;
                }).join('')}
            </div>
        </div>

        <!-- 联系信息管理 -->
        <div class="property-section">
            <h4 class="section-title">联系信息</h4>
            <div class="contacts-manager">
                ${props.contacts.map((contact, index) => `
                    <div class="contact-editor">
                        <div class="contact-editor-header">
                            <span class="contact-editor-title">联系方式 ${index + 1}</span>
                            <div class="contact-editor-actions">
                                <button type="button" class="contact-action-btn delete" 
                                        onclick="deleteContact('${component.id}', ${index})">删除</button>
                            </div>
                        </div>
                        <div class="contact-editor-content">
                            <div class="property-group">
                                <label class="property-label">类型</label>
                                <select class="property-input" onchange="updateContactItemProperty('${component.id}', ${index}, 'type', this.value)">
                                    ${Object.entries(contactTypes).map(([key, config]) =>
                                        `<option value="${key}" ${contact.type === key ? 'selected' : ''}>${config.label}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="property-group">
                                <label class="property-label">标签</label>
                                <input type="text" class="property-input" value="${contact.label}"
                                       onchange="updateContactItemProperty('${component.id}', ${index}, 'label', this.value)">
                            </div>
                            <div class="property-group">
                                <label class="property-label">内容</label>
                                <input type="text" class="property-input" value="${contact.value}"
                                       onchange="updateContactItemProperty('${component.id}', ${index}, 'value', this.value)">
                            </div>
                            <div class="property-group contact-icon-editor">
                                <label class="property-label">图标</label>
                                <div class="contact-icon-selector-wrapper">
                                    <button type="button" class="contact-icon-selector-btn"
                                            onclick="toggleContactIconPicker('${component.id}', ${index})">
                                        <span class="contact-selected-icon">${contact.icon}</span>
                                        <span class="contact-selector-arrow">▼</span>
                                    </button>
                                    <div class="contact-icon-picker" id="contact-icon-picker-${component.id}-${index}" style="display: none;">
                                        <div class="contact-icon-grid">
                                            ${contactIconLibrary.map(icon => `
                                                <div class="contact-icon-option ${contact.icon === icon ? 'selected' : ''}"
                                                     onclick="selectContactIcon('${component.id}', ${index}, '${icon}')">
                                                    <span class="contact-icon-emoji">${icon}</span>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
            <div class="contact-actions">
                <button type="button" class="add-contact-btn" onclick="addContact('${component.id}')">
                    + 添加联系方式
                </button>
            </div>
        </div>

        <!-- 布局设置 - 借鉴卡片组件 -->
        <div class="property-section">
            <h4 class="section-title">布局设置</h4>
            <div class="property-group">
                <label class="property-label">布局模式</label>
                <div class="layout-buttons-clean">
                    ${['vertical', 'horizontal', 'grid'].map(layout => `
                        <button type="button" class="layout-btn ${props.layout === layout ? 'active' : ''}"
                                onclick="updateContactProperty('${component.id}', 'layout', '${layout}')">
                            ${layout === 'vertical' ? '垂直' : layout === 'horizontal' ? '水平' : '网格'}
                        </button>
                    `).join('')}
                </div>
            </div>
            ${props.layout === 'grid' ? `
                <div class="property-group">
                    <label class="property-label">列数</label>
                    <div class="contact-columns-grid">
                        ${[1, 2, 3, 4].map(cols => `
                            <button type="button" class="contact-column-btn ${props.columns === cols ? 'active' : ''}"
                                    onclick="updateContactProperty('${component.id}', 'columns', ${cols})">
                                ${cols}列
                            </button>
                        `).join('')}
                    </div>
                </div>
            ` : ''}

            ${(props.layout === 'vertical' || (props.layout === 'grid' && props.columns === 1)) ? `
                <div class="property-group">
                    <label class="property-label">背景图片</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" ${props.showBackgroundImage ? 'checked' : ''}
                                   onchange="updateContactProperty('${component.id}', 'showBackgroundImage', this.checked)">
                            <label>显示背景图</label>
                        </div>
                    </div>
                    ${props.showBackgroundImage ? `
                        <div class="contact-bg-options">
                            ${contactBackgroundLibrary.map((bg, index) => `
                                <div class="contact-bg-option ${props.backgroundImageIndex === index ? 'selected' : ''}"
                                     onclick="updateContactProperty('${component.id}', 'backgroundImageIndex', ${index})"
                                     style="background-image: url('${bg}'); background-size: cover; background-position: center;">
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            ` : ''}
        </div>

        <!-- 颜色设置 - 完全复制卡片组件的布局 -->
        <div class="property-section">
            <h4 class="section-title">颜色设置</h4>
            <div class="property-group">
                <div class="color-row">
                    <div class="color-item">
                        <label>背景色</label>
                        <input type="color" class="property-input" value="${props.bgColor}"
                               onchange="updateContactProperty('${component.id}', 'bgColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>图标色</label>
                        <input type="color" class="property-input" value="${props.iconColor}"
                               onchange="updateContactProperty('${component.id}', 'iconColor', this.value)">
                    </div>
                </div>
                <div class="color-row">
                    <div class="color-item">
                        <label>标签色</label>
                        <input type="color" class="property-input" value="${props.labelColor}"
                               onchange="updateContactProperty('${component.id}', 'labelColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>内容色</label>
                        <input type="color" class="property-input" value="${props.valueColor}"
                               onchange="updateContactProperty('${component.id}', 'valueColor', this.value)">
                    </div>
                </div>
                <div class="color-row">
                    <div class="color-item">
                        <label>边框色</label>
                        <input type="color" class="property-input" value="${props.borderColor}"
                               onchange="updateContactProperty('${component.id}', 'borderColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>悬停色</label>
                        <input type="color" class="property-input" value="${props.hoverBg}"
                               onchange="updateContactProperty('${component.id}', 'hoverBg', this.value)">
                    </div>
                </div>
            </div>
        </div>

        <!-- 尺寸设置 - 借鉴卡片组件的滑块控件 -->
        <div class="property-section">
            <h4 class="section-title">尺寸设置</h4>
            <div class="property-group">
                <label class="property-label">最大宽度</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.maxWidth}"
                           min="600" max="1400" step="50"
                           onchange="updateContactProperty('${component.id}', 'maxWidth', parseInt(this.value))">
                    <span class="range-value">${props.maxWidth}px</span>
                </div>
            </div>
            <div class="property-group">
                <label class="property-label">内边距</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.padding}"
                           min="16" max="80" step="4"
                           onchange="updateContactProperty('${component.id}', 'padding', parseInt(this.value))">
                    <span class="range-value">${props.padding}px</span>
                </div>
            </div>
            <div class="property-group">
                <label class="property-label">圆角大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.borderRadius}"
                           min="0" max="30" step="2"
                           onchange="updateContactProperty('${component.id}', 'borderRadius', parseInt(this.value))">
                    <span class="range-value">${props.borderRadius}px</span>
                </div>
            </div>
            <div class="property-group">
                <label class="property-label">图标大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.iconSize}"
                           min="16" max="40" step="2"
                           onchange="updateContactProperty('${component.id}', 'iconSize', parseInt(this.value))">
                    <span class="range-value">${props.iconSize}px</span>
                </div>
            </div>
            <div class="property-group">
                <label class="property-label">标签字号</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.labelSize}"
                           min="10" max="20" step="1"
                           onchange="updateContactProperty('${component.id}', 'labelSize', parseInt(this.value))">
                    <span class="range-value">${props.labelSize}px</span>
                </div>
            </div>
            <div class="property-group">
                <label class="property-label">内容字号</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.valueSize}"
                           min="12" max="24" step="1"
                           onchange="updateContactProperty('${component.id}', 'valueSize', parseInt(this.value))">
                    <span class="range-value">${props.valueSize}px</span>
                </div>
            </div>
        </div>

        <!-- 效果设置 - 借鉴卡片组件的复选框组 -->
        <div class="property-section">
            <h4 class="section-title">效果设置</h4>
            <div class="property-group">
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" ${props.shadow ? 'checked' : ''}
                               onchange="updateContactProperty('${component.id}', 'shadow', this.checked)">
                        <label>阴影效果</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" ${props.showBorder ? 'checked' : ''}
                               onchange="updateContactProperty('${component.id}', 'showBorder', this.checked)">
                        <label>显示边框</label>
                    </div>
                </div>

            </div>
            ${props.shadow ? `
                <div class="property-group">
                    <label class="property-label">阴影强度</label>
                    <div class="range-control">
                        <input type="range" class="range-slider" value="${props.shadowIntensity}"
                               min="0.05" max="0.3" step="0.05"
                               onchange="updateContactProperty('${component.id}', 'shadowIntensity', parseFloat(this.value))">
                        <span class="range-value">${props.shadowIntensity}</span>
                    </div>
                </div>
            ` : ''}
        </div>
    `;
}

// 应用联系信息样式预设 - 完全参考卡片组件的实现
function applyContactStylePreset(componentId, presetIndex) {
    const component = document.getElementById(componentId);
    if (!component) return;

    const preset = contactStylePresets[presetIndex];
    let props = component._contactProperties || JSON.parse(JSON.stringify(contactComponent.properties));

    // 应用预设样式
    props.stylePreset = preset.name.toLowerCase().replace(/\s+/g, '-');
    props.bgColor = preset.bgColor;
    props.labelColor = preset.labelColor;
    props.valueColor = preset.valueColor;
    props.iconColor = preset.iconColor;
    props.borderColor = preset.borderColor;
    props.hoverBg = preset.hoverBg;

    component._contactProperties = props;
    updateContactDisplay(component, props);
    updatePropertiesPanel(component);
}

// 更新联系信息属性 - 参考卡片组件的属性更新逻辑
function updateContactProperty(componentId, property, value) {
    const component = document.getElementById(componentId);
    if (!component) return;

    // 获取属性 - 参考卡片组件的模式
    let props;
    if (component._contactProperties) {
        props = component._contactProperties;
    } else {
        props = JSON.parse(JSON.stringify(contactComponent.properties));
        component._contactProperties = props;
    }

    // 类型转换 - 参考卡片组件的处理方式
    if (property === 'maxWidth' || property === 'padding' || property === 'borderRadius' ||
        property === 'iconSize' || property === 'labelSize' || property === 'valueSize' ||
        property === 'columns' || property === 'itemSpacing' || property === 'backgroundImageIndex') {
        props[property] = parseInt(value);
    } else if (property === 'shadowIntensity') {
        props[property] = parseFloat(value);
    } else if (property === 'shadow' || property === 'showBorder' || property === 'animation' ||
               property === 'showBackgroundImage') {
        props[property] = value;
    } else {
        props[property] = value;
    }

    // 更新显示和属性面板 - 参考卡片组件的调用方式
    updateContactDisplay(component, props);
    updatePropertiesPanel(component);
}

// 更新单个联系方式属性
function updateContactItemProperty(componentId, contactIndex, property, value) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props = component._contactProperties || JSON.parse(JSON.stringify(contactComponent.properties));

    if (props.contacts[contactIndex]) {
        props.contacts[contactIndex][property] = value;

        // 如果更改了类型，自动更新图标和标签
        if (property === 'type' && contactTypes[value]) {
            props.contacts[contactIndex].icon = contactTypes[value].icon;
            props.contacts[contactIndex].label = contactTypes[value].label;
        }
    }

    component._contactProperties = props;
    updateContactDisplay(component, props);
    updatePropertiesPanel(component);
}

// 添加联系方式
function addContact(componentId) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props = component._contactProperties || JSON.parse(JSON.stringify(contactComponent.properties));

    // 添加新的联系方式
    props.contacts.push({
        type: 'phone',
        label: '联系电话',
        value: '请输入联系方式',
        icon: '📞'
    });

    component._contactProperties = props;
    updateContactDisplay(component, props);
    updatePropertiesPanel(component);
}

// 删除联系方式
function deleteContact(componentId, contactIndex) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props = component._contactProperties || JSON.parse(JSON.stringify(contactComponent.properties));

    if (props.contacts.length > 1) {
        props.contacts.splice(contactIndex, 1);
        component._contactProperties = props;
        updateContactDisplay(component, props);
        updatePropertiesPanel(component);
    } else {
        alert('至少需要保留一个联系方式');
    }
}

// 切换图标选择器显示
function toggleContactIconPicker(componentId, contactIndex) {
    const pickerId = `contact-icon-picker-${componentId}-${contactIndex}`;
    const picker = document.getElementById(pickerId);

    if (picker) {
        // 关闭所有其他图标选择器
        document.querySelectorAll('.contact-icon-picker').forEach(p => {
            if (p.id !== pickerId) {
                p.style.display = 'none';
            }
        });

        // 切换当前选择器
        picker.style.display = picker.style.display === 'none' ? 'block' : 'none';
    }
}

// 选择联系方式图标
function selectContactIcon(componentId, contactIndex, icon) {
    updateContactItemProperty(componentId, contactIndex, 'icon', icon);

    // 关闭图标选择器
    const pickerId = `contact-icon-picker-${componentId}-${contactIndex}`;
    const picker = document.getElementById(pickerId);
    if (picker) {
        picker.style.display = 'none';
    }
}

// 更新联系信息显示 - 核心显示更新函数
function updateContactDisplay(component, props) {
    if (!component || !props) return;

    const container = component.querySelector('.contact-container') || component;

    // 设置布局类名
    container.className = `contact-container layout-${props.layout}`;

    // 检查是否需要显示背景图
    const shouldShowBg = props.showBackgroundImage &&
                        (props.layout === 'vertical' || (props.layout === 'grid' && props.columns === 1));

    // 生成联系信息HTML
    const contactsHTML = props.contacts.map((contact, index) => {
        return `
            <div class="contact-item" data-type="${contact.type}" data-index="${index}">
                <div class="contact-icon">${contact.icon}</div>
                <div class="contact-content">
                    <div class="contact-label">${contact.label}</div>
                    <div class="contact-value">${contact.value}</div>
                </div>
            </div>
        `;
    }).join('');

    // 根据是否显示背景图生成不同的HTML结构
    if (shouldShowBg) {
        container.innerHTML = `
            <div class="contact-content-wrapper">
                ${contactsHTML}
            </div>
            <div class="contact-background-area">
                <div class="contact-background-image" style="background-image: url('${contactBackgroundLibrary[props.backgroundImageIndex]}');"></div>
            </div>
        `;
    } else {
        container.innerHTML = contactsHTML;
    }

    // 应用样式到容器
    container.style.display = props.layout === 'grid' ? 'grid' : 'flex';
    container.style.flexDirection = props.layout === 'vertical' ? 'column' : 'row';
    container.style.gridTemplateColumns = props.layout === 'grid' ? `repeat(${props.columns}, 1fr)` : 'none';
    container.style.gap = `${props.itemSpacing}px`;
    container.style.maxWidth = `${props.maxWidth}px`;
    container.style.margin = '0 auto';
    container.style.padding = `${props.padding}px`;
    container.style.background = props.bgColor;
    container.style.borderRadius = `${props.borderRadius}px`;
    container.style.boxShadow = props.shadow ? `0 4px 20px rgba(0,0,0,${props.shadowIntensity})` : 'none';
    container.style.border = props.showBorder ? `1px solid ${props.borderColor}` : 'none';

    // 应用样式到所有联系项
    const contactItems = container.querySelectorAll('.contact-item');

    // 如果有背景图，调整容器布局
    if (shouldShowBg) {
        // 强制设置容器为水平布局
        container.style.display = 'flex !important';
        container.style.flexDirection = 'row !important';
        container.style.alignItems = 'stretch';
        container.style.minHeight = '300px';

        const contentWrapper = container.querySelector('.contact-content-wrapper');
        const backgroundArea = container.querySelector('.contact-background-area');

        if (contentWrapper && backgroundArea) {
            // 左侧内容区域
            contentWrapper.style.flex = '1';
            contentWrapper.style.display = 'flex';
            contentWrapper.style.flexDirection = 'column';
            contentWrapper.style.gap = `${props.itemSpacing}px`;
            contentWrapper.style.paddingRight = '24px';
            contentWrapper.style.minWidth = '0'; // 防止内容溢出

            // 右侧背景图区域
            backgroundArea.style.flex = '0 0 65%';
            backgroundArea.style.position = 'relative';
            backgroundArea.style.borderRadius = `${props.borderRadius}px`;
            backgroundArea.style.overflow = 'hidden';
            backgroundArea.style.minHeight = '250px';
            backgroundArea.style.backgroundColor = '#f8fafc';

            const bgImage = backgroundArea.querySelector('.contact-background-image');
            if (bgImage) {
                bgImage.style.position = 'absolute';
                bgImage.style.top = '0';
                bgImage.style.left = '0';
                bgImage.style.right = '0';
                bgImage.style.bottom = '0';
                bgImage.style.backgroundSize = 'cover';
                bgImage.style.backgroundPosition = 'center';
                bgImage.style.backgroundRepeat = 'no-repeat';
            }
        }
    } else {
        // 恢复原始布局
        container.style.display = props.layout === 'grid' ? 'grid' : 'flex';
        container.style.flexDirection = props.layout === 'vertical' ? 'column' : 'row';
    }

    contactItems.forEach((item) => {

        // 基础样式
        item.style.display = 'flex';
        item.style.alignItems = 'center';
        item.style.padding = '16px';
        item.style.borderRadius = '8px';
        item.style.transition = 'all 0.3s ease';
        item.style.cursor = 'default';
        item.style.border = `1px solid transparent`;

        // 悬停效果
        item.addEventListener('mouseenter', () => {
            item.style.background = props.hoverBg;
            item.style.borderColor = props.borderColor;
            item.style.transform = 'translateY(-2px)';
        });

        item.addEventListener('mouseleave', () => {
            item.style.background = 'transparent';
            item.style.borderColor = 'transparent';
            item.style.transform = 'translateY(0)';
        });

        // 图标样式
        const icon = item.querySelector('.contact-icon');
        if (icon) {
            icon.style.fontSize = `${props.iconSize}px`;
            icon.style.color = props.iconColor;
            icon.style.marginRight = '12px';
            icon.style.flexShrink = '0';
        }

        // 内容样式
        const content = item.querySelector('.contact-content');
        if (content) {
            content.style.flex = '1';
        }

        // 标签样式
        const label = item.querySelector('.contact-label');
        if (label) {
            label.style.fontSize = `${props.labelSize}px`;
            label.style.color = props.labelColor;
            label.style.marginBottom = '4px';
            label.style.fontWeight = '500';
        }

        // 值样式
        const value = item.querySelector('.contact-value');
        if (value) {
            value.style.fontSize = `${props.valueSize}px`;
            value.style.color = props.valueColor;
            value.style.fontWeight = '600';
        }




    });

    // 响应式处理
    applyContactResponsive(container, props);
}



// 应用响应式样式
function applyContactResponsive(container, props) {
    // 移动端适配
    const mediaQuery = window.matchMedia('(max-width: 768px)');

    function handleResponsive(e) {
        if (e.matches) {
            // 移动端样式
            if (props.layout === 'grid' && props.columns > 2) {
                container.style.gridTemplateColumns = 'repeat(2, 1fr)';
            } else if (props.layout === 'horizontal') {
                container.style.flexDirection = 'column';
            }

            // 调整间距和内边距
            container.style.padding = `${Math.max(16, props.padding * 0.6)}px`;
            container.style.gap = `${Math.max(8, props.itemSpacing * 0.7)}px`;

            // 调整字体大小
            const items = container.querySelectorAll('.contact-item');
            items.forEach(item => {
                const label = item.querySelector('.contact-label');
                const value = item.querySelector('.contact-value');
                const icon = item.querySelector('.contact-icon');

                if (label) label.style.fontSize = `${Math.max(12, props.labelSize * 0.9)}px`;
                if (value) value.style.fontSize = `${Math.max(14, props.valueSize * 0.9)}px`;
                if (icon) icon.style.fontSize = `${Math.max(20, props.iconSize * 0.8)}px`;
            });
        } else {
            // 桌面端样式
            container.style.gridTemplateColumns = props.layout === 'grid' ? `repeat(${props.columns}, 1fr)` : 'none';
            container.style.flexDirection = props.layout === 'vertical' ? 'column' : 'row';
            container.style.padding = `${props.padding}px`;
            container.style.gap = `${props.itemSpacing}px`;

            // 恢复原始字体大小
            const items = container.querySelectorAll('.contact-item');
            items.forEach(item => {
                const label = item.querySelector('.contact-label');
                const value = item.querySelector('.contact-value');
                const icon = item.querySelector('.contact-icon');

                if (label) label.style.fontSize = `${props.labelSize}px`;
                if (value) value.style.fontSize = `${props.valueSize}px`;
                if (icon) icon.style.fontSize = `${props.iconSize}px`;
            });
        }
    }

    // 初始检查
    handleResponsive(mediaQuery);

    // 监听变化
    mediaQuery.addListener(handleResponsive);
}

// 关闭所有图标选择器（点击外部时）
document.addEventListener('click', function(e) {
    if (!e.target.closest('.contact-icon-selector-wrapper')) {
        document.querySelectorAll('.contact-icon-picker').forEach(picker => {
            picker.style.display = 'none';
        });
    }
});

// 注册联系信息组件
if (typeof ComponentManager !== 'undefined') {
    ComponentManager.register('contact', contactComponent, generateContactProperties, updateContactDisplay);
}

// 初始化日志
console.log('📞 联系信息组件已加载');

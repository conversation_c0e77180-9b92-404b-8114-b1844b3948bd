/**
 * 导航栏组件
 * 包含Logo、菜单项、按钮等导航元素
 */

// 导航栏组件模板
const navbarComponent = {
    name: '导航栏',
    html: `<div class="navbar-component">
        <div class="navbar-logo">网站Logo</div>
        <div class="navbar-menu">
            <a href="#" class="navbar-item">首页</a>
            <div class="navbar-dropdown">
                <a href="#" class="navbar-item dropdown-toggle">产品介绍 <i class="dropdown-arrow">▼</i></a>
                <div class="dropdown-menu">
                    <a href="#" class="dropdown-item">产品A</a>
                    <a href="#" class="dropdown-item">产品B</a>
                </div>
            </div>
            <div class="navbar-dropdown">
                <a href="#" class="navbar-item dropdown-toggle">解决方案 <i class="dropdown-arrow">▼</i></a>
                <div class="dropdown-menu">
                    <a href="#" class="dropdown-item">企业方案</a>
                    <a href="#" class="dropdown-item">个人方案</a>
                </div>
            </div>
            <a href="#" class="navbar-item">客户案例</a>
            <a href="#" class="navbar-item">新闻资讯</a>
            <a href="#" class="navbar-item">关于我们</a>
            <a href="#" class="navbar-item">联系我们</a>
        </div>
        <div class="navbar-buttons">
            <button class="navbar-btn">登录</button>
            <button class="navbar-btn primary">注册</button>
        </div>
    </div>`,
    properties: {
        logoType: 'text',
        logoText: '网站Logo',
        logoImage: '',
        logoFontSize: 24,
        logoColor: '#2d3748',
        menuItems: [
            { name: '首页', link: '#', type: 'normal', children: [] },
            {
                name: '产品介绍',
                link: '#',
                type: 'dropdown',
                children: [
                    { name: '产品A', link: '#' },
                    { name: '产品B', link: '#' }
                ]
            },
            {
                name: '解决方案',
                link: '#',
                type: 'dropdown',
                children: [
                    { name: '企业方案', link: '#' },
                    { name: '个人方案', link: '#' }
                ]
            },
            { name: '客户案例', link: '#', type: 'normal', children: [] },
            { name: '新闻资讯', link: '#', type: 'normal', children: [] },
            { name: '关于我们', link: '#', type: 'normal', children: [] },
            { name: '联系我们', link: '#', type: 'normal', children: [] }
        ],
        btn1Text: '登录',
        btn1Link: '#',
        btn1Color: '#667eea',
        btn1BorderColor: '#667eea',
        btn1BorderRadius: 6,
        btn2Text: '注册',
        btn2Link: '#',
        btn2Color: '#ffffff',
        btn2BorderColor: '#667eea',
        btn2BackgroundColor: '#667eea',
        btn2BorderRadius: 6,
        bgColor: '#ffffff',
        textColor: '#2d3748',
        hoverColor: '#667eea',
        shadow: true,
        sticky: false
    }
};

// 生成导航栏组件属性面板
function generateNavbarProperties(component) {
    const props = navbarComponent.properties;
    
    let html = `
        <!-- Logo设置 -->
        <div class="property-section">
            <h4 class="section-title">Logo设置</h4>

            <div class="property-group nav-setting-group">
                <label class="property-label">Logo类型</label>
                <div class="input-group">
                    <select class="property-input" onchange="updateNavbarProperty('${component.id}', 'logoType', this.value)">
                        <option value="text" ${props.logoType === 'text' ? 'selected' : ''}>文字Logo</option>
                        <option value="image" ${props.logoType === 'image' ? 'selected' : ''}>图片Logo</option>
                    </select>
                </div>
                <div class="input-group">
                    <input type="text" class="property-input" value="${props.logoType === 'text' ? props.logoText : props.logoImage}"
                           placeholder="${props.logoType === 'text' ? '请输入Logo文字' : '请输入图片URL地址'}"
                           onchange="updateNavbarProperty('${component.id}', '${props.logoType === 'text' ? 'logoText' : 'logoImage'}', this.value)">
                </div>

                ${props.logoType === 'text' ? `
                <div class="compact-row">
                    <div class="compact-item">
                        <label>Logo颜色</label>
                        <input type="color" class="compact-color-input" value="${props.logoColor}"
                               onchange="updateNavbarProperty('${component.id}', 'logoColor', this.value)">
                    </div>
                    <div class="compact-item">
                        <label>字体大小</label>
                        <input type="number" class="compact-number-input" value="${props.logoFontSize}" min="12" max="48"
                               onchange="updateNavbarProperty('${component.id}', 'logoFontSize', this.value)">
                    </div>
                </div>
                ` : ''}
            </div>
        </div>

        <!-- 菜单设置 -->
        <div class="property-section">
            <h4 class="section-title">菜单设置</h4>

            <div class="property-group nav-menu-compact">
                <label class="property-label">导航菜单项</label>
                <div id="menu-items-${component.id}" class="menu-items-container">
                    ${props.menuItems.map((item, index) => `
                        <div class="menu-item-card" data-index="${index}">
                            <div class="menu-item-header">
                                <div class="menu-item-main">
                                    <input type="text" class="menu-name-input" value="${item.name}" placeholder="菜单名称"
                                           onchange="updateMenuItem('${component.id}', ${index}, 'name', this.value)">
                                    <select class="menu-type-select" onchange="updateMenuItem('${component.id}', ${index}, 'type', this.value)">
                                        <option value="normal" ${item.type === 'normal' ? 'selected' : ''}>普通</option>
                                        <option value="dropdown" ${item.type === 'dropdown' ? 'selected' : ''}>下拉</option>
                                    </select>
                                </div>
                                <div class="menu-item-actions">
                                    ${item.type === 'dropdown' ? `<button class="toggle-children-btn" onclick="toggleMenuChildren('${component.id}', ${index})">⚙️</button>` : ''}
                                    <button onclick="removeMenuItem('${component.id}', ${index})" class="delete-btn-mini">×</button>
                                </div>
                            </div>
                            <div class="menu-item-link">
                                <div class="link-input-group">
                                    <input type="text" class="link-input" value="${item.link}" placeholder="链接地址"
                                           onchange="updateMenuItem('${component.id}', ${index}, 'link', this.value)">
                                    <button class="link-select-btn" onclick="selectMenuLink('${component.id}', ${index})">选择</button>
                                </div>
                            </div>
                            ${item.type === 'dropdown' ? `
                                <div class="submenu-container" id="submenu-${component.id}-${index}" style="display: none;">
                                    <div class="submenu-header">二级菜单</div>
                                    ${item.children && item.children.length > 0 ? item.children.map((child, childIndex) => `
                                        <div class="submenu-item">
                                            <div class="submenu-item-row">
                                                <input type="text" class="submenu-input" value="${child.name}" placeholder="子菜单名称"
                                                       onchange="updateSubMenuItem('${component.id}', ${index}, ${childIndex}, 'name', this.value)">
                                                <button onclick="removeSubMenuItem('${component.id}', ${index}, ${childIndex})" class="delete-btn-mini">×</button>
                                            </div>
                                            <div class="submenu-item-row">
                                                <div class="link-input-group">
                                                    <input type="text" class="submenu-link-input" value="${child.link}" placeholder="子菜单链接"
                                                           onchange="updateSubMenuItem('${component.id}', ${index}, ${childIndex}, 'link', this.value)">
                                                    <button class="link-select-btn-small" onclick="selectSubMenuLink('${component.id}', ${index}, ${childIndex})">选择</button>
                                                </div>
                                            </div>
                                        </div>
                                    `).join('') : '<div class="submenu-empty">暂无子菜单，点击下方按钮添加</div>'}
                                    <button onclick="addSubMenuItem('${component.id}', ${index})" class="add-submenu-btn">+ 添加子菜单</button>
                                </div>
                            ` : ''}
                        </div>
                    `).join('')}
                </div>
                <button onclick="addMenuItem('${component.id}')" class="add-btn">+ 添加菜单项</button>
            </div>
        </div>

        <!-- 按钮设置 -->
        <div class="property-section">
            <h4 class="section-title">按钮设置</h4>

            <div class="property-group nav-button-group">
                <label class="property-label">登录按钮</label>
                <div class="input-group">
                    <input type="text" class="property-input" value="${props.btn1Text}" placeholder="按钮文字"
                           onchange="updateNavbarProperty('${component.id}', 'btn1Text', this.value)">
                </div>
                <div class="input-group">
                    <input type="text" class="property-input" value="${props.btn1Link}" placeholder="按钮链接"
                           onchange="updateNavbarProperty('${component.id}', 'btn1Link', this.value)">
                </div>
                <div class="compact-grid">
                    <div class="compact-item">
                        <label>文字颜色</label>
                        <input type="color" class="compact-color-input" value="${props.btn1Color}"
                               onchange="updateNavbarProperty('${component.id}', 'btn1Color', this.value)">
                    </div>
                    <div class="compact-item">
                        <label>边框颜色</label>
                        <input type="color" class="compact-color-input" value="${props.btn1BorderColor}"
                               onchange="updateNavbarProperty('${component.id}', 'btn1BorderColor', this.value)">
                    </div>
                    <div class="compact-item">
                        <label>圆角</label>
                        <input type="number" class="compact-number-input" value="${props.btn1BorderRadius}" min="0" max="50"
                               onchange="updateNavbarProperty('${component.id}', 'btn1BorderRadius', this.value)">
                    </div>
                </div>
            </div>

            <div class="property-group nav-button-group">
                <label class="property-label">注册按钮</label>
                <div class="input-group">
                    <input type="text" class="property-input" value="${props.btn2Text}" placeholder="按钮文字"
                           onchange="updateNavbarProperty('${component.id}', 'btn2Text', this.value)">
                </div>
                <div class="input-group">
                    <input type="text" class="property-input" value="${props.btn2Link}" placeholder="按钮链接"
                           onchange="updateNavbarProperty('${component.id}', 'btn2Link', this.value)">
                </div>
                <div class="compact-grid">
                    <div class="compact-item">
                        <label>文字颜色</label>
                        <input type="color" class="compact-color-input" value="${props.btn2Color}"
                               onchange="updateNavbarProperty('${component.id}', 'btn2Color', this.value)">
                    </div>
                    <div class="compact-item">
                        <label>背景颜色</label>
                        <input type="color" class="compact-color-input" value="${props.btn2BackgroundColor}"
                               onchange="updateNavbarProperty('${component.id}', 'btn2BackgroundColor', this.value)">
                    </div>
                    <div class="compact-item">
                        <label>圆角</label>
                        <input type="number" class="compact-number-input" value="${props.btn2BorderRadius}" min="0" max="50"
                               onchange="updateNavbarProperty('${component.id}', 'btn2BorderRadius', this.value)">
                    </div>
                </div>
            </div>
        </div>

        <!-- 样式设置 -->
        <div class="property-section">
            <h4 class="section-title">样式设置</h4>

            <div class="property-group nav-colors-compact">
                <label class="property-label">颜色设置</label>
                <div class="compact-grid">
                    <div class="compact-item">
                        <label>背景颜色</label>
                        <input type="color" class="compact-color-input" value="${props.bgColor}"
                               onchange="updateNavbarProperty('${component.id}', 'bgColor', this.value)">
                    </div>
                    <div class="compact-item">
                        <label>文字颜色</label>
                        <input type="color" class="compact-color-input" value="${props.textColor}"
                               onchange="updateNavbarProperty('${component.id}', 'textColor', this.value)">
                    </div>
                    <div class="compact-item">
                        <label>悬停颜色</label>
                        <input type="color" class="compact-color-input" value="${props.hoverColor}"
                               onchange="updateNavbarProperty('${component.id}', 'hoverColor', this.value)">
                    </div>
                </div>
            </div>

            <div class="property-group nav-setting-group">
                <label class="property-label">导航栏效果</label>
                <div class="checkbox-group">
                    <label class="checkbox-item">
                        <input type="checkbox" ${props.shadow ? 'checked' : ''}
                               onchange="updateNavbarProperty('${component.id}', 'shadow', this.checked)">
                        阴影效果
                    </label>
                    <label class="checkbox-item">
                        <input type="checkbox" ${props.sticky ? 'checked' : ''}
                               onchange="updateNavbarProperty('${component.id}', 'sticky', this.checked)">
                        固定顶部
                    </label>
                </div>
            </div>
        </div>
    `;

    return html;
}

// 更新导航栏属性
function updateNavbarProperty(componentId, property, value) {
    const component = document.getElementById(componentId);
    const props = navbarComponent.properties;
    
    if (typeof value === 'boolean') {
        props[property] = value;
    } else if (property.includes('BorderRadius') || property === 'logoFontSize') {
        props[property] = parseInt(value);
    } else {
        props[property] = value;
    }
    
    updateNavbarDisplay(component, props);

    if (property === 'logoType') {
        updatePropertiesPanel(component);
    }
}

// 更新导航栏显示
function updateNavbarDisplay(component, props) {
    const contentDiv = component.querySelector('.navbar-component');
    if (!contentDiv) return;

    // 保存属性到组件实例，供预览页面使用
    component._navbarProperties = props;

    // 更新Logo
    const logo = contentDiv.querySelector('.navbar-logo');
    if (props.logoType === 'text') {
        logo.innerHTML = props.logoText;
        logo.style.fontSize = props.logoFontSize + 'px';
        logo.style.color = props.logoColor;
        logo.style.fontWeight = 'bold';
    } else {
        logo.innerHTML = `<img src="${props.logoImage}" alt="Logo" style="height: ${props.logoFontSize + 16}px; width: auto;">`;
        logo.style.fontSize = '';
        logo.style.color = '';
        logo.style.fontWeight = '';
    }

    // 更新菜单项
    const menu = contentDiv.querySelector('.navbar-menu');
    menu.innerHTML = props.menuItems.map(item => {
        if (item.type === 'dropdown' && item.children && item.children.length > 0) {
            return `
                <div class="navbar-dropdown">
                    <a href="${item.link}" class="navbar-item dropdown-toggle" style="color: ${props.textColor};">
                        ${item.name} <i class="dropdown-arrow">▼</i>
                    </a>
                    <div class="dropdown-menu">
                        ${item.children.map(child =>
                            `<a href="${child.link}" class="dropdown-item" style="color: ${props.textColor};">${child.name}</a>`
                        ).join('')}
                    </div>
                </div>
            `;
        } else {
            return `<a href="${item.link}" class="navbar-item" style="color: ${props.textColor};">${item.name}</a>`;
        }
    }).join('');

    // 更新按钮
    const buttons = contentDiv.querySelector('.navbar-buttons');
    buttons.innerHTML = `
        <button class="navbar-btn" id="navbar-btn1-${component.id}" style="
            color: ${props.btn1Color};
            border-color: ${props.btn1BorderColor};
            border-radius: ${props.btn1BorderRadius}px;
            background: transparent;
        ">${props.btn1Text}</button>
        <button class="navbar-btn" id="navbar-btn2-${component.id}" style="
            color: ${props.btn2Color};
            border-color: ${props.btn2BackgroundColor};
            border-radius: ${props.btn2BorderRadius}px;
            background: ${props.btn2BackgroundColor};
        ">${props.btn2Text}</button>
    `;

    // 添加动态悬停样式
    addNavbarHoverStyles(component.id, props);

    // 更新菜单项悬停样式
    updateMenuItemsHoverStyle(contentDiv, props.hoverColor, props.textColor);

    // 更新整体样式
    contentDiv.style.backgroundColor = props.bgColor;
    contentDiv.style.color = props.textColor;
    contentDiv.style.boxShadow = props.shadow ? '0 2px 10px rgba(0,0,0,0.1)' : 'none';
    contentDiv.style.position = props.sticky ? 'sticky' : 'relative';
    contentDiv.style.top = props.sticky ? '0' : 'auto';
    contentDiv.style.zIndex = props.sticky ? '10000' : '9998';  // 调整z-index，让组件控制按钮能显示在上面
}

// 菜单项管理函数
function updateMenuItem(componentId, index, field, value) {
    const props = navbarComponent.properties;
    props.menuItems[index][field] = value;

    // 如果改变菜单类型，需要初始化或清空children数组
    if (field === 'type') {
        if (value === 'dropdown' && !props.menuItems[index].children) {
            props.menuItems[index].children = [];
        } else if (value === 'normal') {
            props.menuItems[index].children = [];
        }
        // 重新生成属性面板以显示/隐藏子菜单管理
        const component = document.getElementById(componentId);
        updatePropertiesPanel(component);

        // 如果改为dropdown类型且有子菜单，自动展开
        if (value === 'dropdown' && props.menuItems[index].children && props.menuItems[index].children.length > 0) {
            const submenuContainer = document.getElementById(`submenu-${componentId}-${index}`);
            if (submenuContainer) {
                submenuContainer.style.display = 'block';
            }
        }
    }

    const component = document.getElementById(componentId);
    updateNavbarDisplay(component, props);
}

function addMenuItem(componentId) {
    const props = navbarComponent.properties;
    props.menuItems.push({ name: '新菜单', link: '#', type: 'normal', children: [] });

    const component = document.getElementById(componentId);
    updatePropertiesPanel(component);
    updateNavbarDisplay(component, props);
}

function removeMenuItem(componentId, index) {
    const props = navbarComponent.properties;
    props.menuItems.splice(index, 1);

    const component = document.getElementById(componentId);
    updatePropertiesPanel(component);
    updateNavbarDisplay(component, props);
}

// 切换子菜单显示
function toggleMenuChildren(componentId, index) {
    const submenuContainer = document.getElementById(`submenu-${componentId}-${index}`);
    if (submenuContainer) {
        submenuContainer.style.display = submenuContainer.style.display === 'none' ? 'block' : 'none';
    }
}

// 保存所有子菜单的展开状态
function saveAllSubmenuStates(componentId, menuCount) {
    const states = {};
    for (let i = 0; i < menuCount; i++) {
        const submenuContainer = document.getElementById(`submenu-${componentId}-${i}`);
        if (submenuContainer) {
            states[i] = submenuContainer.style.display === 'block';
        }
    }
    return states;
}

// 恢复所有子菜单的展开状态
function restoreAllSubmenuStates(componentId, states) {
    for (const index in states) {
        const submenuContainer = document.getElementById(`submenu-${componentId}-${index}`);
        if (submenuContainer) {
            submenuContainer.style.display = states[index] ? 'block' : 'none';
        }
    }
}

// 更新子菜单项
function updateSubMenuItem(componentId, parentIndex, childIndex, field, value) {
    const props = navbarComponent.properties;
    if (!props.menuItems[parentIndex].children) {
        props.menuItems[parentIndex].children = [];
    }
    props.menuItems[parentIndex].children[childIndex][field] = value;

    const component = document.getElementById(componentId);
    updateNavbarDisplay(component, props);
}

// 添加子菜单项
function addSubMenuItem(componentId, parentIndex) {
    const props = navbarComponent.properties;
    if (!props.menuItems[parentIndex].children) {
        props.menuItems[parentIndex].children = [];
    }
    props.menuItems[parentIndex].children.push({ name: '新子菜单', link: '#' });

    // 记住所有子菜单的展开状态
    const expandedStates = saveAllSubmenuStates(componentId, props.menuItems.length);

    const component = document.getElementById(componentId);
    updatePropertiesPanel(component);
    updateNavbarDisplay(component, props);

    // 恢复所有展开状态
    restoreAllSubmenuStates(componentId, expandedStates);

    // 确保当前操作的子菜单是展开的
    const newSubmenuContainer = document.getElementById(`submenu-${componentId}-${parentIndex}`);
    if (newSubmenuContainer) {
        newSubmenuContainer.style.display = 'block';
    }
}

// 删除子菜单项
function removeSubMenuItem(componentId, parentIndex, childIndex) {
    const props = navbarComponent.properties;

    // 记住所有子菜单的展开状态
    const expandedStates = saveAllSubmenuStates(componentId, props.menuItems.length);

    props.menuItems[parentIndex].children.splice(childIndex, 1);

    const component = document.getElementById(componentId);
    updatePropertiesPanel(component);
    updateNavbarDisplay(component, props);

    // 恢复所有展开状态
    restoreAllSubmenuStates(componentId, expandedStates);
}

// 选择主菜单链接
function selectMenuLink(componentId, menuIndex) {
    showLinkSelector((selectedLink) => {
        const props = navbarComponent.properties;

        // 记住所有子菜单的展开状态
        const expandedStates = saveAllSubmenuStates(componentId, props.menuItems.length);

        props.menuItems[menuIndex].link = selectedLink;

        const component = document.getElementById(componentId);
        updatePropertiesPanel(component);
        updateNavbarDisplay(component, props);

        // 恢复所有展开状态
        restoreAllSubmenuStates(componentId, expandedStates);
    });
}

// 选择子菜单链接
function selectSubMenuLink(componentId, parentIndex, childIndex) {
    showLinkSelector((selectedLink) => {
        const props = navbarComponent.properties;

        // 记住所有子菜单的展开状态
        const expandedStates = saveAllSubmenuStates(componentId, props.menuItems.length);

        props.menuItems[parentIndex].children[childIndex].link = selectedLink;

        const component = document.getElementById(componentId);
        updatePropertiesPanel(component);
        updateNavbarDisplay(component, props);

        // 恢复所有展开状态
        restoreAllSubmenuStates(componentId, expandedStates);
    });
}

// 显示链接选择器弹窗
function showLinkSelector(callback) {
    // 创建弹窗遮罩
    const overlay = document.createElement('div');
    overlay.className = 'link-selector-overlay';

    // 创建弹窗内容
    const modal = document.createElement('div');
    modal.className = 'link-selector-modal';

    // 死链接数据（后续改为动态）
    const linkOptions = [
        { name: '首页', url: '/' },
        { name: '关于我们', url: '/about' },
        { name: '产品中心', url: '/products' },
        { name: '新闻资讯', url: '/news' },
        { name: '联系我们', url: '/contact' },
        { name: '用户登录', url: '/login' },
        { name: '用户注册', url: '/register' },
        { name: '外部链接', url: 'https://www.example.com' }
    ];

    modal.innerHTML = `
        <div class="link-selector-header">
            <h3>选择链接</h3>
            <button class="close-btn" onclick="closeLinkSelector()">&times;</button>
        </div>
        <div class="link-selector-body">
            <div class="link-options">
                ${linkOptions.map(option => `
                    <div class="link-option" onclick="selectLink('${option.url}', '${option.name}')">
                        <div class="link-name">${option.name}</div>
                        <div class="link-url">${option.url}</div>
                    </div>
                `).join('')}
            </div>
        </div>
        <div class="link-selector-footer">
            <button class="cancel-btn" onclick="closeLinkSelector()">取消</button>
        </div>
    `;

    overlay.appendChild(modal);
    document.body.appendChild(overlay);

    // 保存回调函数
    window.currentLinkCallback = callback;
}

// 关闭链接选择器
function closeLinkSelector() {
    const overlay = document.querySelector('.link-selector-overlay');
    if (overlay) {
        overlay.remove();
    }
    window.currentLinkCallback = null;
}

// 选择链接
function selectLink(url, name) {
    if (window.currentLinkCallback) {
        window.currentLinkCallback(url);
    }
    closeLinkSelector();
}

// 添加动态悬停样式
function addNavbarHoverStyles(componentId, props) {
    // 移除旧的样式
    const oldStyle = document.getElementById(`navbar-hover-style-${componentId}`);
    if (oldStyle) {
        oldStyle.remove();
    }

    // 创建新的样式
    const style = document.createElement('style');
    style.id = `navbar-hover-style-${componentId}`;
    style.textContent = `
        #navbar-btn1-${componentId}, #navbar-btn2-${componentId} {
            transition: all 0.2s ease !important;
            position: relative !important;
            overflow: hidden !important;
        }
        #navbar-btn1-${componentId}::before, #navbar-btn2-${componentId}::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s ease;
        }
        #navbar-btn1-${componentId}:hover {
            color: ${props.hoverColor} !important;
            border-color: ${props.hoverColor} !important;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15) !important;
            filter: brightness(1.05) !important;
        }
        #navbar-btn1-${componentId}:hover::before {
            left: 100%;
        }
        #navbar-btn2-${componentId}:hover {
            background: ${props.hoverColor} !important;
            border-color: ${props.hoverColor} !important;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15) !important;
            filter: brightness(1.05) !important;
        }
        #navbar-btn2-${componentId}:hover::before {
            left: 100%;
        }
    `;

    document.head.appendChild(style);
}

// 更新菜单项悬停样式
function updateMenuItemsHoverStyle(contentDiv, hoverColor, textColor) {
    const componentId = contentDiv.closest('[id]').id;

    // 移除旧的样式
    const oldStyle = document.getElementById(`navbar-menu-hover-style-${componentId}`);
    if (oldStyle) {
        oldStyle.remove();
    }

    // 设置CSS变量到根元素
    document.documentElement.style.setProperty('--navbar-hover-color', hoverColor);

    // 创建新的样式
    const style = document.createElement('style');
    style.id = `navbar-menu-hover-style-${componentId}`;
    style.textContent = `
        #${componentId} .navbar-item {
            transition: all 0.2s ease !important;
            border-radius: 6px !important;
            border: 2px solid transparent !important;
            position: relative !important;
            overflow: hidden !important;
        }
        #${componentId} .navbar-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(${hexToRgb(hoverColor)}, 0.4), transparent);
            transition: left 0.5s ease;
        }
        #${componentId} .navbar-item:hover {
            color: ${hoverColor} !important;
            border: 2px solid ${hoverColor} !important;
            background: rgba(${hexToRgb(hoverColor)}, 0.1) !important;
        }
        #${componentId} .navbar-item:hover::before {
            left: 100%;
        }
        #${componentId} .navbar-dropdown .dropdown-item:hover {
            background: ${hoverColor} !important;
            color: white !important;
        }
        #${componentId} .navbar-dropdown .dropdown-item:hover::before {
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent) !important;
        }
    `;

    document.head.appendChild(style);
}

// 辅助函数：将十六进制颜色转换为RGB
function hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ?
        parseInt(result[1], 16) + ',' + parseInt(result[2], 16) + ',' + parseInt(result[3], 16) :
        '102, 126, 234';
}

// 注册导航栏组件
if (typeof ComponentManager !== 'undefined') {
    ComponentManager.register('navbar', navbarComponent, generateNavbarProperties, updateNavbarDisplay);
}

// 初始化日志
console.log('🧭 导航栏组件已加载');

<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * QiyeDIY企业建站系统 - 后端入口文件
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 简单的API响应
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// API路由处理
if (strpos($uri, '/api/') === 0) {
    $path = str_replace('/api/', '', $uri);
    
    switch ($path) {
        case 'test':
            $response = [
                'code' => 200,
                'message' => 'QiyeDIY API 运行正常',
                'data' => [
                    'version' => '1.0.0',
                    'time' => date('Y-m-d H:i:s'),
                    'server' => 'PHP ' . PHP_VERSION
                ]
            ];
            break;
            
        case 'auth/login':
            $response = [
                'code' => 200,
                'message' => '登录成功',
                'data' => [
                    'token' => 'demo_token_' . time(),
                    'user' => [
                        'id' => 1,
                        'username' => 'admin',
                        'nickname' => '管理员'
                    ]
                ]
            ];
            break;
            
        default:
            $response = [
                'code' => 404,
                'message' => 'API接口不存在',
                'data' => null
            ];
            break;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
} else {
    // 默认响应
    echo json_encode([
        'message' => 'QiyeDIY Backend Server',
        'version' => '1.0.0',
        'time' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}

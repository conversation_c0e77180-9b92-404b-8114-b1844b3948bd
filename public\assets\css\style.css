﻿/*
 * 现代企业官网样式文件
 * 专业、美观、响应式设计
 */

/* ===== CSS变量定义 - 三只鱼清新风�?===== */
:root {
    /* 主色�?- 清新淡雅色系 */
    --primary-color: #4fc3f7;
    --primary-dark: #29b6f6;
    --primary-light: #81d4fa;

    /* 辅助�?*/
    --secondary-color: #26c6da;
    --accent-color: #80deea;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;

    /* 文字颜色 */
    --text-primary: #37474f;
    --text-secondary: #546e7a;
    --text-light: #90a4ae;
    --text-white: #ffffff;

    /* 背景�?*/
    --bg-primary: #ffffff;
    --bg-secondary: #fafafa;
    --bg-light: #f5f5f5;
    --bg-dark: #263238;

    /* 渐变�?- 清新淡雅�?*/
    --gradient-primary: linear-gradient(135deg, #e1f5fe 0%, #b3e5fc 50%, #81d4fa 100%);
    --gradient-secondary: linear-gradient(135deg, #f0f9ff 0%, #e0f2f1 100%);
    --gradient-accent: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    --gradient-hero: linear-gradient(135deg, #f0f9ff 0%, #e1f5fe 30%, #b3e5fc 70%, #81d4fa 100%);

    /* 字体 */
    --font-primary: 'Segoe UI', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-secondary: 'Georgia', 'Times New Roman', serif;

    /* 阴影 */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);

    /* 圆角 */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;

    /* 过渡 */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* ===== 基础重置样式 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    font-size: 1rem;
    line-height: 1.7;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== 通用容器 ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.container-fluid {
    width: 100%;
    padding: 0 20px;
}

/* ===== 通用按钮样式 ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-white);
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--text-white);
    transform: translateY(-2px);
}

.btn-accent {
    background: var(--gradient-accent);
    color: var(--text-white);
    box-shadow: var(--shadow-lg);
}

.btn-accent:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-lg {
    padding: 16px 32px;
    font-size: 1.1rem;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 0.9rem;
}

/* ===== 头部导航样式 - 修复菜单布局 ===== */
header {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    z-index: 9999 !important;
    background: transparent !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: none !important;
    transition: all 0.3s ease !important;
    border: none !important;
    min-height: 70px !important;
}

/* 滚动时的背景样式 */
header.scrolled {
    background: #2f4c99f5 !important;
    background-color: #2f4c99f5 !important;
    min-height: 70px !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(15px) !important;
    -webkit-backdrop-filter: blur(15px) !important;
    box-shadow: 0 2px 20px rgba(47, 76, 153, 0.3) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* 确保navbar也有背景 */
header.scrolled .navbar,
header.scrolled .navbar-expand-lg,
header.scrolled .navbar-light {
    background: #2f4c99f5 !important;
    background-color: #2f4c99f5 !important;
}

/* 移除导航栏子元素的背景，但排除下拉菜单 */
header.scrolled .navbar .nav-link,
header.scrolled .navbar .navbar-brand,
header.scrolled .navbar .navbar-toggler,
header.scrolled .navbar .btn {
    background: transparent !important;
}

/* 确保滚动时下拉菜单保持白色背景 */
header.scrolled .dropdown-menu,
header.scrolled .mega-dropdown .dropdown-menu {
    background: #ffffff !important;
    background-color: #ffffff !important;
    opacity: 1 !important;
}

/* 确保滚动时下拉菜单项的悬停效果正常 */
header.scrolled .dropdown-item:hover,
header.scrolled .mega-menu-item:hover,
header.scrolled .solution-item:hover,
header.scrolled .product-item:hover {
    background: rgba(0, 123, 255, 0.08) !important;
}

.navbar {
    padding: 12px 0 !important;
    background: transparent !important;
    border: none !important;
    transition: background 0.3s ease, backdrop-filter 0.3s ease !important;
    min-height: 70px !important;
    display: flex !important;
    align-items: center !important;
}

/* 确保navbar容器正确布局 */
.navbar .container {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    width: 100% !important;
}

/* 滚动时navbar保持相同的内边距 */
header.scrolled .navbar {
    padding: 12px 0 !important;
}

.navbar-brand {
    font-size: 1.6rem !important;
    font-weight: 600 !important;
    color: white !important;
    text-decoration: none !important;
    display: flex !important;
    align-items: center !important;
    background: none !important;
    -webkit-background-clip: unset !important;
    -webkit-text-fill-color: white !important;
    background-clip: unset !important;
    transition: all 0.3s ease !important;
    padding: 0 !important;
    margin-right: auto !important; /* 让logo靠左 */
}

/* 滚动时品牌文字颜色变�?*/
header.scrolled .navbar-brand {
    color: white !important;
    -webkit-text-fill-color: white !important;
}

.navbar-brand .logo {
    height: 45px !important;
    width: auto !important;
    margin-right: 10px !important;
    filter: brightness(0) invert(1) !important;
    transition: all 0.3s ease !important;
    vertical-align: middle !important;
}

/* 滚动时Logo颜色变化 */
header.scrolled .navbar-brand .logo {
    filter: none !important;
}

/* 导航菜单折叠容器 */
.navbar-collapse {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-end !important; /* 让菜单靠�?*/
    flex-grow: 1 !important;
}

/* 导航菜单容器对齐 */
.navbar-nav {
    display: flex !important;
    flex-direction: row !important; /* 确保水平排列 */
    align-items: center !important;
    margin: 0 !important;
    list-style: none !important;
    padding: 0 !important;
}

/* 导航链接样式 */
.navbar-nav .nav-item {
    margin: 0 5px !important;
}

.navbar-nav .nav-link {
    color: white !important;
    font-weight: 400 !important;
    padding: 12px 20px !important;
    position: relative !important;
    transition: color 0.3s ease !important;
    font-size: 15px !important;
    background: none !important;
    border: none !important;
    display: flex !important;
    align-items: center !important;
    line-height: 1.2 !important;
    height: 45px !important;
    box-sizing: border-box !important;
    text-decoration: none !important;
    white-space: nowrap !important; /* 防止文字换行 */
}

/* 导航链接悬停效果 - 加强优先�?*/
.navbar-nav .nav-link:hover {
    color: #ff6b35 !important;
}

/* 导航链接下划线效�?- 排除下拉菜单项 */
.navbar-nav .nav-link:not(.dropdown-toggle)::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:not(.dropdown-toggle):hover::after {
    width: 80%;
}

/* 激活状态的导航链接样式 */
.navbar-nav .nav-link.active {
    color: #ff6b35 !important;
}

.navbar-nav .nav-link.active:not(.dropdown-toggle)::after {
    width: 80%;
    background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
}

/* 滚动时导航链接颜色变�?*/
header.scrolled .navbar-nav .nav-link {
    color: white !important;
    padding: 12px 20px !important;
    height: 45px !important;
}

/* 滚动时导航链接悬停效�?- 加强优先�?*/
header.scrolled .navbar-nav .nav-link:hover {
    color: #ff6b35 !important;
}

/* 滚动时激活状态的导航链接样式 */
header.scrolled .navbar-nav .nav-link.active {
    color: #ff6b35 !important;
}

header.scrolled .navbar-nav .nav-link.active:not(.dropdown-toggle)::after {
    width: 80%;
    background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
}

/* 右侧按钮组对�?*/
.navbar-actions {
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
    margin-left: 20px !important; /* 与菜单保持间�?*/
    height: 45px !important;
    flex-shrink: 0 !important; /* 防止压缩 */
}

.search-btn {
    color: white !important;
    font-size: 16px !important;
    padding: 10px !important;
    transition: color 0.3s ease, background 0.3s ease !important;
    border-radius: 4px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 36px !important;
    height: 36px !important;
    box-sizing: border-box !important;
    text-decoration: none !important;
}

/* 登录注册按钮样式 */
.navbar-actions .btn {
    padding: 8px 18px !important;
    font-size: 14px !important;
    border-radius: 4px !important;
    font-weight: 400 !important;
    transition: color 0.3s ease, background 0.3s ease, border-color 0.3s ease !important;
    text-decoration: none !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    height: 36px !important;
    box-sizing: border-box !important;
    white-space: nowrap !important;
}

.navbar-actions .btn-outline-light {
    color: white !important;
    background: transparent !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
}

.navbar-actions .btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: white !important;
}

.navbar-actions .btn-primary {
    background: #ff6b35 !important;
    border-color: #ff6b35 !important;
    color: white !important;
}

.navbar-actions .btn-primary:hover {
    background: #e55a2b !important;
    border-color: #e55a2b !important;
}

/* 滚动时按钮样式调�?*/
header.scrolled .navbar-actions .btn-outline-light {
    color: white !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
}

header.scrolled .navbar-actions .btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: white !important;
}


/* 移动端菜单按钮样�?*/
.navbar-toggler {
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    padding: 4px 8px !important;
    transition: all 0.3s ease !important;
}

/* 滚动时移动端按钮样式 */
header.scrolled .navbar-toggler {
    border: 1px solid rgba(255, 255, 255, 0.4) !important; /* 白色边框 */
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
    transition: all 0.3s ease !important;
}

/* 滚动时移动端按钮图标 */
header.scrolled .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
}

/* 确保导航菜单在移动端也是白色 */
@media (max-width: 991px) {
    .navbar-collapse {
        background: rgba(47, 76, 153, 0.98) !important; /* 使用相同的蓝色背�?*/
        border-radius: 8px !important;
        margin-top: 10px !important;
        padding: 20px !important;
        transition: all 0.3s ease !important;
    }
    
    /* 滚动时移动端菜单背景 */
    header.scrolled .navbar-collapse {
        background: rgba(47, 76, 153, 0.98) !important; /* 使用相同的蓝色背�?*/
        backdrop-filter: blur(10px) !important;
        -webkit-backdrop-filter: blur(10px) !important;
        box-shadow: 0 4px 20px rgba(47, 76, 153, 0.3) !important; /* 蓝色阴影 */
}

.navbar-nav .nav-link {
        color: white !important;
        padding: 12px 0 !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        margin: 0 !important;
        text-align: left !important;
    }
    
    /* 滚动时移动端链接颜色 */
    header.scrolled .navbar-nav .nav-link {
        color: white !important; /* 在蓝色背景上使用白色文字 */
        border-bottom: 1px solid rgba(255, 255, 255, 0.15) !important; /* 白色边框 */
    }
    
    .navbar-nav .nav-link:last-child {
        border-bottom: none !important;
    }
    
    /* 移动端下划线效果 - 排除下拉菜单项 */
    .navbar-nav .nav-link:not(.dropdown-toggle)::after {
        content: '';
        position: absolute;
        width: 0;
        height: 2px;
        bottom: 0;
        left: 50%;
        background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
        transition: all 0.3s ease;
        transform: translateX(-50%);
    }
    
    .navbar-nav .nav-link:not(.dropdown-toggle):hover::after {
        width: 80%;
}

.navbar-nav .nav-link:hover {
        color: #ff6b35 !important;
    }

    /* 移动端激活状态的导航链接样式 */
    .navbar-nav .nav-link.active {
        color: #ff6b35 !important;
    }

    .navbar-nav .nav-link.active:not(.dropdown-toggle)::after {
        width: 80%;
    }
    
    /* 移动端按钮组 */
    .navbar-actions {
        margin-top: 15px !important;
        padding-top: 15px !important;
        border-top: 1px solid rgba(255, 255, 255, 0.15) !important; /* 白色边框 */
        justify-content: center !important;
    }
    
    header.scrolled .navbar-actions {
        border-top: 1px solid rgba(255, 255, 255, 0.15) !important; /* 白色边框 */
    }
    
    .navbar-actions .btn {
        margin: 0 5px !important;
    }
}

/* 移除顶部信息栏，因为设计中没�?*/
.top-bar {
    display: none !important;
}

/* 移除咨询按钮，保持简洁设�?*/
.navbar-btn {
    display: none !important;
}

.logo {
    height: 45px;
    width: auto;
    transition: var(--transition-fast);
}

.navbar-brand:hover .logo {
    transform: scale(1.05);
}

.navbar-brand {
    font-size: 2rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-decoration: none;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2.5rem;
    margin: 0;
}

.nav-menu a {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    position: relative;
    transition: var(--transition-normal);
    padding: 0.5rem 0;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background: var(--gradient-primary);
    transition: var(--transition-normal);
}

.nav-menu a:hover::after,
.nav-menu a.active::after {
    width: 100%;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

/* ===== 主要内容区域 ===== */
main {
    margin-top: 0 !important; /* 移除顶部边距，让背景完全填充 */
}

/* ===== 全页面动态背�?- 三只鱼风�?===== */
body {
    transition: background 1s ease;
}

body.bg-red {
    background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
}

body.bg-blue {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
}

body.bg-green {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

/* ===== 英雄区域样式 - 调整为三只鱼网络的高�?===== */
.hero-section {
    position: relative;
    height: 630px; /* 调整为和三只鱼网络一样的高度 */
    min-height: 630px; /* 设置最小高�?*/
    width: 100vw;
    overflow: hidden;
    display: flex;
    align-items: center;
    padding: 0;
    margin: 0;
    top: 0;
    left: 0;
    /* 添加默认背景，确保页面加载时立即显示 */
    background-image: url('/assets/images/tu1.png');
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
}

.hero-slider {
    position: relative;
    width: 100%;
    height: 630px; /* 调整轮播图高度为630px */
    z-index: 3;
    overflow: hidden; /* 确保内容不溢�?*/
}

.hero-slider .swiper-slide {
    display: flex !important;
    align-items: center;
    justify-content: center;
    position: relative !important;
    width: 100%;
    height: 100%;
    opacity: 1;
    visibility: visible;
}

/* Swiper激活的幻灯�?- 确保显示 */
.hero-slider .swiper-slide-active {
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 10 !important;
}

/* 确保swiper-wrapper正确设置 */
.hero-slider .swiper-wrapper {
    position: relative !important;
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
}

/* 动态背景层 - 调整高度 */
.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    /* 默认显示第一张背景图片，无需等待JavaScript */
    background-image: url('/assets/images/tu1.png') !important;
    background-size: cover !important;
    background-position: center center !important;
    background-repeat: no-repeat !important;
    opacity: 1 !important;
    /* 过渡效果只在切换时生�?*/
    transition: background-image 0.8s ease-in-out, opacity 0.3s ease;
}

/* 针对高分辨率屏幕优化 - 移除，使用标准设�?*/

#heroSection.bg-slide-1 .hero-background {
    background-image: url('/assets/images/tu1.png') !important;
    /* 确保图片以原始质量显�?*/
    background-size: cover !important;
    background-position: center center !important;
    opacity: 1 !important;
    transition: background-image 0.8s ease-in-out !important;
}

#heroSection.bg-slide-2 .hero-background {
    background-image: url('/assets/images/tu2.png') !important;
    background-size: cover !important;
    background-position: center center !important;
    opacity: 1 !important;
    transition: background-image 0.8s ease-in-out !important;
}

#heroSection.bg-slide-3 .hero-background {
    background-image: url('/assets/images/tu3.png') !important;
    background-size: cover !important;
    background-position: center center !important;
    opacity: 1 !important;
    transition: background-image 0.8s ease-in-out !important;
}

/* 添加第四张幻灯片背景 */
#heroSection.bg-slide-4 .hero-background {
    background-image: url('/assets/images/tu4.png') !important;
    background-size: cover !important;
    background-position: center center !important;
    opacity: 1 !important;
    transition: background-image 0.8s ease-in-out !important;
}

/* 如果有第五张幻灯片，循环使用tu1 */
#heroSection.bg-slide-5 .hero-background {
    background-image: url('/assets/images/tu1.png') !important;
    background-size: cover !important;
}

/* 添加遮罩层样�?*/
.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4); /* 半透明黑色遮罩 */
    z-index: 2; /* 确保在背景之上，内容之下 */
}

/* 装饰元素 - 简化设�?*/
.hero-decorations {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    pointer-events: none;
}

/* 简化装饰圆�?- 只保留少�?*/
.decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.05);
    animation: float 12s ease-in-out infinite;
    border: 1px solid rgba(255, 255, 255, 0.08);
    transition: all 1s ease;
}

.circle-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    right: 5%;
    animation-delay: 0s;
}

.circle-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 4s;
}

.circle-3 {
    width: 100px;
    height: 100px;
    top: 80%;
    right: 25%;
    animation-delay: 8s;
}

/* 简化浮动图�?- 更像三只鱼的风格 */
.floating-icon {
    position: absolute;
    color: rgba(255, 255, 255, 0.6);
    font-size: 1.8rem;
    animation: floatIcon 15s ease-in-out infinite;
    background: rgba(255, 255, 255, 0.08);
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 1s ease;
}

.icon-1 {
    top: 15%;
    right: 8%;
    animation-delay: 0s;
}

.icon-2 {
    top: 35%;
    right: 3%;
    animation-delay: 3s;
}

.icon-3 {
    top: 55%;
    right: 12%;
    animation-delay: 6s;
}

.icon-4 {
    top: 75%;
    right: 6%;
    animation-delay: 9s;
}

.icon-5 {
    top: 25%;
    right: 18%;
    animation-delay: 12s;
}



/* 内容样式 - 居中布局优化 */
.hero-content {
    position: relative;
    z-index: 20 !important;
    color: white !important;
    text-align: center;
    padding: 3rem 0;
    max-width: 800px;
    margin: 0 auto;
    /* 移除背景和模糊效果，保持简�?*/
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.hero-title {
    font-size: 3.2rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: white !important;
    text-shadow: 0 3px 20px rgba(0, 0, 0, 0.8) !important;
    letter-spacing: -0.01em;
    position: relative;
    z-index: 25 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.hero-subtitle {
    font-size: 1.4rem;
    font-weight: 400;
    margin-bottom: 1rem;
    color: rgba(255, 255, 255, 0.95) !important;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.6) !important;
    position: relative;
    z-index: 25 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.hero-description {
    font-size: 1.1rem;
    margin-bottom: 2.5rem;
    color: rgba(255, 255, 255, 0.9) !important;
    line-height: 1.7;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    text-shadow: 0 1px 8px rgba(0, 0, 0, 0.6) !important;
    position: relative;
    z-index: 25 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    justify-content: center;
    position: relative;
    z-index: 25 !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.hero-buttons .btn {
    padding: 16px 36px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 160px;
    position: relative;
    overflow: hidden;
    text-shadow: none;
}

.hero-buttons .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.15), transparent);
    transition: left 0.5s ease;
}

.hero-buttons .btn:hover::before {
    left: 100%;
}

.hero-buttons .btn-primary {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.4);
    color: white;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.hero-buttons .btn-primary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.hero-buttons .btn-outline-light {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.5);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.hero-buttons .btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.7);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 图片样式 - 更简�?*/
.hero-image {
    position: relative;
    z-index: 4;
    text-align: center;
    padding: 2rem 0;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 15px 35px rgba(0, 0, 0, 0.2));
    transition: transform 0.3s ease;
    border-radius: 12px;
}

.hero-image:hover img {
    transform: translateY(-8px);
}

/* 轮播图控制器样式 - 居中显示 */
.swiper-pagination {
    bottom: 30px !important;
    z-index: 5;
    text-align: center !important; /* 确保居中 */
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: auto !important; /* 自动宽度 */
}

.swiper-pagination-bullet {
    width: 12px !important;
    height: 12px !important;
    background: rgba(255, 255, 255, 0.5) !important;
    opacity: 1 !important;
    transition: all 0.3s ease !important;
    margin: 0 6px !important; /* 增加间距 */
    border-radius: 50% !important;
    display: inline-block !important;
    cursor: pointer !important;
}

.swiper-pagination-bullet-active {
    background: white !important;
    transform: scale(1.3) !important; /* 激活时稍微放大 */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important; /* 添加阴影 */
}

.swiper-button-next,
.swiper-button-prev {
    color: white !important;
    background: rgba(255, 255, 255, 0.1);
    width: 40px;
    height: 40px;
    border-radius: 8px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.swiper-button-next::after,
.swiper-button-prev::after {
    font-size: 14px;
    font-weight: 600;
}

/* 动画效果 - 更柔和简�?*/
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(1deg);
    }
}

@keyframes floatIcon {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
    }
    33% {
        transform: translateY(-10px) rotate(2deg) scale(1.02);
    }
    66% {
        transform: translateY(-5px) rotate(-1deg) scale(1.01);
    }
}

/* 响应式设�?*/
@media (max-width: 1200px) {
    .hero-title {
        font-size: 2.4rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .floating-icon {
        font-size: 1.5rem;
        width: 45px;
        height: 45px;
    }
    
    .decoration-circle {
        opacity: 0.6;
    }
}

@media (max-width: 992px) {
    .hero-title {
        font-size: 2.2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .hero-content {
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .floating-icon {
        opacity: 0.5;
    }
    
    .decoration-circle {
        opacity: 0.3;
    }
}

@media (max-width: 768px) {
    .hero-section {
        min-height: 60vh; /* 移动端进一步减少高�?*/
        height: 60vh;
    }
    
    .hero-background {
        height: 60vh; /* 移动端背景高�?*/
        min-height: 60vh;
    }
    
    .hero-slider {
        height: 60vh; /* 移动端轮播图高度 */
    }
    
    .hero-title {
        font-size: 1.8rem;
        margin-bottom: 1rem;
    }
    
    .hero-subtitle {
        font-size: 0.95rem;
    }
    
    .hero-description {
        font-size: 0.9rem;
        margin-bottom: 2rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }
    
    .hero-buttons .btn {
        width: 100%;
        max-width: 260px;
    }
    
    .floating-icon {
        display: none;
    }
    
    .decoration-circle {
        opacity: 0.2;
    }
    
    .swiper-button-next,
    .swiper-button-prev {
        display: none;
    }
    
    .hero-image {
        padding: 1rem 0;
    }
}

/* ===== 特色服务区域 ===== */
.features-section {
    padding: 6rem 0;
    /* 移除ceo-home-vip-bg.png，改为使用渐变背�?*/
    background: var(--gradient-secondary);
    position: relative;
    z-index: 5; /* 确保在轮播图之上 */
}

/* 移除特色服务的背景遮罩，因为不再使用图片背景 */
.features-overlay {
    display: none;
}

/* 恢复特色服务项的原始样式 */
.feature-item-overlay {
    background: var(--bg-primary) !important;
    border-radius: var(--radius-lg) !important;
    box-shadow: var(--shadow-md) !important;
    padding: 2.5rem 1.5rem !important;
    transition: var(--transition-normal) !important;
    height: 100% !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
}

.feature-item-overlay:hover {
    transform: translateY(-10px) !important;
    box-shadow: var(--shadow-xl) !important;
}

/* 恢复特色服务文字颜色 */
.features-section h5 {
    color: var(--text-primary) !important;
}

.features-section p {
    color: var(--text-secondary) !important;
}

.features-section .section-title {
    color: var(--text-primary) !important;
}

.features-section .section-subtitle {
    color: var(--text-secondary) !important;
}

.feature-item {
    padding: 2.5rem 1.5rem;
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
}

.feature-item h5 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.feature-item p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 0;
}

/* 特色图片样式 */
.feature-image {
    width: 120px;
    height: 120px;
    margin: 0 auto 2rem;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid var(--accent-color);
    transition: var(--transition-normal);
    position: relative;
    background: var(--bg-light);
}

.feature-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-normal);
}

.feature-item:hover .feature-image {
    transform: scale(1.05);
    border-color: var(--primary-color);
    box-shadow: 0 10px 30px rgba(0, 180, 216, 0.3);
}

.feature-item:hover .feature-image img {
    transform: scale(1.1);
}

/* ===== 企业展示区域 ===== */
.company-showcase {
    padding: 100px 0;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 600px;
    background-attachment: fixed !important;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    image-rendering: optimizeQuality;
    filter: contrast(1.08) brightness(1.03) saturate(1.05);
}

/* 动态背景装�?*/
.company-showcase::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.05)"><polygon points="0,0 1000,0 1000,100 0,80"/></svg>') no-repeat top;
    background-size: cover;
    z-index: 1;
}

.company-showcase::after {
    content: '';
    position: absolute;
    top: 20%;
    right: -10%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: float 8s ease-in-out infinite;
    z-index: 1;
}

/* 遮罩层优�?*/
.company-showcase .hero-overlay {
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(1px);
    z-index: 2;
}

/* 背景图片清晰度优化 */
.company-showcase[style*="shangjairuzhu.jpg"] {
    filter: contrast(1.15) brightness(1.08) saturate(1.1);
    background-blend-mode: normal;
    -webkit-backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
}

.company-showcase[style*="shangjairuzhu.jpg"]::before {
    opacity: 0.3;
}

.company-showcase[style*="shangjairuzhu.jpg"] .hero-overlay {
    background: rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(0.5px);
}

/* 高分辨率设备优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .company-showcase[style*="shangjairuzhu.jpg"] {
        filter: contrast(1.2) brightness(1.1) saturate(1.15);
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* 内容容器 */
.showcase-content {
    position: relative;
    z-index: 3;
    padding: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.showcase-content:hover {
    transform: translateY(-10px);
    box-shadow: 0 35px 70px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.15);
}

/* 标题美化 */
.showcase-content .section-title {
    font-size: 3rem;
    font-weight: 800;
    color: white;
    margin-bottom: 2rem;
    text-shadow: 3px 3px 10px rgba(0, 0, 0, 0.8);
    letter-spacing: 2px;
    /* 添加渐变文字效果 */
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    /* 备用颜色 */
    color: white;
    position: relative;
}

/* 为标题添加发光效�?*/
.company-bg-section .bg-title::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    color: rgba(255, 255, 255, 0.1);
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    z-index: -1;
}

.company-bg-section .bg-subtitle {
    font-size: 1.4rem;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 3rem;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.7);
    font-weight: 300;
    line-height: 1.6;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 3rem;
}

.company-bg-section .bg-actions {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.company-bg-section .bg-actions .btn {
    padding: 18px 40px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px; /* 圆角按钮 */
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 12px;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
    min-width: 180px;
    justify-content: center;
    /* 添加按钮阴影确保可见�?*/
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* 按钮悬停动画效果 */
.company-bg-section .bg-actions .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
    z-index: -1;
}

.company-bg-section .bg-actions .btn:hover::before {
    left: 100%;
}

.company-bg-section .bg-actions .btn i {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
    z-index: 10;
    position: relative;
}

.company-bg-section .bg-actions .btn span {
    z-index: 10;
    position: relative;
}

.company-bg-section .bg-actions .btn-light {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
    color: #333 !important;
    border: none;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.company-bg-section .bg-actions .btn-light:hover {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 15px 35px rgba(52, 152, 219, 0.4) !important;
    color: #222 !important;
}

.company-bg-section .bg-actions .btn-light:hover i {
    transform: rotate(360deg) scale(1.1);
    color: #222 !important;
}

.company-bg-section .bg-actions .btn-light:hover span {
    color: #222 !important;
}

.company-bg-section .bg-actions .btn-light * {
    color: #333 !important;
    z-index: 10 !important;
    position: relative !important;
}

.company-bg-section .bg-actions .btn-light:hover * {
    color: #222 !important;
}

.company-bg-section .bg-actions .btn-outline-light {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.company-bg-section .bg-actions .btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: white;
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 15px 35px rgba(255, 255, 255, 0.2);
    color: white;
}

.company-bg-section .bg-actions .btn-outline-light:hover i {
    transform: rotate(-360deg) scale(1.1);
}

/* 移动端适配 */
@media (max-width: 768px) {
    .company-bg-section {
        min-height: 400px !important;
        padding: 60px 0 !important;
        background-attachment: scroll !important; /* 移动端不使用fixed */
    }
    
    .company-bg-section .bg-title {
        font-size: 2.2rem;
    }
    
    .company-bg-section .bg-subtitle {
        font-size: 1.1rem;
    }
    
    .company-bg-section .bg-actions {
        flex-direction: column;
    align-items: center;
    }
    
    .company-bg-section .bg-actions .btn {
        width: 100%;
        max-width: 280px;
    }
}

/* ===== 通用标题样式 ===== */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    text-align: center;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    text-align: center;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.showcase-content .section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
    border-radius: 2px;
    animation: expandWidth 2s ease-out;
}

@keyframes expandWidth {
    from { width: 0; }
    to { width: 80px; }
}

/* 副标题美�?*/
.showcase-content .section-subtitle {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;
    line-height: 1.6;
    text-shadow: 1px 1px 5px rgba(0, 0, 0, 0.2);
}

/* 特色列表美化 */
.showcase-features {
    margin: 40px 0;
}

.showcase-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    border-left: 4px solid #4fc3f7;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.showcase-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.6s ease;
}

.showcase-item:hover::before {
    left: 100%;
}

.showcase-item:hover {
    transform: translateX(10px);
    background: rgba(255, 255, 255, 0.15);
    border-left-color: #29b6f6;
    box-shadow: 0 10px 25px rgba(79, 195, 247, 0.3);
}

.showcase-item i {
    font-size: 24px;
    color: #4fc3f7;
    margin-right: 15px;
    transition: all 0.3s ease;
    min-width: 30px;
}

.showcase-item:hover i {
    color: #29b6f6;
    transform: scale(1.2) rotate(360deg);
}

.showcase-item span {
    font-size: 1.1rem;
    font-weight: 600;
    color: white;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
    transition: color 0.3s ease;
}

.showcase-item:hover span {
    color: #f0f9ff;
}

/* 按钮美化 */
.showcase-content .btn {
    padding: 15px 35px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
    margin: 0 10px 10px 0;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.showcase-content .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.showcase-content .btn:hover::before {
    left: 100%;
}

.showcase-content .btn-primary {
    background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
    border: none;
    color: white;
}

.showcase-content .btn-primary:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 15px 35px rgba(79, 195, 247, 0.4);
    background: linear-gradient(135deg, #29b6f6 0%, #1976d2 100%);
}

.showcase-content .btn-outline-primary {
    background: transparent !important;
    border: 2px solid white !important;
    color: white !important;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    outline: none !important;
    box-sizing: border-box !important;
    box-shadow: none !important;
}

.showcase-content .btn-outline-primary:focus,
.showcase-content .btn-outline-primary:active {
    outline: none !important;
    box-shadow: none !important;
    border: 2px solid white !important;
}

/* 白光特效 */
.showcase-content .btn-outline-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s ease;
    z-index: -1;
}

.showcase-content .btn-outline-primary:hover::before {
    left: 100%;
}

.showcase-content .btn-outline-primary:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border: 2px solid #667eea !important;
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4), 0 5px 15px rgba(118, 75, 162, 0.3) !important;
    outline: none !important;
}

.showcase-content .btn-outline-primary:hover:focus,
.showcase-content .btn-outline-primary:hover:active {
    outline: none !important;
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4), 0 5px 15px rgba(118, 75, 162, 0.3) !important;
    border: 2px solid #667eea !important;
}

/* 确保文字和图标始终可见 */
.showcase-content .btn-outline-primary *,
.showcase-content .btn-outline-primary {
    z-index: 10 !important;
    position: relative !important;
}

.showcase-content .btn-outline-primary:hover * {
    color: white !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
}

/* 图片区域美化 */
.showcase-image {
    position: relative;
    z-index: 3;
    text-align: center;
    padding: 20px;
}

.showcase-image img {
    border-radius: 20px;
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.4);
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.showcase-image:hover img {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 40px 80px rgba(0, 0, 0, 0.5);
}

/* 添加装饰元素 */
.showcase-image::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120%;
    height: 120%;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    border-radius: 50%;
    animation: rotate 20s linear infinite;
    z-index: 1;
}

@keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 响应式设�?*/
@media (max-width: 992px) {
    .company-showcase {
        padding: 80px 0;
    }
    
    .showcase-content {
        margin-bottom: 40px;
        padding: 30px 20px;
    }
    
    .showcase-content .section-title {
        font-size: 2.5rem;
    }

    .showcase-content .section-subtitle {
        font-size: 1.1rem;
    }

    .showcase-item {
        padding: 12px 15px;
    }
    
    .showcase-item span {
        font-size: 1rem;
    }
}

@media (max-width: 768px) {
    .company-showcase {
        padding: 60px 0;
    }
    
    .showcase-content .section-title {
        font-size: 2rem;
    }

    .showcase-content .btn {
        padding: 12px 25px;
        font-size: 1rem;
        margin: 5px;
        width: 100%;
        max-width: 200px;
    }
    
    .showcase-item {
        flex-direction: column;
        text-align: center;
        padding: 15px;
    }

    .showcase-item i {
        margin-right: 0;
        margin-bottom: 10px;
    }
}

/* ===== 解决方案区域 ===== */
.solutions-section {
    padding: 6rem 0 0 0;
    background: var(--bg-primary);
    position: relative;
    z-index: 5; /* 确保在轮播图之上 */
}

.solution-card {
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    overflow: hidden;
    height: 100%;
}

.solution-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.solution-card .card-img-top {
    height: 200px;
    object-fit: cover;
    transition: var(--transition-normal);
}

.solution-card:hover .card-img-top {
    transform: scale(1.05);
}

.solution-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-accent);
    border-radius: 50%;
    color: white;
    font-size: 1.5rem;
}

.solution-icon img {
    width: 30px;
    height: 30px;
    object-fit: contain;
}

.solution-card .card-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.solution-card .card-text {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

/* ===== 数据统计区域 ===== */
.stats-section {
    padding: 6rem 0;
    background: var(--gradient-hero);
    position: relative;
    overflow: hidden;
    z-index: 5; /* 确保在轮播图之上 */
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="0,0 1000,0 1000,100 0,80"/></svg>') no-repeat bottom;
    background-size: cover;
}

.stat-item {
    text-align: center;
    color: white;
    position: relative;
    z-index: 2;
}

.stat-item h3 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: white;
}

.stat-item p {
    font-size: 1.1rem;
    margin-bottom: 0;
    opacity: 0.9;
}

.counter {
    display: inline-block;
}

/* ===== 通用标题样式 ===== */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    text-align: center;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    text-align: center;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.showcase-content .section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
    border-radius: 2px;
    animation: expandWidth 2s ease-out;
}

@keyframes expandWidth {
    from { width: 0; }
    to { width: 80px; }
}

/* 副标题美�?*/
.showcase-content .section-subtitle {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;
    line-height: 1.6;
    text-shadow: 1px 1px 5px rgba(0, 0, 0, 0.2);
}

/* 特色列表美化 */
.showcase-features {
    margin: 40px 0;
}

.showcase-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    border-left: 4px solid #4fc3f7;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.showcase-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.6s ease;
}

.showcase-item:hover::before {
    left: 100%;
}

.showcase-item:hover {
    transform: translateX(10px);
    background: rgba(255, 255, 255, 0.15);
    border-left-color: #29b6f6;
    box-shadow: 0 10px 25px rgba(79, 195, 247, 0.3);
}

.showcase-item i {
    font-size: 24px;
    color: #4fc3f7;
    margin-right: 15px;
    transition: all 0.3s ease;
    min-width: 30px;
}

.showcase-item:hover i {
    color: #29b6f6;
    transform: scale(1.2) rotate(360deg);
}

.showcase-item span {
    font-size: 1.1rem;
    font-weight: 600;
    color: white;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
    transition: color 0.3s ease;
}

.showcase-item:hover span {
    color: #f0f9ff;
}

/* 按钮美化 */
.showcase-content .btn {
    padding: 15px 35px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
    margin: 0 10px 10px 0;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.showcase-content .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.showcase-content .btn:hover::before {
    left: 100%;
}

.showcase-content .btn-primary {
    background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
    border: none;
    color: white;
}

.showcase-content .btn-primary:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 15px 35px rgba(79, 195, 247, 0.4);
    background: linear-gradient(135deg, #29b6f6 0%, #1976d2 100%);
}



/* 图片区域美化 */
.showcase-image {
    position: relative;
    z-index: 3;
        text-align: center;
    padding: 20px;
}

.showcase-image img {
    border-radius: 20px;
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.4);
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.showcase-image:hover img {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 40px 80px rgba(0, 0, 0, 0.5);
}

/* 添加装饰元素 */
.showcase-image::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120%;
    height: 120%;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    border-radius: 50%;
    animation: rotate 20s linear infinite;
    z-index: 1;
}

@keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 响应式设�?*/
@media (max-width: 992px) {
    .company-showcase {
        padding: 80px 0;
    }
    
    .showcase-content {
        margin-bottom: 40px;
        padding: 30px 20px;
    }
    
    .showcase-content .section-title {
        font-size: 2.5rem;
    }

    .showcase-content .section-subtitle {
        font-size: 1.1rem;
    }

    .showcase-item {
        padding: 12px 15px;
    }
    
    .showcase-item span {
        font-size: 1rem;
    }
}

@media (max-width: 768px) {
    .company-showcase {
        padding: 60px 0;
    }
    
    .showcase-content .section-title {
        font-size: 2rem;
    }

    .showcase-content .btn {
        padding: 12px 25px;
        font-size: 1rem;
        margin: 5px;
        width: 100%;
        max-width: 200px;
    }
    
    .showcase-item {
        flex-direction: column;
        text-align: center;
        padding: 15px;
    }

    .showcase-item i {
        margin-right: 0;
        margin-bottom: 10px;
    }
}

/* ===== 解决方案区域 ===== */
.solutions-section {
    padding: 6rem 0 0 0;
    background: var(--bg-primary);
    position: relative;
    z-index: 5; /* 确保在轮播图之上 */
}

.solution-card {
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    overflow: hidden;
    height: 100%;
}

.solution-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.solution-card .card-img-top {
    height: 200px;
    object-fit: cover;
    transition: var(--transition-normal);
}

.solution-card:hover .card-img-top {
    transform: scale(1.05);
}

.solution-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-accent);
    border-radius: 50%;
    color: white;
    font-size: 1.5rem;
}

.solution-icon img {
    width: 30px;
    height: 30px;
    object-fit: contain;
}

.solution-card .card-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.solution-card .card-text {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

/* ===== 数据统计区域 ===== */
.stats-section {
    padding: 6rem 0;
    background: var(--gradient-hero);
    position: relative;
    overflow: hidden;
    z-index: 5; /* 确保在轮播图之上 */
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="0,0 1000,0 1000,100 0,80"/></svg>') no-repeat bottom;
    background-size: cover;
}

.stat-item {
    text-align: center;
    color: white;
    position: relative;
    z-index: 2;
}

.stat-item h3 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: white;
}

.stat-item p {
    font-size: 1.1rem;
    margin-bottom: 0;
    opacity: 0.9;
}

.counter {
    display: inline-block;
}

/* ===== 页脚样式 - 现代化美�?===== */
footer.footer {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: white;
    padding: 0;
    margin-top: 0;
    position: relative;
    overflow: hidden;
}

/* 页脚装饰背景 */
footer.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.03)"><polygon points="0,0 1000,0 1000,100 0,80"/></svg>') no-repeat top;
    background-size: cover;
    z-index: 1;
}

/* 主要页脚内容 */
.footer-main {
    position: relative;
    z-index: 2;
    padding: 50px 0 25px;
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(10px);
}

/* 页脚小部件 */
.footer-widget {
    height: 100%;
    padding: 22px; /* 增加内边距 */
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

/* 为中间三个部分添加特殊样式 */
.footer-widget:nth-child(2),
.footer-widget:nth-child(3),
.footer-widget:nth-child(4) {
    background: rgba(255, 255, 255, 0.08); /* 稍微增强背景 */
    border: 1px solid rgba(255, 255, 255, 0.15); /* 增强边框 */
    padding: 25px 20px; /* 增加垂直内边距 */
}

/* 为中间三个部分添加顶部装饰线 */
.footer-widget:nth-child(2)::after,
.footer-widget:nth-child(3)::after,
.footer-widget:nth-child(4)::after {
    content: '';
    position: absolute;
    top: 0;
    left: 20%;
    width: 60%;
    height: 3px;
    background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
    border-radius: 0 0 3px 3px;
    opacity: 0.8;
}

/* 为第二个部分（解决方案）添加橙色装饰 */
.footer-widget:nth-child(2)::after {
    background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
}

/* 为第三个部分（产品服务）添加绿色装饰 */
.footer-widget:nth-child(3)::after {
    background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
}

/* 为第四个部分（帮助支持）添加紫色装饰 */
.footer-widget:nth-child(4)::after {
    background: linear-gradient(135deg, #9c27b0 0%, #8e24aa 100%);
}

.footer-widget::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.6s ease;
}

.footer-widget:hover::before {
    left: 100%;
}

.footer-widget:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

/* 页脚Logo */
.footer-logo {
    margin-bottom: 15px; /* 减少底部边距 */
    text-align: center;
}

.footer-logo img {
    height: 45px; /* 稍微减小Logo高度 */
    width: auto;
    filter: brightness(0) invert(1);
    transition: all 0.3s ease;
}

.footer-widget:hover .footer-logo img {
    transform: scale(1.05);
}

/* 页脚描述 */
.footer-desc {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5; /* 稍微减少行高 */
    margin-bottom: 20px; /* 减少底部边距 */
    font-size: 13px; /* 稍微减小字体 */
}

/* 页脚标题 */
.footer-title {
    color: white;
    font-size: 16px; /* 稍微减小字体 */
    font-weight: 600;
    margin-bottom: 25px; /* 增加底部边距 */
    position: relative;
    padding-bottom: 12px; /* 增加底部内边距 */
    text-align: center; /* 居中对齐 */
}

/* 为中间三个部分的标题添加特殊样式 */
.footer-widget:nth-child(2) .footer-title,
.footer-widget:nth-child(3) .footer-title,
.footer-widget:nth-child(4) .footer-title {
    font-size: 17px; /* 稍微增大字体 */
    font-weight: 700; /* 增加字重 */
    letter-spacing: 0.5px; /* 增加字母间距 */
    text-transform: uppercase; /* 大写字母 */
    margin-bottom: 28px; /* 增加底部边距 */
}

/* 为中间三个部分的标题添加图标 */
.footer-widget:nth-child(2) .footer-title::before {
    content: '🔧'; /* 解决方案图标 */
    margin-right: 8px;
    font-size: 16px;
}

.footer-widget:nth-child(3) .footer-title::before {
    content: '📦'; /* 产品服务图标 */
    margin-right: 8px;
    font-size: 16px;
}

.footer-widget:nth-child(4) .footer-title::before {
    content: '🛠️'; /* 帮助支持图标 */
    margin-right: 8px;
    font-size: 16px;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%; /* 居中对齐 */
    transform: translateX(-50%); /* 居中对齐 */
    width: 50px; /* 增加下划线宽度 */
    height: 2px;
    background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
    border-radius: 1px;
}

/* 为中间三个部分的下划线添加不同颜色 */
.footer-widget:nth-child(2) .footer-title::after {
    background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
}

.footer-widget:nth-child(3) .footer-title::after {
    background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
}

.footer-widget:nth-child(4) .footer-title::after {
    background: linear-gradient(135deg, #9c27b0 0%, #8e24aa 100%);
}

/* 页脚链接 */
.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 14px; /* 增加项目间距 */
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 14px; /* 稍微增大字体 */
    transition: all 0.3s ease;
    position: relative;
    padding-left: 16px; /* 增加左边距 */
    display: flex;
    align-items: center;
    line-height: 1.5; /* 增加行高 */
    word-break: break-word; /* 允许长文字换行 */
    min-height: 24px; /* 设置最小高度确保对齐 */
}

.footer-links a::before {
    content: '▸';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    color: #4fc3f7;
    font-size: 12px; /* 增大箭头大小 */
    transition: all 0.3s ease;
    line-height: 1;
    width: 12px; /* 固定箭头宽度 */
    text-align: center;
}

.footer-links a:hover {
    color: white;
    padding-left: 20px; /* 增加悬停时的移动距离 */
}

.footer-links a:hover::before {
    color: #29b6f6;
    transform: translateY(-50%) translateX(3px); /* 增加移动距离 */
}

/* 社交媒体链接 */
.footer-social {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.social-link:hover {
    background: #4fc3f7;
    color: white;
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 10px 25px rgba(79, 195, 247, 0.4);
    border-color: #4fc3f7;
}

/* 联系信息 */
.footer-contact {
    margin-top: 25px; /* 增加顶部边距 */
}

.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px; /* 减少项目间距 */
    padding: 15px 20px; /* 减少内边距 */
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px; /* 稍微增加圆角 */
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px; /* 稍微减小字体 */
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    position: relative;
    overflow: hidden;
    min-height: 50px; /* 减少最小高度 */
}

.contact-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.6s ease;
}

.contact-item:hover::before {
    left: 100%;
}

.contact-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-left-color: #4fc3f7;
    transform: translateX(8px); /* 增加悬停移动距离 */
    box-shadow: 0 8px 20px rgba(79, 195, 247, 0.25); /* 增强阴影效果 */
}

.contact-item i {
    width: 30px; /* 减小图标容器 */
    height: 30px;
    color: #4fc3f7;
    margin-right: 20px; /* 减少右边距 */
    font-size: 16px; /* 减小图标字体 */
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(79, 195, 247, 0.1);
    border-radius: 50%;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.contact-item:hover i {
    background: rgba(79, 195, 247, 0.2);
    color: #29b6f6;
    transform: scale(1.15); /* 增加悬停缩放效果 */
}

.contact-item span {
    transition: color 0.3s ease;
    font-weight: 500;
    line-height: 1.5; /* 增加行高 */
    flex: 1; /* 让文字占据剩余空间 */
}

.contact-item:hover span {
    color: white;
}

/* 特殊样式 - 地址?*/
.contact-item:first-child {
    border-left-color: #ff6b35;
}

.contact-item:first-child i {
    background: rgba(255, 107, 53, 0.1);
    color: #ff6b35;
}

.contact-item:first-child:hover {
    border-left-color: #ff6b35;
    box-shadow: 0 5px 15px rgba(255, 107, 53, 0.2);
}

.contact-item:first-child:hover i {
    background: rgba(255, 107, 53, 0.2);
    color: #e55a2b;
}

/* 电话项特殊样?*/
.contact-item:nth-child(2) {
    border-left-color: #4caf50;
}

.contact-item:nth-child(2) i {
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.contact-item:nth-child(2):hover {
    border-left-color: #4caf50;
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.2);
}

.contact-item:nth-child(2):hover i {
    background: rgba(76, 175, 80, 0.2);
    color: #45a049;
}

/* 邮箱项特殊样?*/
.contact-item:nth-child(3) {
    border-left-color: #9c27b0;
}

.contact-item:nth-child(3) i {
    background: rgba(156, 39, 176, 0.1);
    color: #9c27b0;
}

.contact-item:nth-child(3):hover {
    border-left-color: #9c27b0;
    box-shadow: 0 5px 15px rgba(156, 39, 176, 0.2);
}

.contact-item:nth-child(3):hover i {
    background: rgba(156, 39, 176, 0.2);
    color: #8e24aa;
}

/* QQ项特殊样?*/
.contact-item:nth-child(4) {
    border-left-color: #ff9800;
}

.contact-item:nth-child(4) i {
    background: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

.contact-item:nth-child(4):hover {
    border-left-color: #ff9800;
    box-shadow: 0 5px 15px rgba(255, 152, 0, 0.2);
}

.contact-item:nth-child(4):hover i {
    background: rgba(255, 152, 0, 0.2);
    color: #f57c00;
}

/* 返回顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    box-shadow: 0 8px 25px rgba(79, 195, 247, 0.3);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    transform: translateY(-5px) scale(1.1);
    box-shadow: 0 15px 35px rgba(79, 195, 247, 0.5);
    background: linear-gradient(135deg, #29b6f6 0%, #1976d2 100%);
}

.back-to-top i {
    font-size: 20px;
    transition: transform 0.3s ease;
}

.back-to-top:hover i {
    transform: translateY(-2px);
}

/* 在线客服 */
.online-service {
    position: fixed;
    bottom: 100px;
    right: 30px;
    z-index: 1000;
}

.service-btn {
    background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
    border: none;
    font-size: 14px;
    font-weight: 500;
}

.service-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 15px 35px rgba(76, 175, 80, 0.5);
    background: linear-gradient(135deg, #45a049 0%, #388e3c 100%);
}

.service-btn i {
    font-size: 16px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.service-panel {
    position: absolute;
    bottom: 60px;
    right: 0;
    width: 300px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.service-panel.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.service-header {
    background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
    color: white;
    padding: 15px 20px;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.service-header h6 {
    margin: 0;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.service-content {
    padding: 20px;
}

.service-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.service-item:hover {
    background: rgba(79, 195, 247, 0.05);
    border-color: rgba(79, 195, 247, 0.2);
    transform: translateX(5px);
}

.service-item:last-child {
    margin-bottom: 0;
}

.service-item img {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    object-fit: cover;
}

.service-info {
    flex: 1;
}

.service-info h6 {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.service-info p {
    margin: 0;
    font-size: 12px;
    color: #666;
}

.service-item .btn {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 15px;
    transition: all 0.3s ease;
}

/* 响应式设?*/
@media (max-width: 768px) {
    .footer-main {
        padding: 40px 0 20px; /* 减少移动端内边距 */
    }
    
    .footer-widget {
        margin-bottom: 25px; /* 减少间距 */
        padding: 16px; /* 减少移动端内边距 */
    }
    
    /* 移动端联系信息优化 */
    .contact-item {
        padding: 12px 16px; /* 减少移动端内边距 */
        margin-bottom: 12px; /* 减少项目间距 */
        min-height: 45px; /* 减少移动端最小高度 */
        border-radius: 8px; /* 减少圆角 */
    }
    
    .contact-item i {
        width: 28px; /* 减小移动端图标 */
        height: 28px;
        margin-right: 16px; /* 减少右边距 */
        font-size: 14px; /* 减小图标字体 */
    }
    
    .contact-item span {
        font-size: 16px; /* 移动端增大字体 */
        line-height: 1.6; /* 增加行高 */
        font-weight: 600; /* 移动端增加字重 */
    }
    
    .footer-social {
        justify-content: center;
    }

    .footer-links-bottom {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }

    .online-service {
        bottom: 80px;
        right: 20px;
    }

    .service-panel {
        width: 280px;
    }
    
    .copyright {
        text-align: center;
        margin-bottom: 15px;
    }
}

/* 页脚底部 */
.footer-bottom {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px 0; /* 减少内边距 */
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    z-index: 2;
}

.copyright {
    text-align: center;
}

.copyright p {
    margin: 0;
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
}

.footer-links-bottom {
    display: flex;
    justify-content: center;
    gap: 25px;
}

.footer-links-bottom a {
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.footer-links-bottom a:hover {
    color: #4fc3f7;
}

/* 为中间三个部分的链接添加不同的悬停颜色 */
.footer-widget:nth-child(2) .footer-links a:hover {
    color: #ff6b35 !important; /* 橙色 */
}

.footer-widget:nth-child(2) .footer-links a:hover::before {
    color: #e55a2b !important;
}

.footer-widget:nth-child(3) .footer-links a:hover {
    color: #4caf50 !important; /* 绿色 */
}

.footer-widget:nth-child(3) .footer-links a:hover::before {
    color: #45a049 !important;
}

.footer-widget:nth-child(4) .footer-links a:hover {
    color: #9c27b0 !important; /* 紫色 */
}

.footer-widget:nth-child(4) .footer-links a:hover::before {
    color: #8e24aa !important;
}

/* 为中间三个部分添加悬停时的背景光效 */
.footer-widget:nth-child(2):hover,
.footer-widget:nth-child(3):hover,
.footer-widget:nth-child(4):hover {
    background: rgba(255, 255, 255, 0.12); /* 增强悬停背景 */
    border-color: rgba(255, 255, 255, 0.25); /* 增强悬停边框 */
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4); /* 增强阴影 */
}

/* 为中间三个部分添加特殊的悬停光效 */
.footer-widget:nth-child(2):hover {
    box-shadow: 0 20px 40px rgba(255, 107, 53, 0.2), 0 0 30px rgba(255, 107, 53, 0.1);
}

.footer-widget:nth-child(3):hover {
    box-shadow: 0 20px 40px rgba(76, 175, 80, 0.2), 0 0 30px rgba(76, 175, 80, 0.1);
}

.footer-widget:nth-child(4):hover {
    box-shadow: 0 20px 40px rgba(156, 39, 176, 0.2), 0 0 30px rgba(156, 39, 176, 0.1);
}

/* 页脚微信二维码样式 */
.footer-qrcode {
    text-align: center;
    margin: 25px 0;
    padding: 20px;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    border: none; /* 去掉外层边框 */
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.footer-qrcode::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.6s ease;
}

.footer-qrcode:hover::before {
    left: 100%;
}

.footer-qrcode:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(79, 195, 247, 0.2);
}

.qrcode-img {
    width: 120px;
    height: 120px;
    border-radius: 8px;
    border: 3px solid rgba(255, 255, 255, 0.3); /* 增强二维码图片边框 */
    transition: all 0.3s ease;
    background: white;
    padding: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.footer-qrcode:hover .qrcode-img {
    transform: scale(1.05);
    border-color: #4fc3f7; /* 悬停时变为蓝色 */
    box-shadow: 0 8px 25px rgba(79, 195, 247, 0.3);
}

.qrcode-text {
    color: rgba(255, 255, 255, 0.9);
    font-size: 13px;
    font-weight: 500;
    margin: 12px 0 0 0;
    transition: color 0.3s ease;
}

.footer-qrcode:hover .qrcode-text {
    color: #4fc3f7;
}

/* ===== 新闻动态区域样式 ===== */
.news-section {
    background: #f8f9fa;
    position: relative;
    overflow: hidden;
    min-height: 700px;
    background-attachment: scroll !important;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
}

.news-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.65);
    z-index: 1;
}

/* 新闻区域背景图片样式 */
.news-section {
    background-attachment: scroll !important;
    background-position: center top !important;
    background-repeat: no-repeat !important;
    background-size: contain !important;
    min-height: 600px !important;
    position: relative !important;
}

/* 响应式背景图片优化 */
@media (max-width: 768px) {
    .news-section {
        background-position: center top !important;
        background-size: contain !important;
        min-height: 500px !important;
    }
}

@media (min-width: 1200px) {
    .news-section {
        background-position: center top !important;
        background-size: contain !important;
        min-height: 700px !important;
    }
}





/* 调整新闻动态区域的布局，使其在指定位置显示 */
.news-section .container {
    max-width: 1200px;
    margin: 0 auto;
    padding-top: 0px;
    padding-bottom: 80px;
}

.news-section .section-header {
    text-align: center;
    margin-bottom: 3rem;
    margin-top: 200px;
    position: relative;
    z-index: 2;
}

.news-section .section-title {
    color: #2c3e50;
    font-weight: 700;
    font-size: 3rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.8);
}

.news-section .section-subtitle {
    color: #6c757d;
    font-size: 1.2rem;
    font-weight: 400;
    margin-bottom: 0;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

.section-header {
    position: relative;
    margin-bottom: 3rem;
}

.section-icon {
    margin-bottom: 1.5rem;
}

.section-icon-img {
    height: 60px;
    width: auto;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
    position: relative;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.section-divider {
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    margin: 0 auto;
    border-radius: 2px;
    position: relative;
}

.section-divider::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 12px;
    height: 8px;
    background: #007bff;
    border-radius: 50%;
}

/* 新闻卡片样式 */
.news-card {
    background: rgba(255, 255, 255, 0.97);
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.2);
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    height: 100%;
    position: relative;
    border: 3px solid rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    z-index: 2;
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 380px;
    margin: 0 auto;
}

.news-card:hover {
    transform: translateY(-15px) scale(1.03);
    box-shadow: 0 30px 80px rgba(0, 0, 0, 0.35);
    border: 3px solid rgba(0, 123, 255, 0.6);
    background: rgba(255, 255, 255, 0.99);
}

.news-image {
    position: relative;
    overflow: hidden;
    height: 240px;
    border-radius: 15px 15px 0 0;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.4s ease;
    filter: brightness(1.05) contrast(1.1) saturate(1.05);
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    image-rendering: optimizeQuality;
}

.news-card:hover .news-image img {
    transform: scale(1.08);
    filter: brightness(1.1) contrast(1.15) saturate(1.1);
}

.news-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0, 123, 255, 0.8) 0%, rgba(0, 86, 179, 0.8) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: flex-end;
    padding: 20px;
}

.news-card:hover .news-overlay {
    opacity: 1;
}

.news-date {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    padding: 10px 15px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    position: absolute;
    top: 15px;
    right: 15px;
}

.news-date .day {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #007bff;
    line-height: 1;
}

.news-date .month {
    display: block;
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
    margin-top: 2px;
}

.news-content {
    padding: 30px;
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.news-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 18px;
    flex-wrap: wrap;
    gap: 12px;
}

.news-category,
.news-time {
    font-size: 0.85rem;
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 6px;
}

.news-category {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1976d2;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.15);
    transition: all 0.3s ease;
}

.news-category:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.25);
}

.news-title {
    margin-bottom: 18px;
}

.news-title a {
    color: #2c3e50;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.2rem;
    line-height: 1.4;
    transition: all 0.3s ease;
    display: block;
}

.news-title a:hover {
    color: #007bff;
    transform: translateX(3px);
}

.news-excerpt {
    color: #6c757d;
    font-size: 1rem;
    line-height: 1.7;
    margin-bottom: 25px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-align: justify;
    flex: 1;
}

.news-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 2px solid #f1f3f4;
    margin-top: auto;
}

.read-more-btn {
    color: #007bff;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border-radius: 25px;
    background: rgba(0, 123, 255, 0.1);
    border: 1px solid rgba(0, 123, 255, 0.2);
}

.read-more-btn:hover {
    color: white;
    background: #007bff;
    border-color: #007bff;
    text-decoration: none;
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.news-stats {
    color: #6c757d;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* 查看更多动态按钮样式 */
.news-more-btn {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    border: none !important;
    color: white !important;
    padding: 15px 40px !important;
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    border-radius: 50px !important;
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3) !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 10px !important;
    z-index: 1 !important;
}

.news-more-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s ease;
    z-index: -1;
}

.news-more-btn:hover::before {
    left: 100%;
}

.news-more-btn:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%) !important;
    transform: translateY(-3px) scale(1.05) !important;
    box-shadow: 0 15px 40px rgba(0, 123, 255, 0.4) !important;
    color: white !important;
    text-decoration: none !important;
}

.news-more-btn i,
.news-more-btn * {
    z-index: 10 !important;
    position: relative !important;
    color: white !important;
}

.news-more-btn:hover i,
.news-more-btn:hover * {
    color: white !important;
}

/* ===== 数据统计区域样式 - 重新设计 ===== */
.stats-section {
    position: relative;
    padding: 100px 0;
    overflow: hidden;
    background: #1a1a2e;
}

.stats-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.stats-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.3;
}

.stats-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(26, 26, 46, 0.9) 0%, rgba(16, 213, 194, 0.1) 100%);
    z-index: 2;
}

.stats-section .container {
    position: relative;
    z-index: 3;
}

.stats-header {
    text-align: center;
    margin-bottom: 4rem;
}

.stats-title {
    font-size: 3rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.stats-subtitle {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
}

.stats-divider {
    width: 100px;
    height: 4px;
    background: linear-gradient(135deg, #10d5c2 0%, #007bff 100%);
    margin: 0 auto;
    border-radius: 2px;
    position: relative;
}

.stats-divider::before {
    content: '';
    position: absolute;
    top: -3px;
    left: 50%;
    transform: translateX(-50%);
    width: 10px;
    height: 10px;
    background: #10d5c2;
    border-radius: 50%;
    box-shadow: 0 0 20px rgba(16, 213, 194, 0.5);
}

/* 统计项目样式 */
.stat-item {
    text-align: center;
    padding: 40px 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    height: 100%;
}

.stat-item:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(16, 213, 194, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.stat-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #10d5c2 0%, #007bff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    position: relative;
    box-shadow: 0 10px 30px rgba(16, 213, 194, 0.3);
}

.stat-icon::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #10d5c2 0%, #007bff 100%);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-item:hover .stat-icon::before {
    opacity: 1;
    animation: pulse 2s infinite;
}

.stat-icon i {
    font-size: 2rem;
    color: white;
}

.stat-icon-img {
    width: 40px;
    height: 40px;
    object-fit: contain;
    filter: brightness(0) invert(1);
    transition: all 0.3s ease;
}

.stat-item:hover .stat-icon-img {
    filter: brightness(0) invert(1);
    transform: scale(1.1);
}

.stat-content {
    position: relative;
}

.stat-content h3 {
    font-size: 3.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0;
    line-height: 1;
    display: inline-block;
}

.stat-plus {
    font-size: 2rem;
    color: #10d5c2;
    font-weight: 700;
    margin-left: 5px;
}

.stat-label {
    font-size: 1.2rem;
    font-weight: 600;
    color: white;
    margin: 15px 0 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-description {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 10px;
}

.stat-decoration {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, rgba(16, 213, 194, 0.2) 0%, rgba(0, 123, 255, 0.2) 100%);
    border-radius: 50%;
    opacity: 0;
    transition: all 0.3s ease;
}

.stat-item:hover .stat-decoration {
    opacity: 1;
    transform: scale(1.2);
}

/* 信任标识样式 */
.trust-indicators {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 40px;
    padding: 40px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.trust-item {
    display: flex;
    align-items: center;
    gap: 10px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.trust-item:hover {
    color: #10d5c2;
    transform: translateY(-2px);
}

.trust-item i {
    font-size: 1.5rem;
    color: #10d5c2;
}

/* 动画效果 */
@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 数字计数动画 */
.counter {
    transition: all 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-title {
        font-size: 2.2rem;
    }

    .stat-content h3 {
        font-size: 2.5rem;
    }

    .stat-plus {
        font-size: 1.5rem;
    }

    .trust-indicators {
        gap: 20px;
    }

    .trust-item {
        font-size: 0.9rem;
    }

    .news-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .section-title {
        font-size: 2rem;
    }

    .stats-section {
        padding: 60px 0;
    }

    .stats-header {
        margin-bottom: 3rem;
    }

    .stat-item {
        padding: 30px 15px;
        margin-bottom: 20px;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 20px;
    }

    .stat-icon i {
        font-size: 1.5rem;
    }

    .stat-icon-img {
        width: 30px;
        height: 30px;
    }

    .news-image {
        height: 200px;
    }

    .news-content {
        padding: 25px;
    }

    .news-section .section-title {
        font-size: 2.5rem;
    }

    .news-section .container {
        padding-top: 60px;
        padding-bottom: 60px;
    }
}
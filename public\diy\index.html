<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DIY页面构建器</title>
    <link rel="stylesheet" href="css/all.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="builder-container">
        <!-- 顶部工具栏 -->
        <div class="toolbar">
            <div class="toolbar-left">
                <div class="logo"><i data-lucide="blocks"></i> 页面构建器</div>
            </div>
            <div class="toolbar-right">
                <button class="toolbar-btn secondary" onclick="previewPage()">预览</button>
                <button class="toolbar-btn secondary" onclick="viewHtmlCode()">查看代码</button>
                <button class="toolbar-btn" onclick="savePage()">保存</button>
            </div>
        </div>

        <!-- 组件库 -->
        <div class="component-library">
            <!-- 网站模板分类 -->
            <div class="component-category">
                <div class="library-title">网站模板</div>
                <div class="components-grid">
                    <div class="component-item" onclick="showTemplateSelector()">
                        <span class="component-icon"><i data-lucide="layout-template"></i></span>
                        <span class="component-name">选择模板</span>
                    </div>
                </div>
            </div>

            <!-- 基础组件分类 -->
            <div class="component-category">
                <div class="library-title">基础组件</div>
                <div class="components-grid">
                    <div class="component-item" onclick="addComponent('background')">
                        <span class="component-icon"><i data-lucide="image"></i></span>
                        <span class="component-name">背景图</span>
                    </div>
                    <div class="component-item" onclick="addComponent('navbar')">
                        <span class="component-icon"><i data-lucide="navigation"></i></span>
                        <span class="component-name">导航栏</span>
                    </div>
                    <div class="component-item" onclick="addComponent('hero')">
                        <span class="component-icon"><i data-lucide="target"></i></span>
                        <span class="component-name">英雄区</span>
                    </div>
                    <div class="component-item" onclick="addComponent('section')">
                        <span class="component-icon"><i data-lucide="layout"></i></span>
                        <span class="component-name">区块</span>
                    </div>
                    <div class="component-item" onclick="addComponent('card')">
                        <span class="component-icon"><i data-lucide="credit-card"></i></span>
                        <span class="component-name">卡片</span>
                    </div>
                    <div class="component-item" onclick="addComponent('footer')">
                        <span class="component-icon"><i data-lucide="align-horizontal-distribute-end"></i></span>
                        <span class="component-name">页脚</span>
                    </div>
                </div>
            </div>

            <!-- 内容组件分类 -->
            <div class="component-category">
                <div class="library-title">内容组件</div>
                <div class="components-grid">
                    <div class="component-item" onclick="addComponent('textblock')">
                        <span class="component-icon"><i data-lucide="file-text"></i></span>
                        <span class="component-name">文本块</span>
                    </div>
                    <div class="component-item" onclick="addComponent('stats')">
                        <span class="component-icon"><i data-lucide="bar-chart-3"></i></span>
                        <span class="component-name">统计数字</span>
                    </div>
                    <div class="component-item" onclick="addComponent('team')">
                        <span class="component-icon"><i data-lucide="users"></i></span>
                        <span class="component-name">团队介绍</span>
                    </div>
                    <div class="component-item" onclick="addComponent('testimonials')">
                        <span class="component-icon"><i data-lucide="message-circle"></i></span>
                        <span class="component-name">客户评价</span>
                    </div>
                    <div class="component-item" onclick="addComponent('contact')">
                        <span class="component-icon"><i data-lucide="phone"></i></span>
                        <span class="component-name">联系信息</span>
                    </div>
                    <div class="component-item" onclick="addComponent('styletemplates')">
                        <span class="component-icon"><i data-lucide="palette"></i></span>
                        <span class="component-name">样式模板</span>
                    </div>
                </div>
            </div>

            <!-- 功能组件分类 -->
            <div class="component-category">
                <div class="library-title">功能组件</div>
                <div class="components-grid">
                    <div class="component-item" onclick="addComponent('news')">
                        <span class="component-icon"><i data-lucide="newspaper"></i></span>
                        <span class="component-name">新闻</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 编辑区 -->
        <div class="editor-area">
            <div class="canvas" id="canvas">
                <div style="text-align: center; padding: 100px 20px; color: #a0aec0;">
                    <h2>从左侧组件库选择组件开始构建页面</h2>
                    <p>点击组件添加到编辑区，支持拖拽排序</p>
                </div>
            </div>
        </div>

        <!-- 属性区 -->
        <div class="properties-panel" id="properties-panel">
            <div class="panel-title">属性设置</div>
            <div class="properties-content" id="properties-content">
                <p style="color: #a0aec0; text-align: center; margin-top: 50px;">请选择一个组件</p>
            </div>
        </div>
    </div>

    <!-- 组件文件 -->
    <script src="js/components/background.js"></script>
    <script src="js/components/navbar.js"></script>
    <script src="js/components/hero.js"></script>
    <script src="js/components/section.js"></script>
    <script src="js/components/card.js"></script>
    <script src="js/components/footer.js"></script>
    <script src="js/components/textblock.js"></script>
    <script src="js/components/stats.js"></script>
    <script src="js/components/team.js"></script>
    <script src="js/components/testimonials.js"></script>
    <script src="js/components/contact.js"></script>
    <script src="js/components/manager.js"></script>

    <!-- 模板系统文件 -->
    <script src="js/templates/template-system.js"></script>
    <script src="js/templates/template-selector.js"></script>

    <!-- Lucide图标库 -->
    <script src="../assets/js/lucide.js"></script>

    <!-- 主文件 -->
    <script src="js/all.js"></script>
</body>
</html>

<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * 文件安全服务 - ThinkPHP6企业级应用
 * 功能：统一文件上传安全验证、恶意文件检测、深度类型验证
 */

namespace app\service;

use think\facade\Config;
use think\exception\ValidateException;

class FileSecurityService
{
    /**
     * 文件魔数映射表
     */
    private static $magicNumbers = [
        'jpg'  => ['FFD8FF'],
        'jpeg' => ['FFD8FF'],
        'png'  => ['89504E47'],
        'gif'  => ['474946383761', '474946383961'],
        'webp' => ['52494646'],
        'pdf'  => ['255044462D'],
        'doc'  => ['D0CF11E0A1B11AE1'],
        'docx' => ['504B0304'],
        'xls'  => ['D0CF11E0A1B11AE1'],
        'xlsx' => ['504B0304'],
        'zip'  => ['504B0304', '504B0506', '504B0708'],
        'rar'  => ['526172211A0700'],
        'mp4'  => ['00000018667479706D703432', '00000020667479706D703432'],
        'avi'  => ['41564920'],
        'mp3'  => ['494433', 'FFFB'],
    ];

    /**
     * 危险文件扩展名
     */
    private static $dangerousExtensions = [
        'php', 'php3', 'php4', 'php5', 'phtml', 'pht', 'phps',
        'asp', 'aspx', 'asa', 'asax', 'ascx', 'ashx', 'asmx',
        'jsp', 'jspx', 'jsw', 'jsv', 'jspf',
        'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'vbe',
        'js', 'jar', 'sh', 'py', 'pl', 'rb', 'go',
        'htaccess', 'htpasswd', 'ini', 'conf', 'config',
        'sql', 'db', 'sqlite', 'mdb',
    ];

    /**
     * 恶意文件内容特征
     */
    private static $maliciousPatterns = [
        // PHP恶意代码
        '/<\?php/i',
        '/<\?=/i',
        '/eval\s*\(/i',
        '/exec\s*\(/i',
        '/system\s*\(/i',
        '/shell_exec\s*\(/i',
        '/passthru\s*\(/i',
        '/file_get_contents\s*\(/i',
        '/file_put_contents\s*\(/i',
        '/fopen\s*\(/i',
        '/fwrite\s*\(/i',
        '/include\s*\(/i',
        '/require\s*\(/i',
        
        // JavaScript恶意代码
        '/document\.cookie/i',
        '/window\.location/i',
        '/eval\s*\(/i',
        '/setTimeout\s*\(/i',
        '/setInterval\s*\(/i',
        
        // SQL注入特征
        '/union\s+select/i',
        '/drop\s+table/i',
        '/delete\s+from/i',
        '/insert\s+into/i',
        '/update\s+set/i',
        
        // 其他恶意特征
        '/base64_decode\s*\(/i',
        '/gzinflate\s*\(/i',
        '/str_rot13\s*\(/i',
        '/chr\s*\(/i',
        '/ord\s*\(/i',
    ];

    /**
     * 统一文件安全验证
     * 
     * @param \think\file\UploadedFile $file 上传文件
     * @param array $options 验证选项
     * @return array 验证结果
     */
    public static function validateFile($file, $options = [])
    {
        try {
            $config = Config::get('security.upload', []);
            $options = array_merge([
                'check_extension' => true,
                'check_mime' => true,
                'check_size' => true,
                'check_magic' => true,
                'check_content' => true,
                'check_image' => true,
                'strict_mode' => true,
            ], $options);

            $errors = [];

            // 1. 基础文件检查
            if (!$file || !$file->isValid()) {
                $errors[] = '文件无效或上传失败';
                return ['valid' => false, 'errors' => $errors];
            }

            $originalName = $file->getOriginalName();
            $extension = strtolower($file->extension());
            $mimeType = $file->getMime();
            $fileSize = $file->getSize();
            $tempPath = $file->getPathname();

            // 2. 文件扩展名检查
            if ($options['check_extension']) {
                $extensionErrors = self::validateExtension($extension, $config);
                $errors = array_merge($errors, $extensionErrors);
            }

            // 3. MIME类型检查
            if ($options['check_mime']) {
                $mimeErrors = self::validateMimeType($mimeType, $config);
                $errors = array_merge($errors, $mimeErrors);
            }

            // 4. 文件大小检查
            if ($options['check_size']) {
                $sizeErrors = self::validateFileSize($fileSize, $config);
                $errors = array_merge($errors, $sizeErrors);
            }

            // 5. 文件魔数检查
            if ($options['check_magic']) {
                $magicErrors = self::validateMagicNumber($tempPath, $extension);
                $errors = array_merge($errors, $magicErrors);
            }

            // 6. 文件内容安全检查
            if ($options['check_content']) {
                $contentErrors = self::validateFileContent($tempPath, $options['strict_mode']);
                $errors = array_merge($errors, $contentErrors);
            }

            // 7. 图片特殊检查
            if ($options['check_image'] && self::isImageFile($extension)) {
                $imageErrors = self::validateImageFile($tempPath);
                $errors = array_merge($errors, $imageErrors);
            }

            return [
                'valid' => empty($errors),
                'errors' => $errors,
                'file_info' => [
                    'original_name' => $originalName,
                    'extension' => $extension,
                    'mime_type' => $mimeType,
                    'file_size' => $fileSize,
                    'temp_path' => $tempPath,
                ]
            ];

        } catch (\Exception $e) {
            return [
                'valid' => false,
                'errors' => ['文件验证过程中发生错误: ' . $e->getMessage()],
                'file_info' => null
            ];
        }
    }

    /**
     * 验证文件扩展名
     */
    private static function validateExtension($extension, $config)
    {
        $errors = [];
        
        // 检查是否为危险扩展名
        if (in_array($extension, self::$dangerousExtensions)) {
            $errors[] = "危险的文件类型: {$extension}";
        }
        
        // 检查是否在允许列表中
        $allowedExtensions = $config['allowed_extensions'] ?? [];
        if (!empty($allowedExtensions) && !in_array($extension, $allowedExtensions)) {
            $errors[] = "不允许的文件扩展名: {$extension}";
        }
        
        // 检查是否在禁止列表中
        $forbiddenExtensions = $config['forbidden_extensions'] ?? [];
        if (in_array($extension, $forbiddenExtensions)) {
            $errors[] = "禁止的文件扩展名: {$extension}";
        }
        
        return $errors;
    }

    /**
     * 验证MIME类型
     */
    private static function validateMimeType($mimeType, $config)
    {
        $errors = [];
        
        $allowedMimes = $config['allowed_mimes'] ?? [];
        if (!empty($allowedMimes) && !in_array($mimeType, $allowedMimes)) {
            $errors[] = "不允许的MIME类型: {$mimeType}";
        }
        
        return $errors;
    }

    /**
     * 验证文件大小
     */
    private static function validateFileSize($fileSize, $config)
    {
        $errors = [];
        
        $maxSize = $config['max_size'] ?? (5 * 1024 * 1024); // 默认5MB
        if ($fileSize > $maxSize) {
            $maxSizeText = self::formatFileSize($maxSize);
            $fileSizeText = self::formatFileSize($fileSize);
            $errors[] = "文件大小超出限制: {$fileSizeText}，最大允许: {$maxSizeText}";
        }
        
        return $errors;
    }

    /**
     * 验证文件魔数
     */
    private static function validateMagicNumber($filePath, $extension)
    {
        $errors = [];
        
        if (!isset(self::$magicNumbers[$extension])) {
            return $errors; // 不在检查列表中的文件类型跳过魔数检查
        }
        
        $handle = fopen($filePath, 'rb');
        if (!$handle) {
            $errors[] = '无法读取文件进行魔数验证';
            return $errors;
        }
        
        $header = fread($handle, 32); // 读取前32字节
        fclose($handle);
        
        $headerHex = strtoupper(bin2hex($header));
        $expectedMagics = self::$magicNumbers[$extension];
        
        $isValid = false;
        foreach ($expectedMagics as $magic) {
            if (strpos($headerHex, $magic) === 0) {
                $isValid = true;
                break;
            }
        }
        
        if (!$isValid) {
            $errors[] = "文件头部验证失败，文件可能被伪造或损坏";
        }
        
        return $errors;
    }

    /**
     * 验证文件内容安全性
     */
    private static function validateFileContent($filePath, $strictMode = true)
    {
        $errors = [];
        
        // 读取文件内容（限制大小避免内存问题）
        $maxReadSize = 1024 * 1024; // 1MB
        $content = file_get_contents($filePath, false, null, 0, $maxReadSize);
        
        if ($content === false) {
            $errors[] = '无法读取文件内容进行安全检查';
            return $errors;
        }
        
        // 检查恶意模式
        foreach (self::$maliciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $errors[] = '文件包含可疑的恶意代码';
                if ($strictMode) {
                    break; // 严格模式下发现一个就停止
                }
            }
        }
        
        return $errors;
    }

    /**
     * 验证图片文件
     */
    private static function validateImageFile($filePath)
    {
        $errors = [];
        
        // 使用getimagesize验证图片
        $imageInfo = @getimagesize($filePath);
        if ($imageInfo === false) {
            $errors[] = '无效的图片文件';
            return $errors;
        }
        
        // 检查图片尺寸
        $width = $imageInfo[0];
        $height = $imageInfo[1];
        
        if ($width <= 0 || $height <= 0) {
            $errors[] = '图片尺寸无效';
        }
        
        // 检查图片类型
        $allowedImageTypes = [IMAGETYPE_JPEG, IMAGETYPE_PNG, IMAGETYPE_GIF, IMAGETYPE_WEBP];
        if (!in_array($imageInfo[2], $allowedImageTypes)) {
            $errors[] = '不支持的图片格式';
        }
        
        return $errors;
    }

    /**
     * 判断是否为图片文件
     */
    private static function isImageFile($extension)
    {
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        return in_array($extension, $imageExtensions);
    }

    /**
     * 格式化文件大小
     */
    private static function formatFileSize($bytes)
    {
        if ($bytes === 0) return '0 B';

        $k = 1024;
        $sizes = ['B', 'KB', 'MB', 'GB'];
        $i = floor(log($bytes) / log($k));

        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }

    /**
     * 安全的文件上传处理
     *
     * @param \think\file\UploadedFile $file 上传文件
     * @param array $options 上传选项
     * @return array 处理结果
     */
    public static function secureUpload($file, $options = [])
    {
        // 1. 文件安全验证
        $validation = self::validateFile($file, $options);
        if (!$validation['valid']) {
            return [
                'success' => false,
                'message' => '文件验证失败: ' . implode(', ', $validation['errors']),
                'data' => null
            ];
        }

        try {
            $fileInfo = $validation['file_info'];
            $extension = $fileInfo['extension'];

            // 2. 生成安全的文件名
            $safeName = self::generateSafeFileName($fileInfo['original_name'], $extension);

            // 3. 确定存储路径
            $storagePath = self::getSecureStoragePath($extension, $options);

            // 4. 创建目录
            $fullPath = public_path() . '/' . $storagePath;
            if (!is_dir($fullPath)) {
                mkdir($fullPath, 0755, true);
            }

            // 5. 移动文件
            $filePath = $storagePath . '/' . $safeName;
            $fullFilePath = public_path() . '/' . $filePath;

            if (!$file->move($fullPath, $safeName)) {
                throw new \Exception('文件保存失败');
            }

            // 6. 设置安全的文件权限
            chmod($fullFilePath, 0644);

            // 7. 生成文件信息
            $result = [
                'success' => true,
                'message' => '文件上传成功',
                'data' => [
                    'original_name' => $fileInfo['original_name'],
                    'safe_name' => $safeName,
                    'file_path' => $filePath,
                    'file_url' => '/' . $filePath,
                    'full_url' => request()->domain() . '/' . $filePath,
                    'file_size' => $fileInfo['file_size'],
                    'file_size_text' => self::formatFileSize($fileInfo['file_size']),
                    'mime_type' => $fileInfo['mime_type'],
                    'extension' => $extension,
                    'upload_time' => date('Y-m-d H:i:s'),
                    'security_check' => 'passed'
                ]
            ];

            return $result;

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '文件上传失败: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 生成安全的文件名
     */
    private static function generateSafeFileName($originalName, $extension)
    {
        // 移除路径信息
        $originalName = basename($originalName);

        // 移除扩展名
        $nameWithoutExt = pathinfo($originalName, PATHINFO_FILENAME);

        // 清理文件名，只保留安全字符
        $safeName = preg_replace('/[^a-zA-Z0-9\-_\.]/', '_', $nameWithoutExt);
        $safeName = preg_replace('/_{2,}/', '_', $safeName); // 合并多个下划线
        $safeName = trim($safeName, '_');

        // 如果清理后为空，使用时间戳
        if (empty($safeName)) {
            $safeName = 'file_' . time();
        }

        // 限制长度
        if (strlen($safeName) > 50) {
            $safeName = substr($safeName, 0, 50);
        }

        // 添加随机后缀避免重名
        $randomSuffix = '_' . substr(md5(uniqid(mt_rand(), true)), 0, 8);

        return $safeName . $randomSuffix . '.' . $extension;
    }

    /**
     * 获取安全的存储路径
     */
    private static function getSecureStoragePath($extension, $options = [])
    {
        $baseDir = $options['base_dir'] ?? 'uploads';
        $subDir = $options['sub_dir'] ?? 'files';

        // 根据文件类型分类存储
        if (self::isImageFile($extension)) {
            $typeDir = 'images';
        } elseif (in_array($extension, ['pdf', 'doc', 'docx', 'xls', 'xlsx'])) {
            $typeDir = 'documents';
        } elseif (in_array($extension, ['mp4', 'avi', 'mov', 'wmv'])) {
            $typeDir = 'videos';
        } elseif (in_array($extension, ['mp3', 'wav', 'flac', 'aac'])) {
            $typeDir = 'audios';
        } else {
            $typeDir = 'others';
        }

        // 按日期分目录
        $dateDir = date('Y/m/d');

        return $baseDir . '/' . $typeDir . '/' . $dateDir;
    }

    /**
     * 批量文件安全验证
     */
    public static function validateMultipleFiles($files, $options = [])
    {
        $results = [];
        $allValid = true;

        foreach ($files as $index => $file) {
            $result = self::validateFile($file, $options);
            $results[$index] = $result;

            if (!$result['valid']) {
                $allValid = false;
            }
        }

        return [
            'all_valid' => $allValid,
            'results' => $results,
            'total_files' => count($files),
            'valid_files' => count(array_filter($results, function($r) { return $r['valid']; })),
            'invalid_files' => count(array_filter($results, function($r) { return !$r['valid']; }))
        ];
    }

    /**
     * 清理恶意文件
     */
    public static function cleanMaliciousFile($filePath)
    {
        if (!file_exists($filePath)) {
            return ['success' => false, 'message' => '文件不存在'];
        }

        try {
            // 删除文件
            unlink($filePath);

            // 记录安全日志
            self::logSecurityEvent('malicious_file_removed', [
                'file_path' => $filePath,
                'ip' => request()->ip(),
                'user_agent' => request()->header('User-Agent'),
                'timestamp' => date('Y-m-d H:i:s')
            ]);

            return ['success' => true, 'message' => '恶意文件已清理'];

        } catch (\Exception $e) {
            return ['success' => false, 'message' => '清理失败: ' . $e->getMessage()];
        }
    }

    /**
     * 记录安全事件
     */
    private static function logSecurityEvent($event, $data)
    {
        $logFile = runtime_path() . 'log/security_' . date('Y-m-d') . '.log';
        $logData = [
            'event' => $event,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        file_put_contents($logFile, json_encode($logData) . "\n", FILE_APPEND | LOCK_EX);
    }

    /**
     * 获取文件安全报告
     */
    public static function getSecurityReport($filePath)
    {
        if (!file_exists($filePath)) {
            return ['error' => '文件不存在'];
        }

        $fileInfo = [
            'path' => $filePath,
            'size' => filesize($filePath),
            'modified' => date('Y-m-d H:i:s', filemtime($filePath)),
            'permissions' => substr(sprintf('%o', fileperms($filePath)), -4),
            'owner' => function_exists('posix_getpwuid') ? posix_getpwuid(fileowner($filePath))['name'] : 'unknown',
        ];

        // 文件类型检测
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        $mimeType = mime_content_type($filePath);

        // 安全检查
        $securityChecks = [
            'extension_safe' => !in_array($extension, self::$dangerousExtensions),
            'size_reasonable' => filesize($filePath) < (50 * 1024 * 1024), // 50MB
            'permissions_safe' => (fileperms($filePath) & 0111) === 0, // 不可执行
            'content_safe' => true // 将在下面检查
        ];

        // 内容安全检查
        if (filesize($filePath) < (1024 * 1024)) { // 只检查小于1MB的文件
            $content = file_get_contents($filePath);
            foreach (self::$maliciousPatterns as $pattern) {
                if (preg_match($pattern, $content)) {
                    $securityChecks['content_safe'] = false;
                    break;
                }
            }
        }

        return [
            'file_info' => $fileInfo,
            'security_checks' => $securityChecks,
            'risk_level' => self::calculateRiskLevel($securityChecks),
            'recommendations' => self::getSecurityRecommendations($securityChecks)
        ];
    }

    /**
     * 计算风险等级
     */
    private static function calculateRiskLevel($checks)
    {
        $failedChecks = count(array_filter($checks, function($check) { return !$check; }));

        if ($failedChecks === 0) return 'low';
        if ($failedChecks <= 2) return 'medium';
        return 'high';
    }

    /**
     * 获取安全建议
     */
    private static function getSecurityRecommendations($checks)
    {
        $recommendations = [];

        if (!$checks['extension_safe']) {
            $recommendations[] = '文件扩展名存在安全风险，建议重命名或删除';
        }

        if (!$checks['size_reasonable']) {
            $recommendations[] = '文件大小异常，请检查文件内容';
        }

        if (!$checks['permissions_safe']) {
            $recommendations[] = '文件权限过高，建议移除执行权限';
        }

        if (!$checks['content_safe']) {
            $recommendations[] = '文件内容包含可疑代码，建议立即删除';
        }

        if (empty($recommendations)) {
            $recommendations[] = '文件安全检查通过';
        }

        return $recommendations;
    }
}

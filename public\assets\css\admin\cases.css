/* 客户案例管理专用样式 - 深色科技风 */
.cases-container {
    background: rgba(15, 15, 15, 0.95);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 20px;
    backdrop-filter: blur(25px);
    box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.4),
    0 0 40px rgba(120, 119, 198, 0.1);
    overflow: hidden;
    position: relative;
    margin-bottom: 30px;
}

.cases-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
    rgba(120, 119, 198, 0.8),
    rgba(255, 119, 198, 0.8),
    rgba(120, 219, 255, 0.8));
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* 列表头部 */
.list-header, .form-header {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.15) 0%,
        rgba(255, 119, 198, 0.1) 50%,
        rgba(120, 219, 255, 0.15) 100%);
    padding: 30px;
    border-bottom: 1px solid rgba(120, 119, 198, 0.2);
}

.list-header-content, .form-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.list-title-section, .form-title-section {
    display: flex;
    align-items: center;
    gap: 20px;
}

.list-icon, .form-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.3),
        rgba(255, 119, 198, 0.3));
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #fff;
    text-shadow: 0 0 20px rgba(120, 119, 198, 0.8);
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.3);
}

.list-title, .form-title {
    font-family: 'Orbitron', monospace;
    font-size: 28px;
    font-weight: 700;
    color: #fff;
    margin: 0;
    text-shadow: 0 0 20px rgba(120, 119, 198, 0.6);
}

.list-subtitle, .form-subtitle {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.7);
    margin: 5px 0 0 0;
    font-weight: 400;
    line-height: 1.5;
}

.btn-add-custom, .btn-back {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8) 0%,
        rgba(255, 119, 198, 0.8) 100%);
    border: 1px solid rgba(120, 119, 198, 0.5);
    color: #fff;
    padding: 12px 24px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.3);
}

.btn-add-custom:hover, .btn-back:hover {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 1) 0%,
        rgba(255, 119, 198, 1) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.4);
    color: #fff;
}

/* 列表主体 */
.list-body, .form-body {
    padding: 30px;
}

/* 案例列表 */
.cases-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 25px;
    padding: 5px;
}

.case-item {
    background: linear-gradient(135deg,
        rgba(20, 20, 30, 0.95) 0%,
        rgba(25, 25, 35, 0.9) 50%,
        rgba(30, 30, 40, 0.95) 100%);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 20px;
    padding: 30px;
    /* 分离边框和阴影的过渡时间 */
    transition-property: transform, box-shadow, border-color, background;
    transition-duration: 0.3s, 0.3s, 0.2s, 0.3s;
    transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(15px);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 2px 8px rgba(120, 119, 198, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    /* 防止边框闪动 - 增强版 */
    will-change: transform, box-shadow, border-color;
    backface-visibility: hidden;
    transform: translateY(0) translateZ(0);
    transform-origin: center center;
}

.case-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(120, 119, 198, 0.1),
        transparent);
    transition: left 0.8s ease;
}

.case-item:hover::before {
    left: 100%;
}

.case-item:hover {
    border-color: rgba(120, 119, 198, 0.45);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.32),
        0 5px 22px rgba(120, 119, 198, 0.18),
        0 0 25px rgba(120, 119, 198, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.06);
    transform: translateY(-1px) translateZ(0);
}

.case-content-wrapper {
    display: flex;
    gap: 24px;
    align-items: flex-start;
    position: relative;
    z-index: 2;
}

/* 案例缩略图 */
.case-thumbnail {
    width: 160px;
    height: 100px;
    border-radius: 20px;
    overflow: hidden;
    flex-shrink: 0;
    background: linear-gradient(135deg,
        rgba(25, 25, 35, 0.95) 0%,
        rgba(35, 35, 50, 0.9) 50%,
        rgba(45, 45, 60, 0.95) 100%);
    border: 2px solid rgba(120, 119, 198, 0.4);
    position: relative;
    box-shadow:
        0 12px 32px rgba(0, 0, 0, 0.4),
        0 4px 16px rgba(120, 119, 198, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.15),
        inset 0 -1px 0 rgba(120, 119, 198, 0.1);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.case-item:hover .case-thumbnail {
    border-color: rgba(120, 119, 198, 0.4);
    box-shadow:
        0 10px 28px rgba(120, 119, 198, 0.15),
        0 0 15px rgba(120, 119, 198, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.case-thumb-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    z-index: 2;
}

.case-item:hover .case-thumb-img {
    transform: scale(1.03);
    filter: brightness(1.03) contrast(1.01);
}

.case-thumb-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(120, 119, 198, 0.7);
    font-size: 28px;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.15) 0%,
        rgba(255, 119, 198, 0.15) 50%,
        rgba(120, 219, 255, 0.15) 100%);
    position: relative;
    z-index: 2;
}

.case-thumb-placeholder::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle,
        rgba(120, 119, 198, 0.2) 0%,
        transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow {
    0%, 100% {
        opacity: 0.5;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.1);
    }
}

/* 案例信息 */
.case-info {
    flex: 1;
    min-width: 0;
}

.case-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    gap: 15px;
}

.case-title {
    font-size: 18px;
    font-weight: 600;
    color: #fff;
    margin: 0;
    line-height: 1.4;
    flex: 1;
}

.case-badges {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.badge {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.badge-featured {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 152, 0, 0.2));
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.badge-industry {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.2), rgba(255, 119, 198, 0.2));
    color: rgba(120, 119, 198, 0.9);
    border: 1px solid rgba(120, 119, 198, 0.3);
}

.case-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 12px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 13px;
}

.meta-item i {
    color: rgba(120, 119, 198, 0.7);
    width: 14px;
}

.case-summary {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
}

/* 案例操作 */
.case-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: flex-end;
    min-width: 120px;
}

.status-toggle {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.status-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 26px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(60, 60, 80, 0.8);
    border: 2px solid rgba(80, 80, 100, 0.6);
    border-radius: 26px;
    transition: all 0.3s ease;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 2px;
    top: 2px;
    background: linear-gradient(135deg, #ffffff, #e8e8e8);
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

input:checked + .slider {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.9), rgba(255, 119, 198, 0.7));
    border-color: rgba(120, 119, 198, 0.8);
    box-shadow: 0 0 15px rgba(120, 119, 198, 0.4);
}

input:checked + .slider:before {
    transform: translateX(24px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.action-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
}

.btn-action {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    font-size: 16px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.btn-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.btn-action:hover::before {
    opacity: 1;
}

.btn-edit {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.2), rgba(3, 169, 244, 0.2));
    color: #2196f3;
    border: 1px solid rgba(33, 150, 243, 0.3);
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.2);
}

.btn-edit:hover {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.3), rgba(3, 169, 244, 0.3));
    color: #1976d2;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.3);
}

.btn-delete {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.2), rgba(229, 57, 53, 0.2));
    color: #f44336;
    border: 1px solid rgba(244, 67, 54, 0.3);
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.2);
}

.btn-delete:hover {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.3), rgba(229, 57, 53, 0.3));
    color: #d32f2f;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.3);
}

/* 图片上传相关样式 */
.image-upload-section {
    margin-top: 15px;
}

.btn-upload-image {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8) 0%,
        rgba(255, 119, 198, 0.6) 100%);
    border: none;
    border-radius: 10px;
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(120, 119, 198, 0.3);
}

.btn-upload-image:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(120, 119, 198, 0.4);
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.9) 0%,
        rgba(255, 119, 198, 0.7) 100%);
}

.btn-upload-image i {
    font-size: 16px;
}

/* 当前图片预览 */
.current-image-preview {
    width: 320px;
    margin-top: 20px;
    padding: 15px;
    background: rgba(30, 30, 40, 0.6);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 12px;
}

.preview-wrapper {
    position: relative;
    display: inline-block;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.preview-wrapper img {
    max-width: 288px;
    max-height: 200px;
    display: block;
    border-radius: 8px;
}

.preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.preview-wrapper:hover .preview-overlay {
    opacity: 1;
}

.btn-change-image,
.btn-remove-image {
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.btn-change-image {
    background: rgba(33, 150, 243, 0.8);
    color: #ffffff;
}

.btn-change-image:hover {
    background: rgba(33, 150, 243, 1);
    transform: translateY(-1px);
}

.btn-remove-image {
    background: rgba(244, 67, 54, 0.8);
    color: #ffffff;
}

.btn-remove-image:hover {
    background: rgba(244, 67, 54, 1);
    transform: translateY(-1px);
}

/* 表单样式 - 深色科技风 */
.case-form .form-grid {
    display: grid;
    gap: 30px;
}

.case-form .form-section {
    background: linear-gradient(135deg,
        rgba(20, 20, 30, 0.95) 0%,
        rgba(25, 25, 35, 0.9) 50%,
        rgba(30, 30, 40, 0.95) 100%);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 20px;
    padding: 30px;
    backdrop-filter: blur(15px);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 2px 8px rgba(120, 119, 198, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
}

.case-form .form-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
        rgba(120, 119, 198, 0.6),
        rgba(255, 119, 198, 0.6),
        rgba(120, 219, 255, 0.6));
    opacity: 0.7;
}

.case-form .section-header {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(120, 119, 198, 0.2);
}

.case-form .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #fff;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
    text-shadow: 0 0 10px rgba(120, 119, 198, 0.5);
}

.case-form .section-title i {
    color: rgba(120, 119, 198, 0.8);
    font-size: 20px;
}

.case-form .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    margin-bottom: 25px;
}

.case-form .form-group {
    display: flex;
    flex-direction: column;
}

.case-form .form-label {
    font-size: 14px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.case-form .form-label i {
    color: rgba(120, 119, 198, 0.7);
    font-size: 14px;
}

.case-form .form-label.required::after {
    content: '*';
    color: #ff6b6b;
    margin-left: 4px;
    font-weight: bold;
}

.case-form .form-input,
.case-form .form-select,
.case-form .form-textarea {
    padding: 15px 18px;
    border: 2px solid rgba(120, 119, 198, 0.3);
    border-radius: 12px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: rgba(30, 30, 40, 0.8);
    color: #fff;
    backdrop-filter: blur(10px);
    box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.case-form .form-input::placeholder,
.case-form .form-textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.case-form .form-input:focus,
.case-form .form-select:focus,
.case-form .form-textarea:focus {
    outline: none;
    border-color: rgba(120, 119, 198, 0.6);
    box-shadow:
        0 0 0 4px rgba(120, 119, 198, 0.1),
        0 6px 20px rgba(120, 119, 198, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    background: rgba(35, 35, 45, 0.9);
}

.case-form .form-select option {
    background: rgba(30, 30, 40, 0.95);
    color: #fff;
    padding: 10px;
}

.case-form .form-help {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 6px;
    line-height: 1.4;
}

/* 复选框样式 */
.case-form .checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.case-form .checkbox-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    padding: 12px 16px;
    border-radius: 12px;
    background: rgba(30, 30, 40, 0.6);
    border: 1px solid rgba(120, 119, 198, 0.2);
    transition: all 0.3s ease;
}

.case-form .checkbox-label:hover {
    background: rgba(35, 35, 45, 0.8);
    border-color: rgba(120, 119, 198, 0.3);
}

.case-form .checkbox-custom {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(120, 119, 198, 0.4);
    border-radius: 6px;
    position: relative;
    background: rgba(30, 30, 40, 0.8);
    transition: all 0.3s ease;
}

.case-form .checkbox-label input[type="checkbox"] {
    display: none;
}

.case-form .checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.8), rgba(255, 119, 198, 0.6));
    border-color: rgba(120, 119, 198, 0.8);
    box-shadow: 0 0 15px rgba(120, 119, 198, 0.3);
}

.case-form .checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-size: 12px;
    font-weight: bold;
}

.case-form .checkbox-text {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.case-form .checkbox-text i {
    color: rgba(120, 119, 198, 0.7);
}

.case-form .checkbox-help {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 4px;
    margin-left: 32px;
}

/* 表单操作按钮 */
.form-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    padding: 30px;

}

.btn-submit, .btn-cancel {
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    min-width: 140px;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.btn-submit {
    background: linear-gradient(135deg,
        rgba(76, 175, 80, 0.8) 0%,
        rgba(139, 195, 74, 0.8) 100%);
    color: #fff;
    border: 1px solid rgba(76, 175, 80, 0.5);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
}

.btn-submit:hover {
    background: linear-gradient(135deg,
        rgba(76, 175, 80, 1) 0%,
        rgba(139, 195, 74, 1) 100%);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(76, 175, 80, 0.4);
}

.btn-cancel {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.6) 0%,
        rgba(255, 119, 198, 0.6) 100%);
    color: #fff;
    border: 1px solid rgba(120, 119, 198, 0.4);
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.2);
}

.btn-cancel:hover {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8) 0%,
        rgba(255, 119, 198, 0.8) 100%);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(120, 119, 198, 0.3);
    color: #fff;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .case-content-wrapper {
        flex-direction: column;
        gap: 16px;
    }

    .case-thumbnail {
        width: 100%;
        height: 200px;
    }

    .case-actions {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    .case-form .form-row {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .form-actions {
        flex-direction: column;
        gap: 15px;
    }

    .btn-submit, .btn-cancel {
        width: 100%;
    }
}

/* 空状态样式 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 40px;
    text-align: center;
    background: linear-gradient(135deg,
        rgba(20, 20, 30, 0.6) 0%,
        rgba(25, 25, 35, 0.4) 50%,
        rgba(30, 30, 40, 0.6) 100%);
    border: 2px dashed rgba(120, 119, 198, 0.3);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.empty-state::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center,
        rgba(120, 119, 198, 0.05) 0%,
        transparent 70%);
    animation: pulse-bg 3s ease-in-out infinite;
}

@keyframes pulse-bg {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

.empty-icon {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.2) 0%,
        rgba(255, 119, 198, 0.2) 50%,
        rgba(120, 219, 255, 0.2) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 48px;
    color: rgba(120, 119, 198, 0.7);
    margin-bottom: 30px;
    position: relative;
    z-index: 1;
    box-shadow:
        0 15px 35px rgba(120, 119, 198, 0.2),
        inset 0 2px 0 rgba(255, 255, 255, 0.1);
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.empty-title {
    font-size: 24px;
    font-weight: 600;
    color: #fff;
    margin: 0 0 15px 0;
    text-shadow: 0 0 20px rgba(120, 119, 198, 0.5);
    position: relative;
    z-index: 1;
}

.empty-description {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6;
    max-width: 400px;
    margin: 0;
    position: relative;
    z-index: 1;
}

/* 分页样式 */
.custom-pagination-container {
    margin-top: 40px;
    padding: 25px 0;
    border-top: 1px solid rgba(120, 119, 198, 0.2);
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.05) 0%,
        rgba(255, 119, 198, 0.03) 50%,
        rgba(120, 219, 255, 0.05) 100%);
}

.custom-pagination-nav {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
}

.pagination-info {
    text-align: center;
}

.pagination-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    font-weight: 500;
}

.pagination-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
    justify-content: center;
}

.pagination-btn {
    padding: 10px 16px;
    border: 1px solid rgba(120, 119, 198, 0.3);
    background: linear-gradient(135deg,
        rgba(30, 30, 40, 0.8) 0%,
        rgba(35, 35, 45, 0.8) 100%);
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 40px;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.pagination-btn:hover:not(.disabled) {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.3) 0%,
        rgba(255, 119, 198, 0.3) 100%);
    border-color: rgba(120, 119, 198, 0.5);
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(120, 119, 198, 0.2);
}

.pagination-btn.active {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8) 0%,
        rgba(255, 119, 198, 0.8) 100%);
    border-color: rgba(120, 119, 198, 0.8);
    color: #fff;
    box-shadow: 0 0 20px rgba(120, 119, 198, 0.4);
}

.pagination-btn.disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background: rgba(30, 30, 40, 0.4);
    border-color: rgba(120, 119, 198, 0.1);
}

.pagination-ellipsis {
    color: rgba(255, 255, 255, 0.5);
    padding: 10px 8px;
    font-size: 14px;
}

/* 日期输入框日历图标样式 */
.case-form input[type="date"] {
    position: relative;
    color-scheme: dark;
}

/* WebKit浏览器（Chrome, Safari, Edge）的日历图标 */
.case-form input[type="date"]::-webkit-calendar-picker-indicator {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgba(255,255,255,0.8)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3e%3c/rect%3e%3cline x1='16' y1='2' x2='16' y2='6'%3e%3c/line%3e%3cline x1='8' y1='2' x2='8' y2='6'%3e%3c/line%3e%3cline x1='3' y1='10' x2='21' y2='10'%3e%3c/line%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 16px 16px;
    width: 20px;
    height: 20px;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.case-form input[type="date"]::-webkit-calendar-picker-indicator:hover {
    opacity: 1;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgba(120,119,198,0.9)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3e%3c/rect%3e%3cline x1='16' y1='2' x2='16' y2='6'%3e%3c/line%3e%3cline x1='8' y1='2' x2='8' y2='6'%3e%3c/line%3e%3cline x1='3' y1='10' x2='21' y2='10'%3e%3c/line%3e%3c/svg%3e");
}

/* Firefox浏览器的日历图标 */
.case-form input[type="date"]::-moz-calendar-picker-indicator {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgba(255,255,255,0.8)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3e%3c/rect%3e%3cline x1='16' y1='2' x2='16' y2='6'%3e%3c/line%3e%3cline x1='8' y1='2' x2='8' y2='6'%3e%3c/line%3e%3cline x1='3' y1='10' x2='21' y2='10'%3e%3c/line%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 16px 16px;
    width: 20px;
    height: 20px;
    cursor: pointer;
    opacity: 0.8;
}

/* 通用日期输入框样式增强 */
.case-form input[type="date"]:focus::-webkit-calendar-picker-indicator {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgba(120,119,198,1)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3e%3c/rect%3e%3cline x1='16' y1='2' x2='16' y2='6'%3e%3c/line%3e%3cline x1='8' y1='2' x2='8' y2='6'%3e%3c/line%3e%3cline x1='3' y1='10' x2='21' y2='10'%3e%3c/line%3e%3c/svg%3e");
    opacity: 1;
}

-- 三只鱼网络科技数据库备份
-- 备份时间: 2025-06-11 01:27:28
-- 数据库: san
-- 版本: 5.7.44

SET FOREIGN_KEY_CHECKS=0;

-- 表结构: admin_users
DROP TABLE IF EXISTS `admin_users`;
CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `real_name` varchar(50) DEFAULT NULL,
  `role` enum('admin','editor') DEFAULT 'editor',
  `status` tinyint(1) DEFAULT '1',
  `last_login` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  <PERSON>IMAR<PERSON>E<PERSON> (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

-- 表数据: admin_users (1 条记录)
INSERT INTO `admin_users` (`id`, `username`, `password`, `email`, `real_name`, `role`, `status`, `last_login`, `created_at`, `updated_at`) VALUES
('1', 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', '系统管理员', 'admin', '1', '2025-06-03 00:57:10', '2025-05-30 02:54:17', '2025-06-03 00:57:10');

-- 表结构: banners
DROP TABLE IF EXISTS `banners`;
CREATE TABLE `banners` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `subtitle` varchar(300) DEFAULT NULL,
  `description` text COMMENT '轮播图描述',
  `image` varchar(500) NOT NULL,
  `link_url` varchar(500) DEFAULT NULL,
  `link_target` enum('_self','_blank') DEFAULT '_self',
  `sort_order` int(11) DEFAULT '0',
  `status` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4;

-- 表结构: cases
DROP TABLE IF EXISTS `cases`;
CREATE TABLE `cases` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `slug` varchar(200) NOT NULL,
  `client_name` varchar(100) NOT NULL,
  `industry` varchar(50) DEFAULT NULL,
  `summary` varchar(500) DEFAULT NULL,
  `description` longtext,
  `image` varchar(500) DEFAULT NULL,
  `gallery` text,
  `project_url` varchar(500) DEFAULT NULL,
  `completion_date` date DEFAULT NULL,
  `sort_order` int(11) DEFAULT '0',
  `status` tinyint(1) DEFAULT '1',
  `views` int(11) DEFAULT '0',
  `is_featured` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4;

-- 表数据: cases (6 条记录)
INSERT INTO `cases` (`id`, `title`, `slug`, `client_name`, `industry`, `summary`, `description`, `image`, `gallery`, `project_url`, `completion_date`, `sort_order`, `status`, `views`, `is_featured`, `created_at`, `updated_at`) VALUES
('1', '某大型制造企业数字化转型项目', 'manufacturing-digital-transformation', '华东制造集团', '制造业', '为华东制造集团打造的全面数字化转型解决方案，包括ERP系统升级、生产管理系统和数据分析平台。', '<p>项目背景：华东制造集团是一家拥有30年历史的大型制造企业，面临传统管理模式效率低下的问题。</p><p>解决方案：我们为其设计了完整的数字化转型方案，包括：</p><ul><li>ERP系统现代化升级</li><li>智能生产管理系统</li><li>实时数据分析平台</li><li>移动端管理应用</li></ul><p>项目成果：生产效率提升35%，管理成本降低25%，获得客户高度认可。</p>', '/uploads/image/cases/manufacturing-digital-transformation.jpg', NULL, 'https://manufacturing-demo.com', '2024-11-15', '0', '1', '245', '1', '2025-06-11 00:29:42', '2025-06-11 01:11:31'),
('2', '知名电商平台开发案例', 'ecommerce-platform-development', '优品商城', '电子商务', '为优品商城开发的多商户电商平台，支持B2C和B2B模式，日处理订单量超过10万单。', '<p>项目需求：客户需要一个功能完善的多商户电商平台，支持大并发访问。</p><p>技术方案：</p><ul><li>微服务架构设计</li><li>Redis缓存优化</li><li>CDN加速部署</li><li>支付系统集成</li><li>物流系统对接</li></ul><p>项目成果：平台上线后日活用户超过50万，订单转化率提升40%。</p>', '/uploads/image/cases/ecommerce-platform-development.jpg', NULL, 'https://youpin-mall.com', '2024-10-20', '0', '1', '190', '1', '2025-06-11 00:29:42', '2025-06-11 01:16:55'),
('3', '教育机构在线学习平台', 'online-learning-platform', '智慧教育科技', '教育培训', '为智慧教育科技开发的在线学习平台，支持直播授课、作业管理、学习进度跟踪等功能。', '<p>项目背景：疫情期间，传统教育机构急需线上教学解决方案。</p><p>功能特色：</p><ul><li>高清视频直播系统</li><li>互动白板功能</li><li>作业批改系统</li><li>学习数据分析</li><li>家长监督功能</li></ul><p>项目成果：平台支持同时在线学习人数超过5000人，获得师生一致好评。</p>', '/uploads/image/cases/online-learning-platform.jpg', NULL, 'https://smart-edu.com', '2024-09-30', '0', '1', '156', '0', '2025-06-11 00:29:42', '2025-06-11 01:11:34'),
('4', '医疗机构信息管理系统', 'hospital-management-system', '仁爱医院', '医疗健康', '为仁爱医院开发的综合信息管理系统，包括挂号、诊疗、药房、财务等全流程管理。', '<p>项目需求：医院需要一套完整的信息化管理系统，提升医疗服务效率。</p><p>系统模块：</p><ul><li>患者挂号系统</li><li>电子病历管理</li><li>药房库存管理</li><li>财务结算系统</li><li>医生工作站</li></ul><p>项目成果：患者就诊时间缩短50%，医院运营效率显著提升。</p>', '/uploads/image/cases/hospital-management-system.jpg', NULL, 'https://renai-hospital.com', '2024-08-25', '0', '1', '134', '1', '2025-06-11 00:29:42', '2025-06-11 01:11:36'),
('5', '金融科技APP开发项目', 'fintech-app-development', '普惠金融', '金融科技', '为普惠金融开发的移动金融服务APP，提供贷款申请、理财投资、账户管理等服务。', '<p>项目挑战：金融行业对安全性和稳定性要求极高，需要严格的风控措施。</p><p>技术亮点：</p><ul><li>多重安全认证</li><li>实时风险控制</li><li>数据加密传输</li><li>生物识别技术</li><li>智能投顾算法</li></ul><p>项目成果：APP上线后用户注册量突破100万，获得金融监管部门认可。</p>', '/uploads/image/cases/fintech-app-development.jpg', NULL, 'https://puhui-finance.com', '2024-07-10', '0', '1', '178', '0', '2025-06-11 00:29:42', '2025-06-11 01:18:18'),
('6', '物流管理系统优化项目', 'logistics-system-optimization', '快运物流', '物流运输', '为快运物流优化的智能物流管理系统，实现路线规划、货物跟踪、成本控制的全面升级。', '<p>项目背景：传统物流企业面临成本上升、效率低下的挑战。</p><p>优化方案：</p><ul><li>智能路线规划算法</li><li>实时货物跟踪系统</li><li>自动化仓储管理</li><li>成本分析与控制</li><li>客户服务优化</li></ul><p>项目成果：运输成本降低30%，客户满意度提升45%，成为行业标杆。</p>', '/uploads/image/cases/logistics-system-optimization.jpg', NULL, 'https://kuaiyun-logistics.com', '2024-06-15', '0', '1', '167', '1', '2025-06-11 00:29:42', '2025-06-11 01:11:40');

-- 表结构: contact_forms
DROP TABLE IF EXISTS `contact_forms`;
CREATE TABLE `contact_forms` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `company` varchar(100) DEFAULT NULL,
  `subject` varchar(200) NOT NULL,
  `message` text NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` varchar(500) DEFAULT NULL,
  `status` enum('new','read','replied','closed') DEFAULT 'new',
  `admin_reply` text,
  `replied_at` datetime DEFAULT NULL,
  `replied_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4;

-- 表结构: image_groups
DROP TABLE IF EXISTS `image_groups`;
CREATE TABLE `image_groups` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '分组ID',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分组名称',
  `slug` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分组别名',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '分组描述',
  `parent_id` int(11) unsigned DEFAULT NULL COMMENT '父分组ID',
  `sort_order` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序权重',
  `image_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '图片数量',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1:启用 0:禁用)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_slug` (`slug`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片分组表';

-- 表数据: image_groups (5 条记录)
INSERT INTO `image_groups` (`id`, `name`, `slug`, `description`, `parent_id`, `sort_order`, `image_count`, `status`, `created_at`, `updated_at`) VALUES
('1', '默认分组', 'default', '系统默认图片分组', NULL, '0', '0', '1', '2025-06-03 03:27:15', '2025-06-03 03:27:15'),
('2', '新闻图片', 'news', '新闻相关图片', NULL, '10', '0', '1', '2025-06-03 03:27:15', '2025-06-03 03:27:15'),
('3', '产品图片', 'products', '产品展示图片', NULL, '20', '0', '1', '2025-06-03 03:27:15', '2025-06-03 03:27:15'),
('4', '轮播图片', 'banners', '首页轮播图片', NULL, '30', '0', '1', '2025-06-03 03:27:15', '2025-06-03 03:27:15'),
('5', '其他图片', 'others', '其他类型图片', NULL, '999', '0', '1', '2025-06-03 03:27:15', '2025-06-03 03:27:15');

-- 表结构: images
DROP TABLE IF EXISTS `images`;
CREATE TABLE `images` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '图片ID',
  `filename` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始文件名',
  `stored_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '存储文件名',
  `file_path` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件路径',
  `file_url` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '访问URL',
  `file_size` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '文件大小(字节)',
  `mime_type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'MIME类型',
  `extension` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件扩展名',
  `width` int(11) unsigned DEFAULT NULL COMMENT '图片宽度',
  `height` int(11) unsigned DEFAULT NULL COMMENT '图片高度',
  `group_id` int(11) unsigned DEFAULT NULL COMMENT '分组ID',
  `alt_text` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片描述',
  `tags` text COLLATE utf8mb4_unicode_ci COMMENT '图片标签(JSON格式)',
  `upload_ip` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上传IP',
  `user_agent` text COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1:正常 0:禁用)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_group_id` (`group_id`),
  KEY `idx_mime_type` (`mime_type`),
  KEY `idx_extension` (`extension`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_file_size` (`file_size`)
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片管理表';

-- 表结构: news
DROP TABLE IF EXISTS `news`;
CREATE TABLE `news` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL,
  `title` varchar(200) NOT NULL,
  `slug` varchar(200) NOT NULL,
  `summary` varchar(500) DEFAULT NULL,
  `content` longtext,
  `image` varchar(500) DEFAULT NULL,
  `author` varchar(50) DEFAULT NULL,
  `tags` varchar(200) DEFAULT NULL,
  `sort_order` int(11) DEFAULT '0',
  `status` tinyint(1) DEFAULT '1',
  `views` int(11) DEFAULT '0',
  `is_featured` tinyint(1) DEFAULT '0',
  `published_at` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `category_id` (`category_id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4;

-- 表数据: news (6 条记录)
INSERT INTO `news` (`id`, `category_id`, `title`, `slug`, `summary`, `content`, `image`, `author`, `tags`, `sort_order`, `status`, `views`, `is_featured`, `published_at`, `created_at`, `updated_at`) VALUES
('1', '1', '三只鱼科技荣获2024年度最佳技术创新奖', 'tech-innovation-award-2024', '在刚刚结束的2024年度科技创新大会上，三只鱼科技凭借其在人工智能和大数据领域的突出贡献，荣获最佳技术创新奖。', '<p>2024年12月19日，在北京举办的年度科技创新大会上，三只鱼科技有限公司凭借其在人工智能和大数据领域的卓越表现，荣获\"2024年度最佳技术创新奖\"。</p><p>该奖项是对我们团队在过去一年中不懈努力和技术突破的认可。我们的AI智能分析平台和企业数字化转型解决方案得到了业界的高度评价。</p><p>未来，我们将继续致力于技术创新，为客户提供更优质的服务。</p>', '/uploads/image/news/tech-innovation-award-2024.jpg', '韩总', '技术创新,人工智能,大数据,获奖', '0', '1', '156', '1', '2024-12-19 10:00:00', '2025-06-11 00:32:25', '2025-06-11 01:10:53'),
('2', '2', '2024年企业数字化转型趋势报告发布', 'digital-transformation-trends-2024', '最新发布的行业报告显示，企业数字化转型已成为2024年的主要趋势，云计算、AI和物联网技术应用快速增长。', '<p>根据最新发布的《2024年企业数字化转型趋势报告》，今年企业在数字化转型方面的投入同比增长35%。</p><p>报告指出，云计算、人工智能和物联网技术成为企业数字化转型的三大核心驱动力。超过80%的企业表示将在未来两年内加大对这些技术的投资。</p><p>三只鱼科技作为行业领先的数字化解决方案提供商，将继续为企业提供专业的转型服务。</p>', '/uploads/image/news/digital-transformation-trends-2024.jpg', '技术部', '数字化转型,云计算,人工智能,物联网', '0', '1', '89', '0', '2024-12-18 14:30:00', '2025-06-11 00:32:25', '2025-06-11 01:10:55'),
('3', '3', '全新AI智能客服系统正式发布', 'ai-customer-service-system-launch', '经过6个月的精心研发，我们的AI智能客服系统正式发布，支持多语言对话，智能问答准确率达95%以上。', '<p>今日，三只鱼科技正式发布全新的AI智能客服系统V2.0。该系统经过6个月的精心研发和测试，在智能对话、问题理解和回答准确性方面都有了显著提升。</p><p>新系统主要特性包括：</p><ul><li>支持中英文双语对话</li><li>智能问答准确率达95%以上</li><li>7x24小时不间断服务</li><li>与现有CRM系统无缝集成</li></ul><p>目前已有多家企业客户开始试用，反馈良好。</p>', '/uploads/image/news/ai-customer-service-system-launch.jpg', '产品经理', '人工智能,客服系统,产品发布,智能对话', '0', '1', '234', '1', '2024-12-17 09:15:00', '2025-06-11 00:32:25', '2025-06-11 01:10:57'),
('4', '4', '微服务架构在企业级应用中的最佳实践', 'microservices-best-practices', '本文分享了微服务架构在企业级应用开发中的最佳实践，包括服务拆分、数据管理、监控和部署策略。', '<p>微服务架构已成为现代企业级应用开发的主流选择。本文将分享我们在实际项目中总结的最佳实践。</p><h3>服务拆分原则</h3><p>1. 按业务领域拆分<br>2. 保持服务的高内聚低耦合<br>3. 考虑团队结构和沟通成本</p><h3>数据管理策略</h3><p>1. 每个服务拥有独立的数据库<br>2. 通过API进行数据交互<br>3. 实现最终一致性</p><h3>监控和运维</h3><p>1. 分布式链路追踪<br>2. 统一日志管理<br>3. 服务健康检查</p>', '/uploads/image/news/microservices-best-practices.jpg', '架构师', '微服务,架构设计,最佳实践,技术分享', '0', '1', '167', '0', '2024-12-16 16:20:00', '2025-06-11 00:32:25', '2025-06-11 01:10:59'),
('5', '1', '三只鱼科技与知名企业达成战略合作', 'strategic-partnership-announcement', '我们很高兴宣布与多家知名企业达成战略合作伙伴关系，共同推进企业数字化转型和技术创新。', '<p>2024年12月15日，三只鱼科技与多家行业领先企业正式签署战略合作协议，建立长期合作伙伴关系。</p><p>此次合作将重点围绕以下几个方面展开：</p><ul><li>企业数字化转型咨询服务</li><li>定制化软件开发</li><li>云计算和大数据解决方案</li><li>人工智能技术应用</li></ul><p>通过强强联合，我们将为更多企业提供专业、高效的技术服务，推动行业数字化发展。</p>', '/uploads/image/news/strategic-partnership-announcement.jpg', '商务部', '战略合作,企业合作,数字化转型,技术服务', '0', '1', '198', '1', '2024-12-15 11:00:00', '2025-06-11 00:32:25', '2025-06-11 01:11:01'),
('6', '4', 'Docker容器化部署实战指南', 'docker-deployment-guide', '详细介绍如何使用Docker进行应用容器化部署，包括镜像构建、容器编排和生产环境最佳实践。', '<p>Docker容器化技术已成为现代应用部署的标准选择。本文将详细介绍Docker在实际项目中的应用。</p><h3>Docker基础概念</h3><p>1. 镜像(Image)和容器(Container)<br>2. Dockerfile编写规范<br>3. 数据卷和网络配置</p><h3>容器编排</h3><p>1. Docker Compose使用<br>2. 服务发现和负载均衡<br>3. 健康检查和自动重启</p><h3>生产环境部署</h3><p>1. 安全配置和权限管理<br>2. 监控和日志收集<br>3. 备份和灾难恢复</p>', '/uploads/image/news/docker-deployment-guide.jpg', '运维工程师', 'Docker,容器化,部署,运维,技术分享', '0', '1', '145', '0', '2024-12-14 13:45:00', '2025-06-11 00:32:25', '2025-06-11 01:11:04');

-- 表结构: news_categories
DROP TABLE IF EXISTS `news_categories`;
CREATE TABLE `news_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `description` text,
  `sort_order` int(11) DEFAULT '0',
  `status` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4;

-- 表数据: news_categories (4 条记录)
INSERT INTO `news_categories` (`id`, `name`, `slug`, `description`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES
('1', '公司动态', 'company-news', '公司最新动态和资讯', '1', '1', '2025-05-31 09:48:35', '2025-05-31 09:48:35'),
('2', '行业资讯', 'industry-news', '行业相关新闻和趋势', '2', '1', '2025-05-31 09:48:35', '2025-06-04 04:38:05'),
('3', '产品发布', 'product-release', '新产品发布和更新', '3', '1', '2025-05-31 09:48:35', '2025-05-31 09:48:35'),
('4', '技术分享', 'tech-sharing', '技术文章和经验分享', '4', '1', '2025-05-31 09:48:35', '2025-05-31 09:48:35');

-- 表结构: page_templates
DROP TABLE IF EXISTS `page_templates`;
CREATE TABLE `page_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板名称',
  `slug` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '页面URL标识',
  `description` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '模板描述',
  `type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'page' COMMENT '模板类型',
  `config` longtext COLLATE utf8mb4_unicode_ci COMMENT '模板配置JSON',
  `html_content` longtext COLLATE utf8mb4_unicode_ci COMMENT '生成的HTML内容',
  `css_content` longtext COLLATE utf8mb4_unicode_ci COMMENT '生成的CSS内容',
  `preview_image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '预览图片',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态：0=草稿，1=已发布',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_slug` (`slug`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort_order`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='页面模板表';

-- 表结构: product_attributes
DROP TABLE IF EXISTS `product_attributes`;
CREATE TABLE `product_attributes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `attribute_name` varchar(100) NOT NULL COMMENT '属性名称',
  `attribute_value` varchar(500) NOT NULL COMMENT '属性值',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品属性表';

-- 表结构: product_categories
DROP TABLE IF EXISTS `product_categories`;
CREATE TABLE `product_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `description` text,
  `icon` varchar(200) DEFAULT NULL,
  `parent_id` int(11) DEFAULT '0',
  `sort_order` int(11) DEFAULT '0',
  `status` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4;

-- 表数据: product_categories (4 条记录)
INSERT INTO `product_categories` (`id`, `name`, `slug`, `description`, `icon`, `parent_id`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES
('1', '软件开发', 'software-development', '定制软件开发服务', '', '0', '1', '1', '2025-06-09 20:24:11', '2025-06-09 20:40:35'),
('2', '网站建设', 'website-development', '企业网站建设服务', NULL, '0', '2', '1', '2025-06-09 20:24:11', '2025-06-09 20:24:11'),
('3', '移动应用', 'mobile-app', '移动APP开发服务', NULL, '0', '3', '1', '2025-06-09 20:24:11', '2025-06-09 20:24:11'),
('4', '系统集成', 'system-integration', '企业系统集成服务', NULL, '0', '4', '1', '2025-06-09 20:24:11', '2025-06-09 20:24:11');

-- 表结构: products
DROP TABLE IF EXISTS `products`;
CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL,
  `name` varchar(200) NOT NULL,
  `slug` varchar(200) NOT NULL,
  `short_description` varchar(500) DEFAULT NULL,
  `description` longtext,
  `features` longtext,
  `specifications` longtext COMMENT '产品规格（JSON格式）',
  `image` varchar(500) DEFAULT NULL,
  `icon` varchar(200) DEFAULT NULL COMMENT '产品图标',
  `gallery` text,
  `price` varchar(50) DEFAULT NULL,
  `original_price` varchar(50) DEFAULT NULL COMMENT '原价',
  `stock_status` enum('in_stock','out_of_stock','pre_order') DEFAULT 'in_stock' COMMENT '库存状态',
  `sku` varchar(100) DEFAULT NULL COMMENT '产品SKU',
  `tags` varchar(200) DEFAULT NULL COMMENT '标签',
  `meta_title` varchar(200) DEFAULT NULL COMMENT 'SEO标题',
  `meta_description` varchar(500) DEFAULT NULL COMMENT 'SEO描述',
  `sort_order` int(11) DEFAULT '0',
  `status` tinyint(1) DEFAULT '1',
  `views` int(11) DEFAULT '0',
  `is_featured` tinyint(1) DEFAULT '0' COMMENT '是否推荐',
  `is_hot` tinyint(1) DEFAULT '0' COMMENT '是否热门',
  `is_new` tinyint(1) DEFAULT '0' COMMENT '是否新品',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `category_id` (`category_id`),
  KEY `idx_featured` (`is_featured`),
  KEY `idx_hot` (`is_hot`),
  KEY `idx_new` (`is_new`),
  KEY `idx_sku` (`sku`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_icon` (`icon`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4;

-- 表数据: products (12 条记录)
INSERT INTO `products` (`id`, `category_id`, `name`, `slug`, `short_description`, `description`, `features`, `specifications`, `image`, `icon`, `gallery`, `price`, `original_price`, `stock_status`, `sku`, `tags`, `meta_title`, `meta_description`, `sort_order`, `status`, `views`, `is_featured`, `is_hot`, `is_new`, `created_at`, `updated_at`) VALUES
('1', '1', 'AI智能分析平台', 'ai-analytics-platform', '基于机器学习的企业数据智能分析平台，提供实时数据洞察和预测分析。', '<p>AI智能分析平台是我们的旗舰产品，集成了最新的机器学习算法和大数据处理技术。</p><p>主要功能包括：数据可视化、预测分析、异常检测、智能报告生成等。</p>', NULL, NULL, '/uploads/image/products/ai-analytics-platform.jpg', 'fas fa-chart-line', NULL, '面议', NULL, 'in_stock', NULL, NULL, NULL, NULL, '0', '1', '0', '1', '1', '0', '2025-06-11 00:29:42', '2025-06-11 01:11:07'),
('2', '1', '企业级CRM系统', 'enterprise-crm-system', '全功能客户关系管理系统，支持销售管理、客户服务、营销自动化等功能。', '<p>专为中大型企业设计的CRM系统，提供完整的客户生命周期管理解决方案。</p><p>包含销售漏斗、客户档案、工单系统、报表分析等核心模块。</p>', NULL, NULL, '/uploads/image/products/enterprise-crm-system.jpg', 'fas fa-users-cog', NULL, '50,000起', NULL, 'in_stock', NULL, NULL, NULL, NULL, '0', '1', '0', '1', '0', '1', '2025-06-11 00:29:42', '2025-06-11 01:21:03'),
('3', '1', '智能办公协作平台', 'smart-office-platform', '集成即时通讯、文档协作、项目管理、视频会议的一体化办公平台。', '<p>为现代企业打造的智能办公解决方案，提升团队协作效率。</p><p>支持多端同步、权限管理、审批流程、数据安全等企业级功能。</p>', NULL, NULL, '/uploads/image/products/smart-office-platform.jpg', 'fas fa-laptop-code', NULL, '30,000起', NULL, 'in_stock', NULL, NULL, NULL, NULL, '0', '1', '1', '0', '1', '1', '2025-06-11 00:29:42', '2025-06-11 01:21:03'),
('4', '2', '响应式企业官网', 'responsive-corporate-website', '专业的企业官网建设服务，支持PC、平板、手机多端适配，SEO优化。', '<p>采用最新的响应式设计技术，确保在各种设备上都有完美的显示效果。</p><p>包含内容管理系统、SEO优化、数据统计、在线客服等功能。</p>', NULL, NULL, '/uploads/image/products/responsive-corporate-website.jpg', 'fas fa-globe', NULL, '15,000起', NULL, 'in_stock', NULL, NULL, NULL, NULL, '0', '1', '0', '1', '0', '0', '2025-06-11 00:29:42', '2025-06-11 01:21:03'),
('5', '2', '电商购物平台', 'ecommerce-platform', '功能完善的电商解决方案，支持多商户、多支付方式、库存管理等。', '<p>专业的电商平台开发服务，支持B2C、B2B、O2O等多种商业模式。</p><p>集成支付网关、物流系统、营销工具、数据分析等核心功能。</p>', NULL, NULL, '/uploads/image/products/ecommerce-platform.png', 'fas fa-shopping-cart', NULL, '80,000起', NULL, 'in_stock', NULL, NULL, NULL, NULL, '0', '1', '0', '0', '1', '0', '2025-06-11 00:29:42', '2025-06-11 01:21:03'),
('6', '2', '内容管理系统', 'content-management-system', '灵活易用的内容管理系统，支持多站点管理、权限控制、模板定制。', '<p>为企业和机构提供专业的内容管理解决方案，简化网站维护工作。</p><p>支持可视化编辑、模板系统、插件扩展、多语言等功能。</p>', NULL, NULL, '/uploads/image/products/content-management-system.png', 'fas fa-edit', NULL, '25,000起', NULL, 'in_stock', NULL, NULL, NULL, NULL, '0', '1', '0', '0', '0', '1', '2025-06-11 00:29:42', '2025-06-11 01:21:03'),
('7', '3', '企业移动APP', 'enterprise-mobile-app', '定制化企业移动应用开发，支持iOS和Android双平台。', '<p>专业的移动应用开发服务，为企业打造专属的移动端解决方案。</p><p>支持原生开发、混合开发、跨平台开发等多种技术方案。</p>', NULL, NULL, '/uploads/image/products/enterprise-mobile-app.jpg', 'fas fa-mobile-alt', NULL, '60,000起', NULL, 'in_stock', NULL, NULL, NULL, NULL, '0', '1', '0', '1', '0', '1', '2025-06-11 00:29:42', '2025-06-11 01:21:03'),
('8', '3', '微信小程序开发', 'wechat-miniprogram', '专业的微信小程序开发服务，快速上线，功能丰富。', '<p>基于微信生态的小程序开发，帮助企业快速获取微信用户。</p><p>支持商城、服务预约、会员管理、营销活动等多种应用场景。</p>', NULL, NULL, '/uploads/image/products/wechat-miniprogram.jpg', 'fab fa-weixin', NULL, '20,000起', NULL, 'in_stock', NULL, NULL, NULL, NULL, '0', '1', '0', '0', '1', '0', '2025-06-11 00:29:42', '2025-06-11 01:21:03'),
('9', '3', 'APP UI/UX设计', 'app-ui-ux-design', '专业的移动应用界面设计服务，注重用户体验和视觉效果。', '<p>由资深设计师团队提供的专业UI/UX设计服务。</p><p>包含用户研究、交互设计、视觉设计、原型制作等完整流程。</p>', NULL, NULL, '/uploads/image/products/app-ui-ux-design.jpg', 'fas fa-palette', NULL, '15,000起', NULL, 'in_stock', NULL, NULL, NULL, NULL, '0', '1', '1', '0', '0', '1', '2025-06-11 00:29:42', '2025-06-11 01:22:51'),
('10', '4', '云服务器部署', 'cloud-server-deployment', '专业的云服务器部署和运维服务，确保系统稳定高效运行。', '<p>提供完整的云基础设施部署和管理服务。</p><p>包含服务器配置、安全加固、监控告警、备份恢复等服务。</p>', NULL, NULL, '/uploads/image/products/cloud-server-deployment.jpg', 'fas fa-cloud', NULL, '8,000起', NULL, 'in_stock', NULL, NULL, NULL, NULL, '0', '1', '0', '0', '1', '0', '2025-06-11 00:29:42', '2025-06-11 01:21:03'),
('11', '4', '数据库优化服务', 'database-optimization', '专业的数据库性能优化服务，提升系统响应速度和稳定性。', '<p>由数据库专家提供的专业优化服务，解决性能瓶颈问题。</p><p>包含查询优化、索引设计、架构调整、监控配置等服务。</p>', NULL, NULL, '/uploads/image/products/database-optimization.jpg', 'fas fa-database', NULL, '12,000起', NULL, 'in_stock', NULL, NULL, NULL, NULL, '0', '1', '0', '0', '0', '0', '2025-06-11 00:29:42', '2025-06-11 01:21:03'),
('12', '4', '系统安全加固', 'system-security-hardening', '全面的系统安全加固服务，保护企业数据和系统安全。', '<p>专业的网络安全服务，为企业提供全方位的安全保护。</p><p>包含漏洞扫描、安全配置、防火墙设置、入侵检测等服务。</p>', NULL, NULL, '/uploads/image/products/system-security-hardening.jpg', 'fas fa-shield-alt', NULL, '18,000起', NULL, 'in_stock', NULL, NULL, NULL, NULL, '0', '1', '0', '1', '0', '0', '2025-06-11 00:29:42', '2025-06-11 01:21:03');

-- 表结构: site_settings
DROP TABLE IF EXISTS `site_settings`;
CREATE TABLE `site_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` longtext,
  `setting_type` enum('text','textarea','number','boolean','json') DEFAULT 'text',
  `description` varchar(200) DEFAULT NULL,
  `group_name` varchar(50) DEFAULT 'general',
  `sort_order` int(11) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4;

-- 表数据: site_settings (19 条记录)
INSERT INTO `site_settings` (`id`, `setting_key`, `setting_value`, `setting_type`, `description`, `group_name`, `sort_order`, `created_at`, `updated_at`) VALUES
('1', 'site_name', '企业官网', 'text', '网站名称', 'basic', '0', '2025-05-30 02:54:17', '2025-06-05 21:39:08'),
('2', 'site_title', '专业的企业解决方案提供商', 'text', '网站标题', 'basic', '0', '2025-05-30 02:54:17', '2025-06-05 21:39:08'),
('3', 'site_description', '我们提供专业的企业级解决方案', 'textarea', '网站描述', 'basic', '0', '2025-05-30 02:54:17', '2025-06-05 21:39:08'),
('4', 'site_keywords', '企业官网,解决方案,技术服务', 'text', '网站关键词', 'basic', '0', '2025-05-30 02:54:17', '2025-06-05 21:39:08'),
('5', 'company_name', '三只鱼科技有限公司', 'text', '公司名称', 'contact', '0', '2025-05-30 02:54:17', '2025-06-03 21:35:56'),
('6', 'company_address', '北京市朝阳区科技园区', 'text', '公司地址', 'contact', '0', '2025-05-30 02:54:17', '2025-06-03 21:35:56'),
('7', 'company_phone', '************', 'text', '联系电话', 'contact', '0', '2025-05-30 02:54:17', '2025-06-03 21:35:56'),
('8', 'company_email', '<EMAIL>', 'text', '联系邮箱', 'contact', '0', '2025-05-30 02:54:17', '2025-06-03 21:35:56'),
('9', 'company_qq', '*********', 'text', 'QQ号码', 'contact', '0', '2025-05-30 02:54:17', '2025-06-03 21:35:56'),
('10', 'company_wechat', 'hsdj37', 'text', '微信号', 'contact', '0', '2025-05-30 02:54:17', '2025-06-03 21:35:56'),
('14', 'site_url', 'http://san.com', 'text', '网站URL', 'basic', '0', '2025-05-31 02:36:32', '2025-06-05 21:39:08'),
('15', 'meta_keywords', '', 'text', 'META关键词', 'seo', '0', '2025-05-31 02:36:32', '2025-06-02 09:02:51'),
('16', 'meta_description', '', 'textarea', 'META描述', 'seo', '0', '2025-05-31 02:36:32', '2025-06-02 09:02:51'),
('17', 'analytics_code', '', 'textarea', '统计代码', 'seo', '0', '2025-05-31 02:36:32', '2025-06-02 09:02:51'),
('18', 'baidu_verify', '', 'text', '百度验证码', 'seo', '0', '2025-05-31 02:36:32', '2025-06-02 09:02:51'),
('19', 'max_file_size', '5', 'number', '最大文件上传大小(MB)', 'system', '0', '2025-05-31 02:36:32', '2025-06-03 21:08:54'),
('20', 'items_per_page', '10', 'number', '每页显示条数', 'system', '0', '2025-05-31 02:36:32', '2025-06-03 21:08:54'),
('21', 'cache_enabled', '1', 'boolean', '启用缓存功能', 'system', '0', '2025-05-31 02:36:32', '2025-06-03 21:08:54'),
('22', 'cache_time', '3600', 'number', '缓存时间(秒)', 'system', '0', '2025-05-31 02:36:32', '2025-06-03 21:08:54');

-- 表结构: solutions
DROP TABLE IF EXISTS `solutions`;
CREATE TABLE `solutions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(200) NOT NULL,
  `slug` varchar(200) NOT NULL,
  `short_description` varchar(500) DEFAULT NULL,
  `description` longtext,
  `icon` varchar(200) DEFAULT NULL,
  `image` varchar(500) DEFAULT NULL,
  `features` longtext,
  `sort_order` int(11) DEFAULT '0',
  `status` tinyint(1) DEFAULT '1',
  `views` int(11) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4;

-- 表结构: team_members
DROP TABLE IF EXISTS `team_members`;
CREATE TABLE `team_members` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `position` varchar(100) NOT NULL,
  `department` varchar(50) DEFAULT NULL,
  `bio` text,
  `photo` varchar(500) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `social_links` text,
  `sort_order` int(11) DEFAULT '0',
  `status` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * QiyeDIY企业建站系统 - API文档控制器
 */

declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use think\Request;
use think\Response;
use think\facade\Route;

/**
 * API文档控制器
 */
class ApiDocController extends BaseController
{
    /**
     * 获取API文档
     * @return Response
     */
    public function index(): Response
    {
        try {
            $swagger = $this->generateSwaggerDoc();
            
            return $this->success('获取成功', $swagger);
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 生成Swagger文档
     * @return array
     */
    private function generateSwaggerDoc(): array
    {
        $swagger = [
            'openapi' => '3.0.0',
            'info' => [
                'title' => 'QiyeDIY企业建站系统 API',
                'description' => '企业级DIY建站系统的RESTful API接口文档',
                'version' => '1.0.0',
                'contact' => [
                    'name' => '三只鱼网络科技',
                    'email' => '<EMAIL>',
                    'url' => 'https://www.qiyediy.com'
                ],
                'license' => [
                    'name' => 'MIT',
                    'url' => 'https://opensource.org/licenses/MIT'
                ]
            ],
            'servers' => [
                [
                    'url' => 'http://localhost:8000/api',
                    'description' => '开发环境'
                ],
                [
                    'url' => 'https://api.qiyediy.com',
                    'description' => '生产环境'
                ]
            ],
            'paths' => $this->generatePaths(),
            'components' => $this->generateComponents()
        ];

        return $swagger;
    }

    /**
     * 生成API路径
     * @return array
     */
    private function generatePaths(): array
    {
        return [
            // 用户认证
            '/auth/login' => [
                'post' => [
                    'tags' => ['认证'],
                    'summary' => '用户登录',
                    'description' => '用户登录获取访问令牌',
                    'requestBody' => [
                        'required' => true,
                        'content' => [
                            'application/json' => [
                                'schema' => [
                                    'type' => 'object',
                                    'required' => ['username', 'password'],
                                    'properties' => [
                                        'username' => [
                                            'type' => 'string',
                                            'description' => '用户名或邮箱'
                                        ],
                                        'password' => [
                                            'type' => 'string',
                                            'description' => '密码'
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'responses' => [
                        '200' => [
                            'description' => '登录成功',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        '$ref' => '#/components/schemas/LoginResponse'
                                    ]
                                ]
                            ]
                        ],
                        '401' => [
                            'description' => '认证失败',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        '$ref' => '#/components/schemas/ErrorResponse'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],

            // DIY页面管理
            '/diy/pages' => [
                'get' => [
                    'tags' => ['DIY页面'],
                    'summary' => '获取页面列表',
                    'description' => '分页获取DIY页面列表',
                    'security' => [['bearerAuth' => []]],
                    'parameters' => [
                        [
                            'name' => 'page',
                            'in' => 'query',
                            'description' => '页码',
                            'schema' => ['type' => 'integer', 'default' => 1]
                        ],
                        [
                            'name' => 'per_page',
                            'in' => 'query',
                            'description' => '每页数量',
                            'schema' => ['type' => 'integer', 'default' => 20]
                        ],
                        [
                            'name' => 'keyword',
                            'in' => 'query',
                            'description' => '搜索关键词',
                            'schema' => ['type' => 'string']
                        ]
                    ],
                    'responses' => [
                        '200' => [
                            'description' => '获取成功',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        '$ref' => '#/components/schemas/PageListResponse'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                'post' => [
                    'tags' => ['DIY页面'],
                    'summary' => '创建页面',
                    'description' => '创建新的DIY页面',
                    'security' => [['bearerAuth' => []]],
                    'requestBody' => [
                        'required' => true,
                        'content' => [
                            'application/json' => [
                                'schema' => [
                                    '$ref' => '#/components/schemas/PageCreateRequest'
                                ]
                            ]
                        ]
                    ],
                    'responses' => [
                        '201' => [
                            'description' => '创建成功',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        '$ref' => '#/components/schemas/PageResponse'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],

            '/diy/pages/{id}' => [
                'get' => [
                    'tags' => ['DIY页面'],
                    'summary' => '获取页面详情',
                    'description' => '根据ID获取页面详细信息',
                    'security' => [['bearerAuth' => []]],
                    'parameters' => [
                        [
                            'name' => 'id',
                            'in' => 'path',
                            'required' => true,
                            'description' => '页面ID',
                            'schema' => ['type' => 'integer']
                        ]
                    ],
                    'responses' => [
                        '200' => [
                            'description' => '获取成功',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        '$ref' => '#/components/schemas/PageResponse'
                                    ]
                                ]
                            ]
                        ],
                        '404' => [
                            'description' => '页面不存在',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        '$ref' => '#/components/schemas/ErrorResponse'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                'put' => [
                    'tags' => ['DIY页面'],
                    'summary' => '更新页面',
                    'description' => '更新页面信息和内容',
                    'security' => [['bearerAuth' => []]],
                    'parameters' => [
                        [
                            'name' => 'id',
                            'in' => 'path',
                            'required' => true,
                            'description' => '页面ID',
                            'schema' => ['type' => 'integer']
                        ]
                    ],
                    'requestBody' => [
                        'required' => true,
                        'content' => [
                            'application/json' => [
                                'schema' => [
                                    '$ref' => '#/components/schemas/PageUpdateRequest'
                                ]
                            ]
                        ]
                    ],
                    'responses' => [
                        '200' => [
                            'description' => '更新成功',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        '$ref' => '#/components/schemas/PageResponse'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                'delete' => [
                    'tags' => ['DIY页面'],
                    'summary' => '删除页面',
                    'description' => '删除指定页面',
                    'security' => [['bearerAuth' => []]],
                    'parameters' => [
                        [
                            'name' => 'id',
                            'in' => 'path',
                            'required' => true,
                            'description' => '页面ID',
                            'schema' => ['type' => 'integer']
                        ]
                    ],
                    'responses' => [
                        '200' => [
                            'description' => '删除成功',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        '$ref' => '#/components/schemas/SuccessResponse'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],

            // 文件上传
            '/upload/image' => [
                'post' => [
                    'tags' => ['文件上传'],
                    'summary' => '上传图片',
                    'description' => '上传图片文件',
                    'security' => [['bearerAuth' => []]],
                    'requestBody' => [
                        'required' => true,
                        'content' => [
                            'multipart/form-data' => [
                                'schema' => [
                                    'type' => 'object',
                                    'properties' => [
                                        'file' => [
                                            'type' => 'string',
                                            'format' => 'binary',
                                            'description' => '图片文件'
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'responses' => [
                        '200' => [
                            'description' => '上传成功',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        '$ref' => '#/components/schemas/UploadResponse'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * 生成组件定义
     * @return array
     */
    private function generateComponents(): array
    {
        return [
            'securitySchemes' => [
                'bearerAuth' => [
                    'type' => 'http',
                    'scheme' => 'bearer',
                    'bearerFormat' => 'JWT'
                ]
            ],
            'schemas' => [
                'SuccessResponse' => [
                    'type' => 'object',
                    'properties' => [
                        'code' => ['type' => 'integer', 'example' => 200],
                        'message' => ['type' => 'string', 'example' => '操作成功'],
                        'data' => ['type' => 'object']
                    ]
                ],
                'ErrorResponse' => [
                    'type' => 'object',
                    'properties' => [
                        'code' => ['type' => 'integer', 'example' => 400],
                        'message' => ['type' => 'string', 'example' => '请求失败'],
                        'data' => ['type' => 'object']
                    ]
                ],
                'LoginResponse' => [
                    'type' => 'object',
                    'properties' => [
                        'code' => ['type' => 'integer', 'example' => 200],
                        'message' => ['type' => 'string', 'example' => '登录成功'],
                        'data' => [
                            'type' => 'object',
                            'properties' => [
                                'token' => ['type' => 'string', 'description' => '访问令牌'],
                                'user' => ['$ref' => '#/components/schemas/User']
                            ]
                        ]
                    ]
                ],
                'User' => [
                    'type' => 'object',
                    'properties' => [
                        'id' => ['type' => 'integer', 'description' => '用户ID'],
                        'username' => ['type' => 'string', 'description' => '用户名'],
                        'email' => ['type' => 'string', 'description' => '邮箱'],
                        'nickname' => ['type' => 'string', 'description' => '昵称'],
                        'avatar' => ['type' => 'string', 'description' => '头像URL'],
                        'role' => ['type' => 'string', 'description' => '角色'],
                        'created_at' => ['type' => 'string', 'format' => 'date-time']
                    ]
                ],
                'PageListResponse' => [
                    'type' => 'object',
                    'properties' => [
                        'code' => ['type' => 'integer', 'example' => 200],
                        'message' => ['type' => 'string', 'example' => '获取成功'],
                        'data' => [
                            'type' => 'object',
                            'properties' => [
                                'data' => [
                                    'type' => 'array',
                                    'items' => ['$ref' => '#/components/schemas/Page']
                                ],
                                'pagination' => ['$ref' => '#/components/schemas/Pagination']
                            ]
                        ]
                    ]
                ],
                'Page' => [
                    'type' => 'object',
                    'properties' => [
                        'id' => ['type' => 'integer', 'description' => '页面ID'],
                        'title' => ['type' => 'string', 'description' => '页面标题'],
                        'slug' => ['type' => 'string', 'description' => '页面路径'],
                        'description' => ['type' => 'string', 'description' => '页面描述'],
                        'is_published' => ['type' => 'boolean', 'description' => '是否发布'],
                        'config' => ['type' => 'object', 'description' => '页面配置'],
                        'components' => ['type' => 'array', 'description' => '页面组件'],
                        'created_at' => ['type' => 'string', 'format' => 'date-time'],
                        'updated_at' => ['type' => 'string', 'format' => 'date-time']
                    ]
                ],
                'Pagination' => [
                    'type' => 'object',
                    'properties' => [
                        'current_page' => ['type' => 'integer', 'description' => '当前页码'],
                        'per_page' => ['type' => 'integer', 'description' => '每页数量'],
                        'total' => ['type' => 'integer', 'description' => '总记录数'],
                        'last_page' => ['type' => 'integer', 'description' => '最后页码']
                    ]
                ],
                'PageCreateRequest' => [
                    'type' => 'object',
                    'required' => ['title'],
                    'properties' => [
                        'title' => ['type' => 'string', 'description' => '页面标题'],
                        'slug' => ['type' => 'string', 'description' => '页面路径'],
                        'description' => ['type' => 'string', 'description' => '页面描述'],
                        'template_id' => ['type' => 'integer', 'description' => '模板ID']
                    ]
                ],
                'PageUpdateRequest' => [
                    'type' => 'object',
                    'properties' => [
                        'title' => ['type' => 'string', 'description' => '页面标题'],
                        'slug' => ['type' => 'string', 'description' => '页面路径'],
                        'description' => ['type' => 'string', 'description' => '页面描述'],
                        'is_published' => ['type' => 'boolean', 'description' => '是否发布'],
                        'config' => ['type' => 'object', 'description' => '页面配置'],
                        'components' => ['type' => 'array', 'description' => '页面组件']
                    ]
                ],
                'PageResponse' => [
                    'type' => 'object',
                    'properties' => [
                        'code' => ['type' => 'integer', 'example' => 200],
                        'message' => ['type' => 'string', 'example' => '获取成功'],
                        'data' => ['$ref' => '#/components/schemas/Page']
                    ]
                ],
                'UploadResponse' => [
                    'type' => 'object',
                    'properties' => [
                        'code' => ['type' => 'integer', 'example' => 200],
                        'message' => ['type' => 'string', 'example' => '上传成功'],
                        'data' => [
                            'type' => 'object',
                            'properties' => [
                                'url' => ['type' => 'string', 'description' => '文件URL'],
                                'filename' => ['type' => 'string', 'description' => '文件名'],
                                'size' => ['type' => 'integer', 'description' => '文件大小'],
                                'type' => ['type' => 'string', 'description' => '文件类型']
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * 获取API文档HTML页面
     * @return string
     */
    public function docs(): string
    {
        $html = '<!DOCTYPE html>
<html>
<head>
    <title>QiyeDIY API文档</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui.css" />
    <style>
        html { box-sizing: border-box; overflow: -moz-scrollbars-vertical; overflow-y: scroll; }
        *, *:before, *:after { box-sizing: inherit; }
        body { margin:0; background: #fafafa; }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                url: "/api/docs/json",
                dom_id: "#swagger-ui",
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout"
            });
        };
    </script>
</body>
</html>';

        return $html;
    }

    /**
     * 获取JSON格式的API文档
     * @return Response
     */
    public function json(): Response
    {
        try {
            $swagger = $this->generateSwaggerDoc();
            
            return response()->json($swagger);
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}

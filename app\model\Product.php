<?php
/**
 * 三只鱼网络科技 | 开发者：韩总 | 创建时间：2024-12-19
 * 文件描述：产品模型 - ThinkPHP6企业级应用
 * 技术栈：PHP 8.0+ + ThinkPHP6 + MySQL + Redis
 * 版权所有：三只鱼网络科技有限公司
 */

namespace app\model;

use think\Model;

/**
 * 产品模型
 */
class Product extends Model
{
    // 数据表名
    protected $name = 'products';

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    // 字段类型转换
    protected $type = [
        'category_id' => 'integer',
        'status' => 'integer',
        'views' => 'integer',
        'is_featured' => 'integer',
        'is_hot' => 'integer',
        'is_new' => 'integer',
        'sort_order' => 'integer',
        'features' => 'json',
        'specifications' => 'json',
        'gallery' => 'json',
    ];

    // 可填充字段
    protected $fillable = [
        'category_id', 'name', 'slug', 'short_description', 'description',
        'features', 'specifications', 'image', 'icon', 'gallery', 'price',
        'original_price', 'stock_status', 'sku', 'tags', 'meta_title',
        'meta_description', 'sort_order', 'status', 'views', 'is_featured',
        'is_hot', 'is_new'
    ];

    // 关联产品分类
    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'category_id');
    }

    // 关联产品属性
    public function attributes()
    {
        return $this->hasMany(ProductAttribute::class, 'product_id');
    }

    // 获取格式化的创建时间
    public function getCreatedAtTextAttr($value, $data)
    {
        return date('Y-m-d H:i', strtotime($data['created_at']));
    }

    // 获取库存状态文本
    public function getStockStatusTextAttr($value, $data)
    {
        $statusMap = [
            'in_stock' => '现货',
            'out_of_stock' => '缺货',
            'pre_order' => '预订'
        ];
        $stockStatus = $data['stock_status'] ?? 'in_stock';  // 默认为现货
        return $statusMap[$stockStatus] ?? '现货';
    }

    // 获取产品特性数组
    public function getFeaturesArrayAttr($value, $data)
    {
        if (empty($data['features'])) {
            return [];
        }
        $result = is_string($data['features']) ? json_decode($data['features'], true) : $data['features'];
        return is_array($result) ? $result : [];
    }

    // 获取产品规格数组
    public function getSpecificationsArrayAttr($value, $data)
    {
        if (empty($data['specifications'])) {
            return [];
        }
        $result = is_string($data['specifications']) ? json_decode($data['specifications'], true) : $data['specifications'];
        return is_array($result) ? $result : [];
    }

    // 获取产品图册数组
    public function getGalleryArrayAttr($value, $data)
    {
        if (empty($data['gallery'])) {
            return [];
        }
        $result = is_string($data['gallery']) ? json_decode($data['gallery'], true) : $data['gallery'];
        return is_array($result) ? $result : [];
    }

    // 生成URL别名
    public static function generateSlug($title)
    {
        if (empty($title)) {
            return '';
        }
        
        $result = '';
        
        // 遍历每个字符
        for ($i = 0; $i < strlen($title); $i++) {
            $char = $title[$i];
            
            // 如果是英文字母或数字，直接保留
            if (preg_match('/[a-zA-Z0-9]/', $char)) {
                $result .= strtolower($char);
            }
            // 如果是空格或其他分隔符，转换为连字符
            elseif (preg_match('/[\s\-_]/', $char)) {
                $result .= '-';
            }
        }
        
        // 清理结果
        $result = preg_replace('/[-]+/', '-', $result);  // 多个连字符合并为一个
        $result = trim($result, '-'); // 去除首尾连字符
        $result = substr($result, 0, 50); // 限制长度
        
        // 如果结果为空或太短，使用时间戳
        if (empty($result) || strlen($result) < 2) {
            $result = 'product-' . date('YmdHi');
        }
        
        return $result;
    }

    // 检查slug唯一性
    public static function checkSlugUnique($slug, $excludeId = null)
    {
        $query = self::where('slug', $slug);
        if ($excludeId) {
            $query->where('id', '<>', $excludeId);
        }
        return !$query->find();
    }

    // 获取启用的产品
    public static function getActiveProducts($limit = null)
    {
        $query = self::where('status', 1)
            ->order('sort_order', 'desc')
            ->order('id', 'desc');
            
        if ($limit) {
            $query->limit($limit);
        }
        
        return $query->select();
    }

    // 获取推荐产品
    public static function getFeaturedProducts($limit = 6)
    {
        return self::where('status', 1)
            ->where('is_featured', 1)
            ->order('sort_order', 'desc')
            ->order('id', 'desc')
            ->limit($limit)
            ->select();
    }

    // 获取热门产品
    public static function getHotProducts($limit = 6)
    {
        return self::where('status', 1)
            ->where('is_hot', 1)
            ->order('views', 'desc')
            ->order('sort_order', 'desc')
            ->limit($limit)
            ->select();
    }

    // 获取新品
    public static function getNewProducts($limit = 6)
    {
        return self::where('status', 1)
            ->where('is_new', 1)
            ->order('created_at', 'desc')
            ->order('sort_order', 'desc')
            ->limit($limit)
            ->select();
    }

    // 增加浏览次数
    public function incrementViews()
    {
        $this->views++;
        $this->save();
    }

    // 获取相关产品
    public function getRelatedProducts($limit = 4)
    {
        return self::where('status', 1)
            ->where('category_id', $this->category_id)
            ->where('id', '<>', $this->id)
            ->order('sort_order', 'desc')
            ->order('id', 'desc')
            ->limit($limit)
            ->select();
    }
}

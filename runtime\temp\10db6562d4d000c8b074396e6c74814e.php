<?php /*a:7:{s:60:"D:\EServer\core\www\san.com\app\view\admin\page_builder.html";i:1749600607;s:58:"D:\EServer\core\www\san.com\app\view\admin\common\css.html";i:1749480866;s:61:"D:\EServer\core\www\san.com\app\view\admin\common\header.html";i:1749773235;s:62:"D:\EServer\core\www\san.com\app\view\admin\common\sidebar.html";i:1749580694;s:62:"D:\EServer\core\www\san.com\app\view\admin\common\message.html";i:1749486569;s:69:"D:\EServer\core\www\san.com\app\view\admin\common\message-styles.html";i:1749487940;s:57:"D:\EServer\core\www\san.com\app\view\admin\common\js.html";i:1749679067;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面装修 - 三只鱼网络</title>
    
    <!-- 引用公共CSS -->
    <link rel="stylesheet" href="/assets/css/bootstrap.min.css">
<link rel="stylesheet" href="/assets/css/all.min.css">
<link rel="stylesheet" href="/assets/css/admin.css">
<link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    
    <!-- 页面装修专用CSS -->
    <link rel="stylesheet" href="/assets/css/admin/page-builder.css">
</head>
<body>
    <!-- 顶部导航 -->
    <!-- 顶部导航 -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <div class="navbar-left">
            <button class="btn btn-link d-md-none" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
            <a class="navbar-brand" href="/admin">
                <i class="fas fa-cogs"></i>
                <span>后台管理</span>
            </a>
        </div>

        <div class="navbar-nav">
            <a class="nav-link" href="/" target="_blank" style="margin-right: 15px;">
                <i class="fas fa-external-link-alt"></i>
                <span>查看网站</span>
            </a>
            <a class="nav-link" href="javascript:void(0);" onclick="clearAllCache()" style="margin-right: 15px; color: #28a745;">
                <i class="fas fa-broom"></i>
                <span>清理缓存</span>
            </a>
            <span class="nav-link" style="margin-right: 15px; color: rgba(255,255,255,0.8);">
                <i class="fas fa-user"></i>
                <span>
                    <?php if(isset($admin_info['real_name']) && $admin_info['real_name']): ?>
                        <?php echo htmlentities((string) $admin_info['real_name']); elseif(isset($admin_info['username']) && $admin_info['username']): ?>
                        <?php echo htmlentities((string) $admin_info['username']); else: ?>
                        系统管理员
                    <?php endif; ?>
                </span>
            </span>
            <a class="nav-link" href="javascript:void(0);" onclick="confirmLogout()" style="color: #ff6b6b;">
                <i class="fas fa-sign-out-alt"></i>
                <span>退出登录</span>
            </a>
        </div>
    </div>
</nav>

<script>
// 退出登录确认对话框
function confirmLogout() {
    // 创建自定义确认对话框
    const modal = document.createElement('div');
    modal.className = 'logout-modal-overlay';
    modal.innerHTML = `
        <div class="logout-modal">
            <div class="logout-modal-header">
                <i class="fas fa-sign-out-alt"></i>
                <h4>确认退出登录</h4>
            </div>
            <div class="logout-modal-body">
                <p>您确定要退出登录吗？</p>
            </div>
            <div class="logout-modal-footer">
                <button class="btn-cancel" onclick="closeLogoutModal()">
                    <i class="fas fa-times"></i> 取消
                </button>
                <button class="btn-confirm" onclick="doLogout()">
                    <i class="fas fa-check"></i> 确认退出
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 添加动画效果
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
}

function closeLogoutModal() {
    const modal = document.querySelector('.logout-modal-overlay');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

function doLogout() {
    window.location.href = '/admin/logout';
}

// 点击遮罩层关闭对话框
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('logout-modal-overlay')) {
        closeLogoutModal();
    }
});


</script>

    
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <!-- 侧边栏 -->
<nav class="col-md-3 col-lg-2 sidebar">
    <div class="sidebar-sticky">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="/admin" data-controller="Index">
                    <i class="fas fa-tachometer-alt"></i> 仪表盘
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/banners" data-controller="Banners">
                    <i class="fas fa-images"></i> 轮播图管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/products" data-controller="Products">
                    <i class="fas fa-cube"></i> 产品管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/solutions" data-controller="Solutions">
                    <i class="fas fa-lightbulb"></i> 解决方案
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/news" data-controller="News">
                    <i class="fas fa-newspaper"></i> 新闻管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/cases" data-controller="Cases">
                    <i class="fas fa-briefcase"></i> 客户案例
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/contacts" data-controller="Contacts">
                    <i class="fas fa-envelope"></i> 联系表单
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/page-builder" data-controller="PageBuilder">
                    <i class="fas fa-paint-brush"></i> 页面装修
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/sys-menu" data-controller="SysMenu">
                    <i class="fas fa-sitemap"></i> 系统菜单
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/settings" data-controller="Settings">
                    <i class="fas fa-cog"></i> 系统设置
                </a>
            </li>
        </ul>
    </div>
</nav>

<!-- 移动端遮罩层 -->
<div class="sidebar-overlay d-md-none"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取当前URL路径
    const currentPath = window.location.pathname;
    console.log('当前路径:', currentPath);

    // 移除所有active状态
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });

    // 根据URL路径设置active状态
    if (currentPath === '/admin' || currentPath === '/admin/' || currentPath.endsWith('/admin/index')) {
        const indexLink = document.querySelector('[data-controller="Index"]');
        if (indexLink) {
            indexLink.classList.add('active');
            console.log('设置仪表盘为active');
        }
    } else if (currentPath.includes('/admin/banners')) {
        const bannersLink = document.querySelector('[data-controller="Banners"]');
        if (bannersLink) {
            bannersLink.classList.add('active');
            console.log('设置轮播图为active');
        }
    } else if (currentPath.includes('/admin/contacts')) {
        const contactsLink = document.querySelector('[data-controller="Contacts"]');
        if (contactsLink) {
            contactsLink.classList.add('active');
            console.log('设置联系表单为active');
        }
    } else if (currentPath.includes('/admin/products')) {
        const productsLink = document.querySelector('[data-controller="Products"]');
        if (productsLink) {
            productsLink.classList.add('active');
            console.log('设置产品管理为active');
        }
    } else if (currentPath.includes('/admin/solutions')) {
        const solutionsLink = document.querySelector('[data-controller="Solutions"]');
        if (solutionsLink) {
            solutionsLink.classList.add('active');
            console.log('设置解决方案为active');
        }
    } else if (currentPath.includes('/admin/news')) {
        const newsLink = document.querySelector('[data-controller="News"]');
        if (newsLink) {
            newsLink.classList.add('active');
            console.log('设置新闻管理为active');
        }
    } else if (currentPath.includes('/admin/cases')) {
        const casesLink = document.querySelector('[data-controller="Cases"]');
        if (casesLink) {
            casesLink.classList.add('active');
            console.log('设置客户案例为active');
        }
    } else if (currentPath.includes('/admin/page-builder')) {
        const pageBuilderLink = document.querySelector('[data-controller="PageBuilder"]');
        if (pageBuilderLink) {
            pageBuilderLink.classList.add('active');
            console.log('设置页面装修为active');
        }
    } else if (currentPath.includes('/admin/sys-menu')) {
        const sysMenuLink = document.querySelector('[data-controller="SysMenu"]');
        if (sysMenuLink) {
            sysMenuLink.classList.add('active');
            console.log('设置系统菜单为active');
        }
    } else if (currentPath.includes('/admin/settings')) {
        const settingsLink = document.querySelector('[data-controller="Settings"]');
        if (settingsLink) {
            settingsLink.classList.add('active');
            console.log('设置系统设置为active');
        }
    }
});
</script>


            <!-- 主要内容 -->
            <main class="main-content">
                <!-- 引用统一消息组件 -->
                <!-- 消息提示组件 -->
<style>
/**
 * 消息提示样式 - 三只鱼网络科技 | 韩总 | 2024-12-19
 * 简洁统一的消息提示系统 - ThinkPHP6企业级应用
 */

/* 消息提示基础样式 - 后台深色科技风完美居中 */
.alert {
    position: fixed !important;
    top: 80px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    padding: 16px 24px !important;
    margin: 0 !important;
    border-radius: 12px !important;
    display: flex !important;
    align-items: center !important;
    gap: 14px !important;
    backdrop-filter: blur(25px) !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.6),
        0 2px 8px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    animation: slideInDown 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    z-index: 99999 !important;
    min-width: 340px !important;
    max-width: 520px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    font-family: 'Rajdhani', 'Microsoft YaHei', sans-serif !important;
    letter-spacing: 0.3px !important;
    /* 确保在所有容器中都能完美居中 */
    margin-left: auto !important;
    margin-right: auto !important;
}

/* 消息图标 - 科技发光效果 */
.alert-icon {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 28px !important;
    height: 28px !important;
    border-radius: 50% !important;
    flex-shrink: 0 !important;
    font-size: 15px !important;
    position: relative !important;
    transition: all 0.3s ease !important;
}

.alert-icon::before {
    content: '' !important;
    position: absolute !important;
    top: -2px !important;
    left: -2px !important;
    right: -2px !important;
    bottom: -2px !important;
    border-radius: 50% !important;
    background: inherit !important;
    opacity: 0.3 !important;
    animation: pulse 2s infinite !important;
}

/* 消息内容 */
.alert-content {
    flex: 1 !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* 成功提示 - 按钮式紫色渐变风格 */
.alert-success {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8) 0%,
        rgba(255, 119, 198, 0.6) 50%,
        rgba(120, 219, 255, 0.7) 100%) !important;
    border: 1px solid rgba(120, 119, 198, 0.6) !important;
    color: #ffffff !important;
    box-shadow: 0 4px 15px rgba(120, 119, 198, 0.3) !important;
}

.alert-success .alert-icon {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.9),
        rgba(240, 240, 255, 1)) !important;
    color: rgba(120, 119, 198, 0.9) !important;
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.4) !important;
}

/* 错误提示 - 后台深色科技风 */
.alert-danger {
    background: linear-gradient(135deg,
        rgba(15, 15, 15, 0.95) 0%,
        rgba(45, 25, 25, 0.95) 100%) !important;
    border: 1px solid rgba(255, 119, 119, 0.4) !important;
    color: #ffb3b3 !important;
}

.alert-danger .alert-icon {
    background: linear-gradient(135deg,
        rgba(255, 119, 119, 0.9),
        rgba(220, 80, 80, 1)) !important;
    color: #ffffff !important;
    box-shadow: 0 0 15px rgba(255, 119, 119, 0.5) !important;
}

/* 警告提示 - 后台深色科技风 */
.alert-warning {
    background: linear-gradient(135deg,
        rgba(15, 15, 15, 0.95) 0%,
        rgba(45, 35, 15, 0.95) 100%) !important;
    border: 1px solid rgba(255, 193, 7, 0.4) !important;
    color: #ffd54f !important;
}

.alert-warning .alert-icon {
    background: linear-gradient(135deg,
        rgba(255, 193, 7, 0.9),
        rgba(255, 152, 0, 1)) !important;
    color: #ffffff !important;
    box-shadow: 0 0 15px rgba(255, 193, 7, 0.5) !important;
}

/* 信息提示 - 后台深色科技风 */
.alert-info {
    background: linear-gradient(135deg,
        rgba(15, 15, 15, 0.95) 0%,
        rgba(25, 25, 45, 0.95) 100%) !important;
    border: 1px solid rgba(120, 119, 198, 0.4) !important;
    color: #b3b3ff !important;
}

.alert-info .alert-icon {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.9),
        rgba(120, 219, 255, 0.9)) !important;
    color: #ffffff !important;
    box-shadow: 0 0 15px rgba(120, 119, 198, 0.5) !important;
}

/* 动画效果 - 科技感流畅动画 */
@keyframes slideInDown {
    0% {
        transform: translateX(-50%) translateY(-100%);
        opacity: 0;
        filter: blur(4px);
    }
    50% {
        transform: translateX(-50%) translateY(-10px);
        opacity: 0.8;
        filter: blur(1px);
    }
    100% {
        transform: translateX(-50%) translateY(0);
        opacity: 1;
        filter: blur(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.15);
        opacity: 0.6;
    }
}

/* 悬停效果 - 科技感交互 */
.alert:hover {
    transform: translateX(-50%) translateY(-3px) !important;
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.7),
        0 4px 12px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

/* 响应式设计 - 移动端优化 */
@media (max-width: 768px) {
    .alert {
        min-width: 300px !important;
        max-width: 92vw !important;
        padding: 14px 18px !important;
        gap: 10px !important;
        font-size: 13px !important;
        border-radius: 10px !important;
        top: 60px !important;
        /* 移动端确保居中 */
        left: 50% !important;
        transform: translateX(-50%) !important;
    }

    .alert-icon {
        width: 24px !important;
        height: 24px !important;
        font-size: 13px !important;
    }

    .alert-icon::before {
        top: -1px !important;
        left: -1px !important;
        right: -1px !important;
        bottom: -1px !important;
    }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
    .alert {
        min-width: 280px !important;
        max-width: 95vw !important;
        padding: 12px 16px !important;
        gap: 8px !important;
        font-size: 12px !important;
        top: 50px !important;
        /* 超小屏幕确保居中 */
        left: 50% !important;
        transform: translateX(-50%) !important;
    }

    .alert-icon {
        width: 22px !important;
        height: 22px !important;
        font-size: 12px !important;
    }
}
</style> 
<?php if(isset($message) && $message): ?>
    <div class="alert alert-<?php echo $messageType=='error' ? 'danger'  :  ($messageType ?: 'info'); ?> alert-dismissible fade show" role="alert" id="alertMessage">
        <div class="alert-icon">
            <i class="fas fa-<?php echo $messageType=='success' ? 'check-circle'  :  ($messageType == 'error' || $messageType == 'danger' ? 'exclamation-circle' : ($messageType == 'warning' ? 'exclamation-triangle' : 'info-circle')); ?>"></i>
        </div>
        <div class="alert-content">
            <?php echo htmlentities((string) $message); ?>
        </div>
    </div>
<?php endif; ?>

<!-- 统一删除确认模态框 -->
<div class="delete-modal" id="deleteModal">
    <div class="modal-content">
        <div class="modal-header">
            <i class="fas fa-exclamation-triangle"></i>
            <h4>确认删除</h4>
        </div>
        <div class="modal-body">
            <p>您确定要删除以下项目吗？</p>
            <div class="delete-item-title" id="deleteItemTitle">
                <!-- 这里将显示要删除的项目标题 -->
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn-cancel" onclick="closeDeleteModal()">
                <i class="fas fa-times"></i>
                取消
            </button>
            <button type="button" class="btn-confirm-delete" id="confirmDeleteBtn">
                <i class="fas fa-trash"></i>
                确认删除
            </button>
        </div>
    </div>
</div>

<script>
// 简化的消息处理 - 主要功能已移至admin.js
function showMessage(message, type = 'info') {
    const existingAlert = document.querySelector('.alert');
    if (existingAlert) existingAlert.remove();

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.id = 'alertMessage';
    alertDiv.innerHTML = `
        <div class="alert-icon">
            <i class="fas fa-${type === 'success' ? 'check-circle' : (type === 'danger' ? 'exclamation-circle' : (type === 'warning' ? 'exclamation-triangle' : 'info-circle'))}"></i>
        </div>
        <div class="alert-content">${message}</div>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv && alertDiv.parentElement) {
            alertDiv.style.transition = 'all 0.5s ease-out';
            alertDiv.style.opacity = '0';
            setTimeout(() => alertDiv.remove(), 500);
        }
    }, 1500);
}

function closeDeleteModal() {
    const modal = document.getElementById('deleteModal');
    if (modal) modal.classList.remove('show');
}

// 页面加载时自动消失消息
document.addEventListener('DOMContentLoaded', function() {
    const alertMessage = document.getElementById('alertMessage');
    if (alertMessage) setTimeout(() => alertMessage.remove(), 1500);
});
</script>

                <!-- 页面内容区域 -->
                <div class="content-body">
                    <div class="page-builder-container">

                    <?php if($action == 'list'): ?>
                        <!-- 模板列表视图 -->
                        <div class="list-header">
                            <div class="list-header-content">
                                <div class="list-title-section">
                                    <div class="list-icon">
                                        <i class="fas fa-paint-brush"></i>
                                    </div>
                                    <div>
                                        <h1 class="list-title">页面装修</h1>
                                        <p class="list-subtitle">可视化页面设计与模板管理</p>
                                    </div>
                                </div>
                                <div class="header-actions">
                                    <?php if(!empty($currentType) && $currentType != 'all'): ?>
                                        <a href="/admin/page-builder?action=add&preset_type=<?php echo htmlentities((string) $currentType); ?>" class="btn-add-custom">
                                            <i class="fas fa-plus"></i>
                                            <span>新建<?php echo htmlentities((string) (isset($templateTypes[$currentType]) && ($templateTypes[$currentType] !== '')?$templateTypes[$currentType]:'模板')); ?></span>
                                        </a>
                                    <?php else: ?>
                                        <a href="/admin/page-builder?action=add" class="btn-add-custom">
                                            <i class="fas fa-plus"></i>
                                            <span>新建模板</span>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- 类型筛选标签 -->
                        <div class="type-filter-tabs">
                            <div class="tabs-container1">
                                <a href="/admin/page-builder?type=all"
                                   class="tab-item <?php echo $currentType=='' || $currentType == 'all' ? 'active'  :  ''; ?>">
                                    <i class="fas fa-th-large"></i>
                                    <span>全部</span>
                                    <span class="count">(<?php echo htmlentities((string) (isset($typeStats['all']) && ($typeStats['all'] !== '')?$typeStats['all']:0)); ?>)</span>
                                </a>
                                <?php if(is_array($templateTypes) || $templateTypes instanceof \think\Collection || $templateTypes instanceof \think\Paginator): $typeKey = 0; $__LIST__ = $templateTypes;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$typeName): $mod = ($typeKey % 2 );++$typeKey;?>
                                <div class="tab-item-wrapper">
                                    <a href="/admin/page-builder?type=<?php echo htmlentities((string) $key); ?>"
                                       class="tab-item <?php echo $currentType==$key ? 'active'  :  ''; ?>">
                                        <?php switch($key): case "home": ?><i class="fas fa-home"></i><?php break; case "product": ?><i class="fas fa-box"></i><?php break; case "news": ?><i class="fas fa-newspaper"></i><?php break; case "case": ?><i class="fas fa-briefcase"></i><?php break; case "contact": ?><i class="fas fa-envelope"></i><?php break; case "about": ?><i class="fas fa-info-circle"></i><?php break; default: ?><i class="fas fa-file-alt"></i>
                                        <?php endswitch; ?>
                                        <span><?php echo htmlentities((string) $typeName); ?></span>
                                        <span class="count">(<?php echo htmlentities((string) (isset($typeStats[$key]) && ($typeStats[$key] !== '')?$typeStats[$key]:0)); ?>)</span>
                                    </a>
                                    <a href="/admin/page-builder?action=add&preset_type=<?php echo htmlentities((string) $key); ?>"
                                       class="add-type-btn" title="添加<?php echo htmlentities((string) $typeName); ?>模板">
                                        <i class="fas fa-plus"></i>
                                    </a>
                                </div>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </div>
                        </div>

                        <!-- 列表主体 -->
                        <div class="list-body">
                            <?php if($templatesList && count($templatesList) > 0): ?>
                                <!-- 模板列表 -->
                                <div class="templates-list">
                                    <?php if(is_array($templatesList) || $templatesList instanceof \think\Collection || $templatesList instanceof \think\Paginator): $key = 0; $__LIST__ = $templatesList;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$template): $mod = ($key % 2 );++$key;?>
                                    <div class="template-item">
                                        <div class="template-content">
                                            <!-- 缩略图 -->
                                            <div class="template-thumbnail">
                                                <div class="template-thumb-placeholder">
                                                    <i class="fas fa-paint-brush"></i>
                                                </div>
                                            </div>

                                            <!-- 模板信息 -->
                                            <div class="template-info">
                                                <div class="template-header">
                                                    <h3 class="template-name"><?php echo htmlentities((string) $template['name']); ?></h3>
                                                </div>

                                                <div class="template-meta">
                                                    <div class="meta-item">
                                                        <i class="fas fa-clock"></i>
                                                        <span><?php echo htmlentities((string) date('m-d H:i',!is_numeric($template['updated_at'])? strtotime($template['updated_at']) : $template['updated_at'])); ?></span>
                                                    </div>
                                                    <div class="meta-item">
                                                        <i class="fas fa-cube"></i>
                                                        <span>组件数: <?php echo htmlentities((string) (isset($template['component_stats']['total']) && ($template['component_stats']['total'] !== '')?$template['component_stats']['total']:0)); ?></span>
                                                    </div>
                                                </div>

                                                <?php if($template['description']): ?>
                                                    <div class="template-description">
                                                        <?php echo htmlentities((string) $template['description']); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <!-- 操作按钮 -->
                                            <div class="template-actions">
                                                <div class="template-badges">
                                                    <span class="badge badge-type">
                                                        <i class="fas fa-tag"></i>
                                                        <?php echo htmlentities((string) $template['type_text']); ?>
                                                    </span>
                                                </div>
                                                <div class="status-toggle">
                                                    <label class="switch">
                                                        <input type="checkbox"
                                                               <?php echo !empty($template['status']) ? 'checked'  :  ''; ?>
                                                               onchange="toggleStatus('<?php echo htmlentities((string) $template['id']); ?>', this.checked ? 1 : 0)">
                                                        <span class="slider"></span>
                                                    </label>
                                                    <span class="status-label"><?php echo !empty($template['status']) ? '已发布'  :  '草稿'; ?></span>
                                                </div>

                                                <div class="action-buttons">
                                                    <?php if(!empty($template['slug']) && $template['status']): ?>
                                                        <a href="/page/<?php echo htmlentities((string) $template['slug']); ?>"
                                                           class="btn-action btn-view" title="查看页面"
                                                           style="background: #17a2b8;" target="_blank">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <!-- <a href="/admin/page-designer?id=<?php echo htmlentities((string) $template['id']); ?>"
                                                       class="btn-action btn-design" title="GrapesJS设计器">
                                                        <i class="fas fa-paint-brush"></i>
                                                    </a> -->
                                                    <a href="/admin/page-designer/diy?id=<?php echo htmlentities((string) $template['id']); ?>"
                                                       class="btn-action btn-design" title="DIY设计器" style="background: #28a745;"
                                                       target="_blank">
                                                        <i class="fas fa-magic"></i>
                                                    </a>
                                                    <a href="/admin/page-builder?action=edit&id=<?php echo htmlentities((string) $template['id']); ?>"
                                                       class="btn-action btn-edit" title="编辑">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button onclick="deleteItem('<?php echo htmlentities((string) $template['id']); ?>', '<?php echo htmlentities((string) htmlentities((string) $template['name'])); ?>', '/admin/page-builder?action=delete&id=<?php echo htmlentities((string) $template['id']); ?>')"
                                                            class="btn-action btn-delete" title="删除">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </div>
                            <?php else: ?>
                                <div class="empty-state">
                                    <div class="empty-icon">
                                        <i class="fas fa-paint-brush"></i>
                                    </div>
                                    <?php if(!empty($currentType) && $currentType != 'all'): ?>
                                        <h3 class="empty-title">暂无<?php echo htmlentities((string) (isset($templateTypes[$currentType]) && ($templateTypes[$currentType] !== '')?$templateTypes[$currentType]:'该类型')); ?>模板</h3>
                                        <p class="empty-subtitle">还没有创建任何<?php echo htmlentities((string) (isset($templateTypes[$currentType]) && ($templateTypes[$currentType] !== '')?$templateTypes[$currentType]:'该类型')); ?>模板，点击下方按钮开始创建吧！</p>
                                        <a href="/admin/page-builder?action=add&preset_type=<?php echo htmlentities((string) $currentType); ?>" class="btn-add-custom">
                                            <i class="fas fa-plus"></i>
                                            创建<?php echo htmlentities((string) (isset($templateTypes[$currentType]) && ($templateTypes[$currentType] !== '')?$templateTypes[$currentType]:'该类型')); ?>模板
                                        </a>
                                    <?php else: ?>
                                        <h3 class="empty-title">暂无模板</h3>
                                        <p class="empty-subtitle">还没有创建任何页面模板，点击上方按钮开始创建您的第一个模板吧！</p>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>



                        <!-- 分页 -->
                        <?php if($templatesList && $templatesList->hasPages()): ?>
                        <div class="pagination-wrapper">
                            <div class="pagination-container">
                                <?php echo $templatesList->render(); ?>
                            </div>
                        </div>
                        <?php endif; elseif($action == 'add' || $action == 'edit'): ?>
                        <!-- 添加/编辑模板表单 -->
                        <div class="form-header">
                            <div class="form-title-section">
                                <div class="form-icon">
                                    <i class="fas fa-<?php echo $action=='add' ? 'plus'  :  'edit'; ?>"></i>
                                </div>
                                <div>
                                    <h1 class="form-title"><?php echo $action=='add' ? '新建模板'  :  '编辑模板'; ?></h1>
                                    <p class="form-subtitle">设置模板基础信息</p>
                                </div>
                            </div>
                            <div class="header-actions">
                                <a href="/admin/page-builder" class="btn-add-custom">
                                    <i class="fas fa-arrow-left"></i>
                                    返回列表
                                </a>
                            </div>
                        </div>

                        <div class="form-body">
                            <form method="post" class="template-form">
                                <input type="hidden" name="action" value="<?php echo htmlentities((string) $action); ?>">
                                <?php if($action == 'edit' && isset($editData) && isset($editData->id)): ?>
                                <input type="hidden" name="id" value="<?php echo htmlentities((string) $editData->id); ?>">
                                <?php endif; ?>

                                <!-- 基本信息区域 -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <h3 class="section-title">
                                            <i class="fas fa-info-circle"></i>
                                            基本信息
                                        </h3>
                                    </div>

                                    <div class="row" style="margin-top: 20px;">
                                        <div class="col-lg-6 col-md-12 mb-3">
                                            <div class="form-group">
                                                <label for="name" class="form-label required">
                                                    <i class="fas fa-tag"></i>
                                                    模板名称
                                                </label>
                                                <input type="text" id="name" name="name" class="form-input"
                                                       value="<?php if(isset($editData) && isset($editData->name)): ?><?php echo htmlentities((string) $editData->name); ?><?php endif; ?>"
                                                       placeholder="请输入模板名称" required>
                                            </div>
                                        </div>

                                        <div class="col-lg-6 col-md-12 mb-3">
                                            <div class="form-group">
                                                <label for="slug" class="form-label required">
                                                    <i class="fas fa-link"></i>
                                                    页面URL标识
                                                </label>
                                                <input type="text" id="slug" name="slug" class="form-input"
                                                       value="<?php if(isset($editData) && isset($editData->slug)): ?><?php echo htmlentities((string) $editData->slug); ?><?php endif; ?>"
                                                       placeholder="例如: about-us" required>
                                                <div class="form-help">用于生成页面访问链接，只能包含字母、数字、连字符</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-lg-6 col-md-12 mb-3">
                                            <div class="form-group">
                                                <label for="type" class="form-label">
                                                    <i class="fas fa-layer-group"></i>
                                                    模板类型
                                                </label>
                                                <select id="type" name="type" class="form-select">
                                                    <?php if(is_array($templateTypes) || $templateTypes instanceof \think\Collection || $templateTypes instanceof \think\Paginator): $typeKey = 0; $__LIST__ = $templateTypes;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$typeName): $mod = ($typeKey % 2 );++$typeKey;?>
                                                    <option value="<?php echo htmlentities((string) $key); ?>" <?php if((isset($editData) && isset($editData->type) && $editData->type == $key) || (isset($editData) && is_object($editData) && property_exists($editData, 'type') && $editData->type == $key)): ?>selected<?php endif; ?>><?php echo htmlentities((string) $typeName); ?></option>
                                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="description" class="form-label">
                                            <i class="fas fa-align-left"></i>
                                            模板描述
                                        </label>
                                        <textarea id="description" name="description" class="form-textarea"
                                                  placeholder="请输入模板描述，说明该模板的用途和特点"><?php if(isset($editData) && isset($editData->description)): ?><?php echo htmlentities((string) $editData->description); ?><?php endif; ?></textarea>
                                        <div class="form-help">简要描述该模板的功能和适用场景</div>
                                    </div>

                                    <div class="row">
                                        <div class="col-lg-6 col-md-12 mb-3">
                                            <div class="form-group">
                                                <label for="sort_order" class="form-label">
                                                    <i class="fas fa-sort-numeric-up"></i>
                                                    排序权重
                                                </label>
                                                <input type="number" id="sort_order" name="sort_order" class="form-input"
                                                       value="<?php if(isset($editData) && isset($editData->sort_order)): ?><?php echo htmlentities((string) (isset($editData->sort_order) && ($editData->sort_order !== '')?$editData->sort_order:'0')); else: ?>0<?php endif; ?>"
                                                       placeholder="数字越大越靠前" min="0">
                                                <div class="form-help">用于控制模板在列表中的显示顺序</div>
                                            </div>
                                        </div>

                                        <div class="col-lg-6 col-md-12 mb-3">
                                            <div class="form-group">
                                                <label class="form-check" style="margin-top: 25px;">
                                                    <input type="checkbox" id="status" name="status" class="form-check-input"
                                                           <?php if(isset($editData) && isset($editData->status) && $editData->status): ?>checked<?php endif; ?>>
                                                    <div class="checkbox-custom"></div>
                                                    <span class="form-check-label">
                                                        <i class="fas fa-eye"></i>
                                                        启用模板
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn-primary">
                                        <i class="fas fa-save"></i>
                                        保存模板
                                    </button>
                                    <a href="/admin/page-builder" class="btn-secondary">
                                        <i class="fas fa-arrow-left"></i>
                                        返回列表
                                    </a>
                                </div>
                            </form>
                        </div>

                    <?php endif; ?>

                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 引用公共JS -->
    <!-- 后台管理系统公共JavaScript文件 -->

<!-- 核心JavaScript库 -->
<script src="/assets/js/jquery.min.js"></script>
<script src="/assets/js/bootstrap.bundle.min.js"></script>

<!-- 后台管理系统核心JS -->
<script src="/assets/js/admin.js"></script>

<!-- 核心初始化脚本 -->
<script>
/**
 * 后台管理系统核心初始化 - 三只鱼网络科技 | 韩总
 * 简化版本，专注核心功能 - ThinkPHP6企业级应用
 */

// 设置全局AJAX超时和错误处理
$.ajaxSetup({
    timeout: 30000 // 30秒超时
});

// 全局AJAX错误处理
$(document).ajaxError(function(event, xhr, settings, error) {
    let message = '操作失败';
    
    if (xhr.responseJSON && xhr.responseJSON.message) {
        message = xhr.responseJSON.message;
    } else if (xhr.status === 404) {
        message = '请求的资源不存在';
    } else if (xhr.status === 403) {
        message = '没有权限执行此操作';
    } else if (xhr.status === 500) {
        message = '服务器内部错误';
    } else if (xhr.status === 'timeout') {
        message = '请求超时，请稍后重试';
    } else if (xhr.status === 'error') {
        message = '网络错误，请检查网络连接';
    }
    
    if (typeof showMessage === 'function') {
        showMessage(message, 'error');
    }
});

// 页面加载完成后的初始化
$(document).ready(function() {
    // 初始化Bootstrap工具提示
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    // 初始化Bootstrap弹出框
    if (typeof bootstrap !== 'undefined' && bootstrap.Popover) {
        const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    }
});
</script>

    
    <!-- 页面装修专用JS -->
    <script src="/assets/js/admin/page-builder.js"></script>
</body>
</html>

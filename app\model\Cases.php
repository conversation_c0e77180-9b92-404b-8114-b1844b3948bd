<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * 客户案例模型
 */
class Cases extends Model
{
    // 设置表名
    protected $name = 'cases';

    // 设置字段信息
    protected $schema = [
        'id'              => 'int',
        'title'           => 'string',
        'slug'            => 'string',
        'client_name'     => 'string',
        'industry'        => 'string',
        'summary'         => 'string',
        'description'     => 'text',
        'image'           => 'string',
        'gallery'         => 'text',
        'project_url'     => 'string',
        'completion_date' => 'date',
        'sort_order'      => 'int',
        'status'          => 'int',
        'views'           => 'int',
        'is_featured'     => 'int',
        'created_at'      => 'datetime',
        'updated_at'      => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 允许写入的字段
    protected $field = [
        'title', 'slug', 'client_name', 'industry', 'summary', 'description',
        'image', 'gallery', 'project_url', 'completion_date', 'sort_order',
        'status', 'views', 'is_featured'
    ];
    
    // 获取推荐案例
    public static function getFeatured($limit = 6)
    {
        return self::where('status', 1)
                   ->where('is_featured', 1)
                   ->order('sort_order', 'asc')
                   ->limit($limit)
                   ->select();
    }

    // 根据slug获取案例
    public static function getBySlug($slug)
    {
        return self::where('slug', $slug)
                   ->where('status', 1)
                   ->find();
    }

    // 获取最新案例
    public static function getLatest($limit = 6)
    {
        return self::where('status', 1)
                   ->order('created_at', 'desc')
                   ->limit($limit)
                   ->select();
    }

    // 获取状态文本
    public function getStatusTextAttr($value, $data)
    {
        $status = [0 => '禁用', 1 => '启用'];
        return $status[$data['status']] ?? '未知';
    }

    // 获取推荐状态文本
    public function getIsFeaturedTextAttr($value, $data)
    {
        $featured = [0 => '否', 1 => '是'];
        return $featured[$data['is_featured']] ?? '否';
    }

    // 获取行业列表
    public static function getIndustries()
    {
        return [
            'internet' => '互联网',
            'finance' => '金融服务',
            'education' => '教育培训',
            'healthcare' => '医疗健康',
            'manufacturing' => '制造业',
            'retail' => '零售电商',
            'realestate' => '房地产',
            'government' => '政府机构',
            'ai' => '人工智能',
            'ecommerce' => '电子商务',
            'other' => '其他'
        ];
    }

    // 获取行业中文名称
    public static function getIndustryName($industry)
    {
        $industries = self::getIndustries();
        return $industries[$industry] ?? $industry;
    }

    // 生成唯一slug
    public static function generateSlug($title, $id = 0)
    {
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title)));
        $originalSlug = $slug;
        $counter = 1;

        while (self::where('slug', $slug)->where('id', '<>', $id)->count() > 0) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    // 检查slug唯一性
    public static function checkSlugUnique($slug, $id = 0)
    {
        return self::where('slug', $slug)->where('id', '<>', $id)->count() === 0;
    }
}

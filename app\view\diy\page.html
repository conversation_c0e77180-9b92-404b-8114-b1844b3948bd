<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$pageData.pageTitle}</title>
    <meta name="description" content="{$pageData.pageDescription}">
    <meta name="keywords" content="{$pageData.pageKeywords}">

    <!-- SEO优化的meta标签 -->
    <meta property="og:title" content="{$pageData.pageTitle}">
    <meta property="og:description" content="{$pageData.pageDescription}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{:request()->url(true)}">
    <!-- Lucide Icons -->
    <script src="/assets/js/lucide.js"></script>

    <!-- 加载DIY组件的通用样式 -->
    <link rel="stylesheet" href="/diy/css/all.css">

    <style>
    /* DIY页面专用样式 */
    body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        line-height: 1.6;
        color: #333;
    }

    .diy-page-container {
        min-height: 100vh;
        width: 100%;
    }

    .diy-content {
        width: 100%;
        max-width: none;
    }

    /* 确保DIY组件样式正确加载 */
    .component-block {
        width: 100%;
        position: relative;
    }

    /* 空页面样式 */
    .empty-page-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        position: relative;
        overflow: hidden;
    }

    .empty-page-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .empty-content {
        text-align: center;
        color: white;
        z-index: 2;
        position: relative;
        max-width: 500px;
        padding: 40px 20px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        animation: fadeInUp 0.8s ease-out;
    }

    .empty-icon {
        font-size: 4rem;
        margin-bottom: 1.5rem;
        opacity: 0.8;
        animation: float 3s ease-in-out infinite;
    }

    .empty-title {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: white;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .empty-description {
        font-size: 1.1rem;
        margin-bottom: 2rem;
        opacity: 0.9;
        line-height: 1.6;
    }

    .empty-actions {
        margin-top: 2rem;
    }

    .btn-home {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        text-decoration: none;
        border-radius: 50px;
        font-weight: 500;
        transition: all 0.3s ease;
        border: 2px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(10px);
    }

    .btn-home:hover {
        background: rgba(255, 255, 255, 0.3);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
    }

    /* 动画效果 */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes float {
        0%, 100% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-10px);
        }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        body {
            font-size: 14px;
        }

        .empty-content {
            margin: 20px;
            padding: 30px 20px;
        }

        .empty-icon {
            font-size: 3rem;
        }

        .empty-title {
            font-size: 1.5rem;
        }

        .empty-description {
            font-size: 1rem;
        }
    }
    </style>

    <!-- 加载DIY页面的自定义CSS样式 -->
    {if condition="!empty($template.css_content)"}
    <style>
    {$template.css_content|raw}
    </style>
    {/if}
</head>
<body class="{$pageData.bodyClass}">{if condition="isset($isPreview)"}
    <!-- 预览模式提示 -->
    <div style="position: fixed; top: 0; left: 0; right: 0; background: #007bff; color: white; text-align: center; padding: 10px; z-index: 9999; font-size: 14px;">
        <i class="fas fa-eye"></i> 预览模式 - 这是页面预览效果
    </div>
    <div style="height: 50px;"></div>
{/if}

<!-- DIY页面内容容器 -->
<div class="diy-page-container">
    <div class="diy-content">
        {if condition="!empty($template.html_content)"}
            <!-- 渲染DIY生成的HTML内容 -->
            {$template.html_content|raw}
        {else /}
            <!-- 空页面提示 -->
            <div class="empty-page-container">
                <div class="empty-content">
                    <div class="empty-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h2 class="empty-title">页面内容为空</h2>
                    <p class="empty-description">该页面暂无内容，请联系管理员添加内容</p>
                    {if condition="!isset($isPreview)"}
                        <div class="empty-actions">
                            <a href="/" class="btn-home">
                                <i class="fas fa-home"></i>
                                返回首页
                            </a>
                        </div>
                    {/if}
                </div>
            </div>
        {/if}
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<!-- 页面底部脚本 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化Lucide图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    // 处理DIY组件的交互功能
    initDiyComponents();
});

function initDiyComponents() {
    // 统计数字动画
    const statsComponents = document.querySelectorAll('.stats-component');
    statsComponents.forEach(component => {
        const numbers = component.querySelectorAll('.stat-number');
        numbers.forEach(number => {
            const finalValue = parseInt(number.textContent);
            if (!isNaN(finalValue)) {
                animateNumber(number, 0, finalValue, 2000);
            }
        });
    });
    
    // 团队成员邮箱联系功能
    const emailLinks = document.querySelectorAll('.team-email');
    emailLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const email = this.textContent;
            if (email && email.includes('@')) {
                window.location.href = 'mailto:' + email;
            }
        });
    });
}

// 数字动画函数
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    
    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = Math.floor(start + (end - start) * progress);
        element.textContent = current;
        
        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }
    
    requestAnimationFrame(update);
}
</script>

</body>
</html>

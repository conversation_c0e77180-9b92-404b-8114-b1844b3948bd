<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\BaseController;
use think\facade\Session;


/**
 * 后台基础控制器
 */
class Base extends BaseController
{
    protected $middleware = [];
    
    public function initialize()
    {
        parent::initialize();

        // 检查登录状态
        $this->checkLogin();
    }
    
    /**
     * 检查登录状态
     */
    protected function checkLogin()
    {
        // 确保session已启动（避免重复启动）
        if (session_status() === PHP_SESSION_NONE && !headers_sent()) {
            session_start();
        }

        $controller = $this->request->controller();

        // 登录页面不需要检查
        if ($controller === 'Login') {
            return;
        }

        // 直接使用原生PHP session检查
        if (!isset($_SESSION['admin_user']) || empty($_SESSION['admin_user'])) {
            if ($this->request->isAjax()) {
                $this->error('请先登录', [], '/admin/login');
            } else {
                if (!headers_sent()) {
                    header('Location: /admin/login');
                }
                exit;
            }
        }
    }
    
    /**
     * 获取当前登录用户
     */
    protected function getAdminUser()
    {
        // 确保session已启动（避免重复启动）
        if (session_status() === PHP_SESSION_NONE && !headers_sent()) {
            session_start();
        }

        return $_SESSION['admin_user'] ?? [
            'id' => 0,
            'username' => 'guest',
            'real_name' => '访客'
        ];
    }
    
    /**
     * 成功响应
     */
    protected function success($msg = '操作成功', $data = [], $url = '')
    {
        return json([
            'code' => 1,
            'msg' => $msg,
            'data' => $data,
            'url' => $url
        ]);
    }
    
    /**
     * 错误响应
     */
    protected function error($msg = '操作失败', $data = [], $url = '')
    {
        return json([
            'code' => 0,
            'msg' => $msg,
            'data' => $data,
            'url' => $url
        ]);
    }
    
    /**
     * 分页数据格式化
     */
    protected function formatPaginate($data)
    {
        return [
            'data' => $data->items(),
            'total' => $data->total(),
            'per_page' => $data->listRows(),
            'current_page' => $data->currentPage(),
            'last_page' => $data->lastPage()
        ];
    }
}

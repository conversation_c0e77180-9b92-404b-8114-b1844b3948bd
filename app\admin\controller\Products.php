<?php
/**
 * 三只鱼网络科技 | 开发者：韩总 | 创建时间：2024-12-19
 * 文件描述：产品管理控制器 - ThinkPHP6企业级应用
 * 技术栈：PHP 8.0+ + ThinkPHP6 + MySQL + Redis
 * 版权所有：三只鱼网络科技有限公司
 */

namespace app\admin\controller;

use app\BaseController;
use app\model\Product as ProductModel;
use app\model\ProductCategory;
use app\model\ProductAttribute;
use app\validate\ProductValidate;
use app\service\FileSecurityService;
use app\validate\SecurityValidate;
use think\facade\Request;
use think\facade\Session;
use think\facade\Filesystem;
use think\exception\ValidateException;

/**
 * 产品管理控制器
 */
class Products extends BaseController
{
    /**
     * 产品列表页面
     */
    public function index()
    {
        // 获取操作类型和参数
        $action = Request::param('action', 'list');
        $tab = Request::param('tab', 'products');
        $id = Request::param('id', 0, 'intval');
        
        // 修复：如果URL中有参数但Request::param()获取不到，尝试从$_GET获取
        if (isset($_GET['action']) && $_GET['action'] !== $action) {
            $action = $_GET['action'];
        }
        if (isset($_GET['tab']) && $_GET['tab'] !== $tab) {
            $tab = $_GET['tab'];
        }
        if (isset($_GET['id']) && $_GET['id'] != $id) {
            $id = intval($_GET['id']);
        }
        
        // 设置分页查询参数
        $queryParams = [
            'action' => $action,
            'tab' => $tab
        ];

        // 处理POST请求
        if (Request::isPost()) {
            return $this->handlePost();
        }

        // 处理删除操作
        if ($action === 'delete' && $id > 0) {
            return $this->delete($id);
        }

        if ($action === 'delete_category' && $id > 0) {
            return $this->deleteCategory($id);
        }

        // 获取编辑数据
        $editData = null;
        $editCategoryData = null;

        if ($action === 'edit' && $id > 0) {
            $editData = ProductModel::find($id);
            if (!$editData) {
                Session::flash('message', '产品不存在');
                Session::flash('messageType', 'error');
                return redirect('/admin/products');
            }
        }

        if ($action === 'edit_category' && $id > 0) {
            $editCategoryData = ProductCategory::find($id);
            if (!$editCategoryData) {
                Session::flash('message', '分类不存在');
                Session::flash('messageType', 'error');
                return redirect('/admin/products?tab=categories');
            }
        }

        // 获取数据
        $productsList = [];
        $categories = [];
        $totalItems = 0;

        if ($action === 'list') {
            // 分页参数
            $page = Request::param('page', 1, 'intval');
            $itemsPerPage = 5;

            if ($tab === 'products') {
                // 获取产品列表
                $totalItems = ProductModel::count();
                $productsList = ProductModel::with('category')
                    ->order('sort_order', 'desc')
                    ->order('id', 'desc')
                    ->paginate([
                        'list_rows' => $itemsPerPage,
                        'page' => $page,
                        'query' => $queryParams
                    ]);
            } else {
                // 获取分类列表，包含产品数量
                $categories = ProductCategory::order('sort_order', 'asc')
                    ->order('id', 'asc')
                    ->select();

                // 为每个分类添加产品数量
                foreach ($categories as $category) {
                    $category->product_count = ProductModel::where('category_id', $category->id)->count();
                }
            }
        }

        // 获取所有分类（用于表单选择）
        $allCategories = ProductCategory::where('status', 1)
            ->order('sort_order', 'asc')
            ->select();

        return view('admin/products', [
            'action' => $action,
            'tab' => $tab,
            'productsList' => $productsList,
            'categories' => $categories,
            'allCategories' => $allCategories,
            'editData' => $editData,
            'editCategoryData' => $editCategoryData,
            'totalItems' => $totalItems,
            'message' => Session::pull('message') ?: '',
            'messageType' => Session::pull('messageType') ?: 'info'
        ]);
    }

    /**
     * 处理POST请求
     */
    private function handlePost()
    {
        $action = Request::param('action');

        switch ($action) {
            case 'add':
            case 'edit':
                return $this->save();

            case 'add_category':
            case 'edit_category':
                return $this->saveCategory();

            case 'upload_image':
                return $this->uploadEditorImage();

            case 'toggle_status':
                return $this->toggleStatus();

            default:
                return json(['success' => false, 'message' => '无效的操作']);
        }
    }

    /**
     * 保存产品
     */
    private function save()
    {
        $data = Request::param();
        $action = $data['action'];
        $id = $data['id'] ?? 0;

        // 安全过滤输入数据
        $validate = new ProductValidate();
        $data = $validate->filterInput($data);

        // 数据安全验证 - 使用统一安全验证器
        $securityCheck = SecurityValidate::validateDataSecurity($data, [
            'name' => 'checkXss',
            'description' => 'checkSqlInjection|checkXss',
            'short_description' => 'checkXss',
            'tags' => 'checkXss',
            'meta_title' => 'checkXss',
            'meta_description' => 'checkXss',
            'sku' => 'checkUsernameSafe',
        ]);

        if (!$securityCheck['valid']) {
            $errors = [];
            foreach ($securityCheck['errors'] as $field => $fieldErrors) {
                $errors[] = $field . ': ' . implode(', ', $fieldErrors);
            }
            Session::flash('message', '数据安全检查失败: ' . implode('; ', $errors));
            Session::flash('messageType', 'error');
            return redirect()->restore();
        }

        // 数据验证
        $scene = $action === 'add' ? 'add' : 'edit';
        try {
            $validate->scene($scene)->check($data);
        } catch (ValidateException $e) {
            Session::flash('message', $e->getError());
            Session::flash('messageType', 'error');
            return redirect()->restore();
        }

        // 处理slug
        $slug = $data['slug'] ?: ProductModel::generateSlug($data['name']);
        if (!ProductModel::checkSlugUnique($slug, $id)) {
            $slug = $slug . '-' . time();
        }

        // 处理图片上传
        $imagePath = '';
        $imageChanged = false;

        // 优先处理新的图片选择器传来的image_url
        if (!empty($data['image_url'])) {
            $imagePath = $data['image_url'];
            $imageChanged = true;
        } else {
            // 兼容传统文件上传
            $file = Request::file('image');
            if ($file) {
                // 文件安全验证 - 保持原有逻辑
                $fileValidation = $validate->validateFileUpload($file);
                if ($fileValidation !== true) {
                    Session::flash('message', $fileValidation);
                    Session::flash('messageType', 'error');
                    return redirect()->restore();
                }

                // 额外的深度安全检查（增强）
                $securityCheck = FileSecurityService::validateFile($file, [
                    'check_magic' => true,
                    'check_content' => true,
                    'strict_mode' => false, // 宽松模式，避免过度拦截
                ]);

                if (!$securityCheck['valid']) {
                    Session::flash('message', '文件安全检查失败：' . implode(', ', $securityCheck['errors']));
                    Session::flash('messageType', 'error');
                    return redirect()->restore();
                }

                try {
                    $savename = Filesystem::disk('public')->putFile('uploads/products', $file);
                    $imagePath = '/storage/' . $savename;
                    $imageChanged = true;
                } catch (\Exception $e) {
                    Session::flash('message', '图片上传失败：' . $e->getMessage());
                    Session::flash('messageType', 'error');
                    return redirect()->restore();
                }
            }
        }

        // 处理产品特性（JSON格式）
        $features = [];
        if (!empty($data['features'])) {
            $featuresArray = explode("\n", $data['features']);
            foreach ($featuresArray as $feature) {
                $feature = trim($feature);
                if (!empty($feature)) {
                    $features[] = $feature;
                }
            }
        }

        // 准备数据
        $saveData = [
            'category_id' => $data['category_id'] ?? 1,
            'name' => $data['name'],
            'slug' => $slug,
            'short_description' => $data['short_description'] ?? '',
            'description' => $data['description'],
            'features' => json_encode($features),
            'icon' => $data['icon'] ?? '',
            'price' => $data['price'] ?? '',
            'original_price' => $data['original_price'] ?? '',
            'stock_status' => $data['stock_status'] ?? 'in_stock',
            'sku' => $data['sku'] ?? '',
            'tags' => $data['tags'] ?? '',
            'meta_title' => $data['meta_title'] ?? '',
            'meta_description' => $data['meta_description'] ?? '',
            'sort_order' => $data['sort_order'] ?? 0,
            'status' => isset($data['status']) ? 1 : 0,
            'is_featured' => isset($data['is_featured']) ? 1 : 0,
            'is_hot' => isset($data['is_hot']) ? 1 : 0,
            'is_new' => isset($data['is_new']) ? 1 : 0,
        ];

        // 只有在图片有变化时才更新图片字段
        if ($imageChanged && $imagePath) {
            $saveData['image'] = $imagePath;
        }

        try {
            if ($action === 'add') {
                $product = ProductModel::create($saveData);
                Session::flash('message', '产品添加成功');
            } else {
                $product = ProductModel::find($id);
                if (!$product) {
                    Session::flash('message', '产品不存在');
                    Session::flash('messageType', 'error');
                    return redirect('/admin/products');
                }

                // 如果上传了新图片，删除旧图片
                if ($imageChanged && $imagePath && $product->image && $product->image !== $imagePath && strpos($product->image, '/storage/') === 0) {
                    try {
                        $oldImagePath = str_replace('/storage/', '', $product->image);
                        Filesystem::disk('public')->delete($oldImagePath);
                    } catch (\Exception $e) {
                        // 忽略删除失败
                    }
                }

                $product->save($saveData);
                Session::flash('message', '产品更新成功');
            }

            Session::flash('messageType', 'success');
            return redirect('/admin/products');

        } catch (\Exception $e) {
            Session::flash('message', '操作失败：' . $e->getMessage());
            Session::flash('messageType', 'error');
            return redirect()->restore();
        }
    }

    /**
     * 保存分类
     */
    private function saveCategory()
    {
        $data = Request::param();
        $action = $data['action'];
        $id = $data['id'] ?? 0;

        // 验证必填字段
        if (empty($data['category_name'])) {
            Session::flash('message', '请输入分类名称');
            Session::flash('messageType', 'error');
            return redirect()->restore();
        }

        // 处理slug
        $slug = $data['category_slug'] ?: ProductCategory::generateSlug($data['category_name']);
        if (!ProductCategory::checkSlugUnique($slug, $id)) {
            $slug = $slug . '-' . time();
        }

        // 准备数据
        $saveData = [
            'name' => $data['category_name'],
            'slug' => $slug,
            'description' => $data['category_description'] ?? '',
            'icon' => $data['category_icon'] ?? '',
            'parent_id' => $data['parent_id'] ?? 0,
            'sort_order' => $data['category_sort_order'] ?? 0,
            'status' => isset($data['status']) ? 1 : 0,
        ];

        try {
            if ($action === 'add_category') {
                ProductCategory::create($saveData);
                Session::flash('message', '分类添加成功');
            } else {
                $category = ProductCategory::find($id);
                if (!$category) {
                    Session::flash('message', '分类不存在');
                    Session::flash('messageType', 'error');
                    return redirect('/admin/products?tab=categories');
                }

                $category->save($saveData);
                Session::flash('message', '分类更新成功');
            }

            Session::flash('messageType', 'success');
            return redirect('/admin/products?tab=categories');

        } catch (\Exception $e) {
            Session::flash('message', '操作失败：' . $e->getMessage());
            Session::flash('messageType', 'error');
            return redirect()->restore();
        }
    }

    /**
     * 删除产品
     */
    private function delete($id)
    {
        try {
            $product = ProductModel::find($id);
            if (!$product) {
                Session::flash('message', '产品不存在');
                Session::flash('messageType', 'error');
                return redirect('/admin/products');
            }

            // 删除图片记录和文件
            if ($product->image) {
                $this->deleteImageRecord($product->image);
            }

            // 删除产品属性
            ProductAttribute::where('product_id', $id)->delete();

            $product->delete();

            Session::flash('message', '产品删除成功');
            Session::flash('messageType', 'success');

        } catch (\Exception $e) {
            Session::flash('message', '删除失败：' . $e->getMessage());
            Session::flash('messageType', 'error');
        }

        return redirect('/admin/products');
    }

    /**
     * 删除图片记录和文件
     */
    private function deleteImageRecord($imageUrl)
    {
        if (empty($imageUrl)) {
            return;
        }

        try {
            // 查找图片数据库记录
            $image = \app\model\Image::where('file_url', $imageUrl)
                ->whereOr('file_path', $imageUrl)
                ->find();

            if ($image) {
                // 删除数据库记录（模型事件会自动删除物理文件）
                $image->delete();
            } else {
                // 如果数据库中没有记录，尝试删除物理文件（兼容旧数据）
                $this->deletePhysicalFile($imageUrl);
            }
        } catch (\Exception $e) {
            // 记录错误但不影响主流程
            trace('删除图片失败：' . $e->getMessage(), 'error');
            // 尝试删除物理文件作为备用方案
            $this->deletePhysicalFile($imageUrl);
        }
    }

    /**
     * 删除物理文件（备用方案）
     */
    private function deletePhysicalFile($imageUrl)
    {
        try {
            if (strpos($imageUrl, '/storage/') === 0) {
                $imagePath = str_replace('/storage/', '', $imageUrl);
                Filesystem::disk('public')->delete($imagePath);
            } elseif (strpos($imageUrl, '/uploads/') === 0) {
                $filePath = public_path() . $imageUrl;
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }
        } catch (\Exception $e) {
            // 忽略删除失败
        }
    }

    /**
     * 删除分类
     */
    private function deleteCategory($id)
    {
        try {
            $category = ProductCategory::find($id);
            if (!$category) {
                Session::flash('message', '分类不存在');
                Session::flash('messageType', 'error');
                return redirect('/admin/products?tab=categories');
            }

            // 检查是否有产品使用此分类
            $productCount = ProductModel::where('category_id', $id)->count();
            if ($productCount > 0) {
                Session::flash('message', '该分类下还有产品，无法删除');
                Session::flash('messageType', 'error');
                return redirect('/admin/products?tab=categories');
            }

            // 检查是否有子分类
            $childCount = ProductCategory::where('parent_id', $id)->count();
            if ($childCount > 0) {
                Session::flash('message', '该分类下还有子分类，无法删除');
                Session::flash('messageType', 'error');
                return redirect('/admin/products?tab=categories');
            }

            $category->delete();

            Session::flash('message', '分类删除成功');
            Session::flash('messageType', 'success');

        } catch (\Exception $e) {
            Session::flash('message', '删除失败：' . $e->getMessage());
            Session::flash('messageType', 'error');
        }

        return redirect('/admin/products?tab=categories');
    }

    /**
     * 编辑器图片上传 - 增强安全检查
     */
    private function uploadEditorImage()
    {
        $file = Request::file('upload');

        if (!$file) {
            return json(['error' => ['message' => '没有上传文件']]);
        }

        try {
            // 使用原有的验证逻辑
            $validate = new ProductValidate();
            $fileValidation = $validate->validateFileUpload($file);
            if ($fileValidation !== true) {
                return json(['error' => ['message' => $fileValidation]]);
            }

            // 额外的深度安全检查
            $securityCheck = FileSecurityService::validateFile($file, [
                'check_magic' => true,
                'check_content' => true,
                'strict_mode' => false, // 宽松模式
            ]);

            if (!$securityCheck['valid']) {
                return json(['error' => ['message' => '文件安全检查失败：' . implode(', ', $securityCheck['errors'])]]);
            }

            $savename = Filesystem::disk('public')->putFile('uploads/editor', $file);
            $url = '/storage/' . $savename;

            return json(['url' => $url]);

        } catch (\Exception $e) {
            return json(['error' => ['message' => '上传失败：' . $e->getMessage()]]);
        }
    }

    /**
     * 切换产品状态
     */
    private function toggleStatus()
    {
        $id = Request::param('id');
        $status = Request::param('status');

        try {
            $product = ProductModel::find($id);
            if (!$product) {
                return json(['success' => false, 'message' => '产品不存在']);
            }

            $product->status = $status;
            $product->save();

            return json(['success' => true, 'message' => '状态更新成功']);

        } catch (\Exception $e) {
            return json(['success' => false, 'message' => '状态更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 专门的添加分类页面方法
     */
    public function addCategory()
    {
        // 强制设置参数
        $action = 'add_category';
        $tab = 'categories';
        $id = 0;

        // 处理POST请求
        if (Request::isPost()) {
            return $this->handlePost();
        }

        // 获取所有分类（用于表单选择）
        $allCategories = ProductCategory::where('status', 1)
            ->order('sort_order', 'asc')
            ->select();

        return view('admin/products', [
            'action' => $action,
            'tab' => $tab,
            'productsList' => [],
            'categories' => [],
            'allCategories' => $allCategories,
            'editData' => null,
            'editCategoryData' => null,
            'totalItems' => 0,
            'message' => Session::pull('message'),
            'messageType' => Session::pull('messageType')
        ]);
    }
}

/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 认证相关类型定义
 */

/**
 * 用户信息
 */
export interface User {
  id: number
  username: string
  email: string
  real_name?: string
  phone?: string
  avatar?: string
  avatar_url?: string
  status: number
  status_text: string
  last_login_at?: string
  last_login_ip?: string
  login_count: number
  created_at: string
  updated_at: string
}

/**
 * 用户信息（详细）
 */
export interface UserInfo extends User {
  phone_mask?: string
  email_mask?: string
}

/**
 * 角色信息
 */
export interface Role {
  id: number
  name: string
  slug: string
  description?: string
  permissions: string[]
  is_default: number
  sort_order: number
  user_count?: number
  created_at: string
  updated_at: string
}

/**
 * 登录凭据
 */
export interface LoginCredentials {
  username: string
  password: string
}

/**
 * 登录响应
 */
export interface LoginResponse {
  token: string
  user: User
  permissions: string[]
  roles?: Role[]
  expires_in: number
}

/**
 * 注册数据
 */
export interface RegisterData {
  username: string
  email: string
  password: string
  confirm_password: string
  real_name?: string
  phone?: string
}

/**
 * 注册响应
 */
export interface RegisterResponse {
  token: string
  user: User
  permissions: string[]
  roles?: Role[]
  expires_in: number
}

/**
 * 修改密码数据
 */
export interface ChangePasswordData {
  old_password: string
  new_password: string
  confirm_new_password: string
}

/**
 * 忘记密码数据
 */
export interface ForgotPasswordData {
  email: string
}

/**
 * 重置密码数据
 */
export interface ResetPasswordData {
  token: string
  password: string
  confirm_password: string
}

/**
 * 发送验证码数据
 */
export interface SendCodeData {
  phone: string
  type: 'register' | 'login' | 'reset_password' | 'bind_phone' | 'change_phone'
}

/**
 * 验证验证码数据
 */
export interface VerifyCodeData {
  phone: string
  code: string
  type: string
}

/**
 * 绑定手机号数据
 */
export interface BindPhoneData {
  phone: string
  code: string
}

/**
 * 更新个人资料数据
 */
export interface UpdateProfileData {
  real_name?: string
  avatar?: string
}

/**
 * 检查响应
 */
export interface CheckResponse {
  available: boolean
  message: string
}

/**
 * API响应基础结构
 */
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
}

/**
 * 分页响应结构
 */
export interface PaginatedResponse<T = any> {
  code: number
  message: string
  data: {
    list: T[]
    total: number
    per_page: number
    current_page: number
    last_page: number
    has_more: boolean
  }
  timestamp: number
}

/**
 * 权限信息
 */
export interface Permission {
  key: string
  name: string
}

/**
 * 权限组
 */
export interface PermissionGroup {
  group: string
  permissions: Permission[]
}

/**
 * 用户统计
 */
export interface UserStatistics {
  total: number
  enabled: number
  disabled: number
  today_new: number
  week_new: number
  month_new: number
  recent_login: number
  role_stats: Array<{
    name: string
    count: number
  }>
}

/**
 * 角色统计
 */
export interface RoleStatistics {
  total: number
  default_role: string
  role_user_stats: Array<{
    name: string
    user_count: number
  }>
  permission_stats: Array<{
    role_name: string
    permission_count: number
  }>
}

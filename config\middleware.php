<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * 中间件配置 - ThinkPHP6企业级应用
 * 功能：统一安全中间件配置和优先级管理
 */

return [
    // 别名或分组
    'alias'    => [
        'AdminAuth' => \app\middleware\AdminAuth::class,
        'Security' => \app\middleware\SecurityMiddleware::class,
        'SqlInjection' => \app\middleware\SqlInjectionMiddleware::class,
    ],

    // 优先级设置，此数组中的中间件会按照数组中的顺序优先执行
    'priority' => [
        // SQL注入防护优先级最高，在所有处理之前进行检查
        \app\middleware\SqlInjectionMiddleware::class,
        // 通用安全中间件
        \app\middleware\SecurityMiddleware::class,
        // 管理员认证中间件
        \app\middleware\AdminAuth::class,
    ],
];

<!--
  三只鱼网络科技 | 韩总 | 2024-12-20
  QiyeDIY企业建站系统 - 数据图表组件
-->

<template>
  <div class="data-chart" :class="{ 'chart-loading': loading }">
    <!-- 图表头部 -->
    <div class="chart-header">
      <div class="chart-title">
        <h3>{{ title }}</h3>
        <p v-if="description" class="chart-description">{{ description }}</p>
      </div>
      
      <div class="chart-actions">
        <!-- 时间范围选择 -->
        <el-select v-model="timeRange" @change="onTimeRangeChange" size="small">
          <el-option label="今天" value="today" />
          <el-option label="昨天" value="yesterday" />
          <el-option label="最近7天" value="week" />
          <el-option label="最近30天" value="month" />
          <el-option label="最近90天" value="quarter" />
          <el-option label="自定义" value="custom" />
        </el-select>
        
        <!-- 自定义时间范围 -->
        <el-date-picker
          v-if="timeRange === 'custom'"
          v-model="customDateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          @change="onCustomDateChange"
        />
        
        <!-- 刷新按钮 -->
        <el-button @click="refreshData" size="small" :loading="loading">
          <Icon name="refresh" />
        </el-button>
        
        <!-- 导出按钮 -->
        <el-dropdown @command="onExport" size="small">
          <el-button>
            <Icon name="download" />
            导出
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="png">导出为PNG</el-dropdown-item>
              <el-dropdown-item command="jpg">导出为JPG</el-dropdown-item>
              <el-dropdown-item command="pdf">导出为PDF</el-dropdown-item>
              <el-dropdown-item command="excel">导出为Excel</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
    <!-- 图表容器 -->
    <div class="chart-container">
      <div
        ref="chartRef"
        class="chart-canvas"
        :style="{ height: height + 'px' }"
      ></div>
      
      <!-- 加载状态 -->
      <div v-if="loading" class="chart-loading-overlay">
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
        <span>数据加载中...</span>
      </div>
      
      <!-- 空数据状态 -->
      <div v-if="!loading && isEmpty" class="chart-empty">
        <Icon name="chart-empty" size="64" />
        <p>暂无数据</p>
        <el-button @click="refreshData" type="primary" size="small">
          重新加载
        </el-button>
      </div>
    </div>
    
    <!-- 图表说明 -->
    <div v-if="showLegend && legendData.length" class="chart-legend">
      <div
        v-for="item in legendData"
        :key="item.name"
        class="legend-item"
        @click="onLegendClick(item)"
      >
        <span
          class="legend-color"
          :style="{ backgroundColor: item.color }"
        ></span>
        <span class="legend-name">{{ item.name }}</span>
        <span class="legend-value">{{ item.value }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import type { ECharts, EChartsOption } from 'echarts'

interface ChartData {
  name: string
  value: number | string
  color?: string
  [key: string]: any
}

interface LegendItem {
  name: string
  value: string | number
  color: string
  visible: boolean
}

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'line',
    validator: (value: string) => ['line', 'bar', 'pie', 'area', 'scatter', 'radar'].includes(value)
  },
  data: {
    type: Array as PropType<ChartData[]>,
    default: () => []
  },
  options: {
    type: Object as PropType<EChartsOption>,
    default: () => ({})
  },
  height: {
    type: Number,
    default: 400
  },
  showLegend: {
    type: Boolean,
    default: true
  },
  autoRefresh: {
    type: Boolean,
    default: false
  },
  refreshInterval: {
    type: Number,
    default: 30000 // 30秒
  },
  apiUrl: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['refresh', 'export', 'legend-click'])

// 响应式数据
const chartRef = ref<HTMLElement>()
const chart = ref<ECharts>()
const loading = ref(false)
const timeRange = ref('week')
const customDateRange = ref<[Date, Date]>()
const legendData = ref<LegendItem[]>([])

// 计算属性
const isEmpty = computed(() => {
  return !props.data || props.data.length === 0
})

// 图表配置
const getChartOption = (): EChartsOption => {
  const baseOption: EChartsOption = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: props.data.map(item => item.name),
      axisLine: {
        lineStyle: {
          color: '#e2e8f0'
        }
      },
      axisLabel: {
        color: '#64748b'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#64748b'
      },
      splitLine: {
        lineStyle: {
          color: '#f1f5f9'
        }
      }
    },
    series: []
  }

  // 根据图表类型生成配置
  switch (props.type) {
    case 'line':
      baseOption.series = [{
        type: 'line',
        data: props.data.map(item => item.value),
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#667eea'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(102, 126, 234, 0.3)' },
              { offset: 1, color: 'rgba(102, 126, 234, 0.05)' }
            ]
          }
        },
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#667eea',
          borderColor: '#fff',
          borderWidth: 2
        }
      }]
      break

    case 'bar':
      baseOption.series = [{
        type: 'bar',
        data: props.data.map(item => item.value),
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#667eea' },
              { offset: 1, color: '#764ba2' }
            ]
          },
          borderRadius: [4, 4, 0, 0]
        }
      }]
      break

    case 'pie':
      baseOption.tooltip = {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      }
      baseOption.series = [{
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '50%'],
        data: props.data.map((item, index) => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: item.color || getColorByIndex(index)
          }
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        }
      }]
      delete baseOption.xAxis
      delete baseOption.yAxis
      delete baseOption.grid
      break

    case 'area':
      baseOption.series = [{
        type: 'line',
        data: props.data.map(item => item.value),
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(102, 126, 234, 0.6)' },
              { offset: 1, color: 'rgba(102, 126, 234, 0.1)' }
            ]
          }
        },
        lineStyle: {
          width: 0
        },
        symbol: 'none'
      }]
      break
  }

  // 合并自定义配置
  return { ...baseOption, ...props.options }
}

// 颜色生成
const getColorByIndex = (index: number): string => {
  const colors = [
    '#667eea', '#764ba2', '#f093fb', '#f5576c',
    '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
    '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3'
  ]
  return colors[index % colors.length]
}

// 初始化图表
const initChart = async () => {
  if (!chartRef.value) return

  chart.value = echarts.init(chartRef.value)
  
  // 设置图表配置
  const option = getChartOption()
  chart.value.setOption(option)
  
  // 生成图例数据
  generateLegendData()
  
  // 监听图表事件
  chart.value.on('click', (params) => {
    emit('legend-click', params)
  })
}

// 更新图表
const updateChart = () => {
  if (!chart.value) return
  
  const option = getChartOption()
  chart.value.setOption(option, true)
  generateLegendData()
}

// 生成图例数据
const generateLegendData = () => {
  if (!props.showLegend) return
  
  legendData.value = props.data.map((item, index) => ({
    name: item.name,
    value: typeof item.value === 'number' ? item.value.toLocaleString() : item.value,
    color: item.color || getColorByIndex(index),
    visible: true
  }))
}

// 刷新数据
const refreshData = async () => {
  if (!props.apiUrl) {
    emit('refresh')
    return
  }
  
  try {
    loading.value = true
    
    const params = {
      time_range: timeRange.value,
      start_date: customDateRange.value?.[0]?.toISOString().split('T')[0],
      end_date: customDateRange.value?.[1]?.toISOString().split('T')[0]
    }
    
    const response = await $fetch(props.apiUrl, { params })
    
    if (response.code === 200) {
      // 更新数据
      emit('refresh', response.data)
    }
    
  } catch (error) {
    console.error('刷新图表数据失败:', error)
    ElMessage.error('刷新数据失败')
  } finally {
    loading.value = false
  }
}

// 时间范围变化
const onTimeRangeChange = () => {
  if (timeRange.value !== 'custom') {
    customDateRange.value = undefined
  }
  refreshData()
}

// 自定义日期变化
const onCustomDateChange = () => {
  refreshData()
}

// 导出图表
const onExport = (format: string) => {
  if (!chart.value) return
  
  try {
    let dataUrl: string
    
    switch (format) {
      case 'png':
        dataUrl = chart.value.getDataURL({
          type: 'png',
          pixelRatio: 2,
          backgroundColor: '#fff'
        })
        downloadImage(dataUrl, `${props.title}.png`)
        break
        
      case 'jpg':
        dataUrl = chart.value.getDataURL({
          type: 'jpeg',
          pixelRatio: 2,
          backgroundColor: '#fff'
        })
        downloadImage(dataUrl, `${props.title}.jpg`)
        break
        
      case 'pdf':
        // PDF导出需要额外处理
        exportToPDF()
        break
        
      case 'excel':
        // Excel导出
        exportToExcel()
        break
    }
    
    emit('export', { format, data: props.data })
    
  } catch (error) {
    console.error('导出图表失败:', error)
    ElMessage.error('导出失败')
  }
}

// 下载图片
const downloadImage = (dataUrl: string, filename: string) => {
  const link = document.createElement('a')
  link.download = filename
  link.href = dataUrl
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 导出PDF
const exportToPDF = () => {
  // 实现PDF导出逻辑
  ElMessage.info('PDF导出功能开发中...')
}

// 导出Excel
const exportToExcel = () => {
  // 实现Excel导出逻辑
  ElMessage.info('Excel导出功能开发中...')
}

// 图例点击
const onLegendClick = (item: LegendItem) => {
  item.visible = !item.visible
  emit('legend-click', item)
}

// 窗口大小变化
const handleResize = () => {
  if (chart.value) {
    chart.value.resize()
  }
}

// 自动刷新
let refreshTimer: NodeJS.Timeout | null = null

const startAutoRefresh = () => {
  if (!props.autoRefresh) return
  
  refreshTimer = setInterval(() => {
    refreshData()
  }, props.refreshInterval)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 生命周期
onMounted(async () => {
  await nextTick()
  await initChart()
  
  window.addEventListener('resize', handleResize)
  
  if (props.autoRefresh) {
    startAutoRefresh()
  }
})

onUnmounted(() => {
  if (chart.value) {
    chart.value.dispose()
  }
  
  window.removeEventListener('resize', handleResize)
  stopAutoRefresh()
})

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

watch(() => props.options, () => {
  updateChart()
}, { deep: true })

// 暴露方法
defineExpose({
  refreshData,
  exportChart: onExport,
  getChartInstance: () => chart.value
})
</script>

<style lang="scss" scoped>
.data-chart {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  &.chart-loading {
    pointer-events: none;
  }
}

.chart-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 16px;
}

.chart-title {
  flex: 1;
  
  h3 {
    margin: 0 0 4px;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
  }
}

.chart-description {
  margin: 0;
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
}

.chart-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.chart-container {
  position: relative;
  padding: 16px 24px 24px;
}

.chart-canvas {
  width: 100%;
}

.chart-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 14px;
  color: #64748b;
}

.chart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #94a3b8;
  
  p {
    margin: 16px 0 20px;
    font-size: 16px;
  }
}

.chart-legend {
  padding: 16px 24px 20px;
  border-top: 1px solid #f1f5f9;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
  
  &:hover {
    background: #f8fafc;
  }
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  flex-shrink: 0;
}

.legend-name {
  font-size: 14px;
  color: #475569;
}

.legend-value {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  margin-left: 4px;
}

// 响应式设计
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .chart-actions {
    justify-content: flex-start;
  }
  
  .chart-container {
    padding: 12px 16px 16px;
  }
  
  .chart-legend {
    padding: 12px 16px 16px;
    gap: 12px;
  }
  
  .legend-item {
    flex: 1;
    min-width: calc(50% - 6px);
  }
}

@media (max-width: 480px) {
  .chart-header {
    padding: 16px 16px 12px;
  }
  
  .chart-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .legend-item {
    min-width: 100%;
  }
}
</style>

<?php /*a:4:{s:56:"D:\EServer\core\www\san.com\app\view\products\index.html";i:1749614980;s:55:"D:\EServer\core\www\san.com\app\view\common\header.html";i:1749620980;s:55:"D:\EServer\core\www\san.com\app\view\common\styles.html";i:1748797665;s:55:"D:\EServer\core\www\san.com\app\view\common\footer.html";i:1749617057;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <title><?php echo htmlentities((string) (isset($pageTitle) && ($pageTitle !== '')?$pageTitle:'首页')); ?> - <?php echo htmlentities((string) (isset($siteConfig['site_name']) && ($siteConfig['site_name'] !== '')?$siteConfig['site_name']:'三只鱼网络')); ?></title>
    <meta name="description" content="<?php echo htmlentities((string) ((isset($pageDescription) && ($pageDescription !== '')?$pageDescription:$siteConfig['site_description']) ?: '专注于为企业提供专业、可靠、高效的数字化转型解决方案')); ?>">
    <meta name="keywords" content="<?php echo htmlentities((string) ((isset($pageKeywords) && ($pageKeywords !== '')?$pageKeywords:$siteConfig['site_keywords']) ?: '数字化转型,企业解决方案,技术服务,创新科技,专业团队')); ?>">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?php echo htmlentities((string) ((isset($pageTitle) && ($pageTitle !== '')?$pageTitle:$siteConfig['site_name']) ?: '三只鱼网络')); ?>">
    <meta property="og:description" content="<?php echo htmlentities((string) ((isset($pageDescription) && ($pageDescription !== '')?$pageDescription:$siteConfig['site_description']) ?: '专注于为企业提供专业、可靠、高效的数字化转型解决方案')); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo htmlentities((string) app('request')->domain()); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo asset('assets/images/favicon.ico'); ?>">
    
    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo asset('assets/css/bootstrap.min.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset('assets/css/all.min.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset('assets/css/animate.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset('assets/css/swiper-bundle.min.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset('assets/css/style.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset('assets/css/contact.css'); ?>">

    <!-- Lucide Icons - 本地版本 -->
    <script src="<?php echo asset('assets/js/lucide.js'); ?>"></script>
    
    <!-- 导航菜单层级修复 -->
    <link rel="stylesheet" href="<?php echo asset('assets/css/nav-fix.css'); ?>">

    <!-- 导航栏样式 -->
    <style>
    /* ===== 下拉菜单基础样式 ===== */
    .dropdown-menu {
        background: #ffffff;
        border: none;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        border-radius: 12px;
        padding: 12px 0;
    }

    .dropdown-item {
        padding: 10px 20px;
        font-size: 14px;
        transition: all 0.2s ease;
        border: none;
        background: transparent;
    }

    .dropdown-item:hover {
        background: rgba(0, 123, 255, 0.08);
        color: #007bff;
    }

    /* ===== 大型下拉菜单容器 ===== */
    .mega-dropdown {
        position: relative;
    }

    .mega-dropdown .dropdown-menu {
        display: none;
        position: fixed;
        top: 80px;
        left: 50%;
        transform: translateX(-50%);
        margin: 0;
        border-radius: 16px;
        background: white;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        border: none;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        pointer-events: none;
        width: 1200px;
        max-width: 90vw;
        max-height: 80vh;
        overflow-y: auto;
    }

    /* 下拉菜单显示状态 - 通过JavaScript控制 */
    .mega-dropdown.show .dropdown-menu {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: auto !important;
    }

    /* 确保菜单内容有正确的显示状态 */
    .dropdown-menu {
        display: none;
        opacity: 0;
        visibility: hidden;
        pointer-events: none;
    }

    /* 当菜单被JavaScript激活时的样式 */
    .dropdown-menu.show,
    .dropdown:hover .dropdown-menu {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: auto !important;
    }

    /* 增加菜单和触发器之间的连接区域 */
    .mega-dropdown::before {
        content: '';
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        height: 20px;
        background: transparent;
        z-index: 9999;
        pointer-events: auto;
    }

    /* ===== 简单可靠的三角形指示器 ===== */
    .mega-dropdown .dropdown-menu::before {
        content: '';
        position: absolute;
        top: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-bottom: 8px solid white;
        z-index: 1001;
    }

    /* 三角形阴影 */
    .mega-dropdown .dropdown-menu::after {
        content: '';
        position: absolute;
        top: -9px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 9px solid transparent;
        border-right: 9px solid transparent;
        border-bottom: 9px solid rgba(0, 0, 0, 0.1);
        z-index: 1000;
    }

    /* ===== 下拉箭头指示器 ===== */
    .dropdown-toggle::after {
        display: inline-block;
        margin-left: 6px;
        vertical-align: middle;
        content: "▼";
        font-size: 10px;
        transition: transform 0.3s ease;
        line-height: 1;
    }

    .dropdown-toggle[aria-expanded="true"]::after,
    .mega-dropdown.show .dropdown-toggle::after {
        transform: rotate(180deg);
    }

    /* 解决方案菜单特殊样式 */
    .solutions-menu {
        padding: 0;
    }

    .solutions-menu .mega-menu-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 25px 30px;
        text-align: center;
        color: white;
        border-radius: 16px 16px 0 0;
    }

    .solutions-menu .mega-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 8px;
        color: white;
    }

    .solutions-menu .mega-subtitle {
        font-size: 0.9rem;
        opacity: 0.9;
        margin: 0;
        color: white;
    }

    /* 解决方案网格布局 */
    .solutions-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
        padding: 30px;
        background: white;
    }

    .solution-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 20px 15px;
        border-radius: 12px;
        transition: all 0.3s ease;
        cursor: pointer;
        text-decoration: none;
        color: inherit;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        min-height: 140px;
        justify-content: flex-start;
    }

    .solution-item:hover {
        background: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.12);
        text-decoration: none;
        color: inherit;
        border-color: rgba(102, 126, 234, 0.3);
    }

    .solution-item .solution-icon {
        width: 64px;
        height: 64px;
        background: transparent;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 12px;
        transition: all 0.3s ease;
        flex-shrink: 0;
    }

    .solution-item .solution-icon img {
        width: 48px;
        height: 48px;
        object-fit: contain;
    }

    .solution-item:hover .solution-icon {
        transform: scale(1.1);
    }

    .solution-item .solution-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 100%;
    }

    .solution-item .solution-content h4 {
        font-size: 1rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        line-height: 1.3;
    }

    .solution-item .solution-content p {
        font-size: 0.8rem;
        color: #666;
        line-height: 1.4;
        margin: 0;
    }

    /* 产品网格布局修正 */
    .products-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 25px;
        padding: 30px;
        background: white;
        min-width: 800px;
    }

    .products-column {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .product-item {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        border-radius: 10px;
        transition: all 0.3s ease;
        cursor: pointer;
        background: white;
        border: 1px solid rgba(102, 126, 234, 0.1);
        text-decoration: none;
        color: inherit;
        margin-bottom: 8px;
    }

    .product-item:hover {
        background: rgba(102, 126, 234, 0.12);
        border-color: rgba(102, 126, 234, 0.25);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
        text-decoration: none;
        color: inherit;
    }

    .product-icon {
        width: 35px;
        height: 35px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        flex-shrink: 0;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .product-icon.purple {
        background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
        color: white;
    }

    .product-icon i {
        font-size: 18px;
        color: white;
        line-height: 1;
        width: auto;
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    /* Lucide图标样式 */
    .product-icon svg {
        width: 18px;
        height: 18px;
        color: white;
        stroke: white;
        stroke-width: 2;
    }

    .product-item:hover .product-icon {
        transform: scale(1.1);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.35);
    }

    .product-item:hover .product-icon i {
        transform: translate(-50%, -50%) scale(1);
    }

    .product-name {
        font-size: 0.9rem;
        font-weight: 500;
        color: #333;
        line-height: 1.4;
        transition: color 0.3s ease;
    }

    .product-item:hover .product-name {
        color: #667eea;
    }

    .product-item-placeholder {
        padding: 12px 15px;
        color: #999;
        font-size: 0.9rem;
        text-align: center;
        font-style: italic;
    }

    /* 菜单底部 */
    .mega-menu-footer {
        text-align: center;
        padding-top: 25px;
        border-top: 1px solid rgba(0, 0, 0, 0.08);
        margin-top: 20px;
    }

    .mega-menu-footer .btn {
        padding: 12px 30px;
        font-size: 0.95rem;
        font-weight: 600;
        border-radius: 25px;
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        color: white;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.25);
    }

    .mega-menu-footer .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(0, 123, 255, 0.35);
        background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
        text-decoration: none;
        color: white;
    }

    /* ===== 产品介绍菜单样式 ===== */
    .products-menu {
        padding: 0;
        z-index: 10000 !important; /* 比解决方案菜单更高的z-index */
    }

    .products-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 30px;
        padding: 30px;
        background: white;
        min-width: 800px;
    }

    .products-column {
        display: flex;
        flex-direction: column;
    }

    .mega-menu-category {
        font-size: 1.1rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #667eea;
        text-align: center;
    }

    .product-item {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        border-radius: 8px;
        transition: all 0.3s ease;
        text-decoration: none;
        color: #333;
        margin-bottom: 8px;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
    }

    .product-item:hover {
        background: white;
        transform: translateX(5px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.15);
        text-decoration: none;
        color: #667eea;
        border-color: #667eea;
    }

    .product-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        transition: all 0.3s ease;
        flex-shrink: 0;
        position: relative;
        overflow: hidden;
    }

    .product-icon.purple {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .product-icon i {
        font-size: 18px;
        line-height: 1;
        width: auto;
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .product-item:hover .product-icon {
        transform: scale(1.1);
    }

    .product-item:hover .product-icon i {
        transform: translate(-50%, -50%) scale(1);
    }

    .product-name {
        font-size: 0.95rem;
        font-weight: 600;
        color: inherit;
    }

    .product-item-placeholder {
        padding: 12px 15px;
        color: #999;
        font-size: 0.9rem;
        text-align: center;
        font-style: italic;
    }

    /* ===== 导航栏基础样式增强 ===== */
    .header {
        position: relative;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    }

    .navbar {
        position: relative;
        z-index: 1001;
    }

    /* 确保所有下拉菜单都有足够高的z-index */
    .mega-dropdown .dropdown-menu {
        z-index: 10001 !important;
    }

    .solutions-menu {
        z-index: 10002 !important;
    }

    .products-menu {
        z-index: 10003 !important;
    }

    /* 确保三角形指示器也有正确的z-index */
    .mega-dropdown .dropdown-menu::before {
        z-index: 10004;
    }

    .mega-dropdown .dropdown-menu::after {
        z-index: 10003;
    }

    /* ===== 三级菜单样式 ===== */
    .dropdown-submenu {
        position: relative;
    }

    .dropdown-submenu .dropdown-menu {
        position: absolute;
        top: 0;
        left: 100%;
        margin-top: -1px;
        display: none;
        min-width: 200px;
    }

    .dropdown-submenu:hover .dropdown-menu {
        display: block;
    }

    .dropdown-submenu .dropdown-toggle::after {
        content: "▶";
        float: right;
        margin-top: 2px;
        border: none;
    }

    /* 响应式处理 */
    @media (max-width: 991px) {
        .dropdown-submenu .dropdown-menu {
            position: static;
            float: none;
            width: auto;
            margin-top: 0;
            background-color: #f8f9fa;
            border: none;
            box-shadow: none;
            padding-left: 20px;
        }
    }
    </style>
</head>
<body class="<?php echo htmlentities((string) (isset($bodyClass) && ($bodyClass !== '')?$bodyClass:'home-page')); ?>">
    <!-- 导航栏 -->
    <header class="header">
        <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="/">
                <img src="<?php echo asset('assets/images/logo.png'); ?>" alt="<?php echo htmlentities((string) (isset($siteConfig['site_name']) && ($siteConfig['site_name'] !== '')?$siteConfig['site_name']:'三只鱼网络')); ?>" height="40">
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto" id="main-nav">
                    <!-- 动态菜单将在这里加载 -->
                </ul>


                <!-- 右侧按钮组 -->
                <div class="navbar-actions ms-3">
                    <a href="#" class="search-btn me-2"><i data-lucide="search"></i></a>
                    <a href="/login" class="btn btn-outline-light btn-sm me-2">登录</a>
                    <a href="/register" class="btn btn-primary btn-sm">注册</a>
                </div>
            </div>
        </div>
    </nav>
</header>

    <!-- 主要内容 -->
    <main>

<script>
// 菜单加载 - 添加调试信息

// URL格式化函数
function formatUrl(url) {
    if (!url || url === '#') {
        return '#';
    }

    // 转换为字符串并清理
    url = String(url).trim();

    // 如果是完整的URL（包含协议），直接返回
    if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
    }

    // 如果已经是正确的相对路径（以/开头），直接返回
    if (url.startsWith('/')) {
        return url;
    }

    // 如果是相对路径，添加/前缀
    return '/' + url;
}

document.addEventListener('DOMContentLoaded', function() {
    loadMenus();
});

function loadMenus() {
    fetch('/api/sys-menus')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {

            if (data && data.success && data.data) {
                renderMenus(data.data);
            } else {
            }
        })
        .catch(error => {
            console.error('菜单加载失败:', error);
            console.error('错误详情:', error.message);
        });
}

function renderMenus(menus) {
    const nav = document.getElementById('main-nav');
    if (!nav) {
        console.error('找不到导航容器 #main-nav');
        return;
    }

    // 清空现有菜单（保留首页）
    const existing = nav.querySelectorAll('li:not(:first-child)');
    existing.forEach(item => item.remove());

    // 添加新菜单
    let hasMorePages = false;
    let hasContact = false;
    
    menus.forEach((menu, index) => {
        const li = createMenuItem(menu);
        nav.appendChild(li);
        
        // 检查是否已经有"更多页面"和"联系我们"菜单
        if (menu.name === '更多页面') {
            hasMorePages = true;
            // 为动态的"更多页面"菜单添加id，以便DIY页面加载
            const dropdownMenu = li.querySelector('.dropdown-menu');
            if (dropdownMenu && !dropdownMenu.id) {
                dropdownMenu.id = 'diy-pages-menu';
            }
        }
        if (menu.name === '联系我们') {
            hasContact = true;
        }
    });

    // 只有当动态菜单中没有"更多页面"时才添加固定的"更多页面"菜单
    if (!hasMorePages) {
        const morePages = document.createElement('li');
        morePages.className = 'nav-item dropdown';
        morePages.innerHTML = `
            <a class="nav-link dropdown-toggle" href="#" id="diyPagesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                更多页面
            </a>
            <ul class="dropdown-menu" aria-labelledby="diyPagesDropdown" id="diy-pages-menu">
            </ul>
        `;
        nav.appendChild(morePages);

        // 为"更多页面"添加悬停事件
        morePages.addEventListener('mouseenter', function() {
            const dropdown = morePages.querySelector('.dropdown-menu');
            if (dropdown) {
                dropdown.style.display = 'block';
                morePages.classList.add('show');
            }
        });

        morePages.addEventListener('mouseleave', function() {
            const dropdown = morePages.querySelector('.dropdown-menu');
            if (dropdown) {
                dropdown.style.display = 'none';
                morePages.classList.remove('show');
            }
        });
    }

    // 只有当动态菜单中没有"联系我们"时才添加固定的"联系我们"菜单
    if (!hasContact) {
        const contact = document.createElement('li');
        contact.className = 'nav-item';
        contact.innerHTML = '<a class="nav-link" href="/contact">联系我们</a>';
        nav.appendChild(contact);
    }


    // 初始化下拉菜单事件
    initDropdownEvents();

    // 确保Lucide图标正确渲染
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // 加载DIY页面到导航菜单
    loadDiyPagesToMenu();
    
}

function createMenuItem(menu) {
    const li = document.createElement('li');
    const url = formatUrl(menu.url || menu.link_value || '#');

    if (menu.children && menu.children.length > 0) {
        // 检查是否是特殊的mega-dropdown菜单
        if (menu.name === '解决方案') {
            return createSolutionsMenuItem(menu, url);
        } else if (menu.name === '产品介绍') {
            return createProductsMenuItem(menu, url);
        } else {
            // 普通下拉菜单
            li.className = 'nav-item dropdown';

            // 创建主链接
            const mainLink = document.createElement('a');
            mainLink.className = 'nav-link dropdown-toggle';
            mainLink.href = url;
            mainLink.setAttribute('role', 'button');
            mainLink.setAttribute('aria-expanded', 'false');
            mainLink.textContent = menu.name;

            // 创建下拉菜单
            const dropdownMenu = document.createElement('ul');
            dropdownMenu.className = 'dropdown-menu';
            dropdownMenu.innerHTML = renderSubMenus(menu.children);

            li.appendChild(mainLink);
            li.appendChild(dropdownMenu);

            // 添加悬停事件
            li.addEventListener('mouseenter', function() {
                const dropdown = li.querySelector('.dropdown-menu');
                if (dropdown) {
                    dropdown.style.display = 'block';
                    li.classList.add('show');
                }
            });

            li.addEventListener('mouseleave', function() {
                const dropdown = li.querySelector('.dropdown-menu');
                if (dropdown) {
                    dropdown.style.display = 'none';
                    li.classList.remove('show');
                }
            });

        }
    } else {
        // 普通菜单项
        li.className = 'nav-item';

        const link = document.createElement('a');
        link.className = 'nav-link';
        link.href = url;
        link.textContent = menu.name;
        li.appendChild(link);
    }

    return li;
}

// 创建解决方案菜单项
function createSolutionsMenuItem(menu, url) {
    const li = document.createElement('li');
    li.className = 'nav-item mega-dropdown';

    const mainLink = document.createElement('a');
    mainLink.className = 'nav-link dropdown-toggle';
    mainLink.href = url;
    mainLink.id = 'solutionsDropdown';
    mainLink.setAttribute('role', 'button');
    mainLink.setAttribute('aria-expanded', 'false');
    mainLink.textContent = menu.name;

    const dropdownMenu = document.createElement('div');
    dropdownMenu.className = 'dropdown-menu solutions-menu';
    
    // 动态生成解决方案网格
    let solutionsHtml = '<div class="solutions-grid">';
    
    if (menu.children && menu.children.length > 0) {
        // 使用真实的子菜单数据
        menu.children.forEach(solution => {
            const solutionUrl = formatUrl(solution.url || solution.link_value || '#');
            const iconPath = solution.icon || '/assets/images/nav/default.png';
            const description = solution.description || solution.remark || '专业的解决方案';
            
            solutionsHtml += `
                <a href="${solutionUrl}" class="solution-item">
                    <div class="solution-icon">
                        <img src="${iconPath}" alt="${solution.name}">
                    </div>
                    <div class="solution-content">
                        <h4>${solution.name}</h4>
                        <p>${description}</p>
                    </div>
                </a>
            `;
        });
    }
    
    solutionsHtml += '</div>';
    dropdownMenu.innerHTML = solutionsHtml;

    li.appendChild(mainLink);
    li.appendChild(dropdownMenu);

    // 添加mega-dropdown悬停事件
    addMegaDropdownEvents(li);

    return li;
}

// 创建产品介绍菜单项
function createProductsMenuItem(menu, url) {
    const li = document.createElement('li');
    li.className = 'nav-item mega-dropdown';

    const mainLink = document.createElement('a');
    mainLink.className = 'nav-link dropdown-toggle';
    mainLink.href = url;
    mainLink.id = 'productsDropdown';
    mainLink.setAttribute('role', 'button');
    mainLink.setAttribute('aria-expanded', 'false');
    mainLink.textContent = menu.name;

    const dropdownMenu = document.createElement('div');
    dropdownMenu.className = 'dropdown-menu products-menu';
    
    // 动态生成产品网格
    let productsHtml = '<div class="products-grid">';
    
    if (menu.children && menu.children.length > 0) {
        // 使用真实的子菜单数据，每个子菜单作为一个分类
        menu.children.forEach(category => {
            productsHtml += '<div class="products-column">';
            productsHtml += `<h6 class="mega-menu-category">${category.name}</h6>`;
            
            // 如果分类有子项目（产品），显示产品列表
            if (category.children && category.children.length > 0) {
                category.children.forEach(product => {
                    const productUrl = formatUrl(product.url || product.link_value || '#');
                    const productIcon = product.icon || 'fas fa-box';
                    
                    productsHtml += `
                        <a href="${productUrl}" class="product-item">
                            <div class="product-icon purple">
                                <i class="${productIcon}"></i>
                            </div>
                            <span class="product-name">${product.name}</span>
                        </a>
                    `;
                });
            } else {
                // 如果分类没有子产品，将分类本身作为产品显示
                const categoryUrl = formatUrl(category.url || category.link_value || '#');
                const categoryIcon = category.icon || 'fas fa-box';
                
                productsHtml += `
                    <a href="${categoryUrl}" class="product-item">
                        <div class="product-icon purple">
                            <i class="${categoryIcon}"></i>
                        </div>
                        <span class="product-name">${category.name}</span>
                    </a>
                `;
            }
            
            productsHtml += '</div>';
        });
    }
    
    productsHtml += '</div>';
    dropdownMenu.innerHTML = productsHtml;

    li.appendChild(mainLink);
    li.appendChild(dropdownMenu);

    // 添加mega-dropdown悬停事件
    addMegaDropdownEvents(li);

    return li;
}

// 添加mega-dropdown悬停事件
function addMegaDropdownEvents(dropdown) {
    const menu = dropdown.querySelector('.dropdown-menu');
    const toggle = dropdown.querySelector('.dropdown-toggle');
    let hoverTimer = null;
    let isMenuHovered = false;
    let isToggleHovered = false;
    
    // 显示菜单的函数
    function showMenu() {
        if (menu) {
            clearTimeout(hoverTimer);
            menu.style.display = 'block';
            menu.style.opacity = '1';
            menu.style.visibility = 'visible';
            menu.style.pointerEvents = 'auto';
            dropdown.classList.add('show');
            toggle.setAttribute('aria-expanded', 'true');
        }
    }
    
    // 隐藏菜单的函数
    function hideMenu() {
        if (menu) {
            hoverTimer = setTimeout(function() {
                if (!isMenuHovered && !isToggleHovered) {
                    menu.style.display = 'none';
                    menu.style.opacity = '0';
                    menu.style.visibility = 'hidden';
                    menu.style.pointerEvents = 'none';
                    dropdown.classList.remove('show');
                    toggle.setAttribute('aria-expanded', 'false');
                }
            }, 150);
        }
    }
    
    // 触发器悬停事件
    toggle.addEventListener('mouseenter', function() {
        isToggleHovered = true;
        showMenu();
    });
    
    toggle.addEventListener('mouseleave', function() {
        isToggleHovered = false;
        hideMenu();
    });
    
    // 菜单悬停事件
    if (menu) {
        menu.addEventListener('mouseenter', function() {
            isMenuHovered = true;
            clearTimeout(hoverTimer);
        });
        
        menu.addEventListener('mouseleave', function() {
            isMenuHovered = false;
            hideMenu();
        });
    }
    
    // 点击触发器时阻止默认行为
    toggle.addEventListener('click', function(e) {
        e.preventDefault();
        if (menu.style.display === 'block') {
            hideMenu();
            isToggleHovered = false;
            isMenuHovered = false;
        } else {
            showMenu();
        }
    });
}

function renderSubMenus(children) {
    const items = children.map(child => {
        const url = formatUrl(child.url || child.link_value || '#');

        const li = document.createElement('li');

        if (child.children && child.children.length > 0) {
            // 有三级菜单的二级菜单项
            li.className = 'dropdown-submenu';

            const link = document.createElement('a');
            link.className = 'dropdown-item';
            link.href = url;
            link.textContent = child.name + ' ▶';

            const submenu = document.createElement('ul');
            submenu.className = 'dropdown-menu submenu';

            child.children.forEach(grandChild => {
                const grandUrl = formatUrl(grandChild.url || grandChild.link_value || '#');
                const grandLi = document.createElement('li');
                const grandLink = document.createElement('a');
                grandLink.className = 'dropdown-item';
                grandLink.href = grandUrl;
                grandLink.textContent = grandChild.name;
                grandLi.appendChild(grandLink);
                submenu.appendChild(grandLi);

            });

            li.appendChild(link);
            li.appendChild(submenu);

        } else {
            // 普通二级菜单项
            const link = document.createElement('a');
            link.className = 'dropdown-item';
            link.href = url;
            link.textContent = child.name;
            li.appendChild(link);

        }

        return li.outerHTML;
    });

    return items.join('');
}

function initDropdownEvents() {
    // Bootstrap下拉菜单自动处理，但需要处理三级菜单
    document.querySelectorAll('.dropdown-submenu').forEach(submenu => {
        const toggle = submenu.querySelector('.dropdown-item');
        const menu = submenu.querySelector('.dropdown-menu');

        if (toggle && menu) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // 切换显示状态
                menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
            });

            // 鼠标悬停显示三级菜单
            submenu.addEventListener('mouseenter', function() {
                menu.style.display = 'block';
            });

            submenu.addEventListener('mouseleave', function() {
                menu.style.display = 'none';
            });
        }
    });

    // 点击页面其他地方时关闭所有mega-dropdown菜单
    document.addEventListener('click', function(e) {
        // 检查点击的目标是否在下拉菜单内
        const isDropdownClick = e.target.closest('.mega-dropdown');
        
        if (!isDropdownClick) {
            const megaDropdowns = document.querySelectorAll('.mega-dropdown');
            megaDropdowns.forEach(function(dropdown) {
                const menu = dropdown.querySelector('.dropdown-menu');
                const toggle = dropdown.querySelector('.dropdown-toggle');
                
                if (menu) {
                    menu.style.display = 'none';
                    menu.style.opacity = '0';
                    menu.style.visibility = 'hidden';
                    menu.style.pointerEvents = 'none';
                    dropdown.classList.remove('show');
                    toggle.setAttribute('aria-expanded', 'false');
                }
            });
        }
    });
}

// 加载DIY页面到导航菜单
function loadDiyPagesToMenu() {


    // 获取菜单容器
    const menuContainer = document.getElementById('diy-pages-menu');
    if (!menuContainer) {
        console.error('未找到DIY页面菜单容器');
        return;
    }

    // 发起API请求
    fetch('/api/diy-pages')
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败: ' + response.status);
            }
            return response.json();
        })
        .then(data => {

            if (data.success && data.pages && data.pages.length > 0) {
                // 清空现有的DIY页面项（保留默认的"关于我们"）
                const existingDiyItems = menuContainer.querySelectorAll('.diy-page-item');
                existingDiyItems.forEach(item => item.remove());

                // 不添加分隔线，保持菜单简洁

                // 添加DIY页面链接
                data.pages.forEach(page => {
                    const li = document.createElement('li');
                    li.className = 'diy-page-item';
                    li.innerHTML = `
                        <a class="dropdown-item" href="/page/${page.slug}" title="${page.description || page.name}">
                            <i class="fas fa-file-alt me-2"></i>
                            ${page.name}
                        </a>
                    `;
                    menuContainer.appendChild(li);
                });

            } else {

            }
        })
        .catch(error => {
            console.error('加载DIY页面失败:', error);

            // 在菜单中显示错误提示（仅在开发环境）
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                const li = document.createElement('li');
                li.innerHTML = '<span class="dropdown-item text-muted">加载页面失败</span>';
                menuContainer.appendChild(li);
            }
        });
}
</script>
<!-- 公共样式文件 -->
<style>
/* ===== Header和Navbar样式 ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar {
    padding: 12px 0 !important;
    background: transparent !important;
    border: none !important;
    transition: background 0.3s ease, backdrop-filter 0.3s ease !important;
    min-height: 70px !important;
    display: flex !important;
    align-items: center !important;
}

/* 确保navbar容器正确布局 */
.navbar .container {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    width: 100% !important;
}

/* 滚动时header背景 */
header.scrolled {
    background: rgba(47, 76, 153, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    box-shadow: 0 2px 20px rgba(47, 76, 153, 0.3) !important;
}

/* 确保navbar也有背景 */
header.scrolled .navbar,
header.scrolled .navbar-expand-lg,
header.scrolled .navbar-light {
    background: #2f4c99f5 !important;
    background-color: #2f4c99f5 !important;
}

.navbar-brand {
    font-size: 1.6rem !important;
    font-weight: 600 !important;
    color: white !important;
    text-decoration: none !important;
    display: flex !important;
    align-items: center !important;
    background: none !important;
    -webkit-background-clip: unset !important;
    -webkit-text-fill-color: white !important;
    background-clip: unset !important;
}

.navbar-brand .logo {
    height: 45px !important;
    width: auto !important;
    margin-right: 10px !important;
    filter: brightness(0) invert(1) !important;
    transition: all 0.3s ease !important;
    vertical-align: middle !important;
}

/* 滚动时Logo颜色变化 */
header.scrolled .navbar-brand .logo {
    filter: none !important;
}

/* 导航菜单容器对齐 */
.navbar-nav {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    margin: 0 !important;
    list-style: none !important;
    padding: 0 !important;
}

/* 导航链接样式 */
.navbar-nav .nav-item {
    margin: 0 5px !important;
}

.navbar-nav .nav-link {
    color: white !important;
    font-weight: 400 !important;
    padding: 12px 20px !important;
    position: relative !important;
    transition: color 0.3s ease !important;
    font-size: 15px !important;
    background: none !important;
    border: none !important;
    display: flex !important;
    align-items: center !important;
}

/* 导航链接悬停效果 */
.navbar-nav .nav-link:hover {
    color: #ff6b35 !important;
}

/* 导航链接下划线效果 - 排除下拉菜单项 */
.navbar-nav .nav-link:not(.dropdown-toggle)::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:not(.dropdown-toggle):hover::after {
    width: 80%;
}

/* 激活状态的导航链接样式 */
.navbar-nav .nav-link.active {
    color: #ff6b35 !important;
}

.navbar-nav .nav-link.active:not(.dropdown-toggle)::after {
    width: 80%;
    background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
}

/* 右侧按钮组对齐 */
.navbar-actions {
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
    margin-left: 20px !important;
    height: 45px !important;
    flex-shrink: 0 !important;
}

.search-btn {
    color: white !important;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    text-decoration: none;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white !important;
    text-decoration: none;
}

.search-btn svg {
    width: 16px;
    height: 16px;
    stroke: currentColor;
    stroke-width: 2;
}

/* 登录注册按钮样式 */
.navbar-actions .btn {
    padding: 8px 18px !important;
    font-size: 14px !important;
    border-radius: 4px !important;
    font-weight: 400 !important;
    transition: color 0.3s ease, background 0.3s ease, border-color 0.3s ease !important;
    text-decoration: none !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.navbar-actions .btn-outline-light {
    color: white !important;
    background: transparent !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
}

.navbar-actions .btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: white !important;
}

.navbar-actions .btn-primary {
    background: #ff6b35 !important;
    border-color: #ff6b35 !important;
    color: white !important;
}

.navbar-actions .btn-primary:hover {
    background: #e55a2b !important;
    border-color: #e55a2b !important;
}

/* 滚动时按钮样式调整 */
header.scrolled .navbar-actions .btn-outline-light {
    color: white !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
}

header.scrolled .navbar-actions .btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: white !important;
}

header.scrolled .navbar-actions .btn-primary {
    background: #ff6b35 !important;
    border-color: #ff6b35 !important;
    color: white !important;
}

header.scrolled .navbar-actions .btn-primary:hover {
    background: #e55a2b !important;
    border-color: #e55a2b !important;
    color: white !important;
}

/* 强制确保滚动时按钮样式不被覆盖 */
header.scrolled .btn-primary,
header.scrolled .navbar-actions .btn-primary,
header.scrolled .navbar .btn-primary {
    background: #ff6b35 !important;
    background-color: #ff6b35 !important;
    border-color: #ff6b35 !important;
    color: white !important;
}

header.scrolled .btn-primary:hover,
header.scrolled .navbar-actions .btn-primary:hover,
header.scrolled .navbar .btn-primary:hover {
    background: #e55a2b !important;
    background-color: #e55a2b !important;
    border-color: #e55a2b !important;
    color: white !important;
}

/* 移动端菜单按钮样式 */
.navbar-toggler {
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    padding: 4px 8px !important;
    transition: all 0.3s ease !important;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
    transition: all 0.3s ease !important;
}

/* 确保主要内容不被固定header遮挡 */
main {
    padding-top: 70px;
}

/* 首页轮播图需要全屏显示，移除顶部间距 */
.home-page main {
    padding-top: 0;
}

/* ===== 产品菜单特殊样式 ===== */
.products-menu {
    padding: 0;
}

.products-header {
    background: #f8f9fa;
    padding: 25px 30px;
    border-bottom: 1px solid #e9ecef;
    border-radius: 16px 16px 0 0;
}

/* 产品网格布局 */
.products-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
    padding: 30px;
}

.products-column {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.product-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-radius: 10px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
    border: 1px solid rgba(102, 126, 234, 0.1);
    text-decoration: none;
    color: inherit;
}

.product-item:hover {
    background: rgba(102, 126, 234, 0.12);
    border-color: rgba(102, 126, 234, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
    text-decoration: none;
    color: inherit;
}

.product-icon {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.product-icon.purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
}

.product-icon i {
    font-size: 18px;
    color: white;
}

/* Lucide图标样式 */
.product-icon svg {
    width: 18px;
    height: 18px;
    color: white;
    stroke: white;
    stroke-width: 2;
}

.product-item:hover .product-icon {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.35);
}

.product-name {
    font-size: 0.9rem;
    font-weight: 500;
    color: #333;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.product-item:hover .product-name {
    color: #667eea;
}

/* ===== 菜单分类标题 ===== */
.mega-menu-category {
    font-size: 1.1rem;
    font-weight: 600;
    color: #007bff;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid rgba(0, 123, 255, 0.2);
    position: relative;
}

.mega-menu-category::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 30px;
    height: 2px;
    background: #007bff;
    border-radius: 1px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .solutions-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 900px) {
    .solutions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 600px) {
    .solutions-grid {
        grid-template-columns: 1fr;
    }
    .products-grid {
        grid-template-columns: 1fr;
    }
}
</style>


<!-- 产品页面样式 -->
<link rel="stylesheet" href="<?php echo asset('assets/css/products.css'); ?>?v=7.1">

<!-- 产品页面头部横幅 -->
<section class="products-hero page-top-spacing">
    <div class="container">
        <div class="hero-content">
            <div class="hero-badge">
                <i data-lucide="package"></i>
                <?php if($currentCategory): ?>
                    <?php echo htmlentities((string) $currentCategory['name']); else: ?>
                    产品中心
                <?php endif; ?>
            </div>
            <h1 class="hero-title">
                <?php if($currentCategory): ?>
                    <?php echo htmlentities((string) $currentCategory['name']); else: ?>
                    核心产品与解决方案
                <?php endif; ?>
            </h1>
            <p class="hero-subtitle">
                <?php if($currentCategory && $currentCategory['description']): ?>
                    <?php echo htmlentities((string) $currentCategory['description']); else: ?>
                    专业的企业级产品和技术解决方案，助力企业数字化转型升级
                <?php endif; ?>
            </p>
            <div class="hero-stats">
                <div class="hero-stat">
                    <span class="hero-stat-number"><?php echo htmlentities((string) $products->total()); ?></span>
                    <span class="hero-stat-label">产品总数</span>
                </div>
                <div class="hero-stat">
                    <span class="hero-stat-number"><?php echo count($categories); ?></span>
                    <span class="hero-stat-label">产品分类</span>
                </div>
                <div class="hero-stat">
                    <span class="hero-stat-number"><?php echo count($featuredProducts); ?></span>
                    <span class="hero-stat-label">推荐产品</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 主要内容区域 -->
<section class="products-main">
    <div class="container">
        <div class="products-container">
            <!-- 侧边栏筛选 -->
            <aside class="products-sidebar">
                <div class="filter-card">
                    <!-- 分类筛选 -->
                    <div class="filter-section">
                        <h5 class="filter-title">
                            <i data-lucide="grid-3x3"></i>
                            产品分类
                        </h5>
                        <div class="filter-options">
                            <a href="/products" class="filter-option <?php if(!$currentCategory): ?>active<?php endif; ?>">
                                <i data-lucide="layers"></i>
                                全部产品
                            </a>
                            <?php if(is_array($categories) || $categories instanceof \think\Collection || $categories instanceof \think\Paginator): $i = 0; $__LIST__ = $categories;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$category): $mod = ($i % 2 );++$i;?>
                            <a href="/products?category=<?php echo htmlentities((string) $category['slug']); ?>" 
                               class="filter-option <?php if($currentCategory && $currentCategory['id'] == $category['id']): ?>active<?php endif; ?>">
                                <?php if($category['icon']): ?>
                                    <i class="<?php echo htmlentities((string) $category['icon']); ?>"></i>
                                <?php else: ?>
                                    <i data-lucide="tag"></i>
                                <?php endif; ?>
                                <?php echo htmlentities((string) $category['name']); ?>
                            </a>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </div>
                    </div>

                    <!-- 推荐产品 -->
                    <?php if($featuredProducts && count($featuredProducts) > 0): ?>
                    <div class="featured-section">
                        <h5 class="filter-title">
                            <i data-lucide="star"></i>
                            推荐产品
                        </h5>
                        <div class="featured-products">
                            <?php if(is_array($featuredProducts) || $featuredProducts instanceof \think\Collection || $featuredProducts instanceof \think\Paginator): $k = 0; $__LIST__ = $featuredProducts;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$featured): $mod = ($k % 2 );++$k;if($k <= 4): ?>
                            <a href="/products/<?php echo !empty($featured['slug']) ? htmlentities((string) $featured['slug']) : htmlentities((string) $featured['id']); ?>" class="featured-item">
                                <div class="featured-image">
                                    <?php if($featured['image']): ?>
                                        <img src="<?php echo htmlentities((string) $featured['image']); ?>" alt="<?php echo htmlentities((string) $featured['name']); ?>">
                                    <?php else: ?>
                                        <div class="no-image">
                                            <i data-lucide="package"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="featured-info">
                                    <div class="featured-name"><?php echo htmlentities((string) $featured['name']); ?></div>
                                    <?php if($featured['price'] > 0): ?>
                                        <div class="featured-price">¥<?php echo htmlentities((string) $featured['price']); ?></div>
                                    <?php endif; ?>
                                </div>
                            </a>
                            <?php endif; ?>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </aside>

            <!-- 产品内容区域 -->
            <main class="products-content">
                <!-- 工具栏 -->
                <div class="products-toolbar">
                    <div class="toolbar-left">
                        <div class="results-info">
                            找到 <span class="results-count"><?php echo htmlentities((string) $products->total()); ?></span> 个产品
                        </div>
                        <form class="search-form" method="get" action="/products/search">
                            <i data-lucide="search" class="search-icon"></i>
                            <input type="text" name="keyword" class="search-input" 
                                   placeholder="搜索产品名称、描述..." 
                                   value="<?php echo htmlentities((string) (app('request')->param('keyword') ?: '')); ?>">
                            <?php if($currentCategory): ?>
                                <input type="hidden" name="category_id" value="<?php echo htmlentities((string) $currentCategory['id']); ?>">
                            <?php endif; ?>
                            <button type="submit" class="search-btn"><i data-lucide="search"></i></button>
                        </form>
                    </div>
                    <div class="toolbar-right">
                        <div class="sort-select-wrapper">
                            <i data-lucide="arrow-up-down" class="sort-icon"></i>
                            <select class="sort-select" id="sortSelect">
                                <option value="/products<?php if($currentCategory): ?>?category=<?php echo htmlentities((string) $currentCategory['slug']); ?><?php endif; ?>" <?php if(!app('request')->param('sort')): ?>selected<?php endif; ?>>默认排序</option>
                                <option value="/products<?php if($currentCategory): ?>?category=<?php echo htmlentities((string) $currentCategory['slug']); ?>&<?php else: ?>?<?php endif; ?>sort=price_asc" <?php if(app('request')->param('sort') == "price_asc"): ?>selected<?php endif; ?>>价格：从高到低</option>
                                <option value="/products<?php if($currentCategory): ?>?category=<?php echo htmlentities((string) $currentCategory['slug']); ?>&<?php else: ?>?<?php endif; ?>sort=price_desc" <?php if(app('request')->param('sort') == "price_desc"): ?>selected<?php endif; ?>>价格：从低到高</option>
                                <option value="/products<?php if($currentCategory): ?>?category=<?php echo htmlentities((string) $currentCategory['slug']); ?>&<?php else: ?>?<?php endif; ?>sort=views_desc" <?php if(app('request')->param('sort') == "views_desc"): ?>selected<?php endif; ?>>热度排序</option>
                                <option value="/products<?php if($currentCategory): ?>?category=<?php echo htmlentities((string) $currentCategory['slug']); ?>&<?php else: ?>?<?php endif; ?>sort=created_desc" <?php if(app('request')->param('sort') == "created_desc"): ?>selected<?php endif; ?>>最新发布</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 产品网格 -->
                <?php if($products && count($products) > 0): ?>
                    <div class="products-page-grid">
                        <?php if(is_array($products) || $products instanceof \think\Collection || $products instanceof \think\Paginator): $i = 0; $__LIST__ = $products;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$product): $mod = ($i % 2 );++$i;?>
                        <article class="products-page-card">
                            <!-- 产品图片 -->
                            <div class="products-page-image">
                                <a href="/products/<?php echo !empty($product['slug']) ? htmlentities((string) $product['slug']) : htmlentities((string) $product['id']); ?>">
                                    <?php if($product['image']): ?>
                                        <img src="<?php echo htmlentities((string) $product['image']); ?>" alt="<?php echo htmlentities((string) $product['name']); ?>" loading="lazy">
                                    <?php else: ?>
                                        <div class="no-image">
                                            <i data-lucide="package"></i>
                                        </div>
                                    <?php endif; ?>
                                </a>
                                
                                <!-- 产品标识 -->
                                <div class="products-page-badges">
                                    <?php if($product['is_featured']): ?>
                                        <span class="products-page-badge badge-featured">推荐</span>
                                    <?php endif; if($product['is_hot']): ?>
                                        <span class="products-page-badge badge-hot">热门</span>
                                    <?php endif; if($product['is_new']): ?>
                                        <span class="products-page-badge badge-new">新品</span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- 产品信息 -->
                            <div class="products-page-info">
                                <?php if($product['category']): ?>
                                    <a href="/products?category=<?php echo htmlentities((string) $product['category']['slug']); ?>" class="products-page-category">
                                        <?php echo htmlentities((string) $product['category']['name']); ?>
                                    </a>
                                <?php endif; ?>
                                
                                <h3 class="products-page-name">
                                    <a href="/products/<?php echo !empty($product['slug']) ? htmlentities((string) $product['slug']) : htmlentities((string) $product['id']); ?>">
                                        <?php echo htmlentities((string) $product['name']); ?>
                                    </a>
                                </h3>
                                
                                <?php if($product['short_description']): ?>
                                    <p class="products-page-description"><?php echo htmlentities((string) $product['short_description']); ?></p>
                                <?php endif; ?>
                            </div>

                            <!-- 产品底部信息 -->
                            <div class="products-page-footer">
                                <div class="products-page-meta">
                                    <div class="products-page-price">
                                        <?php if($product['price'] > 0): ?>
                                            <span class="current-price">¥<?php echo htmlentities((string) $product['price']); ?></span>
                                            <?php if($product['original_price'] > $product['price']): ?>
                                                <span class="original-price">¥<?php echo htmlentities((string) $product['original_price']); ?></span>
                                            <?php endif; else: ?>
                                            <span class="current-price">面议</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="products-page-stats">
                                        <div class="stat-item">
                                            <i data-lucide="eye"></i>
                                            <span><?php echo htmlentities((string) (isset($product['views']) && ($product['views'] !== '')?$product['views']:0)); ?></span>
                                        </div>
                                        <div class="stat-item">
                                            <i data-lucide="heart"></i>
                                            <span><?php echo htmlentities((string) (isset($product['likes']) && ($product['likes'] !== '')?$product['likes']:0)); ?></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="products-page-actions">
                                    <a href="/products/<?php echo !empty($product['slug']) ? htmlentities((string) $product['slug']) : htmlentities((string) $product['id']); ?>" class="btn-primary-outline">
                                        <i data-lucide="eye"></i>
                                        查看详情
                                    </a>
                                    <a href="/contact?product=<?php echo htmlentities((string) $product['id']); ?>" class="btn-secondary-outline" title="咨询产品">
                                        <i data-lucide="message-circle"></i>
                                    </a>
                                </div>
                            </div>
                        </article>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </div>

                    <!-- 分页 -->
                    <?php if($products->hasPages()): ?>
                        <div class="pagination-wrapper">
                            <?php echo htmlentities((string) $products->render()); ?>
                        </div>
                    <?php endif; else: ?>
                    <!-- 空状态 -->
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i data-lucide="package-x"></i>
                        </div>
                        <h3 class="empty-title">暂无产品</h3>
                        <p class="empty-description">
                            <?php if($currentCategory): ?>
                                该分类下暂时没有产品，请查看其他分类或联系我们了解更多信息。
                            <?php else: ?>
                                我们正在努力添加更多优质产品，敬请期待！
                            <?php endif; ?>
                        </p>
                        <a href="/contact" class="empty-action">
                            <i data-lucide="phone"></i>
                            联系我们
                        </a>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>
</section>

<!-- 页面特定的JavaScript -->
<script>
// 确保页面加载时头部状态正确
document.addEventListener('DOMContentLoaded', function() {
    // 检查页面滚动位置并设置头部状态
    const header = document.querySelector('header');
    if (header && window.scrollY > 50) {
        header.classList.add('scrolled');
    }

    // 初始化Lucide图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // 初始化分类无痕刷新
    initCategoryNavigation();

    // 初始化排序无痕刷新
    initSortNavigation();
});

// 监听滚动事件，确保头部样式正确
window.addEventListener('scroll', function() {
    const header = document.querySelector('header');
    if (header) {
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    }
});

// 分类导航无痕刷新功能
function initCategoryNavigation() {
    const filterOptions = document.querySelectorAll('.filter-option');

    filterOptions.forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const url = this.getAttribute('href');
            const currentActive = document.querySelector('.filter-option.active');

            // 如果点击的是当前激活的分类，不做任何操作
            if (this.classList.contains('active')) {
                return;
            }

            // 添加加载状态
            this.style.opacity = '0.7';
            this.style.pointerEvents = 'none';

            // 显示加载指示器
            showLoadingIndicator();

            // 使用fetch进行无痕刷新
            fetch(url, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.text())
            .then(html => {
                // 更新产品内容
                updateProductContent(html, false);

                // 更新分类激活状态
                if (currentActive) {
                    currentActive.classList.remove('active');
                }
                this.classList.add('active');

                // 更新浏览器URL
                window.history.pushState({}, '', url);

                // 滚动到产品区域
                const productsContent = document.querySelector('.products-content');
                if (productsContent) {
                    productsContent.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            })
            .catch(error => {
                console.error('加载失败:', error);
                // 如果AJAX失败，回退到正常页面跳转
                window.location.href = url;
            })
            .finally(() => {
                // 恢复链接状态
                this.style.opacity = '';
                this.style.pointerEvents = '';

                // 隐藏加载指示器
                hideLoadingIndicator();
            });
        });
    });
}

// 显示加载指示器
function showLoadingIndicator() {
    const productsGrid = document.querySelector('.products-page-grid');
    if (productsGrid) {
        productsGrid.style.opacity = '0.5';
        productsGrid.style.pointerEvents = 'none';
    }

    // 添加加载动画
    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'loading-overlay';
    loadingDiv.innerHTML = `
        <div class="loading-spinner">
            <i data-lucide="loader-2"></i>
            <span>加载中...</span>
        </div>
    `;

    const productsContent = document.querySelector('.products-content');
    if (productsContent) {
        productsContent.appendChild(loadingDiv);

        // 初始化加载图标
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }
}

// 隐藏加载指示器
function hideLoadingIndicator() {
    const productsGrid = document.querySelector('.products-page-grid');
    if (productsGrid) {
        productsGrid.style.opacity = '';
        productsGrid.style.pointerEvents = '';
    }

    const loadingOverlay = document.querySelector('.loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.remove();
    }
}

// 排序无痕刷新功能
function initSortNavigation() {
    const sortSelect = document.getElementById('sortSelect');

    if (sortSelect) {
        sortSelect.addEventListener('change', function(e) {
            const url = this.value;
            const sortWrapper = this.closest('.sort-select-wrapper');

            // 添加加载状态
            if (sortWrapper) {
                sortWrapper.classList.add('loading');
            }
            this.disabled = true;

            // 显示加载指示器
            showLoadingIndicator();

            // 使用fetch进行无痕刷新
            fetch(url, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.text())
            .then(html => {
                // 更新产品内容（带过渡效果）
                updateProductContent(html, true);

                // 更新浏览器URL
                window.history.pushState({}, '', url);

                // 滚动到产品区域
                const productsContent = document.querySelector('.products-content');
                if (productsContent) {
                    productsContent.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            })
            .catch(error => {
                console.error('排序加载失败:', error);
                // 如果AJAX失败，回退到正常页面跳转
                window.location.href = url;
            })
            .finally(() => {
                // 恢复选择器状态
                const sortWrapper = this.closest('.sort-select-wrapper');
                if (sortWrapper) {
                    sortWrapper.classList.remove('loading');
                }
                this.disabled = false;

                // 隐藏加载指示器
                hideLoadingIndicator();
            });
        });
    }
}

// 通用的内容更新函数
function updateProductContent(html, showTransition = false) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    // 更新产品网格
    const newProductsGrid = doc.querySelector('.products-page-grid');
    const currentProductsGrid = document.querySelector('.products-page-grid');

    if (newProductsGrid && currentProductsGrid) {
        if (showTransition) {
            currentProductsGrid.style.transition = 'opacity 0.3s ease';
            currentProductsGrid.style.opacity = '0';

            setTimeout(() => {
                currentProductsGrid.innerHTML = newProductsGrid.innerHTML;
                currentProductsGrid.style.opacity = '1';
            }, 150);
        } else {
            currentProductsGrid.innerHTML = newProductsGrid.innerHTML;
        }
    }

    // 更新结果信息
    const newResultsInfo = doc.querySelector('.results-info');
    const currentResultsInfo = document.querySelector('.results-info');

    if (newResultsInfo && currentResultsInfo) {
        currentResultsInfo.innerHTML = newResultsInfo.innerHTML;
    }

    // 更新分页
    const newPagination = doc.querySelector('.pagination-wrapper');
    const currentPagination = document.querySelector('.pagination-wrapper');

    if (newPagination && currentPagination) {
        currentPagination.innerHTML = newPagination.innerHTML;
    } else if (!newPagination && currentPagination) {
        currentPagination.style.display = 'none';
    } else if (newPagination && !currentPagination) {
        const productsContent = document.querySelector('.products-content');
        if (productsContent) {
            productsContent.appendChild(newPagination);
        }
    }

    // 重新初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}
</script>

</main>

    <!-- 页脚 -->
    <footer class="footer">
        <!-- 主要页脚内容 -->
        <div class="footer-main">
            <div class="container">
                <div class="row">
                    <!-- 公司信息 -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="footer-widget">
                            <div class="footer-logo">
                                <img src="<?php echo asset('assets/images/logo.png'); ?>" alt="<?php echo htmlentities((string) (isset($siteConfig['site_name']) && ($siteConfig['site_name'] !== '')?$siteConfig['site_name']:'三只鱼网络')); ?>">
                            </div>
                            <p class="footer-desc">
                                <?php echo htmlentities((string) (isset($siteConfig['site_description']) && ($siteConfig['site_description'] !== '')?$siteConfig['site_description']:'我们提供专业的企业解决方案，助力企业数字化转型，打造专业品牌形象，提供一站式服务支持。')); ?>
                            </p>
                            
                            <!-- 微信二维码 -->
                            <div class="footer-qrcode">
                                <img src="<?php echo asset('assets/images/weixin.png'); ?>" alt="微信二维码" class="qrcode-img">
                                <p class="qrcode-text">扫码关注微信</p>
                            </div>
                            
                            <div class="footer-social">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 解决方案 -->
                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">解决方案</h5>
                            <ul class="footer-links">
                                <?php if(is_array($footerSolutions) || $footerSolutions instanceof \think\Collection || $footerSolutions instanceof \think\Paginator): $i = 0; $__LIST__ = $footerSolutions;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$solution): $mod = ($i % 2 );++$i;?>
                                <li><a href="/solutions/<?php echo htmlentities((string) $solution['link_value']); ?>"><?php echo htmlentities((string) $solution['name']); ?></a></li>
                                <?php endforeach; endif; else: echo "" ;endif; if(empty($footerSolutions) || (($footerSolutions instanceof \think\Collection || $footerSolutions instanceof \think\Paginator ) && $footerSolutions->isEmpty())): ?>
                                <li><a href="/solutions">暂无解决方案</a></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 产品分类 -->
                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">产品分类</h5>
                            <ul class="footer-links">
                                <?php if(is_array($footerProductCategories) || $footerProductCategories instanceof \think\Collection || $footerProductCategories instanceof \think\Paginator): $i = 0; $__LIST__ = $footerProductCategories;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$category): $mod = ($i % 2 );++$i;?>
                                <li><a href="/products?category=<?php echo htmlentities((string) $category['slug']); ?>"><?php echo htmlentities((string) $category['name']); ?></a></li>
                                <?php endforeach; endif; else: echo "" ;endif; if(empty($footerProductCategories) || (($footerProductCategories instanceof \think\Collection || $footerProductCategories instanceof \think\Paginator ) && $footerProductCategories->isEmpty())): ?>
                                <li><a href="/products">暂无产品分类</a></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 普通页面 -->
                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">帮助中心</h5>
                            <ul class="footer-links">
                                <?php if(is_array($footerDiyPages) || $footerDiyPages instanceof \think\Collection || $footerDiyPages instanceof \think\Paginator): $i = 0; $__LIST__ = $footerDiyPages;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$page): $mod = ($i % 2 );++$i;?>
                                <li><a href="/page/<?php echo htmlentities((string) $page['slug']); ?>"><?php echo htmlentities((string) $page['name']); ?></a></li>
                                <?php endforeach; endif; else: echo "" ;endif; if(empty($footerDiyPages) || (($footerDiyPages instanceof \think\Collection || $footerDiyPages instanceof \think\Paginator ) && $footerDiyPages->isEmpty())): ?>
                                <li><a href="/about">关于我们</a></li>
                                <li><a href="/contact">联系我们</a></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 联系信息 -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="footer-widget">
                            <h5 class="footer-title">联系我们</h5>
                            <div class="footer-contact">
                                <div class="contact-item">
                                    <i data-lucide="map-pin"></i>
                                    <span><?php echo htmlentities((string) (isset($siteConfig['company_address']) && ($siteConfig['company_address'] !== '')?$siteConfig['company_address']:'北京市朝阳区科技园区')); ?></span>
                                </div>
                                <div class="contact-item">
                                    <i data-lucide="phone"></i>
                                    <span><?php echo htmlentities((string) (isset($siteConfig['company_phone']) && ($siteConfig['company_phone'] !== '')?$siteConfig['company_phone']:'1800001111')); ?></span>
                                </div>
                                <div class="contact-item">
                                    <i data-lucide="mail"></i>
                                    <span><?php echo htmlentities((string) (isset($siteConfig['company_email']) && ($siteConfig['company_email'] !== '')?$siteConfig['company_email']:'<EMAIL>')); ?></span>
                                </div>
                                <div class="contact-item">
                                    <i data-lucide="message-square"></i>
                                    <span><?php echo htmlentities((string) (isset($siteConfig['company_qq']) && ($siteConfig['company_qq'] !== '')?$siteConfig['company_qq']:'*********')); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 版权信息 -->
        <div class="footer-bottom">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="copyright">
                            <p>&copy; <?php echo date('Y'); ?> <?php echo htmlentities((string) (isset($siteConfig['site_name']) && ($siteConfig['site_name'] !== '')?$siteConfig['site_name']:'三只鱼科技有限公司')); ?>. 版权所有</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="footer-links-bottom">
                            <a href="/privacy">隐私政策</a>
                            <a href="/terms">服务条款</a>
                            <a href="/sitemap">网站地图</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- 返回顶部按钮 -->
    <div class="back-to-top" id="backToTop">
        <i class="fa fa-angle-up"></i>
    </div>

    <!-- 在线客服 -->
    <div class="online-service">
        <div class="service-btn" id="serviceBtn">
            <i data-lucide="message-circle"></i>
            <span>在线客服</span>
        </div>
        <div class="service-panel" id="servicePanel">
            <div class="service-header">
                <h6>在线客服</h6>
                <button class="close-btn" id="closeService">&times;</button>
            </div>
            <div class="service-content">
                <div class="service-item">
                    <div class="service-icon qq-icon">
                        <i data-lucide="message-circle"></i>
                    </div>
                    <div class="service-info">
                        <h6>QQ咨询</h6>
                        <p><?php echo htmlentities((string) (isset($siteConfig['company_qq']) && ($siteConfig['company_qq'] !== '')?$siteConfig['company_qq']:'*********')); ?></p>
                    </div>
                    <a href="http://wpa.qq.com/msgrd?v=3&uin=<?php echo htmlentities((string) (isset($siteConfig['company_qq']) && ($siteConfig['company_qq'] !== '')?$siteConfig['company_qq']:'*********')); ?>&site=qq&menu=yes" target="_blank" class="btn btn-sm btn-primary">咨询</a>
                </div>
                <div class="service-item">
                    <div class="service-icon wechat-icon">
                        <i data-lucide="smartphone"></i>
                    </div>
                    <div class="service-info">
                        <h6>微信咨询</h6>
                        <p><?php echo htmlentities((string) (isset($siteConfig['company_wechat']) && ($siteConfig['company_wechat'] !== '')?$siteConfig['company_wechat']:'hsdj37')); ?></p>
                    </div>
                    <button class="btn btn-sm btn-success" onclick="showWechatQR()">扫码</button>
                </div>
                <div class="service-item">
                    <div class="service-icon phone-icon">
                        <i data-lucide="phone"></i>
                    </div>
                    <div class="service-info">
                        <h6>电话咨询</h6>
                        <p><?php echo htmlentities((string) (isset($siteConfig['company_phone']) && ($siteConfig['company_phone'] !== '')?$siteConfig['company_phone']:'************')); ?></p>
                    </div>
                    <a href="tel:<?php echo htmlentities((string) (isset($siteConfig['company_phone']) && ($siteConfig['company_phone'] !== '')?$siteConfig['company_phone']:'************')); ?>" class="btn btn-sm btn-warning">拨打</a>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="<?php echo asset('assets/js/jquery.min.js'); ?>"></script>
    <script src="<?php echo asset('assets/js/bootstrap.bundle.min.js'); ?>"></script> 
    <script src="<?php echo asset('assets/js/swiper-bundle.min.js'); ?>"></script>
    <script src="<?php echo asset('assets/js/main.js'); ?>"></script>
    <script src="<?php echo asset('assets/js/hero-slider.js'); ?>"></script>
    <script src="<?php echo asset('assets/js/scroll-handler.js'); ?>"></script>

    <!-- 初始化Lucide图标 -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    });
    </script>

</body>
</html>


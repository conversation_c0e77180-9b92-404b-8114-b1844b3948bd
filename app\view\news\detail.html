{assign name="pageTitle" value="$pageData.pageTitle" /}
{include file="common/header"}
<!-- 新闻专用CSS -->
<link rel="stylesheet" href="{:asset('assets/css/news.css')}?v={:time()}">

<!-- 整个新闻详情页面背景区域 -->
<section class="news-page-wrapper">
    <div class="news-background">
        <!-- 3D新闻插画装饰 -->
        <div class="hero-illustration">
            <div class="news-illustration">
                <!-- 新闻报纸 -->
                <div class="newspaper">
                    <div class="newspaper-content">
                        <div class="newspaper-header"></div>
                        <div class="newspaper-lines"></div>
                    </div>
                </div>
                <!-- 信息流泡泡 -->
                <div class="info-bubble bubble-1">
                    <i class="fas fa-rss"></i>
                </div>
                <div class="info-bubble bubble-2">
                    <i class="fas fa-newspaper"></i>
                </div>
                <div class="info-bubble bubble-3">
                    <i class="fas fa-bullhorn"></i>
                </div>
                <!-- 文档页面 -->
                <div class="document-stack">
                    <div class="document doc-1"></div>
                    <div class="document doc-2"></div>
                    <div class="document doc-3"></div>
                </div>
                <!-- 新闻图标 -->
                <div class="news-icon">
                    <i class="fas fa-globe"></i>
                </div>
                <!-- 数据流 -->
                <div class="data-stream">
                    <div class="stream-line line-1"></div>
                    <div class="stream-line line-2"></div>
                    <div class="stream-line line-3"></div>
                </div>
            </div>
        </div>

        <!-- 页面标题区域 -->
        <div class="page-header">
            <div class="container">
                <div class="page-title-content">
                    <h1 class="page-title">新闻详情</h1>
                    <p class="page-subtitle">深度了解最新资讯内容</p>
                    <nav class="page-breadcrumb">
                        <a href="/" class="breadcrumb-link">
                            <i class="fas fa-home"></i>
                            首页
                        </a>
                        <span class="breadcrumb-separator">></span>
                        <a href="/news" class="breadcrumb-link">新闻资讯</a>
                        <span class="breadcrumb-separator">></span>
                        <span class="breadcrumb-current">{$news.title|mb_substr=0,80,'UTF-8'}...</span>
                    </nav>
                </div>
            </div>
        </div>

        <!-- 新闻详情内容区域 -->
        <div class="news-content">
            <div class="container">
                <div class="row">
                    <!-- 左侧主要内容 -->
                    <div class="col-lg-8 col-md-7">
                        <!-- 新闻详情文章 -->
                        <article class="news-detail-article" data-aos="fade-up">
                            <div class="news-detail-card">
                                <!-- 新闻头部图片 -->
                                <div class="news-detail-header">
                                    <!-- <div class="news-detail-image">
                                        {if condition="!empty($news.image)"}
                                            <img src="{$news.image}" alt="{$news.title}" loading="lazy">
                                        {else /}
                                            <img src="{:asset('assets/images/news/default.jpg')}" alt="{$news.title}" loading="lazy">
                                        {/if}
                                        <div class="image-overlay">
                                            <div class="overlay-gradient"></div>
                                        </div>
                                    </div> -->
                                    
                                    <!-- 新闻元信息 -->
                                    <div class="news-detail-meta">
                                        <div class="meta-item category">
                                            <i class="fas fa-tag"></i>
                                            <span>{$news.category.name|default='公司动态'}</span>
                                        </div>
                                        <div class="meta-item date">
                                            <i class="fas fa-clock"></i>
                                            <time datetime="{$news.published_at|default=$news.created_at}">{$news.published_at|default=$news.created_at|date='Y年m月d日'}</time>
                                        </div>
                                        <div class="meta-item author">
                                            <i class="fas fa-user"></i>
                                            <span>{$news.author|default='管理员'}</span>
                                        </div>
                                        <div class="meta-item views">
                                            <i class="fas fa-eye"></i>
                                            <span>{$news.views|default=0} 次阅读</span>
                                        </div>
                                        {if condition="$news.is_featured"}
                                        <div class="meta-item featured">
                                            <i class="fas fa-star"></i>
                                            <span>精选文章</span>
                                        </div>
                                        {/if}
                                    </div>
                                </div>

                                <!-- 新闻标题 -->
                                <div class="news-detail-title-section">
                                    <h1 class="news-detail-title">{$news.title}</h1>
                                    
                                    <!-- 摘要 -->
                                    {if condition="!empty($news.summary)"}
                                    <div class="news-summary">
                                        <div class="summary-icon">
                                            <i class="fas fa-quote-left"></i>
                                        </div>
                                        <div class="summary-content">
                                            <h6 class="summary-label">文章摘要</h6>
                                            <p class="summary-text">{$news.summary}</p>
                                        </div>
                                    </div>
                                    {/if}
                                </div>

                                <!-- 新闻正文 -->
                                <div class="news-detail-content">
                                    <div class="article-content">
                                        {$news.content|raw}
                                    </div>

                                    <!-- 标签 -->
                                    {if condition="!empty($news.tags)"}
                                    <div class="news-tags-section">
                                        <h6 class="tags-title">
                                            <i class="fas fa-tags"></i>
                                            相关标签
                                        </h6>
                                        <div class="tags-list">
                                            {php}
                                                $tagList = explode(',', $news['tags']);
                                            {/php}
                                            {volist name="tagList" id="tag"}
                                            <a href="/news?tag={$tag|trim}" class="tag-item">
                                                #{$tag|trim}
                                            </a>
                                            {/volist}
                                        </div>
                                    </div>
                                    {/if}

                                    <!-- 分享按钮 -->
                                    <div class="news-share-section">
                                        <h6 class="share-title">
                                            <i class="fas fa-share-alt"></i>
                                            分享这篇文章
                                        </h6>
                                        <div class="share-buttons">
                                            <button class="share-btn facebook" onclick="shareToFacebook()" title="分享到Facebook">
                                                <i class="fab fa-facebook-f"></i>
                                                <span>Facebook</span>
                                            </button>
                                            <button class="share-btn twitter" onclick="shareToTwitter()" title="分享到Twitter">
                                                <i class="fab fa-twitter"></i>
                                                <span>Twitter</span>
                                            </button>
                                            <button class="share-btn linkedin" onclick="shareToLinkedIn()" title="分享到LinkedIn">
                                                <i class="fab fa-linkedin-in"></i>
                                                <span>LinkedIn</span>
                                            </button>
                                            <button class="share-btn wechat" onclick="shareToWechat()" title="分享到微信">
                                                <i class="fab fa-weixin"></i>
                                                <span>微信</span>
                                            </button>
                                            <button class="share-btn copy" onclick="copyLink()" title="复制链接">
                                                <i class="fas fa-link"></i>
                                                <span>复制链接</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </article>

                        <!-- 相关文章推荐 -->
                        {if condition="!empty($relatedNews)"}
                        <div class="related-articles-section" data-aos="fade-up" data-aos-delay="200">
                            <div class="section-header">
                                <h4 class="section-title">
                                    <i class="fas fa-newspaper"></i>
                                    相关文章推荐
                                </h4>
                                <div class="section-accent"></div>
                            </div>
                            <div class="related-articles-grid">
                                {volist name="relatedNews" id="related" limit="3"}
                                <div class="related-article-item">
                                    <div class="related-article-card">
                                        <div class="related-article-image">
                                            <a href="/news/{$related.slug}">
                                                {if condition="!empty($related.image)"}
                                                    <img src="{$related.image}" alt="{$related.title}" loading="lazy">
                                                {else /}
                                                    <img src="{:asset('assets/images/news/default.jpg')}" alt="{$related.title}" loading="lazy">
                                                {/if}
                                                <div class="image-overlay">
                                                    <div class="overlay-content">
                                                        <i class="fas fa-eye"></i>
                                                        <span>阅读详情</span>
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                        <div class="related-article-content">
                                            <div class="related-article-meta">
                                                <span class="category">
                                                    <i class="fas fa-tag"></i>
                                                    {$related.category.name|default='未分类'}
                                                </span>
                                                <span class="date">
                                                    <i class="fas fa-clock"></i>
                                                    {$related.published_at|default=$related.created_at|date='m-d'}
                                                </span>
                                            </div>
                                            <h6 class="related-article-title">
                                                <a href="/news/{$related.slug}">{$related.title}</a>
                                            </h6>
                                            <p class="related-article-excerpt">
                                                {$related.summary|default=$related.content|strip_tags|mb_substr=0,80,'UTF-8'}...
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                {/volist}
                            </div>
                        </div>
                        {/if}
                    </div>

                    <!-- 右侧边栏 -->
                    <div class="col-lg-4 col-md-5">
                        <div class="news-sidebar">
                            <!-- 最新文章 -->
                            <div class="sidebar-widget latest-news-widget" data-aos="fade-left" data-aos-delay="100">
                                <div class="widget-header">
                                    <h4 class="widget-title">
                                        <i class="fas fa-clock"></i>
                                        最新文章
                                    </h4>
                                    <div class="widget-accent"></div>
                                </div>
                                <div class="widget-content">
                                    <div class="latest-news-list">
                                        {if condition="!empty($latestNews)"}
                                            {volist name="latestNews" id="latest" limit="5"}
                                            <div class="latest-news-item">
                                                <div class="latest-news-image">
                                                    <img src="{$latest.image|default='/assets/images/news/default.jpg'}" alt="{$latest.title}" loading="lazy">
                                                </div>
                                                <div class="latest-news-content">
                                                    <h6><a href="/news/{$latest.slug}">{$latest.title|mb_substr=0,25,'UTF-8'}...</a></h6>
                                                    <div class="latest-news-meta">
                                                        <span class="category">
                                                            <i class="fas fa-tag"></i>
                                                            {$latest.category.name|default='未分类'}
                                                        </span>
                                                        <span class="date">{$latest.published_at|default=$latest.created_at|date='Y-m-d'}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            {/volist}
                                        {else /}
                                        <div class="empty-state">
                                            <p>暂无最新文章</p>
                                        </div>
                                        {/if}
                                    </div>
                                </div>
                            </div>

                            <!-- 热门文章 -->
                            <div class="sidebar-widget hot-news-widget" data-aos="fade-left" data-aos-delay="200">
                                <div class="widget-header">
                                    <h4 class="widget-title">
                                        <i class="fas fa-fire"></i>
                                        热门文章
                                    </h4>
                                    <div class="widget-accent"></div>
                                </div>
                                <div class="widget-content">
                                    <div class="hot-news-list">
                                        {if condition="!empty($popularNews)"}
                                            {volist name="popularNews" id="popular" key="index" limit="5"}
                                            <div class="hot-news-item">
                                                <div class="hot-news-rank">{$index}</div>
                                                <div class="hot-news-content">
                                                    <h5><a href="/news/{$popular.slug}">{$popular.title|mb_substr=0,25,'UTF-8'}...</a></h5>
                                                    <div class="hot-news-meta">
                                                        <span class="views"><i class="fas fa-eye"></i> {$popular.views|default=0}</span>
                                                        <span class="date">{$popular.published_at|default=$popular.created_at|date='m-d'}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            {/volist}
                                        {else /}
                                        <div class="empty-state">
                                            <p>暂无热门文章</p>
                                        </div>
                                        {/if}
                                    </div>
                                </div>
                            </div>

                            <!-- 文章分类 -->
                            <div class="sidebar-widget categories-widget" data-aos="fade-left" data-aos-delay="300">
                                <div class="widget-header">
                                    <h4 class="widget-title">
                                        <i class="fas fa-chart-bar"></i>
                                        文章分类
                                    </h4>
                                    <div class="widget-accent"></div>
                                </div>
                                <div class="widget-content">
                                    <div class="categories-stats">
                                        {if condition="!empty($categories)"}
                                            {volist name="categories" id="category"}
                                            <div class="category-stat-item">
                                                <a href="/news?category={$category.slug}" class="category-stat-link">
                                                    <div class="category-info">
                                                        <span class="category-name">{$category.name}</span>
                                                        <span class="category-count">{$category.news_count|default=0}</span>
                                                    </div>
                                                    <div class="category-progress">
                                                        {php}$width = min(($category['news_count'] ?? 0) * 10, 100);{/php}
                                                        <div class="progress-bar" style="width: {$width}%"></div>
                                                    </div>
                                                </a>
                                            </div>
                                            {/volist}
                                        {else /}
                                        <div class="empty-state">
                                            <p>暂无分类信息</p>
                                        </div>
                                        {/if}
                                    </div>
                                </div>
                            </div>

                            <!-- 返回列表 -->
                            <div class="sidebar-widget back-to-list-widget" data-aos="fade-left" data-aos-delay="400">
                                <div class="widget-content">
                                    <div class="back-to-list-section">
                                        <a href="/news" class="btn-back-all">
                                            <i class="fas fa-arrow-left"></i>
                                            返回新闻列表
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- JavaScript -->
<script src="{:asset('assets/js/jquery.min.js')}"></script>
<script src="{:asset('assets/js/bootstrap.bundle.min.js')}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化AOS动画库（如果已引入）
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 600,
            easing: 'ease-out',
            once: true
        });
    }

    // 增加阅读量 (静默请求)
    const newsId = '{$news.id}';
    if (newsId) {
        fetch('/news/view/' + newsId, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        }).catch(e => {
            // 静默处理错误
            console.log('阅读量统计失败:', e);
        });
    }

    // 侧边栏组件交互
    const sidebarWidgets = document.querySelectorAll('.sidebar-widget');
    sidebarWidgets.forEach(widget => {
        // 悬停效果
        widget.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        widget.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // 热门新闻项悬停效果
    const hotNewsItems = document.querySelectorAll('.hot-news-item');
    hotNewsItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(5px)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });

    // 最新新闻项悬停效果
    const latestNewsItems = document.querySelectorAll('.latest-news-item');
    latestNewsItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(3px)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });

    // 相关文章悬停效果
    const relatedItems = document.querySelectorAll('.related-article-item');
    relatedItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // 分类统计进度条动画
    const progressBars = document.querySelectorAll('.progress-bar');
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const progressObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const progressBar = entry.target;
                const width = progressBar.style.width;
                progressBar.style.width = '0%';
                setTimeout(() => {
                    progressBar.style.width = width;
                }, 200);
            }
        });
    }, observerOptions);
    
    progressBars.forEach(bar => {
        progressObserver.observe(bar);
    });

    // 3D插画动画效果增强
    const newsIllustration = document.querySelector('.news-illustration');
    if (newsIllustration) {
        // 鼠标移动视差效果
        document.addEventListener('mousemove', function(e) {
            const mouseX = (e.clientX / window.innerWidth - 0.5) * 2;
            const mouseY = (e.clientY / window.innerHeight - 0.5) * 2;
            
            // 报纸元素
            const newspaper = newsIllustration.querySelector('.newspaper');
            if (newspaper) {
                newspaper.style.transform = `rotate(-15deg) translate(${mouseX * 5}px, ${mouseY * 5}px)`;
            }
            
            // 信息气泡
            const bubbles = newsIllustration.querySelectorAll('.info-bubble');
            bubbles.forEach((bubble, index) => {
                const speed = (index + 1) * 3;
                bubble.style.transform = `translate(${mouseX * speed}px, ${mouseY * speed}px) scale(${1 + mouseX * 0.1})`;
            });
            
            // 文档堆叠
            const docStack = newsIllustration.querySelector('.document-stack');
            if (docStack) {
                docStack.style.transform = `translate(${mouseX * 8}px, ${mouseY * 8}px)`;
            }
        });
        
        // 滚动视差效果
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            newsIllustration.style.transform = `translateY(${rate}px)`;
        });
    }

    // 平滑滚动到顶部功能
    const backToTopBtn = document.createElement('button');
    backToTopBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
    backToTopBtn.className = 'back-to-top-btn';
    backToTopBtn.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border: none;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        cursor: pointer;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1000;
        box-shadow: 0 4px 15px rgba(44, 123, 229, 0.3);
    `;
    
    document.body.appendChild(backToTopBtn);
    
    // 显示/隐藏返回顶部按钮
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopBtn.style.opacity = '1';
            backToTopBtn.style.visibility = 'visible';
        } else {
            backToTopBtn.style.opacity = '0';
            backToTopBtn.style.visibility = 'hidden';
        }
    });
    
    // 返回顶部功能
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // 延迟加载图片优化
    const images = document.querySelectorAll('img[loading="lazy"]');
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src || img.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    }

    // 分享按钮点击效果
    const shareButtons = document.querySelectorAll('.share-btn');
    shareButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            // 添加点击波纹效果
            const ripple = document.createElement('span');
            ripple.classList.add('ripple');
            this.appendChild(ripple);
            
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = (rect.width / 2 - size / 2) + 'px';
            ripple.style.top = (rect.height / 2 - size / 2) + 'px';
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
});

// 分享功能
function shareToFacebook() {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent('{$news.title|addslashes}');
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${title}`, '_blank', 'width=600,height=400');
}

function shareToTwitter() {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent('{$news.title|addslashes}');
    window.open(`https://twitter.com/intent/tweet?url=${url}&text=${title}`, '_blank', 'width=600,height=400');
}

function shareToLinkedIn() {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent('{$news.title|addslashes}');
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${title}`, '_blank', 'width=600,height=400');
}

function shareToWechat() {
    // 微信分享通常需要微信SDK，这里显示一个提示
    alert('请复制链接到微信分享：\n' + window.location.href);
}

function copyLink() {
    navigator.clipboard.writeText(window.location.href).then(function() {
        // 显示复制成功提示
        const btn = event.target.closest('.share-btn');
        const originalText = btn.querySelector('span').textContent;
        btn.querySelector('span').textContent = '已复制';
        btn.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
        
        setTimeout(() => {
            btn.querySelector('span').textContent = originalText;
            btn.style.background = '';
        }, 2000);
    }).catch(function(err) {
        console.error('复制失败:', err);
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = window.location.href;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('链接已复制到剪贴板');
    });
}
</script>

{include file="common/footer"} 
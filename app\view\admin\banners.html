<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播图管理 - 后台管理系统</title>

    <!-- CSS -->
    {include file="admin/common/css"}
    <link rel="stylesheet" href="/assets/css/admin/banners.css">
    <link rel="stylesheet" href="/assets/css/image-uploader.css">
</head>
<body>
    <!-- 顶部导航 -->
    {include file="admin/common/header"}

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            {include file="admin/common/sidebar"}

            <!-- 主要内容 -->
            <main class="main-content">
                <!-- 内容头部 -->
                <div class="content-header">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-images"></i> 轮播图管理
                    </h1>
                </div>

                    <!-- 引用统一消息组件 -->
                    {include file="admin/common/message"}

                <!-- 页面内容区域 -->
                <div class="content-body">
                    {if condition="$action == 'list'"}
                    <div class="banners-container">
                        <!-- 列表头部 -->
                        <div class="list-header">
                            <div class="list-header-content">
                                <div class="list-title-section">
                                    <div class="list-icon">
                                        <i class="fas fa-images"></i>
                                    </div>
                                    <div>
                                        <h1 class="list-title">轮播图管理</h1>
                                        <p class="list-subtitle">
                                            {if condition="count($banners) >= 5"}
                                                <span class="limit-warning">已达到最大数量限制 (5/5)</span>
                                            {else /}
                                                <span class="limit-info">当前数量: {$banners|count}/5</span>
                                            {/if}
                                        </p>
                                    </div>
                                </div>
                                {if condition="count($banners) < 5"}
                                <a href="/admin/banners?action=add" class="btn-add-custom">
                                    <i class="fas fa-plus"></i>
                                    添加轮播图
                                </a>
                                {/if}
                            </div>
                        </div>

        <!-- 列表主体 -->
        <div class="list-body">
            {if condition="empty($banners)"}
                <!-- 空状态 -->
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <h3 class="empty-title">暂无轮播图</h3>
                    <p class="empty-subtitle">点击上方按钮添加您的第一个轮播图</p>
                </div>
            {else /}
                <!-- 轮播图列表 -->
                <div class="banners-list">
                    {volist name="banners" id="banner"}
                    <div class="banner-item">
                        <div class="banner-content">
                            <!-- 缩略图 -->
                            <div class="banner-thumbnail">
                                {if condition="$banner.image"}
                                    <img src="{$banner.image}" alt="{$banner.title}" class="banner-thumb-img">
                                {else /}
                                    <i class="fas fa-image banner-thumb-placeholder"></i>
                                {/if}
                            </div>

                            <!-- 轮播图信息 -->
                            <div class="banner-info">
                                <h3 class="banner-title">{$banner.title}</h3>
                                <p class="banner-subtitle">{$banner.subtitle}</p>
                                <p class="banner-description">{$banner.description}</p>
                            </div>

                            <!-- 元数据 -->
                            <div class="banner-meta">
                                <!-- 状态开关 -->
                                <div class="status-switch-wrapper">
                                    <label class="status-switch" title="点击切换启用状态">
                                        <input type="checkbox" class="status-checkbox" 
                                               data-banner-id="{$banner.id}"
                                               {if condition="$banner.status eq 1"}checked{/if}
                                               onchange="toggleBannerStatus('{$banner.id}', this)">
                                        <span class="status-slider"></span>
                                        <span class="status-text">
                                    {if condition="$banner.status eq 1"}启用{else /}禁用{/if}
                                </span>
                                    </label>
                                </div>

                                <!-- 排序 -->
                                <div class="sort-badge">#{$banner.sort_order}</div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="banner-actions">
                                <a href="/admin/banners?action=edit&id={$banner.id}" class="action-btn btn-edit">
                                    <i class="fas fa-edit"></i>
                                    编辑
                                </a>
                                <button type="button" class="action-btn btn-delete" onclick="deleteItem('{$banner.id}', '{$banner.title|htmlentities}', '/admin/banners?action=delete&id={$banner.id}')">
                                    <i class="fas fa-trash"></i>
                                    删除
                                </button>
                            </div>
                        </div>
                    </div>
                    {/volist}
                </div>
            {/if}
        </div>
    </div>
    {/if}

    {if condition="$action == 'add' || $action == 'edit'"}
    <!-- 添加/编辑表单 -->
    <div class="form-container">
        <div class="form-header">
            <div class="list-header-content">
                <div class="list-title-section">
                    <div class="list-icon">
                        <i class="fas fa-{$action == 'add' ? 'plus-circle' : 'edit'}"></i>
                    </div>
                    <div>
                        <h1 class="list-title">{$action == 'add' ? '添加轮播图' : '编辑轮播图'}</h1>
                        <p class="list-subtitle">创建精美的轮播图展示内容</p>
                    </div>
                </div>
                <a href="/admin/banners" class="btn-add-custom">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
            </div>
        </div>
        <div class="form-body">
            <form method="POST" enctype="multipart/form-data" class="banner-form" id="bannerForm">
                <input type="hidden" name="action" value="{$action}">
                {if condition="$action == 'edit' && $editData"}
                <input type="hidden" name="id" value="{$editData.id}">
                {/if}
                <input type="hidden" name="image" id="selectedImagePath" value="{$editData ? $editData.image : ''}">

                <div class="form-grid">
                    <!-- 左侧：基本信息 -->
                    <div class="form-main">
                        <!-- 基本信息卡片 -->
                        <div class="form-card">
                            <div class="card-header">
                                <div class="card-icon">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <h6 class="card-title">基本信息</h6>
                            </div>
                            <div class="card-content">
                                <!-- 标题 -->
                                <div class="form-group">
                                    <label for="title" class="form-label">
                                        <span class="label-text">轮播图标题</span>
                                        <span class="label-required">*</span>
                                    </label>
                                    <div class="input-wrapper">
                                        <div class="input-icon">
                                            <i class="fas fa-heading"></i>
                                        </div>
                                        <input type="text" class="form-control" id="title" name="title"
                                               value="{$editData ? $editData.title : ''}"
                                               placeholder="请输入吸引人的轮播图标题" required>
                                    </div>
                                    <div class="form-help">标题将显示在轮播图上，建议控制在20字以内</div>
                                </div>

                                <!-- 副标题 -->
                                <div class="form-group">
                                    <label for="subtitle" class="form-label">
                                        <span class="label-text">轮播图副标题</span>
                                    </label>
                                    <div class="input-wrapper">
                                        <div class="input-icon">
                                            <i class="fas fa-text-height"></i>
                                        </div>
                                        <input type="text" class="form-control" id="subtitle" name="subtitle"
                                               value="{$editData ? $editData.subtitle : ''}"
                                               placeholder="请输入轮播图副标题">
                                    </div>
                                    <div class="form-help">副标题将显示在主标题下方，建议控制在50字以内</div>
                                </div>

                                <!-- 描述 -->
                                <div class="form-group">
                                    <label for="description" class="form-label">
                                        <span class="label-text">轮播图描述</span>
                                    </label>
                                    <div class="textarea-wrapper">
                                        <div class="textarea-icon">
                                            <i class="fas fa-align-left"></i>
                                        </div>
                                        <textarea class="form-control" id="description" name="description" rows="4"
                                                  placeholder="请输入轮播图的详细描述信息...">{$editData ? $editData.description : ''}</textarea>
                                    </div>
                                    <div class="form-help">简短描述轮播图内容，将显示在标题下方</div>
                                </div>

                                <!-- 链接地址 -->
                                <div class="form-group">
                                    <label for="link_url" class="form-label">
                                        <span class="label-text">跳转链接</span>
                                    </label>
                                    <div class="input-wrapper">
                                        <div class="input-icon">
                                            <i class="fas fa-link"></i>
                                        </div>
                                        <input type="url" class="form-control" id="link_url" name="link_url"
                                               value="{$editData ? $editData.link_url : ''}"
                                               placeholder="https://example.com">
                                    </div>
                                    <div class="form-help">点击轮播图时跳转的链接地址（可选）</div>
                                </div>
                            </div>
                        </div>

                        <!-- 显示设置卡片 -->
                        <div class="form-card">
                            <div class="card-header">
                                <div class="card-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <h6 class="card-title">显示设置</h6>
                            </div>
                            <div class="card-content">
                                <div class="form-row">
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label for="sort_order" class="form-label">
                                                <span class="label-text">显示排序</span>
                                            </label>
                                            <div class="input-wrapper">
                                                <div class="input-icon">
                                                    <i class="fas fa-sort-numeric-down"></i>
                                                </div>
                                                <input type="number" class="form-control" id="sort_order" name="sort_order"
                                                       value="{$editData ? $editData.sort_order : 0}" min="0" max="999">
                                            </div>
                                            <div class="form-help">数字越小排序越靠前</div>
                                        </div>
                                    </div>
                                    <div class="form-col">
                                        <div class="form-group">
                                            <label class="form-label">
                                                <span class="label-text">启用状态</span>
                                            </label>
                                            <div class="switch-wrapper">
                                                <label class="switch">
                                                    <input type="checkbox" id="status" name="status"
                                                           {if condition="!$editData || $editData.status"}checked{/if}>
                                                    <span class="switch-slider"></span>
                                                </label>
                                                <span class="switch-label">启用轮播图显示</span>
                                            </div>
                                            <div class="form-help">关闭后轮播图将不会在前台显示</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：图片选择 -->
                    <div class="form-sidebar">
                        <div class="form-card">
                            <div class="card-header">
                                <div class="card-icon">
                                    <i class="fas fa-image"></i>
                                </div>
                                <h6 class="card-title">轮播图片{if condition="$action == 'add'"} <span style="color: #dc3545;">*</span>{/if}</h6>
                            </div>
                            <div class="card-content">
                                <div class="image-selector-area" id="bannerImageSelector">
                                    <div class="image-preview-container">
                                        {if condition="$editData && $editData.image"}
                                        <div class="current-image-preview" id="currentImagePreview">
                                            <img src="{$editData.image}" alt="当前图片" id="previewImage">
                                            <div class="image-overlay">
                                                <div class="overlay-content">
                                                    <i class="fas fa-camera"></i>
                                                    <span>点击选择图片</span>
                                                </div>
                                            </div>
                                        </div>
                                        {else /}
                                        <div class="image-placeholder" id="imagePlaceholder">
                                            <div class="placeholder-content">
                                                <div class="placeholder-icon">
                                                    <i class="fas fa-image"></i>
                                                </div>
                                                <div class="placeholder-text">
                                                    <h6>选择轮播图片</h6>
                                                    <p>点击选择图片</p>
                                            </div>
                                            </div>
                                        </div>
                                        {/if}
                                    </div>
                                    <button type="button" class="btn-select-image" id="btnSelectImage">
                                        <i class="fas fa-images"></i>
                                        <span>选择图片</span>
                                    </button>
                                </div>
                                <div class="image-info" style="margin-top: 10px;">
                                    <div class="info-item">
                                        <i class="fas fa-info-circle"></i>
                                        <span>支持 JPG、PNG、GIF、WebP 格式</span>
                                    </div>
                                    <div class="info-item">
                                        <i class="fas fa-weight-hanging"></i>
                                        <span>文件大小不超过 5MB</span>
                                    </div>
                                    <div class="info-item">
                                        <i class="fas fa-expand-arrows-alt"></i>
                                        <span>建议尺寸 1920x800 像素</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 表单操作按钮 -->
                <div class="form-actions">
                    <div class="actions-left">
                        <div class="form-tips">
                            <i class="fas fa-info-circle"></i>
                            <span>带 * 号的字段为必填项</span>
                        </div>
                    </div>
                    <div class="actions-right">
                        <a href="/admin/banners" class="btn-cancel">
                            <i class="fas fa-times"></i>
                            <span>取消</span>
                        </a>
                        <button type="submit" class="btn-submit">
                            <i class="fas fa-save"></i>
                            <span>{$action == 'add' ? '添加轮播图' : '保存修改'}</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    {/if}

                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- JavaScript -->

    
    <!-- 图片上传弹窗组件 -->
    <script src="/assets/js/image-uploader.js"></script>
    <!-- 图片选择器扩展 -->
    <script src="/assets/js/image-selector-extension.js"></script>

    <script>
        let bannerImageUploader; // 轮播图图片选择器实例

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加轮播图项目的hover效果增强
            const bannerItems = document.querySelectorAll('.banner-item');
            bannerItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px)';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // 添加按钮点击效果
            const actionBtns = document.querySelectorAll('.action-btn');
            actionBtns.forEach(btn => {
                btn.addEventListener('mousedown', function() {
                    this.style.transform = 'scale(0.95)';
                });

                btn.addEventListener('mouseup', function() {
                    this.style.transform = 'scale(1)';
                });

                btn.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // 初始化图片选择器
            initBannerImageSelector();
            
            // 表单验证
            initFormValidation();
        });

        // 初始化轮播图图片选择器
        function initBannerImageSelector() {
            const btnSelectImage = document.getElementById('btnSelectImage');
            if (!btnSelectImage) return;

            // 初始化轮播图专用图片选择器（限制1张图片）
            bannerImageUploader = createImageUploader({
                uploadUrl: '/admin/image/upload?context=banners',
                uploadField: 'upload',
                maxFiles: 1, // 轮播图只能选择1张图片
                maxSize: 5 * 1024 * 1024, // 5MB限制
                allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
                enableImageSelector: true,
                selectorUrl: '/admin/image/selector',
                context: 'banner', // 标识为轮播图环境
                instanceId: 'banner-uploader', // 唯一实例ID
                                 onSelect: function(files) {
                    
                    // 验证文件类型和大小
                    const validFiles = [];
                    const errors = [];
                    
                    files.forEach(file => {
                        // 检查文件类型
                        if (!['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(file.type)) {
                            errors.push(`${file.name}: 仅支持JPG、PNG、GIF、WebP格式`);
                            return;
                        }
                        
                        // 检查文件大小
                        if (file.size > 5 * 1024 * 1024) {
                            errors.push(`${file.name}: 文件大小不能超过5MB`);
                            return;
                        }
                        
                        validFiles.push(file);
                    });
                    
                    if (errors.length > 0) {
                        // 使用图片上传器自己的提示
                        bannerImageUploader.showMessage('文件验证失败：\n' + errors.join('\n'), 'error');
                        return false;
                    }
                    
                    return validFiles;
                },
                                 onConfirm: function(orderedData, mode) {
                     
                     if (orderedData && orderedData.length > 0) {
                         const selectedItem = orderedData[0]; // 只取第一张图片
                         
                         if (mode === 'select') {
                             // 图片选择模式 - 使用已有图片
                             updateBannerImagePreview(selectedItem.file_url, selectedItem.filename);
                             // 设置图片路径，去掉开头的斜杠以匹配后端期望的格式
                             const imagePath = selectedItem.file_url.replace(/^\//, '');
                             document.getElementById('selectedImagePath').value = imagePath;
                         } else {
                             // 文件上传模式 - 显示文件预览
                             const reader = new FileReader();
                             reader.onload = function(e) {
                                 updateBannerImagePreview(e.target.result, selectedItem.name);
                                 // 文件上传模式下，图片路径将在上传成功后设置
                             };
                             reader.readAsDataURL(selectedItem);
                         }
                         
                         bannerImageUploader.close();
                         // 使用图片上传器自己的提示
                         bannerImageUploader.showMessage('图片选择成功', 'success');
                     }
                 },
                                 onUpload: function(uploadedFiles) {
                     if (uploadedFiles && uploadedFiles.length > 0) {
                         const uploadedFile = uploadedFiles[0];
                         updateBannerImagePreview(uploadedFile.url, uploadedFile.filename);
                         // 设置上传后的图片路径
                         const imagePath = uploadedFile.url.replace(/^\//, '');
                         document.getElementById('selectedImagePath').value = imagePath;
                         // 使用图片上传器自己的提示
                         bannerImageUploader.showMessage('图片上传成功', 'success');
                     }
                 },
                                 onError: function(error) {
                     // 使用图片上传器自己的提示
                     bannerImageUploader.showMessage('操作失败：' + error.message, 'error');
                }
            });

            // 绑定选择图片按钮事件
            btnSelectImage.addEventListener('click', function(e) {
                e.preventDefault();
                                 if (bannerImageUploader) {
                     bannerImageUploader.show();
                 } else {
                     showMessage('图片选择器未初始化', 'error');
                 }
            });

            // 绑定图片预览区域点击事件
            const imageSelector = document.getElementById('bannerImageSelector');
            if (imageSelector) {
                imageSelector.addEventListener('click', function(e) {
                    // 如果点击的不是按钮，也触发选择
                    if (!e.target.closest('.btn-select-image')) {
                        btnSelectImage.click();
                }
            });
        }
        }

        // 更新轮播图图片预览
        function updateBannerImagePreview(imageSrc, filename) {
            const currentPreview = document.getElementById('currentImagePreview');
            const placeholder = document.getElementById('imagePlaceholder');
            const previewImage = document.getElementById('previewImage');
            
            if (placeholder) {
                // 如果当前显示的是占位符，替换为预览图
                placeholder.outerHTML = `
                    <div class="current-image-preview" id="currentImagePreview">
                        <img src="${imageSrc}" alt="${filename}" id="previewImage">
                            <div class="image-overlay">
                                <div class="overlay-content">
                                    <i class="fas fa-camera"></i>
                                    <span>点击更换图片</span>
                                </div>
                            </div>
                        </div>
                    `;
            } else if (previewImage) {
                // 如果已有预览图，直接更新
                previewImage.src = imageSrc;
                previewImage.alt = filename;
                }
        }

        // 表单验证初始化
        function initFormValidation() {
            const bannerForm = document.getElementById('bannerForm');
            if (!bannerForm) return;

            bannerForm.addEventListener('submit', function(e) {
                const action = document.querySelector('input[name="action"]').value;
                const selectedImagePath = document.getElementById('selectedImagePath').value;
                
                // 如果是添加模式，检查是否选择了图片
                if (action === 'add' && !selectedImagePath) {
                        e.preventDefault();
                    showMessage('请选择轮播图片', 'danger');
                        
                    // 高亮显示图片选择区域
                    const imageSelector = document.getElementById('bannerImageSelector');
                    if (imageSelector) {
                        imageSelector.style.borderColor = '#dc3545';
                        imageSelector.style.background = 'rgba(220, 53, 69, 0.1)';
                            
                            // 3秒后恢复正常样式
                            setTimeout(() => {
                            imageSelector.style.borderColor = '';
                            imageSelector.style.background = '';
                            }, 3000);
                        }
                        
                        return false;
                }
                
                return true;
            });
        }

        // 切换轮播图状态
        function toggleBannerStatus(bannerId, checkbox) {
            // 确保bannerId是数字
            bannerId = parseInt(bannerId);
            const newStatus = checkbox.checked ? 1 : 0;
            const statusText = checkbox.parentNode.querySelector('.status-text');
            const originalText = statusText.textContent;
            
            // 显示加载状态
            statusText.textContent = '切换中...';
            checkbox.disabled = true;
            
            // 发送AJAX请求
            fetch('/admin/banners?action=toggle_status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    id: bannerId,
                    status: newStatus
                })
            })
            .then(response => {
                // 检查响应是否是JSON
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    return response.json();
                } else {
                    // 如果不是JSON，可能是错误页面
                    return response.text().then(text => {
                        console.error('服务器返回非JSON响应:', text);
                        throw new Error('服务器返回错误页面');
                    });
                }
            })
            .then(data => {
                if (data.success) {
                    // 更新状态文本
                    statusText.textContent = newStatus ? '启用' : '禁用';
                    showMessage(data.message || '状态更新成功', 'success');
                } else {
                    // 恢复原状态
                    checkbox.checked = !checkbox.checked;
                    statusText.textContent = originalText;
                    showMessage(data.message || '状态更新失败', 'error');
                }
            })
            .catch(error => {
                console.error('状态切换失败:', error);
                // 恢复原状态
                checkbox.checked = !checkbox.checked;
                statusText.textContent = originalText;
                showMessage('网络错误，状态更新失败', 'error');
            })
            .finally(() => {
                checkbox.disabled = false;
            });
        }

    </script>

</body>
</html>

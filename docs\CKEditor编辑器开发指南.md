# 📝 CKEditor 5 编辑器开发指南

**三只鱼网络科技 | 韩总 | 2024-12-19**  
**技术栈：CKEditor 5 Classic + ThinkPHP6 + 图片上传组件**

## 🎯 概述

本指南详细说明了在ThinkPHP6项目中集成和使用CKEditor 5编辑器的标准规范，确保所有编辑器功能的一致性和稳定性。

## 📋 标准配置

### 🔧 必需文件引用
```html
<!-- CSS文件 -->
<link rel="stylesheet" href="/assets/css/ckeditor.css">
<link rel="stylesheet" href="/assets/css/image-uploader.css">

<!-- JavaScript文件 -->
<script src="/assets/js/ckeditor.js"></script>
<script src="/assets/js/zh-cn.js"></script>
<script src="/assets/js/image-uploader.js"></script>
<script src="/assets/js/image-selector-extension.js"></script>
```

### ⚙️ 标准初始化配置
```javascript
ClassicEditor
    .create(document.querySelector('#editor'), {
        language: 'zh-cn',
        placeholder: '请输入内容...',
        toolbar: [
            'heading',
            'bold',
            'italic',
            'underline',
            'numberedList',
            'bulletedList',
            'outdent',
            'indent',
            'link',
            'insertTable',
            'blockQuote',
            'undo',
            'redo'
        ],
        heading: {
            options: [
                { model: 'paragraph', title: '正文', class: 'ck-heading_paragraph' },
                { model: 'heading1', view: 'h1', title: '标题 1', class: 'ck-heading_heading1' },
                { model: 'heading2', view: 'h2', title: '标题 2', class: 'ck-heading_heading2' },
                { model: 'heading3', view: 'h3', title: '标题 3', class: 'ck-heading_heading3' },
                { model: 'heading4', view: 'h4', title: '标题 4', class: 'ck-heading_heading4' }
            ]
        },
        table: {
            contentToolbar: [
                'tableColumn',
                'tableRow',
                'mergeTableCells',
                'tableCellProperties',
                'tableProperties'
            ]
        },
        link: {
            decorators: {
                openInNewTab: {
                    mode: 'manual',
                    label: '在新标签页中打开',
                    attributes: {
                        target: '_blank',
                        rel: 'noopener noreferrer'
                    }
                }
            }
        }
    })
    .then(newEditor => {
        editor = newEditor;
        window.editor = newEditor;

        // 设置编辑器高度
        const editingView = editor.editing.view;
        editingView.change(writer => {
            writer.setStyle('min-height', '300px', editingView.document.getRoot());
            writer.setStyle('max-height', '500px', editingView.document.getRoot());
        });

        // 设置焦点样式
        editor.ui.focusTracker.on('change:isFocused', (evt, name, isFocused) => {
            if (isFocused) {
                $('.ck-editor-container').addClass('focused');
            } else {
                $('.ck-editor-container').removeClass('focused');
            }
        });

        // 添加图片上传按钮 - 延时确保DOM完全加载
        setTimeout(() => {
            addImageUploadButton();
        }, 1000);

        console.log('✅ CKEditor 5 Classic 初始化成功');
    })
    .catch(error => {
        console.error('❌ CKEditor 5 初始化失败:', error);
        // 降级到普通textarea
        $('#editor').addClass('form-textarea').attr('rows', 15).show();
        showMessage('编辑器加载失败，已切换到基础模式', 'warning');
        window.editor = null;
    });
```

## 🖼️ 图片上传按钮集成

### 🎯 核心实现
```javascript
function addImageUploadButton() {
    // 多种方式查找工具栏
    let toolbarItems = document.querySelector('.ck-toolbar .ck-toolbar__items');
    if (!toolbarItems) {
        toolbarItems = document.querySelector('.ck-toolbar__items');
    }
    if (!toolbarItems) {
        toolbarItems = document.querySelector('.ck-toolbar');
    }
    
    if (!toolbarItems) {
        return false;
    }
    
    // 检查按钮是否已存在
    if (document.querySelector('[data-upload-button="true"]')) {
        return true;
    }
    
    // 创建图片上传按钮
    const imageButton = document.createElement('button');
    imageButton.className = 'ck-button ck-button_with-text';
    imageButton.type = 'button';
    imageButton.setAttribute('data-upload-button', 'true');
    imageButton.setAttribute('title', '上传图片');
    imageButton.setAttribute('aria-label', '上传图片');
    
    // 创建图标容器
    const iconContainer = document.createElement('span');
    iconContainer.className = 'ck-button__icon';
    
    // 使用FontAwesome图标
    const icon = document.createElement('i');
    icon.className = 'fas fa-images';
    icon.style.cssText = `
        font-size: 12px !important;
        color: rgba(255, 255, 255, 0.8) !important;
        opacity: 1 !important;
        visibility: visible !important;
        display: inline-block !important;
    `;
    iconContainer.appendChild(icon);
    imageButton.appendChild(iconContainer);
    
    // 添加文本标签
    const textLabel = document.createElement('span');
    textLabel.className = 'ck-button__label';
    textLabel.textContent = '图片';
    imageButton.appendChild(textLabel);
    
    // 绑定点击事件
    imageButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('🖼️ 打开编辑器图片上传弹窗');
        
        if (editorImageUploader) {
            editorImageUploader.show();
        } else {
            console.error('❌ 编辑器图片上传组件未初始化');
        }
    });
    
    // 插入到工具栏末尾
    toolbarItems.appendChild(imageButton);
    console.log('✅ 图片按钮已添加到工具栏');
    return true;
}
```

### 📊 图片上传组件初始化
```javascript
// 初始化编辑器专用图片上传组件
editorImageUploader = createImageUploader({
    uploadUrl: '/admin/image/upload',
    uploadField: 'upload',
    maxFiles: 999,
    maxSize: 5 * 1024 * 1024,
    allowedTypes: ['image/jpeg', 'image/png'],
    enableImageSelector: true,
    selectorUrl: '/admin/image/selector',
    isEditor: true,
    context: 'editor',
    instanceId: 'editor-uploader',
    onSelect: function(files) {
        // 文件验证逻辑
        const validFiles = [];
        const errors = [];

        files.forEach(file => {
            if (file.file_size && file.file_size > 5 * 1024 * 1024) {
                errors.push(`${file.filename}: 文件大小超过5MB`);
            } else if (file.file_type && !['image/jpeg', 'image/png'].includes(file.file_type)) {
                errors.push(`${file.filename}: 不支持的文件类型`);
            } else {
                validFiles.push(file);
            }
        });

        if (errors.length > 0) {
            showMessage('部分文件无效：\n' + errors.join('\n'), 'warning');
        }

        return validFiles;
    },
    onConfirm: function(orderedData, mode) {
        if (mode === 'select') {
            insertSelectedImagesToEditor(orderedData);
        } else {
            insertOrderedFilesToEditor(orderedData);
        }
    },
    onUpload: function(uploadedFiles) {
        uploadedFiles.forEach((fileData, index) => {
            setTimeout(() => {
                insertImageToEditor(fileData.url);
            }, index * 100);
        });
        editorImageUploader.close();
    },
    onError: function(error) {
        showMessage('图片上传失败：' + error.message, 'error');
    }
});
```

## 🔄 图片插入功能

### 📋 插入已选择的图片
```javascript
function insertSelectedImagesToEditor(selectedImages) {
    if (!window.editor) {
        return;
    }

    selectedImages.forEach((image, index) => {
        setTimeout(() => {
            try {
                const imageHtml = `
                    <img src="${image.file_url}" alt="${image.alt_text || image.filename}"
                         style="max-width: 100%; height: auto; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                `;

                const selection = window.editor.model.document.selection;
                const position = selection.getLastPosition();

                const viewFragment = window.editor.data.processor.toView(imageHtml);
                const modelFragment = window.editor.data.toModel(viewFragment);

                window.editor.model.change(writer => {
                    window.editor.model.insertContent(modelFragment, position);
                });

                if (index === selectedImages.length - 1) {
                    showMessage(`已按顺序插入 ${selectedImages.length} 张图片`, 'success');
                }
            } catch (error) {
                console.error('插入图片失败:', error);
            }
        }, index * 200);
    });
}
```

### 📤 插入上传的图片
```javascript
function insertImageToEditor(imageUrl) {
    if (!window.editor) {
        return;
    }

    try {
        const imageHtml = `<img src="${imageUrl}" alt="上传的图片" style="max-width: 100%; height: auto; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">`;

        const selection = window.editor.model.document.selection;
        const position = selection.getLastPosition();

        const viewFragment = window.editor.data.processor.toView(imageHtml);
        const modelFragment = window.editor.data.toModel(viewFragment);

        window.editor.model.change(writer => {
            window.editor.model.insertContent(modelFragment, position);
        });
    } catch (error) {
        showMessage('图片插入失败', 'error');
    }
}
```

## ✅ 表单验证集成

### 📝 内容同步和验证
```javascript
$('form').on('submit', function(e) {
    // 如果CKEditor存在，同步内容
    if (window.editor) {
        const data = window.editor.getData();
        $('#editor').val(data);
        
        // 验证编辑器内容
        if (!data || data.trim() === '') {
            e.preventDefault();
            showMessage('请输入内容', 'warning');
            
            // 聚焦到编辑器
            setTimeout(() => {
                if (window.editor) {
                    window.editor.editing.view.focus();
                }
            }, 100);
            return false;
        }
    } else {
        // 如果CKEditor未初始化，检查原始textarea
        const content = $('#editor').val();
        if (!content || content.trim() === '') {
            e.preventDefault();
            showMessage('请输入内容', 'warning');
            $('#editor').focus();
            return false;
        }
    }
    
    return true;
});
```

## 🚨 常见问题解决方案

### 1. 图片按钮不显示
**问题**：工具栏中看不到图片上传按钮  
**原因**：工具栏查找失败或按钮样式不正确  
**解决**：
- 使用多种方式查找工具栏元素
- 确保使用正确的CSS类名 `ck-button_with-text`
- 检查FontAwesome图标是否正确加载

### 2. 图片上传功能失效
**问题**：点击图片按钮无反应  
**原因**：图片上传组件未正确初始化  
**解决**：
- 确保在编辑器初始化后再添加按钮
- 检查 `editorImageUploader` 实例是否存在
- 验证事件绑定是否正确

### 3. 编辑器样式异常
**问题**：编辑器显示不正常或样式错乱  
**原因**：CSS冲突或缺少必要的样式文件  
**解决**：
- 确保引用了 `ckeditor.css` 文件
- 检查自定义样式是否与编辑器样式冲突
- 使用 `!important` 强制覆盖冲突样式

### 4. 内容同步问题
**问题**：表单提交时编辑器内容丢失  
**原因**：表单提交前未同步编辑器内容  
**解决**：
- 在表单提交前调用 `editor.getData()` 同步内容
- 确保 `window.editor` 实例存在
- 添加内容验证逻辑

## 🎯 开发检查清单

- [ ] 是否引用了所有必需的CSS和JS文件？
- [ ] 是否使用了标准的toolbar配置？
- [ ] 是否正确实现了图片上传按钮？
- [ ] 是否设置了合适的编辑器高度？
- [ ] 是否实现了内容同步和验证？
- [ ] 是否处理了编辑器初始化失败的降级方案？
- [ ] 是否测试了图片上传和插入功能？
- [ ] 是否验证了表单提交流程？

## 📚 参考实现

**标准参考**：`app/view/admin/news.html` - 新闻管理页面  
**最佳实践**：所有编辑器功能都应该与新闻管理保持一致

---

**严格遵循本指南，确保所有CKEditor编辑器功能的一致性和稳定性！**

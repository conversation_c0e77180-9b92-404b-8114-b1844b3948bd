<!--
  三只鱼网络科技 | 韩总 | 2024-12-20
  QiyeDIY企业建站系统 - DIY可视化编辑器
-->

<template>
  <div class="diy-editor">
    <!-- 编辑器头部工具栏 -->
    <div class="editor-header">
      <div class="header-left">
        <el-button :icon="ArrowLeft" @click="handleBack">返回</el-button>
        <div class="page-info">
          <h3 class="page-title">{{ pageData.title || '新建页面' }}</h3>
          <span class="page-status" :class="statusClass">{{ pageData.status_text }}</span>
        </div>
      </div>
      
      <div class="header-center">
        <div class="device-switcher">
          <el-radio-group v-model="currentDevice" size="small">
            <el-radio-button label="desktop">
              <el-icon><Monitor /></el-icon>
              桌面端
            </el-radio-button>
            <el-radio-button label="tablet">
              <el-icon><Iphone /></el-icon>
              平板
            </el-radio-button>
            <el-radio-button label="mobile">
              <el-icon><Cellphone /></el-icon>
              手机
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>
      
      <div class="header-right">
        <el-button :icon="View" @click="handlePreview">预览</el-button>
        <el-button :icon="Document" @click="handleSave" :loading="saving">保存</el-button>
        <el-button type="primary" :icon="Upload" @click="handlePublish" :loading="publishing">
          发布
        </el-button>
        <el-dropdown @command="handleMoreAction">
          <el-button :icon="More" circle />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="settings">页面设置</el-dropdown-item>
              <el-dropdown-item command="template">保存为模板</el-dropdown-item>
              <el-dropdown-item command="export">导出页面</el-dropdown-item>
              <el-dropdown-item command="history" divided>版本历史</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 编辑器主体 -->
    <div class="editor-body">
      <!-- 左侧组件面板 -->
      <div class="editor-sidebar">
        <el-tabs v-model="activeTab" class="sidebar-tabs">
          <!-- 组件库 -->
          <el-tab-pane label="组件" name="components">
            <div class="components-panel">
              <div class="component-category" v-for="category in componentCategories" :key="category.name">
                <h4 class="category-title">{{ category.title }}</h4>
                <div class="component-list">
                  <div 
                    v-for="component in category.components" 
                    :key="component.type"
                    class="component-item"
                    draggable="true"
                    @dragstart="handleDragStart(component)"
                  >
                    <div class="component-icon">
                      <el-icon><component :is="component.icon" /></el-icon>
                    </div>
                    <span class="component-name">{{ component.name }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 页面结构 -->
          <el-tab-pane label="结构" name="structure">
            <div class="structure-panel">
              <el-tree
                :data="pageStructure"
                :props="treeProps"
                node-key="id"
                :default-expand-all="true"
                :highlight-current="true"
                @node-click="handleNodeClick"
              >
                <template #default="{ node, data }">
                  <div class="tree-node">
                    <el-icon><component :is="getComponentIcon(data.type)" /></el-icon>
                    <span class="node-label">{{ data.name || data.type }}</span>
                    <div class="node-actions">
                      <el-icon class="action-icon" @click.stop="handleEditComponent(data)">
                        <Edit />
                      </el-icon>
                      <el-icon class="action-icon" @click.stop="handleDeleteComponent(data)">
                        <Delete />
                      </el-icon>
                    </div>
                  </div>
                </template>
              </el-tree>
            </div>
          </el-tab-pane>
          
          <!-- 样式设置 -->
          <el-tab-pane label="样式" name="styles">
            <div class="styles-panel">
              <ComponentStyleEditor 
                v-if="selectedComponent"
                :component="selectedComponent"
                @update="handleStyleUpdate"
              />
              <div v-else class="no-selection">
                <el-empty description="请选择一个组件进行样式设置" />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 中间画布区域 -->
      <div class="editor-canvas">
        <div class="canvas-container" :class="deviceClass">
          <div class="canvas-wrapper">
            <!-- 画布工具栏 -->
            <div class="canvas-toolbar">
              <div class="zoom-controls">
                <el-button-group size="small">
                  <el-button :icon="ZoomOut" @click="handleZoomOut" />
                  <el-button>{{ Math.round(canvasZoom * 100) }}%</el-button>
                  <el-button :icon="ZoomIn" @click="handleZoomIn" />
                </el-button-group>
              </div>
              <div class="canvas-actions">
                <el-button size="small" :icon="RefreshLeft" @click="handleUndo" :disabled="!canUndo">
                  撤销
                </el-button>
                <el-button size="small" :icon="RefreshRight" @click="handleRedo" :disabled="!canRedo">
                  重做
                </el-button>
              </div>
            </div>
            
            <!-- 画布内容 -->
            <div 
              class="canvas-content"
              :style="{ transform: `scale(${canvasZoom})` }"
              @drop="handleDrop"
              @dragover="handleDragOver"
            >
              <div class="page-canvas" ref="canvasRef">
                <!-- 页面组件渲染 -->
                <DiyComponent
                  v-for="component in pageComponents"
                  :key="component.id"
                  :component="component"
                  :selected="selectedComponent?.id === component.id"
                  @select="handleSelectComponent"
                  @update="handleComponentUpdate"
                  @delete="handleDeleteComponent"
                />
                
                <!-- 空状态 -->
                <div v-if="!pageComponents.length" class="empty-canvas">
                  <el-empty description="拖拽组件到这里开始设计页面" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="editor-properties">
        <el-tabs v-model="activePropertyTab" class="property-tabs">
          <!-- 组件属性 -->
          <el-tab-pane label="属性" name="properties">
            <div class="properties-panel">
              <ComponentPropertyEditor
                v-if="selectedComponent"
                :component="selectedComponent"
                @update="handlePropertyUpdate"
              />
              <div v-else class="no-selection">
                <el-empty description="请选择一个组件进行属性设置" />
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 页面设置 -->
          <el-tab-pane label="页面" name="page">
            <div class="page-settings-panel">
              <PageSettingsEditor
                :page="pageData"
                @update="handlePageUpdate"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 预览对话框 -->
    <PreviewDialog
      v-model="showPreview"
      :page-id="pageId"
      :device="currentDevice"
    />

    <!-- 页面设置对话框 -->
    <PageSettingsDialog
      v-model="showPageSettings"
      :page="pageData"
      @success="handlePageSettingsSuccess"
    />

    <!-- 保存为模板对话框 -->
    <SaveTemplateDialog
      v-model="showSaveTemplate"
      :page="pageData"
      @success="handleSaveTemplateSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { diyPageApi } from '@/api/diy'
import type { DiyPage, DiyComponent as DiyComponentType } from '@/api/diy'
import {
  ArrowLeft, Monitor, Iphone, Cellphone, View, Document, Upload, More,
  Edit, Delete, ZoomOut, ZoomIn, RefreshLeft, RefreshRight
} from '@element-plus/icons-vue'

// 组件导入
import DiyComponent from './components/DiyComponent.vue'
import ComponentStyleEditor from './components/ComponentStyleEditor.vue'
import ComponentPropertyEditor from './components/ComponentPropertyEditor.vue'
import PageSettingsEditor from './components/PageSettingsEditor.vue'
import PreviewDialog from './components/PreviewDialog.vue'
import PageSettingsDialog from './components/PageSettingsDialog.vue'
import SaveTemplateDialog from './components/SaveTemplateDialog.vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 页面数据
const pageId = computed(() => route.params.id as string)
const pageData = ref<DiyPage>({} as DiyPage)
const pageComponents = ref<DiyComponentType[]>([])
const selectedComponent = ref<DiyComponentType | null>(null)

// 编辑器状态
const currentDevice = ref('desktop')
const canvasZoom = ref(1)
const activeTab = ref('components')
const activePropertyTab = ref('properties')
const saving = ref(false)
const publishing = ref(false)

// 对话框状态
const showPreview = ref(false)
const showPageSettings = ref(false)
const showSaveTemplate = ref(false)

// 历史记录
const history = ref<any[]>([])
const historyIndex = ref(-1)

// 计算属性
const statusClass = computed(() => {
  return {
    'status-draft': pageData.value.status === 0,
    'status-published': pageData.value.status === 1,
    'status-archived': pageData.value.status === 2
  }
})

const deviceClass = computed(() => {
  return `device-${currentDevice.value}`
})

const canUndo = computed(() => historyIndex.value > 0)
const canRedo = computed(() => historyIndex.value < history.value.length - 1)

// 组件分类
const componentCategories = ref([
  {
    name: 'basic',
    title: '基础组件',
    components: [
      { type: 'text', name: '文本', icon: 'Document' },
      { type: 'image', name: '图片', icon: 'Picture' },
      { type: 'button', name: '按钮', icon: 'Pointer' },
      { type: 'divider', name: '分割线', icon: 'Minus' }
    ]
  },
  {
    name: 'layout',
    title: '布局组件',
    components: [
      { type: 'container', name: '容器', icon: 'Grid' },
      { type: 'row', name: '行', icon: 'Menu' },
      { type: 'column', name: '列', icon: 'Operation' }
    ]
  },
  {
    name: 'media',
    title: '媒体组件',
    components: [
      { type: 'video', name: '视频', icon: 'VideoPlay' },
      { type: 'audio', name: '音频', icon: 'Microphone' },
      { type: 'carousel', name: '轮播图', icon: 'Picture' }
    ]
  },
  {
    name: 'form',
    title: '表单组件',
    components: [
      { type: 'input', name: '输入框', icon: 'Edit' },
      { type: 'textarea', name: '文本域', icon: 'EditPen' },
      { type: 'select', name: '选择器', icon: 'ArrowDown' },
      { type: 'checkbox', name: '复选框', icon: 'Select' },
      { type: 'radio', name: '单选框', icon: 'CircleCheck' }
    ]
  }
])

// 页面结构树
const pageStructure = computed(() => {
  return pageComponents.value.map(component => ({
    id: component.id,
    type: component.type,
    name: component.name,
    children: component.children || []
  }))
})

const treeProps = {
  children: 'children',
  label: 'name'
}

/**
 * 加载页面数据
 */
const loadPageData = async () => {
  if (pageId.value === 'new') {
    // 新建页面
    pageData.value = {
      title: '新建页面',
      slug: '',
      status: 0,
      is_published: 0
    } as DiyPage
    pageComponents.value = []
  } else {
    try {
      const response = await diyPageApi.getById(Number(pageId.value))
      pageData.value = response.data
      pageComponents.value = response.data.components || []
    } catch (error) {
      ElMessage.error('加载页面数据失败')
      router.push('/diy/pages')
    }
  }
}

/**
 * 处理返回
 */
const handleBack = () => {
  router.push('/diy/pages')
}

/**
 * 处理预览
 */
const handlePreview = () => {
  showPreview.value = true
}

/**
 * 处理保存
 */
const handleSave = async () => {
  try {
    saving.value = true
    
    const saveData = {
      config: pageData.value.config || {},
      custom_css: pageData.value.custom_css || '',
      custom_js: pageData.value.custom_js || '',
      components: pageComponents.value
    }

    if (pageId.value === 'new') {
      // 新建页面需要先创建
      if (!pageData.value.title || !pageData.value.slug) {
        showPageSettings.value = true
        return
      }
      
      const response = await diyPageApi.create({
        ...pageData.value,
        ...saveData
      })
      
      pageData.value = response.data
      router.replace(`/diy/editor/${response.data.id}`)
    } else {
      await diyPageApi.saveContent(Number(pageId.value), saveData)
    }
    
    ElMessage.success('保存成功')
    addToHistory()
    
  } catch (error: any) {
    ElMessage.error(error.message || '保存失败')
  } finally {
    saving.value = false
  }
}

/**
 * 处理发布
 */
const handlePublish = async () => {
  try {
    publishing.value = true
    
    // 先保存
    await handleSave()
    
    // 再发布
    await diyPageApi.publish(Number(pageId.value))
    pageData.value.is_published = 1
    pageData.value.status = 1
    
    ElMessage.success('发布成功')
    
  } catch (error: any) {
    ElMessage.error(error.message || '发布失败')
  } finally {
    publishing.value = false
  }
}

/**
 * 处理更多操作
 */
const handleMoreAction = (command: string) => {
  switch (command) {
    case 'settings':
      showPageSettings.value = true
      break
    case 'template':
      showSaveTemplate.value = true
      break
    case 'export':
      handleExportPage()
      break
    case 'history':
      handleShowHistory()
      break
  }
}

/**
 * 处理拖拽开始
 */
const handleDragStart = (component: any) => {
  // 设置拖拽数据
  event.dataTransfer?.setData('component', JSON.stringify(component))
}

/**
 * 处理拖拽悬停
 */
const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
}

/**
 * 处理拖拽放置
 */
const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  
  const componentData = event.dataTransfer?.getData('component')
  if (!componentData) return
  
  const component = JSON.parse(componentData)
  addComponent(component)
}

/**
 * 添加组件
 */
const addComponent = (componentType: any) => {
  const newComponent: DiyComponentType = {
    id: Date.now(),
    type: componentType.type,
    name: componentType.name,
    config: getDefaultConfig(componentType.type),
    content: getDefaultContent(componentType.type),
    sort_order: pageComponents.value.length,
    is_enabled: 1
  }
  
  pageComponents.value.push(newComponent)
  selectedComponent.value = newComponent
  addToHistory()
}

/**
 * 获取默认配置
 */
const getDefaultConfig = (type: string) => {
  const configs = {
    text: { fontSize: '16px', color: '#333', textAlign: 'left' },
    image: { width: '100%', height: 'auto', alt: '' },
    button: { size: 'medium', type: 'primary', block: false },
    container: { padding: '20px', background: '#fff' }
  }
  return configs[type] || {}
}

/**
 * 获取默认内容
 */
const getDefaultContent = (type: string) => {
  const contents = {
    text: { text: '请输入文本内容' },
    image: { src: '', alt: '图片' },
    button: { text: '按钮', link: '' },
    container: { children: [] }
  }
  return contents[type] || {}
}

/**
 * 处理组件选择
 */
const handleSelectComponent = (component: DiyComponentType) => {
  selectedComponent.value = component
}

/**
 * 处理组件更新
 */
const handleComponentUpdate = (component: DiyComponentType) => {
  const index = pageComponents.value.findIndex(c => c.id === component.id)
  if (index !== -1) {
    pageComponents.value[index] = { ...component }
    addToHistory()
  }
}

/**
 * 处理组件删除
 */
const handleDeleteComponent = (component: DiyComponentType) => {
  const index = pageComponents.value.findIndex(c => c.id === component.id)
  if (index !== -1) {
    pageComponents.value.splice(index, 1)
    if (selectedComponent.value?.id === component.id) {
      selectedComponent.value = null
    }
    addToHistory()
  }
}

/**
 * 添加到历史记录
 */
const addToHistory = () => {
  const state = {
    components: JSON.parse(JSON.stringify(pageComponents.value)),
    timestamp: Date.now()
  }
  
  // 移除当前位置之后的历史记录
  history.value = history.value.slice(0, historyIndex.value + 1)
  history.value.push(state)
  historyIndex.value = history.value.length - 1
  
  // 限制历史记录数量
  if (history.value.length > 50) {
    history.value.shift()
    historyIndex.value--
  }
}

/**
 * 撤销操作
 */
const handleUndo = () => {
  if (canUndo.value) {
    historyIndex.value--
    const state = history.value[historyIndex.value]
    pageComponents.value = JSON.parse(JSON.stringify(state.components))
    selectedComponent.value = null
  }
}

/**
 * 重做操作
 */
const handleRedo = () => {
  if (canRedo.value) {
    historyIndex.value++
    const state = history.value[historyIndex.value]
    pageComponents.value = JSON.parse(JSON.stringify(state.components))
    selectedComponent.value = null
  }
}

/**
 * 缩放控制
 */
const handleZoomIn = () => {
  canvasZoom.value = Math.min(canvasZoom.value + 0.1, 2)
}

const handleZoomOut = () => {
  canvasZoom.value = Math.max(canvasZoom.value - 0.1, 0.5)
}

/**
 * 获取组件图标
 */
const getComponentIcon = (type: string) => {
  const icons = {
    text: 'Document',
    image: 'Picture',
    button: 'Pointer',
    container: 'Grid'
  }
  return icons[type] || 'Grid'
}

/**
 * 键盘快捷键
 */
const handleKeydown = (event: KeyboardEvent) => {
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 's':
        event.preventDefault()
        handleSave()
        break
      case 'z':
        event.preventDefault()
        if (event.shiftKey) {
          handleRedo()
        } else {
          handleUndo()
        }
        break
    }
  }
}

// 生命周期
onMounted(() => {
  loadPageData()
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style lang="scss" scoped>
.diy-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.editor-header {
  height: 60px;
  background: white;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .page-info {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .page-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
    
    .page-status {
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
      
      &.status-draft {
        background: #f0f0f0;
        color: #666;
      }
      
      &.status-published {
        background: #f0f9ff;
        color: #0369a1;
      }
    }
  }
  
  .header-center {
    .device-switcher {
      :deep(.el-radio-button__inner) {
        padding: 8px 12px;
      }
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }
}

.editor-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.editor-sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e5e5e5;
  
  .sidebar-tabs {
    height: 100%;
    
    :deep(.el-tabs__content) {
      height: calc(100% - 40px);
      overflow-y: auto;
    }
  }
}

.components-panel {
  padding: 16px;
  
  .component-category {
    margin-bottom: 24px;
    
    .category-title {
      font-size: 14px;
      font-weight: 600;
      margin: 0 0 12px 0;
      color: #333;
    }
    
    .component-list {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;
    }
    
    .component-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 12px 8px;
      border: 1px solid #e5e5e5;
      border-radius: 6px;
      cursor: grab;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: #409eff;
        background: #f0f9ff;
      }
      
      &:active {
        cursor: grabbing;
      }
      
      .component-icon {
        font-size: 20px;
        color: #409eff;
        margin-bottom: 4px;
      }
      
      .component-name {
        font-size: 12px;
        color: #666;
      }
    }
  }
}

.editor-canvas {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f0f0f0;
  
  .canvas-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 20px;
    overflow: auto;
    
    &.device-desktop .canvas-wrapper {
      width: 100%;
      max-width: 1200px;
    }
    
    &.device-tablet .canvas-wrapper {
      width: 768px;
    }
    
    &.device-mobile .canvas-wrapper {
      width: 375px;
    }
  }
  
  .canvas-wrapper {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }
  
  .canvas-toolbar {
    height: 40px;
    background: #fafafa;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
  }
  
  .canvas-content {
    min-height: 600px;
    transform-origin: top center;
    transition: transform 0.3s ease;
  }
  
  .page-canvas {
    min-height: 600px;
    position: relative;
  }
  
  .empty-canvas {
    height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.editor-properties {
  width: 320px;
  background: white;
  border-left: 1px solid #e5e5e5;
  
  .property-tabs {
    height: 100%;
    
    :deep(.el-tabs__content) {
      height: calc(100% - 40px);
      overflow-y: auto;
    }
  }
}

.no-selection {
  padding: 40px 20px;
  text-align: center;
}
</style>

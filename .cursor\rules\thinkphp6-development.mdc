---
description: 
globs: 
alwaysApply: false
---
# ThinkPHP6企业级开发规范

## 🎯 项目概述
这是一个基于ThinkPHP6框架的企业级Web应用，采用前后端分离架构，支持多端适配。

**核心技术栈**：PHP 8.0+ + ThinkPHP6 + Redis + MySQL + UniApp + Vue.js

## 📁 项目结构规范

### 核心目录说明
- [app/](mdc:app) - 应用核心代码
  - [app/admin/controller/](mdc:app/admin/controller) - 后台管理控制器
  - [app/api/controller/](mdc:app/api/controller) - API接口控制器
  - [app/model/](mdc:app/model) - 数据模型层
  - [app/service/](mdc:app/service) - 业务逻辑服务层
  - [app/validate/](mdc:app/validate) - 数据验证器
  - [app/view/](mdc:app/view) - 视图模板文件
- [config/](mdc:config) - 框架配置文件
- [public/](mdc:public) - Web根目录
  - [public/assets/](mdc:public/assets) - 静态资源
- [tools/](mdc:tools) - 项目开发工具集
- [docs/](mdc:docs) - 项目文档

### 重要配置文件
- [composer.json](mdc:composer.json) - PHP依赖管理
- [think](mdc:think) - 命令行工具入口

## 🔧 开发前必读

### 1. 开发准备流程
```bash
# 1. 项目启动检查
php tools/tool_manager.php batch .

# 2. 数据库结构分析
php tools/db_analyzer.php info

# 3. 性能基线测试
php tools/performance_analyzer.php full
```

### 2. 必须先读取现有文件
**⚠️ 重要**：编辑任何文件前必须先使用view工具查看完整内容，理解现有代码结构和逻辑。

### 3. 遵循现有代码风格
- 控制器继承 `app\BaseController`
- 模型使用 `think\Model`
- 验证器放在 `app\validate` 目录
- 路由定义在 [route/](mdc:route) 目录

## 🎨 前端开发规范

### CSS架构标准
参考：[docs/CSS架构重构指南.md](mdc:docs/CSS架构重构指南.md)

#### 样式文件优先级
1. **通用组件**：[public/assets/css/admin/common.css](mdc:public/assets/css/admin/common.css) - 8KB
2. **基础框架**：[public/assets/css/admin.css](mdc:public/assets/css/admin.css) - 45KB
3. **页面特有**：内联样式 `<style>` 标签，控制在5KB以内

#### 重构示例
参考：[docs/案例管理页面重构示例.md](mdc:docs/案例管理页面重构示例.md)

### 响应式布局要求
- 严格遵循Bootstrap 12列网格系统
- 每行列宽总和必须等于12
- 必须包含响应式断点：`col-md-*` 和 `col-lg-*`
- 优先使用Flexbox对齐
- 自定义CSS类数量限制<30个

### 图片上传组件规范
所有页面的图片上传功能必须使用统一的提示系统：
- 使用各自图片上传器的 `showMessage()` 方法
- 禁止调用全局 `showMessage()` 函数
- 3秒自动关闭，支持手动关闭
- 统一的滑入滑出动画效果

## 🛠️ 开发工具使用

### 核心工具命令
```bash
# 项目检查（项目启动时）
php tools/tool_manager.php batch .

# 数据库分析（获取表结构）
php tools/db_analyzer.php info

# 布局检测（前端开发）
php tools/layout_analyzer.php check 文件路径

# 性能分析（性能优化）
php tools/performance_analyzer.php full

# CSS/JS优化（代码清理时）
php tools/css_js_optimizer.php full
```

### 质量控制标准
- **布局质量**：≥85分(新页面)，≥80分(上线标准)
- **性能评估**：数据库查询<500ms，缓存命中率>80%
- **代码质量**：无Critical问题，High级问题<3个
- **响应式覆盖**：断点覆盖度>60%，移动端适配完整

## 📝 代码规范

### 文件头部注释规范
```php
/**
 * 三只鱼网络科技 | 韩总 | 2025-01-12
 * 文件描述 - ThinkPHP6企业级应用
 */
```

```javascript
/**
 * 三只鱼网络科技 | 韩总 | 2025-01-12
 * 文件描述 - 现代前端解决方案
 */
```

```css
/**
 * 三只鱼网络科技 | 韩总 | 2025-01-12
 * 文件描述 - 响应式设计
 */
```

### 安全和性能要求
- 所有用户输入必须验证和过滤
- 使用TP6内置安全机制
- 页面缓存使用 `think\facade\Cache`
- 会话数据存储到Redis
- 优先使用模型操作，复杂查询使用查询构造器

### API接口标准
- 统一响应格式：`{success: bool, message: string, data: object}`
- 错误码使用标准HTTP状态码+自定义业务码
- 接口参数验证使用TP6验证器
- API版本通过路由前缀管理：`/api/v1/`、`/api/v2/`

## 🚀 智能开发流程

### 信息收集阶段
1. **架构了解**：codebase-retrieval了解整体架构
2. **文件查看**：view工具查看具体文件内容
3. **现状分析**：tools/工具分析当前状态

### 开发执行阶段
1. **精确修改**：str-replace-editor进行精确修改
2. **实时检测**：layout_analyzer实时检测布局质量
3. **大文件处理**：分块处理，避免一次性修改过多内容

### 质量保证阶段
1. **批量检测**：tool_manager批量检测代码质量
2. **性能验证**：performance_analyzer验证性能指标
3. **核心工具验证**：仅使用核心工具进行最终质量验证

## ⚠️ 重要约束

### 禁止事项
- ❌ 不要在没有先读取文件的情况下编辑
- ❌ 不要随意创建文件，优先编辑现有文件
- ❌ 不要写调试打印语句，特别是前端处理时
- ❌ 不要重复造轮子，优先使用TP6内置功能
- ❌ 不要在container内再嵌套container
- ❌ 不要使用全局showMessage函数（图片上传场景）

### 必须事项
- ✅ 必须先说明理解，确认无误后再开发
- ✅ 必须按照程序原本逻辑和样式格式
- ✅ 必须借鉴原有框架的完整逻辑
- ✅ 必须使用Windows PowerShell语法
- ✅ 必须添加开发者信息头部注释
- ✅ 必须保持代码简洁原则：能用3行解决绝不写10行

## 📊 性能优化目标

### CSS优化成果
- 重构前：186KB → 重构后：53KB（减少71%）
- 新页面开发时间减少50%
- 样式调试时间减少60%
- 代码审查效率提升40%

### 开发效率提升
- 按钮样式统一，修改一处生效全部
- 分页组件标准化，新页面直接复用
- 页面特有样式就近维护，便于调试
- 减少样式冲突和覆盖问题

---

**开发原则**：安全第一、性能优先、可维护性、用户体验
**质量标准**：代码简洁、逻辑清晰、注释完整、测试充分


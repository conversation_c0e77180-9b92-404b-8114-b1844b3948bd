<?php
declare(strict_types=1);

namespace app\service;

use think\facade\Db;
use think\facade\Cache;

/**
 * 配置服务类
 * 用于获取和管理站点配置
 */
class ConfigService
{
    /**
     * 获取所有站点配置 - 直接从数据库读取
     */
    public static function getAll(): array
    {
        $cacheKey = 'site_config_all';

        // 调试模式下不使用缓存
        if (!app()->isDebug()) {
            // 先从缓存获取
            $config = Cache::get($cacheKey);
            if ($config !== false && $config !== null && is_array($config)) {
                return $config;
            }
        }

        // 直接从数据库获取，不使用默认值
        try {
            // 强制清理缓存，确保获取最新数据
            Cache::delete($cacheKey);
            
            // 使用完整的数据库查询方式
            $settings = Db::connect('mysql')->table('site_settings')->select();

            // 直接从数据库构建配置数组
            $config = [];

            // 调试日志
            error_log('ConfigService: 从数据库获取到 ' . count($settings) . ' 条配置记录');

            if ($settings && count($settings) > 0) {
                foreach ($settings as $setting) {
                    // 确保数据格式正确
                    $settingArray = is_object($setting) ? $setting->toArray() : $setting;
                    
                    if (isset($settingArray['setting_key']) && isset($settingArray['setting_value'])) {
                        $config[$settingArray['setting_key']] = $settingArray['setting_value'];
                        
                        // 调试日志
                        error_log("ConfigService: 读取配置 {$settingArray['setting_key']}: {$settingArray['setting_value']}");
                    }
                }
            } else {
                error_log('ConfigService: 数据库中没有配置数据');
            }

            // 只在非调试模式下缓存
            if (!app()->isDebug()) {
                Cache::set($cacheKey, $config, 300);
            }

            return $config;

        } catch (\Exception $e) {
            // 记录错误日志用于调试
            error_log('ConfigService数据库查询失败: ' . $e->getMessage());
            error_log('ConfigService错误堆栈: ' . $e->getTraceAsString());
            
            // 尝试备用查询方式
            try {
                error_log('ConfigService: 尝试备用查询方式');
                $settings = \think\facade\Db::query('SELECT setting_key, setting_value FROM site_settings');
                $config = [];
                
                foreach ($settings as $setting) {
                    if (isset($setting['setting_key']) && isset($setting['setting_value'])) {
                        $config[$setting['setting_key']] = $setting['setting_value'];
                    }
                }
                
                error_log('ConfigService: 备用查询成功，获取到 ' . count($config) . ' 条配置');
                return $config;
                
            } catch (\Exception $e2) {
                error_log('ConfigService备用查询也失败: ' . $e2->getMessage());
                // 如果数据库获取失败，返回空数组
                return [];
            }
        }
    }
    
    /**
     * 获取单个配置项
     */
    public static function get(string $key, $default = null)
    {
        try {
            $config = self::getAll();
            return $config[$key] ?? $default;
        } catch (\Exception $e) {
            return $default;
        }
    }
    
    /**
     * 获取站点URL
     */
    public static function getSiteUrl(): string
    {
        // 直接从数据库读取站点URL配置
        $siteUrl = self::get('site_url');
        return rtrim($siteUrl, '/');
    }
    
    /**
     * 生成完整URL
     */
    public static function url(string $path = ''): string
    {
        $siteUrl = self::getSiteUrl();
        $path = ltrim($path, '/');
        
        return $path ? $siteUrl . '/' . $path : $siteUrl;
    }
    
    /**
     * 生成资源URL（图片、CSS、JS等）
     */
    public static function asset(string $path): string
    {
        // 如果已经是完整URL，直接返回
        if (preg_match('/^https?:\/\//', $path)) {
            return $path;
        }
        
        // 如果路径不以/开头，添加/
        if (strpos($path, '/') !== 0) {
            $path = '/' . $path;
        }
        
        return self::getSiteUrl() . $path;
    }
    
    /**
     * 清除配置缓存
     */
    public static function clearCache(): void
    {
        Cache::delete('site_config_all');
    }
    

} 
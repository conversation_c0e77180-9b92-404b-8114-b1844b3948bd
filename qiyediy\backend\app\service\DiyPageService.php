<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * QiyeDIY企业建站系统 - DIY页面服务
 */

declare(strict_types=1);

namespace app\service;

use app\model\DiyPage;
use app\model\DiyComponent;
use think\db\exception\DataNotFoundException;
use think\facade\Db;
use think\facade\Cache;

/**
 * DIY页面服务类
 */
class DiyPageService
{
    /**
     * 获取页面列表
     * @param array $params 查询参数
     * @return object
     */
    public function getList(array $params): object
    {
        $query = DiyPage::with(['user']);

        // 搜索条件
        if (!empty($params['keyword'])) {
            $query->search($params['keyword']);
        }

        // 状态筛选
        if (isset($params['status']) && $params['status'] !== '') {
            $query->status((int)$params['status']);
        }

        // 发布状态筛选
        if (isset($params['is_published']) && $params['is_published'] !== '') {
            $query->published((int)$params['is_published']);
        }

        // 模板筛选
        if (!empty($params['template_id'])) {
            $query->template((int)$params['template_id']);
        }

        // 分类筛选
        if (!empty($params['category'])) {
            $query->category($params['category']);
        }

        // 创建者筛选
        if (!empty($params['user_id'])) {
            $query->where('user_id', $params['user_id']);
        }

        // 时间范围
        if (!empty($params['start_date'])) {
            $query->where('created_at', '>=', $params['start_date'] . ' 00:00:00');
        }
        if (!empty($params['end_date'])) {
            $query->where('created_at', '<=', $params['end_date'] . ' 23:59:59');
        }

        // 排序
        $sortField = $params['sort_field'] ?? 'updated_at';
        $sortOrder = $params['sort_order'] ?? 'desc';
        $query->order($sortField, $sortOrder);

        return $query->paginate($params['per_page'] ?? 15);
    }

    /**
     * 根据ID获取页面
     * @param int $id 页面ID
     * @return DiyPage|null
     */
    public function getById(int $id): ?DiyPage
    {
        return DiyPage::with(['user', 'components'])->find($id);
    }

    /**
     * 创建页面
     * @param array $data 页面数据
     * @return DiyPage
     * @throws \Exception
     */
    public function create(array $data): DiyPage
    {
        Db::startTrans();
        try {
            // 创建页面
            $page = DiyPage::createPage($data);

            // 如果有组件数据，创建组件
            if (!empty($data['components'])) {
                $this->saveComponents($page->id, $data['components']);
            }

            // 清除相关缓存
            $this->clearPageCache($page->id);

            Db::commit();
            return $page;

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 更新页面
     * @param int $id 页面ID
     * @param array $data 更新数据
     * @return DiyPage
     * @throws \Exception
     */
    public function update(int $id, array $data): DiyPage
    {
        $page = DiyPage::find($id);
        if (!$page) {
            throw new \Exception('页面不存在');
        }

        Db::startTrans();
        try {
            // 更新页面信息
            $page->updatePage($data);

            // 如果有组件数据，更新组件
            if (isset($data['components'])) {
                $this->saveComponents($page->id, $data['components']);
            }

            // 清除相关缓存
            $this->clearPageCache($page->id);

            Db::commit();
            return $page->refresh();

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 删除页面
     * @param int $id 页面ID
     * @return bool
     * @throws \Exception
     */
    public function delete(int $id): bool
    {
        $page = DiyPage::find($id);
        if (!$page) {
            throw new \Exception('页面不存在');
        }

        Db::startTrans();
        try {
            // 删除页面组件
            DiyComponent::where('page_id', $id)->delete();

            // 删除页面
            $result = $page->delete();

            // 清除相关缓存
            $this->clearPageCache($id);

            Db::commit();
            return $result;

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 批量删除页面
     * @param array $ids 页面ID数组
     * @return int 删除数量
     * @throws \Exception
     */
    public function batchDelete(array $ids): int
    {
        Db::startTrans();
        try {
            // 删除页面组件
            DiyComponent::whereIn('page_id', $ids)->delete();

            // 删除页面
            $count = DiyPage::whereIn('id', $ids)->delete();

            // 清除相关缓存
            foreach ($ids as $id) {
                $this->clearPageCache($id);
            }

            Db::commit();
            return $count;

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 发布页面
     * @param int $id 页面ID
     * @return bool
     * @throws \Exception
     */
    public function publish(int $id): bool
    {
        $page = DiyPage::find($id);
        if (!$page) {
            throw new \Exception('页面不存在');
        }

        $result = $page->publish();

        // 清除相关缓存
        $this->clearPageCache($id);

        return $result;
    }

    /**
     * 取消发布页面
     * @param int $id 页面ID
     * @return bool
     * @throws \Exception
     */
    public function unpublish(int $id): bool
    {
        $page = DiyPage::find($id);
        if (!$page) {
            throw new \Exception('页面不存在');
        }

        $result = $page->unpublish();

        // 清除相关缓存
        $this->clearPageCache($id);

        return $result;
    }

    /**
     * 复制页面
     * @param int $id 页面ID
     * @param array $data 复制数据
     * @return DiyPage
     * @throws \Exception
     */
    public function copy(int $id, array $data): DiyPage
    {
        $sourcePage = DiyPage::with(['components'])->find($id);
        if (!$sourcePage) {
            throw new \Exception('源页面不存在');
        }

        Db::startTrans();
        try {
            // 创建新页面
            $newPageData = [
                'title' => $data['title'] ?? $sourcePage->title . ' - 副本',
                'slug' => $data['slug'] ?? $sourcePage->slug . '_copy_' . time(),
                'description' => $sourcePage->description,
                'keywords' => $sourcePage->keywords,
                'template_id' => $sourcePage->template_id,
                'category' => $sourcePage->category,
                'config' => $sourcePage->config,
                'custom_css' => $sourcePage->custom_css,
                'custom_js' => $sourcePage->custom_js,
                'status' => DiyPage::STATUS_DRAFT,
                'is_published' => 0
            ];

            $newPage = DiyPage::createPage($newPageData);

            // 复制组件
            if ($sourcePage->components) {
                $components = [];
                foreach ($sourcePage->components as $component) {
                    $components[] = [
                        'type' => $component->type,
                        'name' => $component->name,
                        'config' => $component->config,
                        'content' => $component->content,
                        'sort_order' => $component->sort_order,
                        'is_enabled' => $component->is_enabled
                    ];
                }
                $this->saveComponents($newPage->id, $components);
            }

            Db::commit();
            return $newPage;

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 保存页面内容
     * @param int $id 页面ID
     * @param array $data 内容数据
     * @return bool
     * @throws \Exception
     */
    public function saveContent(int $id, array $data): bool
    {
        $page = DiyPage::find($id);
        if (!$page) {
            throw new \Exception('页面不存在');
        }

        Db::startTrans();
        try {
            // 更新页面配置
            if (isset($data['config'])) {
                $page->config = $data['config'];
            }

            // 更新自定义样式
            if (isset($data['custom_css'])) {
                $page->custom_css = $data['custom_css'];
            }

            // 更新自定义脚本
            if (isset($data['custom_js'])) {
                $page->custom_js = $data['custom_js'];
            }

            $page->save();

            // 保存组件
            if (isset($data['components'])) {
                $this->saveComponents($id, $data['components']);
            }

            // 清除相关缓存
            $this->clearPageCache($id);

            Db::commit();
            return true;

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 保存页面组件
     * @param int $pageId 页面ID
     * @param array $components 组件数据
     * @return bool
     */
    protected function saveComponents(int $pageId, array $components): bool
    {
        // 删除原有组件
        DiyComponent::where('page_id', $pageId)->delete();

        // 保存新组件
        foreach ($components as $index => $component) {
            DiyComponent::create([
                'page_id' => $pageId,
                'type' => $component['type'],
                'name' => $component['name'] ?? '',
                'config' => $component['config'] ?? '{}',
                'content' => $component['content'] ?? '{}',
                'sort_order' => $component['sort_order'] ?? $index,
                'is_enabled' => $component['is_enabled'] ?? 1
            ]);
        }

        return true;
    }

    /**
     * 获取预览数据
     * @param int $id 页面ID
     * @return array
     * @throws \Exception
     */
    public function getPreviewData(int $id): array
    {
        $page = DiyPage::with(['components' => function($query) {
            $query->where('is_enabled', 1)->order('sort_order', 'asc');
        }])->find($id);

        if (!$page) {
            throw new \Exception('页面不存在');
        }

        return [
            'page' => $page->toArray(),
            'components' => $page->components->toArray(),
            'preview_url' => $this->generatePreviewUrl($page)
        ];
    }

    /**
     * 生成预览URL
     * @param DiyPage $page 页面对象
     * @return string
     */
    protected function generatePreviewUrl(DiyPage $page): string
    {
        $baseUrl = config('app.frontend_url', 'http://localhost:3000');
        return $baseUrl . '/preview/' . $page->id . '?token=' . $this->generatePreviewToken($page->id);
    }

    /**
     * 生成预览令牌
     * @param int $pageId 页面ID
     * @return string
     */
    protected function generatePreviewToken(int $pageId): string
    {
        $token = md5($pageId . time() . uniqid());
        Cache::set('preview_token_' . $pageId, $token, 3600); // 1小时有效
        return $token;
    }

    /**
     * 获取页面统计
     * @return array
     */
    public function getStatistics(): array
    {
        $total = DiyPage::count();
        $published = DiyPage::where('is_published', 1)->count();
        $draft = DiyPage::where('status', DiyPage::STATUS_DRAFT)->count();
        
        // 今日新增
        $todayNew = DiyPage::whereDate('created_at', date('Y-m-d'))->count();
        
        // 本周新增
        $weekStart = date('Y-m-d', strtotime('this week'));
        $weekNew = DiyPage::where('created_at', '>=', $weekStart)->count();
        
        // 本月新增
        $monthStart = date('Y-m-01');
        $monthNew = DiyPage::where('created_at', '>=', $monthStart)->count();

        // 分类统计
        $categoryStats = DiyPage::field('category, COUNT(*) as count')
                               ->where('category', '<>', '')
                               ->group('category')
                               ->select()
                               ->toArray();

        // 模板使用统计
        $templateStats = Db::table('diy_pages p')
            ->leftJoin('diy_templates t', 'p.template_id', '=', 't.id')
            ->field('t.name as template_name, COUNT(p.id) as count')
            ->group('p.template_id, t.name')
            ->select()
            ->toArray();

        return [
            'total' => $total,
            'published' => $published,
            'draft' => $draft,
            'today_new' => $todayNew,
            'week_new' => $weekNew,
            'month_new' => $monthNew,
            'category_stats' => $categoryStats,
            'template_stats' => $templateStats
        ];
    }

    /**
     * 导出页面
     * @param array $params 导出参数
     * @return string 文件路径
     * @throws \Exception
     */
    public function export(array $params): string
    {
        // 获取页面数据
        $query = DiyPage::with(['components']);

        // 应用筛选条件
        if (!empty($params['ids'])) {
            $query->whereIn('id', explode(',', $params['ids']));
        }
        if (!empty($params['category'])) {
            $query->where('category', $params['category']);
        }

        $pages = $query->select();

        // 准备导出数据
        $exportData = [];
        foreach ($pages as $page) {
            $exportData[] = [
                'page' => $page->toArray(),
                'components' => $page->components->toArray()
            ];
        }

        // 生成文件
        $filename = 'diy_pages_' . date('YmdHis') . '.json';
        $filePath = upload_path('export', $filename);
        $fullPath = public_path() . '/' . $filePath;

        if (!is_dir(dirname($fullPath))) {
            mkdir(dirname($fullPath), 0755, true);
        }

        file_put_contents($fullPath, json_encode($exportData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

        return $filePath;
    }

    /**
     * 导入页面
     * @param string $filePath 文件路径
     * @return array 导入结果
     * @throws \Exception
     */
    public function import(string $filePath): array
    {
        if (!file_exists($filePath)) {
            throw new \Exception('导入文件不存在');
        }

        $content = file_get_contents($filePath);
        $data = json_decode($content, true);

        if (!$data) {
            throw new \Exception('文件格式错误');
        }

        $successCount = 0;
        $errorCount = 0;
        $errors = [];

        Db::startTrans();
        try {
            foreach ($data as $index => $item) {
                try {
                    $pageData = $item['page'];
                    $components = $item['components'] ?? [];

                    // 移除ID和时间戳
                    unset($pageData['id'], $pageData['created_at'], $pageData['updated_at']);
                    
                    // 确保slug唯一
                    $originalSlug = $pageData['slug'];
                    $counter = 1;
                    while (DiyPage::where('slug', $pageData['slug'])->exists()) {
                        $pageData['slug'] = $originalSlug . '_' . $counter;
                        $counter++;
                    }

                    // 创建页面
                    $page = DiyPage::createPage($pageData);

                    // 创建组件
                    if ($components) {
                        $this->saveComponents($page->id, $components);
                    }

                    $successCount++;

                } catch (\Exception $e) {
                    $errorCount++;
                    $errors[] = "第" . ($index + 1) . "个页面: " . $e->getMessage();
                }
            }

            Db::commit();

            return [
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'errors' => $errors
            ];

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 清除页面缓存
     * @param int $pageId 页面ID
     */
    protected function clearPageCache(int $pageId): void
    {
        Cache::delete('diy_page_' . $pageId);
        Cache::delete('diy_page_components_' . $pageId);
        Cache::delete('preview_token_' . $pageId);
        Cache::tag('diy_pages')->clear();
    }
}

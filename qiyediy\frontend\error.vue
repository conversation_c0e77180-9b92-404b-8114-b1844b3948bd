<!--
  三只鱼网络科技 | 韩总 | 2024-12-20
  QiyeDIY企业建站系统 - 错误页面
-->

<template>
  <div class="error-page">
    <div class="error-container">
      <!-- 错误图标和动画 -->
      <div class="error-animation">
        <div class="error-icon" :class="`error-${error.statusCode}`">
          <component :is="errorIcon" />
        </div>
        <div class="error-particles">
          <div v-for="i in 6" :key="i" class="particle" :style="getParticleStyle(i)"></div>
        </div>
      </div>
      
      <!-- 错误信息 -->
      <div class="error-content">
        <h1 class="error-code">{{ error.statusCode }}</h1>
        <h2 class="error-title">{{ errorTitle }}</h2>
        <p class="error-description">{{ errorDescription }}</p>
        
        <!-- 错误详情（开发环境） -->
        <details v-if="isDev && error.stack" class="error-details">
          <summary>错误详情</summary>
          <pre class="error-stack">{{ error.stack }}</pre>
        </details>
      </div>
      
      <!-- 操作按钮 -->
      <div class="error-actions">
        <button @click="goBack" class="btn btn-secondary">
          <Icon name="arrow-left" />
          返回上页
        </button>
        
        <NuxtLink to="/" class="btn btn-primary">
          <Icon name="home" />
          回到首页
        </NuxtLink>
        
        <button @click="reload" class="btn btn-outline">
          <Icon name="refresh" />
          重新加载
        </button>
      </div>
      
      <!-- 帮助信息 -->
      <div class="error-help">
        <div class="help-section">
          <h3>可能的解决方案：</h3>
          <ul class="help-list">
            <li v-for="solution in solutions" :key="solution">{{ solution }}</li>
          </ul>
        </div>
        
        <div class="help-section">
          <h3>需要帮助？</h3>
          <div class="help-contacts">
            <a href="mailto:<EMAIL>" class="help-link">
              <Icon name="mail" />
              联系技术支持
            </a>
            <a href="/docs" class="help-link">
              <Icon name="book" />
              查看帮助文档
            </a>
            <button @click="reportError" class="help-link">
              <Icon name="bug" />
              报告问题
            </button>
          </div>
        </div>
      </div>
      
      <!-- 搜索建议 -->
      <div v-if="error.statusCode === 404" class="search-suggestion">
        <h3>没找到您要的内容？试试搜索：</h3>
        <SearchBox placeholder="搜索页面、模板..." />
      </div>
    </div>
    
    <!-- 背景装饰 -->
    <div class="error-background">
      <div class="bg-shape shape-1"></div>
      <div class="bg-shape shape-2"></div>
      <div class="bg-shape shape-3"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

// 错误图标组件
const Icon404 = defineComponent({
  template: `
    <svg viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="100" cy="100" r="80" stroke="currentColor" stroke-width="4" fill="none" opacity="0.3"/>
      <path d="M70 70 L130 130 M130 70 L70 130" stroke="currentColor" stroke-width="6" stroke-linecap="round"/>
      <text x="100" y="160" text-anchor="middle" fill="currentColor" font-size="24" font-weight="bold">404</text>
    </svg>
  `
})

const Icon500 = defineComponent({
  template: `
    <svg viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="100" cy="100" r="80" stroke="currentColor" stroke-width="4" fill="none" opacity="0.3"/>
      <path d="M100 60 L100 120 M100 140 L100 150" stroke="currentColor" stroke-width="6" stroke-linecap="round"/>
      <text x="100" y="180" text-anchor="middle" fill="currentColor" font-size="24" font-weight="bold">500</text>
    </svg>
  `
})

const Icon403 = defineComponent({
  template: `
    <svg viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="60" y="80" width="80" height="60" rx="8" stroke="currentColor" stroke-width="4" fill="none"/>
      <path d="M80 80 V70 C80 60 88 50 100 50 C112 50 120 60 120 70 V80" stroke="currentColor" stroke-width="4" fill="none"/>
      <circle cx="100" cy="110" r="6" fill="currentColor"/>
      <text x="100" y="170" text-anchor="middle" fill="currentColor" font-size="24" font-weight="bold">403</text>
    </svg>
  `
})

const IconDefault = defineComponent({
  template: `
    <svg viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="100" cy="100" r="80" stroke="currentColor" stroke-width="4" fill="none" opacity="0.3"/>
      <path d="M100 60 L100 120 M100 140 L100 150" stroke="currentColor" stroke-width="6" stroke-linecap="round"/>
      <text x="100" y="180" text-anchor="middle" fill="currentColor" font-size="20" font-weight="bold">ERROR</text>
    </svg>
  `
})

// Props
const props = defineProps({
  error: {
    type: Object,
    required: true
  }
})

const router = useRouter()
const isDev = process.dev

// 计算属性
const errorIcon = computed(() => {
  switch (props.error.statusCode) {
    case 404:
      return Icon404
    case 500:
      return Icon500
    case 403:
      return Icon403
    default:
      return IconDefault
  }
})

const errorTitle = computed(() => {
  const titles: Record<number, string> = {
    400: '请求错误',
    401: '未授权访问',
    403: '禁止访问',
    404: '页面未找到',
    500: '服务器内部错误',
    502: '网关错误',
    503: '服务不可用',
    504: '网关超时'
  }
  
  return titles[props.error.statusCode] || '发生错误'
})

const errorDescription = computed(() => {
  const descriptions: Record<number, string> = {
    400: '您的请求包含错误的语法或参数，请检查后重试。',
    401: '您需要登录才能访问此页面，请先进行身份验证。',
    403: '您没有权限访问此页面，请联系管理员获取访问权限。',
    404: '抱歉，您访问的页面不存在或已被移动。请检查网址是否正确，或使用搜索功能查找您需要的内容。',
    500: '服务器遇到了一个意外的错误，我们正在努力修复。请稍后重试，或联系技术支持。',
    502: '网关服务器收到了无效的响应，请稍后重试。',
    503: '服务暂时不可用，可能正在维护中。请稍后重试。',
    504: '网关服务器超时，请检查网络连接或稍后重试。'
  }
  
  return descriptions[props.error.statusCode] || props.error.statusMessage || '发生了未知错误，请稍后重试。'
})

const solutions = computed(() => {
  const solutionMap: Record<number, string[]> = {
    400: [
      '检查请求参数是否正确',
      '确认数据格式符合要求',
      '清除浏览器缓存后重试'
    ],
    401: [
      '请先登录您的账户',
      '检查登录状态是否过期',
      '清除浏览器Cookie后重新登录'
    ],
    403: [
      '联系管理员获取访问权限',
      '确认您的账户状态正常',
      '检查是否需要特殊权限'
    ],
    404: [
      '检查网址拼写是否正确',
      '使用搜索功能查找内容',
      '从首页导航到目标页面',
      '查看网站地图寻找相关页面'
    ],
    500: [
      '刷新页面重新尝试',
      '清除浏览器缓存',
      '稍后再次访问',
      '联系技术支持报告问题'
    ],
    502: [
      '检查网络连接',
      '稍后重新访问',
      '尝试使用其他网络'
    ],
    503: [
      '等待服务恢复',
      '查看系统公告',
      '稍后重新访问'
    ],
    504: [
      '检查网络连接速度',
      '稍后重新尝试',
      '联系网络服务提供商'
    ]
  }
  
  return solutionMap[props.error.statusCode] || [
    '刷新页面重新尝试',
    '检查网络连接',
    '联系技术支持'
  ]
})

// 方法
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

const reload = () => {
  window.location.reload()
}

const reportError = async () => {
  try {
    await $fetch('/api/error-report', {
      method: 'POST',
      body: {
        error: {
          statusCode: props.error.statusCode,
          statusMessage: props.error.statusMessage,
          stack: props.error.stack,
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        }
      }
    })
    
    alert('错误报告已提交，感谢您的反馈！')
  } catch (error) {
    console.error('提交错误报告失败:', error)
    alert('提交失败，请稍后重试或直接联系技术支持。')
  }
}

const getParticleStyle = (index: number) => {
  const angle = (index * 60) + Math.random() * 30
  const distance = 60 + Math.random() * 40
  const delay = index * 0.2
  
  return {
    '--angle': `${angle}deg`,
    '--distance': `${distance}px`,
    '--delay': `${delay}s`
  }
}

// SEO设置
useHead({
  title: `${props.error.statusCode} - ${errorTitle.value}`,
  meta: [
    { name: 'robots', content: 'noindex,nofollow' }
  ]
})

// 错误统计
onMounted(() => {
  // 记录错误统计
  if (typeof window !== 'undefined') {
    $fetch('/api/analytics/error', {
      method: 'POST',
      body: {
        statusCode: props.error.statusCode,
        url: window.location.href,
        userAgent: navigator.userAgent,
        referrer: document.referrer
      }
    }).catch(() => {
      // 静默处理统计错误
    })
  }
})
</script>

<style lang="scss" scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  padding: 20px;
}

.error-container {
  background: white;
  border-radius: 24px;
  padding: 48px;
  max-width: 600px;
  width: 100%;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

.error-animation {
  position: relative;
  margin-bottom: 32px;
}

.error-icon {
  width: 120px;
  height: 120px;
  margin: 0 auto;
  color: #ef4444;
  animation: float 3s ease-in-out infinite;
  
  &.error-404 {
    color: #f59e0b;
  }
  
  &.error-500 {
    color: #ef4444;
  }
  
  &.error-403 {
    color: #8b5cf6;
  }
}

.error-particles {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: currentColor;
  border-radius: 50%;
  opacity: 0;
  animation: particle-float 2s ease-in-out infinite;
  animation-delay: var(--delay);
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: inherit;
    border-radius: inherit;
    transform: rotate(var(--angle)) translateY(var(--distance));
  }
}

.error-content {
  margin-bottom: 32px;
}

.error-code {
  font-size: 4rem;
  font-weight: 800;
  color: #1e293b;
  margin: 0 0 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.error-title {
  font-size: 2rem;
  font-weight: 600;
  color: #334155;
  margin: 0 0 16px;
}

.error-description {
  font-size: 1.125rem;
  color: #64748b;
  line-height: 1.6;
  margin: 0 0 24px;
}

.error-details {
  text-align: left;
  margin-top: 24px;
  
  summary {
    cursor: pointer;
    font-weight: 500;
    color: #ef4444;
    margin-bottom: 12px;
  }
}

.error-stack {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  font-size: 12px;
  color: #475569;
  overflow-x: auto;
  white-space: pre-wrap;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 48px;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  cursor: pointer;
  
  &.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    }
  }
  
  &.btn-secondary {
    background: #f1f5f9;
    color: #475569;
    
    &:hover {
      background: #e2e8f0;
      transform: translateY(-1px);
    }
  }
  
  &.btn-outline {
    border-color: #e2e8f0;
    color: #475569;
    background: white;
    
    &:hover {
      border-color: #cbd5e0;
      background: #f8fafc;
      transform: translateY(-1px);
    }
  }
}

.error-help {
  text-align: left;
  margin-bottom: 32px;
}

.help-section {
  margin-bottom: 24px;
  
  h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #334155;
    margin: 0 0 12px;
  }
}

.help-list {
  list-style: none;
  padding: 0;
  margin: 0;
  
  li {
    padding: 8px 0;
    color: #64748b;
    position: relative;
    padding-left: 20px;
    
    &::before {
      content: '•';
      color: #667eea;
      position: absolute;
      left: 0;
      font-weight: bold;
    }
  }
}

.help-contacts {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.help-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #475569;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    background: #f1f5f9;
    border-color: #cbd5e0;
    transform: translateY(-1px);
  }
}

.search-suggestion {
  text-align: center;
  
  h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #334155;
    margin: 0 0 16px;
  }
}

.error-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.bg-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
  
  &.shape-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }
  
  &.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
  }
  
  &.shape-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
  }
}

// 动画
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes particle-float {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .error-container {
    padding: 32px 24px;
    margin: 16px;
  }
  
  .error-code {
    font-size: 3rem;
  }
  
  .error-title {
    font-size: 1.5rem;
  }
  
  .error-description {
    font-size: 1rem;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 100%;
    max-width: 200px;
    justify-content: center;
  }
  
  .help-contacts {
    flex-direction: column;
  }
  
  .help-link {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .error-icon {
    width: 80px;
    height: 80px;
  }
  
  .error-code {
    font-size: 2.5rem;
  }
  
  .error-title {
    font-size: 1.25rem;
  }
}
</style>

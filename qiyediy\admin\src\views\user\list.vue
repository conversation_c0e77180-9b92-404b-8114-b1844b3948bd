<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 用户管理页面
-->

<template>
  <div class="user-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">用户管理</h2>
        <p class="page-description">管理系统用户账户、权限和基本信息</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="handleAdd" v-if="userStore.hasPermission('user.create')">
          新增用户
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="用户名/邮箱/姓名"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="全部状态" clearable style="width: 120px">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="角色">
          <el-select v-model="searchForm.role_id" placeholder="全部角色" clearable style="width: 150px">
            <el-option
              v-for="role in roleList"
              :key="role.id"
              :label="role.name"
              :value="role.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="注册时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
          <el-button :icon="Refresh" @click="handleReset">重置</el-button>
          <el-button :icon="Download" @click="handleExport" v-if="userStore.hasPermission('user.export')">
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <!-- 表格工具栏 -->
      <div class="table-toolbar">
        <div class="toolbar-left">
          <el-button
            type="danger"
            :icon="Delete"
            :disabled="!selectedIds.length"
            @click="handleBatchDelete"
            v-if="userStore.hasPermission('user.delete')"
          >
            批量删除
          </el-button>
          <el-button :icon="Upload" @click="showImportDialog = true" v-if="userStore.hasPermission('user.import')">
            导入用户
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-tooltip content="刷新数据">
            <el-button :icon="Refresh" circle @click="loadData" />
          </el-tooltip>
          <el-tooltip content="列设置">
            <el-button :icon="Setting" circle @click="showColumnSettings = true" />
          </el-tooltip>
        </div>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        stripe
        class="user-table"
      >
        <el-table-column type="selection" width="50" />
        
        <el-table-column prop="id" label="ID" width="80" sortable="custom" />
        
        <el-table-column label="用户信息" min-width="200">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :size="40" :src="row.avatar_url">
                <el-icon><User /></el-icon>
              </el-avatar>
              <div class="user-details">
                <div class="username">{{ row.username }}</div>
                <div class="email">{{ row.email }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="real_name" label="真实姓名" width="120" />
        
        <el-table-column prop="phone" label="手机号" width="130">
          <template #default="{ row }">
            {{ row.phone || '-' }}
          </template>
        </el-table-column>
        
        <el-table-column label="角色" width="150">
          <template #default="{ row }">
            <el-tag
              v-for="role in row.roles"
              :key="role.id"
              :type="role.slug === 'super_admin' ? 'danger' : 'primary'"
              size="small"
              class="role-tag"
            >
              {{ role.name }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(row)"
              :disabled="!userStore.hasPermission('user.update') || row.id === userStore.user?.id"
            />
          </template>
        </el-table-column>
        
        <el-table-column prop="login_count" label="登录次数" width="100" sortable="custom" />
        
        <el-table-column prop="last_login_at" label="最后登录" width="160" sortable="custom">
          <template #default="{ row }">
            {{ formatDate(row.last_login_at) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="注册时间" width="160" sortable="custom">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              :icon="Edit"
              @click="handleEdit(row)"
              v-if="userStore.hasPermission('user.update')"
            >
              编辑
            </el-button>
            <el-button
              type="warning"
              size="small"
              :icon="Key"
              @click="handleResetPassword(row)"
              v-if="userStore.hasPermission('user.reset_password')"
            >
              重置密码
            </el-button>
            <el-button
              type="danger"
              size="small"
              :icon="Delete"
              @click="handleDelete(row)"
              v-if="userStore.hasPermission('user.delete') && row.id !== userStore.user?.id"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadData"
          @current-change="loadData"
        />
      </div>
    </el-card>

    <!-- 用户表单对话框 -->
    <UserFormDialog
      v-model="showUserDialog"
      :user-data="currentUser"
      :is-edit="isEdit"
      @success="handleFormSuccess"
    />

    <!-- 重置密码对话框 -->
    <ResetPasswordDialog
      v-model="showResetDialog"
      :user-data="currentUser"
      @success="handleResetSuccess"
    />

    <!-- 导入用户对话框 -->
    <ImportUserDialog
      v-model="showImportDialog"
      @success="handleImportSuccess"
    />

    <!-- 列设置对话框 -->
    <ColumnSettingsDialog
      v-model="showColumnSettings"
      :columns="tableColumns"
      @change="handleColumnChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { userApi } from '@/api/user'
import { roleApi } from '@/api/role'
import type { User, Role } from '@/types/auth'
import {
  Plus, Search, Refresh, Download, Delete, Upload, Setting,
  Edit, Key, User as UserIcon
} from '@element-plus/icons-vue'
import UserFormDialog from './components/UserFormDialog.vue'
import ResetPasswordDialog from './components/ResetPasswordDialog.vue'
import ImportUserDialog from './components/ImportUserDialog.vue'
import ColumnSettingsDialog from './components/ColumnSettingsDialog.vue'

const userStore = useUserStore()

// 状态
const loading = ref(false)
const tableData = ref<User[]>([])
const roleList = ref<Role[]>([])
const selectedIds = ref<number[]>([])
const showUserDialog = ref(false)
const showResetDialog = ref(false)
const showImportDialog = ref(false)
const showColumnSettings = ref(false)
const currentUser = ref<User | null>(null)
const isEdit = ref(false)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  role_id: '',
  dateRange: []
})

// 分页
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 排序
const sortParams = reactive({
  sort_field: 'created_at',
  sort_order: 'desc'
})

// 表格列配置
const tableColumns = ref([
  { key: 'id', label: 'ID', visible: true },
  { key: 'user_info', label: '用户信息', visible: true },
  { key: 'real_name', label: '真实姓名', visible: true },
  { key: 'phone', label: '手机号', visible: true },
  { key: 'roles', label: '角色', visible: true },
  { key: 'status', label: '状态', visible: true },
  { key: 'login_count', label: '登录次数', visible: true },
  { key: 'last_login_at', label: '最后登录', visible: true },
  { key: 'created_at', label: '注册时间', visible: true }
])

/**
 * 加载数据
 */
const loadData = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.current,
      per_page: pagination.size,
      keyword: searchForm.keyword,
      status: searchForm.status,
      role_id: searchForm.role_id,
      start_date: searchForm.dateRange?.[0],
      end_date: searchForm.dateRange?.[1],
      ...sortParams
    }
    
    const response = await userApi.getList(params)
    tableData.value = response.data.list
    pagination.total = response.data.total
    
  } catch (error) {
    console.error('加载用户数据失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 加载角色列表
 */
const loadRoles = async () => {
  try {
    const response = await roleApi.getAll()
    roleList.value = response.data
  } catch (error) {
    console.error('加载角色列表失败:', error)
  }
}

/**
 * 搜索
 */
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

/**
 * 重置搜索
 */
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: '',
    role_id: '',
    dateRange: []
  })
  pagination.current = 1
  loadData()
}

/**
 * 新增用户
 */
const handleAdd = () => {
  currentUser.value = null
  isEdit.value = false
  showUserDialog.value = true
}

/**
 * 编辑用户
 */
const handleEdit = (row: User) => {
  currentUser.value = { ...row }
  isEdit.value = true
  showUserDialog.value = true
}

/**
 * 删除用户
 */
const handleDelete = async (row: User) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${row.username}" 吗？`,
      '删除确认',
      { type: 'warning' }
    )
    
    await userApi.delete(row.id)
    ElMessage.success('删除成功')
    loadData()
    
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  }
}

/**
 * 批量删除
 */
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedIds.value.length} 个用户吗？`,
      '批量删除确认',
      { type: 'warning' }
    )
    
    await userApi.batchDelete(selectedIds.value)
    ElMessage.success('批量删除成功')
    selectedIds.value = []
    loadData()
    
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '批量删除失败')
    }
  }
}

/**
 * 状态切换
 */
const handleStatusChange = async (row: User) => {
  try {
    if (row.status === 1) {
      await userApi.enable(row.id)
      ElMessage.success('用户已启用')
    } else {
      await userApi.disable(row.id)
      ElMessage.success('用户已禁用')
    }
  } catch (error: any) {
    // 恢复状态
    row.status = row.status === 1 ? 0 : 1
    ElMessage.error(error.message || '状态更新失败')
  }
}

/**
 * 重置密码
 */
const handleResetPassword = (row: User) => {
  currentUser.value = { ...row }
  showResetDialog.value = true
}

/**
 * 导出用户
 */
const handleExport = async () => {
  try {
    const params = {
      keyword: searchForm.keyword,
      status: searchForm.status,
      role_id: searchForm.role_id,
      start_date: searchForm.dateRange?.[0],
      end_date: searchForm.dateRange?.[1]
    }
    
    const response = await userApi.export(params)
    
    // 创建下载链接
    const link = document.createElement('a')
    link.href = response.data.file_path
    link.download = `users_${new Date().getTime()}.xlsx`
    link.click()
    
    ElMessage.success('导出成功')
    
  } catch (error: any) {
    ElMessage.error(error.message || '导出失败')
  }
}

/**
 * 选择变化
 */
const handleSelectionChange = (selection: User[]) => {
  selectedIds.value = selection.map(item => item.id)
}

/**
 * 排序变化
 */
const handleSortChange = ({ prop, order }: any) => {
  if (order) {
    sortParams.sort_field = prop
    sortParams.sort_order = order === 'ascending' ? 'asc' : 'desc'
  } else {
    sortParams.sort_field = 'created_at'
    sortParams.sort_order = 'desc'
  }
  loadData()
}

/**
 * 表单成功回调
 */
const handleFormSuccess = () => {
  showUserDialog.value = false
  loadData()
}

/**
 * 重置密码成功回调
 */
const handleResetSuccess = () => {
  showResetDialog.value = false
  ElMessage.success('密码重置成功')
}

/**
 * 导入成功回调
 */
const handleImportSuccess = () => {
  showImportDialog.value = false
  loadData()
}

/**
 * 列设置变化
 */
const handleColumnChange = (columns: any[]) => {
  tableColumns.value = columns
}

/**
 * 格式化日期
 */
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

onMounted(() => {
  loadData()
  loadRoles()
})
</script>

<style lang="scss" scoped>
.user-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left {
  .page-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 8px 0;
  }
  
  .page-description {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin: 0;
  }
}

.search-card {
  margin-bottom: 20px;
  border-radius: 8px;
  
  .search-form {
    margin-bottom: 0;
  }
}

.table-card {
  border-radius: 8px;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.toolbar-left {
  display: flex;
  gap: 8px;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.user-table {
  .user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .user-details {
      .username {
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 4px;
      }
      
      .email {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }
  
  .role-tag {
    margin-right: 4px;
    margin-bottom: 4px;
  }
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

// 响应式设计
@media (max-width: 768px) {
  .user-list-container {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .search-form {
    :deep(.el-form-item) {
      margin-bottom: 16px;
    }
  }
  
  .table-toolbar {
    flex-direction: column;
    gap: 12px;
  }
  
  .user-table {
    :deep(.el-table__body-wrapper) {
      overflow-x: auto;
    }
  }
}
</style>

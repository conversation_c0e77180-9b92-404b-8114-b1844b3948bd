<!--
  三只鱼网络科技 | 韩总 | 2024-12-20
  QiyeDIY企业建站系统 - 操作日志页面
-->

<template>
  <div class="logs-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>操作日志</h1>
        <p>查看系统操作记录和用户行为日志</p>
      </div>
      
      <div class="header-right">
        <el-button @click="exportLogs" :loading="exporting">
          <Icon name="download" />
          导出日志
        </el-button>
        <el-button @click="showClearDialog = true" type="danger">
          <Icon name="delete" />
          清空日志
        </el-button>
      </div>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon">
          <Icon name="activity" color="#667eea" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total_logs }}</div>
          <div class="stat-label">总日志数</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <Icon name="users" color="#10b981" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.active_users }}</div>
          <div class="stat-label">活跃用户</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <Icon name="calendar" color="#f59e0b" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.today_logs }}</div>
          <div class="stat-label">今日日志</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <Icon name="alert-triangle" color="#ef4444" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.error_logs }}</div>
          <div class="stat-label">错误日志</div>
        </div>
      </div>
    </div>
    
    <!-- 筛选器 -->
    <div class="filters-section">
      <el-card>
        <div class="filters-content">
          <div class="filter-row">
            <div class="filter-item">
              <label>日志级别</label>
              <el-select v-model="filters.level" placeholder="全部级别" clearable>
                <el-option label="信息" value="info" />
                <el-option label="警告" value="warning" />
                <el-option label="错误" value="error" />
                <el-option label="调试" value="debug" />
              </el-select>
            </div>
            
            <div class="filter-item">
              <label>操作类型</label>
              <el-select v-model="filters.action" placeholder="全部操作" clearable>
                <el-option label="登录" value="login" />
                <el-option label="登出" value="logout" />
                <el-option label="创建" value="create" />
                <el-option label="更新" value="update" />
                <el-option label="删除" value="delete" />
                <el-option label="查看" value="view" />
              </el-select>
            </div>
            
            <div class="filter-item">
              <label>用户</label>
              <el-select
                v-model="filters.user_id"
                placeholder="全部用户"
                clearable
                filterable
                remote
                :remote-method="searchUsers"
                :loading="userSearchLoading"
              >
                <el-option
                  v-for="user in userOptions"
                  :key="user.id"
                  :label="user.username"
                  :value="user.id"
                />
              </el-select>
            </div>
            
            <div class="filter-item">
              <label>时间范围</label>
              <el-date-picker
                v-model="filters.dateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </div>
          </div>
          
          <div class="filter-row">
            <div class="filter-item">
              <label>关键词</label>
              <el-input
                v-model="filters.keyword"
                placeholder="搜索日志内容..."
                @keyup.enter="loadLogs"
              >
                <template #prefix>
                  <Icon name="search" />
                </template>
              </el-input>
            </div>
            
            <div class="filter-item">
              <label>IP地址</label>
              <el-input
                v-model="filters.ip"
                placeholder="IP地址"
                @keyup.enter="loadLogs"
              />
            </div>
            
            <div class="filter-actions">
              <el-button @click="loadLogs" type="primary">
                <Icon name="search" />
                搜索
              </el-button>
              <el-button @click="resetFilters">
                <Icon name="refresh" />
                重置
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 日志列表 -->
    <div class="logs-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>日志列表</span>
            <div class="header-actions">
              <el-button @click="loadLogs" size="small" :loading="loading">
                <Icon name="refresh" />
                刷新
              </el-button>
            </div>
          </div>
        </template>
        
        <el-table
          :data="logs"
          v-loading="loading"
          stripe
          @sort-change="onSortChange"
        >
          <el-table-column label="时间" width="160" sortable="custom" prop="created_at">
            <template #default="{ row }">
              <div class="log-time">
                {{ formatDateTime(row.created_at) }}
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="级别" width="80">
            <template #default="{ row }">
              <el-tag
                :type="getLevelType(row.level)"
                size="small"
              >
                {{ getLevelLabel(row.level) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="用户" width="120">
            <template #default="{ row }">
              <div v-if="row.user" class="log-user">
                <el-avatar :size="24" :src="row.user.avatar">
                  {{ row.user.username.charAt(0).toUpperCase() }}
                </el-avatar>
                <span>{{ row.user.username }}</span>
              </div>
              <span v-else class="text-gray">系统</span>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-tag
                :type="getActionType(row.action)"
                size="small"
                effect="plain"
              >
                {{ getActionLabel(row.action) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="描述" min-width="200">
            <template #default="{ row }">
              <div class="log-message">
                {{ row.message }}
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="IP地址" width="120">
            <template #default="{ row }">
              <code class="log-ip">{{ row.ip || '-' }}</code>
            </template>
          </el-table-column>
          
          <el-table-column label="用户代理" width="150" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="log-user-agent">
                {{ getUserAgentInfo(row.user_agent) }}
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="{ row }">
              <el-button
                @click="showLogDetail(row)"
                size="small"
                text
              >
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.per_page"
            :total="pagination.total"
            :page-sizes="[20, 50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadLogs"
            @current-change="loadLogs"
          />
        </div>
      </el-card>
    </div>
    
    <!-- 日志详情对话框 -->
    <el-dialog
      v-model="showDetail"
      title="日志详情"
      width="800px"
      top="5vh"
    >
      <div v-if="selectedLog" class="log-detail">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>时间</label>
              <span>{{ formatDateTime(selectedLog.created_at) }}</span>
            </div>
            <div class="detail-item">
              <label>级别</label>
              <el-tag :type="getLevelType(selectedLog.level)">
                {{ getLevelLabel(selectedLog.level) }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>用户</label>
              <span>{{ selectedLog.user?.username || '系统' }}</span>
            </div>
            <div class="detail-item">
              <label>操作</label>
              <el-tag :type="getActionType(selectedLog.action)" effect="plain">
                {{ getActionLabel(selectedLog.action) }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>IP地址</label>
              <code>{{ selectedLog.ip || '-' }}</code>
            </div>
            <div class="detail-item">
              <label>请求ID</label>
              <code>{{ selectedLog.request_id || '-' }}</code>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h4>描述信息</h4>
          <div class="log-message-detail">
            {{ selectedLog.message }}
          </div>
        </div>
        
        <div v-if="selectedLog.context" class="detail-section">
          <h4>上下文数据</h4>
          <pre class="log-context">{{ JSON.stringify(selectedLog.context, null, 2) }}</pre>
        </div>
        
        <div class="detail-section">
          <h4>请求信息</h4>
          <div class="detail-grid">
            <div class="detail-item full-width">
              <label>用户代理</label>
              <div class="user-agent-detail">
                {{ selectedLog.user_agent || '-' }}
              </div>
            </div>
            <div class="detail-item full-width">
              <label>请求URL</label>
              <code>{{ selectedLog.url || '-' }}</code>
            </div>
            <div class="detail-item">
              <label>请求方法</label>
              <el-tag size="small">{{ selectedLog.method || '-' }}</el-tag>
            </div>
            <div class="detail-item">
              <label>响应状态</label>
              <el-tag
                :type="selectedLog.status_code >= 400 ? 'danger' : 'success'"
                size="small"
              >
                {{ selectedLog.status_code || '-' }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
    
    <!-- 清空日志确认对话框 -->
    <el-dialog
      v-model="showClearDialog"
      title="清空日志"
      width="400px"
    >
      <div class="clear-dialog-content">
        <Icon name="alert-triangle" size="48" color="#f59e0b" />
        <h3>确认清空日志？</h3>
        <p>此操作将永久删除所有日志记录，无法恢复。</p>
        
        <div class="clear-options">
          <el-radio-group v-model="clearOption">
            <el-radio label="all">清空所有日志</el-radio>
            <el-radio label="old">清空30天前的日志</el-radio>
            <el-radio label="level">清空指定级别的日志</el-radio>
          </el-radio-group>
          
          <el-select
            v-if="clearOption === 'level'"
            v-model="clearLevel"
            placeholder="选择日志级别"
            style="margin-top: 12px; width: 100%;"
          >
            <el-option label="信息" value="info" />
            <el-option label="警告" value="warning" />
            <el-option label="错误" value="error" />
            <el-option label="调试" value="debug" />
          </el-select>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showClearDialog = false">取消</el-button>
        <el-button
          @click="clearLogs"
          type="danger"
          :loading="clearing"
        >
          确认清空
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { debounce } from 'lodash-es'

interface LogItem {
  id: string
  level: string
  action: string
  message: string
  context?: any
  user_id?: string
  user?: {
    id: string
    username: string
    avatar: string
  }
  ip: string
  user_agent: string
  url: string
  method: string
  status_code: number
  request_id: string
  created_at: string
}

interface User {
  id: string
  username: string
  avatar: string
}

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const clearing = ref(false)
const userSearchLoading = ref(false)

const logs = ref<LogItem[]>([])
const userOptions = ref<User[]>([])
const selectedLog = ref<LogItem | null>(null)

const showDetail = ref(false)
const showClearDialog = ref(false)
const clearOption = ref('old')
const clearLevel = ref('')

// 统计数据
const stats = reactive({
  total_logs: 0,
  active_users: 0,
  today_logs: 0,
  error_logs: 0
})

// 筛选器
const filters = reactive({
  level: '',
  action: '',
  user_id: '',
  keyword: '',
  ip: '',
  dateRange: null as [string, string] | null
})

// 分页
const pagination = reactive({
  page: 1,
  per_page: 20,
  total: 0
})

// 排序
const sort = reactive({
  prop: 'created_at',
  order: 'descending'
})

// 方法
const loadLogs = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      per_page: pagination.per_page,
      sort: sort.prop,
      order: sort.order === 'descending' ? 'desc' : 'asc',
      ...filters,
      start_date: filters.dateRange?.[0],
      end_date: filters.dateRange?.[1]
    }
    
    // 清理空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })
    
    const response = await $fetch('/api/system/logs', { params })
    
    if (response.code === 200) {
      logs.value = response.data.data
      pagination.total = response.data.pagination.total
    }
    
  } catch (error) {
    console.error('加载日志失败:', error)
    ElMessage.error('加载日志失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    const response = await $fetch('/api/system/logs/stats')
    
    if (response.code === 200) {
      Object.assign(stats, response.data)
    }
    
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const searchUsers = debounce(async (query: string) => {
  if (!query) {
    userOptions.value = []
    return
  }
  
  try {
    userSearchLoading.value = true
    
    const response = await $fetch('/api/users/search', {
      params: { keyword: query, limit: 20 }
    })
    
    if (response.code === 200) {
      userOptions.value = response.data
    }
    
  } catch (error) {
    console.error('搜索用户失败:', error)
  } finally {
    userSearchLoading.value = false
  }
}, 300)

const resetFilters = () => {
  Object.assign(filters, {
    level: '',
    action: '',
    user_id: '',
    keyword: '',
    ip: '',
    dateRange: null
  })
  pagination.page = 1
  loadLogs()
}

const onSortChange = ({ prop, order }) => {
  sort.prop = prop
  sort.order = order
  loadLogs()
}

const showLogDetail = (log: LogItem) => {
  selectedLog.value = log
  showDetail.value = true
}

const exportLogs = async () => {
  try {
    exporting.value = true
    
    const params = {
      ...filters,
      start_date: filters.dateRange?.[0],
      end_date: filters.dateRange?.[1],
      format: 'excel'
    }
    
    const response = await $fetch('/api/system/logs/export', {
      method: 'POST',
      body: params
    })
    
    if (response.code === 200) {
      // 下载文件
      const link = document.createElement('a')
      link.href = response.data.download_url
      link.download = response.data.filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      ElMessage.success('导出成功')
    }
    
  } catch (error) {
    console.error('导出日志失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

const clearLogs = async () => {
  try {
    clearing.value = true
    
    const params: any = {
      type: clearOption.value
    }
    
    if (clearOption.value === 'level') {
      params.level = clearLevel.value
    }
    
    const response = await $fetch('/api/system/logs/clear', {
      method: 'DELETE',
      body: params
    })
    
    if (response.code === 200) {
      ElMessage.success(`已清空 ${response.data.count} 条日志`)
      showClearDialog.value = false
      loadLogs()
      loadStats()
    }
    
  } catch (error) {
    console.error('清空日志失败:', error)
    ElMessage.error('清空失败')
  } finally {
    clearing.value = false
  }
}

// 辅助方法
const formatDateTime = (dateString: string): string => {
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const getLevelType = (level: string): string => {
  const typeMap: Record<string, string> = {
    info: '',
    warning: 'warning',
    error: 'danger',
    debug: 'info'
  }
  return typeMap[level] || ''
}

const getLevelLabel = (level: string): string => {
  const labelMap: Record<string, string> = {
    info: '信息',
    warning: '警告',
    error: '错误',
    debug: '调试'
  }
  return labelMap[level] || level
}

const getActionType = (action: string): string => {
  const typeMap: Record<string, string> = {
    login: 'success',
    logout: 'info',
    create: 'success',
    update: 'warning',
    delete: 'danger',
    view: 'info'
  }
  return typeMap[action] || ''
}

const getActionLabel = (action: string): string => {
  const labelMap: Record<string, string> = {
    login: '登录',
    logout: '登出',
    create: '创建',
    update: '更新',
    delete: '删除',
    view: '查看'
  }
  return labelMap[action] || action
}

const getUserAgentInfo = (userAgent: string): string => {
  if (!userAgent) return '-'
  
  // 简单的用户代理解析
  if (userAgent.includes('Chrome')) return 'Chrome'
  if (userAgent.includes('Firefox')) return 'Firefox'
  if (userAgent.includes('Safari')) return 'Safari'
  if (userAgent.includes('Edge')) return 'Edge'
  
  return '其他'
}

// 生命周期
onMounted(() => {
  loadLogs()
  loadStats()
})
</script>

<style lang="scss" scoped>
.logs-page {
  padding: 24px;
}

.page-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 24px;
  
  h1 {
    margin: 0 0 8px;
    font-size: 24px;
    font-weight: 600;
    color: #1e293b;
  }
  
  p {
    margin: 0;
    color: #64748b;
  }
}

.header-right {
  display: flex;
  gap: 12px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: rgba(102, 126, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
}

.filters-section {
  margin-bottom: 24px;
}

.filters-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: flex;
  align-items: end;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 160px;
  
  label {
    font-size: 14px;
    color: #374151;
    font-weight: 500;
  }
}

.filter-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.logs-section {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.log-time {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-user {
  display: flex;
  align-items: center;
  gap: 8px;
  
  span {
    font-size: 14px;
  }
}

.log-message {
  line-height: 1.4;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.log-ip {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  background: #f1f5f9;
  padding: 2px 6px;
  border-radius: 4px;
}

.log-user-agent {
  font-size: 12px;
  color: #64748b;
}

.pagination-wrapper {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.log-detail {
  .detail-section {
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 12px;
      font-size: 16px;
      font-weight: 600;
      color: #1e293b;
    }
  }
  
  .detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }
  
  .detail-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
    
    &.full-width {
      grid-column: 1 / -1;
    }
    
    label {
      font-size: 12px;
      color: #64748b;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    span, code {
      font-size: 14px;
      color: #1e293b;
    }
    
    code {
      font-family: 'Courier New', monospace;
      background: #f1f5f9;
      padding: 4px 8px;
      border-radius: 4px;
    }
  }
  
  .log-message-detail {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 12px;
    font-size: 14px;
    line-height: 1.5;
  }
  
  .log-context {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 12px;
    font-size: 12px;
    line-height: 1.4;
    overflow-x: auto;
    max-height: 300px;
  }
  
  .user-agent-detail {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    background: #f1f5f9;
    padding: 8px;
    border-radius: 4px;
    word-break: break-all;
  }
}

.clear-dialog-content {
  text-align: center;
  padding: 20px;
  
  h3 {
    margin: 16px 0 8px;
    color: #1e293b;
  }
  
  p {
    margin: 0 0 20px;
    color: #64748b;
  }
}

.clear-options {
  text-align: left;
  
  .el-radio-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
}

.text-gray {
  color: #9ca3af;
}

// 响应式设计
@media (max-width: 768px) {
  .logs-page {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .stats-cards {
    grid-template-columns: 1fr;
  }
  
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-item {
    min-width: auto;
  }
  
  .filter-actions {
    margin-left: 0;
    justify-content: stretch;
  }
}
</style>

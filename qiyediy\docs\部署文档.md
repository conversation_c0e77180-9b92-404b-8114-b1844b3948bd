# QiyeDIY企业建站系统 - 部署文档

> **三只鱼网络科技 | 韩总 | 2024-12-20**  
> **版本**: 1.0.0  
> **生产环境部署指南**

---

## 📋 目录

1. [系统要求](#系统要求)
2. [环境准备](#环境准备)
3. [后端部署](#后端部署)
4. [前端部署](#前端部署)
5. [管理后台部署](#管理后台部署)
6. [数据库配置](#数据库配置)
7. [域名和SSL配置](#域名和ssl配置)
8. [性能优化](#性能优化)
9. [监控和维护](#监控和维护)
10. [故障排除](#故障排除)

---

## 💻 系统要求

### 服务器配置

**最低配置**
- CPU: 2核心
- 内存: 4GB RAM
- 存储: 50GB SSD
- 带宽: 5Mbps

**推荐配置**
- CPU: 4核心以上
- 内存: 8GB RAM以上
- 存储: 100GB SSD以上
- 带宽: 10Mbps以上

### 软件环境

**必需软件**
- PHP 8.0+
- MySQL 8.0+ / MariaDB 10.5+
- Redis 6.0+
- Nginx 1.18+ / Apache 2.4+
- Node.js 18.0+
- Composer 2.0+

**可选软件**
- Elasticsearch (搜索功能)
- Memcached (额外缓存)
- Supervisor (进程管理)

---

## 🔧 环境准备

### 1. 服务器初始化

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础软件
sudo apt install -y curl wget git unzip software-properties-common

# 安装PHP 8.0
sudo add-apt-repository ppa:ondrej/php
sudo apt update
sudo apt install -y php8.0 php8.0-fpm php8.0-mysql php8.0-redis \
    php8.0-gd php8.0-curl php8.0-mbstring php8.0-xml php8.0-zip \
    php8.0-bcmath php8.0-intl php8.0-opcache

# 安装Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# 安装MySQL
sudo apt install -y mysql-server

# 安装Redis
sudo apt install -y redis-server

# 安装Nginx
sudo apt install -y nginx
```

### 2. 创建项目目录

```bash
# 创建项目根目录
sudo mkdir -p /var/www/qiyediy
sudo chown -R www-data:www-data /var/www/qiyediy
sudo chmod -R 755 /var/www/qiyediy

# 创建日志目录
sudo mkdir -p /var/log/qiyediy
sudo chown -R www-data:www-data /var/log/qiyediy
```

---

## 🚀 后端部署

### 1. 代码部署

```bash
# 进入项目目录
cd /var/www/qiyediy

# 克隆代码（或上传代码包）
git clone https://github.com/your-repo/qiyediy.git .

# 进入后端目录
cd backend

# 安装依赖
composer install --no-dev --optimize-autoloader

# 设置权限
sudo chown -R www-data:www-data /var/www/qiyediy
sudo chmod -R 755 /var/www/qiyediy
sudo chmod -R 777 /var/www/qiyediy/backend/runtime
sudo chmod -R 777 /var/www/qiyediy/backend/public/uploads
```

### 2. 环境配置

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑环境配置
nano .env
```

**.env 配置示例**
```ini
# 应用配置
APP_DEBUG = false
APP_TRACE = false

# 数据库配置
DATABASE_TYPE = mysql
DATABASE_HOSTNAME = localhost
DATABASE_DATABASE = qiyediy
DATABASE_USERNAME = qiyediy_user
DATABASE_PASSWORD = your_password
DATABASE_HOSTPORT = 3306
DATABASE_CHARSET = utf8mb4

# Redis配置
REDIS_HOSTNAME = localhost
REDIS_PORT = 6379
REDIS_PASSWORD = 
REDIS_SELECT = 0

# 缓存配置
CACHE_DRIVER = redis
SESSION_DRIVER = redis

# 文件上传配置
UPLOAD_PATH = /uploads/
MAX_FILE_SIZE = 10485760

# 邮件配置
MAIL_TYPE = smtp
MAIL_SMTP_HOST = smtp.example.com
MAIL_SMTP_PORT = 587
MAIL_SMTP_USER = <EMAIL>
MAIL_SMTP_PASS = your_password
MAIL_FROM_EMAIL = <EMAIL>
MAIL_FROM_NAME = QiyeDIY

# JWT配置
JWT_SECRET = your_jwt_secret_key
JWT_TTL = 7200

# 其他配置
FRONTEND_URL = https://www.example.com
ADMIN_URL = https://admin.example.com
```

### 3. Nginx配置

```nginx
# /etc/nginx/sites-available/qiyediy-api
server {
    listen 80;
    server_name api.example.com;
    root /var/www/qiyediy/backend/public;
    index index.php;

    # 日志配置
    access_log /var/log/nginx/qiyediy-api.access.log;
    error_log /var/log/nginx/qiyediy-api.error.log;

    # 安全配置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # 文件上传大小限制
    client_max_body_size 100M;

    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # PHP处理
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;
    }

    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
    }

    location ~ /(config|runtime|vendor) {
        deny all;
    }
}
```

### 4. 启用站点

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/qiyediy-api /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

---

## 🌐 前端部署

### 1. 构建前端

```bash
# 进入前端目录
cd /var/www/qiyediy/frontend

# 安装依赖
npm install

# 构建生产版本
npm run build

# 生成静态文件
npm run generate
```

### 2. Nginx配置

```nginx
# /etc/nginx/sites-available/qiyediy-frontend
server {
    listen 80;
    server_name www.example.com example.com;
    root /var/www/qiyediy/frontend/dist;
    index index.html;

    # 日志配置
    access_log /var/log/nginx/qiyediy-frontend.access.log;
    error_log /var/log/nginx/qiyediy-frontend.error.log;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # SPA路由处理
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://api.example.com;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

---

## 🎛️ 管理后台部署

### 1. 构建管理后台

```bash
# 进入管理后台目录
cd /var/www/qiyediy/admin

# 安装依赖
npm install

# 构建生产版本
npm run build
```

### 2. Nginx配置

```nginx
# /etc/nginx/sites-available/qiyediy-admin
server {
    listen 80;
    server_name admin.example.com;
    root /var/www/qiyediy/admin/dist;
    index index.html;

    # 日志配置
    access_log /var/log/nginx/qiyediy-admin.access.log;
    error_log /var/log/nginx/qiyediy-admin.error.log;

    # 安全配置
    add_header X-Frame-Options "DENY" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    # IP白名单（可选）
    # allow ***********/24;
    # deny all;

    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # SPA路由处理
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://api.example.com;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

---

## 🗄️ 数据库配置

### 1. 创建数据库

```sql
-- 创建数据库
CREATE DATABASE qiyediy CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'qiyediy_user'@'localhost' IDENTIFIED BY 'your_password';

-- 授权
GRANT ALL PRIVILEGES ON qiyediy.* TO 'qiyediy_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. 导入数据

```bash
# 导入数据库结构
mysql -u qiyediy_user -p qiyediy < /var/www/qiyediy/database/qiyediy.sql

# 或使用ThinkPHP迁移
cd /var/www/qiyediy/backend
php think migrate:run
```

### 3. MySQL优化配置

```ini
# /etc/mysql/mysql.conf.d/mysqld.cnf
[mysqld]
# 基本配置
max_connections = 200
max_connect_errors = 10000
table_open_cache = 2048
max_allowed_packet = 100M

# InnoDB配置
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2

# 查询缓存
query_cache_type = 1
query_cache_size = 256M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

---

## 🔐 域名和SSL配置

### 1. 申请SSL证书

```bash
# 安装Certbot
sudo apt install -y certbot python3-certbot-nginx

# 申请证书
sudo certbot --nginx -d example.com -d www.example.com
sudo certbot --nginx -d admin.example.com
sudo certbot --nginx -d api.example.com
```

### 2. 自动续期

```bash
# 添加定时任务
sudo crontab -e

# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. HTTPS重定向

```nginx
# HTTP重定向到HTTPS
server {
    listen 80;
    server_name example.com www.example.com;
    return 301 https://$server_name$request_uri;
}
```

---

## ⚡ 性能优化

### 1. PHP优化

```ini
# /etc/php/8.0/fpm/php.ini
memory_limit = 512M
max_execution_time = 300
max_input_vars = 3000
upload_max_filesize = 100M
post_max_size = 100M

# OPcache配置
opcache.enable = 1
opcache.memory_consumption = 256
opcache.interned_strings_buffer = 16
opcache.max_accelerated_files = 10000
opcache.validate_timestamps = 0
opcache.save_comments = 0
```

### 2. Redis优化

```conf
# /etc/redis/redis.conf
maxmemory 1gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 3. Nginx优化

```nginx
# /etc/nginx/nginx.conf
worker_processes auto;
worker_connections 1024;

# 缓冲区配置
client_body_buffer_size 128k;
client_max_body_size 100m;
client_header_buffer_size 1k;
large_client_header_buffers 4 4k;

# 超时配置
client_body_timeout 12;
client_header_timeout 12;
keepalive_timeout 15;
send_timeout 10;

# Gzip压缩
gzip on;
gzip_comp_level 6;
gzip_min_length 1000;
gzip_proxied any;
gzip_vary on;
```

---

## 📊 监控和维护

### 1. 系统监控

```bash
# 安装监控工具
sudo apt install -y htop iotop nethogs

# 创建监控脚本
sudo nano /usr/local/bin/qiyediy-monitor.sh
```

**监控脚本示例**
```bash
#!/bin/bash
# QiyeDIY系统监控脚本

LOG_FILE="/var/log/qiyediy/monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# 检查服务状态
check_service() {
    if systemctl is-active --quiet $1; then
        echo "[$DATE] $1: 运行正常" >> $LOG_FILE
    else
        echo "[$DATE] $1: 服务异常" >> $LOG_FILE
        systemctl restart $1
    fi
}

# 检查磁盘空间
check_disk() {
    USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ $USAGE -gt 80 ]; then
        echo "[$DATE] 磁盘空间不足: ${USAGE}%" >> $LOG_FILE
    fi
}

# 检查内存使用
check_memory() {
    USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ $USAGE -gt 80 ]; then
        echo "[$DATE] 内存使用过高: ${USAGE}%" >> $LOG_FILE
    fi
}

# 执行检查
check_service nginx
check_service php8.0-fpm
check_service mysql
check_service redis-server
check_disk
check_memory
```

### 2. 日志轮转

```bash
# 创建日志轮转配置
sudo nano /etc/logrotate.d/qiyediy
```

```conf
/var/log/qiyediy/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload nginx
    endscript
}
```

### 3. 备份脚本

```bash
#!/bin/bash
# QiyeDIY备份脚本

BACKUP_DIR="/var/backups/qiyediy"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u qiyediy_user -p qiyediy > $BACKUP_DIR/database_$DATE.sql

# 备份文件
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /var/www/qiyediy/backend/public/uploads

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

---

## 🔧 故障排除

### 常见问题

**1. 500内部服务器错误**
```bash
# 检查PHP错误日志
sudo tail -f /var/log/php8.0-fpm.log

# 检查Nginx错误日志
sudo tail -f /var/log/nginx/error.log

# 检查应用日志
sudo tail -f /var/www/qiyediy/backend/runtime/log/error.log
```

**2. 数据库连接失败**
```bash
# 检查MySQL状态
sudo systemctl status mysql

# 检查连接
mysql -u qiyediy_user -p -h localhost

# 检查配置文件
cat /var/www/qiyediy/backend/.env
```

**3. Redis连接失败**
```bash
# 检查Redis状态
sudo systemctl status redis-server

# 测试连接
redis-cli ping

# 检查配置
cat /etc/redis/redis.conf
```

**4. 文件上传失败**
```bash
# 检查目录权限
ls -la /var/www/qiyediy/backend/public/uploads

# 设置权限
sudo chmod -R 777 /var/www/qiyediy/backend/public/uploads
sudo chown -R www-data:www-data /var/www/qiyediy/backend/public/uploads
```

### 性能问题排查

**1. 页面加载慢**
- 检查数据库慢查询日志
- 检查Redis缓存命中率
- 分析Nginx访问日志
- 使用浏览器开发者工具分析

**2. 内存使用过高**
- 检查PHP进程数量
- 分析MySQL内存使用
- 检查Redis内存使用
- 优化应用代码

**3. CPU使用率高**
- 检查正在运行的进程
- 分析数据库查询
- 检查定时任务
- 优化算法和查询

---

## 📞 技术支持

如果在部署过程中遇到问题，请联系技术支持：

- **邮箱**: <EMAIL>
- **电话**: 400-123-4567
- **文档**: https://docs.qiyediy.com

---

**部署完成后，请及时修改默认密码并做好安全防护！**

{"version": 3, "file": "descriptions-cell.js", "sources": ["../../../../../../packages/components/descriptions/src/descriptions-cell.ts"], "sourcesContent": ["import { defineComponent, h, inject, withDirectives } from 'vue'\nimport { isNil } from 'lodash-unified'\nimport { addUnit, getNormalizedProps } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { descriptionsKey } from './token'\nimport type { DirectiveArguments, PropType, VNode } from 'vue'\n\nimport type {\n  IDescriptionsInject,\n  IDescriptionsItemInject,\n} from './descriptions.type'\nimport type { DescriptionItemVNode } from './description-item'\n\nexport default defineComponent({\n  name: 'ElDescriptionsCell',\n  props: {\n    cell: {\n      type: Object as PropType<DescriptionItemVNode>,\n    },\n    tag: {\n      type: String,\n      default: 'td',\n    },\n    type: {\n      type: String,\n    },\n  },\n  setup() {\n    const descriptions = inject(descriptionsKey, {} as IDescriptionsInject)\n\n    return {\n      descriptions,\n    }\n  },\n  render() {\n    const item = getNormalizedProps(\n      this.cell as VNode\n    ) as IDescriptionsItemInject\n\n    const directives = (this.cell?.dirs || []).map((dire) => {\n      const { dir, arg, modifiers, value } = dire\n      return [dir, value, arg, modifiers]\n    }) as DirectiveArguments\n\n    const { border, direction } = this.descriptions\n    const isVertical = direction === 'vertical'\n    const renderLabel = () => this.cell?.children?.label?.() || item.label\n    const renderContent = () => this.cell?.children?.default?.()\n    const span = item.span\n    const rowspan = item.rowspan\n    const align = item.align ? `is-${item.align}` : ''\n    const labelAlign = item.labelAlign ? `is-${item.labelAlign}` : align\n    const className = item.className\n    const labelClassName = item.labelClassName\n    const width =\n      this.type === 'label'\n        ? item.labelWidth || this.descriptions.labelWidth || item.width\n        : item.width\n\n    const style = {\n      width: addUnit(width),\n      minWidth: addUnit(item.minWidth),\n    }\n    const ns = useNamespace('descriptions')\n\n    switch (this.type) {\n      case 'label':\n        return withDirectives(\n          h(\n            this.tag,\n            {\n              style,\n              class: [\n                ns.e('cell'),\n                ns.e('label'),\n                ns.is('bordered-label', border),\n                ns.is('vertical-label', isVertical),\n                labelAlign,\n                labelClassName,\n              ],\n              colSpan: isVertical ? span : 1,\n              rowspan: isVertical ? 1 : rowspan,\n            },\n            renderLabel()\n          ),\n          directives\n        )\n      case 'content':\n        return withDirectives(\n          h(\n            this.tag,\n            {\n              style,\n              class: [\n                ns.e('cell'),\n                ns.e('content'),\n                ns.is('bordered-content', border),\n                ns.is('vertical-content', isVertical),\n                align,\n                className,\n              ],\n              colSpan: isVertical ? span : span * 2 - 1,\n              rowspan: isVertical ? rowspan * 2 - 1 : rowspan,\n            },\n            renderContent()\n          ),\n          directives\n        )\n      default: {\n        const label = renderLabel()\n        const labelStyle: Record<string, any> = {}\n        const width = addUnit(item.labelWidth || this.descriptions.labelWidth)\n        if (width) {\n          labelStyle.width = width\n          labelStyle.display = 'inline-block'\n        }\n        return withDirectives(\n          h(\n            'td',\n            {\n              style,\n              class: [ns.e('cell'), align],\n              colSpan: span,\n              rowspan,\n            },\n            [\n              !isNil(label)\n                ? h(\n                    'span',\n                    {\n                      style: labelStyle,\n                      class: [ns.e('label'), labelClassName],\n                    },\n                    label\n                  )\n                : undefined,\n              h(\n                'span',\n                {\n                  class: [ns.e('content'), className],\n                },\n                renderContent()\n              ),\n            ]\n          ),\n          directives\n        )\n      }\n    }\n  },\n})\n"], "names": ["defineComponent", "inject", "<PERSON><PERSON><PERSON>", "getNormalizedProps", "style", "addUnit", "useNamespace", "withDirectives", "h", "isNil"], "mappings": ";;;;;;;;;;;AAKA,yBAAeA,mBAAe,CAAC;AAC/B,EAAE,IAAI,EAAE,oBAAoB;AAC5B,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,MAAM;AAClB,KAAK;AACL,IAAI,GAAG,EAAE;AACT,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,OAAO,EAAE,IAAI;AACnB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,MAAM;AAClB,KAAK;AACL,GAAG;AACH,EAAE,KAAK,GAAG;AACV,IAAI,MAAM,YAAY,GAAGC,UAAM,CAACC,qBAAe,EAAE,EAAE,CAAC,CAAC;AACrD,IAAI,OAAO;AACX,MAAM,YAAY;AAClB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,MAAM,GAAG;AACX,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,IAAI,GAAGC,wBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC/C,IAAI,MAAM,UAAU,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,EAAE,EAAE,GAAG,CAAC,CAAC,IAAI,KAAK;AAC3F,MAAM,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;AAClD,MAAM,OAAO,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;AAC1C,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC;AACpD,IAAI,MAAM,UAAU,GAAG,SAAS,KAAK,UAAU,CAAC;AAChD,IAAI,MAAM,WAAW,GAAG,MAAM;AAC9B,MAAM,IAAI,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;AACtB,MAAM,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC;AACxJ,KAAK,CAAC;AACN,IAAI,MAAM,aAAa,GAAG,MAAM;AAChC,MAAM,IAAI,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;AACtB,MAAM,OAAO,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC1I,KAAK,CAAC;AACN,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAC3B,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC;AACvD,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,KAAK,CAAC;AACzE,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACrC,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;AAC/C,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,KAAK,OAAO,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACrH,IAAI,MAAMC,OAAK,GAAG;AAClB,MAAM,KAAK,EAAEC,aAAO,CAAC,KAAK,CAAC;AAC3B,MAAM,QAAQ,EAAEA,aAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;AACtC,KAAK,CAAC;AACN,IAAI,MAAM,EAAE,GAAGC,kBAAY,CAAC,cAAc,CAAC,CAAC;AAC5C,IAAI,QAAQ,IAAI,CAAC,IAAI;AACrB,MAAM,KAAK,OAAO;AAClB,QAAQ,OAAOC,kBAAc,CAACC,KAAC,CAAC,IAAI,CAAC,GAAG,EAAE;AAC1C,iBAAUJ,OAAK;AACf,UAAU,KAAK,EAAE;AACjB,YAAY,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;AACxB,YAAY,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;AACzB,YAAY,EAAE,CAAC,EAAE,CAAC,gBAAgB,EAAE,MAAM,CAAC;AAC3C,YAAY,EAAE,CAAC,EAAE,CAAC,gBAAgB,EAAE,UAAU,CAAC;AAC/C,YAAY,UAAU;AACtB,YAAY,cAAc;AAC1B,WAAW;AACX,UAAU,OAAO,EAAE,UAAU,GAAG,IAAI,GAAG,CAAC;AACxC,UAAU,OAAO,EAAE,UAAU,GAAG,CAAC,GAAG,OAAO;AAC3C,SAAS,EAAE,WAAW,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;AACvC,MAAM,KAAK,SAAS;AACpB,QAAQ,OAAOG,kBAAc,CAACC,KAAC,CAAC,IAAI,CAAC,GAAG,EAAE;AAC1C,iBAAUJ,OAAK;AACf,UAAU,KAAK,EAAE;AACjB,YAAY,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;AACxB,YAAY,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;AAC3B,YAAY,EAAE,CAAC,EAAE,CAAC,kBAAkB,EAAE,MAAM,CAAC;AAC7C,YAAY,EAAE,CAAC,EAAE,CAAC,kBAAkB,EAAE,UAAU,CAAC;AACjD,YAAY,KAAK;AACjB,YAAY,SAAS;AACrB,WAAW;AACX,UAAU,OAAO,EAAE,UAAU,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC;AACnD,UAAU,OAAO,EAAE,UAAU,GAAG,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG,OAAO;AACzD,SAAS,EAAE,aAAa,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;AACzC,MAAM,SAAS;AACf,QAAQ,MAAM,KAAK,GAAG,WAAW,EAAE,CAAC;AACpC,QAAQ,MAAM,UAAU,GAAG,EAAE,CAAC;AAC9B,QAAQ,MAAM,MAAM,GAAGC,aAAO,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;AAChF,QAAQ,IAAI,MAAM,EAAE;AACpB,UAAU,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC;AACpC,UAAU,UAAU,CAAC,OAAO,GAAG,cAAc,CAAC;AAC9C,SAAS;AACT,QAAQ,OAAOE,kBAAc,CAACC,KAAC,CAAC,IAAI,EAAE;AACtC,iBAAUJ,OAAK;AACf,UAAU,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC;AACtC,UAAU,OAAO,EAAE,IAAI;AACvB,UAAU,OAAO;AACjB,SAAS,EAAE;AACX,UAAU,CAACK,mBAAK,CAAC,KAAK,CAAC,GAAGD,KAAC,CAAC,MAAM,EAAE;AACpC,YAAY,KAAK,EAAE,UAAU;AAC7B,YAAY,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,cAAc,CAAC;AAClD,WAAW,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC;AAC5B,UAAUA,KAAC,CAAC,MAAM,EAAE;AACpB,YAAY,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC;AAC/C,WAAW,EAAE,aAAa,EAAE,CAAC;AAC7B,SAAS,CAAC,EAAE,UAAU,CAAC,CAAC;AACxB,OAAO;AACP,KAAK;AACL,GAAG;AACH,CAAC,CAAC;;;;"}
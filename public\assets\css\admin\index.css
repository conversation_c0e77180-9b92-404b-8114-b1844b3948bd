/* 仪表盘样式 */
.dashboard-container {
    max-width: 100%;
    margin: 0;
    padding: 0;
}

/* 仪表盘头部 */
.dashboard-header {
    background: rgba(20, 20, 30, 0.6);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 15px;
    padding: 25px 30px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-title-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.8), rgba(255, 119, 198, 0.6));
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.3);
}

.header-text {
    flex: 1;
}

.dashboard-title {
    font-family: 'Orbitron', monospace;
    font-size: 28px;
    font-weight: 700;
    color: #fff;
    margin: 0 0 8px 0;
    text-shadow: 0 0 20px rgba(120, 119, 198, 0.6);
}

.dashboard-subtitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    margin: 0;
    font-weight: 400;
}

/* 统计卡片网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.stat-card {
    background: rgba(20, 20, 30, 0.6);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 15px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.05) 0%,
        rgba(255, 119, 198, 0.03) 50%,
        rgba(120, 219, 255, 0.05) 100%);
    pointer-events: none;
}

.stat-card:hover {
    transform: translateY(-5px);
    border-color: rgba(120, 119, 198, 0.5);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.stat-card-content {
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    position: relative;
    z-index: 2;
}

.stat-icon {
    width: 75px;
    height: 75px;
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    color: white;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.stat-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 18px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover .stat-icon::before {
    opacity: 1;
}

.stat-icon-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4), 0 0 20px rgba(102, 126, 234, 0.2);
}

.stat-icon-success {
    background: linear-gradient(135deg, #11998e, #38ef7d);
    box-shadow: 0 10px 30px rgba(17, 153, 142, 0.4), 0 0 20px rgba(17, 153, 142, 0.2);
}

.stat-icon-info {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.4), 0 0 20px rgba(59, 130, 246, 0.2);
}

.stat-icon-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    box-shadow: 0 10px 30px rgba(245, 158, 11, 0.4), 0 0 20px rgba(245, 158, 11, 0.2);
}

.stat-info {
    flex: 1;
}

.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
}

.stat-value {
    color: #fff;
    font-size: 36px;
    font-weight: 800;
    font-family: 'Orbitron', monospace;
    margin-bottom: 10px;
    text-shadow: 0 0 15px rgba(120, 119, 198, 0.8);
    line-height: 1;
}

.stat-detail {
    margin-top: 8px;
}

.stat-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 10px;
    font-size: 13px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.stat-badge-new {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.25), rgba(220, 38, 127, 0.2));
    color: #ff6b6b;
    border: 1px solid rgba(239, 68, 68, 0.4);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
}

.stat-badge-normal {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.25), rgba(16, 185, 129, 0.2));
    color: #34d399;
    border: 1px solid rgba(34, 197, 94, 0.4);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
}

.stat-card-footer {
    padding: 15px 25px;
    background: rgba(15, 15, 15, 0.6);
    border-top: 1px solid rgba(120, 119, 198, 0.2);
    position: relative;
    z-index: 2;
}

.stat-link {
    color: rgba(120, 119, 198, 0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.stat-link:hover {
    color: rgba(120, 119, 198, 1);
    text-decoration: none;
}

.stat-link i {
    transition: transform 0.3s ease;
}

.stat-link:hover i {
    transform: translateX(3px);
}

/* 联系表单部分 */
.contacts-section {
    background: rgba(20, 20, 30, 0.6);
    border: 1px solid rgba(120, 119, 198, 0.3);
    border-radius: 15px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    position: relative;
}

.contacts-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.05) 0%,
        rgba(255, 119, 198, 0.03) 50%,
        rgba(120, 219, 255, 0.05) 100%);
    pointer-events: none;
}

.section-header {
    padding: 25px 30px;
    border-bottom: 1px solid rgba(120, 119, 198, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
    position: relative;
    z-index: 2;
}

.section-title-area {
    display: flex;
    align-items: center;
    gap: 15px;
}

.section-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.8), rgba(255, 119, 198, 0.6));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    box-shadow: 0 6px 20px rgba(120, 119, 198, 0.3);
}

.section-title {
    font-family: 'Orbitron', monospace;
    font-size: 22px;
    font-weight: 600;
    color: #fff;
    margin: 0 0 5px 0;
    text-shadow: 0 0 15px rgba(120, 119, 198, 0.5);
}

.section-subtitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    margin: 0;
}

.btn-view-all {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.6), rgba(255, 119, 198, 0.4));
    color: white;
    text-decoration: none;
    padding: 10px 18px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid rgba(120, 119, 198, 0.3);
}

.btn-view-all:hover {
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.8), rgba(255, 119, 198, 0.6));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(120, 119, 198, 0.3);
    color: white;
    text-decoration: none;
}

.contacts-content {
    position: relative;
    z-index: 2;
}

.contacts-list {
    padding: 0;
}

.contact-item:first-child {
    margin-top: 20px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 25px;
    padding: 25px 30px;
    border-bottom: 1px solid rgba(120, 119, 198, 0.15);
    transition: all 0.3s ease;
    position: relative;
    margin: 0 15px;
    border-radius: 12px;
    margin-bottom: 8px;
}

.contact-item:last-child {
    border-bottom: none;
    margin-bottom: 15px;
}

.contact-item:hover {
    background: rgba(120, 119, 198, 0.08);
    transform: translateX(5px);
    border-color: rgba(120, 119, 198, 0.25);
}

.contact-avatar {
    width: 55px;
    height: 55px;
    background: linear-gradient(135deg, rgba(120, 119, 198, 0.8), rgba(255, 119, 198, 0.6));
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    flex-shrink: 0;
    box-shadow: 0 6px 20px rgba(120, 119, 198, 0.25);
}

.contact-info {
    flex: 1;
    min-width: 0;
}

.contact-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    gap: 15px;
}

.contact-name {
    font-size: 18px;
    font-weight: 700;
    color: #fff;
    margin: 0;
    text-shadow: 0 0 12px rgba(120, 119, 198, 0.4);
    font-family: 'Orbitron', monospace;
}

.contact-status-actions {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-shrink: 0;
}

.contact-status {
    flex-shrink: 0;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 14px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 600;
    border: 1px solid;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-new {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.4), rgba(220, 38, 127, 0.35));
    color: #ff8a8a;
    border-color: rgba(239, 68, 68, 0.6);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.status-read {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.25), rgba(37, 99, 235, 0.2));
    color: #60a5fa;
    border-color: rgba(59, 130, 246, 0.4);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.status-replied {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.4), rgba(16, 185, 129, 0.35));
    color: #4ade80;
    border-color: rgba(34, 197, 94, 0.6);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.status-closed {
    background: linear-gradient(135deg, rgba(107, 114, 128, 0.25), rgba(75, 85, 99, 0.2));
    color: #d1d5db;
    border-color: rgba(107, 114, 128, 0.4);
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.2);
}

.contact-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 15px;
    margin-top: 5px;
}

.contact-email,
.contact-subject,
.contact-time {
    display: flex;
    align-items: center;
    gap: 10px;
    color: rgba(255, 255, 255, 0.85);
    font-size: 14px;
    font-weight: 500;
    padding: 8px 12px;
    background: rgba(120, 119, 198, 0.08);
    border-radius: 8px;
    border: 1px solid rgba(120, 119, 198, 0.15);
}

.contact-email i,
.contact-subject i,
.contact-time i {
    color: rgba(120, 119, 198, 0.9);
    width: 16px;
    flex-shrink: 0;
    font-size: 14px;
}

.contact-actions {
    flex-shrink: 0;
}

/* 仪表盘操作按钮样式 - 确保与admin.css统一 */
.btn-action {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 14px;
    border: 1px solid;
    border-radius: 10px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    min-width: 75px;
    justify-content: center;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent);
    transition: left 0.5s ease;
}

.btn-action:hover::before {
    left: 100%;
}

.btn-action i {
    transition: transform 0.3s ease;
    filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.3));
}

.btn-action:hover i {
    transform: scale(1.1);
}

/* 主要操作按钮 - 统一紫色渐变 */
.btn-action {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8) 0%,
        rgba(139, 124, 246, 0.8) 50%,
        rgba(120, 219, 255, 0.8) 100%) !important;
    border-color: rgba(120, 119, 198, 0.6) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.btn-action:hover {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 1) 0%,
        rgba(139, 124, 246, 1) 50%,
        rgba(120, 219, 255, 1) 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(120, 119, 198, 0.4);
    color: #ffffff !important;
    text-decoration: none;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 30px;
}

.empty-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    background: rgba(120, 119, 198, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    color: rgba(120, 119, 198, 0.6);
}

.empty-title {
    font-size: 20px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
    margin: 0 0 10px 0;
}

.empty-subtitle {
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        align-items: flex-start;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .contact-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .contact-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .contact-status-actions {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .contact-details {
        grid-template-columns: 1fr;
        gap: 8px;
    }
}

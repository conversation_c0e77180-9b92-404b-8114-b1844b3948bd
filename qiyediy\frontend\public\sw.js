/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * QiyeDIY企业建站系统 - Service Worker
 */

const CACHE_NAME = 'qiyediy-v1.0.0'
const STATIC_CACHE = 'qiyediy-static-v1.0.0'
const DYNAMIC_CACHE = 'qiyediy-dynamic-v1.0.0'
const IMAGE_CACHE = 'qiyediy-images-v1.0.0'

// 需要缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/css/critical.css',
  '/js/app.js',
  '/images/logo.png',
  '/images/icon-192.png',
  '/images/icon-512.png',
  '/fonts/main.woff2',
  '/manifest.json'
]

// 需要缓存的API路径
const API_CACHE_PATTERNS = [
  /^\/api\/pages\/.*$/,
  /^\/api\/templates\/.*$/,
  /^\/api\/settings\/.*$/
]

// 图片资源模式
const IMAGE_PATTERNS = [
  /\.(?:png|jpg|jpeg|svg|gif|webp)$/,
  /^\/images\/.*$/,
  /^\/uploads\/.*$/
]

// 安装事件
self.addEventListener('install', (event) => {
  console.log('Service Worker 安装中...')
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('缓存静态资源')
        return cache.addAll(STATIC_ASSETS)
      })
      .then(() => {
        return self.skipWaiting()
      })
  )
})

// 激活事件
self.addEventListener('activate', (event) => {
  console.log('Service Worker 激活中...')
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            // 删除旧版本缓存
            if (cacheName !== STATIC_CACHE && 
                cacheName !== DYNAMIC_CACHE && 
                cacheName !== IMAGE_CACHE) {
              console.log('删除旧缓存:', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      })
      .then(() => {
        return self.clients.claim()
      })
  )
})

// 拦截请求
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)
  
  // 只处理同源请求
  if (url.origin !== location.origin) {
    return
  }
  
  // 处理不同类型的请求
  if (request.method === 'GET') {
    if (isStaticAsset(request.url)) {
      // 静态资源：缓存优先
      event.respondWith(cacheFirst(request, STATIC_CACHE))
    } else if (isImageRequest(request.url)) {
      // 图片资源：缓存优先，带过期时间
      event.respondWith(cacheFirstWithExpiry(request, IMAGE_CACHE, 7 * 24 * 60 * 60 * 1000)) // 7天
    } else if (isAPIRequest(request.url)) {
      // API请求：网络优先，带缓存回退
      event.respondWith(networkFirstWithCache(request, DYNAMIC_CACHE))
    } else {
      // 页面请求：网络优先
      event.respondWith(networkFirst(request, DYNAMIC_CACHE))
    }
  }
})

// 判断是否为静态资源
function isStaticAsset(url) {
  return STATIC_ASSETS.some(asset => url.endsWith(asset)) ||
         url.includes('/css/') ||
         url.includes('/js/') ||
         url.includes('/fonts/')
}

// 判断是否为图片请求
function isImageRequest(url) {
  return IMAGE_PATTERNS.some(pattern => pattern.test(url))
}

// 判断是否为API请求
function isAPIRequest(url) {
  return API_CACHE_PATTERNS.some(pattern => pattern.test(url))
}

// 缓存优先策略
async function cacheFirst(request, cacheName) {
  try {
    const cache = await caches.open(cacheName)
    const cachedResponse = await cache.match(request)
    
    if (cachedResponse) {
      return cachedResponse
    }
    
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone())
    }
    
    return networkResponse
  } catch (error) {
    console.error('缓存优先策略失败:', error)
    return new Response('离线状态，资源不可用', { status: 503 })
  }
}

// 带过期时间的缓存优先策略
async function cacheFirstWithExpiry(request, cacheName, maxAge) {
  try {
    const cache = await caches.open(cacheName)
    const cachedResponse = await cache.match(request)
    
    if (cachedResponse) {
      const cachedDate = new Date(cachedResponse.headers.get('date'))
      const now = new Date()
      
      if (now.getTime() - cachedDate.getTime() < maxAge) {
        return cachedResponse
      } else {
        // 缓存过期，删除并重新获取
        await cache.delete(request)
      }
    }
    
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone())
    }
    
    return networkResponse
  } catch (error) {
    console.error('带过期时间的缓存策略失败:', error)
    
    // 如果网络失败，尝试返回过期的缓存
    const cache = await caches.open(cacheName)
    const staleResponse = await cache.match(request)
    
    if (staleResponse) {
      return staleResponse
    }
    
    return new Response('离线状态，资源不可用', { status: 503 })
  }
}

// 网络优先策略
async function networkFirst(request, cacheName) {
  try {
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName)
      cache.put(request, networkResponse.clone())
    }
    
    return networkResponse
  } catch (error) {
    console.error('网络请求失败，尝试缓存:', error)
    
    const cache = await caches.open(cacheName)
    const cachedResponse = await cache.match(request)
    
    if (cachedResponse) {
      return cachedResponse
    }
    
    // 返回离线页面
    if (request.destination === 'document') {
      return caches.match('/offline.html')
    }
    
    return new Response('离线状态，资源不可用', { status: 503 })
  }
}

// 网络优先带缓存回退策略
async function networkFirstWithCache(request, cacheName) {
  try {
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName)
      
      // 只缓存GET请求的成功响应
      if (request.method === 'GET') {
        cache.put(request, networkResponse.clone())
      }
    }
    
    return networkResponse
  } catch (error) {
    console.error('API请求失败，尝试缓存:', error)
    
    const cache = await caches.open(cacheName)
    const cachedResponse = await cache.match(request)
    
    if (cachedResponse) {
      // 添加标识表明这是缓存的响应
      const headers = new Headers(cachedResponse.headers)
      headers.set('X-Served-By', 'ServiceWorker-Cache')
      
      return new Response(cachedResponse.body, {
        status: cachedResponse.status,
        statusText: cachedResponse.statusText,
        headers: headers
      })
    }
    
    return new Response(JSON.stringify({
      error: '网络连接失败，请检查网络设置',
      offline: true
    }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}

// 后台同步
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync())
  }
})

// 执行后台同步
async function doBackgroundSync() {
  try {
    // 这里可以处理离线时的数据同步
    console.log('执行后台同步')
  } catch (error) {
    console.error('后台同步失败:', error)
  }
}

// 推送通知
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json()
    
    const options = {
      body: data.body,
      icon: '/images/icon-192.png',
      badge: '/images/badge.png',
      vibrate: [100, 50, 100],
      data: data.data,
      actions: data.actions
    }
    
    event.waitUntil(
      self.registration.showNotification(data.title, options)
    )
  }
})

// 通知点击
self.addEventListener('notificationclick', (event) => {
  event.notification.close()
  
  if (event.action === 'open') {
    event.waitUntil(
      clients.openWindow(event.notification.data.url)
    )
  }
})

// 消息处理
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting()
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME })
  }
})

// 错误处理
self.addEventListener('error', (event) => {
  console.error('Service Worker 错误:', event.error)
})

self.addEventListener('unhandledrejection', (event) => {
  console.error('Service Worker 未处理的Promise拒绝:', event.reason)
})

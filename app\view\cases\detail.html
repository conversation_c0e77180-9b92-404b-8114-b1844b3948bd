{assign name="pageTitle" value="$pageData.pageTitle" /}

{include file="common/header" /}
<link rel="stylesheet" href="/assets/css/case-detail.css">

<!-- 案例详情主容器 -->
<div class="case-detail-container">
    <!-- 背景装饰 -->
    <div class="case-detail-background">
        <div class="bg-gradient-primary"></div>
        <div class="bg-particles"></div>
    </div>

    <!-- 案例标题区域 -->
    <section class="case-header-section">
        <!-- 3D背景动画层 -->
        <div class="case-3d-background">
            <div class="floating-case-elements">
                <!-- 几何形状 -->
                <div class="geometric-shape shape-triangle-1"></div>
                <div class="geometric-shape shape-square-1"></div>

                <!-- 粒子效果 -->
                <div class="particle-system">
                    <div class="particle particle-1"></div>
                    <div class="particle particle-2"></div>
                    <div class="particle particle-3"></div>
                    <div class="particle particle-4"></div>
                    <div class="particle particle-5"></div>
                    <div class="particle particle-6"></div>
                    <div class="particle particle-7"></div>
                    <div class="particle particle-8"></div>
                    <div class="particle particle-9"></div>
                    <div class="particle particle-10"></div>
                    <div class="particle particle-11"></div>
                    <div class="particle particle-12"></div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="case-header-content">
                <div class="case-title-wrapper">
                    <h1 class="case-title">{$case.title}</h1>
                    <p class="case-subtitle">{$case.summary ?: $case.description|mb_substr=0,120,'utf-8'}</p>
                </div>
                <div class="case-meta-info">
                    <div class="meta-item">
                        <i class="fas fa-industry"></i>
                        <span>{$case.industry}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-calendar"></i>
                        <span>{$case.completion_date ? date('Y年m月', strtotime($case.completion_date)) : '进行中'}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-eye"></i>
                        <span>{$case.views ?: 0} 浏览</span>
                    </div>
                </div>

            </div>
        </div>
    </section>
        <!-- 导航区 -->
        <div class="case-nav-wrapper">
            <nav class="case-breadcrumb">
                <a href="/" class="breadcrumb-link">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
                <i class="fas fa-chevron-right separator"></i>
                <a href="/cases" class="breadcrumb-link">
                    <i class="fas fa-briefcase"></i>
                    <span>案例展示</span>
                </a>
                <i class="fas fa-chevron-right separator"></i>
                <span class="breadcrumb-current">{$case.title}</span>
            </nav>
    </div>
    <!-- 主要内容区域 -->
    <section class="case-main-section">
        <div class="container">
            <div class="case-content-grid">
                <!-- 左侧：项目图片 -->
                <div class="case-image-section">
                    <div class="image-container">
                        {if condition="$case.image"}
                        <div class="main-image">
                            <img src="{$case.image}" alt="{$case.title}" loading="lazy">
                        </div>
                        {else/}
                        <div class="placeholder-image">
                            <i class="fas fa-image"></i>
                            <span>暂无项目图片</span>
                        </div>
                        {/if}
                    </div>
                </div>

                <!-- 右侧：项目信息 -->
                <div class="case-info-section">
                    <div class="info-card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-info-circle"></i>
                                项目信息
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="info-grid">
                                <div class="info-item">
                                    <div class="info-icon">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="info-details">
                                        <span class="info-label">客户名称</span>
                                        <span class="info-value">{$case.client_name}</span>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="info-icon">
                                        <i class="fas fa-industry"></i>
                                    </div>
                                    <div class="info-details">
                                        <span class="info-label">所属行业</span>
                                        <span class="info-value">{$case.industry}</span>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="info-icon">
                                        <i class="fas fa-calendar"></i>
                                    </div>
                                    <div class="info-details">
                                        <span class="info-label">完成时间</span>
                                        <span class="info-value">
                                            {$case.completion_date ? date('Y年m月d日', strtotime($case.completion_date)) : '进行中'}
                                        </span>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="info-icon">
                                        <i class="fas fa-eye"></i>
                                    </div>
                                    <div class="info-details">
                                        <span class="info-label">浏览次数</span>
                                        <span class="info-value">{$case.views ?: 0} 次</span>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 项目详情描述 -->
    <section class="case-description-section">
        <div class="case-content-grid">
            <div class="description-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-file-alt"></i>
                        项目详情
                    </h3>
                </div>
                <div class="card-content">
                    <div class="description-content">
                        {$case.description|raw}
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 操作按钮区域 -->
    <section class="case-actions-section">
        <div class="container">
            <div class="actions-wrapper">
                {if condition="$case.project_url"}
                <a href="{$case.project_url}" target="_blank" class="action-btn primary-btn">
                    <i class="fas fa-external-link-alt"></i>
                    <span>查看项目</span>
                </a>
                {/if}
                <a href="/cases" class="action-btn secondary-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>返回案例列表</span>
                </a>
            </div>
        </div>
    </section>
</div>



{include file="common/footer" /}
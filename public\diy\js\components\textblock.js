// 文本块组件 - 最基础的内容展示组件
// 支持标题、副标题、正文、列表、引用等多种文本内容

// 文本块组件模板
const textblockComponent = {
    name: '文本块',
    html: `<div class="textblock-component">
        <h2 class="textblock-title">精彩内容标题</h2>
        <h3 class="textblock-subtitle">副标题说明文字</h3>
        <div class="textblock-content">
            <p>这里是文本内容，可以放置任何文字信息。支持多行文本，让您的内容更加丰富精彩。文本块组件是最基础的内容展示组件，适用于各种文字内容的展示需求。</p>
            <p>支持多段落内容展示，每个段落都可以有不同的样式设置。您可以根据需要调整字体大小、颜色、对齐方式等样式属性。</p>
            <ul class="textblock-list">
                <li>支持无序列表展示</li>
                <li>支持有序列表展示</li>
                <li>列表项可自定义样式</li>
            </ul>
            <blockquote class="textblock-quote">
                "这里可以放置重要的引用内容或者客户评价，让内容更加生动有说服力。"
            </blockquote>
            <div class="textblock-buttons">
                <a href="#" class="textblock-btn primary">了解更多</a>
                <a href="#" class="textblock-btn secondary">联系我们</a>
            </div>
        </div>
    </div>`,
    properties: {
        // 样式预设
        stylePreset: 'article',

        // 内容设置
        title: '文章标题',
        subtitle: '副标题说明',
        content: '主要文本内容，支持多段落展示。这里可以放置任何文字信息，让您的内容更加丰富精彩。文本块组件是最基础的内容展示组件，适用于各种文字内容的展示需求。\n\n支持多段落内容展示，每个段落都可以有不同的样式设置。您可以根据需要调整字体大小、颜色、对齐方式等样式属性。',

        // 显示控制
        showTitle: true,
        showSubtitle: false,
        showList: false,
        showQuote: false,
        showButtons: false,

        // 列表内容
        listItems: [
            '支持无序列表展示',
            '支持有序列表展示',
            '列表项可自定义样式'
        ],
        listType: 'bullet', // bullet, number, none

        // 引用内容
        quoteText: '这里可以放置重要的引用内容或者客户评价，让内容更加生动有说服力。',
        quoteAuthor: '',

        // 按钮设置
        primaryBtnText: '了解更多',
        secondaryBtnText: '联系我们',
        primaryBtnLink: '#',
        secondaryBtnLink: '#',

        // 样式设置
        titleSize: 32,
        titleColor: '#2d3748',
        titleWeight: 'bold',
        titleAlign: 'left',

        subtitleSize: 20,
        subtitleColor: '#4a5568',
        subtitleWeight: 'normal',

        contentSize: 16,
        contentColor: '#4a5568',
        contentLineHeight: 1.6,
        textAlign: 'left',

        listSize: 16,
        listColor: '#4a5568',

        quoteSize: 18,
        quoteColor: '#667eea',
        quoteStyle: 'italic',
        quoteBorder: true,
        quoteBorderColor: '#667eea',

        // 布局设置
        maxWidth: 800,
        padding: 40,
        margin: 20,

        // 背景和装饰
        bgColor: '#f7fafc',
        borderRadius: 8,
        shadow: false,
        shadowIntensity: 0.1,

        // 按钮样式
        btnPrimaryColor: '#667eea',
        btnSecondaryColor: '#4a5568',
        btnBorderRadius: 6,

        // 响应式设置
        mobilePadding: 20,
        mobileMaxWidth: '100%',
        mobileTitleSize: 24,
        mobileContentSize: 14
    },

    // 生成动态CSS的方法
    generateCSS: function(componentId) {
        const props = this.properties;
        return generateTextblockCSS(componentId, props);
    }
};

// 初始化文本块组件
function initializeTextblockComponent(componentElement) {
    if (!componentElement.componentProperties) {
        componentElement.componentProperties = { ...textblockComponent.properties };
    }

    // 如果有默认样式预设，自动应用
    if (componentElement.componentProperties.stylePreset && componentElement.componentProperties.stylePreset !== '') {
        applyTextblockPreset(componentElement.id, componentElement.componentProperties.stylePreset);
    } else {
        // 确保所有样式都被正确应用
        updateTextblockDisplay(componentElement, componentElement.componentProperties);

        // 强制应用CSS样式
        applyTextblockCSS(componentElement.id, componentElement.componentProperties);

        // 确保DOM元素的内联样式也被正确设置
        const textblockComponent = componentElement.querySelector('.textblock-component');
        if (textblockComponent) {
            const props = componentElement.componentProperties;

            // 应用所有关键样式
            textblockComponent.style.background = props.bgColor;
            textblockComponent.style.maxWidth = props.maxWidth + 'px';
            textblockComponent.style.padding = props.padding + 'px';
            textblockComponent.style.borderRadius = props.borderRadius + 'px';
            textblockComponent.style.textAlign = props.textAlign;
            textblockComponent.style.margin = props.margin + 'px auto';

            // 应用阴影
            if (props.shadow) {
                textblockComponent.style.boxShadow = `0 10px 40px rgba(0,0,0,${props.shadowIntensity})`;
            } else {
                textblockComponent.style.boxShadow = 'none';
            }
        }
    }
}

// 注册文本块组件
if (typeof ComponentManager !== 'undefined') {
    ComponentManager.register('textblock', textblockComponent, generateTextblockProperties, updateTextblockDisplay);
}

// 生成文本块组件CSS
function generateTextblockCSS(componentId, props) {
    let css = `
        #${componentId} .textblock-component {
            max-width: ${props.maxWidth}px;
            margin: ${props.margin}px auto;
            padding: ${props.padding}px;
            text-align: ${props.textAlign};
            background: ${props.bgColor};
            border-radius: ${props.borderRadius}px;
            transition: all 0.3s ease;
        }

        #${componentId} .textblock-title {
            font-size: ${props.titleSize}px;
            color: ${props.titleColor};
            font-weight: ${props.titleWeight};
            text-align: ${props.titleAlign};
            margin-bottom: 15px;
            line-height: 1.2;
            display: ${props.showTitle ? 'block' : 'none'};
        }

        #${componentId} .textblock-subtitle {
            font-size: ${props.subtitleSize}px;
            color: ${props.subtitleColor};
            font-weight: ${props.subtitleWeight};
            text-align: ${props.titleAlign};
            margin-bottom: 20px;
            line-height: 1.3;
            display: ${props.showSubtitle ? 'block' : 'none'};
        }

        #${componentId} .textblock-content {
            text-align: ${props.textAlign};
        }

        #${componentId} .textblock-content p {
            font-size: ${props.contentSize}px;
            color: ${props.contentColor};
            line-height: ${props.contentLineHeight};
            margin-bottom: 15px;
        }

        #${componentId} .textblock-list {
            font-size: ${props.listSize}px;
            color: ${props.listColor};
            margin: 20px 0;
            padding-left: 20px;
            display: ${props.showList ? 'block' : 'none'};
        }

        #${componentId} .textblock-list li {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        #${componentId} .textblock-quote {
            font-size: ${props.quoteSize}px;
            color: ${props.quoteColor};
            font-style: ${props.quoteStyle};
            margin: 30px 0;
            padding: 20px;
            border-left: ${props.quoteBorder ? '4px solid ' + props.quoteBorderColor : 'none'};
            background: rgba(102, 126, 234, 0.05);
            border-radius: 4px;
            display: ${props.showQuote ? 'block' : 'none'};
        }

        #${componentId} .textblock-quote cite {
            display: block;
            margin-top: 10px;
            font-size: 14px;
            font-style: normal;
            opacity: 0.8;
        }

        #${componentId} .textblock-buttons {
            margin-top: 30px;
            display: ${props.showButtons ? 'flex' : 'none'};
            gap: 15px;
            justify-content: ${props.textAlign === 'center' ? 'center' :
                             props.textAlign === 'right' ? 'flex-end' : 'flex-start'};
            flex-wrap: wrap;
        }

        #${componentId} .textblock-btn {
            padding: 12px 24px;
            border: 2px solid;
            border-radius: ${props.btnBorderRadius}px;
            text-decoration: none;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-block;
            position: relative;
            overflow: hidden;
        }

        #${componentId} .textblock-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s ease;
        }

        #${componentId} .textblock-btn.primary {
            background: ${props.btnPrimaryColor};
            color: white;
            border-color: ${props.btnPrimaryColor};
        }

        #${componentId} .textblock-btn.primary:hover {
            background: transparent;
            color: ${props.btnPrimaryColor};
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            filter: brightness(1.05);
        }

        #${componentId} .textblock-btn.primary:hover::before {
            left: 100%;
        }

        #${componentId} .textblock-btn.secondary {
            background: transparent;
            color: ${props.btnSecondaryColor};
            border-color: ${props.btnSecondaryColor};
        }

        #${componentId} .textblock-btn.secondary:hover {
            background: ${props.btnSecondaryColor};
            color: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            filter: brightness(1.05);
        }

        #${componentId} .textblock-btn.secondary:hover::before {
            left: 100%;
        }
    `;

    // 阴影样式
    if (props.shadow) {
        css += `
            #${componentId} .textblock-component {
                box-shadow: 0 10px 40px rgba(0,0,0,${props.shadowIntensity});
            }
        `;
    }

    // 响应式样式
    css += `
        @media (max-width: 768px) {
            #${componentId} .textblock-component {
                padding: ${props.mobilePadding}px;
                max-width: ${props.mobileMaxWidth};
            }

            #${componentId} .textblock-title {
                font-size: ${props.mobileTitleSize}px;
            }

            #${componentId} .textblock-content p {
                font-size: ${props.mobileContentSize}px;
            }

            #${componentId} .textblock-buttons {
                justify-content: center;
            }

            #${componentId} .textblock-btn {
                padding: 10px 20px;
                font-size: 14px;
            }
        }
    `;

    return css;
}

// 更新文本块组件显示
function updateTextblockDisplay(componentBlock, props) {
    if (!componentBlock || !props) return;

    const componentId = componentBlock.id;

    // 更新标题
    const title = componentBlock.querySelector('.textblock-title');
    if (title) {
        title.textContent = props.title;
        title.style.display = props.showTitle ? 'block' : 'none';
        // 应用标题样式
        title.style.fontSize = props.titleSize + 'px';
        title.style.color = props.titleColor;
        title.style.fontWeight = props.titleWeight;
        title.style.textAlign = props.titleAlign;
        title.style.marginBottom = '15px';
        title.style.lineHeight = '1.2';
    }

    // 更新副标题
    const subtitle = componentBlock.querySelector('.textblock-subtitle');
    if (subtitle) {
        subtitle.textContent = props.subtitle;
        subtitle.style.display = props.showSubtitle ? 'block' : 'none';
        // 应用副标题样式
        subtitle.style.fontSize = props.subtitleSize + 'px';
        subtitle.style.color = props.subtitleColor;
        subtitle.style.fontWeight = props.subtitleWeight;
        subtitle.style.textAlign = props.titleAlign;
        subtitle.style.marginBottom = '20px';
        subtitle.style.lineHeight = '1.3';
    }

    // 更新内容段落
    const content = componentBlock.querySelector('.textblock-content');
    if (content) {
        const paragraphs = content.querySelectorAll('p');
        const contentLines = props.content.split('\n\n');

        // 清除现有段落
        paragraphs.forEach(p => p.remove());

        // 添加新段落
        contentLines.forEach(line => {
            if (line.trim()) {
                const p = document.createElement('p');
                p.textContent = line.trim();
                // 应用正文样式
                p.style.fontSize = props.contentSize + 'px';
                p.style.color = props.contentColor;
                p.style.lineHeight = props.contentLineHeight;
                p.style.marginBottom = '15px';
                content.insertBefore(p, content.querySelector('.textblock-list'));
            }
        });

        // 设置内容容器的文本对齐
        content.style.textAlign = props.textAlign;
    }

    // 更新列表
    const list = componentBlock.querySelector('.textblock-list');
    if (list) {
        list.style.display = props.showList ? 'block' : 'none';
        list.innerHTML = '';

        // 应用列表样式
        list.style.fontSize = props.listSize + 'px';
        list.style.color = props.listColor;
        list.style.margin = '20px 0';
        list.style.paddingLeft = '20px';

        props.listItems.forEach(item => {
            const li = document.createElement('li');
            li.textContent = item;
            li.style.marginBottom = '8px';
            li.style.lineHeight = '1.5';
            list.appendChild(li);
        });

        // 设置列表类型
        if (props.listType === 'number') {
            list.style.listStyleType = 'decimal';
        } else if (props.listType === 'none') {
            list.style.listStyleType = 'none';
        } else {
            list.style.listStyleType = 'disc';
        }
    }

    // 更新引用
    const quote = componentBlock.querySelector('.textblock-quote');
    if (quote) {
        quote.style.display = props.showQuote ? 'block' : 'none';

        // 应用引用样式
        quote.style.fontSize = props.quoteSize + 'px';
        quote.style.color = props.quoteColor;
        quote.style.fontStyle = props.quoteStyle;
        quote.style.margin = '30px 0';
        quote.style.padding = '20px';
        quote.style.borderLeft = props.quoteBorder ? '4px solid ' + props.quoteBorderColor : 'none';
        quote.style.background = 'rgba(102, 126, 234, 0.05)';
        quote.style.borderRadius = '4px';

        if (props.quoteAuthor && props.quoteAuthor.trim()) {
            quote.innerHTML = `"${props.quoteText}"<cite style="display: block; margin-top: 10px; font-size: 14px; font-style: normal; opacity: 0.8;">— ${props.quoteAuthor}</cite>`;
        } else {
            quote.textContent = props.quoteText;
        }
    }

    // 更新按钮
    const buttons = componentBlock.querySelector('.textblock-buttons');
    if (buttons) {
        buttons.style.display = props.showButtons ? 'flex' : 'none';

        const primaryBtn = buttons.querySelector('.textblock-btn.primary');
        const secondaryBtn = buttons.querySelector('.textblock-btn.secondary');

        if (primaryBtn) {
            primaryBtn.textContent = props.primaryBtnText;
            primaryBtn.href = props.primaryBtnLink;
        }

        if (secondaryBtn) {
            secondaryBtn.textContent = props.secondaryBtnText;
            secondaryBtn.href = props.secondaryBtnLink;
        }
    }

    // 应用动态CSS
    applyTextblockCSS(componentId, props);

    // 直接在DOM元素上设置样式（用于预览）
    const textblockComponent = componentBlock.querySelector('.textblock-component');
    if (textblockComponent) {
        // 设置背景（只使用纯色背景，移除渐变）
        textblockComponent.style.background = props.bgColor;

        // 设置其他样式
        textblockComponent.style.maxWidth = props.maxWidth + 'px';
        textblockComponent.style.padding = props.padding + 'px';
        textblockComponent.style.borderRadius = props.borderRadius + 'px';
        textblockComponent.style.textAlign = props.textAlign;

        // 设置阴影
        if (props.shadow) {
            textblockComponent.style.boxShadow = `0 10px 40px rgba(0,0,0,${props.shadowIntensity})`;
        } else {
            textblockComponent.style.boxShadow = 'none';
        }
    }
}

// 应用文本块组件CSS
function applyTextblockCSS(componentId, props) {
    // 移除旧的样式
    const oldStyle = document.getElementById(`textblock-style-${componentId}`);
    if (oldStyle) {
        oldStyle.remove();
    }

    // 生成新的CSS
    const css = generateTextblockCSS(componentId, props);

    // 创建新的样式标签
    const style = document.createElement('style');
    style.id = `textblock-style-${componentId}`;
    style.textContent = css;
    document.head.appendChild(style);
}

// 更新文本块属性
function updateTextblockProperty(componentId, property, value) {
    const component = document.getElementById(componentId);
    if (!component) return;

    // 获取或创建组件属性存储
    if (!component.componentProperties) {
        // 如果组件没有属性存储，使用默认属性初始化
        component.componentProperties = { ...textblockComponent.properties };
    }

    // 更新属性值
    component.componentProperties[property] = value;

    // 同时更新全局模板（如果存在）
    if (typeof componentTemplates !== 'undefined' && componentTemplates.textblock) {
        componentTemplates.textblock.properties[property] = value;
    }

    // 重新应用显示
    updateTextblockDisplay(component, component.componentProperties);

    // 如果是显示控制属性或背景类型，需要重新生成属性面板
    if (['showList', 'showQuote', 'showButtons', 'shadow'].includes(property)) {
        if (typeof updatePropertiesPanel === 'function') {
            updatePropertiesPanel(component);
        }
    }
}

// 样式预设功能
function applyTextblockPreset(componentId, presetType) {
    const component = document.getElementById(componentId);
    if (!component) return;

    // 获取或创建组件属性存储
    if (!component.componentProperties) {
        component.componentProperties = { ...textblockComponent.properties };
    }

    const presets = {
        article: {
            stylePreset: 'article',
            titleSize: 36,
            titleColor: '#1a202c',
            titleWeight: 'bold',
            contentSize: 18,
            contentColor: '#2d3748',
            textAlign: 'left',
            padding: 50,
            maxWidth: 1200,
            bgColor: '#ffffff',
            borderRadius: 12,
            shadow: true,
            showTitle: true,
            showSubtitle: true,
            showList: false,
            showQuote: false,
            showButtons: false
        },
        intro: {
            stylePreset: 'intro',
            titleSize: 32,
            titleColor: '#667eea',
            titleWeight: 'bold',
            contentSize: 16,
            contentColor: '#4a5568',
            textAlign: 'left',
            padding: 40,
            maxWidth: 1200,
            bgColor: '#ffffff',
            borderRadius: 8,
            shadow: true,
            showTitle: true,
            showSubtitle: true,
            showList: true,
            showQuote: false,
            showButtons: true
        },
        news: {
            stylePreset: 'news',
            titleSize: 28,
            titleColor: '#2d3748',
            titleWeight: 'bold',
            contentSize: 16,
            contentColor: '#4a5568',
            textAlign: 'left',
            padding: 30,
            maxWidth: 1200,
            bgColor: '#ffffff',
            borderRadius: 4,
            shadow: true,
            showTitle: true,
            showSubtitle: false,
            showList: false,
            showQuote: true,
            showButtons: false
        },
        service: {
            stylePreset: 'service',
            titleSize: 30,
            titleColor: '#2b6cb0',
            titleWeight: 'bold',
            contentSize: 17,
            contentColor: '#2d3748',
            textAlign: 'left',
            padding: 45,
            maxWidth: 1200,
            bgColor: '#ffffff',
            borderRadius: 10,
            shadow: true,
            showTitle: true,
            showSubtitle: true,
            showList: true,
            showQuote: false,
            showButtons: true
        }
    };

    const preset = presets[presetType];
    if (!preset) return;

    // 应用预设样式
    Object.keys(preset).forEach(key => {
        component.componentProperties[key] = preset[key];
        // 同时更新全局模板（如果存在）
        if (typeof componentTemplates !== 'undefined' && componentTemplates.textblock) {
            componentTemplates.textblock.properties[key] = preset[key];
        }
    });

    // 重新应用显示和属性面板
    updateTextblockDisplay(component, component.componentProperties);
    if (typeof updatePropertiesPanel === 'function') {
        updatePropertiesPanel(component);
    }
}

// 列表项管理功能
function addListItem(componentId) {
    const component = document.getElementById(componentId);
    if (!component) return;

    // 获取或创建组件属性存储
    if (!component.componentProperties) {
        component.componentProperties = { ...textblockComponent.properties };
    }

    component.componentProperties.listItems.push('新列表项');
    // 同时更新全局模板（如果存在）
    if (typeof componentTemplates !== 'undefined' && componentTemplates.textblock) {
        componentTemplates.textblock.properties.listItems.push('新列表项');
    }

    updateTextblockDisplay(component, component.componentProperties);
    if (typeof updatePropertiesPanel === 'function') {
        updatePropertiesPanel(component);
    }
}

function removeListItem(componentId, index) {
    const component = document.getElementById(componentId);
    if (!component) return;

    // 获取或创建组件属性存储
    if (!component.componentProperties) {
        component.componentProperties = { ...textblockComponent.properties };
    }

    component.componentProperties.listItems.splice(index, 1);
    // 同时更新全局模板（如果存在）
    if (typeof componentTemplates !== 'undefined' && componentTemplates.textblock) {
        componentTemplates.textblock.properties.listItems.splice(index, 1);
    }

    updateTextblockDisplay(component, component.componentProperties);
    if (typeof updatePropertiesPanel === 'function') {
        updatePropertiesPanel(component);
    }
}

function updateListItem(componentId, index, value) {
    const component = document.getElementById(componentId);
    if (!component) return;

    // 获取或创建组件属性存储
    if (!component.componentProperties) {
        component.componentProperties = { ...textblockComponent.properties };
    }

    component.componentProperties.listItems[index] = value;
    // 同时更新全局模板（如果存在）
    if (typeof componentTemplates !== 'undefined' && componentTemplates.textblock) {
        componentTemplates.textblock.properties.listItems[index] = value;
    }

    updateTextblockDisplay(component, component.componentProperties);
}

// 生成文本块属性面板
function generateTextblockProperties(component) {
    const componentId = component.id;

    // 获取组件属性，优先使用组件自身的属性存储
    let props;
    if (component.componentProperties) {
        props = component.componentProperties;
    } else if (typeof componentTemplates !== 'undefined' && componentTemplates.textblock?.properties) {
        props = componentTemplates.textblock.properties;
    } else {
        props = textblockComponent.properties;
    }

    return `
        <!-- 内容设置 -->
        <div class="property-section">
            <h4 class="section-title">内容设置</h4>

            <div class="property-group">
                <label class="property-label">标题文字</label>
                <input type="text" class="property-input" value="${props.title}"
                       onchange="updateTextblockProperty('${componentId}', 'title', this.value)"
                       placeholder="输入主标题">
            </div>

            <div class="property-group">
                <label class="property-label">副标题文字</label>
                <input type="text" class="property-input" value="${props.subtitle}"
                       onchange="updateTextblockProperty('${componentId}', 'subtitle', this.value)"
                       placeholder="输入副标题">
            </div>

            <div class="property-group">
                <label class="property-label">正文内容</label>
                <textarea class="property-input" rows="4"
                          onchange="updateTextblockProperty('${componentId}', 'content', this.value)"
                          placeholder="输入正文内容，支持多段落（用空行分隔）">${props.content}</textarea>
            </div>
        </div>

        <!-- 样式预设 -->
        <div class="property-section">
            <h4 class="section-title">样式预设</h4>

            <div class="property-group">
                <label class="property-label">快速样式</label>
                <div class="style-presets">
                    <div class="style-preset ${props.stylePreset === 'article' ? 'selected' : ''}"
                         style="background: #ffffff; color: #1a202c; border: 2px solid #667eea;"
                         onclick="applyTextblockPreset('${componentId}', 'article')"
                         title="文章样式">
                        <span style="font-size: 12px; font-weight: bold;">文章样式</span>
                    </div>
                    <div class="style-preset ${props.stylePreset === 'intro' ? 'selected' : ''}"
                         style="background: #f7fafc; color: #667eea; border: 2px solid #667eea;"
                         onclick="applyTextblockPreset('${componentId}', 'intro')"
                         title="介绍样式">
                        <span style="font-size: 12px; font-weight: bold;">介绍样式</span>
                    </div>
                    <div class="style-preset ${props.stylePreset === 'news' ? 'selected' : ''}"
                         style="background: #ffffff; color: #2d3748; border: 2px solid #4a5568;"
                         onclick="applyTextblockPreset('${componentId}', 'news')"
                         title="新闻样式">
                        <span style="font-size: 12px; font-weight: bold;">新闻样式</span>
                    </div>
                    <div class="style-preset ${props.stylePreset === 'service' ? 'selected' : ''}"
                         style="background: #edf2f7; color: #2b6cb0; border: 2px solid #2b6cb0;"
                         onclick="applyTextblockPreset('${componentId}', 'service')"
                         title="服务样式">
                        <span style="font-size: 12px; font-weight: bold;">服务样式</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 显示控制 -->
        <div class="property-section">
            <h4 class="section-title">显示控制</h4>

            <div class="property-group">
                <label class="property-label">内容元素</label>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" ${props.showTitle ? 'checked' : ''}
                               onchange="updateTextblockProperty('${componentId}', 'showTitle', this.checked)">
                        <span>显示标题</span>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" ${props.showSubtitle ? 'checked' : ''}
                               onchange="updateTextblockProperty('${componentId}', 'showSubtitle', this.checked)">
                        <span>显示副标题</span>
                    </div>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">扩展内容</label>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" ${props.showList ? 'checked' : ''}
                               onchange="updateTextblockProperty('${componentId}', 'showList', this.checked)">
                        <span>显示列表</span>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" ${props.showQuote ? 'checked' : ''}
                               onchange="updateTextblockProperty('${componentId}', 'showQuote', this.checked)">
                        <span>显示引用</span>
                    </div>
                </div>
                <div class="checkbox-group" style="margin-top: 8px;">
                    <div class="checkbox-item">
                        <input type="checkbox" ${props.showButtons ? 'checked' : ''}
                               onchange="updateTextblockProperty('${componentId}', 'showButtons', this.checked)">
                        <span>显示按钮</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 列表设置 -->
        ${props.showList ? `
        <div class="property-section">
            <h4 class="section-title">列表设置</h4>

            <div class="property-group">
                <label class="property-label">列表类型</label>
                <select class="property-input" onchange="updateTextblockProperty('${componentId}', 'listType', this.value)">
                    <option value="bullet" ${props.listType === 'bullet' ? 'selected' : ''}>无序列表</option>
                    <option value="number" ${props.listType === 'number' ? 'selected' : ''}>有序列表</option>
                    <option value="none" ${props.listType === 'none' ? 'selected' : ''}>无标记</option>
                </select>
            </div>

            <div class="property-group">
                <label class="property-label">列表项目</label>
                <div id="list-items-${componentId}">
                    ${props.listItems.map((item, index) => `
                        <div class="menu-item-row">
                            <div class="menu-item-inputs">
                                <input type="text" class="property-input" value="${item}" placeholder="列表项内容"
                                       onchange="updateListItem('${componentId}', ${index}, this.value)">
                                <button onclick="removeListItem('${componentId}', ${index})" class="delete-btn">删除</button>
                            </div>
                        </div>
                    `).join('')}
                </div>
                <button onclick="addListItem('${componentId}')" class="add-btn">添加列表项</button>
            </div>
        </div>
        ` : ''}

        <!-- 引用设置 -->
        ${props.showQuote ? `
        <div class="property-section">
            <h4 class="section-title">引用设置</h4>

            <div class="property-group">
                <label class="property-label">引用内容</label>
                <textarea class="property-input" rows="3"
                          onchange="updateTextblockProperty('${componentId}', 'quoteText', this.value)"
                          placeholder="输入引用内容">${props.quoteText}</textarea>
            </div>

            <div class="property-group">
                <label class="property-label">引用作者</label>
                <input type="text" class="property-input" value="${props.quoteAuthor}"
                       onchange="updateTextblockProperty('${componentId}', 'quoteAuthor', this.value)"
                       placeholder="引用作者（可选）">
            </div>

            <div class="property-group">
                <label class="property-label">引用样式</label>
                <div class="color-row">
                    <div class="color-item">
                        <label>字体大小</label>
                        <input type="number" class="property-input" value="${props.quoteSize}" min="12" max="32"
                               onchange="updateTextblockProperty('${componentId}', 'quoteSize', parseInt(this.value))">
                    </div>
                    <div class="color-item">
                        <label>字体颜色</label>
                        <input type="color" class="property-input" value="${props.quoteColor}"
                               onchange="updateTextblockProperty('${componentId}', 'quoteColor', this.value)">
                    </div>
                </div>
            </div>
        </div>
        ` : ''}

        <!-- 按钮设置 -->
        ${props.showButtons ? `
        <div class="property-section">
            <h4 class="section-title">按钮设置</h4>

            <div class="property-group">
                <label class="property-label">主要按钮</label>
                <div class="color-row">
                    <div class="color-item">
                        <label>按钮文字</label>
                        <input type="text" class="property-input" value="${props.primaryBtnText}"
                               onchange="updateTextblockProperty('${componentId}', 'primaryBtnText', this.value)"
                               placeholder="按钮文字">
                    </div>
                    <div class="color-item">
                        <label>链接地址</label>
                        <input type="text" class="property-input" value="${props.primaryBtnLink}"
                               onchange="updateTextblockProperty('${componentId}', 'primaryBtnLink', this.value)"
                               placeholder="链接地址">
                    </div>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">次要按钮</label>
                <div class="color-row">
                    <div class="color-item">
                        <label>按钮文字</label>
                        <input type="text" class="property-input" value="${props.secondaryBtnText}"
                               onchange="updateTextblockProperty('${componentId}', 'secondaryBtnText', this.value)"
                               placeholder="按钮文字">
                    </div>
                    <div class="color-item">
                        <label>链接地址</label>
                        <input type="text" class="property-input" value="${props.secondaryBtnLink}"
                               onchange="updateTextblockProperty('${componentId}', 'secondaryBtnLink', this.value)"
                               placeholder="链接地址">
                    </div>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">按钮颜色</label>
                <div class="color-row">
                    <div class="color-item">
                        <label>主按钮颜色</label>
                        <input type="color" class="property-input" value="${props.btnPrimaryColor}"
                               onchange="updateTextblockProperty('${componentId}', 'btnPrimaryColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>次按钮颜色</label>
                        <input type="color" class="property-input" value="${props.btnSecondaryColor}"
                               onchange="updateTextblockProperty('${componentId}', 'btnSecondaryColor', this.value)">
                    </div>
                </div>
            </div>
        </div>
        ` : ''}

        <!-- 样式设置 -->
        <div class="property-section">
            <h4 class="section-title">样式设置</h4>

            <div class="property-group">
                <label class="property-label">标题大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.titleSize}" min="12" max="72"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTextblockProperty('${componentId}', 'titleSize', parseInt(this.value))">
                    <span class="range-value">${props.titleSize}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">标题样式</label>
                <div class="color-row">
                    <div class="color-item">
                        <label>字体颜色</label>
                        <input type="color" class="property-input" value="${props.titleColor}"
                               onchange="updateTextblockProperty('${componentId}', 'titleColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>字体粗细</label>
                        <select class="property-input" onchange="updateTextblockProperty('${componentId}', 'titleWeight', this.value)">
                            <option value="normal" ${props.titleWeight === 'normal' ? 'selected' : ''}>正常</option>
                            <option value="bold" ${props.titleWeight === 'bold' ? 'selected' : ''}>粗体</option>
                            <option value="600" ${props.titleWeight === '600' ? 'selected' : ''}>半粗体</option>
                            <option value="300" ${props.titleWeight === '300' ? 'selected' : ''}>细体</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">副标题大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.subtitleSize}" min="12" max="48"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTextblockProperty('${componentId}', 'subtitleSize', parseInt(this.value))">
                    <span class="range-value">${props.subtitleSize}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">副标题颜色</label>
                <input type="color" class="property-input" value="${props.subtitleColor}"
                       onchange="updateTextblockProperty('${componentId}', 'subtitleColor', this.value)">
            </div>

            <div class="property-group">
                <label class="property-label">正文大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.contentSize}" min="12" max="24"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTextblockProperty('${componentId}', 'contentSize', parseInt(this.value))">
                    <span class="range-value">${props.contentSize}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">正文样式</label>
                <div class="color-row">
                    <div class="color-item">
                        <label>字体颜色</label>
                        <input type="color" class="property-input" value="${props.contentColor}"
                               onchange="updateTextblockProperty('${componentId}', 'contentColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>文本对齐</label>
                        <select class="property-input" onchange="updateTextblockProperty('${componentId}', 'textAlign', this.value)">
                            <option value="left" ${props.textAlign === 'left' ? 'selected' : ''}>左对齐</option>
                            <option value="center" ${props.textAlign === 'center' ? 'selected' : ''}>居中</option>
                            <option value="right" ${props.textAlign === 'right' ? 'selected' : ''}>右对齐</option>
                            <option value="justify" ${props.textAlign === 'justify' ? 'selected' : ''}>两端对齐</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">行高</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.contentLineHeight}" min="1" max="3" step="0.1"
                           oninput="this.nextElementSibling.textContent = this.value; updateTextblockProperty('${componentId}', 'contentLineHeight', parseFloat(this.value))">
                    <span class="range-value">${props.contentLineHeight}</span>
                </div>
            </div>
        </div>

        <!-- 布局设置 -->
        <div class="property-section">
            <h4 class="section-title">布局设置</h4>

            <div class="property-group">
                <label class="property-label">最大宽度</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.maxWidth}" min="300" max="1200"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTextblockProperty('${componentId}', 'maxWidth', parseInt(this.value))">
                    <span class="range-value">${props.maxWidth}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">内边距</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.padding}" min="0" max="100"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTextblockProperty('${componentId}', 'padding', parseInt(this.value))">
                    <span class="range-value">${props.padding}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">背景颜色</label>
                <input type="color" class="property-input" value="${props.bgColor}"
                       onchange="updateTextblockProperty('${componentId}', 'bgColor', this.value)">
            </div>

            <div class="property-group">
                <label class="property-label">圆角大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.borderRadius}" min="0" max="50"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateTextblockProperty('${componentId}', 'borderRadius', parseInt(this.value))">
                    <span class="range-value">${props.borderRadius}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">阴影效果</label>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" ${props.shadow ? 'checked' : ''}
                               onchange="updateTextblockProperty('${componentId}', 'shadow', this.checked)">
                        <span>启用阴影</span>
                    </div>
                </div>
                ${props.shadow ? `
                    <div class="range-control" style="margin-top: 8px;">
                        <input type="range" class="range-slider" value="${props.shadowIntensity}" min="0" max="0.3" step="0.05"
                               oninput="this.nextElementSibling.textContent = Math.round(this.value * 100) + '%'; updateTextblockProperty('${componentId}', 'shadowIntensity', parseFloat(this.value))">
                        <span class="range-value">${Math.round(props.shadowIntensity * 100)}%</span>
                    </div>
                ` : ''}
            </div>
        </div>
    `;
}

/**
 * 首页英雄区域幻灯片功能 - 修复版
 */

// 确保在所有其他脚本加载完成后再初始化
$(document).ready(function() {
    // 等待一段时间确保main.js中的Swiper初始化完成
    setTimeout(function() {
        initHeroSlider();
    }, 500);
});

// 也监听window.load事件作为备用
$(window).on('load', function() {
    // 如果heroSwiper还没有初始化，再次尝试
    if (!window.heroSwiper) {
        setTimeout(function() {
            initHeroSlider();
        }, 200);
    }
});

function initHeroSlider() {
    // 首先检查是否在首页，如果不在首页则不初始化
    const heroSlider = document.querySelector('.hero-slider');
    if (!heroSlider) {
        // 不在首页，静默返回，不显示错误
        return;
    }

    // 检查必要元素是否存在
    const swiperWrapper = document.querySelector('.swiper-wrapper');
    const swiperSlides = document.querySelectorAll('.swiper-slide');

    if (swiperSlides.length === 0) {
        // 静默返回，不显示错误信息
        return;
    }

    // 检查Swiper是否已定义
    if (typeof Swiper === 'undefined') {
        console.error('Swiper未定义，请检查swiper-bundle.min.js是否正确加载');
        return;
    }


    try {
        // 销毁已存在的heroSwiper实例
        if (window.heroSwiper && typeof window.heroSwiper.destroy === 'function') {
            window.heroSwiper.destroy(true, true);
        }

        // 创建新的Swiper实例
        window.heroSwiper = new Swiper('.hero-slider', {
            // 基础配置
            loop: true,
            speed: 1000,
            effect: 'slide',

            // 自动播放配置
            autoplay: {
                delay: 5000,
                disableOnInteraction: false,
            },

            // 分页器配置
            pagination: {
                el: '.hero-slider .swiper-pagination',
                clickable: true,
            },

            // 导航按钮配置
            navigation: {
                nextEl: '.hero-slider .swiper-button-next',
                prevEl: '.hero-slider .swiper-button-prev',
            },

            // 事件回调
            on: {
                init: function() {

                    // 打印所有幻灯片的文字内容
                    this.slides.forEach((slide, index) => {
                        const heroTitle = slide.querySelector('.hero-title');
                        const heroSubtitle = slide.querySelector('.hero-subtitle');
                        const heroDescription = slide.querySelector('.hero-description');

                        // 获取纯文本内容的函数
                        function getTextContent(element) {
                            if (!element) return '';
                            // 先尝试textContent，如果为空则使用innerHTML并去掉HTML标签
                            let text = element.textContent.trim();
                            if (!text && element.innerHTML) {
                                text = element.innerHTML.replace(/<[^>]*>/g, '').trim();
                            }
                            return text;
                        }

                    });

                    // 初始化时设置第一张背景
                    switchBackground(this.realIndex || 0);
                },
                slideChange: function() {
                    // 切换幻灯片时更新背景
                    switchBackground(this.realIndex);
                }
            }
        });

        // 强制启动自动播放
        setTimeout(function() {
            if (window.heroSwiper && window.heroSwiper.autoplay) {
                window.heroSwiper.autoplay.start();
            }
        }, 500);

    } catch (error) {
        console.log('Swiper初始化失败:', error);
    }
}

// 背景切换函数
function switchBackground(slideIndex) {
    var heroSection = document.querySelector('#heroSection');
    var heroBackground = document.querySelector('.hero-background');

    if (!heroSection || !heroBackground) {
        return;
    }

    // 移除所有可能存在的背景类，避免干扰
    heroSection.classList.remove('bg-slide-1', 'bg-slide-2', 'bg-slide-3', 'bg-slide-4');

    // 计算背景图片索引 (0,1,2,3 -> 1,2,3,4)
    var bgImageIndex = (slideIndex % 4) + 1;
    var defaultBgImage = '/assets/images/tu' + bgImageIndex + '.png';
    var imageUrl = 'url(' + defaultBgImage + ')';



    // 添加对应的背景类，让CSS样式生效
    heroSection.classList.add('bg-slide-' + bgImageIndex);

    // 同时直接设置背景图片，确保立即生效
    heroSection.style.setProperty('background-image', imageUrl, 'important');
    heroBackground.style.setProperty('background-image', imageUrl, 'important');

    // 确保背景图片属性正确设置
    heroBackground.style.setProperty('background-size', 'cover', 'important');
    heroBackground.style.setProperty('background-position', 'center center', 'important');
    heroBackground.style.setProperty('background-repeat', 'no-repeat', 'important');
}

<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * QiyeDIY企业建站系统 - DIY页面验证器
 */

declare(strict_types=1);

namespace app\validate;

use think\Validate;

/**
 * DIY页面验证器
 */
class DiyPageValidate extends Validate
{
    /**
     * 验证规则
     * @var array
     */
    protected $rule = [
        'title' => 'require|length:1,100',
        'slug' => 'require|length:1,100|alphaNum|unique:diy_pages',
        'description' => 'length:0,500',
        'keywords' => 'length:0,200',
        'template_id' => 'integer|gt:0',
        'category' => 'length:0,50',
        'config' => 'checkConfig',
        'custom_css' => 'length:0,50000',
        'custom_js' => 'length:0,50000',
        'status' => 'in:0,1,2',
        'is_published' => 'in:0,1',
        'sort_order' => 'integer|between:0,9999',
        'components' => 'array|checkComponents'
    ];

    /**
     * 错误信息
     * @var array
     */
    protected $message = [
        'title.require' => '页面标题不能为空',
        'title.length' => '页面标题长度必须在1-100个字符之间',
        'slug.require' => '页面别名不能为空',
        'slug.length' => '页面别名长度必须在1-100个字符之间',
        'slug.alphaNum' => '页面别名只能包含字母、数字和下划线',
        'slug.unique' => '页面别名已存在',
        'description.length' => '页面描述长度不能超过500个字符',
        'keywords.length' => '关键词长度不能超过200个字符',
        'template_id.integer' => '模板ID必须是整数',
        'template_id.gt' => '模板ID必须大于0',
        'category.length' => '分类名称长度不能超过50个字符',
        'custom_css.length' => '自定义CSS长度不能超过50000个字符',
        'custom_js.length' => '自定义JS长度不能超过50000个字符',
        'status.in' => '页面状态值不正确',
        'is_published.in' => '发布状态值不正确',
        'sort_order.integer' => '排序必须是整数',
        'sort_order.between' => '排序值必须在0-9999之间',
        'components.array' => '组件数据必须是数组'
    ];

    /**
     * 验证场景
     * @var array
     */
    protected $scene = [
        // 创建场景
        'create' => ['title', 'slug', 'description', 'keywords', 'template_id', 'category', 'config', 'custom_css', 'custom_js', 'status', 'is_published', 'sort_order', 'components'],
        
        // 更新场景
        'update' => ['title', 'slug', 'description', 'keywords', 'template_id', 'category', 'config', 'custom_css', 'custom_js', 'status', 'is_published', 'sort_order', 'components'],
        
        // 保存内容场景
        'save_content' => ['config', 'custom_css', 'custom_js', 'components'],
        
        // 复制场景
        'copy' => ['title', 'slug']
    ];

    /**
     * 创建场景验证规则
     * @return DiyPageValidate
     */
    public function sceneCreate(): DiyPageValidate
    {
        return $this->only(['title', 'slug', 'description', 'keywords', 'template_id', 'category', 'config', 'custom_css', 'custom_js', 'status', 'is_published', 'sort_order', 'components'])
                   ->append('slug', 'checkSlugFormat');
    }

    /**
     * 更新场景验证规则
     * @return DiyPageValidate
     */
    public function sceneUpdate(): DiyPageValidate
    {
        return $this->only(['title', 'slug', 'description', 'keywords', 'template_id', 'category', 'config', 'custom_css', 'custom_js', 'status', 'is_published', 'sort_order', 'components'])
                   ->remove('slug', 'unique')
                   ->append('slug', 'checkSlugUpdate');
    }

    /**
     * 保存内容场景验证规则
     * @return DiyPageValidate
     */
    public function sceneSaveContent(): DiyPageValidate
    {
        return $this->only(['config', 'custom_css', 'custom_js', 'components']);
    }

    /**
     * 复制场景验证规则
     * @return DiyPageValidate
     */
    public function sceneCopy(): DiyPageValidate
    {
        return $this->only(['title', 'slug'])
                   ->append('slug', 'checkSlugFormat');
    }

    /**
     * 自定义验证规则：检查页面别名格式
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkSlugFormat($value, $rule, array $data)
    {
        // 页面别名只能包含字母、数字、下划线、连字符
        if (!preg_match('/^[a-zA-Z0-9_-]+$/', $value)) {
            return '页面别名只能包含字母、数字、下划线和连字符';
        }
        
        // 页面别名不能以数字开头
        if (is_numeric($value[0])) {
            return '页面别名不能以数字开头';
        }
        
        // 页面别名不能全是数字
        if (is_numeric($value)) {
            return '页面别名不能全是数字';
        }
        
        // 检查保留关键字
        $reserved = ['admin', 'api', 'www', 'mail', 'ftp', 'localhost', 'index', 'home', 'about', 'contact', 'login', 'register', 'dashboard'];
        if (in_array(strtolower($value), $reserved)) {
            return '页面别名不能使用系统保留关键字';
        }
        
        return true;
    }

    /**
     * 自定义验证规则：检查页面别名更新时的唯一性
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkSlugUpdate($value, $rule, array $data)
    {
        if (empty($value)) {
            return '页面别名不能为空';
        }

        // 检查格式
        $formatCheck = $this->checkSlugFormat($value, $rule, $data);
        if ($formatCheck !== true) {
            return $formatCheck;
        }

        // 检查唯一性（排除当前页面）
        $pageId = $data['id'] ?? 0;
        $exists = \app\model\DiyPage::where('slug', $value)
                                  ->where('id', '<>', $pageId)
                                  ->exists();
        if ($exists) {
            return '页面别名已存在';
        }
        
        return true;
    }

    /**
     * 自定义验证规则：检查页面配置
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkConfig($value, $rule, array $data)
    {
        if (empty($value)) {
            return true;
        }

        // 如果是字符串，尝试解析JSON
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return '页面配置必须是有效的JSON格式';
            }
            $value = $decoded;
        }

        // 检查配置结构
        if (!is_array($value)) {
            return '页面配置必须是数组或JSON对象';
        }

        // 检查必要的配置项
        $requiredKeys = ['layout', 'theme'];
        foreach ($requiredKeys as $key) {
            if (!isset($value[$key])) {
                return "页面配置缺少必要的 {$key} 配置项";
            }
        }

        // 检查布局配置
        if (!in_array($value['layout'], ['default', 'full', 'sidebar', 'custom'])) {
            return '页面布局配置值不正确';
        }

        return true;
    }

    /**
     * 自定义验证规则：检查组件数据
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkComponents($value, $rule, array $data)
    {
        if (empty($value)) {
            return true;
        }

        if (!is_array($value)) {
            return '组件数据必须是数组';
        }

        // 检查每个组件
        foreach ($value as $index => $component) {
            if (!is_array($component)) {
                return "第" . ($index + 1) . "个组件数据格式错误";
            }

            // 检查必要字段
            if (empty($component['type'])) {
                return "第" . ($index + 1) . "个组件缺少类型字段";
            }

            // 检查组件类型
            $allowedTypes = [
                'text', 'image', 'video', 'button', 'form', 'list', 'grid', 
                'carousel', 'tabs', 'accordion', 'map', 'chart', 'custom'
            ];
            if (!in_array($component['type'], $allowedTypes)) {
                return "第" . ($index + 1) . "个组件类型不支持";
            }

            // 检查配置数据
            if (isset($component['config'])) {
                if (is_string($component['config'])) {
                    $decoded = json_decode($component['config'], true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        return "第" . ($index + 1) . "个组件配置必须是有效的JSON格式";
                    }
                } elseif (!is_array($component['config'])) {
                    return "第" . ($index + 1) . "个组件配置必须是数组或JSON对象";
                }
            }

            // 检查内容数据
            if (isset($component['content'])) {
                if (is_string($component['content'])) {
                    $decoded = json_decode($component['content'], true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        return "第" . ($index + 1) . "个组件内容必须是有效的JSON格式";
                    }
                } elseif (!is_array($component['content'])) {
                    return "第" . ($index + 1) . "个组件内容必须是数组或JSON对象";
                }
            }

            // 检查排序值
            if (isset($component['sort_order']) && (!is_numeric($component['sort_order']) || $component['sort_order'] < 0)) {
                return "第" . ($index + 1) . "个组件排序值必须是非负整数";
            }

            // 检查启用状态
            if (isset($component['is_enabled']) && !in_array($component['is_enabled'], [0, 1, true, false])) {
                return "第" . ($index + 1) . "个组件启用状态值不正确";
            }
        }

        return true;
    }

    /**
     * 自定义验证规则：检查模板ID
     * @param mixed $value 字段值
     * @param mixed $rule 验证规则
     * @param array $data 数据
     * @return bool|string
     */
    protected function checkTemplateId($value, $rule, array $data)
    {
        if (empty($value)) {
            return true;
        }

        // 检查模板是否存在
        $exists = \app\model\DiyTemplate::where('id', $value)
                                       ->where('status', 1)
                                       ->exists();
        if (!$exists) {
            return '选择的模板不存在或已禁用';
        }

        return true;
    }
}

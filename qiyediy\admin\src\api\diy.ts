/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * QiyeDIY企业建站系统 - DIY管理API接口
 */

import { http } from '@/utils/http'
import type { 
  ApiResponse,
  PaginatedResponse
} from '@/types/auth'

/**
 * DIY页面接口
 */
export interface DiyPage {
  id: number
  title: string
  slug: string
  description?: string
  keywords?: string
  template_id?: number
  category?: string
  config: Record<string, any>
  custom_css?: string
  custom_js?: string
  status: number
  status_text: string
  is_published: number
  published_text: string
  sort_order: number
  user_id: number
  user?: any
  components?: DiyComponent[]
  created_at: string
  updated_at: string
}

/**
 * DIY组件接口
 */
export interface DiyComponent {
  id: number
  page_id: number
  type: string
  name: string
  config: Record<string, any>
  content: Record<string, any>
  sort_order: number
  is_enabled: number
  created_at: string
  updated_at: string
}

/**
 * DIY模板接口
 */
export interface DiyTemplate {
  id: number
  name: string
  slug: string
  description?: string
  preview_image?: string
  config: Record<string, any>
  components: Record<string, any>
  category?: string
  tags?: string[]
  version: string
  author?: string
  status: number
  status_text: string
  is_default: number
  sort_order: number
  use_count: number
  created_at: string
  updated_at: string
}

/**
 * DIY页面管理相关API
 */
export const diyPageApi = {
  /**
   * 获取页面列表
   */
  getList: (params: {
    page?: number
    per_page?: number
    keyword?: string
    status?: number | string
    is_published?: number | string
    template_id?: number | string
    category?: string
    user_id?: number | string
    start_date?: string
    end_date?: string
    sort_field?: string
    sort_order?: string
  }): Promise<PaginatedResponse<DiyPage>> => {
    return http.get('/diy/page', { params })
  },

  /**
   * 获取页面详情
   */
  getById: (id: number): Promise<ApiResponse<DiyPage>> => {
    return http.get(`/diy/page/${id}`)
  },

  /**
   * 创建页面
   */
  create: (data: {
    title: string
    slug: string
    description?: string
    keywords?: string
    template_id?: number
    category?: string
    config?: Record<string, any>
    custom_css?: string
    custom_js?: string
    status?: number
    is_published?: number
    sort_order?: number
    components?: any[]
  }): Promise<ApiResponse<DiyPage>> => {
    return http.post('/diy/page', data)
  },

  /**
   * 更新页面
   */
  update: (id: number, data: {
    title?: string
    slug?: string
    description?: string
    keywords?: string
    template_id?: number
    category?: string
    config?: Record<string, any>
    custom_css?: string
    custom_js?: string
    status?: number
    is_published?: number
    sort_order?: number
    components?: any[]
  }): Promise<ApiResponse<DiyPage>> => {
    return http.put(`/diy/page/${id}`, data)
  },

  /**
   * 删除页面
   */
  delete: (id: number): Promise<ApiResponse<void>> => {
    return http.delete(`/diy/page/${id}`)
  },

  /**
   * 批量删除页面
   */
  batchDelete: (ids: number[]): Promise<ApiResponse<{ count: number }>> => {
    return http.delete('/diy/page/batch', { data: { ids } })
  },

  /**
   * 发布页面
   */
  publish: (id: number): Promise<ApiResponse<void>> => {
    return http.post(`/diy/page/${id}/publish`)
  },

  /**
   * 取消发布页面
   */
  unpublish: (id: number): Promise<ApiResponse<void>> => {
    return http.post(`/diy/page/${id}/unpublish`)
  },

  /**
   * 复制页面
   */
  copy: (id: number, data: {
    title: string
    slug: string
  }): Promise<ApiResponse<DiyPage>> => {
    return http.post(`/diy/page/${id}/copy`, data)
  },

  /**
   * 保存页面内容
   */
  saveContent: (id: number, data: {
    config?: Record<string, any>
    custom_css?: string
    custom_js?: string
    components?: any[]
  }): Promise<ApiResponse<void>> => {
    return http.post(`/diy/page/${id}/content`, data)
  },

  /**
   * 预览页面
   */
  preview: (id: number): Promise<ApiResponse<{
    page: DiyPage
    components: DiyComponent[]
    preview_url: string
  }>> => {
    return http.get(`/diy/page/${id}/preview`)
  },

  /**
   * 获取页面统计
   */
  getStatistics: (): Promise<ApiResponse<{
    total: number
    published: number
    draft: number
    today_new: number
    week_new: number
    month_new: number
    category_stats: any[]
    template_stats: any[]
  }>> => {
    return http.get('/diy/page/statistics')
  },

  /**
   * 导出页面
   */
  export: (params: {
    ids?: string
    category?: string
  }): Promise<ApiResponse<{ file_path: string }>> => {
    return http.get('/diy/page/export', { params })
  },

  /**
   * 导入页面
   */
  import: (data: FormData): Promise<ApiResponse<{
    success_count: number
    error_count: number
    errors: string[]
  }>> => {
    return http.post('/diy/page/import', data, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

/**
 * DIY模板管理相关API
 */
export const diyTemplateApi = {
  /**
   * 获取模板列表
   */
  getList: (params: {
    page?: number
    per_page?: number
    keyword?: string
    status?: number | string
    category?: string
    is_default?: number | string
    sort_field?: string
    sort_order?: string
  }): Promise<PaginatedResponse<DiyTemplate>> => {
    return http.get('/diy/template', { params })
  },

  /**
   * 获取所有模板（用于下拉选择）
   */
  getAll: (): Promise<ApiResponse<DiyTemplate[]>> => {
    return http.get('/diy/template/all')
  },

  /**
   * 获取模板详情
   */
  getById: (id: number): Promise<ApiResponse<DiyTemplate>> => {
    return http.get(`/diy/template/${id}`)
  },

  /**
   * 创建模板
   */
  create: (data: {
    name: string
    slug: string
    description?: string
    preview_image?: string
    config?: Record<string, any>
    components?: Record<string, any>
    category?: string
    tags?: string[]
    version?: string
    author?: string
    status?: number
    is_default?: number
    sort_order?: number
  }): Promise<ApiResponse<DiyTemplate>> => {
    return http.post('/diy/template', data)
  },

  /**
   * 更新模板
   */
  update: (id: number, data: {
    name?: string
    slug?: string
    description?: string
    preview_image?: string
    config?: Record<string, any>
    components?: Record<string, any>
    category?: string
    tags?: string[]
    version?: string
    author?: string
    status?: number
    is_default?: number
    sort_order?: number
  }): Promise<ApiResponse<DiyTemplate>> => {
    return http.put(`/diy/template/${id}`, data)
  },

  /**
   * 删除模板
   */
  delete: (id: number): Promise<ApiResponse<void>> => {
    return http.delete(`/diy/template/${id}`)
  },

  /**
   * 批量删除模板
   */
  batchDelete: (ids: number[]): Promise<ApiResponse<{ count: number }>> => {
    return http.delete('/diy/template/batch', { data: { ids } })
  },

  /**
   * 启用模板
   */
  enable: (id: number): Promise<ApiResponse<void>> => {
    return http.post(`/diy/template/${id}/enable`)
  },

  /**
   * 禁用模板
   */
  disable: (id: number): Promise<ApiResponse<void>> => {
    return http.post(`/diy/template/${id}/disable`)
  },

  /**
   * 复制模板
   */
  copy: (id: number, data: {
    name: string
    slug: string
  }): Promise<ApiResponse<DiyTemplate>> => {
    return http.post(`/diy/template/${id}/copy`, data)
  },

  /**
   * 设置默认模板
   */
  setDefault: (id: number): Promise<ApiResponse<void>> => {
    return http.post(`/diy/template/${id}/set-default`)
  },

  /**
   * 获取模板统计
   */
  getStatistics: (): Promise<ApiResponse<{
    total: number
    enabled: number
    disabled: number
    default_template: string
    category_stats: any[]
    usage_stats: any[]
  }>> => {
    return http.get('/diy/template/statistics')
  },

  /**
   * 导出模板
   */
  export: (params: {
    ids?: string
    category?: string
  }): Promise<ApiResponse<{ file_path: string }>> => {
    return http.get('/diy/template/export', { params })
  },

  /**
   * 导入模板
   */
  import: (data: FormData): Promise<ApiResponse<{
    success_count: number
    error_count: number
    errors: string[]
  }>> => {
    return http.post('/diy/template/import', data, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 安装模板
   */
  install: (data: {
    name: string
    source: string
    config?: Record<string, any>
  }): Promise<ApiResponse<DiyTemplate>> => {
    return http.post('/diy/template/install', data)
  },

  /**
   * 卸载模板
   */
  uninstall: (id: number): Promise<ApiResponse<void>> => {
    return http.post(`/diy/template/${id}/uninstall`)
  }
}

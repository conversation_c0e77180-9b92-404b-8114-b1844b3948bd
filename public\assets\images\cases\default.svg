<svg width="400" height="250" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7877c6;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#ff77c6;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#78dbff;stop-opacity:0.8" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#bg)"/>
  <rect x="50" y="50" width="300" height="150" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" stroke-dasharray="10,5"/>
  <text x="200" y="110" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-family="Arial, sans-serif" font-size="16" font-weight="500">案例图片</text>
  <text x="200" y="140" text-anchor="middle" fill="rgba(255,255,255,0.6)" font-family="Arial, sans-serif" font-size="12">暂无图片</text>
  <circle cx="200" cy="80" r="15" fill="none" stroke="rgba(255,255,255,0.4)" stroke-width="2"/>
  <circle cx="200" cy="80" r="5" fill="rgba(255,255,255,0.4)"/>
</svg>

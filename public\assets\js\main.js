/**
 * 主要JavaScript文件
 */

// 自定义下拉菜单实现 - 完全避免Bootstrap冲突

$(function() {
    
    // 页面加载完成后隐藏预加载动画
    $(window).on('load', function() {
        $('#preloader').fadeOut(500);
    });
    
    // 完全自定义的下拉菜单实现 - 避免Bootstrap冲突
    function initCustomDropdowns() {
        // 禁用Bootstrap的自动初始化
        $(document).off('click.bs.dropdown.data-api');
        
        // 清除所有现有的下拉菜单事件
        $('.mega-dropdown').off();
        $('.dropdown-toggle').off();
        $('.dropdown-menu').off();
        
        $('.mega-dropdown').each(function() {
            var $dropdown = $(this);
            var $toggle = $dropdown.find('.dropdown-toggle');
            var $menu = $dropdown.find('.dropdown-menu');
            var closeTimer;
            var isMenuActive = false;

            // 鼠标进入触发器
            $dropdown.on('mouseenter.custom', function() {
                clearTimeout(closeTimer);
                
                // 关闭其他下拉菜单
                $('.mega-dropdown').not(this).removeClass('show').find('.dropdown-menu').removeClass('show');
                
                // 显示当前菜单
                $dropdown.addClass('show');
                $menu.addClass('show');
                $toggle.attr('aria-expanded', 'true');
            });

            // 鼠标离开触发器
            $dropdown.on('mouseleave.custom', function(e) {
                var relatedTarget = e.relatedTarget;
                
                // 检查是否移动到菜单内容
                if (relatedTarget && ($menu[0].contains(relatedTarget) || $menu[0] === relatedTarget)) {
                    return;
                }

                closeTimer = setTimeout(function() {
                    if (!isMenuActive) {
                        $dropdown.removeClass('show');
                        $menu.removeClass('show');
                        $toggle.attr('aria-expanded', 'false');
                    }
                }, 300);
            });

            // 鼠标进入菜单内容
            $menu.on('mouseenter.custom', function() {
                clearTimeout(closeTimer);
                isMenuActive = true;
            });

            // 鼠标离开菜单内容
            $menu.on('mouseleave.custom', function(e) {
                isMenuActive = false;
                var relatedTarget = e.relatedTarget;

                if (relatedTarget && ($dropdown[0].contains(relatedTarget) || $dropdown[0] === relatedTarget)) {
                    return;
                }

                closeTimer = setTimeout(function() {
                    $dropdown.removeClass('show');
                    $menu.removeClass('show');
                    $toggle.attr('aria-expanded', 'false');
                }, 200);
            });

            // 阻止链接的默认行为（保持悬停功能）
            $toggle.on('click.custom', function(e) {
                e.preventDefault();
                return false;
            });
        });

        // 点击页面其他地方关闭所有下拉菜单
        $(document).on('click.custom', function(e) {
            if (!$(e.target).closest('.mega-dropdown').length) {
                $('.mega-dropdown').removeClass('show').find('.dropdown-menu').removeClass('show');
                $('.dropdown-toggle').attr('aria-expanded', 'false');
            }
        });
    }
    
    // 立即初始化自定义下拉菜单
    initCustomDropdowns();
    
    // 调试信息 - 修复版本
    setTimeout(function() {
        console.log('=== 自定义下拉菜单调试信息 ===');
        console.log('jQuery版本:', $.fn.jquery);

        var dropdownElements = document.querySelectorAll('[data-bs-toggle="dropdown"]');
        console.log('找到下拉菜单元素数量:', dropdownElements.length);

        // 安全的元素检查
        dropdownElements.forEach(function(element, index) {
            if (element && typeof element === 'object' && element.nodeType === 1) {
                console.log('下拉菜单 ' + (index + 1) + ':', {
                    id: element.id || 'no-id',
                    tagName: element.tagName,
                    hasEventListener: typeof element.addEventListener === 'function'
                });
            } else {
                console.warn('无效的DOM元素:', element);
            }
        });
        console.log('=== 调试信息结束 ===');
    }, 500);
    
    // 原生JavaScript滚动监听 - 安全版本
    window.addEventListener('scroll', function() {
        var scroll = window.pageYOffset || document.documentElement.scrollTop;
        var header = document.querySelector('header');

        if (header && header.classList) {
            if (scroll >= 50) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        }
    });
    
    // 应用节流到滚动和鼠标移动事件 - 安全版本
    $(window).scroll(throttle(function() {
        var scroll = $(window).scrollTop();

        // 导航栏滚动效果 - 安全检查
        var $header = $('header');
        if ($header.length > 0) {
            if (scroll >= 50) {
                $header.addClass('scrolled');
            } else {
                $header.removeClass('scrolled');
            }
        }

        // 返回顶部按钮显示/隐藏 - 安全检查
        var $backToTop = $('.back-to-top');
        if ($backToTop.length > 0) {
            if (scroll >= 300) {
                $backToTop.addClass('show');
            } else {
                $backToTop.removeClass('show');
            }
        }

        // 滚动动画检查
        checkAnimation();
    }, 100));
    
    // 平滑滚动到锚点 - 安全版本
    $('a[href^="#"]').on('click', function(e) {
        var target = this.hash;
        var $target = $(target);

        if ($target.length > 0 && $target.offset()) {
            e.preventDefault();
            $('html, body').animate({
                scrollTop: $target.offset().top - 100
            }, 800);
        }
    });
    
    // 在线客服功能 - 安全版本
    var $serviceBtn = $('#serviceBtn');
    var $servicePanel = $('#servicePanel');
    var $closeService = $('#closeService');

    if ($serviceBtn.length > 0 && $servicePanel.length > 0) {
        $serviceBtn.click(function() {
            $servicePanel.toggleClass('show');
        });
    }

    if ($closeService.length > 0 && $servicePanel.length > 0) {
        $closeService.click(function() {
            $servicePanel.removeClass('show');
        });
    }

    // 点击其他地方关闭客服面板 - 安全版本
    if ($servicePanel.length > 0) {
        $(document).click(function(e) {
            if (!$(e.target).closest('.online-service').length) {
                $servicePanel.removeClass('show');
            }
        });
    }
    

    
    // 鼠标移动视差效果
    $(document).mousemove(function(e) {
        if ($(window).width() > 768) {
            var mouseX = e.pageX;
            var mouseY = e.pageY;
            var windowWidth = $(window).width();
            var windowHeight = $(window).height();
            
            // 计算鼠标位置百分比
            var xPercent = (mouseX / windowWidth - 0.5) * 2;
            var yPercent = (mouseY / windowHeight - 0.5) * 2;
            
            // 应用视差效果到装饰元素
            $('.floating-icon').each(function(index) {
                var speed = (index + 1) * 0.5;
                var x = xPercent * speed;
                var y = yPercent * speed;
                $(this).css('transform', 'translate(' + x + 'px, ' + y + 'px)');
            });
            
            $('.decoration-circle').each(function(index) {
                var speed = (index + 1) * 0.3;
                var x = xPercent * speed;
                var y = yPercent * speed;
                $(this).css('transform', 'translate(' + x + 'px, ' + y + 'px)');
            });
        }
    });
    

    

    
    // 性能优化 - 节流函数
    function throttle(func, limit) {
        var inThrottle;
        return function() {
            var args = arguments;
            var context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(function() { inThrottle = false; }, limit);
            }
        }
    }
    

    
    // 产品轮播
    if ($('.products-slider').length) {
        var productsSwiper = new Swiper('.products-slider', {
            loop: true,
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
            },
            slidesPerView: 1,
            spaceBetween: 30,
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            breakpoints: {
                768: {
                    slidesPerView: 2,
                },
                992: {
                    slidesPerView: 3,
                },
                1200: {
                    slidesPerView: 4,
                }
            }
        });
    }
    
    // 客户案例轮播
    if ($('.cases-slider').length) {
        var casesSwiper = new Swiper('.cases-slider', {
            loop: true,
            autoplay: {
                delay: 4000,
                disableOnInteraction: false,
            },
            slidesPerView: 1,
            spaceBetween: 30,
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            breakpoints: {
                768: {
                    slidesPerView: 2,
                },
                992: {
                    slidesPerView: 3,
                }
            }
        });
    }
    
    // 数字动画
    function animateNumbers() {
        $('.counter').each(function() {
            var $this = $(this);
            var countTo = $this.attr('data-count');
            
            $({ countNum: $this.text() }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'linear',
                step: function() {
                    $this.text(Math.floor(this.countNum));
                },
                complete: function() {
                    $this.text(this.countNum);
                }
            });
        });
    }
    
    // 滚动动画
    function checkAnimation() {
        $('.fade-in, .slide-in-left, .slide-in-right').each(function() {
            var elementTop = $(this).offset().top;
            var elementBottom = elementTop + $(this).outerHeight();
            var viewportTop = $(window).scrollTop();
            var viewportBottom = viewportTop + $(window).height();
            
            if (elementBottom > viewportTop && elementTop < viewportBottom) {
                $(this).addClass('visible');
            }
        });
        
        // 数字计数器动画
        $('.counter').each(function() {
            var elementTop = $(this).offset().top;
            var elementBottom = elementTop + $(this).outerHeight();
            var viewportTop = $(window).scrollTop();
            var viewportBottom = viewportTop + $(window).height();
            
            if (elementBottom > viewportTop && elementTop < viewportBottom && !$(this).hasClass('animated')) {
                $(this).addClass('animated');
                animateNumbers();
            }
        });
    }
    
    // 表单验证
    $('.contact-form').on('submit', function(e) {
        e.preventDefault();
        
        var form = $(this);
        var formData = new FormData(this);
        var submitBtn = form.find('button[type="submit"]');
        var originalText = submitBtn.text();
        
        // 基本验证
        var isValid = true;
        form.find('input[required], textarea[required]').each(function() {
            if (!$(this).val().trim()) {
                isValid = false;
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        
        // 邮箱验证
        var email = form.find('input[type="email"]').val();
        if (email && !isValidEmail(email)) {
            isValid = false;
            form.find('input[type="email"]').addClass('is-invalid');
        }
        
        if (!isValid) {
            showAlert('请填写所有必填字段', 'error');
            return;
        }
        
        // 提交表单
        submitBtn.text('提交中...').prop('disabled', true);
        
        $.ajax({
            url: form.attr('action') || 'contact-form.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                try {
                    var result = JSON.parse(response);
                    if (result.success) {
                        showAlert('消息发送成功，我们会尽快回复您！', 'success');
                        form[0].reset();
                    } else {
                        showAlert(result.message || '发送失败，请稍后重试', 'error');
                    }
                } catch (e) {
                    showAlert('发送成功！', 'success');
                    form[0].reset();
                }
            },
            error: function() {
                showAlert('网络错误，请稍后重试', 'error');
            },
            complete: function() {
                submitBtn.text(originalText).prop('disabled', false);
            }
        });
    });
    
    // 邮箱验证函数
    function isValidEmail(email) {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    // 显示提示信息
    function showAlert(message, type) {
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                       message +
                       '<button type="button" class="close" data-dismiss="alert">' +
                       '<span>&times;</span>' +
                       '</button>' +
                       '</div>';
        
        // 移除现有的提示
        $('.alert').remove();
        
        // 添加新提示
        $('body').prepend('<div class="alert-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;">' + alertHtml + '</div>');
        
        // 3秒后自动隐藏
        setTimeout(function() {
            $('.alert-container').fadeOut(function() {
                $(this).remove();
            });
        }, 3000);
    }
    
    // 微信二维码显示
    window.showWechatQR = function() {
        // 这里可以显示微信二维码弹窗
        showAlert('请扫描微信二维码添加客服', 'info');
    };
    
    // 初始化WOW动画
    if (typeof WOW !== 'undefined') {
        new WOW().init();
    }
    
    // 返回顶部功能
    $('.back-to-top').click(function() {
        $('html, body').animate({
            scrollTop: 0
        }, 800);
        return false;
    });
    
    // 初始检查动画
    checkAnimation();
    
    // 菜单项点击效果
    function initMenuItemEffects() {
        $('.mega-menu-item, .solution-item, .product-item').on('click', function(e) {
            var $this = $(this);

            // 添加点击动画
            $this.addClass('clicked');
            setTimeout(function() {
                $this.removeClass('clicked');
            }, 200);

            // 如果是链接，允许默认行为
            if ($this.is('a') && $this.attr('href')) {
                return true;
            }
        });
    }

    // 初始化菜单效果
    initMenuItemEffects();

    // 调试函数已移到全局作用域



    // 页面加载进度条
    $(window).on('load', function() {
        $('.progress-bar').animate({
            width: '100%'
        }, 1000);
    });

});

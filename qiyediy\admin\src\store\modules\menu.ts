/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 菜单状态管理
 */

import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface MenuItem {
  path: string
  title: string
  icon?: string
  permission?: string
  disabled?: boolean
  hidden?: boolean
  badge?: string | number
  badgeType?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  children?: MenuItem[]
}

export const useMenuStore = defineStore('menu', () => {
  // 菜单列表
  const menuList = ref<MenuItem[]>([
    {
      path: '/dashboard',
      title: '仪表盘',
      icon: 'Odometer',
      permission: 'dashboard.view'
    },
    {
      path: '/user',
      title: '用户管理',
      icon: 'User',
      permission: 'user.view',
      children: [
        {
          path: '/user/list',
          title: '用户列表',
          icon: 'UserFilled',
          permission: 'user.view'
        },
        {
          path: '/user/roles',
          title: '角色管理',
          icon: 'Avatar',
          permission: 'role.view'
        },
        {
          path: '/user/permissions',
          title: '权限管理',
          icon: 'Key',
          permission: 'role.assign_permissions'
        }
      ]
    },
    {
      path: '/diy',
      title: 'DIY管理',
      icon: 'Edit',
      permission: 'diy.view',
      children: [
        {
          path: '/diy/pages',
          title: '页面管理',
          icon: 'Document',
          permission: 'diy.view'
        },
        {
          path: '/diy/editor',
          title: '可视化编辑',
          icon: 'EditPen',
          permission: 'diy.create'
        },
        {
          path: '/diy/templates',
          title: '模板管理',
          icon: 'Collection',
          permission: 'diy.template'
        },
        {
          path: '/diy/components',
          title: '组件管理',
          icon: 'Grid',
          permission: 'diy.component'
        }
      ]
    },
    {
      path: '/content',
      title: '内容管理',
      icon: 'Notebook',
      permission: 'content.view',
      children: [
        {
          path: '/content/articles',
          title: '文章管理',
          icon: 'Document',
          permission: 'content.view'
        },
        {
          path: '/content/categories',
          title: '分类管理',
          icon: 'FolderOpened',
          permission: 'content.category'
        },
        {
          path: '/content/tags',
          title: '标签管理',
          icon: 'PriceTag',
          permission: 'content.tag'
        }
      ]
    },
    {
      path: '/media',
      title: '媒体库',
      icon: 'Picture',
      permission: 'media.view',
      children: [
        {
          path: '/media/files',
          title: '文件管理',
          icon: 'Folder',
          permission: 'media.view'
        },
        {
          path: '/media/upload',
          title: '文件上传',
          icon: 'Upload',
          permission: 'media.upload'
        }
      ]
    },
    {
      path: '/system',
      title: '系统管理',
      icon: 'Setting',
      permission: 'system.view',
      children: [
        {
          path: '/system/settings',
          title: '系统设置',
          icon: 'Tools',
          permission: 'system.update'
        },
        {
          path: '/system/menus',
          title: '菜单管理',
          icon: 'Menu',
          permission: 'system.menu'
        },
        {
          path: '/system/logs',
          title: '操作日志',
          icon: 'Document',
          permission: 'system.log'
        },
        {
          path: '/system/backup',
          title: '数据备份',
          icon: 'Download',
          permission: 'system.backup'
        },
        {
          path: '/system/cache',
          title: '缓存管理',
          icon: 'Refresh',
          permission: 'system.cache'
        }
      ]
    },
    {
      path: '/tools',
      title: '开发工具',
      icon: 'Wrench',
      permission: 'system.view',
      children: [
        {
          path: '/tools/generator',
          title: '代码生成',
          icon: 'DocumentAdd',
          permission: 'system.view'
        },
        {
          path: '/tools/database',
          title: '数据库管理',
          icon: 'Coin',
          permission: 'system.view'
        },
        {
          path: '/tools/api-docs',
          title: 'API文档',
          icon: 'Document',
          permission: 'system.view'
        }
      ]
    }
  ])

  /**
   * 根据路径查找菜单项
   */
  const findMenuByPath = (path: string): MenuItem | null => {
    const findInMenu = (items: MenuItem[]): MenuItem | null => {
      for (const item of items) {
        if (item.path === path) {
          return item
        }
        if (item.children) {
          const found = findInMenu(item.children)
          if (found) return found
        }
      }
      return null
    }
    
    return findInMenu(menuList.value)
  }

  /**
   * 获取面包屑路径
   */
  const getBreadcrumbPath = (path: string): MenuItem[] => {
    const breadcrumbs: MenuItem[] = []
    
    const findPath = (items: MenuItem[], targetPath: string, parents: MenuItem[] = []): boolean => {
      for (const item of items) {
        const currentPath = [...parents, item]
        
        if (item.path === targetPath) {
          breadcrumbs.push(...currentPath)
          return true
        }
        
        if (item.children && findPath(item.children, targetPath, currentPath)) {
          return true
        }
      }
      return false
    }
    
    findPath(menuList.value, path)
    return breadcrumbs
  }

  /**
   * 获取所有菜单路径
   */
  const getAllMenuPaths = (): string[] => {
    const paths: string[] = []
    
    const collectPaths = (items: MenuItem[]) => {
      items.forEach(item => {
        paths.push(item.path)
        if (item.children) {
          collectPaths(item.children)
        }
      })
    }
    
    collectPaths(menuList.value)
    return paths
  }

  /**
   * 更新菜单徽章
   */
  const updateMenuBadge = (path: string, badge?: string | number, badgeType?: string) => {
    const menu = findMenuByPath(path)
    if (menu) {
      menu.badge = badge
      menu.badgeType = badgeType as any
    }
  }

  /**
   * 设置菜单禁用状态
   */
  const setMenuDisabled = (path: string, disabled: boolean) => {
    const menu = findMenuByPath(path)
    if (menu) {
      menu.disabled = disabled
    }
  }

  /**
   * 设置菜单隐藏状态
   */
  const setMenuHidden = (path: string, hidden: boolean) => {
    const menu = findMenuByPath(path)
    if (menu) {
      menu.hidden = hidden
    }
  }

  /**
   * 添加菜单项
   */
  const addMenuItem = (parentPath: string | null, menuItem: MenuItem) => {
    if (!parentPath) {
      menuList.value.push(menuItem)
    } else {
      const parent = findMenuByPath(parentPath)
      if (parent) {
        if (!parent.children) {
          parent.children = []
        }
        parent.children.push(menuItem)
      }
    }
  }

  /**
   * 删除菜单项
   */
  const removeMenuItem = (path: string) => {
    const removeFromMenu = (items: MenuItem[]): boolean => {
      for (let i = 0; i < items.length; i++) {
        if (items[i].path === path) {
          items.splice(i, 1)
          return true
        }
        if (items[i].children && removeFromMenu(items[i].children!)) {
          return true
        }
      }
      return false
    }
    
    removeFromMenu(menuList.value)
  }

  /**
   * 重置菜单
   */
  const resetMenu = () => {
    // 这里可以重新加载默认菜单或从服务器获取
    console.log('重置菜单')
  }

  return {
    // 状态
    menuList,
    
    // 方法
    findMenuByPath,
    getBreadcrumbPath,
    getAllMenuPaths,
    updateMenuBadge,
    setMenuDisabled,
    setMenuHidden,
    addMenuItem,
    removeMenuItem,
    resetMenu
  }
})

---
description:
globs:
alwaysApply: false
---
# Development Best Practices

## Code Quality Standards

### Before Making Changes
- **Always read files before editing** - Understand current implementation
- **Include debug information** in program output for troubleshooting
- **Use Test-Driven Development (TDD)** - Write tests first to clarify behavior
- **Test every feature** - Fix bugs immediately before continuing

### File Operations
- **Read before edit** - Always examine file contents before modifications
- **Prefer editing over creating** - Edit existing files rather than creating new ones
- **Don't create unnecessary files** - Only create files absolutely necessary for goals
- **Never create documentation proactively** - Only create docs when explicitly requested

### Security & Dependencies
- **Run `npm audit` when vulnerabilities appear** in terminal
- **Ask before using `-force` git commands** - These can be destructive
- **Validate all user inputs** using validation rules in `app/validate/`
- **Use proper authentication** for admin functions

## ThinkPHP 8 Specific Guidelines

### Database Operations
- Use think-orm models in `app/model/` for all database interactions
- Define relationships properly between models
- Use validation rules before saving data
- Handle database exceptions gracefully

### Controller Structure
- Extend [app/BaseController.php](mdc:app/BaseController.php) for common functionality
- Keep controllers thin - move business logic to services in `app/service/`
- Return proper HTTP status codes
- Use middleware for authentication and authorization

### Frontend Development
- Maintain responsive design for all components
- Use existing CSS frameworks and components in `public/assets/`
- Test on multiple device sizes
- Optimize images and assets for web delivery

### DIY Page Builder
- Follow existing component structure in `public/diy/`
- Ensure components are reusable and configurable
- Implement proper drag-and-drop functionality
- Maintain real-time preview capabilities

## Error Handling & Debugging

### Common Issues
- **Composer dependency conflicts** - Use `composer update` carefully
- **Database connection issues** - Check config files and credentials
- **File permission problems** - Ensure proper write permissions for uploads
- **Route conflicts** - Check route definitions in `route/` directory

### Debugging Strategies
- Use think-dumper for remote debugging capabilities
- Log errors appropriately using ThinkPHP logging
- Include meaningful error messages for users
- Test error scenarios thoroughly

## Performance Considerations

- **Optimize database queries** - Use proper indexing and avoid N+1 problems
- **Cache frequently accessed data** - Use ThinkPHP caching mechanisms
- **Minimize HTTP requests** - Combine and minify assets
- **Optimize images** - Use appropriate formats and compression

## Documentation Standards

- **Update README.md** when adding major features
- **Document API endpoints** with proper examples
- **Include setup instructions** for new developers
- **Maintain changelog** for version tracking

<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 角色管理控制器
 */

declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use app\model\Role;
use app\service\RoleService;
use app\validate\RoleValidate;
use think\Response;

/**
 * 角色管理控制器
 */
class RoleController extends BaseController
{
    protected RoleService $roleService;

    public function __construct()
    {
        parent::__construct();
        $this->roleService = new RoleService();
    }

    /**
     * 角色列表
     * @return Response
     */
    public function index(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('role.view')) {
                return $this->error('没有权限访问', 403);
            }

            $params = $this->getPaginateParams();
            $result = $this->roleService->getList($params);

            // 记录操作日志
            $this->logOperation('view', 'role_list');

            return $this->paginate($result);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 角色详情
     * @param int $id 角色ID
     * @return Response
     */
    public function read(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('role.view')) {
                return $this->error('没有权限访问', 403);
            }

            $role = $this->roleService->getById($id);
            if (!$role) {
                return $this->error('角色不存在', 404);
            }

            // 记录操作日志
            $this->logOperation('view', 'role', $id);

            return $this->success($role);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 创建角色
     * @return Response
     */
    public function save(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('role.create')) {
                return $this->error('没有权限创建角色', 403);
            }

            $data = $this->post();

            // 数据验证
            $this->validate($data, RoleValidate::class . '.create');

            // 创建角色
            $role = $this->roleService->create($data);

            // 记录操作日志
            $this->logOperation('create', 'role', $role->id, $data);

            return $this->success($role, '角色创建成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 更新角色
     * @param int $id 角色ID
     * @return Response
     */
    public function update(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('role.update')) {
                return $this->error('没有权限更新角色', 403);
            }

            $data = $this->post();

            // 数据验证
            $this->validate($data, RoleValidate::class . '.update');

            // 更新角色
            $role = $this->roleService->update($id, $data);

            // 记录操作日志
            $this->logOperation('update', 'role', $id, $data);

            return $this->success($role, '角色更新成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除角色
     * @param int $id 角色ID
     * @return Response
     */
    public function delete(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('role.delete')) {
                return $this->error('没有权限删除角色', 403);
            }

            // 删除角色
            $this->roleService->delete($id);

            // 记录操作日志
            $this->logOperation('delete', 'role', $id);

            return $this->success([], '角色删除成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 批量删除角色
     * @return Response
     */
    public function batchDelete(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('role.delete')) {
                return $this->error('没有权限删除角色', 403);
            }

            $ids = $this->post('ids', []);
            $this->validateBatchDelete($ids);

            // 批量删除
            $count = $this->roleService->batchDelete($ids);

            // 记录操作日志
            $this->logOperation('batch_delete', 'role', null, ['ids' => $ids, 'count' => $count]);

            return $this->success(['count' => $count], "成功删除 {$count} 个角色");

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 设置默认角色
     * @param int $id 角色ID
     * @return Response
     */
    public function setDefault(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('role.update')) {
                return $this->error('没有权限操作', 403);
            }

            $this->roleService->setDefault($id);

            // 记录操作日志
            $this->logOperation('set_default', 'role', $id);

            return $this->success([], '默认角色设置成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 同步权限
     * @param int $id 角色ID
     * @return Response
     */
    public function syncPermissions(int $id): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('role.assign_permissions')) {
                return $this->error('没有权限分配权限', 403);
            }

            $permissions = $this->post('permissions', []);
            if (!is_array($permissions)) {
                return $this->error('权限列表必须是数组');
            }

            $this->roleService->syncPermissions($id, $permissions);

            // 记录操作日志
            $this->logOperation('sync_permissions', 'role', $id, ['permissions' => $permissions]);

            return $this->success([], '权限同步成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取所有角色（用于下拉选择）
     * @return Response
     */
    public function all(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('role.view')) {
                return $this->error('没有权限访问', 403);
            }

            $roles = $this->roleService->getAll();

            return $this->success($roles);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取权限列表
     * @return Response
     */
    public function permissions(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('role.view')) {
                return $this->error('没有权限访问', 403);
            }

            $permissions = $this->roleService->getPermissions();

            return $this->success($permissions);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取角色统计
     * @return Response
     */
    public function statistics(): Response
    {
        try {
            // 检查权限
            if (!$this->checkPermission('role.view')) {
                return $this->error('没有权限访问', 403);
            }

            $stats = $this->roleService->getStatistics();

            return $this->success($stats);

        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}

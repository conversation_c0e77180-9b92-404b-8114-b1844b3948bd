---
description:
globs:
alwaysApply: false
---
# 前端开发规范与最佳实践

## 项目前端架构

### 技术栈
- **HTML5**: 语义化标签，无障碍访问
- **CSS3**: Flexbox, Grid, 动画效果
- **JavaScript ES6+**: 模块化开发
- **Bootstrap**: 响应式框架
- **Font Awesome**: 图标库

### 目录结构
```
public/
├── assets/
│   ├── css/
│   │   └── admin/          # 后台管理样式
│   ├── images/             # 图片资源
│   │   ├── cases/          # 案例图片
│   │   ├── goods/          # 产品图片
│   │   ├── nav/            # 导航图片
│   │   └── news/           # 新闻图片
│   ├── js/
│   │   └── admin/          # 后台管理脚本
│   └── webfonts/           # 字体文件
├── diy/                    # 页面构建器
│   ├── css/                # 构建器样式
│   ├── js/                 # 构建器脚本
│   │   ├── components/     # 组件库
│   │   └── templates/      # 模板库
│   └── docs/               # 文档
├── js/                     # 公共JavaScript
└── static/                 # 静态资源
```

## HTML 开发规范

### 语义化标签
```html
<!-- 正确的语义化结构 -->
<header class="site-header">
    <nav class="main-navigation">
        <ul class="nav-menu">
            <li><a href="/">首页</a></li>
            <li><a href="/news">新闻</a></li>
        </ul>
    </nav>
</header>

<main class="main-content">
    <article class="news-article">
        <header class="article-header">
            <h1 class="article-title">文章标题</h1>
            <time datetime="2024-01-01">2024年1月1日</time>
        </header>
        <section class="article-content">
            <p>文章内容...</p>
        </section>
    </article>
</main>

<footer class="site-footer">
    <p>&copy; 2024 公司名称</p>
</footer>
```

### 无障碍访问
```html
<!-- 图片alt属性 -->
<img src="news.jpg" alt="新闻标题描述">

<!-- 表单标签关联 -->
<label for="email">邮箱地址</label>
<input type="email" id="email" name="email" required>

<!-- 按钮语义 -->
<button type="submit" aria-label="提交表单">提交</button>

<!-- 跳转链接 -->
<a href="#main-content" class="skip-link">跳转到主要内容</a>
```

## CSS 开发规范

### BEM 命名规范
```css
/* Block - 块 */
.news-card {}

/* Element - 元素 */
.news-card__title {}
.news-card__content {}
.news-card__image {}

/* Modifier - 修饰符 */
.news-card--featured {}
.news-card__title--large {}
```

### CSS 变量管理
```css
:root {
    /* 主色调 */
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    
    /* 中性色 */
    --text-color: #2d3748;
    --text-light: #718096;
    --bg-color: #ffffff;
    --bg-light: #f7fafc;
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    
    /* 字体 */
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    
    /* 阴影 */
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.12);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
    
    /* 圆角 */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 1rem;
}
```

### 响应式设计
```css
/* 移动优先设计 */
.container {
    width: 100%;
    padding: 0 var(--spacing-md);
}

/* 平板 */
@media (min-width: 768px) {
    .container {
        max-width: 750px;
        margin: 0 auto;
    }
}

/* 桌面 */
@media (min-width: 1024px) {
    .container {
        max-width: 1200px;
    }
}

/* 大屏幕 */
@media (min-width: 1200px) {
    .container {
        max-width: 1400px;
    }
}
```

### Flexbox 和 Grid 布局
```css
/* Flexbox 布局 */
.flex-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

@media (min-width: 768px) {
    .flex-container {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }
}

/* Grid 布局 */
.grid-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
}

@media (min-width: 768px) {
    .grid-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .grid-container {
        grid-template-columns: repeat(3, 1fr);
    }
}
```

## JavaScript 开发规范

### ES6+ 语法
```javascript
// 使用 const/let 替代 var
const API_URL = '/api';
let currentPage = 1;

// 箭头函数
const fetchNews = async (page = 1) => {
    try {
        const response = await fetch(`${API_URL}/news?page=${page}`);
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('获取新闻失败:', error);
        throw error;
    }
};

// 解构赋值
const { title, content, image } = newsData;

// 模板字符串
const newsHtml = `
    <article class="news-card">
        <h3 class="news-card__title">${title}</h3>
        <p class="news-card__content">${content}</p>
        ${image ? `<img src="${image}" alt="${title}">` : ''}
    </article>
`;

// 扩展运算符
const newsList = [...existingNews, ...newNews];
```

### 模块化开发
```javascript
// utils.js - 工具函数模块
export const formatDate = (timestamp) => {
    return new Date(timestamp * 1000).toLocaleDateString('zh-CN');
};

export const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

// news.js - 新闻模块
import { formatDate, debounce } from './utils.js';

class NewsManager {
    constructor() {
        this.currentPage = 1;
        this.loading = false;
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadNews();
    }
    
    bindEvents() {
        const searchInput = document.querySelector('#news-search');
        if (searchInput) {
            searchInput.addEventListener('input', 
                debounce(this.handleSearch.bind(this), 300)
            );
        }
    }
    
    async loadNews(page = 1) {
        if (this.loading) return;
        
        this.loading = true;
        try {
            const data = await fetchNews(page);
            this.renderNews(data.list);
            this.currentPage = page;
        } catch (error) {
            this.showError('加载新闻失败，请稍后重试');
        } finally {
            this.loading = false;
        }
    }
    
    renderNews(newsList) {
        const container = document.querySelector('#news-container');
        if (!container) return;
        
        const html = newsList.map(news => `
            <article class="news-card" data-id="${news.id}">
                <div class="news-card__image">
                    <img src="${news.image}" alt="${news.title}" loading="lazy">
                </div>
                <div class="news-card__content">
                    <h3 class="news-card__title">${news.title}</h3>
                    <p class="news-card__summary">${news.summary}</p>
                    <div class="news-card__meta">
                        <time datetime="${news.published_at}">
                            ${formatDate(news.published_at)}
                        </time>
                        <span class="news-card__views">${news.views} 次浏览</span>
                    </div>
                </div>
            </article>
        `).join('');
        
        container.innerHTML = html;
    }
    
    handleSearch(event) {
        const keyword = event.target.value.trim();
        if (keyword) {
            this.searchNews(keyword);
        } else {
            this.loadNews(1);
        }
    }
    
    showError(message) {
        // 显示错误提示
        const toast = document.createElement('div');
        toast.className = 'toast toast--error';
        toast.textContent = message;
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    new NewsManager();
});
```

### 事件处理
```javascript
// 事件委托
document.addEventListener('click', (event) => {
    // 新闻卡片点击
    const newsCard = event.target.closest('.news-card');
    if (newsCard) {
        const newsId = newsCard.dataset.id;
        window.location.href = `/news/${newsId}`;
        return;
    }
    
    // 分页按钮点击
    const pageBtn = event.target.closest('.pagination__btn');
    if (pageBtn) {
        const page = parseInt(pageBtn.dataset.page);
        newsManager.loadNews(page);
        return;
    }
});

// 滚动加载
let isScrollLoading = false;
window.addEventListener('scroll', debounce(() => {
    if (isScrollLoading) return;
    
    const { scrollTop, scrollHeight, clientHeight } = document.documentElement;
    if (scrollTop + clientHeight >= scrollHeight - 100) {
        isScrollLoading = true;
        newsManager.loadMore().finally(() => {
            isScrollLoading = false;
        });
    }
}, 100));
```

## 性能优化

### 图片优化
```html
<!-- 响应式图片 -->
<picture>
    <source media="(min-width: 768px)" srcset="large.jpg">
    <source media="(min-width: 480px)" srcset="medium.jpg">
    <img src="small.jpg" alt="描述" loading="lazy">
</picture>

<!-- 懒加载 -->
<img src="placeholder.jpg" 
     data-src="actual-image.jpg" 
     alt="描述" 
     class="lazy-load">
```

### CSS 优化
```css
/* 使用 transform 替代改变位置属性 */
.element {
    transform: translateX(100px);
    /* 而不是 left: 100px; */
}

/* 使用 will-change 提示浏览器优化 */
.animated-element {
    will-change: transform;
}

/* 避免重排重绘 */
.optimized {
    transform: scale(1.1);
    opacity: 0.8;
    /* 而不是改变 width, height */
}
```

### JavaScript 优化
```javascript
// 防抖函数
const debounce = (func, wait) => {
    let timeout;
    return (...args) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
};

// 节流函数
const throttle = (func, limit) => {
    let inThrottle;
    return (...args) => {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
};

// 虚拟滚动（大列表优化）
class VirtualScroll {
    constructor(container, itemHeight, items) {
        this.container = container;
        this.itemHeight = itemHeight;
        this.items = items;
        this.visibleCount = Math.ceil(container.clientHeight / itemHeight);
        this.init();
    }
    
    init() {
        this.render();
        this.container.addEventListener('scroll', 
            throttle(this.handleScroll.bind(this), 16)
        );
    }
    
    handleScroll() {
        const scrollTop = this.container.scrollTop;
        const startIndex = Math.floor(scrollTop / this.itemHeight);
        this.render(startIndex);
    }
    
    render(startIndex = 0) {
        const endIndex = Math.min(
            startIndex + this.visibleCount + 1, 
            this.items.length
        );
        
        const visibleItems = this.items.slice(startIndex, endIndex);
        const html = visibleItems.map((item, index) => 
            this.renderItem(item, startIndex + index)
        ).join('');
        
        this.container.innerHTML = html;
        this.container.scrollTop = startIndex * this.itemHeight;
    }
}
```

## 浏览器兼容性

### CSS 兼容性
```css
/* 使用 autoprefixer 自动添加前缀 */
.element {
    display: flex;
    /* 自动生成：
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    */
}

/* 渐进增强 */
.modern-feature {
    background: #ccc; /* 降级方案 */
    background: linear-gradient(45deg, #667eea, #764ba2);
}

/* 特性检测 */
@supports (display: grid) {
    .grid-container {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
    }
}

@supports not (display: grid) {
    .grid-container {
        display: flex;
        flex-wrap: wrap;
    }
}
```

### JavaScript 兼容性
```javascript
// 使用 Babel 转译 ES6+ 代码
// 或者提供降级方案

// Promise 降级
if (!window.Promise) {
    // 加载 Promise polyfill
    loadScript('https://cdn.jsdelivr.net/npm/es6-promise@4/dist/es6-promise.auto.min.js');
}

// fetch 降级
const request = window.fetch || function(url, options) {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open(options.method || 'GET', url);
        xhr.onload = () => resolve({
            json: () => Promise.resolve(JSON.parse(xhr.responseText))
        });
        xhr.onerror = reject;
        xhr.send(options.body);
    });
};
```

## 调试和测试

### 调试工具
```javascript
// 开发环境调试
const DEBUG = process.env.NODE_ENV === 'development';

const log = (...args) => {
    if (DEBUG) {
        console.log('[DEBUG]', ...args);
    }
};

// 性能监控
const performanceMonitor = {
    start(name) {
        performance.mark(`${name}-start`);
    },
    
    end(name) {
        performance.mark(`${name}-end`);
        performance.measure(name, `${name}-start`, `${name}-end`);
        const measure = performance.getEntriesByName(name)[0];
        log(`${name} 耗时: ${measure.duration}ms`);
    }
};

// 使用示例
performanceMonitor.start('loadNews');
await loadNews();
performanceMonitor.end('loadNews');
```

### 错误处理
```javascript
// 全局错误处理
window.addEventListener('error', (event) => {
    console.error('JavaScript错误:', event.error);
    // 发送错误报告到服务器
    reportError({
        message: event.error.message,
        stack: event.error.stack,
        url: window.location.href,
        userAgent: navigator.userAgent
    });
});

// Promise 错误处理
window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
    event.preventDefault();
});

// 异步函数错误处理
const safeAsync = (asyncFn) => {
    return async (...args) => {
        try {
            return await asyncFn(...args);
        } catch (error) {
            console.error('异步函数错误:', error);
            throw error;
        }
    };
};
```

## 部署优化

### 资源压缩
```bash
# CSS 压缩
npm install -g clean-css-cli
cleancss -o style.min.css style.css

# JavaScript 压缩
npm install -g terser
terser script.js -o script.min.js

# 图片压缩
npm install -g imagemin-cli
imagemin images/*.jpg --out-dir=dist/images
```

### 缓存策略
```html
<!-- 静态资源版本控制 -->
<link rel="stylesheet" href="/assets/css/style.css?v=1.2.3">
<script src="/assets/js/app.js?v=1.2.3"></script>

<!-- 预加载关键资源 -->
<link rel="preload" href="/assets/fonts/main.woff2" as="font" type="font/woff2" crossorigin>
<link rel="preload" href="/assets/css/critical.css" as="style">

<!-- DNS 预解析 -->
<link rel="dns-prefetch" href="//cdn.example.com">
```

参考页面构建器相关文件了解更多前端开发实践：
- [all.js](mdc:public/diy/js/all.js) - 主要构建器逻辑
- [组件文件](mdc:public/diy/js/components/) - 各种UI组件实现

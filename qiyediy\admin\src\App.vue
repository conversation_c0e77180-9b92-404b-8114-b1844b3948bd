<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 管理后台根组件
-->

<template>
  <div class="admin-layout">
    <!-- 顶部导航 -->
    <div class="header">
      <div class="header-left">
        <h1 class="logo">QiyeDIY管理后台</h1>
      </div>
      <div class="header-right">
        <el-dropdown>
          <span class="user-info">
            <el-avatar :size="32" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
            <span class="username">管理员</span>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>个人中心</el-dropdown-item>
              <el-dropdown-item>系统设置</el-dropdown-item>
              <el-dropdown-item divided @click="handleLogout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="main-container">
      <!-- 侧边栏 -->
      <div class="sidebar">
        <el-menu
          default-active="dashboard"
          class="sidebar-menu"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#ffffff"
          :collapse="false"
        >
          <el-menu-item index="dashboard">
            <el-icon><House /></el-icon>
            <span>仪表盘</span>
          </el-menu-item>
          <el-sub-menu index="site">
            <template #title>
              <el-icon><Monitor /></el-icon>
              <span>网站管理</span>
            </template>
            <el-menu-item index="site-list">网站列表</el-menu-item>
            <el-menu-item index="site-create">创建网站</el-menu-item>
          </el-sub-menu>
          <el-sub-menu index="template">
            <template #title>
              <el-icon><Document /></el-icon>
              <span>模板管理</span>
            </template>
            <el-menu-item index="template-list">模板列表</el-menu-item>
            <el-menu-item index="template-upload">上传模板</el-menu-item>
          </el-sub-menu>
          <el-sub-menu index="user">
            <template #title>
              <el-icon><User /></el-icon>
              <span>用户管理</span>
            </template>
            <el-menu-item index="user-list">用户列表</el-menu-item>
            <el-menu-item index="user-role">角色权限</el-menu-item>
          </el-sub-menu>
          <el-menu-item index="diy-editor">
            <el-icon><Edit /></el-icon>
            <span>DIY编辑器</span>
          </el-menu-item>
          <el-menu-item index="system">
            <el-icon><Setting /></el-icon>
            <span>系统设置</span>
          </el-menu-item>
        </el-menu>
      </div>

      <!-- 内容区域 -->
      <div class="content">
        <div class="content-header">
          <h2>仪表盘</h2>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>首页</el-breadcrumb-item>
            <el-breadcrumb-item>仪表盘</el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <div class="content-body">
          <!-- 统计卡片 -->
          <div class="stats-grid">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon">
                  <el-icon size="40" color="#409eff"><Monitor /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ stats.sites }}</div>
                  <div class="stat-label">网站总数</div>
                </div>
              </div>
            </el-card>

            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon">
                  <el-icon size="40" color="#67c23a"><User /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ stats.users }}</div>
                  <div class="stat-label">用户总数</div>
                </div>
              </div>
            </el-card>

            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon">
                  <el-icon size="40" color="#e6a23c"><Document /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ stats.templates }}</div>
                  <div class="stat-label">模板总数</div>
                </div>
              </div>
            </el-card>

            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon">
                  <el-icon size="40" color="#f56c6c"><View /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ stats.visits }}</div>
                  <div class="stat-label">今日访问</div>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 快速操作 -->
          <el-card class="quick-actions">
            <template #header>
              <div class="card-header">
                <span>快速操作</span>
              </div>
            </template>
            <div class="action-buttons">
              <el-button type="primary" @click="createSite">
                <el-icon><Plus /></el-icon>
                创建网站
              </el-button>
              <el-button type="success" @click="openDIYEditor">
                <el-icon><Edit /></el-icon>
                DIY编辑器
              </el-button>
              <el-button type="warning" @click="manageTemplates">
                <el-icon><Document /></el-icon>
                管理模板
              </el-button>
              <el-button type="info" @click="viewStats">
                <el-icon><DataAnalysis /></el-icon>
                查看统计
              </el-button>
            </div>
          </el-card>

          <!-- 最近活动 -->
          <el-card class="recent-activity">
            <template #header>
              <div class="card-header">
                <span>最近活动</span>
              </div>
            </template>
            <el-timeline>
              <el-timeline-item
                v-for="activity in recentActivities"
                :key="activity.id"
                :timestamp="activity.time"
                :color="activity.color"
              >
                {{ activity.content }}
              </el-timeline-item>
            </el-timeline>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import {
  House, Monitor, Document, User, Edit, Setting,
  Plus, DataAnalysis, View
} from '@element-plus/icons-vue'

// 统计数据
const stats = reactive({
  sites: 156,
  users: 1024,
  templates: 48,
  visits: 2847
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    content: '用户张三创建了新网站"企业官网"',
    time: '2024-12-20 14:30',
    color: '#409eff'
  },
  {
    id: 2,
    content: '管理员上传了新模板"电商模板v2.0"',
    time: '2024-12-20 13:15',
    color: '#67c23a'
  },
  {
    id: 3,
    content: '用户李四修改了网站配置',
    time: '2024-12-20 12:45',
    color: '#e6a23c'
  },
  {
    id: 4,
    content: '系统自动备份数据库',
    time: '2024-12-20 10:00',
    color: '#909399'
  }
])

// 方法
const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('退出登录成功')
  })
}

const createSite = () => {
  ElMessage.info('跳转到创建网站页面')
}

const openDIYEditor = () => {
  ElMessage.info('打开DIY编辑器')
}

const manageTemplates = () => {
  ElMessage.info('跳转到模板管理页面')
}

const viewStats = () => {
  ElMessage.info('跳转到统计页面')
}
</script>

<style lang="scss">
// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: 'Inter', 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--el-text-color-primary);
  background-color: var(--el-bg-color);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
  width: 100%;
}

.app-container {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.welcome-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;

  h1 {
    font-size: 32px;
    color: #409eff;
    margin-bottom: 16px;
  }

  p {
    font-size: 16px;
    color: #666;
    margin-bottom: 24px;
  }
}

.test-btn {
  padding: 12px 24px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.test-btn:hover {
  background: #66b1ff;
}

// Element Plus 样式覆盖
.el-message {
  min-width: 300px;
  
  .el-message__content {
    font-size: 14px;
  }
}

.el-notification {
  .el-notification__title {
    font-size: 16px;
    font-weight: 600;
  }
  
  .el-notification__content {
    font-size: 14px;
    line-height: 1.6;
  }
}

.el-dialog {
  .el-dialog__header {
    padding: 20px 20px 10px;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }
  }
  
  .el-dialog__body {
    padding: 10px 20px 20px;
  }
}

.el-table {
  .el-table__header {
    th {
      background-color: var(--el-fill-color-light);
      font-weight: 600;
    }
  }
}

.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .el-card__header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--el-border-color-light);
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
  }
  
  .el-card__body {
    padding: 20px;
  }
}

// 自定义滚动条
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-fill-color-dark);
  border-radius: 3px;
  
  &:hover {
    background: var(--el-fill-color-darker);
  }
}

// 工具类
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.cursor-pointer {
  cursor: pointer;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

// 响应式工具类
@media (max-width: 768px) {
  .hidden-xs {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .visible-xs {
    display: none !important;
  }
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

// 管理后台布局样式
.admin-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);

  .header-left {
    .logo {
      font-size: 20px;
      font-weight: 600;
      color: #409eff;
      margin: 0;
    }
  }

  .header-right {
    .user-info {
      display: flex;
      align-items: center;
      cursor: pointer;

      .username {
        margin-left: 8px;
        font-size: 14px;
        color: #606266;
      }
    }
  }
}

.main-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sidebar {
  width: 200px;
  background: #304156;
  overflow-y: auto;

  .sidebar-menu {
    border: none;
    height: 100%;
    width: 100%;

    .el-menu-item,
    .el-sub-menu__title {
      height: 50px;
      line-height: 50px;
      border-bottom: none !important;

      &:hover {
        background-color: #263445 !important;
        color: #ffffff !important;
      }
    }

    .el-menu-item.is-active {
      background-color: #409eff !important;
      color: #ffffff !important;
      border-right: 3px solid #66b1ff;

      &::before {
        display: none;
      }
    }

    .el-sub-menu .el-menu-item {
      background-color: #263445;

      &:hover {
        background-color: #1f2d3d !important;
      }

      &.is-active {
        background-color: #409eff !important;
      }
    }
  }
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
  overflow: hidden;
}

.content-header {
  background: #fff;
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;

  h2 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 500;
    color: #303133;
  }
}

.content-body {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 24px;

  .stat-card {
    .stat-content {
      display: flex;
      align-items: center;

      .stat-icon {
        font-size: 40px;
        margin-right: 16px;
      }

      .stat-info {
        .stat-number {
          font-size: 28px;
          font-weight: 600;
          color: #303133;
          line-height: 1;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }
}

.quick-actions {
  margin-bottom: 24px;

  .action-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }
}

.recent-activity {
  .el-timeline {
    padding-left: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .sidebar {
    width: 60px;

    .sidebar-menu {
      .el-menu-item span,
      .el-sub-menu__title span {
        display: none;
      }
    }
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    .el-button {
      flex: 1;
      min-width: 120px;
    }
  }
}
</style>

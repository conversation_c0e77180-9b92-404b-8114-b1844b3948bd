<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * 轮播图模型
 */
class Banner extends Model
{
    // 设置表名
    protected $name = 'banners';
    
    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'title'       => 'string',
        'subtitle'    => 'string',
        'description' => 'text',
        'image'       => 'string',
        'link_url'    => 'string',
        'link_target' => 'string',
        'sort_order'  => 'int',
        'status'      => 'int',
        'created_at'  => 'datetime',
        'updated_at'  => 'datetime',
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 获取启用的轮播图
    public static function getActive($limit = 5)
    {
        return self::where('status', 1)
                   ->order('sort_order', 'asc')
                   ->order('id', 'desc')
                   ->limit($limit)
                   ->select();
    }
    
    // 获取轮播图状态文本
    public function getStatusTextAttr($value, $data)
    {
        $status = [0 => '禁用', 1 => '启用'];
        return $status[$data['status']] ?? '未知';
    }
    
    // 获取链接目标文本
    public function getLinkTargetTextAttr($value, $data)
    {
        $targets = ['_self' => '当前窗口', '_blank' => '新窗口'];
        return $targets[$data['link_target']] ?? '当前窗口';
    }
}

<?php
/**
 * 三只鱼网络科技 | 开发者：韩总 | 创建时间：2024-12-19
 * 文件描述：数据库备份恢复工具 - ThinkPHP6企业级应用
 * 技术栈：PHP 8.0+ + ThinkPHP6 + MySQL + Redis
 * 版权所有：三只鱼网络科技有限公司
 */

require_once __DIR__ . '/../vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

// 初始化ThinkPHP
$app = new \think\App();
$app->initialize();

class DatabaseBackup
{
    private $backupDir;
    private $dbConfig;
    
    public function __construct()
    {
        $this->backupDir = __DIR__ . '/backups';
        $this->dbConfig = Config::get('database.connections.mysql');
        
        // 确保备份目录存在
        if (!is_dir($this->backupDir)) {
            mkdir($this->backupDir, 0755, true);
        }
    }
    
    /**
     * 创建数据库备份
     */
    public function backup($options = [])
    {
        $includeData = $options['data'] ?? true;
        $compress = $options['compress'] ?? false;
        $tables = $options['tables'] ?? [];
        
        echo "=== 三只鱼网络科技 - 数据库备份工具 ===\n\n";
        
        try {
            $backupFile = $this->generateBackupFilename($compress);
            
            echo "开始备份数据库...\n";
            echo "目标文件: " . basename($backupFile) . "\n";
            
            // 获取要备份的表
            $tablesToBackup = empty($tables) ? $this->getAllTables() : $tables;
            echo "备份表数量: " . count($tablesToBackup) . "\n\n";
            
            // 创建备份
            $this->createBackup($backupFile, $tablesToBackup, $includeData);
            
            // 压缩备份文件
            if ($compress) {
                $this->compressBackup($backupFile);
            }
            
            // 验证备份
            $this->verifyBackup($backupFile);
            
            echo "\n🎉 数据库备份完成！\n";
            echo "备份文件: " . basename($backupFile) . "\n";
            echo "文件大小: " . $this->formatFileSize(filesize($backupFile)) . "\n";
            
            return $backupFile;
            
        } catch (Exception $e) {
            echo "❌ 备份失败: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 恢复数据库
     */
    public function restore($backupFile, $options = [])
    {
        $force = $options['force'] ?? false;
        
        echo "=== 三只鱼网络科技 - 数据库恢复工具 ===\n\n";
        
        try {
            // 检查备份文件
            if (!file_exists($backupFile)) {
                throw new Exception("备份文件不存在: {$backupFile}");
            }
            
            echo "恢复文件: " . basename($backupFile) . "\n";
            echo "文件大小: " . $this->formatFileSize(filesize($backupFile)) . "\n";
            
            // 确认恢复
            if (!$force && !$this->confirmRestore()) {
                echo "恢复已取消\n";
                return false;
            }
            
            // 解压备份文件（如果需要）
            $sqlFile = $this->decompressIfNeeded($backupFile);
            
            // 执行恢复
            $this->executeRestore($sqlFile);
            
            // 清理临时文件
            if ($sqlFile !== $backupFile) {
                unlink($sqlFile);
            }
            
            echo "\n🎉 数据库恢复完成！\n";
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ 恢复失败: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 列出所有备份文件
     */
    public function listBackups()
    {
        echo "=== 备份文件列表 ===\n\n";
        
        $backups = glob($this->backupDir . '/backup_*.sql*');
        
        if (empty($backups)) {
            echo "没有找到备份文件\n";
            return;
        }
        
        // 按时间排序
        usort($backups, function($a, $b) {
            return filemtime($b) - filemtime($a);
        });
        
        foreach ($backups as $backup) {
            $filename = basename($backup);
            $size = $this->formatFileSize(filesize($backup));
            $time = date('Y-m-d H:i:s', filemtime($backup));
            
            echo "📁 {$filename}\n";
            echo "   大小: {$size}\n";
            echo "   时间: {$time}\n\n";
        }
    }
    
    /**
     * 清理旧备份
     */
    public function cleanup($keepDays = 30)
    {
        echo "清理 {$keepDays} 天前的备份文件...\n";
        
        $backups = glob($this->backupDir . '/backup_*.sql*');
        $cutoffTime = time() - ($keepDays * 24 * 3600);
        $deletedCount = 0;
        
        foreach ($backups as $backup) {
            if (filemtime($backup) < $cutoffTime) {
                unlink($backup);
                echo "删除: " . basename($backup) . "\n";
                $deletedCount++;
            }
        }
        
        echo "清理完成，删除了 {$deletedCount} 个文件\n";
    }
    
    /**
     * 生成备份文件名
     */
    private function generateBackupFilename($compress = false)
    {
        $timestamp = date('Y-m-d_H-i-s');
        $extension = $compress ? '.sql.gz' : '.sql';
        
        return $this->backupDir . "/backup_{$timestamp}{$extension}";
    }
    
    /**
     * 获取所有表
     */
    private function getAllTables()
    {
        $tables = Db::query('SHOW TABLES');
        $dbName = $this->dbConfig['database'];
        $tableKey = "Tables_in_{$dbName}";
        
        return array_column($tables, $tableKey);
    }
    
    /**
     * 创建备份
     */
    private function createBackup($backupFile, $tables, $includeData)
    {
        $handle = fopen($backupFile, 'w');
        
        if (!$handle) {
            throw new Exception("无法创建备份文件: {$backupFile}");
        }
        
        // 写入头部信息
        $this->writeBackupHeader($handle);
        
        foreach ($tables as $table) {
            echo "备份表: {$table}...\n";
            
            // 备份表结构
            $this->backupTableStructure($handle, $table);
            
            // 备份表数据
            if ($includeData) {
                $this->backupTableData($handle, $table);
            }
        }
        
        fclose($handle);
    }
    
    /**
     * 写入备份头部
     */
    private function writeBackupHeader($handle)
    {
        $header = "-- 三只鱼网络科技数据库备份\n";
        $header .= "-- 备份时间: " . date('Y-m-d H:i:s') . "\n";
        $header .= "-- 数据库: " . $this->dbConfig['database'] . "\n";
        $header .= "-- 版本: " . Db::query('SELECT VERSION() as version')[0]['version'] . "\n\n";
        $header .= "SET FOREIGN_KEY_CHECKS=0;\n\n";
        
        fwrite($handle, $header);
    }
    
    /**
     * 备份表结构
     */
    private function backupTableStructure($handle, $table)
    {
        // 获取建表语句
        $createTable = Db::query("SHOW CREATE TABLE `{$table}`");
        $sql = $createTable[0]['Create Table'];
        
        fwrite($handle, "-- 表结构: {$table}\n");
        fwrite($handle, "DROP TABLE IF EXISTS `{$table}`;\n");
        fwrite($handle, $sql . ";\n\n");
    }
    
    /**
     * 备份表数据
     */
    private function backupTableData($handle, $table)
    {
        $count = Db::table($table)->count();
        
        if ($count === 0) {
            return;
        }
        
        fwrite($handle, "-- 表数据: {$table} ({$count} 条记录)\n");
        
        // 分批处理大表
        $batchSize = 1000;
        $offset = 0;
        
        while ($offset < $count) {
            $rows = Db::table($table)->limit($batchSize)->select()->toArray();
            
            if (empty($rows)) {
                break;
            }
            
            $this->writeInsertStatements($handle, $table, $rows);
            $offset += $batchSize;
        }
        
        fwrite($handle, "\n");
    }
    
    /**
     * 写入INSERT语句
     */
    private function writeInsertStatements($handle, $table, $rows)
    {
        if (empty($rows)) {
            return;
        }
        
        $columns = array_keys($rows[0]);
        $columnList = '`' . implode('`, `', $columns) . '`';
        
        fwrite($handle, "INSERT INTO `{$table}` ({$columnList}) VALUES\n");
        
        $values = [];
        foreach ($rows as $row) {
            $rowValues = [];
            foreach ($row as $value) {
                if ($value === null) {
                    $rowValues[] = 'NULL';
                } else {
                    $rowValues[] = "'" . addslashes($value) . "'";
                }
            }
            $values[] = '(' . implode(', ', $rowValues) . ')';
        }
        
        fwrite($handle, implode(",\n", $values) . ";\n");
    }
    
    /**
     * 压缩备份文件
     */
    private function compressBackup($backupFile)
    {
        if (!function_exists('gzencode')) {
            echo "⚠️  gzip扩展不可用，跳过压缩\n";
            return;
        }
        
        echo "压缩备份文件...\n";
        
        $data = file_get_contents($backupFile);
        $compressed = gzencode($data, 9);
        
        $compressedFile = $backupFile . '.gz';
        file_put_contents($compressedFile, $compressed);
        
        unlink($backupFile);
        
        echo "压缩完成，节省空间: " . 
             $this->formatFileSize(strlen($data) - strlen($compressed)) . "\n";
    }
    
    /**
     * 验证备份
     */
    private function verifyBackup($backupFile)
    {
        echo "验证备份文件...\n";
        
        if (!file_exists($backupFile)) {
            throw new Exception("备份文件不存在");
        }
        
        $size = filesize($backupFile);
        if ($size < 100) {
            throw new Exception("备份文件太小，可能损坏");
        }
        
        echo "✅ 备份文件验证通过\n";
    }
    
    /**
     * 确认恢复操作
     */
    private function confirmRestore()
    {
        echo "\n⚠️  此操作将覆盖现有数据库！\n";
        echo "是否继续恢复? (y/N): ";
        
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        return strtolower(trim($line)) === 'y';
    }
    
    /**
     * 解压文件（如果需要）
     */
    private function decompressIfNeeded($backupFile)
    {
        if (pathinfo($backupFile, PATHINFO_EXTENSION) !== 'gz') {
            return $backupFile;
        }
        
        echo "解压备份文件...\n";
        
        $compressed = file_get_contents($backupFile);
        $data = gzdecode($compressed);
        
        $tempFile = $this->backupDir . '/temp_restore.sql';
        file_put_contents($tempFile, $data);
        
        return $tempFile;
    }
    
    /**
     * 执行恢复
     */
    private function executeRestore($sqlFile)
    {
        echo "执行数据库恢复...\n";
        
        $sql = file_get_contents($sqlFile);
        $statements = explode(';', $sql);
        
        $successCount = 0;
        
        Db::startTrans();
        
        try {
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (empty($statement) || strpos($statement, '--') === 0) {
                    continue;
                }
                
                Db::execute($statement);
                $successCount++;
                
                if ($successCount % 100 === 0) {
                    echo "  已执行 {$successCount} 条语句...\n";
                }
            }
            
            Db::commit();
            echo "✅ 恢复完成，执行了 {$successCount} 条语句\n";
            
        } catch (Exception $e) {
            Db::rollback();
            throw new Exception("恢复失败: " . $e->getMessage());
        }
    }
    
    /**
     * 格式化文件大小
     */
    private function formatFileSize($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;
        
        while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
            $bytes /= 1024;
            $unitIndex++;
        }
        
        return round($bytes, 2) . ' ' . $units[$unitIndex];
    }
}

// 命令行处理
if (php_sapi_name() === 'cli') {
    $backup = new DatabaseBackup();
    
    $command = $argv[1] ?? 'help';
    
    switch ($command) {
        case 'backup':
            $options = [];
            if (in_array('--no-data', $argv)) {
                $options['data'] = false;
            }
            if (in_array('--compress', $argv)) {
                $options['compress'] = true;
            }
            $backup->backup($options);
            break;
            
        case 'restore':
            $file = $argv[2] ?? null;
            if (!$file) {
                echo "请指定备份文件\n";
                exit(1);
            }
            $options = [];
            if (in_array('--force', $argv)) {
                $options['force'] = true;
            }
            $backup->restore($file, $options);
            break;
            
        case 'list':
            $backup->listBackups();
            break;
            
        case 'cleanup':
            $days = 30;
            foreach ($argv as $arg) {
                if (strpos($arg, '--days=') === 0) {
                    $days = (int)substr($arg, 7);
                }
            }
            $backup->cleanup($days);
            break;
            
        default:
            echo "数据库备份工具使用说明:\n";
            echo "php database/backup.php backup [--no-data] [--compress]\n";
            echo "php database/backup.php restore <file> [--force]\n";
            echo "php database/backup.php list\n";
            echo "php database/backup.php cleanup [--days=30]\n";
            break;
    }
}

let componentCounter = 0;
let selectedComponent = null;

// 注意：背景样式和3D效果样式已移动到各组件文件中

// 组件模板 - 使用组件管理器
const componentTemplates = {};
// 初始化组件模板（等待组件管理器加载后填充）
function initializeComponentTemplates() {
    if (typeof ComponentManager !== 'undefined') {
        componentTemplates.background = ComponentManager.getTemplate('background');
        componentTemplates.navbar = ComponentManager.getTemplate('navbar');
        componentTemplates.hero = ComponentManager.getTemplate('hero');
        componentTemplates.section = ComponentManager.getTemplate('section');
        componentTemplates.card = ComponentManager.getTemplate('card');
        componentTemplates.footer = ComponentManager.getTemplate('footer');
        componentTemplates.textblock = ComponentManager.getTemplate('textblock');
        componentTemplates.stats = ComponentManager.getTemplate('stats');
        componentTemplates.team = ComponentManager.getTemplate('team');
        componentTemplates.testimonials = ComponentManager.getTemplate('testimonials');
        componentTemplates.contact = ComponentManager.getTemplate('contact');


    } else {
        console.warn('⚠️ 组件管理器未加载，将在1秒后重试');
        setTimeout(initializeComponentTemplates, 1000);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initializeComponentTemplates);

// 显示模板选择器
function showTemplateSelector() {
    // 清除当前选择
    selectedComponent = null;
    document.querySelectorAll('.component-block').forEach(block => {
        block.classList.remove('selected');
    });

    // 初始化模板选择器
    if (typeof TemplateSelector !== 'undefined') {
        TemplateSelector.init();
    } else {
        console.error('模板选择器未加载');
        alert('模板选择器加载失败，请刷新页面重试');
    }
}

// 全局模板函数 - 预览模板
function previewTemplate(templateId) {
    console.log('🔍 全局预览模板:', templateId);
    if (typeof TemplateSelector !== 'undefined') {
        TemplateSelector.previewTemplate(templateId);
    } else {
        console.error('❌ TemplateSelector 未定义');
        alert('模板选择器未加载');
    }
}





// 全局页面管理函数 - 显示页面
function showPage(pageId) {
    console.log('📄 全局显示页面:', pageId);
    if (typeof TemplateManager !== 'undefined') {
        TemplateManager.showPage(pageId);
    } else {
        console.error('❌ TemplateManager 未定义');
        alert('模板管理器未加载');
    }
}

// 全局页面管理函数 - 添加页面
function addPage() {
    console.log('➕ 全局添加页面');
    if (typeof TemplateManager !== 'undefined') {
        TemplateManager.addPage();
    } else {
        console.error('❌ TemplateManager 未定义');
        alert('模板管理器未加载');
    }
}


// 添加组件
function addComponent(type) {
    const canvas = document.getElementById('canvas');

    // 确保组件模板已初始化
    if (!componentTemplates[type]) {
        if (typeof ComponentManager !== 'undefined' && ComponentManager.isRegistered(type)) {
            componentTemplates[type] = ComponentManager.getTemplate(type);
        } else {
            console.error(`组件类型 ${type} 未找到`);
            return;
        }
    }

    const template = componentTemplates[type];
    if (!template) return;

    // 背景组件特殊处理
    if (type === 'background') {
        // 检查是否已有背景组件
        const existingBg = canvas.querySelector('[data-type="background"]');
        if (existingBg) {
            // 如果已有背景，直接选择它进行编辑
            selectComponent(existingBg.id);
            alert('背景组件已存在，请在右侧属性面板中编辑');
            return;
        }

        // 创建背景组件并设置为画布背景
        createBackgroundLayer();
        return;
    }

    // 清空提示文字
    if (canvas.children.length === 1 && canvas.children[0].style.textAlign === 'center') {
        canvas.innerHTML = '';
    }

    componentCounter++;
    const componentId = `component_${type}_${componentCounter}`;

    const componentBlock = document.createElement('div');
    componentBlock.className = 'component-block';
    componentBlock.id = componentId;
    componentBlock.dataset.type = type;
    
    componentBlock.innerHTML = `
        <div class="component-controls">
            <button class="control-btn" onclick="moveUp('${componentId}')" title="上移">上移</button>
            <button class="control-btn" onclick="moveDown('${componentId}')" title="下移">下移</button>
            <button class="control-btn" onclick="moveToTop('${componentId}')" title="置顶">置顶</button>
            <button class="control-btn" onclick="moveToBottom('${componentId}')" title="置底">置底</button>
            <button class="control-btn layer-control" onclick="setComponentLayer('${componentId}', 999)" title="最上层">最上层</button>
            <button class="control-btn layer-control" onclick="setComponentLayer('${componentId}', 1)" title="最下层">最下层</button>
            <button class="control-btn" onclick="deleteComponent('${componentId}')" title="删除">删除</button>
        </div>
        ${template.html}
    `;

    // 绑定点击事件
    componentBlock.addEventListener('click', (e) => {
        e.stopPropagation();
        selectComponent(componentId);
    });

    canvas.appendChild(componentBlock);

    // 如果是英雄区组件，立即应用所有默认样式
    if (type === 'hero') {
        const props = componentTemplates.hero.properties;
        if (typeof updateHeroDisplay === 'function') {
            updateHeroDisplay(componentBlock, props);
        }
    }

    // 如果是导航栏组件，立即应用样式
    if (type === 'navbar') {
        const props = componentTemplates.navbar.properties;
        if (typeof updateNavbarDisplay === 'function') {
            updateNavbarDisplay(componentBlock, props);
        }
    }

    // 如果是卡片组件，立即应用样式
    if (type === 'card') {
        const props = componentTemplates.card.properties;
        if (typeof updateCardDisplay === 'function') {
            updateCardDisplay(componentBlock, props);
        }
    }

    // 如果是区块组件，延迟初始化样式确保DOM完全渲染
    if (type === 'section') {
        if (typeof initializeSectionComponent === 'function') {
            setTimeout(() => {
                initializeSectionComponent(componentId);
            }, 50);
        }
    }

    // 如果是文本块组件，立即应用样式
    if (type === 'textblock') {
        // 为组件设置属性存储
        componentBlock.componentProperties = { ...componentTemplates.textblock.properties };

        // 确保初始化函数被调用（包含默认预设应用）
        if (typeof initializeTextblockComponent === 'function') {
            setTimeout(() => {
                initializeTextblockComponent(componentBlock);
            }, 50);
        }
    }

    // 如果是统计数字组件，立即应用样式
    if (type === 'stats') {
        // 为组件设置属性存储
        componentBlock._statsProperties = { ...componentTemplates.stats.properties };

        // 立即应用默认样式
        setTimeout(() => {
            updateStatsDisplay(componentBlock, componentBlock._statsProperties);
        }, 50);
    }

    // 如果是团队介绍组件，立即应用样式
    if (type === 'team') {
        // 为组件设置属性存储
        componentBlock._teamProperties = { ...componentTemplates.team.properties };

        // 立即应用默认样式
        setTimeout(() => {
            updateTeamDisplay(componentBlock, componentBlock._teamProperties);
        }, 50);
    }

    // 如果是客户评价组件，立即应用样式
    if (type === 'testimonials') {
        // 为组件设置属性存储
        componentBlock._testimonialsProperties = { ...componentTemplates.testimonials.properties };

        // 立即应用默认样式
        setTimeout(() => {
            updateTestimonialsDisplay(componentBlock, componentBlock._testimonialsProperties);
        }, 50);
    }

    // 如果是联系信息组件，立即应用样式
    if (type === 'contact') {
        // 为组件设置属性存储
        componentBlock._contactProperties = { ...componentTemplates.contact.properties };

        // 立即应用默认样式
        setTimeout(() => {
            updateContactDisplay(componentBlock, componentBlock._contactProperties);
        }, 50);
    }

    selectComponent(componentId);

    // 调整布局
    setTimeout(() => adjustComponentsLayout(), 100);
}

// 创建背景层（仅创建虚拟背景组件，不显示在编辑区）
function createBackgroundLayer() {
    // 不在编辑区创建实际的背景层，只创建虚拟的背景组件用于属性编辑
    // 背景效果只在预览页面中显示



    // 选择背景进行编辑
    selectBackgroundLayer();
}

// 选择背景层
function selectBackgroundLayer() {
    // 清除其他选择
    document.querySelectorAll('.component-block').forEach(block => {
        block.classList.remove('selected');
    });

    selectedComponent = 'background-layer';
    updateBackgroundProperties();
}

// 选择组件
function selectComponent(componentId) {
    // 清除之前的选择
    document.querySelectorAll('.component-block').forEach(block => {
        block.classList.remove('selected');
    });

    // 选择当前组件
    const component = document.getElementById(componentId);
    if (component) {
        component.classList.add('selected');
        selectedComponent = componentId;
        updatePropertiesPanel(component);
    }
}

// 更新属性面板
function updatePropertiesPanel(component) {
    if (!component) return;
    const type = component.dataset.type;
    const template = componentTemplates[type];
    const propertiesContent = document.getElementById('properties-content');

    if (!template) {
        propertiesContent.innerHTML = '<p style="color: #e53e3e;">组件模板未找到</p>';
        return;
    }

    let html = `<div class="property-group">
        <label class="property-label">组件类型</label>
        <input type="text" class="property-input" value="${template.name}" readonly>
    </div>`;

    // 使用组件管理器生成属性面板
    if (typeof ComponentManager !== 'undefined' && ComponentManager.isRegistered(type)) {
        html += ComponentManager.generateProperties(type, component);
    } else {
        html += '<p style="color: #e53e3e;">组件管理器未加载</p>';
    }

    propertiesContent.innerHTML = html;
}

// 更新背景属性面板（使用组件管理器）
function updateBackgroundProperties() {
    if (typeof ComponentManager !== 'undefined' && ComponentManager.isRegistered('background')) {
        const propertiesContent = document.getElementById('properties-content');
        const mockComponent = { id: 'background-layer', dataset: { type: 'background' } };

        let html = `
            <div class="property-group">
                <label class="property-label">组件类型</label>
                <input type="text" class="property-input" value="背景层" readonly>
            </div>
        `;

        html += ComponentManager.generateProperties('background', mockComponent);
        propertiesContent.innerHTML = html;
    } else {
        console.warn('背景组件管理器未加载');
    }
}

// 保留必要的兼容性函数
function updateBackgroundProperty(componentId, property, value) {
    if (componentTemplates.background && componentTemplates.background.properties) {
        componentTemplates.background.properties[property] = value;

        if (componentId === 'background-layer') {
            updateBackgroundProperties();
        }
    }
}

// 注意：组件显示更新功能已移动到各组件文件中

// 移动组件
function moveUp(componentId) {
    const component = document.getElementById(componentId);
    const prev = component.previousElementSibling;
    if (prev) {
        component.parentNode.insertBefore(component, prev);
        adjustComponentsLayout(); // 重新调整布局
    }
}

function moveDown(componentId) {
    const component = document.getElementById(componentId);
    const next = component.nextElementSibling;
    if (next) {
        component.parentNode.insertBefore(next, component);
        adjustComponentsLayout(); // 重新调整布局
    }
}

function moveToTop(componentId) {
    const component = document.getElementById(componentId);
    const canvas = document.getElementById('canvas');
    canvas.insertBefore(component, canvas.firstChild);
    adjustComponentsLayout(); // 重新调整布局
}

function moveToBottom(componentId) {
    const component = document.getElementById(componentId);
    const canvas = document.getElementById('canvas');
    canvas.appendChild(component);
    adjustComponentsLayout(); // 重新调整布局
}

// 设置组件层级
function setComponentLayer(componentId, zIndex) {
    const component = document.getElementById(componentId);
    if (!component) return;

    // 设置z-index样式
    component.style.zIndex = zIndex;

    // 如果是卡片组件，同时更新属性
    if (component.dataset.type === 'card' && typeof updateCardProperty === 'function') {
        updateCardProperty(componentId, 'zIndex', zIndex);
    }

    // 智能调整所有组件的位置适配
    adjustComponentsLayout();

    // 显示层级变化提示
    const layerText = zIndex === 999 ? '已置于上层' : '已置于下层';
    showLayerToast(componentId, layerText);
}

// 智能调整组件布局
function adjustComponentsLayout() {
    const canvas = document.getElementById('canvas');
    const components = Array.from(canvas.querySelectorAll('.component-block'));

    // 先重置所有组件的margin-top
    components.forEach(component => {
        component.style.marginTop = '0px';
    });

    // 计算每个组件应该的位置
    let accumulatedOffset = 0;

    components.forEach((component) => {
        const currentZIndex = parseInt(component.style.zIndex) || 1;

        if (currentZIndex === 999) {
            // 悬浮组件：不影响后续组件的位置计算
            // 但需要设置相对定位确保悬浮效果
            component.style.position = 'relative';

            // 为悬浮组件添加红色边框以便区分
            component.style.border = '2px solid #e53e3e';
            component.style.borderRadius = '4px';

            // 获取组件实际高度（包括margin和padding）
            const rect = component.getBoundingClientRect();
            const computedStyle = window.getComputedStyle(component);
            const marginTop = parseInt(computedStyle.marginTop) || 0;
            const marginBottom = parseInt(computedStyle.marginBottom) || 0;

            // 累计悬浮组件的高度到偏移量中
            accumulatedOffset += rect.height + marginTop + marginBottom;
        } else {
            // 普通组件：移除可能的悬浮样式
            component.style.border = '';
            component.style.borderRadius = '';

            // 普通组件：需要向上移动以填补前面悬浮组件留下的空隙
            if (accumulatedOffset > 0) {
                component.style.marginTop = `-${accumulatedOffset}px`;
                component.style.position = 'relative';

                // 重要：一旦有普通组件填补了空隙，就重置偏移量
                // 这样后续的普通组件就会正常排列，不会重叠
                accumulatedOffset = 0;
            }
        }
    });
}

// 显示层级变化提示
function showLayerToast(componentId, layerText) {
    const component = document.getElementById(componentId);
    if (!component) return;

    // 创建提示元素
    const toast = document.createElement('div');
    toast.textContent = layerText;
    toast.style.cssText = `
        position: absolute;
        top: -35px;
        left: 50%;
        transform: translateX(-50%);
        background: #4a5568;
        color: white;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 12px;
        z-index: 10000;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.3s ease;
        white-space: nowrap;
    `;

    // 添加到组件
    component.style.position = 'relative';
    component.appendChild(toast);

    // 显示动画
    setTimeout(() => {
        toast.style.opacity = '1';
    }, 10);

    // 自动消失
    setTimeout(() => {
        toast.style.opacity = '0';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 1500);
}



// 删除组件
function deleteComponent(componentId) {
    const component = document.getElementById(componentId);
    component.remove();

    // 显示页面信息面板
    showPageInfoPanel();

    selectedComponent = null;

    // 调整剩余组件的布局
    adjustComponentsLayout();

    // 如果没有组件了，显示提示
    const canvas = document.getElementById('canvas');
    if (canvas.children.length === 0) {
        canvas.innerHTML = `
            <div style="text-align: center; padding: 100px 20px; color: #a0aec0;">
                <h2>从左侧组件库选择组件开始构建页面</h2>
                <p>点击组件添加到编辑区，使用按钮进行排序</p>
            </div>
        `;
    }
}

// 查看HTML代码 - 自定义弹窗显示
function viewHtmlCode() {
    const canvas = document.getElementById('canvas');
    const components = canvas.querySelectorAll('.component-block');

    if (components.length === 0) {
        alert('请先添加一些组件');
        return;
    }

    // 获取完整的previewPage代码
    const html = getPreviewHTML();

    // 创建自定义弹窗
    showCodeModal(html);
}

// 获取预览页面的完整HTML代码
function getPreviewHTML(componentsParam = null) {
    let components;

    if (componentsParam) {
        components = componentsParam;
    } else {
        const canvas = document.getElementById('canvas');
        if (!canvas) {
            console.error('Canvas元素未找到');
            return '';
        }
        components = canvas.querySelectorAll('.component-block');
    }

    let html = '';
    html += '<!DOCTYPE html>\n';
    html += '<html lang="zh-CN">\n';
    html += '<head>\n';
    html += '    <meta charset="UTF-8">\n';
    html += '    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n';
    html += `    <title>${pageInfo.title}</title>\n`;
    html += `    <meta name="keywords" content="${pageInfo.keywords}">\n`;
    html += `    <meta name="description" content="${pageInfo.description}">\n`;
    html += `    <meta name="author" content="${pageInfo.author}">\n`;
    html += `    <meta name="contact" content="${pageInfo.contact}">\n`;
    html += `    <meta name="company" content="${pageInfo.company}">\n`;
    // 使用绝对路径确保CSS正确加载
    const baseUrl = window.location.origin;
    const cssPath = `${baseUrl}/diy/css/all.css`;
    html += `    <link rel="stylesheet" href="${cssPath}">\n`;
    // 使用绝对路径加载Lucide图标库
    const lucidePath = `${baseUrl}/assets/js/lucide.js`;
    html += `    <script src="${lucidePath}"></script>\n`;
    html += '    <style>\n';
    html += '        * { margin: 0; padding: 0; box-sizing: border-box; }\n';
    html += '        body {\n';
    html += '            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", sans-serif;\n';
    html += '            position: relative;\n';
    html += '            min-height: 100vh;\n';
    html += '            overflow-x: hidden;\n';
    html += '        }\n';
    html += '        .page-background {\n';
    html += '            position: fixed;\n';
    html += '            top: 0;\n';
    html += '            left: 0;\n';
    html += '            right: 0;\n';
    html += '            bottom: 0;\n';
    html += '            z-index: -1;\n';
    html += '            overflow: hidden;\n';
    html += '        }\n';
    html += '        .effect-container {\n';
    html += '            position: absolute;\n';
    html += '            top: 0;\n';
    html += '            left: 0;\n';
    html += '            right: 0;\n';
    html += '            bottom: 0;\n';
    html += '            pointer-events: none;\n';
    html += '            z-index: 1;\n';
    html += '        }\n';

    // 检查是否有英雄区组件，如果有则添加对应的悬停样式
    const heroComponent = Array.from(components).find(comp => comp.dataset.type === 'hero');
    if (heroComponent && typeof heroComponent !== 'undefined' && typeof componentTemplates.hero !== 'undefined') {
        const heroProps = componentTemplates.hero.properties;
        const hoverColor = heroProps.navHoverColor || '#ffffff';
        const rgbColor = hexToRgb(hoverColor);

        html += '        /* 英雄区悬停效果 */\n';
        html += '        .nav-item::before {\n';
        html += `            background: linear-gradient(90deg, transparent, rgba(${rgbColor}, 0.4), transparent) !important;\n`;
        html += '        }\n';
        html += '        .nav-item:hover {\n';
        html += `            border-color: ${hoverColor} !important;\n`;
        html += `            background: rgba(${rgbColor}, 0.1) !important;\n`;
        html += `            color: ${hoverColor} !important;\n`;
        html += '        }\n';
    }

    // 检查是否有导航栏组件，如果有则添加对应的悬停样式
    const navbarComponent = Array.from(components).find(comp => comp.dataset.type === 'navbar');
    if (navbarComponent && typeof navbarComponent !== 'undefined') {
        // 尝试从组件实例获取属性，如果没有则使用模板默认属性
        let navbarProps;
        if (navbarComponent._navbarProperties) {
            navbarProps = navbarComponent._navbarProperties;
        } else if (typeof componentTemplates.navbar !== 'undefined') {
            navbarProps = componentTemplates.navbar.properties;
        } else {
            navbarProps = { hoverColor: '#667eea' };
        }

        const hoverColor = navbarProps.hoverColor || '#667eea';
        const rgbColor = hexToRgb(hoverColor);

        html += '        /* 导航栏悬停效果 */\n';
        html += '        .navbar-item {\n';
        html += '            transition: all 0.2s ease !important;\n';
        html += '            border-radius: 6px !important;\n';
        html += '            border: 2px solid transparent !important;\n';
        html += '            position: relative !important;\n';
        html += '            overflow: hidden !important;\n';
        html += '        }\n';
        html += '        .navbar-item::before {\n';
        html += '            content: "";\n';
        html += '            position: absolute;\n';
        html += '            top: 0;\n';
        html += '            left: -100%;\n';
        html += '            width: 100%;\n';
        html += '            height: 100%;\n';
        html += `            background: linear-gradient(90deg, transparent, rgba(${rgbColor}, 0.4), transparent);\n`;
        html += '            transition: left 0.5s ease;\n';
        html += '        }\n';
        html += '        .navbar-item:hover {\n';
        html += `            color: ${hoverColor} !important;\n`;
        html += `            border: 2px solid ${hoverColor} !important;\n`;
        html += `            background: rgba(${rgbColor}, 0.1) !important;\n`;
        html += '        }\n';
        html += '        .navbar-item:hover::before {\n';
        html += '            left: 100%;\n';
        html += '        }\n';
        html += '        .navbar-btn {\n';
        html += '            transition: all 0.2s ease !important;\n';
        html += '            position: relative !important;\n';
        html += '            overflow: hidden !important;\n';
        html += '        }\n';
        html += '        .navbar-btn::before {\n';
        html += '            content: "";\n';
        html += '            position: absolute;\n';
        html += '            top: 0;\n';
        html += '            left: -100%;\n';
        html += '            width: 100%;\n';
        html += '            height: 100%;\n';
        html += '            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n';
        html += '            transition: left 0.6s ease;\n';
        html += '        }\n';
        html += '        .navbar-btn:hover {\n';
        html += '            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15) !important;\n';
        html += '            filter: brightness(1.05) !important;\n';
        html += '        }\n';
        html += '        .navbar-btn:hover::before {\n';
        html += '            left: 100%;\n';
        html += '        }\n';
        html += '        /* 下拉菜单悬停效果 */\n';
        html += '        .navbar-component .navbar-dropdown .dropdown-item:hover {\n';
        html += `            background: ${hoverColor} !important;\n`;
        html += '            color: white !important;\n';
        html += '            padding-left: 25px !important;\n';
        html += '        }\n';
        html += '        .navbar-component .navbar-dropdown .dropdown-item:hover::before {\n';
        html += '            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent) !important;\n';
        html += '        }\n';
    }

    // 检查是否有页脚组件，如果有则添加对应的悬停样式
    const footerComponent = Array.from(components).find(comp => comp.dataset.type === 'footer');
    if (footerComponent && typeof footerComponent !== 'undefined' && typeof componentTemplates.footer !== 'undefined') {
        const footerProps = componentTemplates.footer.properties;
        const linkColor = footerProps.linkColor || '#a0aec0';
        const linkHoverColor = footerProps.linkHoverColor || '#ffffff';
        const linkHoverRgb = hexToRgb(linkHoverColor);

        html += '        /* 页脚悬停效果 */\n';
        html += '        .footer-links a {\n';
        html += `            color: ${linkColor} !important;\n`;
        html += '        }\n';
        html += '        .footer-links a::before {\n';
        html += `            background: linear-gradient(90deg, transparent, rgba(${linkHoverRgb}, 0.3), transparent) !important;\n`;
        html += '        }\n';
        html += '        .footer-links a:hover {\n';
        html += `            color: ${linkHoverColor} !important;\n`;
        html += `            border: 2px solid ${linkHoverColor} !important;\n`;
        html += `            background: rgba(${linkHoverRgb}, 0.1) !important;\n`;
        html += '        }\n';
    }

    html += '    </style>\n';
    html += '</head>\n';
    html += '<body>\n';

    // 辅助函数：将十六进制颜色转换为RGB
    function hexToRgb(hex) {
        const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);
        return result ?
            parseInt(result[1], 16) + ',' + parseInt(result[2], 16) + ',' + parseInt(result[3], 16) :
            '255, 255, 255';
    }

    // 不再检查统计组件的背景设置，统计组件应该在自己的区域内处理背景
    // const strengthWitnessComponent = Array.from(components).find(comp => {
    //     if (comp.dataset.type === 'stats') {
    //         // 检查组件实例的属性，而不是全局模板属性
    //         const componentProps = comp._statsProperties || componentTemplates.stats?.properties;
    //         return componentProps?.styleTemplate === 'strength';
    //     }
    //     return false;
    // });



    let backgroundStyle = '';

    // 不再使用统计组件的背景设置应用到全局，统计组件应该在自己的区域内设置背景
    // 统计组件的背景应该在组件内部处理，不应该影响全局页面背景

    // 使用全局背景组件设置
    const props = componentTemplates.background?.properties || {
        bgValue: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        bgType: 'gradient',
        effect3D: 'none'
    };

    if (props.bgType === 'image') {
        backgroundStyle = `background-image: url('${props.bgValue}'); background-size: cover; background-position: center; background-repeat: no-repeat;`;
    } else {
        backgroundStyle = `background: ${props.bgValue};`;
    }

    html += '    <div class="page-background" style="' + backgroundStyle + '">\n';

    // 添加3D效果（现在总是使用全局背景设置）
    const bgProps = componentTemplates.background?.properties || { effect3D: 'none' };
    if (bgProps.effect3D !== 'none') {
        html += '        <div class="effect-container">\n';
        html += generate3DEffectHTML(bgProps.effect3D).replace(/</g, '            <');
        html += '        </div>\n';
    }
    html += '    </div>\n\n';

    // 添加所有组件
    if (components.length > 0) {
        components.forEach(component => {
            // 查找组件内容，支持多种类名模式
            let content = component.querySelector('[class$="-component"]');
            if (!content) {
                // 特殊处理卡片组件 - 获取完整的wrapper
                content = component.querySelector('.cards-wrapper');
            }
            if (!content) {
                // 特殊处理统计组件 - 获取完整的wrapper
                content = component.querySelector('.stats-wrapper');
            }
            if (!content) {
                // 特殊处理页脚组件 - 获取完整内容
                if (component.dataset.type === 'footer') {
                    // 优先获取footer-component容器，它包含完整的页脚结构
                    content = component.querySelector('.footer-component');
                    if (!content) {
                        content = component.querySelector('.footer-main-style1, .footer-content, [class*="footer"]');
                    }
                }
            }
            if (!content) {
                // 其他可能的容器类名
                content = component.querySelector('[class*="container"]');
            }

            if (content) {
                // 获取组件的完整HTML，包括所有属性和内容
                let componentHTML = content.outerHTML;

                // 特殊处理页脚组件：确保包含外层容器和样式类
                if (component.dataset.type === 'footer') {
                    // 检查是否已经是完整的footer-component容器
                    if (!content.classList.contains('footer-component')) {
                        // 检查组件是否已经有style1类名或相关元素
                        const hasStyle1 = content.classList.contains('style1') || 
                                         content.querySelector('.footer-main-style1') !== null ||
                                         content.querySelector('.footer-bottom-style1') !== null ||
                                         (component.footerProps && component.footerProps.style === 'style1');
                        
                        if (hasStyle1) {
                            // 为风格1页脚添加完整的容器结构
                            componentHTML = `<div class="footer-component style1">${componentHTML}</div>`;
                        } else {
                            // 为默认风格页脚添加容器
                            componentHTML = `<div class="footer-component">${componentHTML}</div>`;
                        }
                    } else {
                        // 如果已经是footer-component容器，检查风格状态是否正确
                        const hasStyle1 = content.classList.contains('style1') || 
                                         content.querySelector('.footer-main-style1') !== null ||
                                         content.querySelector('.footer-bottom-style1') !== null ||
                                         (component.footerProps && component.footerProps.style === 'style1');
                        
                        // 确保容器的style1类名与内容匹配
                        if (hasStyle1 && !content.classList.contains('style1')) {
                            componentHTML = componentHTML.replace('class="footer-component"', 'class="footer-component style1"');
                        } else if (!hasStyle1 && content.classList.contains('style1')) {
                            componentHTML = componentHTML.replace('class="footer-component style1"', 'class="footer-component"');
                        }
                    }
                }

                // 修复图片路径：将相对路径转换为绝对路径
                const baseUrl = window.location.origin;
                componentHTML = componentHTML.replace(/src="\.\.\/assets\//g, `src="${baseUrl}/assets/`);



                // 检查组件的层级和定位
                const componentTransform = component.style.transform;
                const componentZIndex = parseInt(component.style.zIndex) || 1;

                if (componentZIndex === 999) {
                    // 悬浮组件：使用absolute定位，不占用文档流空间
                    let wrapperStyle = 'position: absolute; width: 100%; z-index: 999;';
                    if (componentTransform) {
                        wrapperStyle += ` transform: ${componentTransform};`;
                    }
                    componentHTML = `<div style="${wrapperStyle}">\n        ${componentHTML}\n    </div>`;
                } else if (componentTransform) {
                    // 普通组件有transform：使用relative定位
                    let wrapperStyle = 'position: relative;';
                    if (componentTransform) wrapperStyle += ` transform: ${componentTransform};`;
                    if (componentZIndex > 1) wrapperStyle += ` z-index: ${componentZIndex};`;

                    componentHTML = `<div style="${wrapperStyle}">\n        ${componentHTML}\n    </div>`;
                }

                html += '    ' + componentHTML + '\n\n';
            }
        });
    } else {
        html += '    <!-- 暂无组件内容 -->\n';
        html += '    <div style="padding: 100px; text-align: center; color: #666;">\n';
        html += '        <h2>页面内容</h2>\n';
        html += '        <p>请在页面构建器中添加组件</p>\n';
        html += '    </div>\n\n';
    }

    // 添加Lucide图标初始化脚本
    html += '    <script>\n';
    html += '        document.addEventListener("DOMContentLoaded", function() {\n';
    html += '            if (typeof lucide !== "undefined") {\n';
    html += '                lucide.createIcons();\n';
    html += '            }\n';
    html += '        });\n';
    html += '    </script>\n';
    html += '</body>\n';
    html += '</html>';

    return html;
}

// 显示代码弹窗
function showCodeModal(htmlCode) {
    // 创建弹窗遮罩
    const modal = document.createElement('div');
    modal.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; display: flex; align-items: center; justify-content: center;';

    // 创建弹窗内容
    const modalContent = document.createElement('div');
    modalContent.style.cssText = 'background: #1e1e1e; color: #d4d4d4; width: 95%; max-width: 1200px; height: 90%; max-height: 800px; border-radius: 8px; display: flex; flex-direction: column; font-family: "Consolas", "Monaco", "Courier New", monospace;';

    // 弹窗头部
    const header = document.createElement('div');
    header.style.cssText = 'background: #2d2d30; padding: 15px 20px; border-bottom: 1px solid #3e3e42; display: flex; justify-content: space-between; align-items: center; border-radius: 8px 8px 0 0;';
    header.innerHTML = '<div style="font-size: 18px; font-weight: bold; color: #ffffff;">📄 HTML代码查看</div><button onclick="closeCodeModal()" style="background: #e53e3e; color: white; border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer;">✕ 关闭</button>';

    // 代码内容区域
    const codeArea = document.createElement('div');
    codeArea.style.cssText = 'flex: 1; padding: 20px; overflow: auto; white-space: pre-wrap; font-size: 14px; line-height: 1.6; background: #1e1e1e;';
    codeArea.textContent = htmlCode;

    // 底部操作按钮
    const footer = document.createElement('div');
    footer.style.cssText = 'padding: 15px 20px; background: #2d2d30; border-top: 1px solid #3e3e42; display: flex; gap: 10px; border-radius: 0 0 8px 8px;';
    footer.innerHTML = '<button onclick="copyCodeToClipboard()" style="background: #0e639c; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">📋 复制代码</button><button onclick="downloadCodeAsFile()" style="background: #0e639c; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">💾 下载HTML</button>';

    // 组装弹窗
    modalContent.appendChild(header);
    modalContent.appendChild(codeArea);
    modalContent.appendChild(footer);
    modal.appendChild(modalContent);

    // 添加到页面
    document.body.appendChild(modal);

    // 存储代码供其他函数使用
    window.currentViewCode = htmlCode;
    window.currentModal = modal;

    // 点击遮罩关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeCodeModal();
        }
    });
}

// 关闭代码弹窗
function closeCodeModal() {
    if (window.currentModal) {
        document.body.removeChild(window.currentModal);
        window.currentModal = null;
        window.currentViewCode = null;
    }
}

// 复制代码到剪贴板
function copyCodeToClipboard() {
    if (window.currentViewCode) {
        navigator.clipboard.writeText(window.currentViewCode).then(() => {
            alert('代码已复制到剪贴板！');
        }).catch(() => {
            // 回退方案：创建临时文本区域
            const textArea = document.createElement('textarea');
            textArea.value = window.currentViewCode;
            textArea.style.position = 'fixed';
            textArea.style.opacity = '0';
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                alert('代码已复制到剪贴板！');
            } catch {
                alert('复制失败，请手动复制代码');
            }
            document.body.removeChild(textArea);
        });
    }
}

// 下载代码为文件
function downloadCodeAsFile() {
    if (window.currentViewCode) {
        const blob = new Blob([window.currentViewCode], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'page-' + new Date().getTime() + '.html';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// 预览页面
function previewPage() {
    const canvas = document.getElementById('canvas');
    const components = canvas.querySelectorAll('.component-block');

    if (components.length === 0) {
        alert('请先添加一些组件');
        return;
    }

    // 使用统一的HTML生成函数，传递组件数据
    let html = getPreviewHTML(components);
    html = html.replace('页面预览', '内部页面预览');

    const previewWindow = window.open('', '_blank', 'fullscreen=yes,scrollbars=yes');
    previewWindow.document.write(html);
    previewWindow.document.close();
}

// 生成3D效果HTML（简化版）
function generate3DEffectHTML(effectType) {
    const effects = {
        bubbles: { count: 20, class: 'bubble', sizeRange: [20, 80] },
        particles: { count: 50, class: 'particle', sizeRange: [2, 6] },
        geometric: { count: 8, class: 'geometric', sizeRange: [40, 120] },
        stars: { count: 100, class: 'bg-star', sizeRange: [1, 4] },
        waves: { count: 6, class: 'wave', sizeRange: [50, 150] }
    };

    const effect = effects[effectType];
    if (!effect) return '';

    let html = '';
    for (let i = 0; i < effect.count; i++) {
        const size = Math.random() * (effect.sizeRange[1] - effect.sizeRange[0]) + effect.sizeRange[0];
        const left = Math.random() * 90;
        const top = Math.random() * 90;
        const delay = Math.random() * 6;

        let style = `left: ${left}%; top: ${top}%; animation-delay: ${delay}s;`;

        if (effectType !== 'particles') {
            style += ` width: ${size}px; height: ${size}px;`;
        }

        if (effectType === 'geometric' && Math.random() > 0.5) {
            style += ' border-radius: 50%;';
        }

        html += `<div class="${effect.class}" style="${style}"></div>`;
    }

    return html;
}

// 保存页面
function savePage() {
    const components = document.querySelectorAll('.component-block');
    if (components.length === 0) return alert('请先添加一些组件');

    const pageData = {
        components: Array.from(components).map(comp => ({
            id: comp.id,
            type: comp.dataset.type,
            properties: componentTemplates[comp.dataset.type].properties
        })),
        timestamp: new Date().toISOString()
    };

    localStorage.setItem('savedPage', JSON.stringify(pageData));
    alert('页面已保存到本地存储');
}

// 页面基本信息
const pageInfo = {
    title: '我的模板'
};

// 显示页面基本信息属性面板
function showPageInfoPanel() {
    const propertiesContent = document.getElementById('properties-content');

    // 如果属性面板内容区域不存在（比如在模板管理模式下），则不执行
    if (!propertiesContent) {
        console.log('⚠️ 属性面板内容区域不存在，跳过显示页面信息面板');
        return;
    }

    let html = `
        <!-- 基本信息 -->
        <div class="property-section">
            <h4 class="section-title">页面基本信息</h4>

            <div class="property-group page-info-group">
                <label class="property-label">模板标题</label>
                <div class="input-group">
                    <input type="text" class="property-input" value="${pageInfo.title}"
                           onchange="updatePageInfo('title', this.value)" placeholder="请输入模板标题">
                </div>
            </div>
        </div>

        <!-- 操作提示 -->
        <div class="property-section">
            <h4 class="section-title">操作提示</h4>
            <div class="page-info-tips">
                <p>💡 从左侧组件库选择组件开始构建页面</p>
                <p>🎨 点击组件可以编辑其属性和样式</p>
                <p>📱 支持响应式设计，自动适配移动设备</p>
                <p>🚀 模板标题将作为页面的主要标识</p>
            </div>
        </div>
    `;

    propertiesContent.innerHTML = html;
}

// 更新页面信息
function updatePageInfo(property, value) {
    pageInfo[property] = value;

    // 同时更新全局window对象中的pageInfo
    if (window.pageInfo) {
        window.pageInfo[property] = value;
    }
}

// 更新SEO预览
function updateSEOPreview() {
    // 由于只保留标题字段，SEO预览功能已简化
}

// 页面初始化
document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('canvas');

    // 初始化Lucide图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // 显示页面基本信息面板
    showPageInfoPanel();

    // 点击空白区域取消选择
    canvas.addEventListener('click', (e) => {
        if (e.target.id === 'canvas' || e.target.closest('.canvas')) {
            document.querySelectorAll('.component-block').forEach(block => block.classList.remove('selected'));
            selectedComponent = null;
            // 显示页面信息面板
            showPageInfoPanel();
        }
    });

    // 注意：拖拽功能已移除，使用按钮进行组件排序


});

<!--
  三只鱼网络科技 | 韩总 | 2024-12-20
  QiyeDIY企业建站系统 - 页面组件渲染器
-->

<template>
  <div class="page-renderer" :style="pageStyles">
    <ComponentRenderer
      v-for="component in components"
      :key="component.id"
      :component="component"
      :page-config="pageConfig"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { DiyComponent, PageConfig } from '~/types/diy'

interface Props {
  components: DiyComponent[]
  pageConfig?: PageConfig
}

const props = withDefaults(defineProps<Props>(), {
  pageConfig: () => ({})
})

// 计算页面样式
const pageStyles = computed(() => {
  const config = props.pageConfig || {}
  const styles: Record<string, any> = {}
  
  // 页面背景
  if (config.backgroundColor) {
    styles.backgroundColor = config.backgroundColor
  }
  
  if (config.backgroundImage) {
    styles.backgroundImage = `url(${config.backgroundImage})`
    styles.backgroundSize = config.backgroundSize || 'cover'
    styles.backgroundPosition = config.backgroundPosition || 'center'
    styles.backgroundRepeat = config.backgroundRepeat || 'no-repeat'
  }
  
  // 页面间距
  if (config.padding) {
    styles.padding = config.padding
  }
  
  // 最小高度
  if (config.minHeight) {
    styles.minHeight = config.minHeight
  }
  
  return styles
})
</script>

<style lang="scss" scoped>
.page-renderer {
  width: 100%;
  min-height: 100px;
}
</style>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品管理 - 后台管理系统</title>

    <!-- CSS -->
    {include file="admin/common/css"}
    <link rel="stylesheet" href="/assets/css/admin/news.css">
    <link rel="stylesheet" href="/assets/css/image-uploader.css">
    <!-- CKEditor 5 CSS -->
    <link rel="stylesheet" href="/assets/css/ckeditor.css">

    <!-- 图标选择器样式 -->
    <style>
        /* 图标选择器容器 */
        .icon-selector-container {
            margin-top: 10px;
        }

        .icon-input-group {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 15px;
        }

        .icon-input-group .form-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(120, 119, 198, 0.3);
            color: #fff;
        }

        .btn-icon-selector {
            background: linear-gradient(135deg,
                rgba(120, 119, 198, 0.8) 0%,
                rgba(255, 119, 198, 0.6) 50%,
                rgba(120, 219, 255, 0.7) 100%);
            border: 1px solid rgba(120, 119, 198, 0.6);
            color: #ffffff;
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
        }

        .btn-icon-selector:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(120, 119, 198, 0.4);
        }

        .icon-preview {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(120, 119, 198, 0.3);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin-bottom: 10px;
        }

        .preview-icon {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            color: #fff;
        }

        .preview-icon i {
            font-size: 32px;
            color: rgba(120, 219, 255, 0.9);
        }

        .preview-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            color: rgba(255, 255, 255, 0.5);
        }

        .preview-placeholder i {
            font-size: 32px;
            color: rgba(255, 255, 255, 0.3);
        }

        /* 图标选择器弹窗 */
        .icon-selector-modal {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background: rgba(0, 0, 0, 0.85) !important;
            z-index: 99999 !important;
            display: none !important;
            align-items: center !important;
            justify-content: center !important;
            backdrop-filter: blur(5px) !important;
        }

        .icon-selector-modal.show {
            display: flex !important;
        }

        .icon-selector-content {
            background: linear-gradient(135deg, rgba(15, 15, 15, 0.98), rgba(25, 25, 35, 0.98)) !important;
            border: 1px solid rgba(120, 119, 198, 0.5) !important;
            border-radius: 12px !important;
            width: 90% !important;
            max-width: 900px !important;
            max-height: 85vh !important;
            overflow: hidden !important;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8) !important;
            position: relative !important;
            margin: auto !important;
        }

        .icon-selector-header {
            padding: 20px;
            border-bottom: 1px solid rgba(120, 119, 198, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .icon-selector-title {
            color: #fff;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .icon-selector-close {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .icon-selector-close:hover {
            color: #fff;
            background: rgba(255, 255, 255, 0.1);
        }

        .icon-selector-body {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .icon-search {
            width: 100%;
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(120, 119, 198, 0.3);
            border-radius: 8px;
            color: #fff;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .icon-search::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 10px;
        }

        .icon-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(120, 119, 198, 0.3);
            border-radius: 8px;
            padding: 15px 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: rgba(255, 255, 255, 0.8);
        }

        .icon-item:hover {
            background: rgba(120, 119, 198, 0.2);
            border-color: rgba(120, 119, 198, 0.6);
            transform: translateY(-2px);
        }

        .icon-item.selected {
            background: rgba(120, 119, 198, 0.3);
            border-color: rgba(120, 219, 255, 0.8);
            color: #fff;
        }

        .icon-item i {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }

        .icon-item span {
            font-size: 10px;
            display: block;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    {include file="admin/common/header"}
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            {include file="admin/common/sidebar"}

            <!-- 主要内容 -->
            <main class="main-content">
                <!-- 内容头部 -->
                <div class="content-header">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-box"></i> 产品管理
                    </h1>
                </div>

                <!-- 引用统一消息组件 -->
                {include file="admin/common/message"}

                <!-- 页面内容区域 -->
                <div class="content-body">
                    <div class="news-container">

                    {if condition="$action == 'list'"}
                        <!-- 产品管理选项卡视图 -->

                        <div class="list-header">
                            <div class="list-header-content">
                                <div class="list-title-section">
                                    <div class="list-icon">
                                        <i class="fas fa-box"></i>
                                    </div>
                                    <div>
                                        <h1 class="list-title">产品中心</h1>
                                        <p class="list-subtitle">管理企业产品信息和分类</p>
                                    </div>
                                </div>
                                <div class="header-actions">
                                    {if condition="$tab == 'products'"}
                                        <a href="/admin/products?action=add" class="btn-add-custom">
                                            <i class="fas fa-plus"></i>
                                            <span>添加产品</span>
                                        </a>
                                    {elseif condition="$tab == 'categories'"}
                                        <a href="/admin/products/add-category" class="btn-add-custom">
                                            <i class="fas fa-plus"></i>
                                            <span>添加分类</span>
                                        </a>
                                    {/if}
                                </div>
                            </div>
                        </div>

                        <!-- 选项卡导航 -->
                        <div class="tabs-container">
                            <div class="tabs-nav">
                                <a href="/admin/products?tab=products" class="tab-btn {$tab == 'products' ? 'active' : ''}">
                                    <i class="fas fa-box"></i>
                                    <span>产品列表</span>
                                </a>
                                <a href="/admin/products?tab=categories" class="tab-btn {$tab == 'categories' ? 'active' : ''}">
                                    <i class="fas fa-tags"></i>
                                    <span>分类管理</span>
                                </a>
                            </div>
                        </div>

                        <div class="list-body">

                            {if condition="$tab == 'products'"}
                                <!-- 产品列表 -->
                                {if condition="$productsList && count($productsList) > 0"}
                                    <div class="news-list">
                                        {volist name="productsList" id="product"}
                                        <div class="news-item">
                                            <div class="news-content-wrapper">
                                                <div class="news-thumbnail">
                                                    {if condition="$product.image"}
                                                        <img src="{$product.image}" alt="{$product.name}" class="news-thumb-img"
                                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                                        <div class="news-thumb-placeholder" style="display: none;">
                                                            <i class="fas fa-box"></i>
                                                        </div>
                                                    {else /}
                                                        <div class="news-thumb-placeholder">
                                                            <i class="fas fa-box"></i>
                                                        </div>
                                                    {/if}
                                                </div>

                                                <div class="news-info">
                                                    <div class="news-header">
                                                        <h3 class="news-title">{$product.name}</h3>
                                                        <div class="news-badges">
                                                            {if condition="$product.is_featured"}
                                                                <span class="badge badge-featured">
                                                                    <i class="fas fa-star"></i>
                                                                    推荐
                                                                </span>
                                                            {/if}
                                                            {if condition="$product.is_hot"}
                                                                <span class="badge badge-category" style="background: linear-gradient(135deg, #ff4757, #ff3742); color: #ffffff; font-weight: 700; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4); box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);">
                                                                    <i class="fas fa-fire"></i>
                                                                    热门
                                                                </span>
                                                            {/if}
                                                            {if condition="$product.is_new"}
                                                                <span class="badge badge-category" style="background: linear-gradient(135deg, #2ed573, #1dd1a1); color: #ffffff; font-weight: 700; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4); box-shadow: 0 2px 8px rgba(46, 213, 115, 0.4);">
                                                                    <i class="fas fa-certificate"></i>
                                                                    新品
                                                                </span>
                                                            {/if}
                                                            <span class="badge badge-category">
                                                                <i class="fas fa-tag"></i>
                                                                {$product.category.name ?? '未分类'}
                                                            </span>
                                                        </div>
                                                    </div>

                                                    <div class="news-meta">
                                                        {if condition="$product.price"}
                                                            <div class="meta-item">
                                                                <i class="fas fa-yen-sign"></i>
                                                                <span>{$product.price}</span>
                                                            </div>
                                                        {/if}
                                                        {if condition="$product.sku"}
                                                            <div class="meta-item">
                                                                <i class="fas fa-barcode"></i>
                                                                <span>SKU: {$product.sku}</span>
                                                            </div>
                                                        {/if}
                                                        <div class="meta-item">
                                                            <i class="fas fa-calendar"></i>
                                                            <span>{$product.created_at|date='Y-m-d H:i'}</span>
                                                        </div>
                                                        <div class="meta-item">
                                                            <i class="fas fa-eye"></i>
                                                            <span>{$product.views ?? 0} 次浏览</span>
                                                        </div>
                                                        <div class="meta-item">
                                                            <i class="fas fa-cube"></i>
                                                            <span>{$product.stock_status_text}</span>
                                                        </div>
                                                    </div>

                                                    {if condition="$product.short_description"}
                                                        <div class="news-summary">
                                                            {$product.short_description|mb_substr=0,150,'UTF-8'}
                                                            {if condition="mb_strlen($product.short_description, 'UTF-8') > 150"}...{/if}
                                                        </div>
                                                    {/if}
                                                </div>

                                                <div class="news-actions">
                                                    <div class="status-toggle">
                                                        <label class="switch">
                                                            <input type="checkbox"
                                                                   {$product.status ? 'checked' : ''}
                                                                   onchange="toggleStatus('{$product.id}', this.checked ? 1 : 0)">
                                                            <span class="slider"></span>
                                                        </label>
                                                        <span class="status-label">{$product.status ? '已发布' : '草稿'}</span>
                                                    </div>

                                                    <div class="action-buttons">
                                                        <a href="/admin/products?action=edit&id={$product.id}" class="btn-action btn-edit" title="编辑">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <button onclick="deleteItem('{$product.id}', '{$product.name|htmlentities}', '/admin/products?action=delete&id={$product.id}')"
                                                                class="btn-action btn-delete" title="删除">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {/volist}
                                    </div>

                                    <!-- 分页组件（复用新闻管理的分页样式） -->
                                    {if condition="$productsList->hasPages()"}
                                        <div class="custom-pagination-container">
                                            <nav class="custom-pagination-nav">
                                                <div class="pagination-info">
                                                    <span class="pagination-text">
                                                        显示第 {$productsList->currentPage()} 页，共 {$productsList->lastPage()} 页，总计 {$productsList->total()} 条记录
                                                    </span>
                                                </div>
                                                <div class="pagination-buttons">
                                                    {if condition="$productsList->currentPage() > 1"}
                                                        <a href="/admin/products?action=list&tab={$tab}&page=1" class="pagination-btn pagination-first">
                                                            <i class="fas fa-angle-double-left"></i>
                                                            首页
                                                        </a>
                                                        <a href="/admin/products?action=list&tab={$tab}&page={$productsList->currentPage() - 1}" class="pagination-btn pagination-prev">
                                                            <i class="fas fa-angle-left"></i>
                                                            上一页
                                                        </a>
                                                    {else /}
                                                        <span class="pagination-btn pagination-first disabled">
                                                            <i class="fas fa-angle-double-left"></i>
                                                            首页
                                                        </span>
                                                        <span class="pagination-btn pagination-prev disabled">
                                                            <i class="fas fa-angle-left"></i>
                                                            上一页
                                                        </span>
                                                    {/if}
                                                    
                                                    {php}
                                                        $currentPage = $productsList->currentPage();
                                                        $lastPage = $productsList->lastPage();
                                                        
                                                        if ($lastPage <= 7) {
                                                            $startPage = 1;
                                                            $endPage = $lastPage;
                                                        } else {
                                                            $startPage = max(1, $currentPage - 2);
                                                            $endPage = min($lastPage, $currentPage + 2);
                                                        }
                                                    {/php}
                                                    
                                                    {if condition="$startPage > 1"}
                                                        <a href="/admin/products?action=list&tab={$tab}&page=1" class="pagination-btn pagination-number">1</a>
                                                        {if condition="$startPage > 2"}
                                                            <span class="pagination-ellipsis">...</span>
                                                        {/if}
                                                    {/if}
                                                    
                                                    {php}
                                                        $pageNumbers = range($startPage, $endPage);
                                                    {/php}
                                                    
                                                    {volist name="pageNumbers" id="pageNum"}
                                                        {if condition="$pageNum == $currentPage"}
                                                            <span class="pagination-btn pagination-number active">{$pageNum}</span>
                                                        {else /}
                                                            <a href="/admin/products?action=list&tab={$tab}&page={$pageNum}" class="pagination-btn pagination-number">{$pageNum}</a>
                                                        {/if}
                                                    {/volist}
                                                    
                                                    {if condition="$endPage < $lastPage"}
                                                        {if condition="$endPage < $lastPage - 1"}
                                                            <span class="pagination-ellipsis">...</span>
                                                        {/if}
                                                        <a href="/admin/products?action=list&tab={$tab}&page={$lastPage}" class="pagination-btn pagination-number">{$lastPage}</a>
                                                    {/if}
                                                    
                                                    {if condition="$productsList->currentPage() < $productsList->lastPage()"}
                                                        <a href="/admin/products?action=list&tab={$tab}&page={$productsList->currentPage() + 1}" class="pagination-btn pagination-next">
                                                            下一页
                                                            <i class="fas fa-angle-right"></i>
                                                        </a>
                                                        <a href="/admin/products?action=list&tab={$tab}&page={$productsList->lastPage()}" class="pagination-btn pagination-last">
                                                            末页
                                                            <i class="fas fa-angle-double-right"></i>
                                                        </a>
                                                    {else /}
                                                        <span class="pagination-btn pagination-next disabled">
                                                            下一页
                                                            <i class="fas fa-angle-right"></i>
                                                        </span>
                                                        <span class="pagination-btn pagination-last disabled">
                                                            末页
                                                            <i class="fas fa-angle-double-right"></i>
                                                        </span>
                                                    {/if}
                                                </div>
                                            </nav>
                                        </div>
                                    {/if}
                                {else /}
                                    <div class="empty-state">
                                        <div class="empty-icon">
                                            <i class="fas fa-box"></i>
                                        </div>
                                        <h3 class="empty-title">暂无产品</h3>
                                        <p class="empty-description">还没有添加任何产品，点击上方按钮开始添加第一个产品吧！</p>
                                    </div>
                                {/if}

                            {elseif condition="$tab == 'categories'"}
                                <!-- 分类列表 -->
                                {if condition="$categories && count($categories) > 0"}
                                    <div class="categories-list">
                                        {volist name="categories" id="category"}
                                        <div class="category-item">
                                            <div class="category-content-wrapper">
                                                <div class="category-icon">
                                                    {if condition="$category.icon"}
                                                        <i class="{$category.icon}"></i>
                                                    {else /}
                                                        <i class="fas fa-tag"></i>
                                                    {/if}
                                                </div>

                                                <div class="category-info">
                                                    <div class="category-header">
                                                        <h3 class="category-name">{$category.name}</h3>
                                                        <div class="category-badges">
                                                            <span class="badge badge-slug">
                                                                <i class="fas fa-link"></i>
                                                                {$category.slug}
                                                            </span>
                                                        </div>
                                                    </div>

                                                    <div class="category-meta">
                                                        <div class="meta-item">
                                                            <i class="fas fa-sort"></i>
                                                            <span>排序: {$category.sort_order}</span>
                                                        </div>
                                                        <div class="meta-item">
                                                            <i class="fas fa-calendar"></i>
                                                            <span>{$category.created_at|date='Y-m-d H:i'}</span>
                                                        </div>
                                                        <div class="meta-item">
                                                            <i class="fas fa-box"></i>
                                                            <span>{$category.product_count ?? 0} 个产品</span>
                                                        </div>
                                                    </div>

                                                    {if condition="$category.description"}
                                                        <div class="category-description">
                                                            {$category.description}
                                                        </div>
                                                    {/if}
                                                </div>

                                                <div class="category-actions">
                                                    <div class="action-buttons">
                                                        <a href="/admin/products?action=edit_category&id={$category.id}&tab=categories" class="btn-action btn-edit" title="编辑分类">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        {if condition="($category.product_count ?? 0) == 0"}
                                                            <button onclick="deleteItem('{$category.id}', '{$category.name|htmlentities}', '/admin/products?action=delete_category&id={$category.id}&tab=categories')"
                                                                    class="btn-action btn-delete" title="删除分类">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        {else /}
                                                            <button class="btn-action btn-delete disabled" title="该分类下有产品，无法删除" disabled>
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        {/if}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {/volist}
                                    </div>
                                {else /}
                                    <div class="empty-state">
                                        <div class="empty-icon">
                                            <i class="fas fa-tags"></i>
                                        </div>
                                        <h3 class="empty-title">暂无分类</h3>
                                        <p class="empty-description">还没有添加任何产品分类，点击上方按钮开始添加第一个分类吧！</p>
                                    </div>
                                {/if}
                            {/if}
                        </div>

                    {elseif condition="$action == 'add' OR $action == 'edit'"}
                        <!-- 添加/编辑产品表单 -->
                        <div class="form-header">
                            <div class="form-header-content">
                                <div class="form-title-section">
                                    <div class="form-icon">
                                        <i class="fas fa-{$action == 'add' ? 'plus' : 'edit'}"></i>
                                    </div>
                                    <div>
                                        <h1 class="form-title">{$action == 'add' ? '添加产品' : '编辑产品'}</h1>
                                        <p class="form-subtitle">{$action == 'add' ? '创建新的产品信息' : '修改产品信息'}</p>
                                    </div>
                                </div>
                                <a href="/admin/products" class="btn-back">
                                    <i class="fas fa-arrow-left"></i>
                                    <span>返回列表</span>
                                </a>
                            </div>
                        </div>

                        <div class="form-body">

                            <form method="POST" action="/admin/products" enctype="multipart/form-data" class="news-form">
                                <input type="hidden" name="action" value="{$action}">
                                {if condition="$editData"}
                                    <input type="hidden" name="id" value="{$editData.id}">
                                {/if}

                                <div class="form-grid">
                                    <!-- 基本信息 -->
                                    <div class="form-section">
                                        <div class="section-header">
                                            <h3 class="section-title">
                                                <i class="fas fa-info-circle"></i>
                                                基本信息
                                            </h3>
                                        </div>

                                        <div class="form-row" style="margin-top: 20px;">
                                            <div class="form-group">
                                                <label for="name" class="form-label required">
                                                    <i class="fas fa-box"></i>
                                                    产品名称
                                                </label>
                                                <input type="text" class="form-input" id="name" name="name"
                                                       value="{$editData.name|default=''}"
                                                       placeholder="请输入产品名称" required>
                                            </div>

                                            <div class="form-group">
                                                <label for="slug" class="form-label">
                                                    <i class="fas fa-link"></i>
                                                    URL别名
                                                </label>
                                                <input type="text" class="form-input" id="slug" name="slug"
                                                       value="{$editData.slug|default=''}"
                                                       placeholder="自动生成或手动输入">
                                            </div>
                                        </div>

                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="category_id" class="form-label">
                                                    <i class="fas fa-tag"></i>
                                                    产品分类
                                                </label>
                                                <select class="form-select" id="category_id" name="category_id">
                                                    <option value="1">默认分类</option>
                                                    {volist name="allCategories" id="category"}
                                                        <option value="{$category.id}"
                                                                {if condition="$editData && $editData.category_id == $category.id"}selected{/if}>
                                                            {$category.name}
                                                        </option>
                                                    {/volist}
                                                </select>
                                            </div>

                                            <div class="form-group">
                                                <label for="sku" class="form-label">
                                                    <i class="fas fa-barcode"></i>
                                                    产品SKU
                                                </label>
                                                <input type="text" class="form-input" id="sku" name="sku"
                                                       value="{$editData.sku|default=''}"
                                                       placeholder="请输入产品SKU">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="short_description" class="form-label">
                                                <i class="fas fa-align-left"></i>
                                                产品简介
                                            </label>
                                            <textarea class="form-textarea" id="short_description" name="short_description" rows="3"
                                                      placeholder="请输入产品简介，用于列表页显示">{$editData.short_description|default=''}</textarea>
                                            <div class="form-help">建议长度在100-200字之间，用于列表页和搜索结果显示</div>
                                        </div>

                                        <!-- 价格和库存 -->
                                        <div class="form-row three-columns" style="margin-top: 20px;">
                                            <div class="form-group">
                                                <label for="price" class="form-label">
                                                    <i class="fas fa-yen-sign"></i>
                                                    销售价格
                                                </label>
                                                <input type="text" class="form-input" id="price" name="price"
                                                       value="{$editData.price|default=''}"
                                                       placeholder="例如：999元 或 1000-2000元">
                                            </div>

                                            <div class="form-group">
                                                <label for="original_price" class="form-label">
                                                    <i class="fas fa-tag"></i>
                                                    原价
                                                </label>
                                                <input type="text" class="form-input" id="original_price" name="original_price"
                                                       value="{$editData.original_price|default=''}"
                                                       placeholder="例如：1299元">
                                            </div>

                                            <div class="form-group">
                                                <label for="stock_status" class="form-label">
                                                    <i class="fas fa-cube"></i>
                                                    库存状态
                                                </label>
                                                <select class="form-select" id="stock_status" name="stock_status">
                                                    <option value="in_stock" {if condition="!$editData || $editData.stock_status == 'in_stock'"}selected{/if}>现货</option>
                                                    <option value="out_of_stock" {if condition="$editData && $editData.stock_status == 'out_of_stock'"}selected{/if}>缺货</option>
                                                    <option value="pre_order" {if condition="$editData && $editData.stock_status == 'pre_order'"}selected{/if}>预订</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="description" class="form-label required">
                                                <i class="fas fa-file-text"></i>
                                                产品详情
                                            </label>
                                            <!-- CKEditor 5编辑器 -->
                                            <div class="ck-editor-container">
                                                <textarea id="editor" name="description">{$editData.description|default=''}</textarea>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="features" class="form-label">
                                                <i class="fas fa-star"></i>
                                                产品特性
                                            </label>
                                            <textarea class="form-textarea" id="features" name="features" rows="4"
                                                      placeholder="每行一个特性，例如：&#10;高性能处理器&#10;超长续航&#10;防水防尘">{if condition="$editData && $editData.features_array && is_array($editData.features_array)"}{$editData.features_array|implode="\n"}{/if}</textarea>
                                            <div class="form-help">每行输入一个产品特性，用于产品详情页展示</div>
                                        </div>

                                        <div class="form-group">
                                            <label for="tags" class="form-label">
                                                <i class="fas fa-tags"></i>
                                                标签
                                            </label>
                                            <input type="text" class="form-input" id="tags" name="tags"
                                                   value="{$editData.tags|default=''}"
                                                   placeholder="多个标签用英文逗号分隔">
                                            <div class="form-help">用于产品分类和搜索，多个标签用英文逗号分隔</div>
                                        </div>
                                    </div>



                                    <!-- 产品图片和图标 -->
                                    <div class="form-section">
                                        <div class="section-header">
                                            <h3 class="section-title">
                                                <i class="fas fa-image"></i>
                                                产品图片和图标
                                            </h3>
                                        </div>

                                        <div class="form-row" style="margin-top: 20px;">
                                            <!-- 产品图片 -->
                                            <div class="form-group">
                                                <label class="form-label">
                                                    <i class="fas fa-upload"></i>
                                                    产品图片
                                                </label>

                                                <!-- 图片上传弹窗按钮 -->
                                                <div class="image-upload-section">
                                                    <button type="button" class="btn-upload-image" id="btnSelectProductImage">
                                                        <i class="fas fa-images"></i>
                                                        <span>选择图片</span>
                                                    </button>
                                                    <div class="upload-help">
                                                        <small>支持上传新图片或从图库中选择，推荐尺寸：800x600px</small>
                                                    </div>
                                                </div>

                                                <!-- 隐藏的表单字段存储选择的图片URL -->
                                                <input type="hidden" id="selectedImageUrl" name="image_url" value="{$editData.image|default=''}">

                                                <!-- 当前选择的图片预览 -->
                                                <div class="selected-image-preview" id="selectedImagePreview" {if condition='!$editData || !$editData.image'}style="display: none;"{/if}>
                                                    <label class="form-label">已选择的图片：</label>
                                                    <div class="image-preview-container">
                                                        <img src="{$editData.image|default=''}" alt="产品图片" class="preview-img" id="previewImg">
                                                        <div class="image-actions">
                                                            <button type="button" class="btn-change-image" onclick="changeProductImage()">
                                                                <i class="fas fa-edit"></i>
                                                                更换图片
                                                            </button>
                                                            <button type="button" class="btn-remove-image" onclick="removeProductImage()">
                                                                <i class="fas fa-trash"></i>
                                                                移除图片
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 产品图标 -->
                                            <div class="form-group">
                                                <label for="icon" class="form-label">
                                                    <i class="fas fa-star"></i>
                                                    图标选择
                                                </label>

                                                <!-- 图标选择器 -->
                                                <div class="icon-selector-container">
                                                    <div class="icon-input-group">
                                                        <input type="text" class="form-input" id="icon" name="icon"
                                                               value="{$editData.icon|default=''}"
                                                               placeholder="例如：fas fa-laptop"
                                                               readonly>
                                                        <button type="button" class="btn-icon-selector" id="btnSelectIcon">
                                                            <i class="fas fa-icons"></i>
                                                            选择图标
                                                        </button>
                                                    </div>

                                                    <!-- 图标预览 -->
                                                    <div class="icon-preview" id="iconPreview">
                                                        {if condition="$editData && $editData.icon"}
                                                            <div class="preview-icon">
                                                                <i class="{$editData.icon}"></i>
                                                                <span>当前图标</span>
                                                            </div>
                                                        {else/}
                                                            <div class="preview-placeholder">
                                                                <i class="fas fa-question-circle"></i>
                                                                <span>未选择图标</span>
                                                            </div>
                                                        {/if}
                                                    </div>

                                                    <div class="form-help">
                                                        选择FontAwesome图标代表此产品
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- SEO设置和发布设置 -->
                                    <div class="form-section">
                                        <div class="section-header">
                                            <h3 class="section-title">
                                                <i class="fas fa-cog"></i>
                                                SEO设置和发布设置
                                            </h3>
                                        </div>

                                        <!-- SEO设置 -->
                                        <div class="form-row" style="margin-top: 20px;">
                                            <div class="form-group">
                                                <label for="meta_title" class="form-label">
                                                    <i class="fas fa-heading"></i>
                                                    SEO标题
                                                </label>
                                                <input type="text" class="form-input" id="meta_title" name="meta_title"
                                                       value="{$editData.meta_title|default=''}"
                                                       placeholder="留空则使用产品名称">
                                                <div class="form-help">搜索引擎显示的标题，建议60字符以内</div>
                                            </div>

                                            <div class="form-group">
                                                <label for="sort_order" class="form-label">
                                                    <i class="fas fa-sort"></i>
                                                    排序权重
                                                </label>
                                                <input type="number" class="form-input" id="sort_order" name="sort_order"
                                                       value="{$editData.sort_order|default=0}"
                                                       placeholder="0" min="0">
                                                <div class="form-help">数值越大排序越靠前</div>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="meta_description" class="form-label">
                                                <i class="fas fa-align-left"></i>
                                                SEO描述
                                            </label>
                                            <textarea class="form-textarea" id="meta_description" name="meta_description" rows="3"
                                                      placeholder="留空则使用产品简介">{$editData.meta_description|default=''}</textarea>
                                            <div class="form-help">搜索引擎显示的描述，建议160字符以内</div>
                                        </div>

                                        <!-- 发布设置 -->
                                        <div class="form-row">
                                            <div class="checkbox-group">
                                                <label class="checkbox-label">
                                                    <input type="checkbox" name="status" value="1"
                                                           {if condition="!$editData || $editData.status"}checked{/if}>
                                                    <span class="checkbox-custom"></span>
                                                    <span class="checkbox-text">
                                                        <i class="fas fa-eye"></i>
                                                        立即发布
                                                    </span>
                                                </label>
                                            </div>

                                            <div class="checkbox-group">
                                                <label class="checkbox-label">
                                                    <input type="checkbox" name="is_featured" value="1"
                                                           {if condition="$editData && $editData.is_featured"}checked{/if}>
                                                    <span class="checkbox-custom"></span>
                                                    <span class="checkbox-text">
                                                        <i class="fas fa-star"></i>
                                                        推荐产品
                                                    </span>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="form-row">
                                            <div class="checkbox-group">
                                                <label class="checkbox-label">
                                                    <input type="checkbox" name="is_hot" value="1"
                                                           {if condition="$editData && $editData.is_hot"}checked{/if}>
                                                    <span class="checkbox-custom"></span>
                                                    <span class="checkbox-text">
                                                        <i class="fas fa-fire"></i>
                                                        热门产品
                                                    </span>
                                                </label>
                                            </div>

                                            <div class="checkbox-group">
                                                <label class="checkbox-label">
                                                    <input type="checkbox" name="is_new" value="1"
                                                           {if condition="$editData && $editData.is_new"}checked{/if}>
                                                    <span class="checkbox-custom"></span>
                                                    <span class="checkbox-text">
                                                        <i class="fas fa-certificate"></i>
                                                        新品标识
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 表单操作按钮 -->
                                <div class="form-actions">
                                    <button type="submit" class="btn-submit">
                                        <i class="fas fa-save"></i>
                                        <span>保存产品</span>
                                    </button>
                                    <a href="/admin/products" class="btn-cancel">
                                        <i class="fas fa-times"></i>
                                        <span>取消</span>
                                    </a>
                                </div>
                            </form>
                        </div>

                    {elseif condition="$action == 'add_category' OR $action == 'edit_category'"}
                        <!-- 添加/编辑分类表单 -->
                        <div class="form-header">
                            <div class="form-header-content">
                                <div class="form-title-section">
                                    <div class="form-icon">
                                        <i class="fas fa-{$action == 'add_category' ? 'plus' : 'edit'}"></i>
                                    </div>
                                    <div>
                                        <h1 class="form-title">{$action == 'add_category' ? '添加分类' : '编辑分类'}</h1>
                                        <p class="form-subtitle">{$action == 'add_category' ? '创建新的产品分类' : '修改分类信息'}</p>
                                    </div>
                                </div>
                                <a href="/admin/products?tab=categories" class="btn-back">
                                    <i class="fas fa-arrow-left"></i>
                                    <span>返回列表</span>
                                </a>
                            </div>
                        </div>

                        <div class="form-body">

                            <form method="POST" action="/admin/products/add-category" class="category-form">
                                <input type="hidden" name="action" value="{$action}">
                                {if condition="$editCategoryData"}
                                    <input type="hidden" name="id" value="{$editCategoryData.id}">
                                {/if}

                                <div class="form-grid">
                                    <!-- 基本信息 -->
                                    <div class="form-section">
                                        <div class="section-header">
                                            <h3 class="section-title">
                                                <i class="fas fa-info-circle"></i>
                                                基本信息
                                            </h3>
                                        </div>

                                        <div class="form-row" style="margin-top: 20px;">
                                            <div class="form-group">
                                                <label for="category_name" class="form-label required">
                                                    <i class="fas fa-tag"></i>
                                                    分类名称
                                                </label>
                                                <input type="text" class="form-input" id="category_name" name="category_name"
                                                       value="{$editCategoryData.name|default=''}"
                                                       placeholder="请输入分类名称" required>
                                            </div>

                                            <div class="form-group">
                                                <label for="category_slug" class="form-label">
                                                    <i class="fas fa-link"></i>
                                                    URL别名
                                                </label>
                                                <input type="text" class="form-input" id="category_slug" name="category_slug"
                                                       value="{$editCategoryData.slug|default=''}"
                                                       placeholder="留空自动生成">
                                            </div>
                                        </div>

                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="category_icon" class="form-label">
                                                    <i class="fas fa-icons"></i>
                                                    分类图标
                                                </label>
                                                <input type="text" class="form-input" id="category_icon" name="category_icon"
                                                       value="{$editCategoryData.icon|default=''}"
                                                       placeholder="例如：fas fa-laptop">
                                                <div class="form-help">FontAwesome图标类名，例如：fas fa-laptop</div>
                                            </div>

                                            <div class="form-group">
                                                <label for="parent_id" class="form-label">
                                                    <i class="fas fa-sitemap"></i>
                                                    父分类
                                                </label>
                                                <select class="form-select" id="parent_id" name="parent_id">
                                                    <option value="0">顶级分类</option>
                                                    {volist name="allCategories" id="category"}
                                                        {if condition="!$editCategoryData || $editCategoryData.id != $category.id"}
                                                            <option value="{$category.id}"
                                                                    {if condition="$editCategoryData && $editCategoryData.parent_id == $category.id"}selected{/if}>
                                                                {$category.name}
                                                            </option>
                                                        {/if}
                                                    {/volist}
                                                </select>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="category_description" class="form-label">
                                                <i class="fas fa-align-left"></i>
                                                分类描述
                                            </label>
                                            <textarea class="form-textarea" id="category_description" name="category_description" rows="4"
                                                      placeholder="请输入分类描述">{$editCategoryData.description|default=''}</textarea>
                                            <div class="form-help">简要描述该分类的用途和内容范围</div>
                                        </div>

                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="category_sort_order" class="form-label">
                                                    <i class="fas fa-sort"></i>
                                                    排序权重
                                                </label>
                                                <input type="number" class="form-input" id="category_sort_order" name="category_sort_order"
                                                       value="{$editCategoryData.sort_order|default=0}"
                                                       placeholder="0" min="0">
                                            </div>

                                            <div class="checkbox-group">
                                                <label class="checkbox-label">
                                                    <input type="checkbox" name="status" value="1"
                                                           {if condition="!$editCategoryData || $editCategoryData.status"}checked{/if}>
                                                    <span class="checkbox-custom"></span>
                                                    <span class="checkbox-text">
                                                        <i class="fas fa-eye"></i>
                                                        启用分类
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 表单操作按钮 -->
                                <div class="form-actions">
                                    <button type="submit" class="btn-submit">
                                        <i class="fas fa-save"></i>
                                        <span>保存分类</span>
                                    </button>
                                    <a href="/admin/products?tab=categories" class="btn-cancel">
                                        <i class="fas fa-times"></i>
                                        <span>取消</span>
                                    </a>
                                </div>
                            </form>
                        </div>
                    {/if}
                    </div>
                </div>


            </main>
        </div>
    </div>

    <!-- 图标选择器弹窗 -->
    <div class="icon-selector-modal" id="iconSelectorModal">
        <div class="icon-selector-content">
            <div class="icon-selector-header">
                <h3 class="icon-selector-title">
                    <i class="fas fa-icons"></i>
                    选择产品图标
                </h3>
                <button type="button" class="icon-selector-close" onclick="closeIconSelector()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="icon-selector-body">
                <input type="text" class="icon-search" id="iconSearch" placeholder="搜索图标...">
                <div class="icon-grid" id="iconGrid">
                    <!-- 图标将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    {include file="admin/common/js"}

    <!-- CKEditor 5 Classic版本 -->
    <script src="/assets/js/ckeditor.js"></script>
    <!-- CKEditor 5 中文语言包 -->
    <script src="/assets/js/zh-cn.js"></script>
    <!-- 图片上传弹窗组件 -->
    <script src="/assets/js/image-uploader.js"></script>
    <!-- 图片选择器扩展 -->
    <script src="/assets/js/image-selector-extension.js"></script>

    <script>
        let editor;
        let editorImageUploader; // 编辑器专用图片上传组件实例
        let productImageUploader; // 产品图片选择器实例

        // 生成URL别名的函数（复用新闻管理的逻辑）
        function generateSlug(text, type = 'product') {
            if (!text || text.trim() === '') {
                return '';
            }

            let result = '';

            // 遍历每个字符
            for (let i = 0; i < text.length; i++) {
                const char = text[i];

                // 如果是英文字母或数字，直接保留
                if (/[a-zA-Z0-9]/.test(char)) {
                    result += char.toLowerCase();
                }
                // 如果是空格或其他分隔符，转换为连字符
                else if (/[\s\-_]/.test(char)) {
                    result += '-';
                }
                // 中文字符和其他字符忽略
            }

            // 清理结果
            result = result
                .replace(/[-]+/g, '-')  // 多个连字符合并为一个
                .replace(/^-+|-+$/g, '') // 去除首尾连字符
                .substring(0, 50); // 限制长度

            // 如果结果为空或太短，使用时间戳
            if (!result || result.length < 2) {
                const now = new Date();
                const timestamp = now.getFullYear() +
                        String(now.getMonth() + 1).padStart(2, '0') +
                        String(now.getDate()).padStart(2, '0') + '-' +
                        String(now.getHours()).padStart(2, '0') +
                        String(now.getMinutes()).padStart(2, '0');

                // 根据类型使用不同的前缀
                if (type === 'category') {
                    result = 'cat-' + timestamp;
                } else {
                    result = 'product-' + timestamp;
                }
            } else {
                // 为结果添加类型前缀，确保分类和产品URL不冲突
                if (type === 'category') {
                    result = 'cat-' + result;
                }
            }

            return result;
        }

        // 常用图标数据
        const iconData = [
            // 核心产品图标
            { class: 'fas fa-shopping-cart', name: '购物车' },
            { class: 'fas fa-share-alt', name: '分享' },
            { class: 'fas fa-users', name: '用户' },
            { class: 'fas fa-user-tie', name: '商务' },
            { class: 'fas fa-handshake', name: '合作' },
            { class: 'fas fa-chart-line', name: '增长' },

            // 管理工具图标
            { class: 'fas fa-cog', name: '设置' },
            { class: 'fas fa-chart-bar', name: '图表' },
            { class: 'fas fa-boxes', name: '库存' },
            { class: 'fas fa-tasks', name: '任务' },
            { class: 'fas fa-clipboard-list', name: '清单' },
            { class: 'fas fa-database', name: '数据库' },

            // 移动应用图标
            { class: 'fas fa-mobile-alt', name: '手机' },
            { class: 'fab fa-weixin', name: '微信' },
            { class: 'fab fa-html5', name: 'HTML5' },
            { class: 'fas fa-tablet-alt', name: '平板' },
            { class: 'fas fa-wifi', name: 'WiFi' },
            { class: 'fas fa-qrcode', name: '二维码' },

            // 定制服务图标
            { class: 'fas fa-code', name: '代码' },
            { class: 'fas fa-cloud', name: '云服务' },
            { class: 'fas fa-lightbulb', name: '创意' },
            { class: 'fas fa-tools', name: '工具' },
            { class: 'fas fa-rocket', name: '火箭' },
            { class: 'fas fa-magic', name: '魔法' },

            // 通用图标
            { class: 'fas fa-star', name: '星星' },
            { class: 'fas fa-heart', name: '心形' },
            { class: 'fas fa-thumbs-up', name: '点赞' },
            { class: 'fas fa-shield-alt', name: '盾牌' },
            { class: 'fas fa-lock', name: '锁' },
            { class: 'fas fa-key', name: '钥匙' },
            { class: 'fas fa-globe', name: '地球' },
            { class: 'fas fa-envelope', name: '邮件' },
            { class: 'fas fa-phone', name: '电话' },
            { class: 'fas fa-home', name: '首页' },
            { class: 'fas fa-building', name: '建筑' },
            { class: 'fas fa-industry', name: '工业' },
            { class: 'fas fa-graduation-cap', name: '教育' },
            { class: 'fas fa-hospital', name: '医院' },
            { class: 'fas fa-car', name: '汽车' },
            { class: 'fas fa-plane', name: '飞机' },
            { class: 'fas fa-ship', name: '轮船' },
            { class: 'fas fa-camera', name: '相机' },
            { class: 'fas fa-video', name: '视频' },
            { class: 'fas fa-music', name: '音乐' },
            { class: 'fas fa-gamepad', name: '游戏' },
            { class: 'fas fa-gift', name: '礼物' },
            { class: 'fas fa-trophy', name: '奖杯' },
            { class: 'fas fa-medal', name: '奖牌' },
            { class: 'fas fa-crown', name: '皇冠' },
            { class: 'fas fa-gem', name: '宝石' },
            { class: 'fas fa-fire', name: '火焰' },
            { class: 'fas fa-bolt', name: '闪电' },
            { class: 'fas fa-sun', name: '太阳' },
            { class: 'fas fa-moon', name: '月亮' },
            { class: 'fas fa-leaf', name: '叶子' },
            { class: 'fas fa-tree', name: '树' },
            { class: 'fas fa-mountain', name: '山' },
            { class: 'fas fa-water', name: '水' }
        ];

        $(document).ready(function() {
            // 初始化编辑器专用图片上传组件
            editorImageUploader = createImageUploader({
                uploadUrl: '/admin/image/upload?context=products',
                uploadField: 'upload',
                maxFiles: 999,
                maxSize: 5 * 1024 * 1024,
                allowedTypes: ['image/jpeg', 'image/png'],
                enableImageSelector: true,
                selectorUrl: '/admin/image/selector',
                isEditor: true,
                context: 'editor',
                instanceId: 'editor-uploader',
                onSelect: function(files) {
                    const validFiles = [];
                    const errors = [];

                    files.forEach(file => {
                        if (!['image/jpeg', 'image/png'].includes(file.type)) {
                            errors.push(`${file.name}: 仅支持JPG和PNG格式`);
                            return;
                        }

                        if (file.size > 5 * 1024 * 1024) {
                            errors.push(`${file.name}: 文件大小不能超过5MB`);
                            return;
                        }

                        validFiles.push(file);
                    });

                    if (errors.length > 0) {
                        // 使用图片上传器自己的提示
                        editorImageUploader.showMessage('文件验证失败：\n' + errors.join('\n'), 'error');
                        return false;
                    }

                    return validFiles;
                },
                onConfirm: function(orderedData, mode) {
                    if (mode === 'select') {
                        insertSelectedImagesToEditor(orderedData);
                    } else {
                        insertOrderedFilesToEditor(orderedData);
                    }
                },
                onUpload: function(uploadedFiles) {
                    uploadedFiles.forEach((fileData, index) => {
                        setTimeout(() => {
                            insertImageToEditor(fileData.url);
                        }, index * 100);
                    });
                    editorImageUploader.close();
                },
                onError: function(error) {
                    editorImageUploader.showMessage('图片上传失败：' + error.message, 'error');
                }
            });

            // 初始化CKEditor 5编辑器（复用新闻管理的配置）
            if ($('#editor').length) {
                ClassicEditor
                    .create(document.querySelector('#editor'), {
                        language: 'zh-cn',
                        placeholder: '请输入产品详情...',
                        toolbar: [
                            'heading',
                            'bold',
                            'italic',
                            'underline',
                            'numberedList',
                            'bulletedList',
                            'outdent',
                            'indent',
                            'link',
                            'insertTable',
                            'blockQuote',
                            'undo',
                            'redo'
                        ],
                        heading: {
                            options: [
                                { model: 'paragraph', title: '正文', class: 'ck-heading_paragraph' },
                                { model: 'heading1', view: 'h1', title: '标题 1', class: 'ck-heading_heading1' },
                                { model: 'heading2', view: 'h2', title: '标题 2', class: 'ck-heading_heading2' },
                                { model: 'heading3', view: 'h3', title: '标题 3', class: 'ck-heading_heading3' },
                                { model: 'heading4', view: 'h4', title: '标题 4', class: 'ck-heading_heading4' }
                            ]
                        },
                        table: {
                            contentToolbar: [
                                'tableColumn',
                                'tableRow',
                                'mergeTableCells',
                                'tableCellProperties',
                                'tableProperties'
                            ]
                        },
                        link: {
                            decorators: {
                                openInNewTab: {
                                    mode: 'manual',
                                    label: '在新标签页中打开',
                                    attributes: {
                                        target: '_blank',
                                        rel: 'noopener noreferrer'
                                    }
                                }
                            }
                        }
                    })
                    .then(newEditor => {
                        editor = newEditor;
                        window.editor = newEditor;

                        // 设置编辑器高度
                        const editingView = editor.editing.view;
                        editingView.change(writer => {
                            writer.setStyle('min-height', '300px', editingView.document.getRoot());
                            writer.setStyle('max-height', '500px', editingView.document.getRoot());
                        });

                        // 设置焦点样式
                        editor.ui.focusTracker.on('change:isFocused', (evt, name, isFocused) => {
                            if (isFocused) {
                                $('.ck-editor-container').addClass('focused');
                            } else {
                                $('.ck-editor-container').removeClass('focused');
                            }
                        });

                        // 添加图片上传按钮
                        setTimeout(() => {
                            addImageUploadButton();
                        }, 1000);

                        console.log('✅ CKEditor 5 Classic 初始化成功');
                    })
                    .catch(error => {
                        console.error('❌ CKEditor 5 初始化失败:', error);
                        $('#editor').addClass('form-textarea').attr('rows', 15).show();
                        showMessage('编辑器加载失败，已切换到基础模式', 'warning');
                        window.editor = null;
                    });
            }

            // 添加图片上传按钮到编辑器工具栏
            function addImageUploadButton() {
                let toolbarItems = document.querySelector('.ck-toolbar .ck-toolbar__items');
                if (!toolbarItems) {
                    toolbarItems = document.querySelector('.ck-toolbar__items');
                }
                if (!toolbarItems) {
                    toolbarItems = document.querySelector('.ck-toolbar');
                }

                if (!toolbarItems) {
                    return false;
                }

                if (document.querySelector('[data-upload-button="true"]')) {
                    return true;
                }

                const imageButton = document.createElement('button');
                imageButton.className = 'ck-button ck-button_with-text';
                imageButton.type = 'button';
                imageButton.setAttribute('data-upload-button', 'true');
                imageButton.setAttribute('title', '上传图片');
                imageButton.setAttribute('aria-label', '上传图片');

                const iconContainer = document.createElement('span');
                iconContainer.className = 'ck-button__icon';

                const icon = document.createElement('i');
                icon.className = 'fas fa-images';
                icon.style.cssText = `
                    font-size: 12px !important;
                    color: rgba(255, 255, 255, 0.8) !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                    display: inline-block !important;
                `;
                iconContainer.appendChild(icon);
                imageButton.appendChild(iconContainer);

                const textLabel = document.createElement('span');
                textLabel.className = 'ck-button__label';
                textLabel.textContent = '图片';
                imageButton.appendChild(textLabel);

                imageButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    if (editorImageUploader) {
                        editorImageUploader.show();
                    }
                });

                toolbarItems.appendChild(imageButton);
                return true;
            }

            // 插入选择的图片到编辑器
            function insertSelectedImagesToEditor(selectedImages) {
                if (!window.editor) {
                    return;
                }

                selectedImages.forEach((image, index) => {
                    setTimeout(() => {
                        try {
                            const imageHtml = `
                                <img src="${image.file_url}" alt="${image.alt_text || image.filename}"
                                     style="max-width: 100%; height: auto; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            `;

                            const selection = window.editor.model.document.selection;
                            const position = selection.getLastPosition();

                            const viewFragment = window.editor.data.processor.toView(imageHtml);
                            const modelFragment = window.editor.data.toModel(viewFragment);

                            window.editor.model.change(writer => {
                                window.editor.model.insertContent(modelFragment, position);
                            });

                            if (index === selectedImages.length - 1) {
                                editorImageUploader.showMessage(`已按顺序插入 ${selectedImages.length} 张图片`, 'success');
                            }
                        } catch (error) {
                            console.error(`图片插入失败:`, error);
                        }
                    }, index * 200);
                });
            }

            // 插入文件预览到编辑器
            function insertOrderedFilesToEditor(orderedFiles) {
                if (!window.editor) {
                    return;
                }

                orderedFiles.forEach((file, index) => {
                    const localUrl = URL.createObjectURL(file);

                    setTimeout(() => {
                        try {
                            const placeholderHtml = `
                                <div style="border: 2px dashed rgba(120, 119, 198, 0.5); padding: 20px; margin: 10px 0; text-align: center; background: rgba(120, 119, 198, 0.1); border-radius: 8px;">
                                    <img src="${localUrl}" alt="${file.name}" style="max-width: 200px; max-height: 150px; border-radius: 4px; margin-bottom: 10px;">
                                    <p style="color: rgba(120, 219, 255, 0.9); margin: 5px 0; font-size: 12px;">
                                        <strong>第 ${index + 1} 张:</strong> ${file.name}
                                    </p>
                                    <p style="color: rgba(255, 255, 255, 0.6); margin: 0; font-size: 11px;">
                                        <em>预览图片 - 需要上传后才能保存</em>
                                    </p>
                                </div>
                            `;

                            const selection = window.editor.model.document.selection;
                            const position = selection.getLastPosition();

                            const viewFragment = window.editor.data.processor.toView(placeholderHtml);
                            const modelFragment = window.editor.data.toModel(viewFragment);

                            window.editor.model.change(writer => {
                                window.editor.model.insertContent(modelFragment, position);
                            });

                            if (index === orderedFiles.length - 1) {
                                editorImageUploader.showMessage(`已按顺序插入 ${orderedFiles.length} 张图片预览`, 'success');
                            }
                        } catch (error) {
                            console.error(`图片插入失败:`, error);
                        }
                    }, index * 200);
                });
            }

            // 插入图片到编辑器
            function insertImageToEditor(imageUrl) {
                if (!window.editor) {
                    return;
                }

                try {
                    const imageHtml = `<img src="${imageUrl}" alt="上传的图片" style="max-width: 100%; height: auto; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">`;

                    const selection = window.editor.model.document.selection;
                    const position = selection.getLastPosition();

                    const viewFragment = window.editor.data.processor.toView(imageHtml);
                    const modelFragment = window.editor.data.toModel(viewFragment);

                    window.editor.model.change(writer => {
                        window.editor.model.insertContent(modelFragment, position);
                    });
                } catch (error) {
                    editorImageUploader.showMessage('图片插入失败', 'error');
                }
            }

            // 产品名称自动生成slug
            $('#name').on('input', function() {
                if ($('#slug').val() === '') {
                    var name = $(this).val();
                    var slug = generateSlug(name, 'product');
                    $('#slug').val(slug);
                }
            });

            // 分类名称自动生成slug
            $('#category_name').on('input', function() {
                var name = $(this).val();
                var currentSlug = $('#category_slug').val();

                if (currentSlug === '' || currentSlug === $(this).data('lastGeneratedSlug')) {
                    var slug = generateSlug(name, 'category');
                    $('#category_slug').val(slug);
                    $(this).data('lastGeneratedSlug', slug);
                }
            });

            // 分类slug手动编辑时清除自动生成标记
            $('#category_slug').on('input', function() {
                $('#category_name').removeData('lastGeneratedSlug');
            });

            // 初始化产品图片选择器
            initProductImageSelector();

            // 初始化图标选择器
            initIconSelector();

            // 页面加载时的消息提示自动隐藏
            if ($('#alertMessage').length) {
                setTimeout(function() {
                    if ($('#alertMessage').length) {
                        $('#alertMessage').fadeOut(500);
                    }
                }, 3000);
            }

            // 表单提交前验证
            $('form').on('submit', function(e) {
                const isProductForm = $(this).hasClass('news-form');
                const isCategoryForm = $(this).hasClass('category-form');

                if (isCategoryForm) {
                    return validateCategoryForm(e);
                }

                if (isProductForm) {
                    return validateProductForm(e);
                }

                if ($('#editor').length > 0) {
                    return validateProductForm(e);
                }

                return true;
            });

            // 产品表单验证函数
            function validateProductForm(e) {
                // 同步编辑器内容
                if (window.editor) {
                    const data = window.editor.getData();
                    $('#editor').val(data);

                    if (!data || data.trim() === '') {
                        e.preventDefault();
                        showMessage('请输入产品详情', 'warning');

                        setTimeout(() => {
                            if (window.editor) {
                                window.editor.editing.view.focus();
                            }
                        }, 100);
                        return false;
                    }
                } else {
                    const content = $('#editor').val();
                    if (!content || content.trim() === '') {
                        e.preventDefault();
                        showMessage('请输入产品详情', 'warning');
                        $('#editor').focus();
                        return false;
                    }
                }

                // 验证产品名称
                const name = $('#name').val();
                if (!name || name.trim() === '') {
                    e.preventDefault();
                    showMessage('请输入产品名称', 'warning');
                    $('#name').focus();
                    return false;
                }

                return true;
            }

            // 分类表单验证函数
            function validateCategoryForm(e) {
                const categoryName = $('#category_name').val();
                if (!categoryName || categoryName.trim() === '') {
                    e.preventDefault();
                    showMessage('请输入分类名称', 'warning');
                    $('#category_name').focus();
                    return false;
                }

                const categorySlug = $('#category_slug').val();
                if (!categorySlug || categorySlug.trim() === '') {
                    const generatedSlug = generateSlug(categoryName, 'category');
                    $('#category_slug').val(generatedSlug);
                }

                return true;
            }

        });

        // 初始化产品图片选择器
        function initProductImageSelector() {
            productImageUploader = createImageUploader({
                uploadUrl: '/admin/image/upload?context=products',
                uploadField: 'upload',
                maxFiles: 1,
                maxSize: 5 * 1024 * 1024,
                allowedTypes: ['image/jpeg', 'image/png'],
                enableImageSelector: true,
                selectorUrl: '/admin/image/selector',
                isEditor: false,
                context: 'product',
                instanceId: 'product-image-uploader',
                onSelect: function(files) {
                    if (files.length > 1) {
                        // 使用图片上传器自己的提示
                        productImageUploader.showMessage('产品图片只能选择一张', 'warning');
                        return false;
                    }

                    const file = files[0];
                    if (!['image/jpeg', 'image/png'].includes(file.type)) {
                        // 使用图片上传器自己的提示
                        productImageUploader.showMessage('仅支持JPG和PNG格式的图片', 'error');
                        return false;
                    }

                    if (file.size > 5 * 1024 * 1024) {
                        // 使用图片上传器自己的提示
                        productImageUploader.showMessage('图片大小不能超过5MB', 'error');
                        return false;
                    }

                    return [file];
                },
                onConfirm: function(orderedData, mode) {
                    if (mode === 'select' && orderedData.length > 0) {
                        const selectedImage = orderedData[0];
                        setProductImage(selectedImage.file_url);
                        productImageUploader.close();
                        // 使用图片上传器自己的提示
                        productImageUploader.showMessage('产品图片选择成功', 'success');
                    } else if (mode === 'upload' && orderedData.length > 0) {
                        // 上传模式会在onUpload回调中处理
                    }
                },
                onUpload: function(uploadedFiles) {
                    if (uploadedFiles && uploadedFiles.length > 0) {
                        const uploadedFile = uploadedFiles[0];
                        setProductImage(uploadedFile.url);
                        // 使用图片上传器自己的提示
                        productImageUploader.showMessage('产品图片上传成功', 'success');
                    }
                    productImageUploader.close();
                },
                onError: function(error) {
                    // 使用图片上传器自己的提示
                    productImageUploader.showMessage('产品图片操作失败：' + error.message, 'error');
                }
            });

            // 绑定选择图片按钮事件
            $('#btnSelectProductImage').on('click', function(e) {
                e.preventDefault();
                if (productImageUploader) {
                    productImageUploader.show();
                }
            });
        }

        // 设置产品图片
        function setProductImage(imageUrl) {
            $('#selectedImageUrl').val(imageUrl);
            $('#previewImg').attr('src', imageUrl);
            $('#selectedImagePreview').show();
        }

        // 更换产品图片
        function changeProductImage() {
            if (productImageUploader) {
                productImageUploader.show();
            }
        }

        // 移除产品图片
        function removeProductImage() {
            $('#selectedImageUrl').val('');
            $('#previewImg').attr('src', '');
            $('#selectedImagePreview').hide();
            if (productImageUploader) {
                productImageUploader.showMessage('已移除产品图片', 'success');
            } else {
            showMessage('已移除产品图片', 'info');
            }
        }

        // 切换产品状态
        function toggleStatus(id, status) {
            $.ajax({
                url: '/admin/products',
                type: 'POST',
                data: {
                    action: 'toggle_status',
                    id: id,
                    status: status
                },
                success: function(response) {
                    if (response.success) {
                        showMessage(response.message || '状态更新成功', 'success');
                    } else {
                        showMessage(response.message || '状态更新失败', 'error');
                        // 恢复开关状态
                        $(`input[onchange*="${id}"]`).prop('checked', !status);
                    }
                },
                error: function() {
                    showMessage('网络错误，请重试', 'error');
                    // 恢复开关状态
                    $(`input[onchange*="${id}"]`).prop('checked', !status);
                }
            });
        }

        // 删除项目功能已移至admin.js统一管理

        // 确认对话框功能已移至admin.js统一管理



        // 初始化图标选择器
        function initIconSelector() {
            // 生成图标网格
            generateIconGrid();

            // 绑定选择图标按钮事件
            $('#btnSelectIcon').on('click', function(e) {
                e.preventDefault();
                showIconSelector();
            });

            // 绑定搜索功能
            $('#iconSearch').on('input', function() {
                const searchTerm = $(this).val().toLowerCase();
                filterIcons(searchTerm);
            });

            // 绑定弹窗关闭事件
            $('#iconSelectorModal').on('click', function(e) {
                if (e.target === this) {
                    closeIconSelector();
                }
            });

            // 绑定ESC键关闭弹窗
            $(document).on('keydown', function(e) {
                if (e.keyCode === 27 && $('#iconSelectorModal').hasClass('show')) {
                    closeIconSelector();
                }
            });

            // 确保弹窗初始状态正确
            $('#iconSelectorModal').css({
                'position': 'fixed',
                'top': '0',
                'left': '0',
                'width': '100vw',
                'height': '100vh',
                'z-index': '99999',
                'display': 'none'
            });
        }

        // 生成图标网格
        function generateIconGrid() {
            const iconGrid = $('#iconGrid');
            iconGrid.empty();

            iconData.forEach(icon => {
                const iconItem = $(`
                    <div class="icon-item" data-icon="${icon.class}">
                        <i class="${icon.class}"></i>
                        <span>${icon.name}</span>
                    </div>
                `);

                iconItem.on('click', function() {
                    selectIcon(icon.class);
                });

                iconGrid.append(iconItem);
            });
        }

        // 过滤图标
        function filterIcons(searchTerm) {
            $('.icon-item').each(function() {
                const iconClass = $(this).data('icon');
                const iconName = $(this).find('span').text().toLowerCase();

                if (iconClass.toLowerCase().includes(searchTerm) ||
                    iconName.includes(searchTerm)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        }

        // 显示图标选择器
        function showIconSelector() {
            const modal = $('#iconSelectorModal');

            // 确保弹窗在最顶层
            modal.css({
                'position': 'fixed',
                'top': '0',
                'left': '0',
                'width': '100vw',
                'height': '100vh',
                'z-index': '99999',
                'display': 'flex',
                'align-items': 'center',
                'justify-content': 'center'
            });

            modal.addClass('show').fadeIn(300);

            // 延迟聚焦，确保弹窗完全显示
            setTimeout(() => {
                $('#iconSearch').focus();
            }, 350);

            // 高亮当前选中的图标
            const currentIcon = $('#icon').val();
            $('.icon-item').removeClass('selected');
            if (currentIcon) {
                $(`.icon-item[data-icon="${currentIcon}"]`).addClass('selected');
            }

            // 阻止页面滚动
            $('body').css('overflow', 'hidden');
        }

        // 关闭图标选择器
        function closeIconSelector() {
            const modal = $('#iconSelectorModal');
            modal.removeClass('show').fadeOut(300);
            $('#iconSearch').val('');
            $('.icon-item').show();

            // 恢复页面滚动
            $('body').css('overflow', 'auto');
        }

        // 选择图标
        function selectIcon(iconClass) {
            // 更新输入框
            $('#icon').val(iconClass);

            // 更新预览
            updateIconPreview(iconClass);

            // 关闭弹窗
            closeIconSelector();

            // 显示成功消息
            showMessage('图标选择成功', 'success');
        }

        // 更新图标预览
        function updateIconPreview(iconClass) {
            const iconPreview = $('#iconPreview');

            if (iconClass) {
                iconPreview.html(`
                    <div class="preview-icon">
                        <i class="${iconClass}"></i>
                        <span>当前图标</span>
                    </div>
                `);
            } else {
                iconPreview.html(`
                    <div class="preview-placeholder">
                        <i class="fas fa-question-circle"></i>
                        <span>未选择图标</span>
                    </div>
                `);
            }
        }
    </script>
</body>
</html>

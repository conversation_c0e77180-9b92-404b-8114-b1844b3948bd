
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: #0a0a0a;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    overflow: hidden;
    position: relative;
}

/* 动态背景 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    animation: backgroundShift 10s ease-in-out infinite alternate;
}

@keyframes backgroundShift {
    0% { transform: scale(1) rotate(0deg); }
    100% { transform: scale(1.1) rotate(5deg); }
}

/* 粒子效果 */
.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
    50% { transform: translateY(-20px) rotate(180deg); opacity: 0.5; }
}

.login-container {
    background: rgba(15, 15, 15, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    overflow: hidden;
    width: 100%;
    max-width: 430px;
    position: relative;
    z-index: 10;
    animation: containerGlow 3s ease-in-out infinite alternate;
}

@keyframes containerGlow {
    0% { box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(120, 119, 198, 0.2); }
    100% { box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(120, 119, 198, 0.5); }
}

.login-header {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.2) 0%,
        rgba(255, 119, 198, 0.2) 50%,
        rgba(120, 219, 255, 0.2) 100%);
    color: white;
    padding: 40px 30px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.login-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shine 3s linear infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.login-header h2 {
    margin: 0;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 28px;
    text-shadow: 0 0 20px rgba(120, 119, 198, 0.8);
    position: relative;
    z-index: 2;
}

.login-header p {
    margin: 15px 0 0 0;
    opacity: 0.9;
    font-weight: 300;
    font-size: 16px;
    position: relative;
    z-index: 2;
}

.login-body {
    padding: 40px 30px;
    background: rgba(10, 10, 10, 0.8);
}

.form-group {
    margin-bottom: 30px;
    position: relative;
}

.form-label {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 12px;
    font-size: 15px;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    display: block;
    padding-left: 5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
}

.input-icon {
    position: absolute;
    left: 18px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    z-index: 2;
    transition: all 0.3s ease;
}

.form-control {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px 20px 15px 50px;
    color: white;
    font-size: 16px;
    transition: all 0.3s ease;
    width: 100%;
    outline: none !important;
    box-shadow: none !important;
}

.form-control:focus {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(120, 119, 198, 0.8);
    box-shadow: 0 0 20px rgba(120, 119, 198, 0.3) !important;
    color: white;
    outline: none !important;
}

.input-wrapper:focus-within .input-icon {
    color: rgba(120, 119, 198, 1);
    text-shadow: 0 0 10px rgba(120, 119, 198, 0.5);
}

.input-wrapper:focus-within .form-control {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(120, 119, 198, 0.8);
    box-shadow: 0 0 20px rgba(120, 119, 198, 0.3) !important;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.btn-login {
    background: linear-gradient(135deg,
        rgba(120, 119, 198, 0.8) 0%,
        rgba(255, 119, 198, 0.8) 50%,
        rgba(120, 219, 255, 0.8) 100%);
    border: none;
    border-radius: 12px;
    padding: 16px 20px;
    font-weight: 600;
    width: 100%;
    color: white;
    font-size: 16px;
    text-transform: uppercase;
    letter-spacing: 1.2px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-top: 10px;
}

.btn-login::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-login:hover::before {
    left: 100%;
}

.btn-login:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(120, 119, 198, 0.4);
    color: white;
}

.alert {
    border-radius: 10px;
    border: none;
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.3);
    color: #ff6b6b;
    padding: 15px 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.alert i {
    font-size: 16px;
    flex-shrink: 0;
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.3);
    color: #28a745;
}

.form-check {
    margin-bottom: 25px;
    padding-left: 5px;
}

.form-check-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin-left: 8px;
    cursor: pointer;
}

.form-check-input {
    margin-top: 0.2em;
}

.form-check-input:checked {
    background-color: rgba(120, 119, 198, 0.8);
    border-color: rgba(120, 119, 198, 0.8);
}

.login-footer {
    text-align: center;
    padding: 25px 30px;
    background: rgba(5, 5, 5, 0.9);
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.login-footer a {
    color: rgba(120, 119, 198, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
}

.login-footer a:hover {
    color: rgba(120, 119, 198, 1);
    text-shadow: 0 0 10px rgba(120, 119, 198, 0.5);
}

/* 响应式设计 */
@media (max-width: 480px) {
    .login-container {
        margin: 20px;
        max-width: none;
    }

    .login-header h2 {
        font-size: 24px;
    }

    .login-body {
        padding: 30px 20px;
    }
}

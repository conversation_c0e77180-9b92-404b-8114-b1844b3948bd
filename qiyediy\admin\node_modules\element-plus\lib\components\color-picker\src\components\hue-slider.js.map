{"version": 3, "file": "hue-slider.js", "sources": ["../../../../../../../packages/components/color-picker/src/components/hue-slider.vue"], "sourcesContent": ["<template>\n  <div :class=\"[ns.b(), ns.is('vertical', vertical)]\">\n    <div ref=\"bar\" :class=\"ns.e('bar')\" @click=\"handleClick\" />\n    <div\n      ref=\"thumb\"\n      :class=\"ns.e('thumb')\"\n      :style=\"{\n        left: thumbLeft + 'px',\n        top: thumbTop + 'px',\n      }\"\n    />\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  onMounted,\n  ref,\n  watch,\n} from 'vue'\nimport { getClientXY } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { draggable } from '../utils/draggable'\n\nimport type { PropType } from 'vue'\nimport type Color from '../utils/color'\n\nexport default defineComponent({\n  name: 'ElColorHueSlider',\n\n  props: {\n    color: {\n      type: Object as PropType<Color>,\n      required: true,\n    },\n\n    vertical: <PERSON><PERSON><PERSON>,\n  },\n  setup(props) {\n    const ns = useNamespace('color-hue-slider')\n    const instance = getCurrentInstance()!\n    // ref\n    const thumb = ref<HTMLElement>()\n    const bar = ref<HTMLElement>()\n    // data\n    const thumbLeft = ref(0)\n    const thumbTop = ref(0)\n    // computed\n    const hueValue = computed(() => {\n      return props.color.get('hue')\n    })\n    // watch\n    watch(\n      () => hueValue.value,\n      () => {\n        update()\n      }\n    )\n\n    // methods\n    function handleClick(event: MouseEvent | TouchEvent) {\n      const target = event.target\n\n      if (target !== thumb.value) {\n        handleDrag(event)\n      }\n    }\n\n    function handleDrag(event: MouseEvent | TouchEvent) {\n      if (!bar.value || !thumb.value) return\n\n      const el = instance.vnode.el as HTMLElement\n      const rect = el.getBoundingClientRect()\n      const { clientX, clientY } = getClientXY(event)\n      let hue\n\n      if (!props.vertical) {\n        let left = clientX - rect.left\n        left = Math.min(left, rect.width - thumb.value.offsetWidth / 2)\n        left = Math.max(thumb.value.offsetWidth / 2, left)\n\n        hue = Math.round(\n          ((left - thumb.value.offsetWidth / 2) /\n            (rect.width - thumb.value.offsetWidth)) *\n            360\n        )\n      } else {\n        let top = clientY - rect.top\n\n        top = Math.min(top, rect.height - thumb.value.offsetHeight / 2)\n        top = Math.max(thumb.value.offsetHeight / 2, top)\n        hue = Math.round(\n          ((top - thumb.value.offsetHeight / 2) /\n            (rect.height - thumb.value.offsetHeight)) *\n            360\n        )\n      }\n      props.color.set('hue', hue)\n    }\n\n    function getThumbLeft() {\n      if (!thumb.value) return 0\n\n      const el = instance.vnode.el\n\n      if (props.vertical) return 0\n      const hue = props.color.get('hue')\n\n      if (!el) return 0\n      return Math.round(\n        (hue * (el.offsetWidth - thumb.value.offsetWidth / 2)) / 360\n      )\n    }\n\n    function getThumbTop() {\n      if (!thumb.value) return 0\n\n      const el = instance.vnode.el as HTMLElement\n      if (!props.vertical) return 0\n      const hue = props.color.get('hue')\n\n      if (!el) return 0\n      return Math.round(\n        (hue * (el.offsetHeight - thumb.value.offsetHeight / 2)) / 360\n      )\n    }\n\n    function update() {\n      thumbLeft.value = getThumbLeft()\n      thumbTop.value = getThumbTop()\n    }\n\n    // mounded\n    onMounted(() => {\n      if (!bar.value || !thumb.value) return\n\n      const dragConfig = {\n        drag: (event: MouseEvent | TouchEvent) => {\n          handleDrag(event)\n        },\n        end: (event: MouseEvent | TouchEvent) => {\n          handleDrag(event)\n        },\n      }\n\n      draggable(bar.value, dragConfig)\n      draggable(thumb.value, dragConfig)\n      update()\n    })\n\n    return {\n      bar,\n      thumb,\n      thumbLeft,\n      thumbTop,\n      hueValue,\n      handleClick,\n      update,\n      ns,\n    }\n  },\n})\n</script>\n"], "names": ["defineComponent", "useNamespace", "getCurrentInstance", "ref", "computed", "watch", "getClientXY", "onMounted", "draggable", "_openBlock", "_createElementBlock", "_createElementVNode", "_normalizeClass", "_normalizeStyle", "_export_sfc"], "mappings": ";;;;;;;;;;AA8BA,MAAK,YAAaA,mBAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,kBAAA;AAAA,EAEN,KAAO,EAAA;AAAA,IACL,KAAO,EAAA;AAAA,MACL,IAAM,EAAA,MAAA;AAAA,MACN,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,IAEA,QAAU,EAAA,OAAA;AAAA,GACZ;AAAA,EACA,MAAM,KAAO,EAAA;AACX,IAAM,MAAA,EAAA,GAAKC,mBAAa,kBAAkB,CAAA,CAAA;AAC1C,IAAA,MAAM,WAAWC,sBAAmB,EAAA,CAAA;AAEpC,IAAA,MAAM,QAAQC,OAAiB,EAAA,CAAA;AAC/B,IAAA,MAAM,MAAMA,OAAiB,EAAA,CAAA;AAE7B,IAAM,MAAA,SAAA,GAAYA,QAAI,CAAC,CAAA,CAAA;AACvB,IAAM,MAAA,QAAA,GAAWA,QAAI,CAAC,CAAA,CAAA;AAEtB,IAAM,MAAA,QAAA,GAAWC,aAAS,MAAM;AAC9B,MAAO,OAAA,KAAA,CAAM,KAAM,CAAA,GAAA,CAAI,KAAK,CAAA,CAAA;AAAA,KAC7B,CAAA,CAAA;AAED,IAAAC,SAAA,CAAA,MAAA,QAAA,CAAA,KAAA,EAAA,MAAA;AAAA,MACE,MAAM,EAAS,CAAA;AAAA,KAAA,CACf,CAAM;AACJ,IAAO,SAAA,WAAA,CAAA,KAAA,EAAA;AAAA,MACT,MAAA,MAAA,GAAA,KAAA,CAAA,MAAA,CAAA;AAAA,MACF,IAAA,MAAA,KAAA,KAAA,CAAA,KAAA,EAAA;AAGA,QAAA,iBAAqB,CAAgC;AACnD,OAAA;AAEA,KAAI;AACF,IAAA,SAAA,UAAgB,CAAA,KAAA,EAAA;AAAA,MAClB,IAAA,CAAA,GAAA,CAAA,KAAA,IAAA,CAAA,KAAA,CAAA,KAAA;AAAA,QACF,OAAA;AAEA,MAAA,MAAA,aAAoD,CAAA,KAAA,CAAA,EAAA,CAAA;AAClD,MAAA,MAAK,IAAa,GAAA,EAAA,CAAA,qBAAc,EAAA,CAAA;AAEhC,MAAM,MAAA,EAAA,gBAAoB,EAAA,GAAAC,oBAAA,CAAA,KAAA,CAAA,CAAA;AAC1B,MAAM,IAAA,GAAA,CAAA;AACN,MAAA,IAAA,CAAA,KAAQ,CAAA,QAAiB,EAAA;AACzB,QAAI,IAAA,IAAA,GAAA,OAAA,GAAA,IAAA,CAAA,IAAA,CAAA;AAEJ,QAAI,WAAiB,CAAA,GAAA,CAAA,IAAA,EAAA,IAAA,CAAA,KAAA,GAAA,KAAA,CAAA,KAAA,CAAA,WAAA,GAAA,CAAA,CAAA,CAAA;AACnB,QAAI,IAAA,GAAA,IAAO,UAAU,CAAK,KAAA,CAAA,WAAA,GAAA,CAAA,EAAA,IAAA,CAAA,CAAA;AAC1B,QAAO,GAAA,GAAA,IAAA,CAAA,KAAS,CAAM,CAAA,IAAA,GAAA,WAAmB,CAAA,wBAAoB,CAAC,KAAA,GAAA,KAAA,CAAA,KAAA,CAAA,WAAA,CAAA,GAAA,GAAA,CAAA,CAAA;AAC9D,OAAA,MAAA;AAEA,QAAA,IAAA,GAAW,GAAA,OAAA,GAAA,IAAA,CAAA,GAAA,CAAA;AAAA,QACP,GAAA,GAAA,IAAO,QAAY,EAAA,IAAA,CAAA,MAAA,GAAA,WACb,CAAA,YAAc,GAAA,CAAA,CAAA,CAAA;AACpB,QACJ,GAAA,GAAA,IAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,CAAA,YAAA,GAAA,CAAA,EAAA,GAAA,CAAA,CAAA;AAAA,QACK,GAAA,GAAA,IAAA,CAAA,KAAA,CAAA,CAAA,GAAA,GAAA,KAAA,CAAA,KAAA,CAAA,YAAA,GAAA,CAAA,KAAA,IAAA,CAAA,MAAA,GAAA,KAAA,CAAA,KAAA,CAAA,YAAA,CAAA,GAAA,GAAA,CAAA,CAAA;AACL,OAAI;AAEJ,MAAM,KAAA,CAAA,KAAA,CAAA,SAAc,EAAA,GAAA,CAAK;AACzB,KAAA;AACA,IAAA,SAAA,YAAW,GAAA;AAAA,MACP,IAAA,CAAA,KAAA,CAAM;AAEN,QACJ,OAAA,CAAA,CAAA;AAAA,MACF,MAAA,EAAA,GAAA,QAAA,CAAA,KAAA,CAAA,EAAA,CAAA;AACA,MAAM,IAAA,KAAA,CAAA,QAAU;AAAU,QAC5B,OAAA,CAAA,CAAA;AAEA,MAAA,MAAA,GAAwB,GAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,KAAA,CAAA,CAAA;AACtB,MAAI,IAAA,CAAC,EAAM;AAEX,QAAM,OAAA,CAAA,CAAK;AAEX,MAAI,OAAA,cAAuB,IAAA,EAAA,CAAA,WAAA,GAAA,KAAA,CAAA,KAAA,CAAA,WAAA,GAAA,CAAA,CAAA,GAAA,GAAA,CAAA,CAAA;AAC3B,KAAA;AAEA,IAAI,oBAAY,GAAA;AAChB,MAAA,IAAA,CAAA,KAAY,CAAA,KAAA;AAAA,QACT,OAAO,CAAG,CAAA;AAA8C,MAC3D,MAAA,EAAA,GAAA,QAAA,CAAA,KAAA,CAAA,EAAA,CAAA;AAAA,MACF,IAAA,CAAA,KAAA,CAAA,QAAA;AAEA,QAAA,OAAuB,CAAA,CAAA;AACrB,MAAI,MAAO,GAAA,GAAA,KAAO,CAAO,KAAA,CAAA,GAAA,CAAA,KAAA,CAAA,CAAA;AAEzB,MAAM,IAAA,CAAA,EAAA;AACN,QAAI,OAAO,CAAA,CAAA;AACX,MAAA,OAAY,IAAA,CAAA,KAAA,CAAM,GAAM,IAAA,EAAA,CAAI,YAAK,GAAA,KAAA,CAAA,KAAA,CAAA,YAAA,GAAA,CAAA,CAAA,GAAA,GAAA,CAAA,CAAA;AAEjC,KAAI;AACJ,IAAA,SAAO,MAAK,GAAA;AAAA,MAAA,SACF,CAAG,KAAA,GAAA,YAAqB,EAAA,CAAA;AAA2B,MAC7D,QAAA,CAAA,KAAA,GAAA,WAAA,EAAA,CAAA;AAAA,KACF;AAEA,IAAAC,aAAS,CAAS,MAAA;AAChB,MAAA,IAAA,CAAA,GAAA,CAAA,SAAkB,CAAa,KAAA,CAAA,KAAA;AAC/B,QAAA,OAAS;AAAoB,MAC/B,MAAA,UAAA,GAAA;AAGA,QAAA,IAAA,EAAU,CAAM,KAAA,KAAA;AACd,UAAI,UAAc,CAAA;AAElB,SAAA;AAAmB,QACjB,GAAA,EAAA,CAAM,KAAoC,KAAA;AACxC,UAAA,UAAA,CAAW,KAAK,CAAA,CAAA;AAAA,SAClB;AAAA,OACA,CAAA;AACE,MAAAC,mBAAA,CAAA,GAAA,CAAA,KAAgB,EAAA,UAAA,CAAA,CAAA;AAAA,MAClBA,mBAAA,CAAA,KAAA,CAAA,KAAA,EAAA,UAAA,CAAA,CAAA;AAAA,MACF,MAAA,EAAA,CAAA;AAEA,KAAU,CAAA,CAAA;AACV,IAAU,OAAA;AACV,MAAO,GAAA;AAAA,MACR,KAAA;AAED,MAAO,SAAA;AAAA,MACL,QAAA;AAAA,MACA,QAAA;AAAA,MACA,WAAA;AAAA,MACA,MAAA;AAAA,MACA,EAAA;AAAA,KACA,CAAA;AAAA,GACA;AAAA,CACA,CAAA,CAAA;AACF,SACF,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACF,EAAC,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;;;AAnKC,IAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,MAUM,GAAA,EAAA,KAAA;AAAA,MAAA,KAAA,EAAAC,kBAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA;AAAA,MAVA,OAAK,kBAAM;AAA+B,KAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,SAAA,CAAA,CAAA;;MAC9C,GAA2D,EAAA,OAAA;AAAA,MAAA,KAAlD,EAAAA,kBAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;AAAA,MAAO,KAAA,EAAAC,kBAAO,CAAA;AAAI,QAAU,IAAO,EAAA,IAAA,CAAA,SAAA,GAAA,IAAA;AAAA,QAAA,GAAA,EAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AAC5C,OAAA,CAAA;AAAA,KAOE,EAAA,IAAA,EAAA,CAAA,CAAA;AAAA,GAAA,EAAA,CAAA,CAAA,CAAA;AAAA,CAAA;AALY,gBACN,gBAAAC,iCAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,gBAAA,CAAA,CAAA,CAAA;;;;"}
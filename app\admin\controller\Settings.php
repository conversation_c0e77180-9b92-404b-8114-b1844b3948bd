<?php
declare(strict_types=1);

namespace app\admin\controller;

use think\facade\Db;
use think\facade\Request;
use think\facade\Session;
use app\service\ConfigService;
use app\validate\SecurityValidate;

class Settings extends Base
{
    public function index()
    {
        $admin_info = $this->getAdminUser();

        // 处理表单提交
        if (Request::isPost()) {
            try {
                // 基本信息设置
                if (Request::has('basic_settings')) {
                    $basicSettings = [
                        'site_name' => Request::param('site_name', ''),
                        'site_title' => Request::param('site_title', ''),
                        'site_description' => Request::param('site_description', ''),
                        'site_keywords' => Request::param('site_keywords', ''),
                        'site_url' => Request::param('site_url', '')
                    ];

                    // 数据安全验证 - 使用统一安全验证器
                    $securityCheck = SecurityValidate::validateDataSecurity($basicSettings, [
                        'site_name' => 'checkXss',
                        'site_title' => 'checkXss',
                        'site_description' => 'checkXss',
                        'site_keywords' => 'checkXss',
                        'site_url' => 'checkUrlSafe',
                    ]);

                    if (!$securityCheck['valid']) {
                        $errors = [];
                        foreach ($securityCheck['errors'] as $field => $fieldErrors) {
                            $errors[] = $field . ': ' . implode(', ', $fieldErrors);
                        }
                        Session::set('settings_message', '数据安全检查失败: ' . implode('; ', $errors));
                        Session::set('settings_message_type', 'danger');
                        return redirect('/admin/settings');
                    }

                    foreach ($basicSettings as $key => $value) {
                        $this->updateSetting($key, trim($value));
                    }
                    
                    // 清除配置缓存
                    ConfigService::clearCache();
                    
                    // 使用Session传递消息
                    Session::set('settings_message', '基本信息设置已保存！');
                    Session::set('settings_message_type', 'success');
                    return redirect('/admin/settings');
                }
                
                // 联系信息设置
                if (Request::has('contact_settings')) {
                    $contactSettings = [
                        'company_name' => Request::param('company_name', ''),
                        'company_address' => Request::param('company_address', ''),
                        'company_phone' => Request::param('company_phone', ''),
                        'company_email' => Request::param('company_email', ''),
                        'company_qq' => Request::param('company_qq', ''),
                        'company_wechat' => Request::param('company_wechat', '')
                    ];

                    // 数据安全验证 - 使用统一安全验证器
                    $securityCheck = SecurityValidate::validateDataSecurity($contactSettings, [
                        'company_name' => 'checkXss',
                        'company_address' => 'checkXss',
                        'company_phone' => 'checkUsernameSafe',
                        'company_email' => 'checkEmailSafe',
                        'company_qq' => 'checkUsernameSafe',
                        'company_wechat' => 'checkUsernameSafe',
                    ]);

                    if (!$securityCheck['valid']) {
                        $errors = [];
                        foreach ($securityCheck['errors'] as $field => $fieldErrors) {
                            $errors[] = $field . ': ' . implode(', ', $fieldErrors);
                        }
                        Session::set('settings_message', '数据安全检查失败: ' . implode('; ', $errors));
                        Session::set('settings_message_type', 'danger');
                        return redirect('/admin/settings');
                    }

                    foreach ($contactSettings as $key => $value) {
                        $this->updateSetting($key, trim($value));
                    }
                    
                    // 清除配置缓存
                    ConfigService::clearCache();
                    
                    Session::set('settings_message', '联系信息设置已保存！');
                    Session::set('settings_message_type', 'success');
                    return redirect('/admin/settings');
                }
                
                // SEO设置
                if (Request::has('seo_settings')) {
                    $seoSettings = [
                        'meta_keywords' => Request::param('meta_keywords', ''),
                        'meta_description' => Request::param('meta_description', ''),
                        'analytics_code' => Request::param('analytics_code', ''),
                        'baidu_verify' => Request::param('baidu_verify', '')
                    ];

                    // 数据安全验证 - 使用统一安全验证器
                    $securityCheck = SecurityValidate::validateDataSecurity($seoSettings, [
                        'meta_keywords' => 'checkXss',
                        'meta_description' => 'checkXss',
                        'analytics_code' => 'checkScriptSafe', // 特殊处理分析代码
                        'baidu_verify' => 'checkUsernameSafe',
                    ]);

                    if (!$securityCheck['valid']) {
                        $errors = [];
                        foreach ($securityCheck['errors'] as $field => $fieldErrors) {
                            $errors[] = $field . ': ' . implode(', ', $fieldErrors);
                        }
                        Session::set('settings_message', '数据安全检查失败: ' . implode('; ', $errors));
                        Session::set('settings_message_type', 'danger');
                        return redirect('/admin/settings');
                    }

                    foreach ($seoSettings as $key => $value) {
                        $this->updateSetting($key, trim($value));
                    }
                    
                    // 清除配置缓存
                    ConfigService::clearCache();
                    
                    Session::set('settings_message', 'SEO设置已保存！');
                    Session::set('settings_message_type', 'success');
                    return redirect('/admin/settings');
                }
                
                // 系统配置
                if (Request::has('system_settings')) {
                    $systemSettings = [
                        'max_file_size' => Request::param('max_file_size', '5'),
                        'items_per_page' => Request::param('items_per_page', '10'),
                        'cache_enabled' => Request::has('cache_enabled') ? '1' : '0',
                        'cache_time' => Request::param('cache_time', '3600')
                    ];

                    // 数据安全验证 - 使用统一安全验证器
                    $securityCheck = SecurityValidate::validateDataSecurity($systemSettings, [
                        'max_file_size' => 'checkNumberSafe',
                        'items_per_page' => 'checkNumberSafe',
                        'cache_enabled' => 'checkNumberSafe',
                        'cache_time' => 'checkNumberSafe',
                    ]);

                    if (!$securityCheck['valid']) {
                        $errors = [];
                        foreach ($securityCheck['errors'] as $field => $fieldErrors) {
                            $errors[] = $field . ': ' . implode(', ', $fieldErrors);
                        }
                        Session::set('settings_message', '数据安全检查失败: ' . implode('; ', $errors));
                        Session::set('settings_message_type', 'danger');
                        return redirect('/admin/settings');
                    }

                    foreach ($systemSettings as $key => $value) {
                        $this->updateSetting($key, trim($value));
                    }
                    
                    // 清除配置缓存
                    ConfigService::clearCache();
                    
                    Session::set('settings_message', '系统配置已保存！');
                    Session::set('settings_message_type', 'success');
                    return redirect('/admin/settings');
                }
                
            } catch (\Exception $e) {
                Session::set('settings_message', '保存失败：' . $e->getMessage());
                Session::set('settings_message_type', 'danger');
                return redirect('/admin/settings');
            }
        }

        // 获取当前设置
        $currentSettings = [];
        try {
            $settings = Db::table('site_settings')->select()->toArray();
            foreach ($settings as $setting) {
                $currentSettings[$setting['setting_key']] = $setting['setting_value'];
            }
        } catch (\Exception $e) {
            // 记录错误日志
        }

        // 从Session获取消息
        $message = Session::get('settings_message', '');
        $messageType = Session::get('settings_message_type', '');
        
        // 清除Session中的消息，防止重复显示
        if ($message) {
            Session::delete('settings_message');
            Session::delete('settings_message_type');
        }

        return view('admin/settings', [
            'pageTitle' => '系统设置',
            'pageIcon' => 'fas fa-cogs',
            'admin_info' => $admin_info,
            'currentSettings' => $currentSettings,
            'message' => $message,
            'messageType' => $messageType,
            'currentController' => 'Settings'
        ]);
    }

    private function updateSetting($key, $value)
    {
        $exists = Db::table('site_settings')->where('setting_key', $key)->find();

        if ($exists) {
            Db::table('site_settings')
                ->where('setting_key', $key)
                ->update([
                    'setting_value' => $value,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
        } else {
            Db::table('site_settings')->insert([
                'setting_key' => $key,
                'setting_value' => $value,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }
    }
}

<!-- 侧边栏 -->
<nav class="col-md-3 col-lg-2 sidebar">
    <div class="sidebar-sticky">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="/admin" data-controller="Index">
                    <i class="fas fa-tachometer-alt"></i> 仪表盘
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/banners" data-controller="Banners">
                    <i class="fas fa-images"></i> 轮播图管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/products" data-controller="Products">
                    <i class="fas fa-cube"></i> 产品管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/solutions" data-controller="Solutions">
                    <i class="fas fa-lightbulb"></i> 解决方案
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/news" data-controller="News">
                    <i class="fas fa-newspaper"></i> 新闻管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/cases" data-controller="Cases">
                    <i class="fas fa-briefcase"></i> 客户案例
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/contacts" data-controller="Contacts">
                    <i class="fas fa-envelope"></i> 联系表单
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/page-builder" data-controller="PageBuilder">
                    <i class="fas fa-paint-brush"></i> 页面装修
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/sys-menu" data-controller="SysMenu">
                    <i class="fas fa-sitemap"></i> 系统菜单
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/admin/settings" data-controller="Settings">
                    <i class="fas fa-cog"></i> 系统设置
                </a>
            </li>
        </ul>
    </div>
</nav>

<!-- 移动端遮罩层 -->
<div class="sidebar-overlay d-md-none"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取当前URL路径
    const currentPath = window.location.pathname;
    console.log('当前路径:', currentPath);

    // 移除所有active状态
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });

    // 根据URL路径设置active状态
    if (currentPath === '/admin' || currentPath === '/admin/' || currentPath.endsWith('/admin/index')) {
        const indexLink = document.querySelector('[data-controller="Index"]');
        if (indexLink) {
            indexLink.classList.add('active');
            console.log('设置仪表盘为active');
        }
    } else if (currentPath.includes('/admin/banners')) {
        const bannersLink = document.querySelector('[data-controller="Banners"]');
        if (bannersLink) {
            bannersLink.classList.add('active');
            console.log('设置轮播图为active');
        }
    } else if (currentPath.includes('/admin/contacts')) {
        const contactsLink = document.querySelector('[data-controller="Contacts"]');
        if (contactsLink) {
            contactsLink.classList.add('active');
            console.log('设置联系表单为active');
        }
    } else if (currentPath.includes('/admin/products')) {
        const productsLink = document.querySelector('[data-controller="Products"]');
        if (productsLink) {
            productsLink.classList.add('active');
            console.log('设置产品管理为active');
        }
    } else if (currentPath.includes('/admin/solutions')) {
        const solutionsLink = document.querySelector('[data-controller="Solutions"]');
        if (solutionsLink) {
            solutionsLink.classList.add('active');
            console.log('设置解决方案为active');
        }
    } else if (currentPath.includes('/admin/news')) {
        const newsLink = document.querySelector('[data-controller="News"]');
        if (newsLink) {
            newsLink.classList.add('active');
            console.log('设置新闻管理为active');
        }
    } else if (currentPath.includes('/admin/cases')) {
        const casesLink = document.querySelector('[data-controller="Cases"]');
        if (casesLink) {
            casesLink.classList.add('active');
            console.log('设置客户案例为active');
        }
    } else if (currentPath.includes('/admin/page-builder')) {
        const pageBuilderLink = document.querySelector('[data-controller="PageBuilder"]');
        if (pageBuilderLink) {
            pageBuilderLink.classList.add('active');
            console.log('设置页面装修为active');
        }
    } else if (currentPath.includes('/admin/sys-menu')) {
        const sysMenuLink = document.querySelector('[data-controller="SysMenu"]');
        if (sysMenuLink) {
            sysMenuLink.classList.add('active');
            console.log('设置系统菜单为active');
        }
    } else if (currentPath.includes('/admin/settings')) {
        const settingsLink = document.querySelector('[data-controller="Settings"]');
        if (settingsLink) {
            settingsLink.classList.add('active');
            console.log('设置系统设置为active');
        }
    }
});
</script>

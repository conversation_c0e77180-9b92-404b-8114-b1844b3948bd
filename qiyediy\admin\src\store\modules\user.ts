/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 用户状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/auth'
import type { User, LoginCredentials, RegisterData } from '@/types/auth'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string>('')
  const permissions = ref<string[]>([])
  const roles = ref<any[]>([])
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => roles.value.some(role => role.slug === 'admin'))
  const isSuperAdmin = computed(() => roles.value.some(role => role.slug === 'super_admin'))
  const avatar = computed(() => user.value?.avatar_url || '/static/images/default-avatar.png')
  const displayName = computed(() => user.value?.real_name || user.value?.username || '未知用户')

  /**
   * 用户登录
   */
  const login = async (credentials: LoginCredentials): Promise<void> => {
    try {
      loading.value = true
      const response = await authApi.login(credentials)
      
      // 保存用户信息
      token.value = response.token
      user.value = response.user
      permissions.value = response.permissions || []
      roles.value = response.roles || []
      
      // 保存到本地存储
      localStorage.setItem('token', response.token)
      localStorage.setItem('user', JSON.stringify(response.user))
      
      ElMessage.success('登录成功')
    } catch (error: any) {
      ElMessage.error(error.message || '登录失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 用户注册
   */
  const register = async (data: RegisterData): Promise<void> => {
    try {
      loading.value = true
      const response = await authApi.register(data)
      
      // 保存用户信息
      token.value = response.token
      user.value = response.user
      permissions.value = response.permissions || []
      roles.value = response.roles || []
      
      // 保存到本地存储
      localStorage.setItem('token', response.token)
      localStorage.setItem('user', JSON.stringify(response.user))
      
      ElMessage.success('注册成功')
    } catch (error: any) {
      ElMessage.error(error.message || '注册失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 用户登出
   */
  const logout = async (): Promise<void> => {
    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除状态
      user.value = null
      token.value = ''
      permissions.value = []
      roles.value = []
      
      // 清除本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      
      ElMessage.success('已退出登录')
    }
  }

  /**
   * 获取用户信息
   */
  const getUserInfo = async (): Promise<void> => {
    try {
      const response = await authApi.me()
      
      user.value = response.user
      permissions.value = response.permissions || []
      roles.value = response.roles || []
      
      // 更新本地存储
      localStorage.setItem('user', JSON.stringify(response.user))
    } catch (error: any) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，清除登录状态
      await logout()
      throw error
    }
  }

  /**
   * 刷新Token
   */
  const refreshToken = async (): Promise<void> => {
    try {
      const response = await authApi.refresh()
      token.value = response.token
      localStorage.setItem('token', response.token)
    } catch (error: any) {
      console.error('刷新Token失败:', error)
      await logout()
      throw error
    }
  }

  /**
   * 修改密码
   */
  const changePassword = async (oldPassword: string, newPassword: string): Promise<void> => {
    try {
      loading.value = true
      await authApi.changePassword({
        old_password: oldPassword,
        new_password: newPassword,
        confirm_new_password: newPassword
      })
      
      ElMessage.success('密码修改成功')
    } catch (error: any) {
      ElMessage.error(error.message || '密码修改失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新个人资料
   */
  const updateProfile = async (data: Partial<User>): Promise<void> => {
    try {
      loading.value = true
      const response = await authApi.updateProfile(data)
      
      user.value = { ...user.value, ...response }
      localStorage.setItem('user', JSON.stringify(user.value))
      
      ElMessage.success('个人资料更新成功')
    } catch (error: any) {
      ElMessage.error(error.message || '个人资料更新失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 上传头像
   */
  const uploadAvatar = async (file: File): Promise<void> => {
    try {
      loading.value = true
      const formData = new FormData()
      formData.append('avatar', file)
      
      const response = await authApi.uploadAvatar(formData)
      
      if (user.value) {
        user.value.avatar = response.avatar
        user.value.avatar_url = response.avatar_url
        localStorage.setItem('user', JSON.stringify(user.value))
      }
      
      ElMessage.success('头像上传成功')
    } catch (error: any) {
      ElMessage.error(error.message || '头像上传失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 检查权限
   */
  const hasPermission = (permission: string): boolean => {
    if (isSuperAdmin.value) {
      return true
    }
    return permissions.value.includes(permission)
  }

  /**
   * 检查角色
   */
  const hasRole = (role: string): boolean => {
    return roles.value.some(r => r.slug === role)
  }

  /**
   * 检查多个权限（AND关系）
   */
  const hasAllPermissions = (perms: string[]): boolean => {
    if (isSuperAdmin.value) {
      return true
    }
    return perms.every(perm => permissions.value.includes(perm))
  }

  /**
   * 检查多个权限（OR关系）
   */
  const hasAnyPermission = (perms: string[]): boolean => {
    if (isSuperAdmin.value) {
      return true
    }
    return perms.some(perm => permissions.value.includes(perm))
  }

  /**
   * 初始化用户状态
   */
  const initUserState = (): void => {
    const savedToken = localStorage.getItem('token')
    const savedUser = localStorage.getItem('user')
    
    if (savedToken && savedUser) {
      token.value = savedToken
      try {
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        localStorage.removeItem('user')
      }
    }
  }

  /**
   * 重置用户状态
   */
  const resetUserState = (): void => {
    user.value = null
    token.value = ''
    permissions.value = []
    roles.value = []
    loading.value = false
  }

  // 初始化
  initUserState()

  return {
    // 状态
    user: readonly(user),
    token: readonly(token),
    permissions: readonly(permissions),
    roles: readonly(roles),
    loading: readonly(loading),
    
    // 计算属性
    isLoggedIn,
    isAdmin,
    isSuperAdmin,
    avatar,
    displayName,
    
    // 方法
    login,
    register,
    logout,
    getUserInfo,
    refreshToken,
    changePassword,
    updateProfile,
    uploadAvatar,
    hasPermission,
    hasRole,
    hasAllPermissions,
    hasAnyPermission,
    initUserState,
    resetUserState
  }
})

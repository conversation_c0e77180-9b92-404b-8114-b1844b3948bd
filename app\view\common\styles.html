<!-- 公共样式文件 -->
<style>
/* ===== Header和Navbar样式 ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar {
    padding: 12px 0 !important;
    background: transparent !important;
    border: none !important;
    transition: background 0.3s ease, backdrop-filter 0.3s ease !important;
    min-height: 70px !important;
    display: flex !important;
    align-items: center !important;
}

/* 确保navbar容器正确布局 */
.navbar .container {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    width: 100% !important;
}

/* 滚动时header背景 */
header.scrolled {
    background: rgba(47, 76, 153, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    box-shadow: 0 2px 20px rgba(47, 76, 153, 0.3) !important;
}

/* 确保navbar也有背景 */
header.scrolled .navbar,
header.scrolled .navbar-expand-lg,
header.scrolled .navbar-light {
    background: #2f4c99f5 !important;
    background-color: #2f4c99f5 !important;
}

.navbar-brand {
    font-size: 1.6rem !important;
    font-weight: 600 !important;
    color: white !important;
    text-decoration: none !important;
    display: flex !important;
    align-items: center !important;
    background: none !important;
    -webkit-background-clip: unset !important;
    -webkit-text-fill-color: white !important;
    background-clip: unset !important;
}

.navbar-brand .logo {
    height: 45px !important;
    width: auto !important;
    margin-right: 10px !important;
    filter: brightness(0) invert(1) !important;
    transition: all 0.3s ease !important;
    vertical-align: middle !important;
}

/* 滚动时Logo颜色变化 */
header.scrolled .navbar-brand .logo {
    filter: none !important;
}

/* 导航菜单容器对齐 */
.navbar-nav {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    margin: 0 !important;
    list-style: none !important;
    padding: 0 !important;
}

/* 导航链接样式 */
.navbar-nav .nav-item {
    margin: 0 5px !important;
}

.navbar-nav .nav-link {
    color: white !important;
    font-weight: 400 !important;
    padding: 12px 20px !important;
    position: relative !important;
    transition: color 0.3s ease !important;
    font-size: 15px !important;
    background: none !important;
    border: none !important;
    display: flex !important;
    align-items: center !important;
}

/* 导航链接悬停效果 */
.navbar-nav .nav-link:hover {
    color: #ff6b35 !important;
}

/* 导航链接下划线效果 - 排除下拉菜单项 */
.navbar-nav .nav-link:not(.dropdown-toggle)::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:not(.dropdown-toggle):hover::after {
    width: 80%;
}

/* 激活状态的导航链接样式 */
.navbar-nav .nav-link.active {
    color: #ff6b35 !important;
}

.navbar-nav .nav-link.active:not(.dropdown-toggle)::after {
    width: 80%;
    background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
}

/* 右侧按钮组对齐 */
.navbar-actions {
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
    margin-left: 20px !important;
    height: 45px !important;
    flex-shrink: 0 !important;
}

.search-btn {
    color: white !important;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    text-decoration: none;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white !important;
    text-decoration: none;
}

.search-btn svg {
    width: 16px;
    height: 16px;
    stroke: currentColor;
    stroke-width: 2;
}

/* 登录注册按钮样式 */
.navbar-actions .btn {
    padding: 8px 18px !important;
    font-size: 14px !important;
    border-radius: 4px !important;
    font-weight: 400 !important;
    transition: color 0.3s ease, background 0.3s ease, border-color 0.3s ease !important;
    text-decoration: none !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.navbar-actions .btn-outline-light {
    color: white !important;
    background: transparent !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
}

.navbar-actions .btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: white !important;
}

.navbar-actions .btn-primary {
    background: #ff6b35 !important;
    border-color: #ff6b35 !important;
    color: white !important;
}

.navbar-actions .btn-primary:hover {
    background: #e55a2b !important;
    border-color: #e55a2b !important;
}

/* 滚动时按钮样式调整 */
header.scrolled .navbar-actions .btn-outline-light {
    color: white !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
}

header.scrolled .navbar-actions .btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: white !important;
}

header.scrolled .navbar-actions .btn-primary {
    background: #ff6b35 !important;
    border-color: #ff6b35 !important;
    color: white !important;
}

header.scrolled .navbar-actions .btn-primary:hover {
    background: #e55a2b !important;
    border-color: #e55a2b !important;
    color: white !important;
}

/* 强制确保滚动时按钮样式不被覆盖 */
header.scrolled .btn-primary,
header.scrolled .navbar-actions .btn-primary,
header.scrolled .navbar .btn-primary {
    background: #ff6b35 !important;
    background-color: #ff6b35 !important;
    border-color: #ff6b35 !important;
    color: white !important;
}

header.scrolled .btn-primary:hover,
header.scrolled .navbar-actions .btn-primary:hover,
header.scrolled .navbar .btn-primary:hover {
    background: #e55a2b !important;
    background-color: #e55a2b !important;
    border-color: #e55a2b !important;
    color: white !important;
}

/* 移动端菜单按钮样式 */
.navbar-toggler {
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    padding: 4px 8px !important;
    transition: all 0.3s ease !important;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
    transition: all 0.3s ease !important;
}

/* 确保主要内容不被固定header遮挡 */
main {
    padding-top: 70px;
}

/* 首页轮播图需要全屏显示，移除顶部间距 */
.home-page main {
    padding-top: 0;
}

/* ===== 产品菜单特殊样式 ===== */
.products-menu {
    padding: 0;
}

.products-header {
    background: #f8f9fa;
    padding: 25px 30px;
    border-bottom: 1px solid #e9ecef;
    border-radius: 16px 16px 0 0;
}

/* 产品网格布局 */
.products-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
    padding: 30px;
}

.products-column {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.product-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-radius: 10px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
    border: 1px solid rgba(102, 126, 234, 0.1);
    text-decoration: none;
    color: inherit;
}

.product-item:hover {
    background: rgba(102, 126, 234, 0.12);
    border-color: rgba(102, 126, 234, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
    text-decoration: none;
    color: inherit;
}

.product-icon {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.product-icon.purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
}

.product-icon i {
    font-size: 18px;
    color: white;
}

/* Lucide图标样式 */
.product-icon svg {
    width: 18px;
    height: 18px;
    color: white;
    stroke: white;
    stroke-width: 2;
}

.product-item:hover .product-icon {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.35);
}

.product-name {
    font-size: 0.9rem;
    font-weight: 500;
    color: #333;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.product-item:hover .product-name {
    color: #667eea;
}

/* ===== 菜单分类标题 ===== */
.mega-menu-category {
    font-size: 1.1rem;
    font-weight: 600;
    color: #007bff;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid rgba(0, 123, 255, 0.2);
    position: relative;
}

.mega-menu-category::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 30px;
    height: 2px;
    background: #007bff;
    border-radius: 1px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .solutions-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 900px) {
    .solutions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 600px) {
    .solutions-grid {
        grid-template-columns: 1fr;
    }
    .products-grid {
        grid-template-columns: 1fr;
    }
}
</style>

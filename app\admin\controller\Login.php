<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\BaseController;
use think\Request;
use think\facade\Session;
use think\facade\Validate;

/**
 * 后台登录控制器
 */
class Login extends BaseController
{
    /**
     * 登录页面
     */
    public function index()
    {
        // 确保session已启动
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // 如果已经登录，跳转到后台首页
        if (isset($_SESSION['admin_user']) && !empty($_SESSION['admin_user'])) {
            header('Location: /admin');
            exit;
        }

        return view('admin/login');
    }
    
    /**
     * 处理登录
     */
    public function doLogin(Request $request)
    {
        if (!$request->isPost()) {
            return $this->error('请求方式错误');
        }

        $data = $request->post();

        // 验证数据
        $validate = Validate::rule([
            'username' => 'require',
            'password' => 'require'
        ])->message([
            'username.require' => '用户名不能为空',
            'password.require' => '密码不能为空'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        // 验证用户名和密码（这里使用固定的管理员账号）
        $adminUsername = 'admin';
        $adminPassword = 'password';

        if ($data['username'] !== $adminUsername || $data['password'] !== $adminPassword) {
            return $this->error('用户名或密码错误');
        }

        // 登录成功，保存session
        // 确保session已启动
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        $_SESSION['admin_user'] = [
            'id' => 1,
            'username' => $adminUsername,
            'real_name' => '系统管理员',
            'login_time' => time(),
            'login_ip' => $request->ip()
        ];

        // 测试session是否保存成功
        if (empty($_SESSION['admin_user'])) {
            return $this->error('Session保存失败');
        }

        return $this->success('登录成功', [], '/admin/index');
    }
    
    /**
     * 退出登录
     */
    public function logout()
    {
        // 确保session已启动
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // 清除admin_user session
        unset($_SESSION['admin_user']);

        // 可选：销毁整个session
        // session_destroy();

        header('Location: /admin/login');
        exit;
    }
    
    /**
     * 成功响应
     */
    protected function success($msg = '操作成功', $data = [], $url = '')
    {
        return json([
            'code' => 1,
            'msg' => $msg,
            'data' => $data,
            'url' => $url
        ]);
    }
    
    /**
     * 错误响应
     */
    protected function error($msg = '操作失败', $data = [], $url = '')
    {
        return json([
            'code' => 0,
            'msg' => $msg,
            'data' => $data,
            'url' => $url
        ]);
    }
}

/**
 * 统计数字组件 (Stats Component)
 * 专业的数据统计展示组件，支持多个统计项的可视化展示，带有动画效果
 */

// 统计数字样式预设 - 借鉴卡片组件的设计模式
const statsStylePresets = [
    { name: '商务专业', bgColor: '#ffffff', numberColor: '#2d3748', labelColor: '#4a5568', iconColor: '#667eea', borderColor: '#e2e8f0', template: 'default' },
    { name: '科技蓝调', bgColor: '#1a202c', numberColor: '#4299e1', labelColor: '#a0aec0', iconColor: '#63b3ed', borderColor: '#2d3748', template: 'default' },
    { name: '温暖橙色', bgColor: '#fff5f5', numberColor: '#c53030', labelColor: '#2d3748', iconColor: '#ed8936', borderColor: '#fed7d7', template: 'default' },
    { name: '自然绿意', bgColor: '#f0fff4', numberColor: '#22543d', labelColor: '#2d3748', iconColor: '#38a169', borderColor: '#c6f6d5', template: 'default' },
    { name: '优雅紫韵', bgColor: '#faf5ff', numberColor: '#553c9a', labelColor: '#2d3748', iconColor: '#805ad5', borderColor: '#e9d8fd', template: 'default' },
    { name: '现代灰调', bgColor: '#f7fafc', numberColor: '#1a202c', labelColor: '#4a5568', iconColor: '#718096', borderColor: '#e2e8f0', template: 'default' },
    { name: '实力见证', bgColor: 'rgba(255, 255, 255, 0.05)', numberColor: '#ffffff', labelColor: '#ffffff', iconColor: '#10d5c2', borderColor: 'rgba(255, 255, 255, 0.1)', template: 'strength' }
];

// 统计图标库
const statsIconLibrary = [
    '👥', '💼', '❤️', '🎧', '🚀', '⭐', '📊', '💡', '🎯', '🏆',
    '📈', '💰', '🌟', '🔥', '⚡', '🎨', '🛡️', '🌍', '📱', '💎'
];

// 实力见证专用Lucide图标库
const strengthIconLibrary = [
    { icon: 'users', emoji: '👥', name: '成功案例' },
    { icon: 'handshake', emoji: '🤝', name: '合作伙伴' },
    { icon: 'link', emoji: '🔗', name: '专业团队' },
    { icon: 'shield', emoji: '🛡️', name: '服务年限' },
    { icon: 'trophy', emoji: '🏆', name: '获奖数量' },
    { icon: 'star', emoji: '⭐', name: '客户评价' },
    { icon: 'rocket', emoji: '🚀', name: '项目完成' },
    { icon: 'heart', emoji: '❤️', name: '满意度' },
    { icon: 'clock', emoji: '🕐', name: '响应时间' },
    { icon: 'award', emoji: '📜', name: '认证资质' }
];

// 统计数字组件模板
const statsComponent = {
    name: '统计数字',
    html: `<div class="stats-wrapper">
        <div class="stats-background"></div>
        <div class="stats-overlay"></div>
        <div class="stats-content">
            <div class="stats-container">
                <div class="stats-item">
                    <div class="stats-icon">👥</div>
                    <div class="stats-number" data-target="1000">0</div>
                    <div class="stats-label">客户数量</div>
                </div>
                <div class="stats-item">
                    <div class="stats-icon">💼</div>
                    <div class="stats-number" data-target="50">0</div>
                    <div class="stats-label">项目经验</div>
                </div>
                <div class="stats-item">
                    <div class="stats-icon">❤️</div>
                    <div class="stats-number" data-target="99">0</div>
                    <div class="stats-label">满意度%</div>
                </div>
                <div class="stats-item">
                    <div class="stats-icon">🎧</div>
                    <div class="stats-number" data-target="24">0</div>
                    <div class="stats-label">小时支持</div>
                </div>
            </div>
        </div>
    </div>`,
    render: function(component, properties) {
        updateStatsDisplay(component, properties);
    },
    properties: {
        // 统计项数据数组
        stats: [
            { number: '500', label: '成功案例', icon: '👥', iconType: 'emoji', suffix: '' },
            { number: '1000', label: '合作伙伴', icon: '🤝', iconType: 'emoji', suffix: '' },
            { number: '50', label: '专业团队', icon: '🔗', iconType: 'emoji', suffix: '' },
            { number: '10', label: '服务年限', icon: '🛡️', iconType: 'emoji', suffix: '' }
        ],

        // 布局设置
        columnsCount: 4, // 1, 2, 3, 4, 5, 6
        layout: 'horizontal', // horizontal, vertical, grid
        alignment: 'center', // left, center, right

        // 全局样式设置
        styleTemplate: 'default', // default, strength
        stylePreset: 'business-professional',
        bgColor: '#ffffff',
        numberColor: '#2d3748',
        labelColor: '#4a5568',
        iconColor: '#667eea',
        borderColor: '#e2e8f0',

        // 实力见证模板专用设置
        strengthBgType: 'combined', // combined, gradient, color, image
        strengthBgColor: '#1a1a2e', // 基础背景色
        strengthBgGradient: 'linear-gradient(135deg, rgba(26, 26, 46, 0.9) 0%, rgba(16, 213, 194, 0.1) 100%)', // 叠加渐变
        strengthBgImage: '/assets/images/index_section6_bg.png', // 背景图片
        strengthBgImageOpacity: 0.3, // 背景图片透明度
        strengthTitle: '实力见证',
        strengthSubtitle: '用数据说话，用实力证明',
        strengthIconSize: 48, // 实力见证模板专用图标大小
        showTrustIndicators: true,
        trustIndicators: [
            { icon: 'shield', text: '安全可靠' },
            { icon: 'clock', text: '7×24服务' },
            { icon: 'award', text: '权威认证' },
            { icon: 'thumbs-up', text: '客户满意' }
        ],

        // 尺寸设置
        maxWidth: 1200,
        borderRadius: 12,
        padding: 32,
        itemSpacing: 24,
        marginHorizontal: 20,
        marginVertical: 20,
        positionVertical: 0,

        // 字体设置
        numberSize: 36,
        labelSize: 16,
        iconSize: 48,
        fontWeight: 'bold', // normal, bold, bolder

        // 边框和阴影设置
        showBorder: false,
        borderWidth: 1,
        shadow: true,
        shadowIntensity: 0.1,

        // 动画设置
        animation: false, // 默认关闭动画
        animationDuration: 2000,
        animationDelay: 200,

        // 响应式设置
        mobileColumns: 2,
        tabletColumns: 3
    }
};

// 生成统计数字组件属性面板
function generateStatsProperties(component) {
    // 获取或初始化组件属性
    let props;
    if (component._statsProperties) {
        props = component._statsProperties;
    } else {
        props = JSON.parse(JSON.stringify(statsComponent.properties));
        component._statsProperties = props;
    }

    const html = `
        <!-- 样式模板选择 -->
        <div class="property-section">
            <h4 class="section-title">样式模板</h4>
            <div class="property-group">
                <label class="property-label">选择模板</label>
                <div class="layout-buttons-clean">
                    <button type="button" class="layout-btn ${props.styleTemplate === 'default' ? 'active' : ''}"
                            onclick="updateStatsStyleTemplate('${component.id}', 'default')">
                        默认样式
                    </button>
                    <button type="button" class="layout-btn ${props.styleTemplate === 'strength' ? 'active' : ''}"
                            onclick="updateStatsStyleTemplate('${component.id}', 'strength')">
                        实力见证
                    </button>
                </div>
            </div>
        </div>

        <!-- 样式预设 -->
        ${props.styleTemplate !== 'strength' ? `
        <div class="property-section">
            <h4 class="section-title">快速样式</h4>
            <div class="style-presets">
                ${statsStylePresets.filter(preset => preset.template === props.styleTemplate).map((preset) => `
                    <div class="style-preset ${props.stylePreset === preset.name.toLowerCase().replace(/\s+/g, '-') ? 'active' : ''}"
                         onclick="applyStatsStylePreset('${component.id}', ${statsStylePresets.indexOf(preset)})"
                         style="background: ${preset.bgColor}; border-color: ${preset.borderColor};">
                        <div class="preset-demo">
                            <span style="color: ${preset.numberColor}; font-weight: bold;">99</span>
                            <span style="color: ${preset.labelColor}; font-size: 12px;">示例</span>
                        </div>
                        <div class="preset-name">${preset.name}</div>
                    </div>
                `).join('')}
            </div>
        </div>
        ` : ''}

        ${props.styleTemplate === 'strength' ? `
        <!-- 实力见证设置 -->
        <div class="property-section">
            <h4 class="section-title">实力见证设置</h4>

            <div class="property-group">
                <label class="property-label">背景类型</label>
                <div class="layout-buttons-clean">
                    <button type="button" class="layout-btn ${props.strengthBgType === 'combined' ? 'active' : ''}"
                            onclick="updateStatsProperty('${component.id}', 'strengthBgType', 'combined')">
                        组合
                    </button>
                    <button type="button" class="layout-btn ${props.strengthBgType === 'gradient' ? 'active' : ''}"
                            onclick="updateStatsProperty('${component.id}', 'strengthBgType', 'gradient')">
                        渐变
                    </button>
                    <button type="button" class="layout-btn ${props.strengthBgType === 'color' ? 'active' : ''}"
                            onclick="updateStatsProperty('${component.id}', 'strengthBgType', 'color')">
                        纯色
                    </button>
                    <button type="button" class="layout-btn ${props.strengthBgType === 'image' ? 'active' : ''}"
                            onclick="updateStatsProperty('${component.id}', 'strengthBgType', 'image')">
                        图片
                    </button>
                </div>
            </div>

            ${props.strengthBgType === 'combined' ? `
                <div class="property-group">
                    <label class="property-label">基础背景色</label>
                    <input type="color" class="property-input" value="${props.strengthBgColor}"
                           onchange="updateStatsProperty('${component.id}', 'strengthBgColor', this.value)">
                </div>

                <div class="property-group">
                    <label class="property-label">背景图片URL</label>
                    <input type="text" class="property-input" value="${props.strengthBgImage}"
                           onchange="updateStatsProperty('${component.id}', 'strengthBgImage', this.value)"
                           placeholder="输入图片URL">
                </div>

                <div class="property-group">
                    <label class="property-label">图片透明度</label>
                    <input type="range" class="property-slider" min="0" max="1" step="0.1" value="${props.strengthBgImageOpacity}"
                           onchange="updateStatsProperty('${component.id}', 'strengthBgImageOpacity', parseFloat(this.value))">
                    <span class="slider-value">${props.strengthBgImageOpacity}</span>
                </div>
            ` : ''}

            ${props.strengthBgType === 'color' ? `
                <div class="property-group">
                    <label class="property-label">背景颜色</label>
                    <input type="color" class="property-input" value="${props.strengthBgColor}"
                           onchange="updateStatsProperty('${component.id}', 'strengthBgColor', this.value)">
                </div>
            ` : ''}

            ${props.strengthBgType === 'image' ? `
                <div class="property-group">
                    <label class="property-label">背景图片URL</label>
                    <input type="text" class="property-input" value="${props.strengthBgImage}"
                           onchange="updateStatsProperty('${component.id}', 'strengthBgImage', this.value)"
                           placeholder="输入图片URL">
                </div>
            ` : ''}

            <div class="property-group">
                <label class="property-label">主标题</label>
                <input type="text" class="property-input" value="${props.strengthTitle}"
                       onchange="updateStatsProperty('${component.id}', 'strengthTitle', this.value)">
            </div>

            <div class="property-group">
                <label class="property-label">副标题</label>
                <input type="text" class="property-input" value="${props.strengthSubtitle}"
                       onchange="updateStatsProperty('${component.id}', 'strengthSubtitle', this.value)">
            </div>

            <div class="property-group">
                <label class="property-label">图标大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.strengthIconSize}" min="32" max="72" step="4"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateStatsPropertySmooth('${component.id}', 'strengthIconSize', this.value)"
                           onchange="updateStatsProperty('${component.id}', 'strengthIconSize', this.value)">
                    <span class="range-value">${props.strengthIconSize}px</span>
                </div>
            </div>

            <div class="property-group">
                <div class="checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" ${props.showTrustIndicators ? 'checked' : ''}
                               onchange="updateStatsProperty('${component.id}', 'showTrustIndicators', this.checked)">
                        显示信任标识
                    </label>
                </div>
            </div>
        </div>
        ` : ''}

        <!-- 布局设置 -->
        ${props.styleTemplate !== 'strength' ? `
        <div class="property-section">
            <h4 class="section-title">布局设置</h4>

            <div class="property-group">
                <label class="property-label">列数设置</label>
                <div class="stats-columns-grid">
                    ${[1, 2, 3, 4, 5, 6].map(num => `
                        <button type="button" class="stats-column-btn ${props.columnsCount === num ? 'active' : ''}"
                                onclick="updateStatsColumns('${component.id}', ${num})">
                            ${num}列
                        </button>
                    `).join('')}
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">对齐方式</label>
                <div class="layout-buttons-clean">
                    ${[
                        { value: 'left', label: '左对齐' },
                        { value: 'center', label: '居中' },
                        { value: 'right', label: '右对齐' }
                    ].map(align => `
                        <button type="button" class="layout-btn ${props.alignment === align.value ? 'active' : ''}"
                                onclick="updateStatsProperty('${component.id}', 'alignment', '${align.value}')">
                            ${align.label}
                        </button>
                    `).join('')}
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">最大宽度</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.maxWidth}" min="600" max="1400" step="50"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateStatsPropertySmooth('${component.id}', 'maxWidth', this.value)"
                           onchange="updateStatsProperty('${component.id}', 'maxWidth', this.value)">
                    <span class="range-value">${props.maxWidth}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">项目间距</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.itemSpacing}" min="8" max="60" step="4"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateStatsPropertySmooth('${component.id}', 'itemSpacing', this.value)"
                           onchange="updateStatsProperty('${component.id}', 'itemSpacing', this.value)">
                    <span class="range-value">${props.itemSpacing}px</span>
                </div>
            </div>
        </div>
        ` : ''}
    `;

    return html + `
        <!-- 统计项内容管理 -->
        <div class="property-section">
            <h4 class="section-title">统计项内容 (${props.stats.length}个)</h4>

            <div class="stats-manager">
                ${props.stats.map((stat, index) => `
                    <div class="stat-editor" data-index="${index}">
                        <div class="stat-editor-header">
                            <span class="stat-editor-title">统计项 ${index + 1}</span>
                            <div class="stat-editor-actions">
                                <button type="button" class="card-action-btn" onclick="toggleStatEditor(this)">
                                    <span class="toggle-icon">▼</span>
                                </button>
                                ${props.stats.length > 1 ? `
                                    <button type="button" class="card-action-btn delete" onclick="deleteStat('${component.id}', ${index})">
                                        ✕
                                    </button>
                                ` : ''}
                            </div>
                        </div>

                        <div class="stat-editor-content">
                            <div class="property-group">
                                <label class="property-label">数字</label>
                                <input type="text" class="property-input" value="${stat.number}"
                                       onchange="updateStatData('${component.id}', ${index}, 'number', this.value)"
                                       placeholder="如: 1000+, 99%, 24/7">
                            </div>

                            <div class="property-group">
                                <label class="property-label">标签</label>
                                <input type="text" class="property-input" value="${stat.label}"
                                       onchange="updateStatData('${component.id}', ${index}, 'label', this.value)"
                                       placeholder="如: 客户数量">
                            </div>

                            <div class="property-group">
                                <label class="property-label">图标选择</label>
                                <div class="stats-icon-selector">
                                    <div class="stats-current-icon" onclick="toggleStatsIconPicker(this, ${index})">
                                        <span class="stats-icon-display">
                                            ${stat.iconType === 'lucide'
                                                ? `<i data-lucide="${stat.icon}"></i>`
                                                : stat.icon}
                                        </span>
                                        <span class="stats-icon-arrow">▼</span>
                                    </div>
                                    <div class="stats-icon-picker" id="iconPicker${index}" style="display: none;">
                                        ${props.styleTemplate === 'strength' ? `
                                            <div class="stats-icon-tabs">
                                                <button type="button" class="icon-tab active" onclick="switchIconTab(this, 'lucide')">Lucide图标</button>
                                                <button type="button" class="icon-tab" onclick="switchIconTab(this, 'emoji')">Emoji</button>
                                            </div>
                                            <div class="stats-icon-grid" data-tab="lucide">
                                                ${strengthIconLibrary.map(iconData => `
                                                    <div class="stats-icon-option ${stat.icon === iconData.icon ? 'selected' : ''}"
                                                         onclick="selectStatIcon('${component.id}', ${index}, '${iconData.icon}', 'lucide')"
                                                         title="${iconData.name}">
                                                        <i data-lucide="${iconData.icon}"></i>
                                                    </div>
                                                `).join('')}
                                            </div>
                                            <div class="stats-icon-grid" data-tab="emoji" style="display: none;">
                                                ${statsIconLibrary.map(icon => `
                                                    <div class="stats-icon-option ${stat.icon === icon ? 'selected' : ''}"
                                                         onclick="selectStatIcon('${component.id}', ${index}, '${icon}', 'emoji')">
                                                        <span class="stats-icon-emoji">${icon}</span>
                                                    </div>
                                                `).join('')}
                                            </div>
                                        ` : `
                                            <div class="stats-icon-grid">
                                                ${statsIconLibrary.map(icon => `
                                                    <div class="stats-icon-option ${stat.icon === icon ? 'selected' : ''}"
                                                         onclick="selectStatIcon('${component.id}', ${index}, '${icon}', 'emoji')">
                                                        <span class="stats-icon-emoji">${icon}</span>
                                                    </div>
                                                `).join('')}
                                            </div>
                                        `}
                                    </div>
                                </div>
                            </div>

                            <div class="property-group">
                                <label class="property-label">后缀</label>
                                <input type="text" class="property-input" value="${stat.suffix}"
                                       onchange="updateStatData('${component.id}', ${index}, 'suffix', this.value)"
                                       placeholder="如: %, +, 万">
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>

            <div class="stats-actions">
                <button type="button" class="add-stat-btn" onclick="addStat('${component.id}')">
                    + 添加统计项
                </button>
            </div>
        </div>

        <!-- 颜色设置 -->
        ${props.styleTemplate !== 'strength' ? `
        <div class="property-section">
            <h4 class="section-title">颜色设置</h4>

            <div class="property-group">
                <div class="color-row">
                    <div class="color-item">
                        <label>背景色</label>
                        <input type="color" class="property-input" value="${props.bgColor}"
                               onchange="updateStatsProperty('${component.id}', 'bgColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>数字色</label>
                        <input type="color" class="property-input" value="${props.numberColor}"
                               onchange="updateStatsProperty('${component.id}', 'numberColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>标签色</label>
                        <input type="color" class="property-input" value="${props.labelColor}"
                               onchange="updateStatsProperty('${component.id}', 'labelColor', this.value)">
                    </div>
                </div>
                <div class="color-row" style="margin-top: 8px;">
                    <div class="color-item">
                        <label>图标色</label>
                        <input type="color" class="property-input" value="${props.iconColor}"
                               onchange="updateStatsProperty('${component.id}', 'iconColor', this.value)">
                    </div>
                    <div class="color-item">
                        <label>边框色</label>
                        <input type="color" class="property-input" value="${props.borderColor}"
                               onchange="updateStatsProperty('${component.id}', 'borderColor', this.value)">
                    </div>
                </div>
            </div>
        </div>
        ` : ''}

        <!-- 字体设置 -->
        ${props.styleTemplate !== 'strength' ? `
        <div class="property-section">
            <h4 class="section-title">字体设置</h4>

            <div class="property-group">
                <label class="property-label">数字大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.numberSize}" min="20" max="60" step="2"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateStatsPropertySmooth('${component.id}', 'numberSize', this.value)"
                           onchange="updateStatsProperty('${component.id}', 'numberSize', this.value)">
                    <span class="range-value">${props.numberSize}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">标签大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.labelSize}" min="12" max="24" step="1"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateStatsPropertySmooth('${component.id}', 'labelSize', this.value)"
                           onchange="updateStatsProperty('${component.id}', 'labelSize', this.value)">
                    <span class="range-value">${props.labelSize}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">图标大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.iconSize}" min="24" max="72" step="4"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateStatsPropertySmooth('${component.id}', 'iconSize', this.value)"
                           onchange="updateStatsProperty('${component.id}', 'iconSize', this.value)">
                    <span class="range-value">${props.iconSize}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">字体粗细</label>
                <div class="layout-buttons-clean">
                    ${[
                        { value: 'normal', label: '正常' },
                        { value: 'bold', label: '粗体' },
                        { value: 'bolder', label: '更粗' }
                    ].map(weight => `
                        <button type="button" class="layout-btn ${props.fontWeight === weight.value ? 'active' : ''}"
                                onclick="updateStatsProperty('${component.id}', 'fontWeight', '${weight.value}')">
                            ${weight.label}
                        </button>
                    `).join('')}
                </div>
            </div>
        </div>
        ` : ''}

        <!-- 边框和阴影设置 -->
        ${props.styleTemplate !== 'strength' ? `
        <div class="property-section">
            <h4 class="section-title">边框和阴影</h4>

            <div class="property-group">
                <div class="checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" ${props.showBorder ? 'checked' : ''}
                               onchange="updateStatsProperty('${component.id}', 'showBorder', this.checked)">
                        显示边框
                    </label>
                </div>
            </div>

            ${props.showBorder ? `
                <div class="property-group">
                    <label class="property-label">边框宽度</label>
                    <div class="range-control">
                        <input type="range" class="range-slider" value="${props.borderWidth}" min="1" max="5" step="1"
                               oninput="this.nextElementSibling.textContent = this.value + 'px'; updateStatsPropertySmooth('${component.id}', 'borderWidth', this.value)"
                               onchange="updateStatsProperty('${component.id}', 'borderWidth', this.value)">
                        <span class="range-value">${props.borderWidth}px</span>
                    </div>
                </div>
            ` : ''}

            <div class="property-group">
                <div class="checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" ${props.shadow ? 'checked' : ''}
                               onchange="updateStatsProperty('${component.id}', 'shadow', this.checked)">
                        显示阴影
                    </label>
                </div>
            </div>

            ${props.shadow ? `
                <div class="property-group">
                    <label class="property-label">阴影强度</label>
                    <div class="range-control">
                        <input type="range" class="range-slider" value="${props.shadowIntensity}" min="0" max="0.3" step="0.05"
                               oninput="this.nextElementSibling.textContent = Math.round(this.value * 100) + '%'; updateStatsPropertySmooth('${component.id}', 'shadowIntensity', this.value)"
                               onchange="updateStatsProperty('${component.id}', 'shadowIntensity', this.value)">
                        <span class="range-value">${Math.round(props.shadowIntensity * 100)}%</span>
                    </div>
                </div>
            ` : ''}
        </div>
        ` : `
        <!-- 实力见证阴影设置 -->
        <div class="property-section">
            <h4 class="section-title">阴影设置</h4>
            <div class="property-group">
                <div class="checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" ${props.shadow ? 'checked' : ''}
                               onchange="updateStatsProperty('${component.id}', 'shadow', this.checked)">
                        显示阴影效果
                    </label>
                </div>
            </div>
        </div>
        `}

        <!-- 动画设置 -->
        <div class="property-section">
            <h4 class="section-title">动画设置</h4>

            <div class="property-group">
                <div class="checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" ${props.animation ? 'checked' : ''}
                               onchange="updateStatsProperty('${component.id}', 'animation', this.checked)">
                        启用数字动画
                    </label>
                </div>
            </div>

            ${props.animation && props.styleTemplate !== 'strength' ? `
                <div class="property-group">
                    <label class="property-label">动画时长</label>
                    <div class="range-control">
                        <input type="range" class="range-slider" value="${props.animationDuration}" min="500" max="5000" step="250"
                               oninput="this.nextElementSibling.textContent = this.value + 'ms'; updateStatsPropertySmooth('${component.id}', 'animationDuration', this.value)"
                               onchange="updateStatsProperty('${component.id}', 'animationDuration', this.value)">
                        <span class="range-value">${props.animationDuration}ms</span>
                    </div>
                </div>

                <div class="property-group">
                    <label class="property-label">动画延迟</label>
                    <div class="range-control">
                        <input type="range" class="range-slider" value="${props.animationDelay}" min="0" max="1000" step="50"
                               oninput="this.nextElementSibling.textContent = this.value + 'ms'; updateStatsPropertySmooth('${component.id}', 'animationDelay', this.value)"
                               onchange="updateStatsProperty('${component.id}', 'animationDelay', this.value)">
                        <span class="range-value">${props.animationDelay}ms</span>
                    </div>
                </div>
            ` : ''}
        </div>

        <!-- 尺寸和间距 -->
        ${props.styleTemplate !== 'strength' ? `
        <div class="property-section">
            <h4 class="section-title">尺寸和间距</h4>

            <div class="property-group">
                <label class="property-label">内边距</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.padding}" min="16" max="60" step="4"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateStatsPropertySmooth('${component.id}', 'padding', this.value)"
                           onchange="updateStatsProperty('${component.id}', 'padding', this.value)">
                    <span class="range-value">${props.padding}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">圆角大小</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.borderRadius}" min="0" max="30" step="2"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateStatsPropertySmooth('${component.id}', 'borderRadius', this.value)"
                           onchange="updateStatsProperty('${component.id}', 'borderRadius', this.value)">
                    <span class="range-value">${props.borderRadius}px</span>
                </div>
            </div>

            <div class="property-group">
                <label class="property-label">垂直位置</label>
                <div class="range-control">
                    <input type="range" class="range-slider" value="${props.positionVertical}" min="-100" max="100" step="5"
                           oninput="this.nextElementSibling.textContent = this.value + 'px'; updateStatsPropertySmooth('${component.id}', 'positionVertical', this.value)"
                           onchange="updateStatsProperty('${component.id}', 'positionVertical', this.value)">
                    <span class="range-value">${props.positionVertical}px</span>
                </div>
            </div>
        </div>
        ` : ''}
    `;
}

// 更新统计数字属性
function updateStatsProperty(componentId, property, value, skipPanelUpdate = false) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props;
    if (component._statsProperties) {
        props = component._statsProperties;
    } else {
        props = JSON.parse(JSON.stringify(statsComponent.properties));
        component._statsProperties = props;
    }

    // 更新属性值
    if (property === 'maxWidth' || property === 'itemSpacing' || property === 'padding' ||
        property === 'borderRadius' || property === 'positionVertical' || property === 'numberSize' ||
        property === 'labelSize' || property === 'iconSize' || property === 'borderWidth' ||
        property === 'animationDuration' || property === 'animationDelay') {
        props[property] = parseInt(value);
    } else if (property === 'shadowIntensity') {
        props[property] = parseFloat(value);
    } else if (property === 'shadow' || property === 'showBorder' || property === 'animation' ||
               property === 'showTrustIndicators') {
        props[property] = value;
    } else if (property === 'styleTemplate' || property === 'strengthBgType' ||
               property === 'strengthBgColor' || property === 'strengthBgGradient' ||
               property === 'strengthBgImage' || property === 'strengthTitle' ||
               property === 'strengthSubtitle' || property === 'strengthBgImageOpacity' ||
               property === 'strengthIconSize') {
        props[property] = value;
    } else {
        props[property] = value;
    }

    updateStatsDisplay(component, props);

    // 只有在不跳过面板更新时才更新面板
    if (!skipPanelUpdate) {
        updatePropertiesPanel(component);
        // 属性面板更新后立即恢复图标显示状态
        setTimeout(() => {
            updateIconDisplayInPanel(componentId);
        }, 50);
    }
}

// 优化的拖动条更新函数 - 只更新显示，不重新渲染面板
function updateStatsPropertySmooth(componentId, property, value) {
    updateStatsProperty(componentId, property, value, true);
}

// 更新统计数字列数
function updateStatsColumns(componentId, columns) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props;
    if (component._statsProperties) {
        props = component._statsProperties;
    } else {
        props = JSON.parse(JSON.stringify(statsComponent.properties));
        component._statsProperties = props;
    }

    props.columnsCount = columns;

    // 调整统计项数量以匹配列数
    while (props.stats.length < columns) {
        const newStat = {
            number: `${(props.stats.length + 1) * 10}`,
            label: `统计项${props.stats.length + 1}`,
            icon: statsIconLibrary[props.stats.length % statsIconLibrary.length],
            iconType: 'emoji',
            suffix: ''
        };

        // 如果当前是实力见证模板，使用Lucide图标和添加后缀
        if (props.styleTemplate === 'strength') {
            const strengthIcon = strengthIconLibrary[props.stats.length % strengthIconLibrary.length];
            newStat.icon = strengthIcon.icon;
            newStat.iconType = 'lucide';
            newStat.suffix = '+';
        }

        props.stats.push(newStat);
    }

    // 如果统计项数量超过列数，保留前N个
    if (props.stats.length > columns) {
        props.stats = props.stats.slice(0, columns);
    }

    updateStatsDisplay(component, props);
    updatePropertiesPanel(component);
    // 属性面板更新后立即恢复图标显示状态
    setTimeout(() => {
        updateIconDisplayInPanel(componentId);
    }, 50);
}

// 更新样式模板
function updateStatsStyleTemplate(componentId, template) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props;
    if (component._statsProperties) {
        props = component._statsProperties;
    } else {
        props = JSON.parse(JSON.stringify(statsComponent.properties));
        component._statsProperties = props;
    }


    props.styleTemplate = template;

    // 根据模板应用默认样式
    if (template === 'strength') {
        // 实力见证样式
        const strengthPreset = statsStylePresets.find(preset => preset.template === 'strength');
        if (strengthPreset) {
            props.stylePreset = strengthPreset.name.toLowerCase().replace(/\s+/g, '-');
            props.bgColor = strengthPreset.bgColor;
            props.numberColor = strengthPreset.numberColor;
            props.labelColor = strengthPreset.labelColor;
            props.iconColor = strengthPreset.iconColor;
            props.borderColor = strengthPreset.borderColor;
        }

        // 将所有统计项的图标类型改为lucide，并添加后缀
        props.stats.forEach((stat, index) => {
            if (stat.iconType !== 'lucide') {
                // 如果当前图标不是lucide类型，使用对应的lucide图标
                const strengthIcon = strengthIconLibrary.find(iconData => iconData.emoji === stat.icon);
                if (strengthIcon) {
                    stat.icon = strengthIcon.icon;
                    stat.iconType = 'lucide';
                } else {
                    // 如果找不到对应的图标，使用默认的lucide图标
                    stat.icon = strengthIconLibrary[index % strengthIconLibrary.length].icon;
                    stat.iconType = 'lucide';
                }
            }
            // 为实力见证模板添加默认后缀
            if (!stat.suffix || stat.suffix === '') {
                stat.suffix = '+';
            }
        });
    } else {
        // 默认样式
        const defaultPreset = statsStylePresets.find(preset => preset.template === 'default');
        if (defaultPreset) {
            props.stylePreset = defaultPreset.name.toLowerCase().replace(/\s+/g, '-');
            props.bgColor = defaultPreset.bgColor;
            props.numberColor = defaultPreset.numberColor;
            props.labelColor = defaultPreset.labelColor;
            props.iconColor = defaultPreset.iconColor;
            props.borderColor = defaultPreset.borderColor;
        }

        // 将所有统计项的图标类型改为emoji，并清除后缀
        props.stats.forEach((stat, index) => {
            if (stat.iconType === 'lucide') {
                // 如果当前图标是lucide类型，转换为对应的emoji
                const strengthIcon = strengthIconLibrary.find(iconData => iconData.icon === stat.icon);
                if (strengthIcon) {
                    stat.icon = strengthIcon.emoji;
                    stat.iconType = 'emoji';
                } else {
                    // 如果找不到对应的图标，使用默认的emoji图标
                    stat.icon = statsIconLibrary[index % statsIconLibrary.length];
                    stat.iconType = 'emoji';
                }
            }
            // 清除后缀
            stat.suffix = '';
        });
    }

    updateStatsDisplay(component, props);
    updatePropertiesPanel(component);

    // 延迟初始化Lucide图标和恢复图标显示状态
    setTimeout(() => {
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
        updateIconDisplayInPanel(componentId);
    }, 100);
}

// 应用样式预设
function applyStatsStylePreset(componentId, presetIndex) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props;
    if (component._statsProperties) {
        props = component._statsProperties;
    } else {
        props = JSON.parse(JSON.stringify(statsComponent.properties));
        component._statsProperties = props;
    }

    const preset = statsStylePresets[presetIndex];

    // 应用预设样式
    props.stylePreset = preset.name.toLowerCase().replace(/\s+/g, '-');
    props.bgColor = preset.bgColor;
    props.numberColor = preset.numberColor;
    props.labelColor = preset.labelColor;
    props.iconColor = preset.iconColor;
    props.borderColor = preset.borderColor;

    updateStatsDisplay(component, props);
    updatePropertiesPanel(component);
    // 属性面板更新后立即恢复图标显示状态
    setTimeout(() => {
        updateIconDisplayInPanel(componentId);
    }, 50);
}

// 更新统计项数据
function updateStatData(componentId, index, field, value) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props;
    if (component._statsProperties) {
        props = component._statsProperties;
    } else {
        props = JSON.parse(JSON.stringify(statsComponent.properties));
        component._statsProperties = props;
    }

    if (props.stats[index]) {
        props.stats[index][field] = value;
        updateStatsDisplay(component, props);
    }
}

// 更新图标显示状态（独立函数，不影响其他状态）
function updateIconDisplayInPanel(componentId) {
    const component = document.getElementById(componentId);
    if (!component || !component._statsProperties) return;

    const props = component._statsProperties;

    // 更新每个统计项的图标显示
    props.stats.forEach((stat, index) => {
        const iconDisplay = document.querySelector(`[data-index="${index}"] .stats-icon-display`);
        if (iconDisplay) {
            if (stat.iconType === 'lucide') {
                iconDisplay.innerHTML = `<i data-lucide="${stat.icon}"></i>`;
            } else {
                iconDisplay.textContent = stat.icon;
            }
        }
    });

    // 重新初始化Lucide图标
    if (typeof lucide !== 'undefined') {
        setTimeout(() => lucide.createIcons(), 10);
    }
}

// 选择统计项图标
function selectStatIcon(componentId, index, icon, iconType = 'emoji') {
    // 直接更新数据，不触发属性面板重新生成
    const component = document.getElementById(componentId);
    if (!component) return;

    let props;
    if (component._statsProperties) {
        props = component._statsProperties;
    } else {
        props = JSON.parse(JSON.stringify(statsComponent.properties));
        component._statsProperties = props;
    }

    // 更新统计项数据
    if (props.stats[index]) {
        props.stats[index].icon = icon;
        props.stats[index].iconType = iconType;
    }

    // 关闭图标选择器
    const iconPicker = document.getElementById(`iconPicker${index}`);
    if (iconPicker) {
        iconPicker.style.display = 'none';
        // 移除父级统计项的CSS类
        const parentEditor = iconPicker.closest('.stat-editor');
        if (parentEditor) {
            parentEditor.classList.remove('icon-picker-open');
        }
    }

    // 更新当前图标显示
    const iconDisplay = document.querySelector(`[data-index="${index}"] .stats-icon-display`);
    if (iconDisplay) {
        if (iconType === 'lucide') {
            iconDisplay.innerHTML = `<i data-lucide="${icon}"></i>`;
            // 重新初始化Lucide图标
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        } else {
            iconDisplay.textContent = icon;
        }
    }

    // 重置箭头状态
    const arrow = document.querySelector(`[data-index="${index}"] .stats-icon-arrow`);
    if (arrow) {
        arrow.textContent = '▼';
    }

    // 只更新显示，不重新生成属性面板
    updateStatsDisplay(component, props);
}

// 切换图标选择器显示
function toggleStatsIconPicker(element, index) {
    // 关闭所有其他图标选择器并移除CSS类
    document.querySelectorAll('.stats-icon-picker').forEach(picker => {
        if (picker.id !== `iconPicker${index}`) {
            picker.style.display = 'none';
            // 移除父级统计项的CSS类
            const parentEditor = picker.closest('.stat-editor');
            if (parentEditor) {
                parentEditor.classList.remove('icon-picker-open');
            }
        }
    });

    // 重置所有箭头
    document.querySelectorAll('.stats-icon-arrow').forEach(arrow => {
        arrow.textContent = '▼';
    });

    // 切换当前图标选择器
    const iconPicker = document.getElementById(`iconPicker${index}`);
    if (iconPicker) {
        const isVisible = iconPicker.style.display === 'block';
        iconPicker.style.display = isVisible ? 'none' : 'block';

        // 获取父级统计项编辑器
        const parentEditor = iconPicker.closest('.stat-editor');

        if (isVisible) {
            // 关闭时移除CSS类
            if (parentEditor) {
                parentEditor.classList.remove('icon-picker-open');
            }
        } else {
            // 打开时添加CSS类
            if (parentEditor) {
                parentEditor.classList.add('icon-picker-open');
            }

            // 打开图标选择器时初始化Lucide图标
            setTimeout(() => {
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            }, 50);
        }

        // 更新箭头方向
        const arrow = element.querySelector('.stats-icon-arrow');
        if (arrow) {
            arrow.textContent = isVisible ? '▼' : '▲';
        }
    }
}

// 切换图标标签
function switchIconTab(button, tabType) {
    const picker = button.closest('.stats-icon-picker');
    if (!picker) return;

    // 更新标签按钮状态
    const tabs = picker.querySelectorAll('.icon-tab');
    tabs.forEach(tab => tab.classList.remove('active'));
    button.classList.add('active');

    // 切换图标网格显示
    const grids = picker.querySelectorAll('.stats-icon-grid');
    grids.forEach(grid => {
        if (grid.dataset.tab === tabType) {
            grid.style.display = 'grid';
        } else {
            grid.style.display = 'none';
        }
    });

    // 如果切换到Lucide图标标签，重新初始化图标
    if (tabType === 'lucide') {
        setTimeout(() => {
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }, 50);
    }
}

// 点击外部关闭图标选择器
document.addEventListener('click', function(event) {
    if (!event.target.closest('.stats-icon-selector')) {
        document.querySelectorAll('.stats-icon-picker').forEach(picker => {
            picker.style.display = 'none';
            // 移除父级统计项的CSS类
            const parentEditor = picker.closest('.stat-editor');
            if (parentEditor) {
                parentEditor.classList.remove('icon-picker-open');
            }
        });
        document.querySelectorAll('.stats-icon-arrow').forEach(arrow => {
            arrow.textContent = '▼';
        });
    }
});

// 切换统计项编辑器
function toggleStatEditor(button) {
    const content = button.closest('.stat-editor').querySelector('.stat-editor-content');
    const icon = button.querySelector('.toggle-icon');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        icon.textContent = '▼';
    } else {
        content.style.display = 'none';
        icon.textContent = '▶';
    }
}

// 添加统计项
function addStat(componentId) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props;
    if (component._statsProperties) {
        props = component._statsProperties;
    } else {
        props = JSON.parse(JSON.stringify(statsComponent.properties));
        component._statsProperties = props;
    }

    if (props.stats.length < 6) { // 最多6个统计项
        const newStat = {
            number: `${(props.stats.length + 1) * 10}`,
            label: `统计项${props.stats.length + 1}`,
            icon: statsIconLibrary[props.stats.length % statsIconLibrary.length],
            iconType: 'emoji',
            suffix: ''
        };

        // 如果当前是实力见证模板，使用Lucide图标和添加后缀
        if (props.styleTemplate === 'strength') {
            const strengthIcon = strengthIconLibrary[props.stats.length % strengthIconLibrary.length];
            newStat.icon = strengthIcon.icon;
            newStat.iconType = 'lucide';
            newStat.suffix = '+';
        }

        props.stats.push(newStat);

        updateStatsDisplay(component, props);
        updatePropertiesPanel(component);
        // 属性面板更新后立即恢复图标显示状态
        setTimeout(() => {
            updateIconDisplayInPanel(componentId);
        }, 50);
    }
}

// 删除统计项
function deleteStat(componentId, index) {
    const component = document.getElementById(componentId);
    if (!component) return;

    let props;
    if (component._statsProperties) {
        props = component._statsProperties;
    } else {
        props = JSON.parse(JSON.stringify(statsComponent.properties));
        component._statsProperties = props;
    }

    if (props.stats.length > 1) {
        props.stats.splice(index, 1);

        // 如果删除后统计项数量少于列数，调整列数
        if (props.stats.length < props.columnsCount) {
            props.columnsCount = props.stats.length;
        }

        updateStatsDisplay(component, props);
        updatePropertiesPanel(component);
        // 属性面板更新后立即恢复图标显示状态
        setTimeout(() => {
            updateIconDisplayInPanel(componentId);
        }, 50);
    }
}

// 数字动画函数
function animateNumber(element, target, duration, delay = 0) {
    if (!element) return;

    setTimeout(() => {
        const start = 0;
        const startTime = performance.now();

        // 提取数字部分
        const numericTarget = parseInt(target.toString().replace(/[^\d]/g, '')) || 0;

        function updateNumber(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用缓动函数
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const current = Math.floor(start + (numericTarget * easeOutQuart));

            // 保持原有的非数字字符
            const originalText = target.toString();

            if (numericTarget > 0) {
                element.textContent = originalText.replace(/\d+/g, current.toString());
            } else {
                element.textContent = originalText;
            }

            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            } else {
                element.textContent = originalText;
            }
        }

        requestAnimationFrame(updateNumber);
    }, delay);
}

// 更新统计数字显示
function updateStatsDisplay(component, props) {
    const wrapper = component.querySelector('.stats-wrapper');
    const container = component.querySelector('.stats-container');
    if (!container || !wrapper) return;

    // 动态管理HTML结构 - 根据模板类型添加或移除元素
    const content = wrapper.querySelector('.stats-content');
    if (!content) return;

    // 管理标题区域
    let header = content.querySelector('.stats-header');
    if (props.styleTemplate === 'strength') {
        // 实力见证模板需要标题区域
        if (!header) {
            header = document.createElement('div');
            header.className = 'stats-header';
            header.innerHTML = `
                <h2 class="stats-title">${props.strengthTitle}</h2>
                <p class="stats-subtitle">${props.strengthSubtitle}</p>
                <div class="stats-divider"></div>
            `;
            content.insertBefore(header, container);
        }
    } else {
        // 默认模板不需要标题区域
        if (header) {
            header.remove();
        }
    }

    // 管理信任标识区域
    let trustIndicators = content.querySelector('.trust-indicators');
    if (props.styleTemplate === 'strength' && props.showTrustIndicators) {
        // 实力见证模板且开启信任标识
        if (!trustIndicators) {
            trustIndicators = document.createElement('div');
            trustIndicators.className = 'trust-indicators';
            trustIndicators.innerHTML = props.trustIndicators.map(indicator => `
                <div class="trust-item">
                    <i data-lucide="${indicator.icon}"></i>
                    <span>${indicator.text}</span>
                </div>
            `).join('');
            content.appendChild(trustIndicators);
        }
    } else {
        // 不需要信任标识
        if (trustIndicators) {
            trustIndicators.remove();
        }
    }

    // 更新整体包装器样式
    if (props.styleTemplate === 'strength') {
        // 实力见证模板的整体样式
        wrapper.style.position = 'relative';
        wrapper.style.padding = '100px 0';
        wrapper.style.overflow = 'hidden';
        wrapper.style.minHeight = '600px';

        // 设置背景 - 支持多层组合
        const background = component.querySelector('.stats-background');
        const overlay = component.querySelector('.stats-overlay');



        if (background) {
            // 清除之前的样式
            background.style.background = '';
            background.style.backgroundImage = '';
            background.style.opacity = '';

            background.style.position = 'absolute';
            background.style.top = '0';
            background.style.left = '0';
            background.style.width = '100%';
            background.style.height = '100%';
            background.style.zIndex = '1';

            if (props.strengthBgType === 'combined') {
                // 组合背景：基础色 + 图片（带透明度）
                background.style.backgroundColor = props.strengthBgColor;
                background.style.opacity = '1'; // 确保背景层不透明
                if (props.strengthBgImage) {
                    // 使用伪元素或叠加层来实现图片透明度
                    background.style.backgroundImage = `url(${props.strengthBgImage})`;
                    background.style.backgroundSize = 'cover';
                    background.style.backgroundPosition = 'center';
                    background.style.backgroundRepeat = 'no-repeat';
                    // 通过CSS混合模式实现透明度效果
                    background.style.backgroundBlendMode = 'overlay';
                }
            } else if (props.strengthBgType === 'gradient') {
                background.style.background = props.strengthBgGradient;
                background.style.opacity = '1';
            } else if (props.strengthBgType === 'color') {
                background.style.background = props.strengthBgColor;
                background.style.opacity = '1';
            } else if (props.strengthBgType === 'image' && props.strengthBgImage) {
                background.style.backgroundImage = `url(${props.strengthBgImage})`;
                background.style.backgroundSize = 'cover';
                background.style.backgroundPosition = 'center';
                background.style.backgroundRepeat = 'no-repeat';
                background.style.opacity = '1';
            }
        }

        // 设置叠加层（用于组合背景的渐变效果）
        if (overlay) {
            overlay.style.position = 'absolute';
            overlay.style.top = '0';
            overlay.style.left = '0';
            overlay.style.width = '100%';
            overlay.style.height = '100%';
            overlay.style.zIndex = '2';

            if (props.strengthBgType === 'combined') {
                overlay.style.background = props.strengthBgGradient;
            } else {
                overlay.style.background = 'transparent';
            }
        }

        // 显示并更新标题区域
        const header = component.querySelector('.stats-header');
        if (header) {
            header.style.display = 'block';
            header.style.textAlign = 'center';
            header.style.marginBottom = '4rem';
            header.style.position = 'relative';
            header.style.zIndex = '3';
        }

        // 更新标题
        const title = component.querySelector('.stats-title');
        const subtitle = component.querySelector('.stats-subtitle');

        if (title) {
            title.textContent = props.strengthTitle;
            title.style.fontSize = '3rem';
            title.style.fontWeight = '700';
            title.style.color = 'white';
            title.style.marginBottom = '1rem';
            title.style.textShadow = '0 4px 20px rgba(0, 0, 0, 0.3)';
            title.style.display = 'block';
        }
        if (subtitle) {
            subtitle.textContent = props.strengthSubtitle;
            subtitle.style.fontSize = '1.2rem';
            subtitle.style.color = 'rgba(255, 255, 255, 0.8)';
            subtitle.style.marginBottom = '2rem';
            subtitle.style.display = 'block';
        }

        // 更新装饰线
        const divider = component.querySelector('.stats-divider');
        if (divider) {
            divider.style.display = 'block';
            divider.style.width = '100px';
            divider.style.height = '4px';
            divider.style.background = 'linear-gradient(135deg, #10d5c2 0%, #007bff 100%)';
            divider.style.margin = '0 auto';
            divider.style.borderRadius = '2px';
        }

        // 设置内容区域
        const content = component.querySelector('.stats-content');
        if (content) {
            content.style.position = 'relative';
            content.style.zIndex = '3';
            content.style.textAlign = 'center';
        }

        // 更新信任标识
        const trustIndicators = component.querySelector('.trust-indicators');
        if (trustIndicators) {
            trustIndicators.style.display = props.showTrustIndicators ? 'flex' : 'none';
            trustIndicators.style.justifyContent = 'center';
            trustIndicators.style.alignItems = 'center';
            trustIndicators.style.gap = '40px';
            trustIndicators.style.marginTop = '5rem';
            trustIndicators.style.padding = '40px 0'; // 使用padding而不是paddingTop
            trustIndicators.style.flexWrap = 'wrap';
            trustIndicators.style.borderTop = '1px solid rgba(255, 255, 255, 0.1)'; // 顶部边框
            trustIndicators.style.position = 'relative';
            trustIndicators.style.zIndex = '4';
        }
    } else {
        // 默认模板样式
        wrapper.style.position = 'relative';
        wrapper.style.padding = '20px 0';
        wrapper.style.background = 'transparent';
        wrapper.style.minHeight = 'auto';
        wrapper.style.overflow = 'visible';

        // 清除实力见证的背景和叠加层
        const background = component.querySelector('.stats-background');
        const overlay = component.querySelector('.stats-overlay');
        if (background) {
            background.style.background = 'transparent';
            background.style.backgroundImage = 'none';
            background.style.backgroundColor = 'transparent';
            background.style.opacity = '1';
            background.style.position = 'static';
            background.style.width = 'auto';
            background.style.height = 'auto';
            background.style.zIndex = 'auto';
        }
        if (overlay) {
            overlay.style.background = 'transparent';
            overlay.style.position = 'static';
            overlay.style.width = 'auto';
            overlay.style.height = 'auto';
            overlay.style.zIndex = 'auto';
        }

        // 隐藏实力见证专用元素
        const header = component.querySelector('.stats-header');
        const trustIndicators = component.querySelector('.trust-indicators');
        if (header) header.style.display = 'none';
        if (trustIndicators) trustIndicators.style.display = 'none';

        // 重置内容区域样式
        const content = component.querySelector('.stats-content');
        if (content) {
            content.style.position = 'static';
            content.style.zIndex = 'auto';
            content.style.textAlign = 'left';
        }
    }

    // 设置容器样式
    container.style.display = 'grid';
    container.style.gridTemplateColumns = `repeat(${props.columnsCount}, 1fr)`;
    container.style.gap = `${props.itemSpacing}px`;
    container.style.margin = `${props.marginVertical}px ${props.marginHorizontal}px`;
    container.style.maxWidth = `${props.maxWidth}px`;
    container.style.marginLeft = 'auto';
    container.style.marginRight = 'auto';
    container.style.textAlign = props.alignment;

    // 将transform应用到整个component-block
    component.style.transform = `translateY(${props.positionVertical}px)`;
    component.style.position = 'relative';

    // 生成统计项HTML - 根据模板选择不同的结构
    if (props.styleTemplate === 'strength') {
        // 实力见证模板 - 添加特殊的装饰元素
        container.innerHTML = props.stats.map((stat) => {
            // 判断图标类型，如果是Lucide图标则使用<i>标签，否则使用emoji
            const iconHtml = stat.iconType === 'lucide'
                ? `<i data-lucide="${stat.icon}"></i>`
                : stat.icon;

            return `
                <div class="stats-item stats-item-strength">
                    <div class="stat-icon">
                        <div class="stats-icon-circle">
                            ${iconHtml}
                        </div>
                    </div>
                    <div class="stat-content">
                        <h3 class="stats-number" data-target="${stat.number}">${stat.number}</h3>
                        <span class="stat-plus">${stat.suffix || '+'}</span>
                        <p class="stats-label">${stat.label}</p>
                        <div class="stat-description">专业品质保证</div>
                    </div>
                    <div class="stat-decoration"></div>
                </div>
            `;
        }).join('');
    } else {
        // 默认模板
        container.innerHTML = props.stats.map((stat) => {
            // 判断图标类型，如果是Lucide图标则使用<i>标签，否则使用emoji
            const iconHtml = stat.iconType === 'lucide'
                ? `<i data-lucide="${stat.icon}"></i>`
                : stat.icon;

            return `
                <div class="stats-item">
                    <div class="stats-icon">${iconHtml}</div>
                    <div class="stats-number" data-target="${stat.number}">${stat.number}${stat.suffix || ''}</div>
                    <div class="stats-label">${stat.label}</div>
                </div>
            `;
        }).join('');
    }

    // 应用样式到所有统计项
    const statsItems = container.querySelectorAll('.stats-item');
    statsItems.forEach((statsItem, index) => {

        if (props.styleTemplate === 'strength') {
            // 实力见证样式 - 使用CSS类而不是内联样式
            statsItem.className = 'stats-item stats-item-strength';

            // 只设置必要的动态样式
            statsItem.style.padding = `${props.padding}px`;

            // 实力见证特有的阴影效果
            if (props.shadow) {
                statsItem.style.boxShadow = `0 4px 20px rgba(0,0,0,0.3), 0 0 20px rgba(16, 213, 194, 0.1)`;
            }
        } else {
            // 默认样式
            statsItem.style.backgroundColor = props.bgColor;
            statsItem.style.borderRadius = `${props.borderRadius}px`;
            statsItem.style.padding = `${props.padding}px`;
            statsItem.style.textAlign = 'center';
            statsItem.style.boxShadow = props.shadow ?
                `0 4px 20px rgba(0,0,0,${props.shadowIntensity})` : 'none';
            statsItem.style.transition = 'all 0.3s ease';
            statsItem.style.overflow = 'hidden';
            statsItem.style.position = 'relative';

            // 边框样式
            if (props.showBorder) {
                statsItem.style.border = `${props.borderWidth}px solid ${props.borderColor}`;
            } else {
                statsItem.style.border = 'none';
            }
        }

        if (props.styleTemplate === 'strength') {
            // 实力见证模板的样式
            const iconCircle = statsItem.querySelector('.stats-icon-circle');
            if (iconCircle) {
                // 设置图标圆圈大小
                iconCircle.style.width = `${props.strengthIconSize + 16}px`;
                iconCircle.style.height = `${props.strengthIconSize + 16}px`;

                // 设置图标大小
                const icon = iconCircle.querySelector('i[data-lucide]');
                if (icon) {
                    icon.style.width = `${props.strengthIconSize}px`;
                    icon.style.height = `${props.strengthIconSize}px`;
                } else {
                    // Emoji图标
                    iconCircle.style.fontSize = `${props.strengthIconSize}px`;
                }
            }

            const numberElement = statsItem.querySelector('.stats-number');
            if (numberElement) {
                // 实力见证模板使用CSS类样式，不需要设置内联样式
                // CSS已经定义了白色文字和固定大小
            }

            const plusElement = statsItem.querySelector('.stat-plus');
            if (plusElement) {
                // 实力见证模板使用CSS类样式，不需要设置内联样式
                // CSS已经定义了青色后缀和固定大小
            }

            const labelElement = statsItem.querySelector('.stats-label');
            if (labelElement) {
                // 实力见证模板使用CSS类样式，不需要设置内联样式
                // CSS已经定义了白色标签和固定大小
            }

            const descElement = statsItem.querySelector('.stat-description');
            if (descElement) {
                descElement.style.fontSize = `${props.labelSize - 2}px`;
                descElement.style.color = 'rgba(255, 255, 255, 0.7)';
                descElement.style.fontWeight = 'normal';
            }
        } else {
            // 默认模板的样式
            const iconElement = statsItem.querySelector('.stats-icon');
            if (iconElement) {
                iconElement.style.color = props.iconColor;
                iconElement.style.marginBottom = '12px';
                iconElement.style.display = 'block';

                // 检查是否包含Lucide图标
                const lucideIcon = iconElement.querySelector('i[data-lucide]');
                if (lucideIcon) {
                    // Lucide图标使用width和height
                    lucideIcon.style.width = `${props.iconSize}px`;
                    lucideIcon.style.height = `${props.iconSize}px`;
                    lucideIcon.style.color = props.iconColor;
                } else {
                    // Emoji图标使用fontSize
                    iconElement.style.fontSize = `${props.iconSize}px`;
                }
            }

            const numberElement = statsItem.querySelector('.stats-number');
            if (numberElement) {
                numberElement.style.fontSize = `${props.numberSize}px`;
                numberElement.style.color = props.numberColor;
                numberElement.style.fontWeight = props.fontWeight;
                numberElement.style.marginBottom = '8px';
                numberElement.style.display = 'block';
                numberElement.style.lineHeight = '1.2';
            }

            const labelElement = statsItem.querySelector('.stats-label');
            if (labelElement) {
                labelElement.style.fontSize = `${props.labelSize}px`;
                labelElement.style.color = props.labelColor;
                labelElement.style.fontWeight = 'normal';
                labelElement.style.display = 'block';
                labelElement.style.lineHeight = '1.4';
            }
        }

        // 悬停效果
        if (props.styleTemplate === 'strength') {
            // 实力见证模板的悬停效果
            statsItem.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = `0 8px 30px rgba(0,0,0,0.4), 0 0 30px rgba(16, 213, 194, 0.3)`;

                const iconCircle = this.querySelector('.stats-icon-circle');
                if (iconCircle) {
                    iconCircle.style.transform = 'scale(1.1)';
                    iconCircle.style.boxShadow = `0 0 20px rgba(16, 213, 194, 0.5)`;
                }
            });

            statsItem.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = props.shadow ?
                    `0 4px 20px rgba(0,0,0,0.3), 0 0 20px rgba(16, 213, 194, 0.1)` : 'none';

                const iconCircle = this.querySelector('.stats-icon-circle');
                if (iconCircle) {
                    iconCircle.style.transform = 'scale(1)';
                    iconCircle.style.boxShadow = 'none';
                }
            });
        } else {
            // 默认模板的悬停效果
            statsItem.addEventListener('mouseenter', function() {
                if (props.shadow) {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = `0 8px 30px rgba(0,0,0,${props.shadowIntensity + 0.05})`;
                }
            });

            statsItem.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = props.shadow ?
                    `0 4px 20px rgba(0,0,0,${props.shadowIntensity})` : 'none';
            });
        }
    });

    // 启动数字动画
    if (props.animation) {
        const numberElements = container.querySelectorAll('.stats-number');
        numberElements.forEach((element, index) => {
            const target = element.getAttribute('data-target');
            animateNumber(element, target, props.animationDuration, index * props.animationDelay);
        });
    }

    // 重新初始化Lucide图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}

// 旧的防抖函数已被优化的updateStatsPropertySmooth替代

// 注册统计数字组件到管理器
if (typeof ComponentManager !== 'undefined') {
    ComponentManager.register('stats', statsComponent, generateStatsProperties, updateStatsDisplay);
}

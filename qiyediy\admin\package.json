{"name": "qiyediy-admin", "version": "1.0.0", "description": "QiyeDIY企业建站系统 - 管理后台", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "grapesjs": "^0.21.7", "grapesjs-blocks-basic": "^1.0.2", "grapesjs-plugin-forms": "^2.0.6", "grapesjs-component-countdown": "^1.0.1", "grapesjs-plugin-export": "^1.0.11", "grapesjs-tui-image-editor": "^0.1.3", "grapesjs-style-gradient": "^2.0.0", "grapesjs-plugin-ckeditor": "^1.0.0", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "js-cookie": "^3.0.5", "sortablejs": "^1.15.0", "vue-draggable-plus": "^0.3.5", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "monaco-editor": "^0.45.0", "vue-json-pretty": "^2.2.4", "qrcode": "^1.5.3", "file-saver": "^2.0.5", "xlsx": "^0.18.5", "crypto-js": "^4.2.0", "mitt": "^3.0.1"}, "devDependencies": {"@types/node": "^20.10.4", "@types/lodash-es": "^4.17.12", "@types/nprogress": "^0.2.3", "@types/js-cookie": "^3.0.6", "@types/sortablejs": "^1.15.7", "@types/qrcode": "^1.5.5", "@types/file-saver": "^2.0.7", "@types/crypto-js": "^4.2.1", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.0", "typescript": "~5.3.0", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.25.2", "vite": "^5.0.8", "vue-tsc": "^1.8.25", "sass": "^1.69.5", "unocss": "^0.58.0", "@unocss/preset-uno": "^0.58.0", "@unocss/preset-attributify": "^0.58.0", "@unocss/preset-icons": "^0.58.0"}, "keywords": ["vue3", "typescript", "element-plus", "vite", "admin", "cms", "diy", "website-builder"], "author": {"name": "韩总", "email": "<EMAIL>", "url": "https://www.qiyediy.com"}, "license": "MIT", "homepage": "https://www.qiyediy.com", "repository": {"type": "git", "url": "https://github.com/qiyediy/admin.git"}, "bugs": {"url": "https://github.com/qiyediy/admin/issues"}}
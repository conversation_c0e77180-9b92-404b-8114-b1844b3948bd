/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * QiyeDIY企业建站系统 - 性能优化插件
 */

export default defineNuxtPlugin(() => {
  // 只在客户端运行
  if (process.server) return
  
  const { initPerformanceMonitoring, useLazyLoad, preloadImages } = usePerformance()
  
  // 初始化性能监控
  initPerformanceMonitoring()
  
  // 初始化懒加载
  const { initLazyLoad, observeImage } = useLazyLoad({
    rootMargin: '50px',
    threshold: 0.1
  })
  
  initLazyLoad()
  
  // 自动为所有带有data-src的图片启用懒加载
  const setupLazyImages = () => {
    const images = document.querySelectorAll('img[data-src]')
    images.forEach((img) => {
      observeImage(img as HTMLImageElement)
    })
  }
  
  // 页面加载完成后设置懒加载
  onMounted(() => {
    setupLazyImages()
    
    // 监听DOM变化，为新添加的图片启用懒加载
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element
            
            // 检查新添加的图片
            if (element.tagName === 'IMG' && element.hasAttribute('data-src')) {
              observeImage(element as HTMLImageElement)
            }
            
            // 检查新添加元素内的图片
            const images = element.querySelectorAll('img[data-src]')
            images.forEach((img) => {
              observeImage(img as HTMLImageElement)
            })
          }
        })
      })
    })
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    })
  })
  
  // 预加载关键资源
  const preloadCriticalResources = () => {
    // 预加载关键CSS
    const criticalCSS = [
      '/css/critical.css',
      '/css/fonts.css'
    ]
    
    criticalCSS.forEach(href => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = href
      link.as = 'style'
      document.head.appendChild(link)
    })
    
    // 预加载关键图片
    const criticalImages = [
      '/images/logo.png',
      '/images/hero-bg.jpg'
    ]
    
    preloadImages(criticalImages)
    
    // 预加载关键字体
    const criticalFonts = [
      '/fonts/main.woff2'
    ]
    
    criticalFonts.forEach(href => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = href
      link.as = 'font'
      link.type = 'font/woff2'
      link.crossOrigin = 'anonymous'
      document.head.appendChild(link)
    })
  }
  
  // 设置Service Worker
  const setupServiceWorker = async () => {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js')
        console.log('Service Worker注册成功:', registration)
        
        // 监听更新
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                // 有新版本可用
                console.log('新版本可用，请刷新页面')
              }
            })
          }
        })
      } catch (error) {
        console.error('Service Worker注册失败:', error)
      }
    }
  }
  
  // 优化滚动性能
  const optimizeScrolling = () => {
    let ticking = false
    
    const updateScrollPosition = () => {
      // 处理滚动相关的操作
      ticking = false
    }
    
    const onScroll = () => {
      if (!ticking) {
        requestAnimationFrame(updateScrollPosition)
        ticking = true
      }
    }
    
    window.addEventListener('scroll', onScroll, { passive: true })
  }
  
  // 优化触摸事件
  const optimizeTouchEvents = () => {
    // 为触摸事件添加passive选项
    const touchEvents = ['touchstart', 'touchmove', 'touchend']
    
    touchEvents.forEach(event => {
      document.addEventListener(event, () => {}, { passive: true })
    })
  }
  
  // 内存清理
  const setupMemoryCleanup = () => {
    // 页面隐藏时清理不必要的资源
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // 清理定时器、动画等
        console.log('页面隐藏，清理资源')
      }
    })
    
    // 内存压力时的处理
    if ('memory' in performance) {
      const checkMemoryUsage = () => {
        const memory = (performance as any).memory
        const usageRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit
        
        if (usageRatio > 0.8) {
          console.warn('内存使用率过高，建议清理资源')
          // 触发垃圾回收或清理缓存
        }
      }
      
      setInterval(checkMemoryUsage, 30000) // 每30秒检查一次
    }
  }
  
  // 网络优化
  const optimizeNetwork = () => {
    // 根据网络状况调整资源加载策略
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      
      const adjustForConnection = () => {
        const effectiveType = connection.effectiveType
        
        switch (effectiveType) {
          case 'slow-2g':
          case '2g':
            // 低速网络：减少资源加载
            document.documentElement.classList.add('slow-network')
            break
          case '3g':
            // 中速网络：适中的资源加载
            document.documentElement.classList.add('medium-network')
            break
          case '4g':
            // 高速网络：正常资源加载
            document.documentElement.classList.add('fast-network')
            break
        }
      }
      
      adjustForConnection()
      connection.addEventListener('change', adjustForConnection)
    }
  }
  
  // 初始化所有优化
  onMounted(() => {
    preloadCriticalResources()
    setupServiceWorker()
    optimizeScrolling()
    optimizeTouchEvents()
    setupMemoryCleanup()
    optimizeNetwork()
  })
  
  // 提供全局方法
  return {
    provide: {
      performance: {
        preloadImages,
        setupLazyImages
      }
    }
  }
})

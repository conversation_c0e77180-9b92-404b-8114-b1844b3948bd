{"version": 3, "file": "use-checkbox-disabled.mjs", "sources": ["../../../../../../../packages/components/checkbox/src/composables/use-checkbox-disabled.ts"], "sourcesContent": ["import { computed, inject } from 'vue'\nimport { useFormDisabled } from '@element-plus/components/form'\nimport { isUndefined } from '@element-plus/utils'\nimport { checkboxGroupContextKey } from '../constants'\n\nimport type { CheckboxModel, CheckboxStatus } from '../composables'\n\nexport const useCheckboxDisabled = ({\n  model,\n  isChecked,\n}: Pick<CheckboxModel, 'model'> & Pick<CheckboxStatus, 'isChecked'>) => {\n  const checkboxGroup = inject(checkboxGroupContextKey, undefined)\n\n  const isLimitDisabled = computed(() => {\n    const max = checkboxGroup?.max?.value\n    const min = checkboxGroup?.min?.value\n    return (\n      (!isUndefined(max) && model.value.length >= max && !isChecked.value) ||\n      (!isUndefined(min) && model.value.length <= min && isChecked.value)\n    )\n  })\n\n  const isDisabled = useFormDisabled(\n    computed(() => checkboxGroup?.disabled.value || isLimitDisabled.value)\n  )\n\n  return {\n    isDisabled,\n    isLimitDisabled,\n  }\n}\n\nexport type CheckboxDisabled = ReturnType<typeof useCheckboxDisabled>\n"], "names": [], "mappings": ";;;;;AAIY,MAAC,mBAAmB,GAAG,CAAC;AACpC,EAAE,KAAK;AACP,EAAE,SAAS;AACX,CAAC,KAAK;AACN,EAAE,MAAM,aAAa,GAAG,MAAM,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC,CAAC;AAChE,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM;AACzC,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,MAAM,GAAG,GAAG,CAAC,EAAE,GAAG,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC;AACtG,IAAI,MAAM,GAAG,GAAG,CAAC,EAAE,GAAG,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC;AACtG,IAAI,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAI,GAAG,IAAI,SAAS,CAAC,KAAK,CAAC;AACnJ,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,UAAU,GAAG,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,QAAQ,CAAC,KAAK,KAAK,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/I,EAAE,OAAO;AACT,IAAI,UAAU;AACd,IAAI,eAAe;AACnB,GAAG,CAAC;AACJ;;;;"}
<!--
  三只鱼网络科技 | 韩总 | 2024-12-19
  QiyeDIY企业建站系统 - 个人资料对话框
-->

<template>
  <el-dialog
    v-model="visible"
    title="个人资料"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="profile-content">
      <!-- 头像上传 -->
      <div class="avatar-section">
        <div class="avatar-wrapper">
          <el-avatar :size="80" :src="userStore.avatar" class="profile-avatar">
            <el-icon size="40"><User /></el-icon>
          </el-avatar>
          <div class="avatar-overlay" @click="handleAvatarClick">
            <el-icon><Camera /></el-icon>
            <span>更换头像</span>
          </div>
        </div>
        <input
          ref="avatarInputRef"
          type="file"
          accept="image/*"
          style="display: none"
          @change="handleAvatarChange"
        />
      </div>

      <!-- 基本信息 -->
      <el-form
        ref="profileFormRef"
        :model="profileForm"
        :rules="profileRules"
        label-width="80px"
        size="large"
      >
        <el-form-item label="用户名">
          <el-input v-model="profileForm.username" disabled />
        </el-form-item>

        <el-form-item label="邮箱">
          <el-input v-model="profileForm.email" disabled />
        </el-form-item>

        <el-form-item label="真实姓名" prop="realName">
          <el-input
            v-model="profileForm.realName"
            placeholder="请输入真实姓名"
            clearable
            :disabled="loading"
          />
        </el-form-item>

        <el-form-item label="手机号">
          <el-input v-model="profileForm.phone" disabled>
            <template #append>
              <el-button @click="showBindPhone = true">
                {{ profileForm.phone ? '更换' : '绑定' }}
              </el-button>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="注册时间">
          <el-input :value="formatDate(profileForm.createdAt)" disabled />
        </el-form-item>

        <el-form-item label="最后登录">
          <el-input :value="formatDate(profileForm.lastLoginAt)" disabled />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="loading">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="loading">
          保存
        </el-button>
      </div>
    </template>

    <!-- 绑定手机号对话框 -->
    <BindPhoneDialog v-model="showBindPhone" @success="handleBindPhoneSuccess" />
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, computed, watch } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { User, Camera } from '@element-plus/icons-vue'
import type { FormInstance, FormRules, UploadFile } from 'element-plus'
import BindPhoneDialog from './BindPhoneDialog.vue'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const userStore = useUserStore()

// 表单引用
const profileFormRef = ref<FormInstance>()
const avatarInputRef = ref<HTMLInputElement>()

// 状态
const loading = ref(false)
const showBindPhone = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 个人资料表单
const profileForm = reactive({
  username: '',
  email: '',
  realName: '',
  phone: '',
  avatar: '',
  createdAt: '',
  lastLoginAt: ''
})

// 验证规则
const profileRules: FormRules = {
  realName: [
    { min: 2, max: 50, message: '真实姓名长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

/**
 * 格式化日期
 */
const formatDate = (dateStr: string) => {
  if (!dateStr) return '暂无'
  return new Date(dateStr).toLocaleString('zh-CN')
}

/**
 * 处理头像点击
 */
const handleAvatarClick = () => {
  avatarInputRef.value?.click()
}

/**
 * 处理头像更换
 */
const handleAvatarChange = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (!file) return
  
  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }
  
  // 验证文件大小（5MB）
  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过 5MB')
    return
  }
  
  try {
    loading.value = true
    await userStore.uploadAvatar(file)
    ElMessage.success('头像更新成功')
  } catch (error) {
    console.error('头像上传失败:', error)
  } finally {
    loading.value = false
    // 清空文件输入
    target.value = ''
  }
}

/**
 * 处理保存
 */
const handleSave = async () => {
  if (!profileFormRef.value) return
  
  try {
    await profileFormRef.value.validate()
    loading.value = true
    
    await userStore.updateProfile({
      real_name: profileForm.realName
    })
    
    ElMessage.success('个人资料更新成功')
    visible.value = false
    
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 处理关闭
 */
const handleClose = () => {
  if (loading.value) return
  visible.value = false
  resetForm()
}

/**
 * 重置表单
 */
const resetForm = () => {
  profileFormRef.value?.resetFields()
  loadUserData()
}

/**
 * 加载用户数据
 */
const loadUserData = () => {
  const user = userStore.user
  if (user) {
    Object.assign(profileForm, {
      username: user.username,
      email: user.email,
      realName: user.real_name || '',
      phone: user.phone || '',
      avatar: user.avatar || '',
      createdAt: user.created_at,
      lastLoginAt: user.last_login_at
    })
  }
}

/**
 * 处理绑定手机号成功
 */
const handleBindPhoneSuccess = (phone: string) => {
  profileForm.phone = phone
  showBindPhone.value = false
  ElMessage.success('手机号绑定成功')
}

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal) {
    loadUserData()
  }
})
</script>

<style lang="scss" scoped>
.profile-content {
  padding: 20px 0;
}

.avatar-section {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
}

.avatar-wrapper {
  position: relative;
  cursor: pointer;
  
  .profile-avatar {
    border: 3px solid var(--el-border-color-light);
    transition: all 0.3s ease;
  }
  
  .avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    color: white;
    font-size: 12px;
    
    .el-icon {
      font-size: 20px;
      margin-bottom: 4px;
    }
  }
  
  &:hover {
    .profile-avatar {
      border-color: var(--el-color-primary);
    }
    
    .avatar-overlay {
      opacity: 1;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
  
  .el-input {
    .el-input__wrapper {
      border-radius: 6px;
    }
  }
  
  .el-input.is-disabled {
    .el-input__wrapper {
      background-color: var(--el-fill-color-lighter);
    }
  }
}

:deep(.el-dialog) {
  border-radius: 12px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

:deep(.el-dialog__body) {
  padding: 0 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid var(--el-border-color-lighter);
}
</style>

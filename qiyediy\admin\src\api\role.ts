/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 角色管理API接口
 */

import { http } from '@/utils/http'
import type { 
  Role, 
  PermissionGroup,
  RoleStatistics,
  ApiResponse,
  PaginatedResponse
} from '@/types/auth'

/**
 * 角色管理相关API
 */
export const roleApi = {
  /**
   * 获取角色列表
   */
  getList: (params: {
    page?: number
    per_page?: number
    keyword?: string
    is_default?: number | string
    sort_field?: string
    sort_order?: string
  }): Promise<PaginatedResponse<Role>> => {
    return http.get('/role', { params })
  },

  /**
   * 获取所有角色（用于下拉选择）
   */
  getAll: (): Promise<ApiResponse<Role[]>> => {
    return http.get('/role/all')
  },

  /**
   * 获取角色详情
   */
  getById: (id: number): Promise<ApiResponse<Role>> => {
    return http.get(`/role/${id}`)
  },

  /**
   * 创建角色
   */
  create: (data: {
    name: string
    slug: string
    description?: string
    permissions?: string[]
    is_default?: number
    sort_order?: number
  }): Promise<ApiResponse<Role>> => {
    return http.post('/role', data)
  },

  /**
   * 更新角色
   */
  update: (id: number, data: {
    name?: string
    slug?: string
    description?: string
    permissions?: string[]
    is_default?: number
    sort_order?: number
  }): Promise<ApiResponse<Role>> => {
    return http.put(`/role/${id}`, data)
  },

  /**
   * 删除角色
   */
  delete: (id: number): Promise<ApiResponse<void>> => {
    return http.delete(`/role/${id}`)
  },

  /**
   * 批量删除角色
   */
  batchDelete: (ids: number[]): Promise<ApiResponse<{ count: number }>> => {
    return http.delete('/role/batch', { data: { ids } })
  },

  /**
   * 设置默认角色
   */
  setDefault: (id: number): Promise<ApiResponse<void>> => {
    return http.post(`/role/${id}/set-default`)
  },

  /**
   * 同步权限
   */
  syncPermissions: (id: number, data: {
    permissions: string[]
  }): Promise<ApiResponse<void>> => {
    return http.post(`/role/${id}/sync-permissions`, data)
  },

  /**
   * 获取权限列表
   */
  getPermissions: (): Promise<ApiResponse<PermissionGroup[]>> => {
    return http.get('/role/permissions')
  },

  /**
   * 获取角色统计
   */
  getStatistics: (): Promise<ApiResponse<RoleStatistics>> => {
    return http.get('/role/statistics')
  },

  /**
   * 检查角色名称是否可用
   */
  checkName: (name: string, excludeId?: number): Promise<ApiResponse<{
    available: boolean
    message: string
  }>> => {
    return http.get('/role/check-name', { 
      params: { name, exclude_id: excludeId } 
    })
  },

  /**
   * 检查角色标识是否可用
   */
  checkSlug: (slug: string, excludeId?: number): Promise<ApiResponse<{
    available: boolean
    message: string
  }>> => {
    return http.get('/role/check-slug', { 
      params: { slug, exclude_id: excludeId } 
    })
  },

  /**
   * 复制角色
   */
  copy: (id: number, data: {
    name: string
    slug: string
    description?: string
  }): Promise<ApiResponse<Role>> => {
    return http.post(`/role/${id}/copy`, data)
  },

  /**
   * 获取角色用户列表
   */
  getUsers: (id: number, params?: {
    page?: number
    per_page?: number
    keyword?: string
  }): Promise<PaginatedResponse<any>> => {
    return http.get(`/role/${id}/users`, { params })
  }
}

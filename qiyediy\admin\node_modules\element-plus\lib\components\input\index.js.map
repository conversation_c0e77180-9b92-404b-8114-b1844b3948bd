{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/input/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Input from './src/input.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElInput: SFCWithInstall<typeof Input> = withInstall(Input)\nexport default ElInput\n\nexport * from './src/input'\nexport type { InputInstance } from './src/instance'\n"], "names": ["withInstall", "Input"], "mappings": ";;;;;;;;AAEY,MAAC,OAAO,GAAGA,mBAAW,CAACC,kBAAK;;;;;;;"}
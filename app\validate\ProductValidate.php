<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * 产品验证器 - ThinkPHP6企业级应用
 * 功能：产品数据验证和安全过滤
 */

namespace app\validate;

class ProductValidate extends BaseValidate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'name' => 'require|length:1,200|checkSql',
        'slug' => 'require|length:1,200|alphaNum|checkSql',
        'category_id' => 'require|integer|gt:0',
        'short_description' => 'length:0,500|checkSql',
        'description' => 'checkSql',
        'features' => 'checkSql',
        'specifications' => 'checkSql',
        'price' => 'length:0,50|checkSql',
        'original_price' => 'length:0,50|checkSql',
        'stock_status' => 'in:in_stock,out_of_stock,pre_order',
        'sku' => 'length:0,100|alphaNum|checkSql',
        'tags' => 'length:0,200|checkSql',
        'meta_title' => 'length:0,200|checkSql',
        'meta_description' => 'length:0,500|checkSql',
        'sort_order' => 'integer|egt:0',
        'status' => 'in:0,1',
        'is_featured' => 'in:0,1',
        'is_hot' => 'in:0,1',
        'is_new' => 'in:0,1',
        'image_url' => 'url|checkSql',
    ];
    
    /**
     * 验证消息
     */
    protected $message = [
        'name.require' => '产品名称不能为空',
        'name.length' => '产品名称长度不能超过200个字符',
        'name.checkSql' => '产品名称包含非法字符',
        'slug.require' => '产品别名不能为空',
        'slug.length' => '产品别名长度不能超过200个字符',
        'slug.alphaNum' => '产品别名只能包含字母和数字',
        'slug.checkSql' => '产品别名包含非法字符',
        'category_id.require' => '请选择产品分类',
        'category_id.integer' => '产品分类ID必须为整数',
        'category_id.gt' => '产品分类ID必须大于0',
        'short_description.length' => '简短描述长度不能超过500个字符',
        'short_description.checkSql' => '简短描述包含非法字符',
        'description.checkSql' => '产品描述包含非法字符',
        'features.checkSql' => '产品特性包含非法字符',
        'specifications.checkSql' => '产品规格包含非法字符',
        'price.length' => '价格长度不能超过50个字符',
        'price.checkSql' => '价格包含非法字符',
        'original_price.length' => '原价长度不能超过50个字符',
        'original_price.checkSql' => '原价包含非法字符',
        'stock_status.in' => '库存状态值无效',
        'sku.length' => 'SKU长度不能超过100个字符',
        'sku.alphaNum' => 'SKU只能包含字母和数字',
        'sku.checkSql' => 'SKU包含非法字符',
        'tags.length' => '标签长度不能超过200个字符',
        'tags.checkSql' => '标签包含非法字符',
        'meta_title.length' => 'SEO标题长度不能超过200个字符',
        'meta_title.checkSql' => 'SEO标题包含非法字符',
        'meta_description.length' => 'SEO描述长度不能超过500个字符',
        'meta_description.checkSql' => 'SEO描述包含非法字符',
        'sort_order.integer' => '排序必须为整数',
        'sort_order.egt' => '排序必须大于等于0',
        'status.in' => '状态值无效',
        'is_featured.in' => '推荐状态值无效',
        'is_hot.in' => '热门状态值无效',
        'is_new.in' => '新品状态值无效',
        'image_url.url' => '图片URL格式无效',
        'image_url.checkSql' => '图片URL包含非法字符',
    ];
    
    /**
     * 验证场景
     */
    protected $scene = [
        'add' => ['name', 'category_id', 'short_description', 'description', 'features', 'specifications', 'price', 'original_price', 'stock_status', 'sku', 'tags', 'meta_title', 'meta_description', 'sort_order', 'status', 'is_featured', 'is_hot', 'is_new', 'image_url'],
        'edit' => ['name', 'category_id', 'short_description', 'description', 'features', 'specifications', 'price', 'original_price', 'stock_status', 'sku', 'tags', 'meta_title', 'meta_description', 'sort_order', 'status', 'is_featured', 'is_hot', 'is_new', 'image_url'],
        'search' => ['keyword', 'category_id'],
    ];
    
    /**
     * 自定义验证规则：SQL注入检查
     */
    protected function checkSql($value, $rule, $data = [])
    {
        if (!$this->checkSqlInjection($value)) {
            return '输入内容包含非法字符';
        }
        return true;
    }
    
    /**
     * 验证搜索参数
     */
    public function validateSearch($data)
    {
        $rules = [
            'keyword' => 'require|length:1,100|checkSql',
            'category_id' => 'integer|egt:0',
        ];
        
        $messages = [
            'keyword.require' => '搜索关键词不能为空',
            'keyword.length' => '搜索关键词长度不能超过100个字符',
            'keyword.checkSql' => '搜索关键词包含非法字符',
            'category_id.integer' => '分类ID必须为整数',
            'category_id.egt' => '分类ID必须大于等于0',
        ];
        
        $validate = new self();
        $validate->rule($rules)->message($messages);
        
        if (!$validate->check($data)) {
            return $validate->getError();
        }
        
        return true;
    }
    
    /**
     * 验证文件上传
     */
    public function validateFileUpload($file)
    {
        if (!$file) {
            return '请选择要上传的文件';
        }
        
        // 检查文件大小（5MB限制）
        if ($file->getSize() > 5 * 1024 * 1024) {
            return '文件大小不能超过5MB';
        }
        
        // 检查文件扩展名
        if (!$this->checkFileExtension($file->getOriginalName())) {
            return '只允许上传jpg、jpeg、png、gif、webp格式的图片';
        }
        
        // 检查MIME类型
        if (!$this->checkImageMimeType($file->getMime())) {
            return '文件类型不正确，只允许上传图片文件';
        }
        
        // 检查文件名安全性
        if (!$this->checkFilename($file->getOriginalName())) {
            return '文件名包含非法字符';
        }
        
        return true;
    }
}

{"version": 3, "file": "form-item2.mjs", "sources": ["../../../../../../packages/components/form/src/form-item.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"formItemRef\"\n    :class=\"formItemClasses\"\n    :role=\"isGroup ? 'group' : undefined\"\n    :aria-labelledby=\"isGroup ? labelId : undefined\"\n  >\n    <form-label-wrap\n      :is-auto-width=\"labelStyle.width === 'auto'\"\n      :update-all=\"formContext?.labelWidth === 'auto'\"\n    >\n      <component\n        :is=\"labelFor ? 'label' : 'div'\"\n        v-if=\"hasLabel\"\n        :id=\"labelId\"\n        :for=\"labelFor\"\n        :class=\"ns.e('label')\"\n        :style=\"labelStyle\"\n      >\n        <slot name=\"label\" :label=\"currentLabel\">\n          {{ currentLabel }}\n        </slot>\n      </component>\n    </form-label-wrap>\n\n    <div :class=\"ns.e('content')\" :style=\"contentStyle\">\n      <slot />\n      <transition-group :name=\"`${ns.namespace.value}-zoom-in-top`\">\n        <slot v-if=\"shouldShowError\" name=\"error\" :error=\"validateMessage\">\n          <div :class=\"validateClasses\">\n            {{ validateMessage }}\n          </div>\n        </slot>\n      </transition-group>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  inject,\n  nextTick,\n  onBeforeUnmount,\n  onMounted,\n  provide,\n  reactive,\n  ref,\n  toRefs,\n  useSlots,\n  watch,\n} from 'vue'\nimport AsyncValidator from 'async-validator'\nimport { clone } from 'lodash-unified'\nimport { refDebounced } from '@vueuse/core'\nimport {\n  addUnit,\n  ensureArray,\n  getProp,\n  isArray,\n  isBoolean,\n  isFunction,\n  isString,\n} from '@element-plus/utils'\nimport { useId, useNamespace } from '@element-plus/hooks'\nimport { useFormSize } from './hooks'\nimport { formItemProps } from './form-item'\nimport FormLabelWrap from './form-label-wrap'\nimport { formContextKey, formItemContextKey } from './constants'\n\nimport type { CSSProperties } from 'vue'\nimport type { RuleItem } from 'async-validator'\nimport type { Arrayable } from '@element-plus/utils'\nimport type {\n  FormItemContext,\n  FormItemRule,\n  FormValidateFailure,\n} from './types'\nimport type { FormItemValidateState } from './form-item'\n\ndefineOptions({\n  name: 'ElFormItem',\n})\nconst props = defineProps(formItemProps)\nconst slots = useSlots()\n\nconst formContext = inject(formContextKey, undefined)\nconst parentFormItemContext = inject(formItemContextKey, undefined)\n\nconst _size = useFormSize(undefined, { formItem: false })\nconst ns = useNamespace('form-item')\n\nconst labelId = useId().value\nconst inputIds = ref<string[]>([])\n\nconst validateState = ref<FormItemValidateState>('')\nconst validateStateDebounced = refDebounced(validateState, 100)\nconst validateMessage = ref('')\nconst formItemRef = ref<HTMLDivElement>()\n// special inline value.\nlet initialValue: any = undefined\nlet isResettingField = false\n\nconst labelPosition = computed(\n  () => props.labelPosition || formContext?.labelPosition\n)\n\nconst labelStyle = computed<CSSProperties>(() => {\n  if (labelPosition.value === 'top') {\n    return {}\n  }\n\n  const labelWidth = addUnit(props.labelWidth || formContext?.labelWidth || '')\n  if (labelWidth) return { width: labelWidth }\n  return {}\n})\n\nconst contentStyle = computed<CSSProperties>(() => {\n  if (labelPosition.value === 'top' || formContext?.inline) {\n    return {}\n  }\n  if (!props.label && !props.labelWidth && isNested) {\n    return {}\n  }\n  const labelWidth = addUnit(props.labelWidth || formContext?.labelWidth || '')\n  if (!props.label && !slots.label) {\n    return { marginLeft: labelWidth }\n  }\n  return {}\n})\n\nconst formItemClasses = computed(() => [\n  ns.b(),\n  ns.m(_size.value),\n  ns.is('error', validateState.value === 'error'),\n  ns.is('validating', validateState.value === 'validating'),\n  ns.is('success', validateState.value === 'success'),\n  ns.is('required', isRequired.value || props.required),\n  ns.is('no-asterisk', formContext?.hideRequiredAsterisk),\n  formContext?.requireAsteriskPosition === 'right'\n    ? 'asterisk-right'\n    : 'asterisk-left',\n  {\n    [ns.m('feedback')]: formContext?.statusIcon,\n    [ns.m(`label-${labelPosition.value}`)]: labelPosition.value,\n  },\n])\n\nconst _inlineMessage = computed(() =>\n  isBoolean(props.inlineMessage)\n    ? props.inlineMessage\n    : formContext?.inlineMessage || false\n)\n\nconst validateClasses = computed(() => [\n  ns.e('error'),\n  { [ns.em('error', 'inline')]: _inlineMessage.value },\n])\n\nconst propString = computed(() => {\n  if (!props.prop) return ''\n  return isString(props.prop) ? props.prop : props.prop.join('.')\n})\n\nconst hasLabel = computed<boolean>(() => {\n  return !!(props.label || slots.label)\n})\n\nconst labelFor = computed<string | undefined>(() => {\n  return (\n    props.for ?? (inputIds.value.length === 1 ? inputIds.value[0] : undefined)\n  )\n})\n\nconst isGroup = computed<boolean>(() => {\n  return !labelFor.value && hasLabel.value\n})\n\nconst isNested = !!parentFormItemContext\n\nconst fieldValue = computed(() => {\n  const model = formContext?.model\n  if (!model || !props.prop) {\n    return\n  }\n  return getProp(model, props.prop).value\n})\n\nconst normalizedRules = computed(() => {\n  const { required } = props\n\n  const rules: FormItemRule[] = []\n\n  if (props.rules) {\n    rules.push(...ensureArray(props.rules))\n  }\n\n  const formRules = formContext?.rules\n  if (formRules && props.prop) {\n    const _rules = getProp<Arrayable<FormItemRule> | undefined>(\n      formRules,\n      props.prop\n    ).value\n    if (_rules) {\n      rules.push(...ensureArray(_rules))\n    }\n  }\n\n  if (required !== undefined) {\n    const requiredRules = rules\n      .map((rule, i) => [rule, i] as const)\n      .filter(([rule]) => Object.keys(rule).includes('required'))\n\n    if (requiredRules.length > 0) {\n      for (const [rule, i] of requiredRules) {\n        if (rule.required === required) continue\n        rules[i] = { ...rule, required }\n      }\n    } else {\n      rules.push({ required })\n    }\n  }\n\n  return rules\n})\n\nconst validateEnabled = computed(() => normalizedRules.value.length > 0)\n\nconst getFilteredRule = (trigger: string) => {\n  const rules = normalizedRules.value\n  return (\n    rules\n      .filter((rule) => {\n        if (!rule.trigger || !trigger) return true\n        if (isArray(rule.trigger)) {\n          return rule.trigger.includes(trigger)\n        } else {\n          return rule.trigger === trigger\n        }\n      })\n      // exclude trigger\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      .map(({ trigger, ...rule }): RuleItem => rule)\n  )\n}\n\nconst isRequired = computed(() =>\n  normalizedRules.value.some((rule) => rule.required)\n)\n\nconst shouldShowError = computed(\n  () =>\n    validateStateDebounced.value === 'error' &&\n    props.showMessage &&\n    (formContext?.showMessage ?? true)\n)\n\nconst currentLabel = computed(\n  () => `${props.label || ''}${formContext?.labelSuffix || ''}`\n)\n\nconst setValidationState = (state: FormItemValidateState) => {\n  validateState.value = state\n}\n\nconst onValidationFailed = (error: FormValidateFailure) => {\n  const { errors, fields } = error\n  if (!errors || !fields) {\n    console.error(error)\n  }\n\n  setValidationState('error')\n  validateMessage.value = errors\n    ? errors?.[0]?.message ?? `${props.prop} is required`\n    : ''\n\n  formContext?.emit('validate', props.prop!, false, validateMessage.value)\n}\n\nconst onValidationSucceeded = () => {\n  setValidationState('success')\n  formContext?.emit('validate', props.prop!, true, '')\n}\n\nconst doValidate = async (rules: RuleItem[]): Promise<true> => {\n  const modelName = propString.value\n  const validator = new AsyncValidator({\n    [modelName]: rules,\n  })\n  return validator\n    .validate({ [modelName]: fieldValue.value }, { firstFields: true })\n    .then(() => {\n      onValidationSucceeded()\n      return true as const\n    })\n    .catch((err: FormValidateFailure) => {\n      onValidationFailed(err)\n      return Promise.reject(err)\n    })\n}\n\nconst validate: FormItemContext['validate'] = async (trigger, callback) => {\n  // skip validation if its resetting\n  if (isResettingField || !props.prop) {\n    return false\n  }\n\n  const hasCallback = isFunction(callback)\n  if (!validateEnabled.value) {\n    callback?.(false)\n    return false\n  }\n\n  const rules = getFilteredRule(trigger)\n  if (rules.length === 0) {\n    callback?.(true)\n    return true\n  }\n\n  setValidationState('validating')\n\n  return doValidate(rules)\n    .then(() => {\n      callback?.(true)\n      return true as const\n    })\n    .catch((err: FormValidateFailure) => {\n      const { fields } = err\n      callback?.(false, fields)\n      return hasCallback ? false : Promise.reject(fields)\n    })\n}\n\nconst clearValidate: FormItemContext['clearValidate'] = () => {\n  setValidationState('')\n  validateMessage.value = ''\n  isResettingField = false\n}\n\nconst resetField: FormItemContext['resetField'] = async () => {\n  const model = formContext?.model\n  if (!model || !props.prop) return\n\n  const computedValue = getProp(model, props.prop)\n\n  // prevent validation from being triggered\n  isResettingField = true\n\n  computedValue.value = clone(initialValue)\n\n  await nextTick()\n  clearValidate()\n\n  isResettingField = false\n}\n\nconst addInputId: FormItemContext['addInputId'] = (id: string) => {\n  if (!inputIds.value.includes(id)) {\n    inputIds.value.push(id)\n  }\n}\n\nconst removeInputId: FormItemContext['removeInputId'] = (id: string) => {\n  inputIds.value = inputIds.value.filter((listId) => listId !== id)\n}\n\nwatch(\n  () => props.error,\n  (val) => {\n    validateMessage.value = val || ''\n    setValidationState(val ? 'error' : '')\n  },\n  { immediate: true }\n)\n\nwatch(\n  () => props.validateStatus,\n  (val) => setValidationState(val || '')\n)\n\nconst context: FormItemContext = reactive({\n  ...toRefs(props),\n  $el: formItemRef,\n  size: _size,\n  validateState,\n  labelId,\n  inputIds,\n  isGroup,\n  hasLabel,\n  fieldValue,\n  addInputId,\n  removeInputId,\n  resetField,\n  clearValidate,\n  validate,\n})\n\nprovide(formItemContextKey, context)\n\nonMounted(() => {\n  if (props.prop) {\n    formContext?.addField(context)\n    initialValue = clone(fieldValue.value)\n  }\n})\n\nonBeforeUnmount(() => {\n  formContext?.removeField(context)\n})\n\ndefineExpose({\n  /**\n   * @description Form item size.\n   */\n  size: _size,\n  /**\n   * @description Validation message.\n   */\n  validateMessage,\n  /**\n   * @description Validation state.\n   */\n  validateState,\n  /**\n   * @description Validate form item.\n   */\n  validate,\n  /**\n   * @description Remove validation status of the field.\n   */\n  clearValidate,\n  /**\n   * @description Reset current field and remove validation result.\n   */\n  resetField,\n})\n</script>\n"], "names": ["ensureArray", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref", "_createVNode", "_withCtx", "_createBlock", "_resolveDynamicComponent", "_normalizeStyle", "_renderSlot", "_createTextVNode", "_toDisplayString", "_createCommentVNode"], "mappings": ";;;;;;;;;;;;;;;;mCAgFc,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAEA,IAAA,MAAM,QAAQ,QAAS,EAAA,CAAA;AAEvB,IAAM,MAAA,WAAA,GAAc,MAAO,CAAA,cAAA,EAAgB,KAAS,CAAA,CAAA,CAAA;AACpD,IAAM,MAAA,qBAAA,GAAwB,MAAO,CAAA,kBAAA,EAAoB,KAAS,CAAA,CAAA,CAAA;AAElE,IAAA,MAAM,QAAQ,WAAY,CAAA,KAAA,CAAA,EAAW,EAAE,QAAA,EAAU,OAAO,CAAA,CAAA;AACxD,IAAM,MAAA,EAAA,GAAK,aAAa,WAAW,CAAA,CAAA;AAEnC,IAAM,MAAA,OAAA,GAAU,OAAQ,CAAA,KAAA,CAAA;AACxB,IAAM,MAAA,QAAA,GAAW,GAAc,CAAA,EAAE,CAAA,CAAA;AAEjC,IAAM,MAAA,aAAA,GAAgB,IAA2B,EAAE,CAAA,CAAA;AACnD,IAAM,MAAA,sBAAA,GAAyB,YAAa,CAAA,aAAA,EAAe,GAAG,CAAA,CAAA;AAC9D,IAAM,MAAA,eAAA,GAAkB,IAAI,EAAE,CAAA,CAAA;AAC9B,IAAA,MAAM,cAAc,GAAoB,EAAA,CAAA;AAExC,IAAA,IAAI,YAAoB,GAAA,KAAA,CAAA,CAAA;AACxB,IAAA,IAAI,gBAAmB,GAAA,KAAA,CAAA;AAEvB,IAAA,MAAM,aAAgB,GAAA,QAAA,CAAA,MAAA,KAAA,CAAA,aAAA,KAAA,WAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,WAAA,CAAA,aAAA,CAAA,CAAA,CAAA;AAAA,IACpB,MAAA,UAAY,GAAA,QAAA,CAAA,MAA8B;AAAA,MAC5C,IAAA,aAAA,CAAA,KAAA,KAAA,KAAA,EAAA;AAEA,QAAM,OAAA,EAAA,CAAA;AACJ,OAAI;AACF,MAAA,MAAA,UAAQ,GAAA,OAAA,CAAA,KAAA,CAAA,UAAA,KAAA,WAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,WAAA,CAAA,UAAA,CAAA,IAAA,EAAA,CAAA,CAAA;AAAA,MACV,IAAA,UAAA;AAEA,QAAA,0BAA2B,EAAA,CAAA;AAC3B,MAAA,OAAgB,EAAA,CAAA;AAChB,KAAA,CAAA,CAAA;AAAQ,IACV,MAAC,YAAA,GAAA,QAAA,CAAA,MAAA;AAED,MAAM,IAAA,aAAA,CAAA,UAAuC,KAAM,KAAA,WAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,WAAA,CAAA,MAAA,CAAA,EAAA;AACjD,QAAA,OAAkB,EAAA,CAAA;AAChB,OAAA;AAAQ,MACV,IAAA,CAAA,KAAA,CAAA,KAAA,IAAA,CAAA,KAAA,CAAA,UAAA,IAAA,QAAA,EAAA;AACA,QAAA,OAAW,EAAA,CAAA;AACT,OAAA;AAAQ,MACV,MAAA,UAAA,GAAA,OAAA,CAAA,KAAA,CAAA,UAAA,KAAA,WAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,WAAA,CAAA,UAAA,CAAA,IAAA,EAAA,CAAA,CAAA;AACA,MAAA,IAAA,CAAA,eAA2B,CAAA,KAAA,CAAA,KAAA,EAAoB;AAC/C,QAAA,OAAK,EAAM,UAAU,YAAa,EAAA,CAAA;AAChC,OAAO;AAAyB,MAClC,OAAA,EAAA,CAAA;AACA,KAAA,CAAA,CAAA;AAAQ,IACV,MAAC,eAAA,GAAA,QAAA,CAAA,MAAA;AAED,MAAM,EAAA,CAAA,CAAA,EAAA;AAAiC,MACrC,GAAG,CAAE,CAAA,KAAA,CAAA,KAAA,CAAA;AAAA,MACL,EAAA,CAAG,EAAE,CAAA,OAAW,EAAA,aAAA,CAAA,KAAA,KAAA,OAAA,CAAA;AAAA,MAChB,EAAG,CAAA,EAAA,CAAG,YAAS,EAAA,mBAAwB,KAAO,YAAA,CAAA;AAAA,MAC9C,EAAG,CAAA,EAAA,CAAG,SAAc,EAAA,aAAA,CAAA,mBAAoC,CAAA;AAAA,MACxD,EAAG,CAAA,EAAA,CAAG,UAAW,EAAA,UAAA,CAAA,cAAiC,CAAA,QAAA,CAAA;AAAA,MAClD,GAAG,EAAG,CAAA,aAAuB,EAAA,WAAA,IAAA,YAAuB,CAAA,GAAA,WAAA,CAAA,oBAAA,CAAA;AAAA,MACpD,CAAG,WAAkB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,WAAiC,CAAA,uBAAA,MAAA,OAAA,GAAA,gBAAA,GAAA,eAAA;AAAA,MACtD;AAEI,QACJ,CAAA,EAAA,CAAA,CAAA,CAAA,UAAA,CAAA,GAAA,WAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,WAAA,CAAA,UAAA;AAAA,QACE,CAAC,EAAG,CAAA,CAAA,CAAE,CAAU,MAAA,EAAA,aAAiB,CAAA,KAAA,CAAA,CAAA,CAAA,GAAA,aAAA,CAAA,KAAA;AAAA,OACjC;AAAsD,KACxD,CAAA,CAAA;AAAA,IACF,MAAC,cAAA,GAAA,QAAA,CAAA,MAAA,SAAA,CAAA,KAAA,CAAA,aAAA,CAAA,GAAA,KAAA,CAAA,aAAA,GAAA,CAAA,WAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,WAAA,CAAA,aAAA,KAAA,KAAA,CAAA,CAAA;AAED,IAAA,MAAM,eAAiB,GAAA,QAAA,CAAA,MAAA;AAAA,MAAS,EAAA,CAAA,CAAA,CAAA;AAGI,MACpC,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,OAAA,EAAA,QAAA,CAAA,GAAA,cAAA,CAAA,KAAA,EAAA;AAEA,KAAM,CAAA,CAAA;AAAiC,IACrC,gBAAY,GAAA,QAAA,CAAA,MAAA;AAAA,MACZ,IAAM,CAAA;AAA6C,QACpD,OAAA,EAAA,CAAA;AAED,MAAM,OAAA,QAAA,CAAA,UAAsB,CAAM,GAAA,KAAA,CAAA,IAAA,GAAA,KAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;AAChC,KAAI,CAAA,CAAA;AACJ,IAAO,MAAA,QAAA,GAAA,QAAmB,CAAA,MAAI;AAAgC,MAC/D,OAAA,CAAA,EAAA,KAAA,CAAA,KAAA,IAAA,KAAA,CAAA,KAAA,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,QAAgB,GAAA,QAAA,CAAA,MAAe;AAAA,MAChC,IAAA,EAAA,CAAA;AAED,MAAM,OAAA,CAAA,EAAA,GAAA,cAA8C,IAAA,GAAA,EAAA,GAAA,QAAA,CAAA,KAAA,CAAA,MAAA,KAAA,CAAA,GAAA,QAAA,CAAA,KAAA,CAAA,CAAA,CAAA,GAAA,KAAA,CAAA,CAAA;AAClD,KACE,CAAA,CAAA;AAAgE,IAEpE,MAAC,OAAA,GAAA,QAAA,CAAA,MAAA;AAED,MAAM,OAAA,CAAA,kBAAkC,QAAA,CAAA,KAAA,CAAA;AACtC,KAAO,CAAA,CAAA;AAA4B,IACrC,MAAC,QAAA,GAAA,CAAA,CAAA,qBAAA,CAAA;AAED,IAAM,MAAA,UAAA,GAAa,QAAA,CAAA,MAAA;AAEnB,MAAM,MAAA,KAAA,GAAA,eAA4B,IAAA,GAAA,KAAA,CAAA,GAAA,WAAA,CAAA,KAAA,CAAA;AAChC,MAAA,IAAA,CAAA,SAAc,CAAa,KAAA,CAAA,IAAA,EAAA;AAC3B,QAAA,OAAK;AACH,OAAA;AAAA,MACF,OAAA,OAAA,CAAA,KAAA,EAAA,KAAA,CAAA,IAAA,CAAA,CAAA,KAAA,CAAA;AACA,KAAA,CAAA,CAAA;AAAkC,IACpC,MAAC,eAAA,GAAA,QAAA,CAAA,MAAA;AAED,MAAM,MAAA,EAAA,QAAA,EAAA,GAAA;AACJ,MAAM,MAAA,WAAW;AAEjB,MAAA,IAAA,WAA+B,EAAA;AAE/B,QAAA,UAAiB,CAAA,GAAAA,SAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACf,OAAA;AAAsC,MACxC,MAAA,SAAA,GAAA,WAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,WAAA,CAAA,KAAA,CAAA;AAEA,MAAA,IAAA,kBAA+B,CAAA,IAAA,EAAA;AAC/B,QAAI,MAAA,MAAA,GAAa,OAAY,CAAA,SAAA,EAAA,KAAA,CAAA,IAAA,CAAA,CAAA,KAAA,CAAA;AAC3B,QAAA,IAAA,MAAe,EAAA;AAAA,UACb,KAAA,CAAA,IAAA,CAAA,GAAAA,SAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,SAAA;AACM,OAAA;AAER,MAAA,IAAA,QAAY,KAAA,KAAA,CAAA,EAAA;AACV,QAAA,MAAA,aAA0B,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,IAAO,EAAA,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,QAAA,CAAA,UAAA,CAAA,CAAA,CAAA;AAAA,QACnC,IAAA,aAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AAAA,UACF,KAAA,MAAA,CAAA,IAAA,EAAA,CAAA,CAAA,IAAA,aAAA,EAAA;AAEA,6BAA4B,KAAA,QAAA;AAC1B,cAAM,SAAA;AAIN,YAAI,KAAA,CAAA,CAAA,CAAA,GAAA,EAAA,iBAA0B,EAAA,CAAA;AAC5B,WAAA;AACE,SAAI,MAAA;AACJ,UAAA,KAAA,CAAA,IAAO,CAAA,EAAI,WAAW,CAAS;AAAA,SACjC;AAAA,OAAA;AAEA,MAAM,OAAA,KAAA,CAAA;AAAiB,KACzB,CAAA,CAAA;AAAA,IACF,MAAA,eAAA,GAAA,QAAA,CAAA,MAAA,eAAA,CAAA,KAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA;AAEA,IAAO,MAAA,eAAA,GAAA,CAAA,OAAA,KAAA;AAAA,MACR,MAAA,KAAA,GAAA,eAAA,CAAA,KAAA,CAAA;AAED,MAAA,yBAAiC,KAAA;AAEjC,QAAM,IAAA,CAAA,IAAA,CAAA,OAAA,IAAmB,CAAoB,OAAA;AAC3C,UAAA,WAA8B,CAAA;AAC9B,QACE,IAAA,OACG,CAAO,IAAA,CAAA,OAAU,CAAA,EAAA;AAChB,UAAA,OAAK,IAAgB,CAAA,OAAA,CAAC,QAAS,CAAO,OAAA,CAAA,CAAA;AACtC,SAAI,MAAA;AACF,UAAO,OAAA,IAAA,CAAK,OAAQ,KAAA,OAAgB,CAAA;AAAA,SAC/B;AACL,OAAA,CAAA,CAAA,GAAA,CAAA,CAAA,EAAA,OAAwB,EAAA,QAAA,EAAA,GAAA,IAAA,EAAA,KAAA,IAAA,CAAA,CAAA;AAAA,KAC1B,CAAA;AAAA,IACF,MAGC,qBAAgB,CAAA,MAAG,eAAyB,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AAAA,IAEnD,MAAA,eAAA,GAAA,QAAA,CAAA,MAAA;AAEA,MAAA,IAAM,EAAa,CAAA;AAAA,MAAS,OACV,4BAAY,KAAA,WAAsB,KAAA,CAAA,WAAA,KAAA,CAAA,EAAA,GAAA,WAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,WAAA,CAAA,WAAA,KAAA,IAAA,GAAA,EAAA,GAAA,IAAA,CAAA,CAAA;AAAA,KACpD,CAAA,CAAA;AAEA,IAAA,MAAM,YAAkB,GAAA,QAAA,CAAA,MAAA,CAAA,EAAA,KAAA,CAAA,KAAA,IAAA,EAAA,CAAA,EAAA,CAAA,WAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,WAAA,CAAA,WAAA,KAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AAAA,IAAA,wBAEG,GAAA,CAAA,KAAA,KAAA;AAEM,MACjC,aAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAEA,KAAA,CAAA;AAAqB,IACnB,MAAA,kBAAe,IAAW,KAAG,KAAA;AAA8B,MAC7D,IAAA,EAAA,EAAA,EAAA,CAAA;AAEA,MAAM,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,GAAuD,KAAA,CAAA;AAC3D,MAAA,IAAA,CAAA,MAAA,IAAsB,CAAA,MAAA,EAAA;AAAA,QACxB,OAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA;AAEA,OAAM;AACJ,MAAM,kBAAU,CAAA,OAAW,CAAA,CAAA;AAC3B,MAAI,eAAW,CAAC,KAAQ,GAAA,MAAA,GAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,MAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,MAAA,CAAA,CAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,KAAA,IAAA,GAAA,EAAA,GAAA,CAAA,EAAA,KAAA,CAAA,IAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA;AACtB,MAAA,mBAAmB,GAAA,KAAA,CAAA,GAAA,WAAA,CAAA,IAAA,CAAA,UAAA,EAAA,KAAA,CAAA,IAAA,EAAA,KAAA,EAAA,eAAA,CAAA,KAAA,CAAA,CAAA;AAAA,KACrB,CAAA;AAEA,IAAA,MAAA,qBAA0B,GAAA,MAAA;AAC1B,MAAgB,kBAAA,CAAA;AAIhB,MAAA,WAAA,QAA8B,GAAA,KAAA,CAAA,GAAA,WAAa,CAAA,IAAA,CAAA,iBAAuB,CAAK,IAAA,EAAA,IAAA,EAAA,EAAA,CAAA,CAAA;AAAA,KACzE,CAAA;AAEA,IAAA,MAAM,yBAA8B,KAAA;AAClC,MAAA,MAAA,SAAA,GAAA,UAA4B,CAAA,KAAA,CAAA;AAC5B,MAAA,MAAA,SAAkB,GAAA,IAAA,cAAkB,CAAA;AAAe,QACrD,CAAA,SAAA,GAAA,KAAA;AAEA,OAAM,CAAA,CAAA;AACJ,MAAA,yBAA6B,CAAA,EAAA,CAAA,SAAA,GAAA,UAAA,CAAA,KAAA,EAAA,EAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,CAAA,IAAA,CAAA,MAAA;AAC7B,QAAM,qBAA+B,EAAA,CAAA;AAAA,QACnC,WAAa,CAAA;AAAA,OACd,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA;AACD,QAAA,kBACG,CAAA,GAAA,CAAA,CAAS;AAER,QAAsB,OAAA,OAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA;AACtB,OAAO,CAAA,CAAA;AAAA,KAAA,CACT;AAEE,IAAA,MAAA,QAAA,GAAA,OAAsB,OAAA,EAAA,QAAA,KAAA;AACtB,MAAO,IAAA,oBAAe,CAAG,KAAA,CAAA,IAAA,EAAA;AAAA,QAC1B,OAAA,KAAA,CAAA;AAAA,OACL;AAEA,MAAM,MAAA,WAA+C,GAAA,UAAA,CAAS,QAAa,CAAA,CAAA;AAEzE,MAAI,IAAA,CAAA,eAAA,CAAA,KAAqB,EAAA;AACvB,QAAO,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QACT,OAAA,KAAA,CAAA;AAEA,OAAM;AACN,MAAI,6BAAwB,CAAA,OAAA,CAAA,CAAA;AAC1B,MAAA,IAAA,KAAA,CAAA,MAAgB,KAAA,CAAA,EAAA;AAChB,QAAO,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,IAAA,CAAA,CAAA;AAAA,QACT,OAAA,IAAA,CAAA;AAEA,OAAM;AACN,MAAI,+BAAoB,CAAA,CAAA;AACtB,MAAA,OAAA,UAAe,CAAA,KAAA,CAAA,CAAA,IAAA,CAAA,MAAA;AACf,QAAO,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,IAAA,CAAA,CAAA;AAAA,QACT,OAAA,IAAA,CAAA;AAEA,OAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA;AAEA,QAAA,MAAkB,EAAA,MAAA,EAAA,GAAA,GACf,CAAA;AACC,QAAA,QAAA,IAAe,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,KAAA,EAAA,MAAA,CAAA,CAAA;AACf,QAAO,OAAA,WAAA,GAAA,KAAA,GAAA,OAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA;AAAA,OACR,CAAA,CACA;AACC,KAAM,CAAA;AACN,IAAA,MAAA,gBAAkB,MAAM;AACxB,MAAA,kBAAqB,CAAA,EAAA,CAAA,CAAA;AAA6B,MACpD,eAAC,CAAA,KAAA,GAAA,EAAA,CAAA;AAAA,MACL,gBAAA,GAAA,KAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,UAAA,GAAA,YAAqB;AACrB,MAAA,MAAA,KAAA,GAAA,WAAwB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,WAAA,CAAA,KAAA,CAAA;AACxB,MAAmB,IAAA,CAAA,KAAA,IAAA,CAAA,KAAA,CAAA,IAAA;AAAA,QACrB,OAAA;AAEA,MAAA,mBAA8D,GAAA,OAAA,CAAA,KAAA,EAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAC5D,MAAA,gBAA2B,GAAA,IAAA,CAAA;AAC3B,MAAA,aAAK,CAAS,KAAC,GAAY,KAAA,CAAA,YAAA,CAAA,CAAA;AAE3B,MAAA,MAAM,QAAgB,EAAA,CAAA;AAGtB,MAAmB,aAAA,EAAA,CAAA;AAEnB,MAAc,gBAAA,GAAA;AAEd,KAAA,CAAA;AACA,IAAc,MAAA,UAAA,GAAA,CAAA,EAAA,KAAA;AAEd,MAAmB,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA,CAAA,EAAA,CAAA,EAAA;AAAA,QACrB,QAAA,CAAA,KAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AAEA,OAAM;AACJ,KAAA,CAAA;AACE,IAAS,MAAA,aAAM,MAAO,KAAA;AAAA,MACxB,QAAA,CAAA,KAAA,GAAA,QAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,MAAA,KAAA,MAAA,KAAA,EAAA,CAAA,CAAA;AAAA,KACF,CAAA;AAEA,IAAM,KAAA,CAAA,MAAA,KAAA,CAAA,KAAmD,EAAe,CAAA,GAAA,KAAA;AACtE,MAAA,qBAA0B,GAAA,GAAA,IAAA;AAAsC,MAClE,kBAAA,CAAA,GAAA,GAAA,OAAA,GAAA,EAAA,CAAA,CAAA;AAEA,KAAA,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AAAA,IAAA,YACc,KAAA,CAAA,cAAA,EAAA,CAAA,GAAA,KAAA,kBAAA,CAAA,GAAA,IAAA,EAAA,CAAA,CAAA,CAAA;AAAA,IAAA,MACH,OAAA,GAAA,QAAA,CAAA;AACP,MAAA,GAAA,MAAA,CAAA,KAAA,CAAA;AACA,MAAmB,GAAA,EAAA,WAAA;AAAkB,MACvC,IAAA,EAAA,KAAA;AAAA,MACA,aAAa;AAAK,MACpB,OAAA;AAEA,MAAA,QAAA;AAAA,MACE,OAAY;AAAA,MACZ,QAAC;AAAoC,MACvC,UAAA;AAEA,MAAA;AAA0C,MACxC,aAAe;AAAA,MACf,UAAK;AAAA,MACL,aAAM;AAAA,MACN,QAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACA,OAAA,CAAA,kBAAA,EAAA,OAAA,CAAA,CAAA;AAAA,IACA,SAAA,CAAA,MAAA;AAAA,MACA,IAAA,KAAA,CAAA,IAAA,EAAA;AAAA,QACA,WAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,WAAA,CAAA,QAAA,CAAA,OAAA,CAAA,CAAA;AAAA,QACA,YAAA,GAAA,KAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OACA;AAAA,KACA,CAAA,CAAA;AAAA,IACA,eAAA,CAAA,MAAA;AAAA,MACA,WAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,WAAA,CAAA,WAAA,CAAA,OAAA,CAAA,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAA,MAAA,CAAA;AAEA,MAAA,IAAA,EAAA,KAAgB;AACd,MAAA,eAAgB;AACd,MAAA,aAAA;AACA,MAAe,QAAA;AAAsB,MACvC,aAAA;AAAA,MACD,UAAA;AAED,KAAA,CAAA,CAAA;AACE,IAAA,OAAA,CAAA,IAAA,EAAA;AAAgC,MACjC,IAAA,EAAA,CAAA;AAED,MAAa,OAAAC,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAAA,aAAA;AAAA,QAAA,GAAA,EAAA,WAAA;AAAA,QAAA,KAAA,EAAAC,cAAA,CAAAC,KAAA,CAAA,eAAA,CAAA,CAAA;AAAA,QAIL,IAAA,EAAAA,KAAA,CAAA,OAAA,CAAA,GAAA,OAAA,GAAA,KAAA,CAAA;AAAA,QAAA,iBAAA,EAAAA,KAAA,CAAA,OAAA,CAAA,GAAAA,KAAA,CAAA,OAAA,CAAA,GAAA,KAAA,CAAA;AAAA,OAAA,EAAA;AAAA,QAAAC,WAAA,CAAAD,KAAA,CAAA,aAAA,CAAA,EAAA;AAAA,UAIN,eAAA,EAAAA,KAAA,CAAA,UAAA,CAAA,CAAA,KAAA,KAAA,MAAA;AAAA,UAAA,YAAA,EAAA,CAAA,CAAA,EAAA,GAAAA,KAAA,CAAA,WAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAA,MAAA,MAAA;AAAA,SAAA,EAAA;AAAA,UAAA,OAAA,EAAAE,OAAA,CAAA,MAAA;AAAA,YAIAF,KAAA,CAAA,QAAA,CAAA,IAAAH,SAAA,EAAA,EAAAM,WAAA,CAAAC,uBAAA,CAAAJ,KAAA,CAAA,QAAA,CAAA,GAAA,OAAA,GAAA,KAAA,CAAA,EAAA;AAAA,cAAA,GAAA,EAAA,CAAA;AAAA,cAAA,EAAA,EAAAA,KAAA,CAAA,OAAA,CAAA;AAAA,cAAA,GAAA,EAAAA,KAAA,CAAA,QAAA,CAAA;AAAA,cAIA,KAAA,EAAAD,cAAA,CAAAC,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;AAAA,cAAA,KAAA,EAAAK,cAAA,CAAAL,KAAA,CAAA,UAAA,CAAA,CAAA;AAAA,aAAA,EAAA;AAAA,cAAA,OAAA,EAAAE,OAAA,CAAA,MAAA;AAAA,gBAIAI,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,OAAA,EAAA,EAAA,KAAA,EAAAN,KAAA,CAAA,YAAA,CAAA,EAAA,EAAA,MAAA;AAAA,kBAAAO,eAAA,CAAAC,eAAA,CAAAR,KAAA,CAAA,YAAA,CAAA,CAAA,EAAA,CAAA,CAAA;AAAA,iBAAA,CAAA;AAAA,eAAA,CAAA;AAAA,cAIA,CAAA,EAAA,CAAA;AAAA,aACD,EAAA,CAAA,EAAA,CAAA,IAAA,EAAA,KAAA,EAAA,OAAA,EAAA,OAAA,CAAA,CAAA,IAAAS,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}
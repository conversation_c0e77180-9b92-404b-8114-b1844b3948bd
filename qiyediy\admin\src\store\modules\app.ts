/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 应用状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export type ThemeMode = 'light' | 'dark' | 'auto'
export type LayoutMode = 'vertical' | 'horizontal' | 'mix'
export type SizeType = 'large' | 'default' | 'small'

export const useAppStore = defineStore('app', () => {
  // 状态
  const theme = ref<ThemeMode>('light')
  const layout = ref<LayoutMode>('vertical')
  const size = ref<SizeType>('default')
  const sidebarCollapsed = ref(false)
  const loading = ref(false)
  const locale = ref('zh-CN')
  const breadcrumbEnabled = ref(true)
  const tagsViewEnabled = ref(true)
  const fixedHeader = ref(true)
  const showLogo = ref(true)
  const showSettings = ref(true)
  const pageLoading = ref(false)

  // 设备信息
  const device = ref<'desktop' | 'tablet' | 'mobile'>('desktop')
  const isMobile = computed(() => device.value === 'mobile')
  const isTablet = computed(() => device.value === 'tablet')
  const isDesktop = computed(() => device.value === 'desktop')

  // 窗口尺寸
  const windowWidth = ref(window.innerWidth)
  const windowHeight = ref(window.innerHeight)

  /**
   * 切换主题
   */
  const toggleTheme = (): void => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
    applyTheme()
    saveSettings()
  }

  /**
   * 设置主题
   */
  const setTheme = (newTheme: ThemeMode): void => {
    theme.value = newTheme
    applyTheme()
    saveSettings()
  }

  /**
   * 应用主题
   */
  const applyTheme = (): void => {
    const html = document.documentElement
    
    if (theme.value === 'dark') {
      html.classList.add('dark')
    } else if (theme.value === 'light') {
      html.classList.remove('dark')
    } else {
      // auto 模式根据系统主题
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      if (prefersDark) {
        html.classList.add('dark')
      } else {
        html.classList.remove('dark')
      }
    }
  }

  /**
   * 切换侧边栏
   */
  const toggleSidebar = (): void => {
    sidebarCollapsed.value = !sidebarCollapsed.value
    saveSettings()
  }

  /**
   * 设置侧边栏状态
   */
  const setSidebarCollapsed = (collapsed: boolean): void => {
    sidebarCollapsed.value = collapsed
    saveSettings()
  }

  /**
   * 设置布局模式
   */
  const setLayout = (newLayout: LayoutMode): void => {
    layout.value = newLayout
    saveSettings()
  }

  /**
   * 设置组件尺寸
   */
  const setSize = (newSize: SizeType): void => {
    size.value = newSize
    saveSettings()
  }

  /**
   * 设置语言
   */
  const setLocale = (newLocale: string): void => {
    locale.value = newLocale
    saveSettings()
  }

  /**
   * 设置面包屑
   */
  const setBreadcrumbEnabled = (enabled: boolean): void => {
    breadcrumbEnabled.value = enabled
    saveSettings()
  }

  /**
   * 设置标签页
   */
  const setTagsViewEnabled = (enabled: boolean): void => {
    tagsViewEnabled.value = enabled
    saveSettings()
  }

  /**
   * 设置固定头部
   */
  const setFixedHeader = (fixed: boolean): void => {
    fixedHeader.value = fixed
    saveSettings()
  }

  /**
   * 设置显示Logo
   */
  const setShowLogo = (show: boolean): void => {
    showLogo.value = show
    saveSettings()
  }

  /**
   * 设置显示设置
   */
  const setShowSettings = (show: boolean): void => {
    showSettings.value = show
    saveSettings()
  }

  /**
   * 设置全局加载状态
   */
  const setLoading = (isLoading: boolean): void => {
    loading.value = isLoading
  }

  /**
   * 设置页面加载状态
   */
  const setPageLoading = (isLoading: boolean): void => {
    pageLoading.value = isLoading
  }

  /**
   * 更新设备信息
   */
  const updateDevice = (): void => {
    const width = window.innerWidth
    windowWidth.value = width
    windowHeight.value = window.innerHeight

    if (width < 768) {
      device.value = 'mobile'
      // 移动端自动收起侧边栏
      sidebarCollapsed.value = true
    } else if (width < 1024) {
      device.value = 'tablet'
    } else {
      device.value = 'desktop'
      // 桌面端恢复侧边栏状态
      const savedSettings = getSavedSettings()
      if (savedSettings.sidebarCollapsed !== undefined) {
        sidebarCollapsed.value = savedSettings.sidebarCollapsed
      }
    }
  }

  /**
   * 保存设置到本地存储
   */
  const saveSettings = (): void => {
    const settings = {
      theme: theme.value,
      layout: layout.value,
      size: size.value,
      sidebarCollapsed: sidebarCollapsed.value,
      locale: locale.value,
      breadcrumbEnabled: breadcrumbEnabled.value,
      tagsViewEnabled: tagsViewEnabled.value,
      fixedHeader: fixedHeader.value,
      showLogo: showLogo.value,
      showSettings: showSettings.value
    }
    
    localStorage.setItem('app-settings', JSON.stringify(settings))
  }

  /**
   * 从本地存储获取设置
   */
  const getSavedSettings = (): any => {
    try {
      const saved = localStorage.getItem('app-settings')
      return saved ? JSON.parse(saved) : {}
    } catch (error) {
      console.error('解析应用设置失败:', error)
      return {}
    }
  }

  /**
   * 加载设置
   */
  const loadSettings = (): void => {
    const settings = getSavedSettings()
    
    if (settings.theme) theme.value = settings.theme
    if (settings.layout) layout.value = settings.layout
    if (settings.size) size.value = settings.size
    if (settings.sidebarCollapsed !== undefined) sidebarCollapsed.value = settings.sidebarCollapsed
    if (settings.locale) locale.value = settings.locale
    if (settings.breadcrumbEnabled !== undefined) breadcrumbEnabled.value = settings.breadcrumbEnabled
    if (settings.tagsViewEnabled !== undefined) tagsViewEnabled.value = settings.tagsViewEnabled
    if (settings.fixedHeader !== undefined) fixedHeader.value = settings.fixedHeader
    if (settings.showLogo !== undefined) showLogo.value = settings.showLogo
    if (settings.showSettings !== undefined) showSettings.value = settings.showSettings
  }

  /**
   * 重置设置
   */
  const resetSettings = (): void => {
    theme.value = 'light'
    layout.value = 'vertical'
    size.value = 'default'
    sidebarCollapsed.value = false
    locale.value = 'zh-CN'
    breadcrumbEnabled.value = true
    tagsViewEnabled.value = true
    fixedHeader.value = true
    showLogo.value = true
    showSettings.value = true
    
    localStorage.removeItem('app-settings')
    applyTheme()
  }

  /**
   * 初始化主题
   */
  const initTheme = (): void => {
    // 监听系统主题变化
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      mediaQuery.addEventListener('change', () => {
        if (theme.value === 'auto') {
          applyTheme()
        }
      })
    }
    
    applyTheme()
  }

  /**
   * 初始化应用
   */
  const initApp = (): void => {
    loadSettings()
    updateDevice()
    initTheme()
    
    // 监听窗口大小变化
    window.addEventListener('resize', updateDevice)
  }

  /**
   * 销毁应用
   */
  const destroyApp = (): void => {
    window.removeEventListener('resize', updateDevice)
  }

  // 初始化
  initApp()

  return {
    // 状态
    theme: readonly(theme),
    layout: readonly(layout),
    size: readonly(size),
    sidebarCollapsed: readonly(sidebarCollapsed),
    loading: readonly(loading),
    locale: readonly(locale),
    breadcrumbEnabled: readonly(breadcrumbEnabled),
    tagsViewEnabled: readonly(tagsViewEnabled),
    fixedHeader: readonly(fixedHeader),
    showLogo: readonly(showLogo),
    showSettings: readonly(showSettings),
    pageLoading: readonly(pageLoading),
    device: readonly(device),
    windowWidth: readonly(windowWidth),
    windowHeight: readonly(windowHeight),
    
    // 计算属性
    isMobile,
    isTablet,
    isDesktop,
    
    // 方法
    toggleTheme,
    setTheme,
    applyTheme,
    toggleSidebar,
    setSidebarCollapsed,
    setLayout,
    setSize,
    setLocale,
    setBreadcrumbEnabled,
    setTagsViewEnabled,
    setFixedHeader,
    setShowLogo,
    setShowSettings,
    setLoading,
    setPageLoading,
    updateDevice,
    saveSettings,
    loadSettings,
    resetSettings,
    initTheme,
    initApp,
    destroyApp
  }
})

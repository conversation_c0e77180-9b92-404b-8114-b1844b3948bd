<!--
  三只鱼网络科技 | 韩总 | 2024-12-20
  QiyeDIY企业建站系统 - 前端组件渲染器
-->

<template>
  <div 
    class="component-renderer"
    :class="componentClasses"
    :style="componentStyles"
  >
    <!-- 文本组件 -->
    <div v-if="component.type === 'text'" class="text-component">
      <div 
        v-html="component.content.text || ''"
        :style="textStyles"
      ></div>
    </div>

    <!-- 图片组件 -->
    <div v-else-if="component.type === 'image'" class="image-component">
      <NuxtImg 
        :src="component.content.src || '/images/placeholder.png'"
        :alt="component.content.alt || '图片'"
        :style="imageStyles"
        loading="lazy"
        @error="handleImageError"
      />
    </div>

    <!-- 按钮组件 -->
    <div v-else-if="component.type === 'button'" class="button-component">
      <NuxtLink
        v-if="component.content.link"
        :to="component.content.link"
        class="component-button"
        :class="buttonClasses"
        :style="buttonStyles"
      >
        {{ component.content.text || '按钮' }}
      </NuxtLink>
      <button
        v-else
        class="component-button"
        :class="buttonClasses"
        :style="buttonStyles"
        @click="handleButtonClick"
      >
        {{ component.content.text || '按钮' }}
      </button>
    </div>

    <!-- 容器组件 -->
    <div v-else-if="component.type === 'container'" class="container-component" :style="containerStyles">
      <ComponentRenderer
        v-for="child in component.children || []"
        :key="child.id"
        :component="child"
        :page-config="pageConfig"
      />
    </div>

    <!-- 分割线组件 -->
    <div v-else-if="component.type === 'divider'" class="divider-component">
      <hr :style="dividerStyles" />
      <span v-if="component.content.text" class="divider-text">
        {{ component.content.text }}
      </span>
    </div>

    <!-- 视频组件 -->
    <div v-else-if="component.type === 'video'" class="video-component">
      <video 
        :src="component.content.src"
        :poster="component.content.poster"
        :controls="component.config.controls !== false"
        :autoplay="component.config.autoplay"
        :loop="component.config.loop"
        :muted="component.config.muted"
        :style="videoStyles"
      >
        您的浏览器不支持视频播放
      </video>
    </div>

    <!-- 轮播图组件 -->
    <div v-else-if="component.type === 'carousel'" class="carousel-component">
      <div class="carousel-container" :style="carouselStyles">
        <div 
          class="carousel-track"
          :style="{ transform: `translateX(-${currentSlide * 100}%)` }"
        >
          <div 
            v-for="(item, index) in component.content.items || []"
            :key="index"
            class="carousel-slide"
          >
            <NuxtImg 
              :src="item.image"
              :alt="item.alt || `轮播图 ${index + 1}`"
              loading="lazy"
            />
            <div v-if="item.title || item.description" class="carousel-caption">
              <h3 v-if="item.title">{{ item.title }}</h3>
              <p v-if="item.description">{{ item.description }}</p>
            </div>
          </div>
        </div>
        
        <!-- 导航点 -->
        <div v-if="component.config.showDots" class="carousel-dots">
          <button
            v-for="(item, index) in component.content.items || []"
            :key="index"
            class="carousel-dot"
            :class="{ active: index === currentSlide }"
            @click="goToSlide(index)"
          ></button>
        </div>
        
        <!-- 导航箭头 -->
        <div v-if="component.config.showArrows" class="carousel-arrows">
          <button class="carousel-arrow prev" @click="prevSlide">‹</button>
          <button class="carousel-arrow next" @click="nextSlide">›</button>
        </div>
      </div>
    </div>

    <!-- 表单组件 -->
    <div v-else-if="component.type === 'form'" class="form-component">
      <form @submit.prevent="handleFormSubmit" :style="formStyles">
        <div
          v-for="field in component.content.fields || []"
          :key="field.name"
          class="form-field"
        >
          <label v-if="field.label" :for="field.name">
            {{ field.label }}
            <span v-if="field.required" class="required">*</span>
          </label>
          
          <input
            v-if="field.type === 'input'"
            :id="field.name"
            v-model="formData[field.name]"
            :type="field.inputType || 'text'"
            :placeholder="field.placeholder"
            :required="field.required"
          />
          
          <textarea
            v-else-if="field.type === 'textarea'"
            :id="field.name"
            v-model="formData[field.name]"
            :rows="field.rows || 3"
            :placeholder="field.placeholder"
            :required="field.required"
          ></textarea>
          
          <select
            v-else-if="field.type === 'select'"
            :id="field.name"
            v-model="formData[field.name]"
            :required="field.required"
          >
            <option value="">{{ field.placeholder || '请选择' }}</option>
            <option
              v-for="option in field.options || []"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </option>
          </select>
          
          <div v-else-if="field.type === 'checkbox'" class="checkbox-group">
            <label
              v-for="option in field.options || []"
              :key="option.value"
              class="checkbox-item"
            >
              <input
                type="checkbox"
                :value="option.value"
                v-model="formData[field.name]"
              />
              {{ option.label }}
            </label>
          </div>
          
          <div v-else-if="field.type === 'radio'" class="radio-group">
            <label
              v-for="option in field.options || []"
              :key="option.value"
              class="radio-item"
            >
              <input
                type="radio"
                :name="field.name"
                :value="option.value"
                v-model="formData[field.name]"
              />
              {{ option.label }}
            </label>
          </div>
        </div>
        
        <button type="submit" class="form-submit" :style="submitButtonStyles">
          {{ component.content.submitText || '提交' }}
        </button>
      </form>
    </div>

    <!-- 列表组件 -->
    <div v-else-if="component.type === 'list'" class="list-component">
      <div class="list-container" :style="listStyles">
        <div
          v-for="(item, index) in component.content.items || []"
          :key="index"
          class="list-item"
          :style="listItemStyles"
        >
          <div v-if="item.image" class="list-item-image">
            <NuxtImg :src="item.image" :alt="item.title" loading="lazy" />
          </div>
          <div class="list-item-content">
            <h3 v-if="item.title" class="list-item-title">{{ item.title }}</h3>
            <p v-if="item.description" class="list-item-description">{{ item.description }}</p>
            <NuxtLink
              v-if="item.link"
              :to="item.link"
              class="list-item-link"
            >
              {{ item.linkText || '查看详情' }}
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>

    <!-- 未知组件类型 -->
    <div v-else class="unknown-component">
      <div class="unknown-content">
        <p>未知组件类型: {{ component.type }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import type { DiyComponent, PageConfig } from '~/types/diy'

interface Props {
  component: DiyComponent
  pageConfig?: PageConfig
}

const props = withDefaults(defineProps<Props>(), {
  pageConfig: () => ({})
})

// 响应式数据
const currentSlide = ref(0)
const formData = reactive<Record<string, any>>({})
const carouselTimer = ref<NodeJS.Timeout | null>(null)

// 计算属性
const componentClasses = computed(() => {
  return {
    [`component-${props.component.type}`]: true,
    'is-enabled': props.component.is_enabled
  }
})

const componentStyles = computed(() => {
  const config = props.component.config || {}
  const styles: Record<string, any> = {}
  
  // 通用样式
  if (config.width) styles.width = config.width
  if (config.height) styles.height = config.height
  if (config.margin) styles.margin = config.margin
  if (config.padding) styles.padding = config.padding
  if (config.background) styles.background = config.background
  if (config.border) styles.border = config.border
  if (config.borderRadius) styles.borderRadius = config.borderRadius
  if (config.boxShadow) styles.boxShadow = config.boxShadow
  
  // 边距
  if (config.marginTop) styles.marginTop = config.marginTop + 'px'
  if (config.marginRight) styles.marginRight = config.marginRight + 'px'
  if (config.marginBottom) styles.marginBottom = config.marginBottom + 'px'
  if (config.marginLeft) styles.marginLeft = config.marginLeft + 'px'
  if (config.paddingTop) styles.paddingTop = config.paddingTop + 'px'
  if (config.paddingRight) styles.paddingRight = config.paddingRight + 'px'
  if (config.paddingBottom) styles.paddingBottom = config.paddingBottom + 'px'
  if (config.paddingLeft) styles.paddingLeft = config.paddingLeft + 'px'
  
  // 边框
  if (config.borderWidth) {
    styles.borderWidth = config.borderWidth + 'px'
    styles.borderStyle = config.borderStyle || 'solid'
    styles.borderColor = config.borderColor || '#e5e5e5'
  }
  
  return styles
})

const textStyles = computed(() => {
  const config = props.component.config || {}
  const styles: Record<string, any> = {}
  
  if (config.fontSize) styles.fontSize = config.fontSize + 'px'
  if (config.color) styles.color = config.color
  if (config.textAlign) styles.textAlign = config.textAlign
  if (config.lineHeight) styles.lineHeight = config.lineHeight
  if (config.fontWeight) styles.fontWeight = config.fontWeight
  if (config.fontFamily) styles.fontFamily = config.fontFamily
  
  return styles
})

const imageStyles = computed(() => {
  const config = props.component.config || {}
  const styles: Record<string, any> = {}
  
  if (config.width) styles.width = config.width
  if (config.height) styles.height = config.height
  if (config.objectFit) styles.objectFit = config.objectFit
  if (config.borderRadius) styles.borderRadius = config.borderRadius + 'px'
  
  return styles
})

const buttonClasses = computed(() => {
  const config = props.component.config || {}
  return {
    [`btn-${config.type || 'primary'}`]: true,
    [`btn-${config.size || 'default'}`]: true,
    'btn-plain': config.plain,
    'btn-round': config.round,
    'btn-circle': config.circle
  }
})

const buttonStyles = computed(() => {
  const config = props.component.config || {}
  const styles: Record<string, any> = {}
  
  if (config.width) styles.width = config.width
  if (config.height) styles.height = config.height
  if (config.fontSize) styles.fontSize = config.fontSize + 'px'
  if (config.backgroundColor) styles.backgroundColor = config.backgroundColor
  if (config.color) styles.color = config.color
  if (config.borderRadius) styles.borderRadius = config.borderRadius + 'px'
  
  return styles
})

const containerStyles = computed(() => {
  const config = props.component.config || {}
  const styles: Record<string, any> = {}
  
  if (config.display) styles.display = config.display
  if (config.flexDirection) styles.flexDirection = config.flexDirection
  if (config.justifyContent) styles.justifyContent = config.justifyContent
  if (config.alignItems) styles.alignItems = config.alignItems
  if (config.gap) styles.gap = config.gap + 'px'
  if (config.minHeight) styles.minHeight = config.minHeight + 'px'
  
  return styles
})

const dividerStyles = computed(() => {
  const config = props.component.config || {}
  const styles: Record<string, any> = {}
  
  if (config.color) styles.borderColor = config.color
  if (config.width) styles.borderWidth = config.width + 'px'
  if (config.style) styles.borderStyle = config.style
  
  return styles
})

const videoStyles = computed(() => {
  const config = props.component.config || {}
  const styles: Record<string, any> = {}
  
  if (config.width) styles.width = config.width
  if (config.height) styles.height = config.height
  if (config.objectFit) styles.objectFit = config.objectFit
  
  return styles
})

const carouselStyles = computed(() => {
  const config = props.component.config || {}
  const styles: Record<string, any> = {}
  
  if (config.height) styles.height = config.height
  
  return styles
})

const formStyles = computed(() => {
  const config = props.component.config || {}
  const styles: Record<string, any> = {}
  
  if (config.maxWidth) styles.maxWidth = config.maxWidth
  
  return styles
})

const submitButtonStyles = computed(() => {
  const config = props.component.config || {}
  const styles: Record<string, any> = {}
  
  if (config.submitButtonColor) styles.backgroundColor = config.submitButtonColor
  
  return styles
})

const listStyles = computed(() => {
  const config = props.component.config || {}
  const styles: Record<string, any> = {}
  
  if (config.columns) {
    styles.display = 'grid'
    styles.gridTemplateColumns = `repeat(${config.columns}, 1fr)`
    styles.gap = (config.gap || 20) + 'px'
  }
  
  return styles
})

const listItemStyles = computed(() => {
  const config = props.component.config || {}
  const styles: Record<string, any> = {}
  
  if (config.itemBackground) styles.background = config.itemBackground
  if (config.itemPadding) styles.padding = config.itemPadding
  if (config.itemBorderRadius) styles.borderRadius = config.itemBorderRadius + 'px'
  
  return styles
})

/**
 * 处理按钮点击
 */
const handleButtonClick = () => {
  // 可以添加自定义点击事件处理
  console.log('按钮被点击')
}

/**
 * 处理图片错误
 */
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/image-error.png'
}

/**
 * 轮播图控制
 */
const goToSlide = (index: number) => {
  const items = props.component.content.items || []
  currentSlide.value = Math.max(0, Math.min(index, items.length - 1))
}

const nextSlide = () => {
  const items = props.component.content.items || []
  currentSlide.value = (currentSlide.value + 1) % items.length
}

const prevSlide = () => {
  const items = props.component.content.items || []
  currentSlide.value = currentSlide.value === 0 ? items.length - 1 : currentSlide.value - 1
}

const startAutoPlay = () => {
  const config = props.component.config || {}
  if (config.autoplay && config.interval) {
    carouselTimer.value = setInterval(() => {
      nextSlide()
    }, config.interval * 1000)
  }
}

const stopAutoPlay = () => {
  if (carouselTimer.value) {
    clearInterval(carouselTimer.value)
    carouselTimer.value = null
  }
}

/**
 * 处理表单提交
 */
const handleFormSubmit = async () => {
  try {
    // 表单验证
    const fields = props.component.content.fields || []
    for (const field of fields) {
      if (field.required && !formData[field.name]) {
        alert(`请填写${field.label}`)
        return
      }
    }
    
    // 提交表单数据
    const response = await $fetch('/api/forms/submit', {
      method: 'POST',
      body: {
        form_id: props.component.id,
        data: formData
      }
    })
    
    if (response.code === 200) {
      alert('提交成功！')
      // 重置表单
      Object.keys(formData).forEach(key => {
        formData[key] = ''
      })
    } else {
      alert(response.message || '提交失败')
    }
    
  } catch (error) {
    console.error('表单提交失败:', error)
    alert('提交失败，请稍后重试')
  }
}

// 生命周期
onMounted(() => {
  // 初始化表单数据
  if (props.component.type === 'form') {
    const fields = props.component.content.fields || []
    fields.forEach(field => {
      if (field.type === 'checkbox') {
        formData[field.name] = []
      } else {
        formData[field.name] = field.defaultValue || ''
      }
    })
  }
  
  // 启动轮播图自动播放
  if (props.component.type === 'carousel') {
    startAutoPlay()
  }
})

onUnmounted(() => {
  stopAutoPlay()
})
</script>

<style lang="scss" scoped>
.component-renderer {
  width: 100%;
  
  &:not(.is-enabled) {
    opacity: 0.5;
    pointer-events: none;
  }
}

// 文本组件样式
.text-component {
  :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
    margin: 0.5em 0;
  }
  
  :deep(p) {
    margin: 0.5em 0;
  }
  
  :deep(a) {
    color: #409eff;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

// 图片组件样式
.image-component {
  img {
    max-width: 100%;
    height: auto;
    display: block;
  }
}

// 按钮组件样式
.button-component {
  .component-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
    
    &.btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
      }
    }
    
    &.btn-success {
      background: #67c23a;
      color: white;
    }
    
    &.btn-warning {
      background: #e6a23c;
      color: white;
    }
    
    &.btn-danger {
      background: #f56c6c;
      color: white;
    }
    
    &.btn-info {
      background: #909399;
      color: white;
    }
    
    &.btn-default {
      background: #ffffff;
      color: #606266;
      border-color: #dcdfe6;
    }
    
    &.btn-large {
      padding: 1rem 2rem;
      font-size: 1.125rem;
    }
    
    &.btn-small {
      padding: 0.5rem 1rem;
      font-size: 0.875rem;
    }
    
    &.btn-plain {
      background: transparent;
      border-color: currentColor;
    }
    
    &.btn-round {
      border-radius: 2rem;
    }
    
    &.btn-circle {
      border-radius: 50%;
      width: 3rem;
      height: 3rem;
      padding: 0;
    }
  }
}

// 容器组件样式
.container-component {
  min-height: 50px;
}

// 分割线组件样式
.divider-component {
  position: relative;
  text-align: center;
  margin: 1rem 0;
  
  hr {
    border: none;
    border-top: 1px solid #e5e5e5;
    margin: 0;
  }
  
  .divider-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 0 1rem;
    color: #666;
    font-size: 0.875rem;
  }
}

// 视频组件样式
.video-component {
  video {
    max-width: 100%;
    height: auto;
  }
}

// 轮播图组件样式
.carousel-component {
  .carousel-container {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
    height: 400px;
  }
  
  .carousel-track {
    display: flex;
    transition: transform 0.5s ease;
    height: 100%;
  }
  
  .carousel-slide {
    min-width: 100%;
    position: relative;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .carousel-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 2rem;
    
    h3 {
      margin: 0 0 0.5rem 0;
      font-size: 1.5rem;
    }
    
    p {
      margin: 0;
      opacity: 0.9;
    }
  }
  
  .carousel-dots {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 0.5rem;
  }
  
  .carousel-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: background 0.3s ease;
    
    &.active {
      background: white;
    }
  }
  
  .carousel-arrows {
    .carousel-arrow {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 3rem;
      height: 3rem;
      border: none;
      background: rgba(0, 0, 0, 0.5);
      color: white;
      font-size: 1.5rem;
      cursor: pointer;
      transition: background 0.3s ease;
      
      &:hover {
        background: rgba(0, 0, 0, 0.7);
      }
      
      &.prev {
        left: 1rem;
      }
      
      &.next {
        right: 1rem;
      }
    }
  }
}

// 表单组件样式
.form-component {
  .form-field {
    margin-bottom: 1.5rem;
    
    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #333;
      
      .required {
        color: #f56c6c;
      }
    }
    
    input, textarea, select {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #dcdfe6;
      border-radius: 0.25rem;
      font-size: 1rem;
      transition: border-color 0.3s ease;
      
      &:focus {
        outline: none;
        border-color: #409eff;
      }
    }
    
    .checkbox-group, .radio-group {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
    }
    
    .checkbox-item, .radio-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      
      input {
        width: auto;
      }
    }
  }
  
  .form-submit {
    background: #409eff;
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 0.25rem;
    font-size: 1rem;
    cursor: pointer;
    transition: background 0.3s ease;
    
    &:hover {
      background: #337ecc;
    }
  }
}

// 列表组件样式
.list-component {
  .list-item {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
    transition: transform 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
    }
  }
  
  .list-item-image {
    flex-shrink: 0;
    width: 100px;
    height: 100px;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 0.25rem;
    }
  }
  
  .list-item-content {
    flex: 1;
  }
  
  .list-item-title {
    margin: 0 0 0.5rem 0;
    font-size: 1.125rem;
    font-weight: 600;
  }
  
  .list-item-description {
    margin: 0 0 1rem 0;
    color: #666;
    line-height: 1.5;
  }
  
  .list-item-link {
    color: #409eff;
    text-decoration: none;
    font-weight: 500;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

// 未知组件样式
.unknown-component {
  padding: 2rem;
  text-align: center;
  background: #f5f5f5;
  border-radius: 0.5rem;
  color: #666;
}

// 响应式设计
@media (max-width: 768px) {
  .carousel-container {
    height: 250px !important;
  }
  
  .carousel-caption {
    padding: 1rem;
    
    h3 {
      font-size: 1.125rem;
    }
  }
  
  .list-item {
    flex-direction: column;
    
    .list-item-image {
      width: 100%;
      height: 200px;
    }
  }
  
  .list-container {
    grid-template-columns: 1fr !important;
  }
}
</style>

<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-20
 * QiyeDIY企业建站系统 - 搜索服务类
 */

declare(strict_types=1);

namespace app\service;

use think\facade\Db;
use think\facade\Cache;
use think\facade\Log;

/**
 * 搜索服务类
 */
class SearchService extends BaseService
{
    /**
     * 全站搜索
     * @param array $params
     * @return array
     */
    public function search(array $params): array
    {
        $keyword = $params['keyword'];
        $type = $params['type'] ?? 'all';
        $page = (int)($params['page'] ?? 1);
        $perPage = (int)($params['per_page'] ?? 20);
        $sort = $params['sort'] ?? 'relevance';
        $order = $params['order'] ?? 'desc';

        // 记录搜索历史
        $this->recordSearchHistory($keyword, $type);

        $results = [];
        $total = 0;

        switch ($type) {
            case 'page':
                $results = $this->searchPages($keyword, $page, $perPage, $sort, $order);
                break;
            case 'template':
                $results = $this->searchTemplates($keyword, $page, $perPage, $sort, $order);
                break;
            case 'user':
                $results = $this->searchUsers($keyword, $page, $perPage, $sort, $order);
                break;
            default:
                $results = $this->searchAll($keyword, $page, $perPage, $sort, $order);
                break;
        }

        return [
            'data' => $results['data'],
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $results['total'],
                'last_page' => ceil($results['total'] / $perPage)
            ],
            'keyword' => $keyword,
            'type' => $type,
            'took' => $results['took'] ?? 0
        ];
    }

    /**
     * 搜索页面
     * @param string $keyword
     * @param int $page
     * @param int $perPage
     * @param string $sort
     * @param string $order
     * @return array
     */
    private function searchPages(string $keyword, int $page, int $perPage, string $sort, string $order): array
    {
        $startTime = microtime(true);

        $query = Db::name('diy_pages')
            ->where('is_published', 1)
            ->where(function ($query) use ($keyword) {
                $query->whereLike('title', "%{$keyword}%")
                    ->whereOr('description', 'like', "%{$keyword}%")
                    ->whereOr('content', 'like', "%{$keyword}%")
                    ->whereOr('tags', 'like', "%{$keyword}%");
            });

        // 排序
        switch ($sort) {
            case 'created_at':
                $query->order('created_at', $order);
                break;
            case 'updated_at':
                $query->order('updated_at', $order);
                break;
            case 'views':
                $query->order('views', $order);
                break;
            default:
                // 相关性排序（简单实现）
                $query->orderRaw("
                    CASE 
                        WHEN title LIKE '%{$keyword}%' THEN 3
                        WHEN description LIKE '%{$keyword}%' THEN 2
                        WHEN content LIKE '%{$keyword}%' THEN 1
                        ELSE 0
                    END DESC
                ");
                break;
        }

        $total = $query->count();
        $data = $query->page($page, $perPage)
            ->field('id,title,slug,description,thumbnail,views,created_at,updated_at')
            ->select()
            ->toArray();

        // 高亮关键词
        foreach ($data as &$item) {
            $item['title'] = $this->highlightKeyword($item['title'], $keyword);
            $item['description'] = $this->highlightKeyword($item['description'], $keyword);
            $item['type'] = 'page';
        }

        $took = round((microtime(true) - $startTime) * 1000, 2);

        return [
            'data' => $data,
            'total' => $total,
            'took' => $took
        ];
    }

    /**
     * 搜索模板
     * @param string $keyword
     * @param int $page
     * @param int $perPage
     * @param string $sort
     * @param string $order
     * @return array
     */
    private function searchTemplates(string $keyword, int $page, int $perPage, string $sort, string $order): array
    {
        $startTime = microtime(true);

        $query = Db::name('diy_templates')
            ->where('status', 'published')
            ->where(function ($query) use ($keyword) {
                $query->whereLike('name', "%{$keyword}%")
                    ->whereOr('description', 'like', "%{$keyword}%")
                    ->whereOr('tags', 'like', "%{$keyword}%")
                    ->whereOr('category', 'like', "%{$keyword}%");
            });

        // 排序
        switch ($sort) {
            case 'created_at':
                $query->order('created_at', $order);
                break;
            case 'updated_at':
                $query->order('updated_at', $order);
                break;
            case 'downloads':
                $query->order('downloads', $order);
                break;
            default:
                $query->orderRaw("
                    CASE 
                        WHEN name LIKE '%{$keyword}%' THEN 3
                        WHEN description LIKE '%{$keyword}%' THEN 2
                        WHEN tags LIKE '%{$keyword}%' THEN 1
                        ELSE 0
                    END DESC
                ");
                break;
        }

        $total = $query->count();
        $data = $query->page($page, $perPage)
            ->field('id,name,description,preview_image,category,tags,downloads,rating,created_at')
            ->select()
            ->toArray();

        // 高亮关键词
        foreach ($data as &$item) {
            $item['name'] = $this->highlightKeyword($item['name'], $keyword);
            $item['description'] = $this->highlightKeyword($item['description'], $keyword);
            $item['type'] = 'template';
        }

        $took = round((microtime(true) - $startTime) * 1000, 2);

        return [
            'data' => $data,
            'total' => $total,
            'took' => $took
        ];
    }

    /**
     * 搜索用户
     * @param string $keyword
     * @param int $page
     * @param int $perPage
     * @param string $sort
     * @param string $order
     * @return array
     */
    private function searchUsers(string $keyword, int $page, int $perPage, string $sort, string $order): array
    {
        $startTime = microtime(true);

        $query = Db::name('users')
            ->where('status', 'active')
            ->where(function ($query) use ($keyword) {
                $query->whereLike('username', "%{$keyword}%")
                    ->whereOr('nickname', 'like', "%{$keyword}%")
                    ->whereOr('email', 'like', "%{$keyword}%");
            });

        // 排序
        switch ($sort) {
            case 'created_at':
                $query->order('created_at', $order);
                break;
            case 'updated_at':
                $query->order('last_login_at', $order);
                break;
            default:
                $query->orderRaw("
                    CASE 
                        WHEN username LIKE '%{$keyword}%' THEN 3
                        WHEN nickname LIKE '%{$keyword}%' THEN 2
                        WHEN email LIKE '%{$keyword}%' THEN 1
                        ELSE 0
                    END DESC
                ");
                break;
        }

        $total = $query->count();
        $data = $query->page($page, $perPage)
            ->field('id,username,nickname,avatar,role,created_at,last_login_at')
            ->select()
            ->toArray();

        // 高亮关键词
        foreach ($data as &$item) {
            $item['username'] = $this->highlightKeyword($item['username'], $keyword);
            $item['nickname'] = $this->highlightKeyword($item['nickname'], $keyword);
            $item['type'] = 'user';
        }

        $took = round((microtime(true) - $startTime) * 1000, 2);

        return [
            'data' => $data,
            'total' => $total,
            'took' => $took
        ];
    }

    /**
     * 全部搜索
     * @param string $keyword
     * @param int $page
     * @param int $perPage
     * @param string $sort
     * @param string $order
     * @return array
     */
    private function searchAll(string $keyword, int $page, int $perPage, string $sort, string $order): array
    {
        $startTime = microtime(true);

        // 分别搜索各类型数据
        $pageResults = $this->searchPages($keyword, 1, 5, $sort, $order);
        $templateResults = $this->searchTemplates($keyword, 1, 5, $sort, $order);
        $userResults = $this->searchUsers($keyword, 1, 5, $sort, $order);

        // 合并结果
        $allData = array_merge(
            $pageResults['data'],
            $templateResults['data'],
            $userResults['data']
        );

        $total = $pageResults['total'] + $templateResults['total'] + $userResults['total'];

        // 分页处理
        $offset = ($page - 1) * $perPage;
        $data = array_slice($allData, $offset, $perPage);

        $took = round((microtime(true) - $startTime) * 1000, 2);

        return [
            'data' => $data,
            'total' => $total,
            'took' => $took,
            'summary' => [
                'pages' => $pageResults['total'],
                'templates' => $templateResults['total'],
                'users' => $userResults['total']
            ]
        ];
    }

    /**
     * 高亮关键词
     * @param string $text
     * @param string $keyword
     * @return string
     */
    private function highlightKeyword(string $text, string $keyword): string
    {
        if (empty($text) || empty($keyword)) {
            return $text;
        }

        return preg_replace(
            '/(' . preg_quote($keyword, '/') . ')/i',
            '<mark>$1</mark>',
            $text
        );
    }

    /**
     * 获取搜索建议
     * @param string $keyword
     * @param int $limit
     * @return array
     */
    public function getSuggestions(string $keyword, int $limit = 10): array
    {
        $cacheKey = "search_suggestions_{$keyword}_{$limit}";
        
        return Cache::remember($cacheKey, function () use ($keyword, $limit) {
            $suggestions = [];

            // 从页面标题获取建议
            $pageTitles = Db::name('diy_pages')
                ->where('is_published', 1)
                ->whereLike('title', "%{$keyword}%")
                ->limit($limit)
                ->column('title');

            // 从模板名称获取建议
            $templateNames = Db::name('diy_templates')
                ->where('status', 'published')
                ->whereLike('name', "%{$keyword}%")
                ->limit($limit)
                ->column('name');

            $suggestions = array_merge($pageTitles, $templateNames);
            $suggestions = array_unique($suggestions);
            $suggestions = array_slice($suggestions, 0, $limit);

            return $suggestions;
        }, 300); // 缓存5分钟
    }

    /**
     * 获取热门搜索关键词
     * @param int $limit
     * @param string $period
     * @return array
     */
    public function getHotKeywords(int $limit = 10, string $period = 'week'): array
    {
        $cacheKey = "hot_keywords_{$period}_{$limit}";
        
        return Cache::remember($cacheKey, function () use ($limit, $period) {
            $startDate = $this->getPeriodStartDate($period);

            return Db::name('search_logs')
                ->where('created_at', '>=', $startDate)
                ->group('keyword')
                ->field('keyword, COUNT(*) as count')
                ->order('count', 'desc')
                ->limit($limit)
                ->select()
                ->toArray();
        }, 3600); // 缓存1小时
    }

    /**
     * 记录搜索历史
     * @param string $keyword
     * @param string $type
     * @return void
     */
    private function recordSearchHistory(string $keyword, string $type): void
    {
        try {
            $userId = $this->getCurrentUserId();
            $ip = request()->ip();
            $userAgent = request()->header('User-Agent');

            Db::name('search_logs')->insert([
                'user_id' => $userId,
                'keyword' => $keyword,
                'type' => $type,
                'ip' => $ip,
                'user_agent' => $userAgent,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            // 更新用户搜索历史
            if ($userId) {
                $this->updateUserSearchHistory($userId, $keyword);
            }

        } catch (\Exception $e) {
            Log::error('记录搜索历史失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新用户搜索历史
     * @param int $userId
     * @param string $keyword
     * @return void
     */
    private function updateUserSearchHistory(int $userId, string $keyword): void
    {
        // 检查是否已存在
        $exists = Db::name('user_search_history')
            ->where('user_id', $userId)
            ->where('keyword', $keyword)
            ->find();

        if ($exists) {
            // 更新搜索次数和时间
            Db::name('user_search_history')
                ->where('id', $exists['id'])
                ->update([
                    'count' => $exists['count'] + 1,
                    'last_search_at' => date('Y-m-d H:i:s')
                ]);
        } else {
            // 新增搜索记录
            Db::name('user_search_history')->insert([
                'user_id' => $userId,
                'keyword' => $keyword,
                'count' => 1,
                'last_search_at' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }

        // 保持用户搜索历史不超过100条
        $this->cleanupUserSearchHistory($userId);
    }

    /**
     * 清理用户搜索历史
     * @param int $userId
     * @return void
     */
    private function cleanupUserSearchHistory(int $userId): void
    {
        $count = Db::name('user_search_history')
            ->where('user_id', $userId)
            ->count();

        if ($count > 100) {
            $oldestIds = Db::name('user_search_history')
                ->where('user_id', $userId)
                ->order('last_search_at', 'asc')
                ->limit($count - 100)
                ->column('id');

            if (!empty($oldestIds)) {
                Db::name('user_search_history')
                    ->whereIn('id', $oldestIds)
                    ->delete();
            }
        }
    }

    /**
     * 获取用户搜索历史
     * @param int $userId
     * @param int $limit
     * @return array
     */
    public function getSearchHistory(int $userId, int $limit = 20): array
    {
        return Db::name('user_search_history')
            ->where('user_id', $userId)
            ->order('last_search_at', 'desc')
            ->limit($limit)
            ->field('keyword,count,last_search_at')
            ->select()
            ->toArray();
    }

    /**
     * 清空用户搜索历史
     * @param int $userId
     * @return void
     */
    public function clearSearchHistory(int $userId): void
    {
        Db::name('user_search_history')
            ->where('user_id', $userId)
            ->delete();
    }

    /**
     * 获取搜索统计
     * @param array $params
     * @return array
     */
    public function getSearchStatistics(array $params): array
    {
        $startDate = $params['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
        $endDate = $params['end_date'] ?? date('Y-m-d');
        $type = $params['type'] ?? 'all';

        $query = Db::name('search_logs')
            ->whereBetween('created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']);

        if ($type !== 'all') {
            $query->where('type', $type);
        }

        // 总搜索次数
        $totalSearches = $query->count();

        // 每日搜索统计
        $dailyStats = $query->group('DATE(created_at)')
            ->field('DATE(created_at) as date, COUNT(*) as count')
            ->order('date', 'asc')
            ->select()
            ->toArray();

        // 热门关键词
        $topKeywords = $query->group('keyword')
            ->field('keyword, COUNT(*) as count')
            ->order('count', 'desc')
            ->limit(10)
            ->select()
            ->toArray();

        // 搜索类型分布
        $typeStats = Db::name('search_logs')
            ->whereBetween('created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59'])
            ->group('type')
            ->field('type, COUNT(*) as count')
            ->select()
            ->toArray();

        return [
            'total_searches' => $totalSearches,
            'daily_stats' => $dailyStats,
            'top_keywords' => $topKeywords,
            'type_stats' => $typeStats,
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ]
        ];
    }

    /**
     * 高级搜索
     * @param array $params
     * @return array
     */
    public function advancedSearch(array $params): array
    {
        // 实现高级搜索逻辑
        // 支持多字段组合搜索
        return [];
    }

    /**
     * 获取搜索过滤器选项
     * @return array
     */
    public function getSearchFilters(): array
    {
        return [
            'types' => [
                ['value' => 'all', 'label' => '全部'],
                ['value' => 'page', 'label' => '页面'],
                ['value' => 'template', 'label' => '模板'],
                ['value' => 'user', 'label' => '用户']
            ],
            'sort_options' => [
                ['value' => 'relevance', 'label' => '相关性'],
                ['value' => 'created_at', 'label' => '创建时间'],
                ['value' => 'updated_at', 'label' => '更新时间'],
                ['value' => 'views', 'label' => '浏览量']
            ]
        ];
    }

    /**
     * 保存搜索
     * @param array $params
     * @return array
     */
    public function saveSearch(array $params): array
    {
        $data = [
            'user_id' => $params['user_id'],
            'name' => $params['name'],
            'keyword' => $params['keyword'],
            'filters' => json_encode($params['filters'] ?? []),
            'description' => $params['description'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ];

        $id = Db::name('saved_searches')->insertGetId($data);

        return ['id' => $id] + $data;
    }

    /**
     * 获取保存的搜索
     * @param int $userId
     * @param int $page
     * @param int $perPage
     * @return array
     */
    public function getSavedSearches(int $userId, int $page, int $perPage): array
    {
        $query = Db::name('saved_searches')->where('user_id', $userId);

        $total = $query->count();
        $data = $query->page($page, $perPage)
            ->order('created_at', 'desc')
            ->select()
            ->toArray();

        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'last_page' => ceil($total / $perPage)
            ]
        ];
    }

    /**
     * 删除保存的搜索
     * @param int $id
     * @param int $userId
     * @return void
     */
    public function deleteSavedSearch(int $id, int $userId): void
    {
        Db::name('saved_searches')
            ->where('id', $id)
            ->where('user_id', $userId)
            ->delete();
    }

    /**
     * 导出搜索结果
     * @param array $params
     * @return array
     */
    public function exportSearchResults(array $params): array
    {
        // 实现搜索结果导出
        return [];
    }

    /**
     * 重建搜索索引
     * @param string $type
     * @return array
     */
    public function rebuildIndex(string $type): array
    {
        // 实现搜索索引重建
        return ['message' => '索引重建完成'];
    }

    /**
     * 获取搜索配置
     * @return array
     */
    public function getSearchConfig(): array
    {
        return [
            'enable_suggestions' => true,
            'max_suggestions' => 10,
            'enable_history' => true,
            'max_history' => 20,
            'enable_hot_keywords' => true,
            'search_timeout' => 5000
        ];
    }

    /**
     * 更新搜索配置
     * @param array $config
     * @return void
     */
    public function updateSearchConfig(array $config): void
    {
        // 实现搜索配置更新
    }

    /**
     * 获取时间段开始日期
     * @param string $period
     * @return string
     */
    private function getPeriodStartDate(string $period): string
    {
        switch ($period) {
            case 'day':
                return date('Y-m-d 00:00:00');
            case 'week':
                return date('Y-m-d 00:00:00', strtotime('-7 days'));
            case 'month':
                return date('Y-m-d 00:00:00', strtotime('-30 days'));
            default:
                return date('Y-m-d 00:00:00', strtotime('-7 days'));
        }
    }
}

<?php
declare (strict_types = 1);

namespace app\model;

use think\Model;

/**
 * 页面模板模型
 */
class PageTemplate extends Model
{
    // 设置表名
    protected $name = 'page_templates';

    // 设置字段信息
    protected $schema = [
        'id'           => 'int',
        'name'         => 'string',
        'slug'         => 'string',
        'description'  => 'string',
        'type'         => 'string',
        'config'       => 'text',
        'html_content' => 'text',
        'css_content'  => 'text',
        'preview_image'=> 'string',
        'status'       => 'int',
        'sort_order'   => 'int',
        'created_at'   => 'datetime',
        'updated_at'   => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 允许写入的字段
    protected $field = [
        'name', 'slug', 'description', 'type', 'config', 'html_content',
        'css_content', 'preview_image', 'status', 'sort_order'
    ];

    // JSON字段自动转换
    protected $json = ['config'];

    // 获取模板类型列表
    public static function getTypes()
    {
        return [
            'page' => '普通页面',
            'home' => '首页模板',
            'product' => '产品页面',
            'news' => '新闻页面',
            'case' => '案例页面',
            'contact' => '联系页面',
            'about' => '关于页面',
        ];
    }

    // 获取已发布的模板
    public static function getPublished($type = null)
    {
        $query = self::where('status', 1);
        
        if ($type) {
            $query->where('type', $type);
        }
        
        return $query->order('sort_order', 'asc')
                    ->order('updated_at', 'desc')
                    ->select();
    }

    // 根据类型获取默认模板
    public static function getDefaultByType($type)
    {
        return self::where('type', $type)
                   ->where('status', 1)
                   ->order('sort_order', 'asc')
                   ->find();
    }

    // 获取模板的预览URL
    public function getPreviewUrlAttr()
    {
        return '/admin/page-builder/preview/' . $this->id;
    }

    // 获取模板的设计URL
    public function getDesignUrlAttr()
    {
        return '/admin/page-builder?action=design&id=' . $this->id;
    }

    // 获取状态文本
    public function getStatusTextAttr()
    {
        return $this->status ? '已发布' : '草稿';
    }

    // 获取类型文本
    public function getTypeTextAttr()
    {
        $types = self::getTypes();
        return $types[$this->type] ?? $this->type;
    }

    // 获取组件统计（获取器）
    public function getComponentStatsAttr()
    {
        return $this->getComponentStats();
    }

    // 复制模板
    public function duplicate($newName = null)
    {
        $data = $this->toArray();
        unset($data['id'], $data['created_at'], $data['updated_at']);
        
        $data['name'] = $newName ?: ($this->name . ' - 副本');
        $data['status'] = 0; // 新复制的模板默认为草稿状态
        
        return self::create($data);
    }

    // 验证配置数据
    public function validateConfig($config)
    {
        if (is_string($config)) {
            $config = json_decode($config, true);
        }
        
        if (!is_array($config)) {
            return false;
        }
        
        // 基本结构验证
        return isset($config['components']) && is_array($config['components']);
    }

    // 获取组件统计
    public function getComponentStats()
    {
        $config = $this->config;
        if (is_string($config)) {
            $config = json_decode($config, true);
        }
        
        if (!is_array($config) || !isset($config['components'])) {
            return ['total' => 0, 'types' => []];
        }
        
        $components = $config['components'];
        $stats = [
            'total' => count($components),
            'types' => []
        ];
        
        foreach ($components as $component) {
            $type = $component['type'] ?? 'unknown';
            $stats['types'][$type] = ($stats['types'][$type] ?? 0) + 1;
        }
        
        return $stats;
    }
}

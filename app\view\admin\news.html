<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新闻管理 - 后台管理系统</title>

    <!-- CSS -->
    {include file="admin/common/css"}
    <link rel="stylesheet" href="/assets/css/admin/news.css">
    <link rel="stylesheet" href="/assets/css/image-uploader.css">
    <!-- CKEditor 5 CSS -->
    <link rel="stylesheet" href="/assets/css/ckeditor.css">
</head>
<body>
    <!-- 顶部导航 -->
    {include file="admin/common/header"}
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            {include file="admin/common/sidebar"}

            <!-- 主要内容 -->
            <main class="main-content">
                <!-- 内容头部 -->
                <div class="content-header">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-newspaper"></i> 新闻管理
                    </h1>
                </div>

                <!-- 引用统一消息组件 -->
                {include file="admin/common/message"}

                <!-- 页面内容区域 -->
                <div class="content-body">
                    <div class="news-container">

                    {if condition="$action == 'list'"}
                        <!-- 新闻管理选项卡视图 -->

                        <div class="list-header">
                            <div class="list-header-content">
                                <div class="list-title-section">
                                    <div class="list-icon">
                                        <i class="fas fa-newspaper"></i>
                                    </div>
                                    <div>
                                        <h1 class="list-title">新闻资讯</h1>
                                        <p class="list-subtitle">管理网站新闻资讯内容和分类</p>
                                    </div>
                                </div>
                                <div class="header-actions">
                                    {if condition="$tab == 'news'"}
                                        <a href="/admin/news?action=add" class="btn-add-custom">
                                            <i class="fas fa-plus"></i>
                                            <span>添加新闻</span>
                                        </a>
                                    {elseif condition="$tab == 'categories'"}
                                        <a href="/admin/news/add-category" class="btn-add-custom">
                                            <i class="fas fa-plus"></i>
                                            <span>添加分类</span>
                                        </a>
                                    {/if}
                                </div>
                            </div>
                        </div>

                        <!-- 选项卡导航 -->
                        <div class="tabs-container">
                            <div class="tabs-nav">
                                <a href="/admin/news?tab=news" class="tab-btn {$tab == 'news' ? 'active' : ''}">
                                    <i class="fas fa-newspaper"></i>
                                    <span>新闻列表</span>
                                </a>
                                <a href="/admin/news?tab=categories" class="tab-btn {$tab == 'categories' ? 'active' : ''}">
                                    <i class="fas fa-tags"></i>
                                    <span>分类管理</span>
                                </a>
                            </div>
                        </div>

                                                <div class="list-body">

                            {if condition="$tab == 'news'"}
                                <!-- 新闻列表 -->
                                {if condition="$newsList && count($newsList) > 0"}
                                    <div class="news-list">
                                        {volist name="newsList" id="news"}
                                        <div class="news-item">
                                            <div class="news-content-wrapper">
                                                <div class="news-thumbnail">
                                                    {if condition="$news.image"}
                                                        <img src="{$news.image}" alt="{$news.title}" class="news-thumb-img"
                                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                                        <div class="news-thumb-placeholder" style="display: none;">
                                                            <i class="fas fa-newspaper"></i>
                                                        </div>
                                                    {else /}
                                                        <div class="news-thumb-placeholder">
                                                            <i class="fas fa-newspaper"></i>
                                                        </div>
                                                    {/if}
                                                </div>

                                                <div class="news-info">
                                                    <div class="news-header">
                                                        <h3 class="news-title">{$news.title}</h3>
                                                        <div class="news-badges">
                                                            {if condition="$news.is_featured"}
                                                                <span class="badge badge-featured">
                                                                    <i class="fas fa-star"></i>
                                                                    推荐
                                                                </span>
                                                            {/if}
                                                            <span class="badge badge-category">
                                                                <i class="fas fa-tag"></i>
                                                                {$news.category.name ?? '未分类'}
                                                            </span>
                                                        </div>
                                                    </div>

                                                    <div class="news-meta">
                                                        <div class="meta-item">
                                                            <i class="fas fa-user"></i>
                                                            <span>{$news.author ?? '管理员'}</span>
                                                        </div>
                                                        <div class="meta-item">
                                                            <i class="fas fa-calendar"></i>
                                                            <span>{$news.published_at|date='Y-m-d H:i'}</span>
                                                        </div>
                                                        <div class="meta-item">
                                                            <i class="fas fa-eye"></i>
                                                            <span>{$news.views ?? 0} 次浏览</span>
                                                        </div>
                                                        {if condition="$news.tags"}
                                                            <div class="meta-item">
                                                                <i class="fas fa-tags"></i>
                                                                <span>{$news.tags}</span>
                                                            </div>
                                                        {/if}
                                                    </div>

                                                    {if condition="$news.summary"}
                                                        <div class="news-summary">
                                                            {$news.summary|mb_substr=0,150,'UTF-8'}
                                                            {if condition="mb_strlen($news.summary, 'UTF-8') > 150"}...{/if}
                                                        </div>
                                                    {/if}
                                                </div>

                                                <div class="news-actions">
                                                    <div class="status-toggle">
                                                        <label class="switch">
                                                            <input type="checkbox"
                                                                   {$news.status ? 'checked' : ''}
                                                                   onchange="toggleStatus('{$news.id}', this.checked ? 1 : 0)">
                                                            <span class="slider"></span>
                                                        </label>
                                                        <span class="status-label">{$news.status ? '已发布' : '草稿'}</span>
                                                    </div>

                                                    <div class="action-buttons">
                                                        <a href="/admin/news?action=edit&id={$news.id}" class="btn-action btn-edit" title="编辑">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <button onclick="deleteItem('{$news.id}', '{$news.title|htmlentities}', '/admin/news?action=delete&id={$news.id}')"
                                                                class="btn-action btn-delete" title="删除">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {/volist}
                                    </div>

                                    <!-- 自定义分页 -->
                                    {if condition="$newsList->hasPages()"}
                                        <div class="custom-pagination-container">
                                            <nav class="custom-pagination-nav">
                                                <div class="pagination-info">
                                                    <span class="pagination-text">
                                                        显示第 {$newsList->currentPage()} 页，共 {$newsList->lastPage()} 页，总计 {$newsList->total()} 条记录
                                                    </span>
                                                </div>
                                                <div class="pagination-buttons">
                                                    {if condition="$newsList->currentPage() > 1"}
                                                        <a href="/admin/news?action=list&tab={$tab}&page=1" class="pagination-btn pagination-first">
                                                            <i class="fas fa-angle-double-left"></i>
                                                            首页
                                                        </a>
                                                        <a href="/admin/news?action=list&tab={$tab}&page={$newsList->currentPage() - 1}" class="pagination-btn pagination-prev">
                                                            <i class="fas fa-angle-left"></i>
                                                            上一页
                                                        </a>
                                                    {else /}
                                                        <span class="pagination-btn pagination-first disabled">
                                                            <i class="fas fa-angle-double-left"></i>
                                                            首页
                                                        </span>
                                                        <span class="pagination-btn pagination-prev disabled">
                                                            <i class="fas fa-angle-left"></i>
                                                            上一页
                                                        </span>
                                                    {/if}
                                                    
                                                    <!-- 页码按钮 -->
                                                    {php}
                                                        $currentPage = $newsList->currentPage();
                                                        $lastPage = $newsList->lastPage();
                                                        
                                                        // 简化逻辑：直接显示所有页码（当页数不多时）
                                                        if ($lastPage <= 7) {
                                                            // 页数少时显示全部
                                                            $startPage = 1;
                                                            $endPage = $lastPage;
                                                        } else {
                                                            // 页数多时显示当前页前后2页
                                                            $startPage = max(1, $currentPage - 2);
                                                            $endPage = min($lastPage, $currentPage + 2);
                                                        }
                                                    {/php}
                                                    
                                                    {if condition="$startPage > 1"}
                                                        <a href="/admin/news?action=list&tab={$tab}&page=1" class="pagination-btn pagination-number">1</a>
                                                        {if condition="$startPage > 2"}
                                                            <span class="pagination-ellipsis">...</span>
                                                        {/if}
                                                    {/if}
                                                    
                                                    {php}
                                                        // 生成页码数组
                                                        $pageNumbers = range($startPage, $endPage);
                                                    {/php}
                                                    
                                                    {volist name="pageNumbers" id="pageNum"}
                                                        {if condition="$pageNum == $currentPage"}
                                                            <span class="pagination-btn pagination-number active">{$pageNum}</span>
                                                        {else /}
                                                            <a href="/admin/news?action=list&tab={$tab}&page={$pageNum}" class="pagination-btn pagination-number">{$pageNum}</a>
                                                        {/if}
                                                    {/volist}
                                                    
                                                    {if condition="$endPage < $lastPage"}
                                                        {if condition="$endPage < $lastPage - 1"}
                                                            <span class="pagination-ellipsis">...</span>
                                                        {/if}
                                                        <a href="/admin/news?action=list&tab={$tab}&page={$lastPage}" class="pagination-btn pagination-number">{$lastPage}</a>
                                                    {/if}
                                                    
                                                    {if condition="$newsList->currentPage() < $newsList->lastPage()"}
                                                        <a href="/admin/news?action=list&tab={$tab}&page={$newsList->currentPage() + 1}" class="pagination-btn pagination-next">
                                                            下一页
                                                            <i class="fas fa-angle-right"></i>
                                                        </a>
                                                        <a href="/admin/news?action=list&tab={$tab}&page={$newsList->lastPage()}" class="pagination-btn pagination-last">
                                                            末页
                                                            <i class="fas fa-angle-double-right"></i>
                                                        </a>
                                                    {else /}
                                                        <span class="pagination-btn pagination-next disabled">
                                                            下一页
                                                            <i class="fas fa-angle-right"></i>
                                                        </span>
                                                        <span class="pagination-btn pagination-last disabled">
                                                            末页
                                                            <i class="fas fa-angle-double-right"></i>
                                                        </span>
                                                    {/if}
                                                </div>
                                            </nav>
                                        </div>
                                    {/if}
                                {else /}
                                    <div class="empty-state">
                                        <div class="empty-icon">
                                            <i class="fas fa-newspaper"></i>
                                        </div>
                                        <h3 class="empty-title">暂无新闻</h3>
                                        <p class="empty-description">还没有添加任何新闻，点击上方按钮开始添加第一条新闻吧！</p>
                                                                         </div>
                                {/if}

                            {elseif condition="$tab == 'categories'"}
                                <!-- 分类列表 -->
                                {if condition="$categories && count($categories) > 0"}
                                    <div class="categories-list">
                                        {volist name="categories" id="category"}
                                        <div class="category-item">
                                            <div class="category-content-wrapper">
                                                <div class="category-icon">
                                                    <i class="fas fa-tag"></i>
                                                </div>

                                                <div class="category-info">
                                                    <div class="category-header">
                                                        <h3 class="category-name">{$category.name}</h3>
                                                        <div class="category-badges">
                                                            <span class="badge badge-slug">
                                                                <i class="fas fa-link"></i>
                                                                {$category.slug}
                                                            </span>
                                                        </div>
                                                    </div>

                                                    <div class="category-meta">
                                                        <div class="meta-item">
                                                            <i class="fas fa-sort"></i>
                                                            <span>排序: {$category.sort_order}</span>
                                                        </div>
                                                        <div class="meta-item">
                                                            <i class="fas fa-calendar"></i>
                                                            <span>{$category.created_at|date='Y-m-d H:i'}</span>
                                                        </div>
                                                        <div class="meta-item">
                                                            <i class="fas fa-newspaper"></i>
                                                            <span>{$category.news_count ?? 0} 篇新闻</span>
                                                        </div>
                                                    </div>

                                                    {if condition="$category.description"}
                                                        <div class="category-description">
                                                            {$category.description}
                                                        </div>
                                                    {/if}
                                                </div>

                                                <div class="category-actions">
                                                    <div class="action-buttons">
                                                        <a href="/admin/news?action=edit_category&id={$category.id}&tab=categories" class="btn-action btn-edit" title="编辑分类">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        {if condition="($category.news_count ?? 0) == 0"}
                                                            <button onclick="deleteItem('{$category.id}', '{$category.name|htmlentities}', '/admin/news?action=delete_category&id={$category.id}&tab=categories')"
                                                                    class="btn-action btn-delete" title="删除分类">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        {else /}
                                                            <button class="btn-action btn-delete disabled" title="该分类下有新闻，无法删除" disabled>
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        {/if}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {/volist}
                                    </div>
                                {else /}
                                    <div class="empty-state">
                                        <div class="empty-icon">
                                            <i class="fas fa-tags"></i>
                                        </div>
                                        <h3 class="empty-title">暂无分类</h3>
                                        <p class="empty-description">还没有添加任何新闻分类，点击上方按钮开始添加第一个分类吧！</p>
                                    </div>
                                {/if}
                            {/if}
                        </div>

                    {elseif condition="$action == 'add' OR $action == 'edit'"}
                        <!-- 添加/编辑新闻表单 -->
                        <div class="form-header">
                            <div class="form-header-content">
                                <div class="form-title-section">
                                    <div class="form-icon">
                                        <i class="fas fa-{$action == 'add' ? 'plus' : 'edit'}"></i>
                                    </div>
                                    <div>
                                        <h1 class="form-title">{$action == 'add' ? '添加新闻' : '编辑新闻'}</h1>
                                        <p class="form-subtitle">{$action == 'add' ? '创建新的新闻资讯' : '修改新闻信息'}</p>
                                    </div>
                                </div>
                                <a href="/admin/news" class="btn-back">
                                    <i class="fas fa-arrow-left"></i>
                                    <span>返回列表</span>
                                </a>
                            </div>
                        </div>

                        <div class="form-body">

                            <form method="POST" action="/admin/news" enctype="multipart/form-data" class="news-form">
                                <input type="hidden" name="action" value="{$action}">
                                {if condition="$editData"}
                                    <input type="hidden" name="id" value="{$editData.id}">
                                {/if}

                                <div class="form-grid">
                                    <!-- 基本信息 -->
                                    <div class="form-section">
                                        <div class="section-header">
                                            <h3 class="section-title">
                                                <i class="fas fa-info-circle"></i>
                                                基本信息
                                            </h3>
                                        </div>

                                        <div class="form-row" style="margin-top: 20px;">
                                            <div class="form-group">
                                                <label for="title" class="form-label required">
                                                    <i class="fas fa-heading"></i>
                                                    新闻标题
                                                </label>
                                                <input type="text" class="form-input" id="title" name="title"
                                                       value="{$editData.title|default=''}"
                                                       placeholder="请输入新闻标题" required>
                                            </div>

                                            <div class="form-group">
                                                <label for="slug" class="form-label">
                                                    <i class="fas fa-link"></i>
                                                    URL别名
                                                </label>
                                                <input type="text" class="form-input" id="slug" name="slug"
                                                       value="{$editData.slug|default=''}"
                                                       placeholder="自动生成或手动输入">
                                            </div>
                                        </div>

                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="category_id" class="form-label">
                                                    <i class="fas fa-tag"></i>
                                                    新闻分类
                                                </label>
                                                <select class="form-select" id="category_id" name="category_id">
                                                    <option value="1">默认分类</option>
                                                    {volist name="allCategories" id="category"}
                                                        <option value="{$category.id}"
                                                                {if condition="$editData && $editData.category_id == $category.id"}selected{/if}>
                                                            {$category.name}
                                                        </option>
                                                    {/volist}
                                                </select>
                                            </div>

                                            <div class="form-group">
                                                <label for="author" class="form-label">
                                                    <i class="fas fa-user"></i>
                                                    作者
                                                </label>
                                                <input type="text" class="form-input" id="author" name="author"
                                                       value="{$editData.author|default='管理员'}"
                                                       placeholder="请输入作者姓名">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="summary" class="form-label">
                                                <i class="fas fa-align-left"></i>
                                                新闻摘要
                                            </label>
                                            <textarea class="form-textarea" id="summary" name="summary" rows="3"
                                                      placeholder="请输入新闻摘要，用于列表页显示">{$editData.summary|default=''}</textarea>
                                            <div class="form-help">建议长度在100-200字之间，用于列表页和搜索结果显示</div>
                                        </div>

                                        <div class="form-group">
                                            <label for="content" class="form-label required">
                                                <i class="fas fa-file-text"></i>
                                                新闻内容
                                            </label>
                                                                        <!-- CKEditor 5编辑器 -->
                            <div class="ck-editor-container">
                                <textarea id="editor" name="content">{$editData.content|default=''}</textarea>
                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="tags" class="form-label">
                                                <i class="fas fa-tags"></i>
                                                标签
                                            </label>
                                            <input type="text" class="form-input" id="tags" name="tags"
                                                   value="{$editData.tags|default=''}"
                                                   placeholder="多个标签用英文逗号分隔">
                                            <div class="form-help">用于新闻分类和搜索，多个标签用英文逗号分隔</div>
                                        </div>
                                    </div>

                                    <!-- 图片上传 -->
                                    <div class="form-section">
                                        <div class="section-header">
                                            <h3 class="section-title">
                                                <i class="fas fa-image"></i>
                                                新闻图片
                                            </h3>
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label" style="margin-top: 20px;">
                                                <i class="fas fa-upload"></i>
                                                新闻图片
                                            </label>
                                            
                                            <!-- 图片上传弹窗按钮 -->
                                            <div class="image-upload-section">
                                                <button type="button" class="btn-upload-image" id="btnSelectNewsImage">
                                                    <i class="fas fa-images"></i>
                                                    <span>选择图片</span>
                                                </button>
                                                <div class="upload-help">
                                                    <small>支持上传新图片或从图库中选择，推荐尺寸：800x600px</small>
                                                </div>
                                            </div>

                                            <!-- 隐藏的表单字段存储选择的图片URL -->
                                            <input type="hidden" id="selectedImageUrl" name="image_url" value="{$editData.image|default=''}">
                                            
                                            <!-- 当前选择的图片预览 -->
                                            <div class="selected-image-preview" id="selectedImagePreview" {if condition='!$editData || !$editData.image'}style="display: none;"{/if}>
                                                <label class="form-label">已选择的图片：</label>
                                                <div class="image-preview-container">
                                                    <img src="{$editData.image|default=''}" alt="新闻图片" class="preview-img" id="previewImg">
                                                    <div class="image-actions">
                                                        <button type="button" class="btn-change-image" onclick="changeNewsImage()">
                                                            <i class="fas fa-edit"></i>
                                                            更换图片
                                                        </button>
                                                        <button type="button" class="btn-remove-image" onclick="removeNewsImage()">
                                                            <i class="fas fa-trash"></i>
                                                            移除图片
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 发布设置 -->
                                    <div class="form-section">
                                        <div class="section-header">
                                            <h3 class="section-title">
                                                <i class="fas fa-cog"></i>
                                                发布设置
                                            </h3>
                                        </div>

                                        <div class="form-row" style="margin-top: 20px;">
                                            <div class="form-group">
                                                <label for="published_at" class="form-label">
                                                    <i class="fas fa-calendar"></i>
                                                    发布时间
                                                </label>
                                                <input type="datetime-local" class="form-input" id="published_at" name="published_at"
                                                       value="{if condition='$editData && isset($editData.published_at) && $editData.published_at'}{$editData.published_at|date='Y-m-d\\TH:i'}{elseif condition='$editData && isset($editData.created_at) && $editData.created_at'}{$editData.created_at|date='Y-m-d\\TH:i'}{else /}{:date('Y-m-d\\TH:i')}{/if}">
                                            </div>

                                            <div class="form-group">
                                                <label for="sort_order" class="form-label">
                                                    <i class="fas fa-sort"></i>
                                                    排序权重
                                                </label>
                                                <input type="number" class="form-input" id="sort_order" name="sort_order"
                                                       value="{$editData.sort_order|default=0}"
                                                       placeholder="0" min="0">
                                            </div>
                                        </div>

                                        <div class="form-row">
                                            <div class="checkbox-group">
                                                <label class="checkbox-label">
                                                    <input type="checkbox" name="status" value="1"
                                                           {if condition="!$editData || $editData.status"}checked{/if}>
                                                    <span class="checkbox-custom"></span>
                                                    <span class="checkbox-text">
                                                        <i class="fas fa-eye"></i>
                                                        立即发布
                                                    </span>
                                                </label>
                                            </div>

                                            <div class="checkbox-group">
                                                <label class="checkbox-label">
                                                    <input type="checkbox" name="is_featured" value="1"
                                                           {if condition="$editData && $editData.is_featured"}checked{/if}>
                                                    <span class="checkbox-custom"></span>
                                                    <span class="checkbox-text">
                                                        <i class="fas fa-star"></i>
                                                        推荐新闻
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 表单操作按钮 -->
                                <div class="form-actions">
                                    <button type="submit" class="btn-submit">
                                        <i class="fas fa-save"></i>
                                        <span>保存新闻</span>
                                    </button>
                                    <a href="/admin/news" class="btn-cancel">
                                        <i class="fas fa-times"></i>
                                        <span>取消</span>
                                    </a>
                                </div>
                            </form>
                        </div>

                    {elseif condition="$action == 'add_category' OR $action == 'edit_category'"}
                        <!-- 添加/编辑分类表单 -->
                        <div class="form-header">
                            <div class="form-header-content">
                                <div class="form-title-section">
                                    <div class="form-icon">
                                        <i class="fas fa-{$action == 'add_category' ? 'plus' : 'edit'}"></i>
                                    </div>
                                    <div>
                                        <h1 class="form-title">{$action == 'add_category' ? '添加分类' : '编辑分类'}</h1>
                                        <p class="form-subtitle">{$action == 'add_category' ? '创建新的新闻分类' : '修改分类信息'}</p>
                                    </div>
                                </div>
                                <a href="/admin/news?tab=categories" class="btn-back">
                                    <i class="fas fa-arrow-left"></i>
                                    <span>返回列表</span>
                                </a>
                            </div>
                        </div>

                        <div class="form-body">

                            <form method="POST" action="/admin/news/add-category" class="category-form">
                                <input type="hidden" name="action" value="{$action}">
                                {if condition="$editCategoryData"}
                                    <input type="hidden" name="id" value="{$editCategoryData.id}">
                                {/if}

                                <div class="form-grid">
                                    <!-- 基本信息 -->
                                    <div class="form-section">
                                        <div class="section-header">
                                            <h3 class="section-title">
                                                <i class="fas fa-info-circle"></i>
                                                基本信息
                                            </h3>
                                        </div>

                                        <div class="form-row" style="margin-top: 20px;">
                                            <div class="form-group">
                                                <label for="category_name" class="form-label required">
                                                    <i class="fas fa-tag"></i>
                                                    分类名称
                                                </label>
                                                <input type="text" class="form-input" id="category_name" name="category_name"
                                                       value="{$editCategoryData.name|default=''}"
                                                       placeholder="请输入分类名称" required>
                                            </div>

                                            <div class="form-group">
                                                <label for="category_slug" class="form-label">
                                                    <i class="fas fa-link"></i>
                                                    URL别名
                                                </label>
                                                <input type="text" class="form-input" id="category_slug" name="category_slug"
                                                       value="{$editCategoryData.slug|default=''}"
                                                       placeholder="留空自动生成">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="category_description" class="form-label">
                                                <i class="fas fa-align-left"></i>
                                                分类描述
                                            </label>
                                            <textarea class="form-textarea" id="category_description" name="category_description" rows="4"
                                                      placeholder="请输入分类描述">{$editCategoryData.description|default=''}</textarea>
                                            <div class="form-help">简要描述该分类的用途和内容范围</div>
                                        </div>

                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="category_sort_order" class="form-label">
                                                    <i class="fas fa-sort"></i>
                                                    排序权重
                                                </label>
                                                <input type="number" class="form-input" id="category_sort_order" name="category_sort_order"
                                                       value="{$editCategoryData.sort_order|default=0}"
                                                       placeholder="0" min="0">
                                            </div>

                                            <div class="checkbox-group">
                                                <label class="checkbox-label">
                                                    <input type="checkbox" name="status" value="1"
                                                           {if condition="!$editCategoryData || $editCategoryData.status"}checked{/if}>
                                                    <span class="checkbox-custom"></span>
                                                    <span class="checkbox-text">
                                                        <i class="fas fa-eye"></i>
                                                        启用分类
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 表单操作按钮 -->
                                <div class="form-actions">
                                    <button type="submit" class="btn-submit">
                                        <i class="fas fa-save"></i>
                                        <span>保存分类</span>
                                    </button>
                                    <a href="/admin/news?tab=categories" class="btn-cancel">
                                        <i class="fas fa-times"></i>
                                        <span>取消</span>
                                    </a>
                                </div>
                            </form>
                        </div>
                    {/if}
                    </div>
                </div>


            </main>
        </div>
    </div>

    <!-- JavaScript -->
    {include file="admin/common/js"}
    <!-- CKEditor 5 Classic版本 -->
    <script src="/assets/js/ckeditor.js"></script>
    <!-- CKEditor 5 中文语言包 -->
    <script src="/assets/js/zh-cn.js"></script>
    <!-- 图片上传弹窗组件 -->
    <script src="/assets/js/image-uploader.js"></script>
    <!-- 图片选择器扩展 -->
    <script src="/assets/js/image-selector-extension.js"></script>

    <script>
        let editor;
        let editorImageUploader; // 编辑器专用图片上传组件实例
        let newsImageUploader; // 新闻图片选择器实例
        
        // 生成URL别名的函数
        function generateSlug(text, type = 'article') {
            if (!text || text.trim() === '') {
                return '';
            }
            
            let result = '';
            
            // 遍历每个字符
            for (let i = 0; i < text.length; i++) {
                const char = text[i];
                
                // 如果是英文字母或数字，直接保留
                if (/[a-zA-Z0-9]/.test(char)) {
                    result += char.toLowerCase();
                }
                // 如果是空格或其他分隔符，转换为连字符
                else if (/[\s\-_]/.test(char)) {
                    result += '-';
                }
                // 中文字符和其他字符忽略
            }
            
            // 清理结果
            result = result
                .replace(/[-]+/g, '-')  // 多个连字符合并为一个
                .replace(/^-+|-+$/g, '') // 去除首尾连字符
                .substring(0, 50); // 限制长度
            
            // 如果结果为空或太短，使用时间戳
            if (!result || result.length < 2) {
                const now = new Date();
                const timestamp = now.getFullYear() + 
                        String(now.getMonth() + 1).padStart(2, '0') + 
                        String(now.getDate()).padStart(2, '0') + '-' +
                        String(now.getHours()).padStart(2, '0') + 
                        String(now.getMinutes()).padStart(2, '0');
                
                // 根据类型使用不同的前缀
                if (type === 'category') {
                    result = 'cat-' + timestamp;
                } else {
                    result = 'article-' + timestamp;
                }
            } else {
                // 为结果添加类型前缀，确保分类和文章URL不冲突
                if (type === 'category') {
                    // 分类使用 "cat-" 前缀
                    result = 'cat-' + result;
                } else {
                    // 文章不添加前缀，保持简洁
                    // result = result; // 文章保持原样
                }
            }
            
            return result;
        }

        $(document).ready(function() {
            // 初始化编辑器专用图片上传组件（不限制数量）
            editorImageUploader = createImageUploader({
                uploadUrl: '/admin/image/upload?context=news',
                uploadField: 'upload',
                maxFiles: 999, // 编辑器不限制图片数量
                maxSize: 5 * 1024 * 1024, // 5MB限制
                allowedTypes: ['image/jpeg', 'image/png'], // 只允许JPG和PNG
                enableImageSelector: true,
                selectorUrl: '/admin/image/selector',
                isEditor: true, // 标记为编辑器环境
                context: 'news', // 明确标识上下文
                instanceId: 'editor-uploader', // 唯一实例ID
                onSelect: function(files) {
                    console.log('编辑器选择了文件:', files);
                    
                    // 验证文件类型和大小
                    const validFiles = [];
                    const errors = [];
                    
                    files.forEach(file => {
                        // 检查文件类型
                        if (!['image/jpeg', 'image/png'].includes(file.type)) {
                            errors.push(`${file.name}: 仅支持JPG和PNG格式`);
                            return;
                        }
                        
                        // 检查文件大小
                        if (file.size > 5 * 1024 * 1024) {
                            errors.push(`${file.name}: 文件大小不能超过5MB`);
                            return;
                        }
                        
                        // 检查文件名安全性
                        if (file.name.includes('..') || file.name.includes('/') || file.name.includes('\\')) {
                            errors.push(`${file.name}: 文件名包含非法字符`);
                            return;
                        }
                        
                        validFiles.push(file);
                    });
                    
                    if (errors.length > 0) {
                        editorImageUploader.showMessage('文件验证失败：\n' + errors.join('\n'), 'error');
                        return false;
                    }
                    
                    return validFiles;
                },
                onConfirm: function(orderedData, mode) {
                    console.log('编辑器确认选择的数据（按顺序）:', orderedData, '模式:', mode);
                    
                    if (mode === 'select') {
                        // 图片选择模式 - 直接插入已有图片到编辑器
                        insertSelectedImagesToEditor(orderedData);
                    } else {
                        // 文件上传模式 - 插入文件预览到编辑器
                        insertOrderedFilesToEditor(orderedData);
                    }
                },
                onUpload: function(uploadedFiles) {
                    console.log('编辑器上传成功:', uploadedFiles);
                    // 将上传的图片按顺序插入到编辑器中
                    uploadedFiles.forEach((fileData, index) => {
                        setTimeout(() => {
                            insertImageToEditor(fileData.url);
                        }, index * 100); // 延时确保按顺序插入
                    });
                    editorImageUploader.close();
                },
                onError: function(error) {
                    console.error('编辑器上传错误:', error);
                    editorImageUploader.showMessage('图片上传失败：' + error.message, 'error');
                }
            });
            
            // 初始化CKEditor 5编辑器 - Classic版本
            if ($('#editor').length) {
                ClassicEditor
                    .create(document.querySelector('#editor'), {
                        language: 'zh-cn',
                        placeholder: '请输入新闻内容...',
                        toolbar: [
                            'heading',
                            'bold',
                            'italic',
                            'underline',
                            'numberedList',
                            'bulletedList',
                            'outdent',
                            'indent',
                            'link',
                            'insertTable',
                            'blockQuote',
                            'undo',
                            'redo'
                        ],
                        heading: {
                            options: [
                                { model: 'paragraph', title: '正文', class: 'ck-heading_paragraph' },
                                { model: 'heading1', view: 'h1', title: '标题 1', class: 'ck-heading_heading1' },
                                { model: 'heading2', view: 'h2', title: '标题 2', class: 'ck-heading_heading2' },
                                { model: 'heading3', view: 'h3', title: '标题 3', class: 'ck-heading_heading3' },
                                { model: 'heading4', view: 'h4', title: '标题 4', class: 'ck-heading_heading4' }
                            ]
                        },

                        table: {
                            contentToolbar: [
                                'tableColumn',
                                'tableRow',
                                'mergeTableCells',
                                'tableCellProperties',
                                'tableProperties'
                            ]
                        },
                        link: {
                            decorators: {
                                openInNewTab: {
                                    mode: 'manual',
                                    label: '在新标签页中打开',
                                    attributes: {
                                        target: '_blank',
                                        rel: 'noopener noreferrer'
                                    }
                                }
                            }
                        }
                    })
                    .then(newEditor => {
                        editor = newEditor;
                        window.editor = newEditor;
                        
                        // 设置编辑器高度
                        const editingView = editor.editing.view;
                        editingView.change(writer => {
                            writer.setStyle('min-height', '300px', editingView.document.getRoot());
                            writer.setStyle('max-height', '500px', editingView.document.getRoot());
                        });
                        
                        // 设置焦点样式
                        editor.ui.focusTracker.on('change:isFocused', (evt, name, isFocused) => {
                            if (isFocused) {
                                $('.ck-editor-container').addClass('focused');
                            } else {
                                $('.ck-editor-container').removeClass('focused');
                            }
                        });
                        
                        // 添加图片上传按钮 - 使用新的弹窗组件
                        setTimeout(() => {
                            addImageUploadButton();
                        }, 1000);
                        
                        console.log('✅ CKEditor 5 Classic 初始化成功');
                    })
                    .catch(error => {
                        console.error('❌ CKEditor 5 初始化失败:', error);
                        // 降级到普通textarea
                        $('#editor').addClass('form-textarea').attr('rows', 15).show();
                        
                        // 显示错误提示
                        showMessage('编辑器加载失败，已切换到基础模式', 'warning');
                        
                        // 确保window.editor为null，表单验证会使用textarea
                        window.editor = null;
                    });
            }
            
            // 添加图片上传按钮 - 使用新的弹窗组件
            function addImageUploadButton() {
                // 多种方式查找工具栏
                let toolbarItems = document.querySelector('.ck-toolbar .ck-toolbar__items');
                if (!toolbarItems) {
                    toolbarItems = document.querySelector('.ck-toolbar__items');
                }
                if (!toolbarItems) {
                    toolbarItems = document.querySelector('.ck-toolbar');
                }
                
                if (!toolbarItems) {
                    return false;
                }
                
                // 检查按钮是否已存在
                if (document.querySelector('[data-upload-button="true"]')) {
                    return true;
                }
                
                // 创建图片上传按钮
                const imageButton = document.createElement('button');
                imageButton.className = 'ck-button ck-button_with-text';
                imageButton.type = 'button';
                imageButton.setAttribute('data-upload-button', 'true');
                imageButton.setAttribute('title', '上传图片');
                imageButton.setAttribute('aria-label', '上传图片');
                
                // 创建图标容器
                const iconContainer = document.createElement('span');
                iconContainer.className = 'ck-button__icon';
                
                // 使用FontAwesome图标
                const icon = document.createElement('i');
                icon.className = 'fas fa-images';
                icon.style.cssText = `
                    font-size: 12px !important;
                    color: rgba(255, 255, 255, 0.8) !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                    display: inline-block !important;
                `;
                iconContainer.appendChild(icon);
                imageButton.appendChild(iconContainer);
                
                // 添加文本标签
                const textLabel = document.createElement('span');
                textLabel.className = 'ck-button__label';
                textLabel.textContent = '图片';
                imageButton.appendChild(textLabel);
                
                // 绑定点击事件 - 打开编辑器专用弹窗
                imageButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🖼️ 打开编辑器图片上传弹窗');
                    
                    if (editorImageUploader) {
                        editorImageUploader.show();
                    } else {
                        console.error('❌ 编辑器图片上传组件未初始化');
                    }
                });
                
                // 插入到工具栏末尾
                toolbarItems.appendChild(imageButton);
                console.log('✅ 图片按钮已添加到工具栏');
                return true;
            }
            
            // 移除工具栏分隔符功能已禁用
            // function removeSeparators() {
            //     // 此功能已禁用，因为会误删图片按钮图标
            // }
            
            // 插入选择的图片到编辑器（已有图片）
            function insertSelectedImagesToEditor(selectedImages) {
                if (!window.editor) {
                    console.error('❌ 编辑器实例不存在');
                    return;
                }
                
                console.log(`📋 开始按顺序插入 ${selectedImages.length} 张已选择的图片到编辑器`);
                
                selectedImages.forEach((image, index) => {
                    setTimeout(() => {
                        try {
                            // 插入最终图片
                            const imageHtml = `
                                <img src="${image.file_url}" alt="${image.alt_text || image.filename}" 
                                     style="max-width: 100%; height: auto; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            `;
                            
                            // 获取当前选择位置
                            const selection = window.editor.model.document.selection;
                            const position = selection.getLastPosition();
                            
                            // 将HTML转换为模型并插入
                            const viewFragment = window.editor.data.processor.toView(imageHtml);
                            const modelFragment = window.editor.data.toModel(viewFragment);
                            
                            window.editor.model.change(writer => {
                                window.editor.model.insertContent(modelFragment, position);
                            });
                            
                            console.log(`✅ 第 ${index + 1} 张图片已插入: ${image.filename}`);
                            
                            if (index === selectedImages.length - 1) {
                                editorImageUploader.showMessage(`已按顺序插入 ${selectedImages.length} 张图片`, 'success');
                            }
                        } catch (error) {
                            console.error(`❌ 第 ${index + 1} 张图片插入失败:`, error);
                        }
                    }, index * 200); // 延时确保按顺序插入
                });
            }
            
            // 按顺序插入文件到编辑器（仅插入预览，不上传）
            function insertOrderedFilesToEditor(orderedFiles) {
                if (!window.editor) {
                    console.error('❌ 编辑器实例不存在');
                    return;
                }
                
                console.log(`📋 开始按顺序插入 ${orderedFiles.length} 个文件到编辑器`);
                
                orderedFiles.forEach((file, index) => {
                    // 创建本地预览URL
                    const localUrl = URL.createObjectURL(file);
                    
                    setTimeout(() => {
                        try {
                            // 插入图片占位符，显示文件名
                            const placeholderHtml = `
                                <div style="border: 2px dashed rgba(120, 119, 198, 0.5); padding: 20px; margin: 10px 0; text-align: center; background: rgba(120, 119, 198, 0.1); border-radius: 8px;">
                                    <img src="${localUrl}" alt="${file.name}" style="max-width: 200px; max-height: 150px; border-radius: 4px; margin-bottom: 10px;">
                                    <p style="color: rgba(120, 219, 255, 0.9); margin: 5px 0; font-size: 12px;">
                                        <strong>第 ${index + 1} 张:</strong> ${file.name}
                                    </p>
                                    <p style="color: rgba(255, 255, 255, 0.6); margin: 0; font-size: 11px;">
                                        <em>预览图片 - 需要上传后才能保存</em>
                                    </p>
                                </div>
                            `;
                            
                            // 获取当前选择位置
                            const selection = window.editor.model.document.selection;
                            const position = selection.getLastPosition();
                            
                            // 将HTML转换为模型并插入
                            const viewFragment = window.editor.data.processor.toView(placeholderHtml);
                            const modelFragment = window.editor.data.toModel(viewFragment);
                            
                            window.editor.model.change(writer => {
                                window.editor.model.insertContent(modelFragment, position);
                            });
                            
                            console.log(`✅ 第 ${index + 1} 张图片预览已插入: ${file.name}`);
                            
                            if (index === orderedFiles.length - 1) {
                            editorImageUploader.showMessage(`已按顺序插入 ${orderedFiles.length} 张图片预览`, 'success');
                            }
                        } catch (error) {
                            console.error(`❌ 第 ${index + 1} 张图片插入失败:`, error);
                        }
                    }, index * 200); // 延时确保按顺序插入
                });
            }
            
            // 将图片插入到编辑器中（上传后的最终图片）
            function insertImageToEditor(imageUrl) {
                if (!window.editor) {
                    console.error('❌ 编辑器实例不存在');
                    return;
                }
                
                try {
                    // 使用HTML方式插入图片
                    const imageHtml = `<img src="${imageUrl}" alt="上传的图片" style="max-width: 100%; height: auto; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">`;
                    
                    // 获取当前选择位置
                    const selection = window.editor.model.document.selection;
                    const position = selection.getLastPosition();
                    
                    // 将HTML转换为模型并插入
                    const viewFragment = window.editor.data.processor.toView(imageHtml);
                    const modelFragment = window.editor.data.toModel(viewFragment);
                    
                    window.editor.model.change(writer => {
                        window.editor.model.insertContent(modelFragment, position);
                    });
                    
                    console.log('✅ 图片已插入编辑器:', imageUrl);
                } catch (error) {
                    console.error('❌ 图片插入失败:', error);
                    editorImageUploader.showMessage('图片插入失败', 'error');
                }
            }
            
            // 修复颜色选择器显示
            function fixColorPickers() {
                // 创建观察器来监听颜色面板的出现
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1 && (node.classList.contains('ck-dropdown__panel') || node.querySelector('.ck-color-grid'))) {
                                setTimeout(() => {
                                    const colorTiles = node.querySelectorAll('.ck-color-grid__tile');
                                    const colors = [
                                        '#000000', '#424242', '#616161', '#757575', '#9E9E9E',
                                        '#BDBDBD', '#E0E0E0', '#EEEEEE', '#F5F5F5', '#FFFFFF',
                                        '#F44336', '#E91E63', '#9C27B0', '#673AB7', '#3F51B5',
                                        '#2196F3', '#03A9F4', '#00BCD4', '#009688', '#4CAF50',
                                        '#8BC34A', '#CDDC39', '#FFEB3B', '#FFC107', '#FF9800',
                                        '#FF5722', '#795548', '#607D8B'
                                    ];
                                    
                                    colorTiles.forEach((tile, index) => {
                                        if (colors[index] && (!tile.style.backgroundColor || tile.style.backgroundColor === '')) {
                                            tile.style.backgroundColor = colors[index];
                                        }
                                    });
                                }, 50);
                            }
                        });
                    });
                });
                
                // 开始观察
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });
            }
            


            // 标题自动生成slug（文章类型）
            $('#title').on('input', function() {
                if ($('#slug').val() === '') {
                    var title = $(this).val();
                    var slug = generateSlug(title, 'article');
                    $('#slug').val(slug);
                    console.log('文章URL别名自动生成:', title, '→', slug);
                }
            });

            // 分类名称自动生成slug - 增强版（分类类型）
            $('#category_name').on('input', function() {
                var name = $(this).val();
                var currentSlug = $('#category_slug').val();
                
                // 如果slug为空或者是之前自动生成的，则重新生成
                if (currentSlug === '' || currentSlug === $(this).data('lastGeneratedSlug')) {
                    var slug = generateSlug(name, 'category');
                    $('#category_slug').val(slug);
                    $(this).data('lastGeneratedSlug', slug);
                    console.log('分类URL别名自动生成:', name, '→', slug);
                }
            });
            
            // 分类slug手动编辑时清除自动生成标记
            $('#category_slug').on('input', function() {
                $('#category_name').removeData('lastGeneratedSlug');
                console.log('分类URL别名手动编辑，停止自动生成');
            });

            // 初始化新闻图片选择器
            initNewsImageSelector();
            
            // 图片上传预览（保留兼容性）
            $('#image').on('change', function() {
                var file = this.files[0];
                if (file) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        var preview = '<div class="upload-preview"><img src="' + e.target.result + '" alt="预览图片" class="preview-img"></div>';
                        $('.upload-area').find('.upload-preview').remove();
                        $('.upload-area').append(preview);
                    };
                    reader.readAsDataURL(file);
                }
            });

            // 页面加载时的消息提示自动隐藏 - 统一处理
            if ($('#alertMessage').length) {
                console.log('检测到页面消息提示，3秒后自动隐藏');
                setTimeout(function() {
                    if ($('#alertMessage').length) {
                        $('#alertMessage').fadeOut(500, function() {
                            console.log('页面消息提示已自动隐藏');
                        });
                    }
                }, 3000);
            }
            
            // 表单提交前同步编辑器内容和验证
            $('form').on('submit', function(e) {
                // 获取表单类型
                const isNewsForm = $(this).hasClass('news-form');
                const isCategoryForm = $(this).hasClass('category-form');
                
                console.log('表单提交验证 - 新闻表单:', isNewsForm, '分类表单:', isCategoryForm);
                
                // 分类表单验证（优先处理）
                if (isCategoryForm) {
                    console.log('执行分类表单验证');
                    return validateCategoryForm(e);
                }
                
                // 新闻表单验证
                if (isNewsForm) {
                    console.log('执行新闻表单验证');
                    return validateNewsForm(e);
                }
                
                // 检查是否在编辑器页面（有CKEditor）
                if ($('#editor').length > 0) {
                    console.log('检测到编辑器，执行新闻表单验证');
                    return validateNewsForm(e);
                }
                
                // 其他表单不做特殊验证
                console.log('其他表单，跳过特殊验证');
                return true;
            });
            
            // 新闻表单验证函数
            function validateNewsForm(e) {
                // 如果CKEditor存在，同步内容
                if (window.editor) {
                    const data = window.editor.getData();
                    $('#editor').val(data);
                    
                    // 验证编辑器内容
                    if (!data || data.trim() === '') {
                        e.preventDefault();
                        showMessage('请输入新闻内容', 'warning');
                        
                        // 聚焦到编辑器
                        setTimeout(() => {
                            if (window.editor) {
                                window.editor.editing.view.focus();
                            }
                        }, 100);
                        return false;
                    }
                } else {
                    // 如果CKEditor未初始化，检查原始textarea
                    const content = $('#editor').val();
                    if (!content || content.trim() === '') {
                        e.preventDefault();
                        showMessage('请输入新闻内容', 'warning');
                        $('#editor').focus();
                        return false;
                    }
                }
                
                // 验证标题
                const title = $('#title').val();
                if (!title || title.trim() === '') {
                    e.preventDefault();
                    showMessage('请输入新闻标题', 'warning');
                    $('#title').focus();
                    return false;
                }
                
                return true;
            }
            
            // 分类表单验证函数
            function validateCategoryForm(e) {
                // 验证分类名称
                const categoryName = $('#category_name').val();
                if (!categoryName || categoryName.trim() === '') {
                    e.preventDefault();
                    showMessage('请输入分类名称', 'warning');
                    $('#category_name').focus();
                    return false;
                }
                
                // 自动生成slug（如果为空）
                const categorySlug = $('#category_slug').val();
                if (!categorySlug || categorySlug.trim() === '') {
                    const generatedSlug = generateSlug(categoryName, 'category');
                    $('#category_slug').val(generatedSlug);
                    console.log('自动生成分类URL别名:', generatedSlug);
                }
                
                return true;
            }

        });


        // 状态切换
        function toggleStatus(id, status) {
            $.post('/admin/news?action=toggle_status', {
                id: parseInt(id),
                status: parseInt(status)
            }, function(response) {
                if (response && response.success) {
                    showMessage('状态更新成功', 'success');
                } else {
                    showMessage(response.message || '状态更新失败', 'danger');
                    // 恢复开关状态
                    $('input[onchange*="toggleStatus(\'' + id + '\'"]').prop('checked', !status);
                }
            }, 'json').fail(function() {
                showMessage('网络错误，请重试', 'danger');
                $('input[onchange*="toggleStatus(\'' + id + '\'"]').prop('checked', !status);
            });
        }






        // 初始化新闻图片选择器
        function initNewsImageSelector() {
            // 创建新闻专用图片上传器实例
            newsImageUploader = createImageUploader({
                uploadUrl: '/admin/image/upload?context=news',
                uploadField: 'upload',
                maxFiles: 1, // 新闻图片只选择一张
                maxSize: 5 * 1024 * 1024, // 5MB限制
                allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
                enableImageSelector: true,
                selectorUrl: '/admin/image/selector',
                context: 'news', // 明确标识上下文
                instanceId: 'news-uploader', // 唯一实例ID
                onSelect: function(files) {
                    // 验证文件类型和大小
                    const validFiles = [];
                    const errors = [];
                    
                    files.forEach(file => {
                        // 检查文件类型
                        if (!['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(file.type)) {
                            errors.push(`${file.name}: 仅支持JPG、PNG、GIF、WebP格式`);
                            return;
                        }
                        
                        // 检查文件大小
                        if (file.size > 5 * 1024 * 1024) {
                            errors.push(`${file.name}: 文件大小不能超过5MB`);
                            return;
                        }
                        
                        validFiles.push(file);
                    });
                    
                    if (errors.length > 0) {
                        // 使用图片上传器自己的提示
                        newsImageUploader.showMessage('文件验证失败：\n' + errors.join('\n'), 'error');
                        return false;
                    }
                    
                    return validFiles;
                },
                onConfirm: function(selectedData, mode) {
                    if (selectedData && selectedData.length > 0) {
                        const imageData = selectedData[0];
                        
                        if (mode === 'select') {
                            // 图片选择模式 - 使用已有图片
                            setNewsImage(imageData.file_url);
                            newsImageUploader.close();
                            // 使用图片上传器自己的提示
                            newsImageUploader.showMessage('新闻图片选择成功', 'success');
                        } else {
                            // 文件上传模式 - 只显示文件预览，不关闭弹窗
                            const reader = new FileReader();
                            reader.onload = function(e) {
                                setNewsImage(e.target.result);
                                // 文件上传模式下，等待用户点击上传按钮
                            };
                            reader.readAsDataURL(imageData);
                            // ❌ 注意：文件上传模式下不要关闭弹窗，让用户看到上传按钮
                        }
                    }
                },
                onUpload: function(uploadedFiles) {
                    if (uploadedFiles && uploadedFiles.length > 0) {
                        const imageData = uploadedFiles[0];
                        setNewsImage(imageData.url);
                        // 使用图片上传器自己的提示
                        newsImageUploader.showMessage('新闻图片上传成功', 'success');
                    }
                },
                onError: function(error) {
                    // 使用图片上传器自己的提示
                    newsImageUploader.showMessage('新闻图片操作失败：' + error.message, 'error');
                }
            });

            // 绑定选择图片按钮事件
            $('#btnSelectNewsImage').on('click', function() {
                console.log('🖼️ 打开新闻图片选择弹窗');
                newsImageUploader.show();
            });

            // 全局函数，供按钮调用
            window.changeNewsImage = function() {
                console.log('🔄 更换新闻图片');
                newsImageUploader.show();
            };

            window.removeNewsImage = function() {
                console.log('🗑️ 移除新闻图片');
                setNewsImage('');
                if (newsImageUploader) {
                    newsImageUploader.showMessage('新闻图片已移除', 'success');
                } else {
                showMessage('新闻图片已移除', 'info');
                }
            };
        }

        // 设置新闻图片
        function setNewsImage(imageUrl) {
            const selectedImageUrl = $('#selectedImageUrl');
            const selectedImagePreview = $('#selectedImagePreview');
            const previewImg = $('#previewImg');

            if (imageUrl) {
                selectedImageUrl.val(imageUrl);
                previewImg.attr('src', imageUrl);
                selectedImagePreview.show();
            } else {
                selectedImageUrl.val('');
                previewImg.attr('src', '');
                selectedImagePreview.hide();
            }
        }
    </script>
</body>
</html>
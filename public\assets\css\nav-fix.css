/* 导航菜单层级修复 CSS */

/* 确保header基础层级 */
header {
    z-index: 9999 !important;
}

.navbar {
    z-index: 9999 !important;
}

/* 重置所有可能冲突的元素z-index */
.hero-section,
.hero-content,
.hero-title,
.hero-subtitle,
.hero-description,
.hero-buttons {
    z-index: 10 !important;
}

.hero-image {
    z-index: 8 !important;
}

.swiper-pagination {
    z-index: 9 !important;
}

/* 确保下拉菜单有最高的层级 */
.mega-dropdown .dropdown-menu {
    z-index: 15000 !important;
    position: fixed !important;
}

.solutions-menu {
    z-index: 15001 !important;
}

.products-menu {
    z-index: 15002 !important;
}

/* 确保三角形指示器层级正确 */
.mega-dropdown .dropdown-menu::before {
    z-index: 15003 !important;
}

.mega-dropdown .dropdown-menu::after {
    z-index: 15002 !important;
}

/* 确保Bootstrap默认下拉菜单也有正确层级 */
.dropdown-menu {
    z-index: 15000 !important;
}

.dropdown-menu.show {
    z-index: 15000 !important;
}

/* 修复可能的overlap问题 */
.mega-dropdown {
    position: relative;
    z-index: 14999 !important;
}

/* 确保菜单触发器有正确的层级 */
.dropdown-toggle {
    z-index: 14999 !important;
    position: relative;
}

/* 强制重置其他高z-index元素 */
.feature-item-overlay,
.showcase-content,
.company-bg-section .bg-actions .btn,
.back-to-top,
.online-service,
.service-panel {
    z-index: 50 !important;
}

/* 确保模态框等不会超过导航菜单 */
.modal {
    z-index: 14000 !important;
}

.modal-backdrop {
    z-index: 13999 !important;
}

/* 修复可能的body层级问题 */
body {
    position: relative;
    z-index: 1;
}

main {
    position: relative;
    z-index: 5;
}

/* 确保导航菜单在移动端也正常工作 */
@media (max-width: 991px) {
    .navbar-collapse {
        z-index: 15000 !important;
        position: relative;
    }
    
    .mega-dropdown .dropdown-menu {
        position: absolute !important;
        z-index: 15001 !important;
    }
} 